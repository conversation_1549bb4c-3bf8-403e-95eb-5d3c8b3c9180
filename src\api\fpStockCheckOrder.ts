import { useRequest } from '@/use/useRequest'

// 获取列表
export const getProductCheckOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/getProductCheckOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addProductCheckOrder = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/addProductCheckOrder',
    method: 'post',
  })
}

// 获取详情
export const getProductCheckOrder = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/getProductCheckOrder',
    method: 'get',
  })
}

// 作废
export const updateProductCheckOrderAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateProductCheckOrderAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateProductCheckOrderAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusReject',
    method: 'put',
  })
}
// 消审
export const updateProductCheckOrderAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusWait',
    method: 'put',
  })
}

// 更新
export const updateProductCheckOrder = () => {
  return useRequest({
    url: '/admin/v1/product/productCheckOrder/updateProductCheckOrder',
    method: 'put',
  })
}
