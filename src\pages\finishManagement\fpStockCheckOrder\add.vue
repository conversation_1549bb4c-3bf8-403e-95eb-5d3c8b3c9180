<script setup lang="ts" name="FpStockCheckOrderEdit">
import { ElMessage } from 'element-plus'
import { computed, h, nextTick, onMounted, reactive, ref, toRefs } from 'vue'
import { VxeInput } from 'vxe-pc-ui'
import AccordingLibAdd from '../components/AccordingLibAdd.vue'
import AccordingRepertoryAdd from '../components/AccordingRepertoryAdd.vue'
import FineSizeAdd from '../components/FineSizeInventoryProfitAdd.vue'
import FineSizeInventoryProfitRepertoryAdd from '../components/FineSizeInventoryProfitRepertoryAdd.vue'
import { useStockCheckOrderSharedMethods } from './shared'
import { addProductCheckOrder } from '@/api/fpStockCheckOrder'
import { EmployeeType } from '@/common/enum'
import { formatDate, formatLengthMul, formatTwoDecimalsMul, formatWeightMul } from '@/common/format'
import { deleteToast, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import GridTable from '@/components/GridTable/index.vue'

const tablesRef = ref()
// 备注: 配布单号
// 带出 采购退货单号
// 出仓匹数不可修改
// form 营销体系 供应商 仓库名称 不可修改 仓管员可修改
// const router = useRouter()
const routerList = useRouterList()
let uuid = 0
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const AccordingRepertoryAddRef = ref()
const FineSizeInventoryProfitRepertoryAddRef = ref()
const state = reactive<any>({
  formInline: {
    sale_system_id: '',
    warehouse_id: '',
    warehouse_manager_id: '',
    remark: '',
    check_time: new Date(),
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    check_time: [{ required: true, message: '请选择盘点日期', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库名称', trigger: 'change' }],
  },
  defaultData: {
    customer_id: 0,
    default_customer_name: '',
  },
})
const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})

const bulkShow = ref(false)
const { multipleSelection, tableConfig, FooterMethod } = useStockCheckOrderSharedMethods()
function handEdit() {
  if (multipleSelection.value.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}
const splitList = ref<any[]>([])
// const splitList = computed(() => {
//   const { page, size } = toRefs(elPaginationConfig.value)
//

//

//   const list = finishProductionOptions.datalist.slice((page.value - 1) * size.value, page.value * size.value)
//

//   // tablesRef.value?.TableRef.loadData(finishProductionOptions.datalist)
//   // tablesRef.value?.TableRef.loadData(list)
//   return list
// })

const page = ref(1)
const size = ref(500)

function handleSizeChange(val: number) {
  size.value = val
  computedList()
}
function handleCurrentChange(val: number) {
  page.value = val
  computedList()
}
const finishProductionOptions = reactive<any>({
  rowIndex: 0,
  tableConfig,
  FooterMethod,
  datalist: [],

  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      children: [
        {
          field: 'product_code',
          title: '成品编号',
          width: 120,
          required: true,
        },
        {
          field: 'product_name',
          title: '成品名称',
          width: 150,
          required: true,
        },
      ],
    },
    {
      title: '成品信息',
      children: [
        {
          field: 'customer_id',
          title: '所属客户',
          width: 150,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.customer_name)
              return row.customer_name
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.customer_name)
              return h(SelectDialog, {
                modelValue: row.customer_id,
                api: 'GetCustomerEnumList',
                query: { name: componentRemoteSearch.customer_name },
                onChangeInput: (val) => {
                  componentRemoteSearch.customer_name = val
                },
                onChangeValue: (val) => {
                  row.customer_id = val.id
                  row.customer_name = val.name
                },
                labelName: row.customer_name,
                columnList: [
                  {
                    title: '客户编号',
                    width: 100,
                    required: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '客户编号',
                        width: 100,
                      },
                    ],
                  },
                  {
                    title: '客户名称',
                    width: 100,
                    colGroupHeader: true,
                    required: true,
                    childrenList: [
                      {
                        isEdit: true,
                        field: 'name',
                        title: '客户名称',
                        width: 100,
                      },
                    ],
                  },
                  {
                    title: '电话',
                    colGroupHeader: true,
                    width: 100,
                    childrenList: [
                      {
                        field: 'phone',
                        isEdit: true,
                        title: '电话',
                        width: 100,
                      },
                    ],
                  },
                  {
                    title: '销售员',
                    width: 100,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'seller_name',
                        title: '销售员',
                        soltName: 'seller_name',
                        isEdit: true,
                        width: 100,
                      },
                    ],
                  },
                ],
                tableColumn: [
                  {
                    field: 'name',
                    title: '客户名称',
                  },
                ],
              })
            },
          },
        },
        {
          field: 'product_color_code',
          title: '色号',
          width: 140,
          required: true,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_color_code)
              return row.product_color_code
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_color_code)
              return h(SelectDialog, {
                modelValue: row.finishProductionColorId,
                api: 'GetFinishProductColorDropdownList',
                query: {
                  finish_product_id: row.product_id,
                  product_color_name: componentRemoteSearch.color_code,
                },
                onChangeInput: (val) => {
                  componentRemoteSearch.color_code = val
                },
                onChangeValue: (item) => {
                  setFinishProductColor(item, row)
                },
                labelField: 'product_color_code',
                columnList: [
                  {
                    field: 'product_color_code',
                    title: '色号',
                    width: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'product_color_code',
                        isEdit: true,
                        title: '色号',
                        width: 100,
                      },
                    ],
                  },
                  {
                    field: 'product_color_name',
                    title: '颜色',
                    width: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'product_color_name',
                        isEdit: true,
                        title: '颜色',
                        width: 100,
                      },
                    ],
                  },
                ],
                tableColumn: [
                  {
                    field: 'product_color_code',
                    title: '色号',
                  },
                ],
              })
            },
          },
        },
        {
          field: 'product_color_name',
          title: '颜色',
          width: 140,
          required: true,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_color_name)
              return row.product_color_name
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_color_name)
              return h(SelectDialog, {
                modelValue: row.finishProductionColorId,
                api: 'GetFinishProductColorDropdownList',
                query: {
                  finish_product_id: row.product_id,
                  product_color_name: componentRemoteSearch.color_name,
                },
                onChangeInput: (val) => {
                  componentRemoteSearch.color_name = val
                },
                onChangeValue: (item) => {
                  setFinishProductColor(item, row)
                },
                labelField: 'product_color_name',
                columnList: [
                  {
                    field: 'product_color_code',
                    title: '色号',
                    width: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'product_color_code',
                        isEdit: true,
                        title: '色号',
                        width: 100,
                      },
                    ],
                  },
                  {
                    field: 'product_color_name',
                    title: '颜色',
                    width: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'product_color_name',
                        isEdit: true,
                        title: '颜色',
                        width: 100,
                      },
                    ],
                  },
                ],
                tableColumn: [
                  {
                    field: 'product_color_name',
                    title: '色号',
                  },
                ],
              })
            },
          },
        },

        {
          field: 'product_level_id',
          title: '成品等级',
          width: 140,
          editRender: { autoFocus: '.el-select' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_level_name)
              return row.product_level_name
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_level_name)
              return h(SelectComponents, {
                modelValue: row.product_level_id,
                api: 'GetInfoBaseFinishedProductLevelEnumList',
                onChangeValue: (item) => {
                  row.product_level_name = item?.name
                  row.product_level_id = item?.id
                },
                labelField: 'name',
                valueField: 'id',
              })
            },
          },
        },
        {
          field: 'dyelot_number',
          title: '染厂缸号',
          width: 150,
          required: true,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.dyelot_number)
              return row.dyelot_number
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.dyelot_number)
              return h(VxeInput, {
                modelValue: row.dyelot_number,
                onInput: (val) => {
                  row.dyelot_number = val.value
                },
              })
            },
          },
        },
        // {
        //   field: 'dye_factory_color_code',
        //   soltName: 'dye_factory_color_code',
        //   title: '染厂色号',
        //   width: 100,
        // },
        // {
        //   field: 'finish_product_width',
        //   soltName: 'finish_product_width',
        //   title: '成品幅宽',
        //   width: 100,
        // },
        // {
        //   field: 'finish_product_gram_weight',
        //   soltName: 'finish_product_gram_weight',
        //   title: '成品克重',
        //   width: 100,
        // },
        {
          field: 'product_ingredient',
          title: '成品成分',
          width: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          width: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          width: 150,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_remark)
              return row.product_remark
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.product_remark)
              return h(VxeInput, {
                modelValue: row.product_remark,
                onInput: (val) => {
                  row.product_remark = val.value
                },
              })
            },
          },
        },
      ],
    },
    {
      title: '库存',
      children: [
        {
          field: 'roll',
          title: '匹数',
          width: 100,
        },
        {
          field: 'weight',
          title: '数量总计',
          width: 100,
        },
        {
          field: 'measurement_unit_name',
          title: '单位',
          width: 100,
        },
        {
          field: 'length',
          title: '辅助数量',
          width: 100,
        },
      ],
    },
    {
      title: '盘点',
      children: [
        {
          field: 'check_roll',
          title: '匹数',
          width: 100,
          required: true,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.check_roll)
              return row.check_roll
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.check_roll)
              return h(VxeInput, {
                modelValue: row.check_roll,
                type: 'float',
                onInput: (val) => {
                  row.check_roll = val.value
                },
              })
            },
          },
        },
        {
          field: 'check_weight',
          title: '数量总计',
          width: 100,
        },
        {
          field: 'check_length',
          title: '辅助数量总计',
          width: 100,
        },
      ],
    },
    {
      title: '盈亏',
      children: [
        {
          field: 'difference_roll',
          soltName: 'difference_roll',
          title: '匹数',
          width: 100,
          slots: {
            default: ({ row }: any) => {
              if (row.difference_roll > 0)
                return h('span', { class: 'text-red-600' }, `+${row.difference_roll}`)
              else if (row.difference_roll < 0)
                return h('span', { class: 'text-green-600' }, row.difference_roll)
              else
                return h('span', {}, row.difference_roll)
            },
          },
        },
        {
          field: 'difference_weight',
          soltName: 'difference_weight',
          title: '数量总计',
          width: 100,
          slots: {
            default: ({ row }: any) => {
              if (row.difference_weight > 0)
                return h('span', { class: 'text-red-600' }, `+${row.difference_weight}`)
              else if (row.difference_weight < 0)
                return h('span', { class: 'text-green-600' }, row.difference_weight)
              else
                return h('span', {}, row.difference_weight)
            },
          },
        },
        {
          field: 'difference_length',
          soltName: 'difference_length',
          title: '辅助数量总计',
          width: 100,
          slots: {
            default: ({ row }: any) => {
              if (row.difference_length > 0)
                return h('span', { class: 'text-red-600' }, `+${row.difference_length}`)
              else if (row.difference_length < 0)
                return h('span', { class: 'text-green-600' }, row.difference_length)
              else
                return h('span', {}, row.difference_length)
            },
          },
        },
      ],
    },
    {
      title: '单据备注信息',
      children: [
        {
          field: 'remark',
          title: '备注',
          width: 150,
          editRender: { autofocus: '.vxe-input--inner' },
          slots: {
            default: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.remark)
              return row.remark
            },
            edit: ({ row }: any) => {
              if (row.is_stock)
                return h('span', row.remark)
              return h(VxeInput, {
                modelValue: row.remark,
                maxlength: 200,
                type: 'text',
                onInput: (val) => {
                  row.remark = val.value
                },
              })
            },
          },
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      children: [
        {
          field: '',
          title: '细码',
          width: 110,
          slots: {
            default: ({ row, rowIndex }: any) => {
              //

              // if (!row.stock_product_id) {
              //   if (row.check_roll) {
              //     return h(
              //       'el-button',
              //       {
              //         onClick: () => {
              //           showFSRepertoryDialog(row, rowIndex)
              //         },
              //       },
              //       [
              //         h(
              //           'span',
              //           {
              //             class: 'cursor-pointer',
              //             style: 'color:#409eff;',
              //           },
              //           '统计',
              //         ),
              //         h(
              //           'span',
              //           {
              //             style: 'color: red',
              //           },
              //           `${row.check_roll - getSFRoll(row) > 0 ? `(${row.check_roll - getSFRoll(row)}条未录)` : ''}`,
              //         ),
              //         h(
              //           'span',
              //           {
              //             style: 'color: #ccc',
              //             vif: row.check_roll === getSFRoll(row),
              //           },
              //           `${row.check_roll === getSFRoll(row) ? `(已统计)` : ''}`,
              //         ),
              //       ],
              //     )
              //   }
              // }
              return h(
                'el-button',
                {
                  type: 'primary',
                  text: true,
                  link: true,
                  onClick: () => {
                    statisticiansClick(row, rowIndex)
                  },
                },
                [
                  h(
                    'span',
                    {
                      class: 'cursor-pointer',
                      style: 'color:#409eff;',
                    },
                    '统计',
                  ),
                  h(
                    'span',
                    {
                      vIf: row.item_data.length === 0,
                      style: `color: ${row.item_data.length === 0 ? 'red' : '#ccc'}`,
                    },
                    row.item_data.length === 0 ? '(未录入)' : '(已统计)',
                  ),
                ],
              )
            },
          },
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      children: [
        {
          field: '',
          title: '操作',
          width: 100,
          slots: {
            default: ({ row }: any) => {
              return h(
                'el-button',
                {
                  text: true,
                  type: 'primary',
                  class: 'cursor-pointer',
                  style: 'color:#409eff;',
                  onClick: () => {
                    handleRowDel(row)
                  },
                },
                '删除',
              )
            },
          },
        },
      ],
    },
  ],
  handleSure: (list: any) => {
    // 成品成分
    // 取配布单不可修改
    list = list.map((item: any) => {
      const product_id = item.id
      delete item.id
      return {
        ...item,
        uuid: ++uuid,
        is_stock: false, // true为库存盘点，false为盘盈新增盘点
        // 24-03-26 盘盈新增，不用自动带出所属客户
        // customer_id: state.defaultData.customer_id,
        // customer_name: state.defaultData.customer_name,
        product_id, // 成品id
        product_code: item.finish_product_code, // 成品编号
        product_name: item.finish_product_name, // 成品名称
        finish_product_width: item.finish_product_width, // 成品幅宽
        finish_product_gram_weight: item.finish_product_gram_weight, // 成品克重
        product_level_name: item.finish_product_level_name, // 等级名称
        product_level_id: item.finish_product_level_id || 0, // 等级id
        product_craft: item.finish_product_craft, // 成品工艺
        roll: 0, // 库存匹数
        weight: 0, // 库存数量
        length: 0, // 库存辅助数量
        measurement_unit_name: item.measurement_unit_name, // 单位name
        measurement_unit_id: item.measurement_unit_id, // 单位id
        product_ingredient: item.finish_product_ingredient, // 成品成分
        product_weight_error: item.weight_error, // 成品资料空差
        product_paper_tube_weight: item.paper_tube_weight, // 成品资料纸筒质量
        item_data: [],
        difference_roll: 0,
        difference_weight: 0,
        difference_length: 0,
        check_roll: 0,
        check_weight: 0,
        check_length: 0,
      }
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
    computedList()
    // tablesRef.value.TableRef.loadData(finishProductionOptions.datalist)
  },
  handleRepertorySure: (list: any) => {
    // 成品成分
    // 取配布单不可修改
    list = list.map((item: any) => {
      delete item.id
      return {
        ...item,
        uuid: ++uuid,
        is_stock: true, // true为库存盘点，false为盘盈新增盘点
        stock_product_id: item.stock_product_id, // 库存id
        product_id: item.product_id, // 成品id
        product_code: item.product_code, // 成品编号
        product_name: item.product_name, // 成品名称
        finish_product_width: item.finish_product_width, // 成品幅宽
        finish_product_gram_weight: item.finish_product_gram_weight, // 成品克重
        customer_name: item.customer_name, // 所属客户
        customer_id: item.customer_id, // 所属客户
        product_color_code: item.product_color_code, // 色号
        product_color_id: item.product_color_id, // 颜色id
        product_color_name: item.product_color_name, // 颜色
        dyelot_number: item.dyelot_number, // 缸号
        dye_factory_color_code: item.dye_factory_color_code, // 染厂色号
        product_level_name: item.product_level_name, // 等级名称
        product_level_id: item.product_level_id, // 等级id
        product_remark: item.product_remark, // 成品备注
        product_craft: item.finish_product_craft, // 成品工艺
        roll: item.roll, // 库存匹数
        weight: item.weight, // 库存数量
        length: item.length, // 库存辅助数量
        measurement_unit_name: item.measurement_unit_name, // 单位name
        measurement_unit_id: item.measurement_unit_id, // 单位id
        product_ingredient: item.finish_product_ingredient, // 成品成分
        item_data: [],
        // 多余字段
        check_roll: 0,
        check_weight: 0,
        check_length: 0,
        base_unit_weight: 0,
        weight_error: 0,
        paper_tube_weight: 0,
        difference_roll: 0,
        difference_weight: 0,
        difference_length: 0,
      }
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]

    computedList()
    // tablesRef.value.TableRef.loadData(finishProductionOptions.datalist)
  },
  handleSureFineSize: (list: any) => {
    let check_roll = 0
    let check_weight = 0
    let check_length = 0

    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_data = list.map((item: any) => {
      check_roll += Number(item.check_roll)
      check_weight += Number(item.check_weight)
      check_length += Number(item.check_length || 0)
      return {
        ...item,
        check_roll: Number(item.check_roll),
        check_weight: Number(item.check_weight),
        check_length: Number(item.check_length) || 0,
        base_unit_weight: 0,
        weight_error: 0,
        paper_tube_weight: 0,
      }
    })
    // 自动计算
    // const check_roll = list.reduce((pre: any, val: any) => pre + Number(val.check_roll), 0)
    // const check_weight = list.reduce((pre: any, val: any) => pre + Number(val.check_weight), 0)
    // const check_length = list.reduce((pre: any, val: any) => pre + Number(val.check_length), 0)

    // 计算盘点匹数、数量总计、辅助数量
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].check_roll = Number(check_roll.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].check_weight = Number(check_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].check_length = Number(check_length.toFixed(2) || 0)
    computedKeys()
  },
  handleFSRepertory: (list: any) => {
    let check_weight = 0
    let check_length = 0
    let check_roll = 0
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_data = list.map((item: any) => {
      check_weight += Number(item.check_weight)
      check_length += Number(item.check_length || 0)
      check_roll += Number(item.check_roll || 0)
      return {
        ...item,
        check_roll: Number(item.check_roll), // 实盘匹数
        check_length: Number(item.check_length), // 实盘辅助数量
        check_weight: Number(item.check_weight), // 实盘数量
        weight: 0,
        roll: 0,
        length: 0,
        finished_check: true,
      }
    })

    // const check_weight = list.reduce((pre: any, val: any) => pre + Number(val.check_weight), 0)
    // const check_length = list.reduce((pre: any, val: any) => pre + Number(val.check_length), 0)
    // const check_roll = list.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_data = list
    // 盘点数量总计 = 细码数量之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].check_roll = check_roll.toFixed(2)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].check_weight = check_weight.toFixed(2)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].check_length = check_length ? check_length.toFixed(2) : 0
    computedKeys()
  },

  //   批量操作
  handEdit,
})

//   删除
async function handleRowDel({ uuid }: any) {
  const res = await deleteToast('是否确认删除该成品')
  if (!res)
    return
  const index = finishProductionOptions.datalist.findIndex((item: any) => item.uuid === uuid)
  finishProductionOptions.datalist.splice(index, 1)
  computedList()
  // tablesRef.value.TableRef.loadData(finishProductionOptions.datalist)
}

// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    valueField: 'id',
    labelField: 'name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
  },
  {
    field: 'finishProductionColorId',
    field_name: 'product_color_code',
    valueField: 'id',
    labelField: 'product_color_code',
    title: '色号',
    component: 'select',
    api: 'GetFinishProductColorDropdownList',
  },
  {
    field: 'finishProductionColorId',
    field_name: 'product_color_name',
    valueField: 'id',
    labelField: 'product_color_name',
    title: '颜色',
    component: 'select',
    api: 'GetFinishProductColorDropdownList',
  },
  {
    field: 'product_level_id',
    field_name: 'product_level_name',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'dyelot_number',
    title: '染厂缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'check_roll',
    title: '盘点匹数',
    component: 'input',
    type: 'text',
  },
  //   {
  //     field: 'finish_product_width',
  //     title: '成品幅宽',
  //     component: 'input',
  //     type: 'text',
  //   },
  //   {
  //     field: 'finish_product_gram_weight',
  //     title: '成品克重',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '单据备注',
    component: 'input',
    type: 'text',
  },
])

async function bulkSubmit({ row, value, quickInputResult }: any) {
  const ids = multipleSelection.value.map((item: any) => item.uuid)

  finishProductionOptions.datalist.map((item: any, index: number) => {
    if (row.quickInput && quickInputResult?.[index]) {
      item[row.field] = quickInputResult[index]
      return item
    }

    if (row.field === 'remark' && ids.includes(item.uuid) && item.is_stock) {
      item[row.field] = value[row.field]
    }
    else if (ids.includes(item.uuid) && !item.is_stock) {
      item[row.field_name] = value[row.field_name]
      item[row.field] = value[row.field]
    }
  })

  ElMessage.success('设置成功')
  handBulkClose()
  computedKeys()
}

function handBulkClose() {
  bulkShow.value = false
}

// function getSFRoll(row: any) {
//   return row.item_data.reduce((pre: any, val: any) => pre + Number(val.check_roll), 0)
// }

function showLibDialog() {
  AccordingRepertoryAddRef.value.state.showModal = true
}

function computedKeys() {
  // 自动计算盘点信息
  finishProductionOptions.datalist.forEach((item: any) => {
    item.difference_roll = Number((Number(item.check_roll) - item.roll).toFixed(2) || 0)
    item.difference_weight = Number((Number(item.check_weight) - item.weight).toFixed(2) || 0)
    item.difference_length = Number((Number(item.check_length) - item.length).toFixed(2) || 0)
  })

  tablesRef.value?.TableRef.loadData(splitList.value)
}

//  点击细码统计
function statisticiansClick(row: any, rowIndex: number) {
  if (row?.is_stock) {
    showFineSizeDialog(row, rowIndex)
    return
  }
  showFSRepertoryDialog(row, rowIndex)
}

function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex

  FineSizeAddRef.value.showDialog(row, { warehouse_id: state.formInline.warehouse_id })
}
function showFSRepertoryDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex

  FineSizeInventoryProfitRepertoryAddRef.value.showDialog(
    {
      ...row,
      in_roll: row.check_roll,
    },
    { warehouse_id: state.formInline.warehouse_id },
  )
}
// const router = useRouter()
const formRef = ref()
// 提交所有数据
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = addProductCheckOrder()
function submitAddAllData() {
  // 校验成品信息
  // if (!finishProductionOptions.datalist.length)
  //   return ElMessage.error('请添加成品信息')

  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const query = {
        ...state.formInline,
        warehouse_id: state.formInline.warehouse_id || 0,
        warehouse_manager_id: state.formInline.warehouse_manager_id || 0,
        warehouse_bin_id: Number(state.formInline.warehouse_bin_id) || 0,
        check_time: formatDate(state.formInline.check_time),
        item_data: [],
      }
      const errMap = await tablesRef.value?.TableRef.validate(true)
      if (errMap)
        return ElMessage.error((Object.values(errMap) as any)[0][0].rules[0].message)

      if (finishProductionOptions.datalist.length) {
        for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
          // 如果是从库存取的数据，按照之前的写法，不需要校验成品信息
          if (finishProductionOptions.datalist[i]?.is_stock)
            continue

          if (!finishProductionOptions.datalist[i].product_code)
            return ElMessage.error('成品编号为必填项')

          if (!finishProductionOptions.datalist[i].product_name)
            return ElMessage.error('成品名称为必填项')

          if (!finishProductionOptions.datalist[i].product_color_id)
            return ElMessage.error('成品色号为必填项')

          // if (!finishProductionOptions.datalist[i].product_level_id) {
          //   return ElMessage.error('成品等级为必填项')
          // }

          if (Number(finishProductionOptions.datalist[i].check_roll) && !finishProductionOptions.datalist[i].is_stock) {
            let FSRoll = finishProductionOptions.datalist[i].item_data.reduce((pre: any, val: any) => pre + Number(val.check_roll), 0)
            FSRoll = Number(FSRoll.toFixed(2))
            if (FSRoll !== Number(finishProductionOptions.datalist[i].check_roll))
              return ElMessage.error('盘点匹数和录入细码匹数不匹配')
          }
        }
        query.item_data = finishProductionOptions.datalist.map((item: any) => {
          const item_data = item.item_data.map((v: any) => {
            return {
              ...v,
              volume_number: Number(v.volume_number) || 0,
              warehouse_bin_id: Number(v.warehouse_bin_id) || 0,
              check_roll: formatTwoDecimalsMul(Number(v.check_roll)),
              check_weight: formatWeightMul(Number(v.check_weight)),
              check_length: v.check_length ? formatLengthMul(Number(v.check_length)) : 0,
              roll: formatTwoDecimalsMul(Number(v.roll)),
              base_unit_weight: formatWeightMul(Number(v.base_unit_weight)),
              weight_error: formatWeightMul(Number(v.weight_error)),
              paper_tube_weight: formatWeightMul(Number(v.paper_tube_weight)),
              length: formatLengthMul(Number(v.length)),
              weight: formatWeightMul(Number(v.weight)),
              stock_product_id: item.stock_product_id || 0,
              dyelot_number: item.dyelot_number,
              is_stock: v.is_stock || false,
            }
          })
          return {
            ...item,
            stock_product_id: item.stock_product_id || 0,
            is_stock: item.is_stock || false,
            customer_id: item.customer_id || 0,
            product_level_id: item.product_level_id || 0,
            roll: formatTwoDecimalsMul(item.roll),
            weight: formatWeightMul(item.weight),
            length: formatLengthMul(item.length),
            check_roll: formatTwoDecimalsMul(Number(item.check_roll)),
            check_weight: formatWeightMul(item.check_weight),
            check_length: formatLengthMul(item.check_length),
            item_data,
          }
        })
      }

      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        routerList.push({
          name: 'FpStockCheckOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

// 色号和颜色联动
function setFinishProductColor(item: any, row: any) {
  row.finishProductionColorId = item?.id
  row.product_color_id = item?.id
  row.product_color_name = item?.product_color_name
  row.product_color_code = item?.product_color_code
}

function showPYDialog() {
  AccordingLibAddRef.value.state.showModal = true
}

const elPaginationConfig = computed(() => ({
  defaultPageSize: 500,
  page: page.value,
  pageSizes: [50, 100, 500, 1000],
  size: size.value,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: finishProductionOptions.datalist.length,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))
function computedList() {
  const { page, size } = toRefs(elPaginationConfig.value)
  splitList.value = finishProductionOptions.datalist.slice((page.value - 1) * size.value, page.value * size.value)
  nextTick(() => {
    tablesRef.value?.TableRef.loadData(splitList.value)
  })
}
function clearCustomer(item: any) {
  state.defaultData.customer_id = item?.default_customer_id || 0
  state.defaultData.customer_name = item?.default_customer_name || ''
  state.formInline.warehouse_id = item?.default_physical_warehouse || 0
}

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  if (!res)
    return
  state.formInline.sale_system_id = res.default_sale_system_id
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                default-status
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="clearCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents v-model="state.formInline.warehouse_id" api="GetPhysicalWarehouseDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓位:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.warehouse_bin_id"
                :disabled="state.formInline.warehouse_id ? false : true"
                :query="{ physical_warehouse_id: state.formInline.warehouse_id }"
                api="GetPhysicalWarehouseBinListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="盘点日期:">
          <template #content>
            <el-form-item prop="check_time">
              <el-date-picker v-model="state.formInline.check_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.warehouse_manager_id"
                :query="{ duty: EmployeeType.warehouseManager }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.formInline.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button type="primary" @click="showPYDialog">
        盘盈新增
      </el-button>
      <el-button type="primary" @click="showLibDialog">
        根据库存添加
      </el-button>
    </template>
    <GridTable
      v-show="finishProductionOptions.datalist.length"
      ref="tablesRef"
      :columns="finishProductionOptions.columnList"
      :data="splitList"
      :config="finishProductionOptions.tableConfig"
      show-pagition
      :el-pagination-config="elPaginationConfig"
      height="600px"
    />
    <div v-show="!finishProductionOptions.datalist.length" class="no_data" style="color: #999">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <!-- 参考这个组件 -->
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <FineSizeInventoryProfitRepertoryAdd ref="FineSizeInventoryProfitRepertoryAddRef" @handle-sure="finishProductionOptions.handleFSRepertory" />
  <AccordingLibAdd ref="AccordingLibAddRef" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
  <AccordingRepertoryAdd ref="AccordingRepertoryAddRef" :warehouse_id="state.formInline.warehouse_id" @handle-sure="finishProductionOptions.handleRepertorySure" />
</template>

<style lang="scss" scoped>
// .el-form-item {
//   width: 100%;
// }
.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
