import { useRequest } from '@/use/useRequest'

// 编辑
export const updateFpmArrangeOrder = () => {
  return useRequest({
    url: `/admin/v1/product/fpmArrangeOrder/updateFpmArrangeOrder`,
    method: 'put',
  })
}

// 获取列表
export const getFpmArrangeOrderList = () => {
  return useRequest({
    url: `/admin/v1/product/fpmArrangeOrder/getFpmArrangeOrderList`,
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取详情
export const getFpmArrangeOrder = () => {
  return useRequest({
    url: `/admin/v1/product/fpmArrangeOrder/getFpmArrangeOrder`,
    method: 'get',
  })
}
// 确认出仓
export const outFpmArrangeOrder = () => {
  return useRequest({
    url: `/admin/v1/product/fpmArrangeOrder/outFpmArrangeOrder`,
    method: 'put',
  })
}

// 作废
export const updateFpmArrangeOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmArrangeOrder/updateFpmArrangeOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmArrangeOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmArrangeOrder/updateFpmArrangeOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmArrangeOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmArrangeOrder/updateFpmArrangeOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmArrangeOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmArrangeOrder/updateFpmArrangeOrderStatusWait',
    method: 'put',
  })
}

// 获取所有出货类型枚举
export const GetWarehouseGoodOutTypeEnum = () => {
  return useRequest({
    url: `/admin/v1/product/enum/getWarehouseGoodOutTypeEnum`,
    method: 'get',
  })
}
