<script setup lang="ts">
import { computed, h, nextTick, ref, watch } from 'vue'
import { ElLink, ElMessage, ElSpace, ElSwitch, ElTag } from 'element-plus'
import currency from 'currency.js'
import GridTable from '@/components/GridTable/index.vue'
import { formatPriceDiv, formatPriceMul, formatWeightDiv, formatWeightMul, sumNum, sumTotal } from '@/common/format'
import { SaleProductPlanOrderProductPushDown, UpdatePushStatus, getSaleProductPlanOrder } from '@/api/productSalePlan'
import SelectStockFabricGrey from '@/pages/saleManagement/components/SelectStockFabricGrey.vue'
import SelectStockFabric from '@/pages/saleManagement/components/SelectStockFabric.vue'
import { getStockProductDropdownList } from '@/api/productOutStock'
import { getGfmWarehouseSumList } from '@/api/greyFabricPurchaseReturn'
import { SaleTypeEnum } from '@/enum'
import FildCard from '@/components/FildCard.vue'
import { BusinessUnitIdEnum } from '@/common/enum'
import SelectComponents from '@/components/SelectComponents/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { getFilterData } from '@/common/util'

interface Props {
  modelValue: boolean
  id: number
  planType: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  id: 0,
  planType: -1,
})
const showModal = defineModel({
  default: false,
})
// const showModal = ref(false)
const list = ref<any[]>([])
const TablesRef = ref()
const state = ref({
  formData: {
    warehouse_id: '',
  },
  info: {
    warehouse_name: '',
  },
})

const { fetchData, data, loading, success, msg } = getSaleProductPlanOrder()
const SelectStockFabricRef = ref()

async function getData() {
  if (!props.id)
    return

  const query = {
    id: props.id,
  }
  await fetchData(getFilterData(query))
  if (success.value) {
    list.value = data.value.item_data.map((item: any, index: number) => {
      if (!item.push_status)
        item.push_status = 1

      item.pushed_roll = 0
      item.pushed_weight = 0
      item.selected = true
      if (
        item.plan_type === SaleTypeEnum.FinishProduct
        && item.push_status === 1
        && (item.wait_push_roll !== 0 || item.wait_push_weight !== 0)
      )
        handleAutoEnterStock(item, index)

      // 坯布-自动获取库存
      if (
        item.plan_type === SaleTypeEnum.GreyFabric
        && state.value.formData.warehouse_id
        && item.push_status === 1
        && (item.roll !== 0 || item.weight !== 0)
      )
        handleAutoEnterStockGrey(item, index)

      return item
    })
    await nextTick(() => {
      if (TablesRef.value)
        TablesRef.value.multipleSelection = list.value
    })
  }
  else {
    ElMessage.error(msg.value)
  }
}

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      list.value = []
      getColumnList()
      getData()
    }
  },
)

function handSureStock(resultList: any, rowIndex: number) {
  const returnPlan = resultList.pop()

  list.value[rowIndex].pushed_roll = returnPlan.roll
  list.value[rowIndex].pushed_weight = returnPlan.weight
  list.value[rowIndex].returnPlanList = resultList
  TablesRef.value.TableRef?.updateFooter()
}

function handCancel() {
  showModal.value = false
}

const {
  fetchData: pushDownApi,
  success: pushDownSuccess,
  msg: pushMsg,
} = SaleProductPlanOrderProductPushDown()

async function handleSure() {
  if (props.planType === SaleTypeEnum.GreyFabric && !state.value.formData.warehouse_id)
    return ElMessage.error('请选择出货单位')

  if (TablesRef.value.multipleSelection.length === 0)
    return ElMessage.error('请选择需要下推的数据')

  if (TablesRef.value.multipleSelection.some((e: any) => Number(e.pushed_weight) === 0 && Number(e.pushed_roll) === 0))
    return ElMessage.error('请选择出货数量或出货匹数库存')

  const item_data: any[] = []
  for (const item of TablesRef.value.multipleSelection) {
    if (!item.returnPlanList)
      continue

    item.returnPlanList.forEach((it: any) => {
      const payload: any = {
        id: item.id,
        push_status: item.push_status,
        pushed_roll: formatPriceMul(it.use_roll),
        pushed_weight: formatWeightMul(it.use_weight),
        stock_product_id: it.stock_product_id,
      }
      if ('dyelot_number' in it)
        payload.dyelot_number = it.dyelot_number
      if (props.planType === SaleTypeEnum.GreyFabric)
        payload.warehouse_sum_id = it.id

      item_data.push(payload)
    })
  }

  await pushDownApi({
    item_data,
    order_id: props.id,
    warehouse_id: props.planType === SaleTypeEnum.GreyFabric ? state.value.formData.warehouse_id : data.value.warehouse_id,
  })
  if (pushDownSuccess.value) {
    ElMessage.success('下推成功')
    handCancel()
  }
  else {
    ElMessage.error(pushMsg.value)
  }
}

// #region 选取库存--坯布销售
const SelectStockFabricGreyRef = ref()
function handSureStockGrey(resultList: any, rowIndex: number) {
  const returnPlan = resultList.pop()

  list.value[rowIndex].pushed_roll = returnPlan.roll
  list.value[rowIndex].pushed_weight = returnPlan.weight
  list.value[rowIndex].returnPlanList = resultList
  TablesRef.value.TableRef?.updateFooter()
}
// #endregion

const columnList_config = computed(() => ({
  showSeq: true,
  // showSpanHeader: true,
  checkboxConfig: {
    checkField: 'selected',
  },
  filterConfig: {
    showIcon: false,
  },
  headerCellClassName({ column }) {
    if (['weight', 'wait_push_weight', 'pushed_weight'].includes(column.field))
      return 'col-purple'
    else if (['roll', 'wait_push_roll', 'pushed_roll'].includes(column.field))
      return 'col-yellow'

    return null
  },
  loading: loading.value,
  showCheckBox: true,
  scrollY: { enabled: true },
  footerMethod: FooterMethod,
}))

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['weight', 'wait_push_weight'].includes(column.field))
        return formatWeightDiv(sumNum(data, column.field))

      if (['roll', 'wait_push_roll'].includes(column.field))
        return formatPriceDiv(sumNum(data, column.field))

      if (['pushed_weight', 'pushed_roll'].includes(column.field))
        return sumNum(data, column.field)

      return null
    }),
  ]
}

const {
  fetchData: updatePushStatus,
  success: updateSuccess,
  msg: updateMsg,
  loading: updateLoading,
} = UpdatePushStatus()

const allColumnList = [
  {
    field: 'product_code',
    title: '成品编号',
    sortable: true,
    saleType: [SaleTypeEnum.FinishProduct],
  },
  {
    field: 'product_name',
    title: '成品名称',
    sortable: true,
    saleType: [SaleTypeEnum.FinishProduct],
  },
  {
    field: 'product_color_code',
    title: '色号',
    sortable: true,
    saleType: [SaleTypeEnum.FinishProduct],
  },
  {
    field: 'product_color_name',
    title: '颜色',
    sortable: true,
    saleType: [SaleTypeEnum.FinishProduct],
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'needle_size',
    title: '针寸数',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'total_needle_size',
    title: '总针数',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'gray_fabric_color_name',
    title: '坯织颜色',
    saleType: [SaleTypeEnum.GreyFabric],
  },
  {
    field: 'weight',
    title: '计划数量',
    slots: {
      default: ({ row }: any) => {
        return formatWeightDiv(row.weight)
      },
    },
  },
  {
    field: 'wait_push_weight',
    title: '待出货数量',
    slots: {
      default: ({ row }: any) => {
        return formatWeightDiv(row.wait_push_weight)
      },
    },
  },
  {
    field: 'pushed_weight',
    title: '本次出货数量',
    slots: {
      default: ({ row, rowIndex }) => {
        return h(
          ElLink,
          {
            type: 'primary',
            underline: false,
            onClick: () => {
              openDialog(row, rowIndex)
            },
          },
          row.pushed_weight
            ? row.isAutoEnter
              ? h(ElSpace, { size: 8 }, [
                row.pushed_weight,
                h(ElTag, { type: 'success' }, '已自动录入'),
              ])
              : row.pushed_weight
            : '选取库存',
        )
      },
    },
  },
  {
    field: 'roll',
    title: '计划匹数',
    slots: {
      default: ({ row }: any) => {
        return formatPriceDiv(row.roll)
      },
    },
  },
  {
    field: 'wait_push_roll',
    title: '待出货匹数',
    slots: {
      default: ({ row }: any) => {
        return formatPriceDiv(row.wait_push_roll)
      },
    },
  },
  {
    field: 'pushed_roll',
    title: '本次出货匹数',
    slots: {
      default: ({ row, rowIndex }) => {
        return h(
          ElLink,
          {
            type: 'primary',
            underline: false,
            onClick: () => {
              openDialog(row, rowIndex)
            },
          },
          row.pushed_roll
            ? row.isAutoEnter
              ? h(ElSpace, { size: 8 }, [
                row.pushed_roll,
                h(ElTag, { type: 'success' }, '已自动录入'),
              ])
              : row.pushed_roll
            : '选取库存',
        )
      },
    },
  },
  {
    field: 'push_status_name',
    title: '下推状态',
    slots: {
      default: ({ row }) => {
        return h(
          ElSwitch,
          {
            'inlinePrompt': true,
            'modelValue': row.push_status,
            'update:modelValue': (val: any) => {
              row.push_status = val
            },
            'loading': updateLoading.value,
            'beforeChange': async () => {
              const target = row.push_status === 1 ? 2 : 1
              await updatePushStatus({
                id: row.id,
                push_status: target,
              })
              if (updateSuccess.value) {
                row.push_status = target
                return true
              }
              else {
                ElMessage.error(updateMsg.value)
                return false
              }
            },
            'activeValue': 2,
            'inactiveValue': 1,
            'activeText': '已完成',
            'inactiveText': '未完成',
          },
          row.push_status_name,
        )
      },
    },
  },
]
const columnList = ref(allColumnList)

function getColumnList() {
  columnList.value = allColumnList.filter((item: any) => !item?.saleType || item?.saleType.includes(props.planType))
}

const { fetchData: getStockProductDropdownListApi }
  = getStockProductDropdownList()

async function handleAutoEnterStock(row: any, rowIndex: number) {
  const res = await getStockProductDropdownListApi({
    product_id: row.product_id,
    product_color_id: row.product_color_id,
    product_level_id: row.product_level_id,
    available_only: true,
  })

  if (res.success) {
    if (res.data.list?.length === 0)
      return

    let totalRoll = 0
    let totalWeight = 0
    res.data.list?.forEach((item: any) => {
      // 汇总库存标识 回显使用
      item.choose_sign = 2
      const arrRoll = sumTotal(res.data.list, 'use_roll')
      const arrWeight = sumTotal(res.data.list, 'use_weight')
      // available_roll 可用匹数 horsepower 计划匹数 arrRoll 已分配匹数
      const planRoll = formatPriceDiv(row.wait_push_roll)
      const planWeight = formatWeightDiv(row.wait_push_weight)
      // 剩下的匹数
      const remainingRoll = currency(planRoll).subtract(arrRoll).value
      // 剩下的数量
      const remainingWeight = currency(planWeight).subtract(arrWeight).value
      item.use_roll = (
        formatPriceDiv(item.available_roll) >= remainingRoll
          ? remainingRoll
          : formatPriceDiv(item.available_roll)
      ).toFixed(2)
      item.use_weight = (
        formatWeightDiv(item.available_weight) >= remainingWeight
          ? remainingWeight
          : formatWeightDiv(item.available_weight)
      ).toFixed(2)
      totalRoll = currency(arrRoll).add(item.use_roll).value
      totalWeight = currency(arrWeight).add(item.use_weight).value
    })

    const autoEnterList = res.data.list.filter(
      (item: any) => Number(item.use_weight) || Number(item.use_roll),
    )
    autoEnterList.push({
      roll: totalRoll,
      weight: totalWeight,
    })
    row.isAutoEnter = true

    handSureStock(autoEnterList, rowIndex)
  }
}

// #region  坯布-自动录入库存
const { fetchData: getGfmWarehouseSumListApi }
  = getGfmWarehouseSumList()

async function handleAutoEnterStockGrey(row: any, rowIndex: number) {
  const res = await getGfmWarehouseSumListApi({
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    available_only: true,
    warehouse_id: state.value.formData.warehouse_id,
  })

  if (res.success) {
    if (!res.data?.list || res.data.list?.length === 0) {
      row.isAutoEnter = false
      handSureStockGrey([{
        roll: 0,
        weight: 0,
      }], rowIndex)
      return
    }

    let totalRoll = 0
    let totalWeight = 0
    res.data.list?.forEach((item: any) => {
      // 汇总库存标识 回显使用
      item.choose_sign = 2
      const arrRoll = sumTotal(res.data.list, 'use_roll')
      const arrWeight = sumTotal(res.data.list, 'use_weight')
      // stock_roll 可用匹数，state.info.roll 计划匹数, arrRoll 已分配匹数
      const planRoll = formatPriceDiv(row.roll)
      const planWeight = formatWeightDiv(row.weight)
      // 剩下的匹数
      const remainingRoll = currency(planRoll).subtract(arrRoll).value
      // 剩下的数量
      const remainingWeight = currency(planWeight).subtract(arrWeight).value
      item.use_roll = (
        formatPriceDiv(item.stock_roll) >= remainingRoll
          ? remainingRoll
          : formatPriceDiv(item.stock_roll)
      ).toFixed(2)
      item.use_weight = (
        formatWeightDiv(item.stock_weight) >= remainingWeight
          ? remainingWeight
          : formatWeightDiv(item.stock_weight)
      ).toFixed(2)
      totalRoll = currency(arrRoll).add(item.use_roll).value
      totalWeight = currency(arrWeight).add(item.use_weight).value
    })

    const autoEnterList = res.data.list.filter(
      (item: any) => Number(item.use_weight) || Number(item.use_roll),
    )
    autoEnterList.push({
      roll: totalRoll,
      weight: totalWeight,
    })
    row.isAutoEnter = true

    handSureStockGrey(autoEnterList, rowIndex)
  }
}
// #endregion

// 选择出货单位
function changeWarehouse(item: any) {
  state.value.info.warehouse_name = item.name
  list.value.forEach((e: any, i) => {
    handleAutoEnterStockGrey(e, i)
  })
}

function openDialog(row: any, rowIndex: number) {
  if (props.planType === SaleTypeEnum.GreyFabric && !state.value.formData.warehouse_id) {
    ElMessage.error('请选择出货单位')
    return
  }
  const SelectRef = row.plan_type === SaleTypeEnum.FinishProduct ? SelectStockFabricRef : SelectStockFabricGreyRef
  SelectRef.value.state.showModal = true
  SelectRef.value.state.rowIndex = rowIndex
  SelectRef.value.state.info = {
    code: row.code,
    name: row.name,
    color_name: row.color_name,
    horsepower: formatPriceDiv(row.roll),
    plan_roll: formatPriceDiv(row.planed_roll),
    product_color_code: row.product_color_code,
    color_code: row.product_color_code,
    customer_name: row.customer_name,
    product_code: row.product_code,
    product_name: row.product_name,
    needWeight: row.weight,
    product_color_id: row.product_color_id,
    // customer_id: row.customer_id,
    finish_product_width: row.finish_product_width,
    finish_product_gram_weight: row.finish_product_gram_weight,
    roll: formatPriceDiv(row.roll),
    weight: formatWeightDiv(row.weight),
  }

  SelectRef.value.filterData.product_id = row.product_id

  // 不默认带出客户
  // SelectStockFabricRef.value.row.filterData.customer_id = row.customer_id
  SelectRef.value.filterData.product_level_id = row.product_level_id
  SelectRef.value.filterData.finish_product_width
    = row.finish_product_width
  SelectRef.value.filterData.finish_product_gram_weight
    = row.finish_product_gram_weight
  SelectRef.value.filterData.product_color_id = row.product_color_id

  if (row.plan_type === SaleTypeEnum.GreyFabric) {
    // 坯布销售
    SelectRef.value.filterData.warehouse_id = state.value.formData.warehouse_id
    SelectRef.value.filterData.grey_fabric_code = row.grey_fabric_code
    SelectRef.value.filterData.grey_fabric_name = row.grey_fabric_name
    SelectRef.value.state.warehouse_name = state.value.info.warehouse_name
  }
  else if (row.plan_type === SaleTypeEnum.FinishProduct) {
    // 成品销售
    SelectRef.value.filterData.radioValue = '2'
  }

  if (row.returnPlanList)
    SelectRef.value.state.multipleSelection = row.returnPlanList
}
</script>

<template>
  <vxe-modal
    v-model="showModal"
    show-footer
    title="下推销售通知"
    width="80vw"
    height="80vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    :z-index="10"
    @hide="handCancel"
  >
    <FildCard :tool-bar="false" class="flex flex-col h-full">
      <div v-if="planType === SaleTypeEnum.GreyFabric" class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="出货单位:">
          <template #content>
            <SelectComponents
              v-model="state.formData.warehouse_id"
              api="BusinessUnitSupplierEnumAll"
              :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory }"
              label-field="name"
              value-field="id"
              :clearable="false"
              @change-value="changeWarehouse"
            />
          </template>
        </DescriptionsFormItem>
      </div>
      <GridTable
        ref="TablesRef"
        :columns="columnList"
        :data="list"
        :config="columnList_config"
        height="100%"
      />
    </FildCard>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
  <!--  根据库存添加 --成品 -->
  <SelectStockFabric
    ref="SelectStockFabricRef"
    @handle-sure="handSureStock"
  />
  <!--  根据库存添加 --坯布 -->
  <SelectStockFabricGrey
    ref="SelectStockFabricGreyRef"
    @handle-sure="handSureStockGrey"
  />
</template>

<style scoped>
::v-deep(.vxe-grid .vxe-header--column.col-purple) {
  background-color: #c2c1ff;
}
::v-deep(.vxe-grid .vxe-header--column.col-yellow) {
  background-color: #fff7ca;
}
</style>
