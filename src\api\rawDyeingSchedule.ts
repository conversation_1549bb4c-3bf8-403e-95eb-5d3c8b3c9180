import { useRequest } from '@/use/useRequest'

// 获取列表
export const rmmDyeOrderItemSituationlist = () => {
  return useRequest({
    url: '/admin/v1/raw_material/rmmDyeOrderItemSituation/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export const rmmDyeOrderItemSituationput = () => {
  return useRequest({
    url: '/admin/v1/raw_material/rmmDyeOrderItemSituation',
    method: 'put',
  })
}

export const use_yarn_listby_src_id = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/in/use_yarn_list/by_src_id',
    method: 'get',
  })
}
