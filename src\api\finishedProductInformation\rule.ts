export interface ProductGetFinishProductData {
  /** 经纬度 */
  center?: string
  /** 创建时间 */
  create_time?: string
  /** 创建人 */
  creator_name?: string
  /** 创建人 */
  creator_id?: number
  /** 密度 */
  density?: string
  /** 染厂跟单用户ID */
  dye_factory_order_follower_id?: number
  /** 染厂跟单用户ID转义 */
  dye_factory_order_follower_name?: string
  /** 染整工艺 */
  dyeing_craft?: string
  /** 染损(两位小数) */
  dyeing_loss?: number
  /** 成品编号 */
  finish_product_code?: string
  /** 成品工艺 */
  finish_product_craft?: string
  /** 成品全称 */
  finish_product_full_name?: string
  /** 成品克重 */
  finish_product_gram_weight?: string
  /** 成品成分 */
  finish_product_ingredient?: string
  /** 成品等级 */
  finish_product_level_id?: number
  /** 成品等级名称 */
  finish_product_level_name?: string
  /** 成品名称 */
  finish_product_name?: string
  /** 成品幅宽 */
  finish_product_width?: string
  /** 坯布编号 */
  grey_fabric_code?: string
  /** 坯布信息ID */
  grey_fabric_id?: number
  /** 坯布名称 */
  grey_fabric_name?: string
  /** 记录ID */
  id?: number
  /** 是否启用色卡编号 */
  is_color_card?: boolean
  /** 长度转数量(公斤/米) */
  length_to_weight_rate?: number
  /** 计量单位id */
  measurement_unit_id?: number
  /** 计量单位id转义 */
  measurement_unit_name?: string
  /** 纸筒重量(公斤) */
  paper_tube_weight?: number
  /** 备注 */
  remark?: string
  /** 尺寸 */
  size?: string
  /** 标准数量(公斤) */
  standard_weight?: number
  /** 状态 */
  status?: any
  /** 状态 */
  status_name?: string
  /** 存放区域 */
  storage_area?: string
  /** 纹理图片URL(单张) */
  texture_url?: string
  /** 手感风格 */
  touch_style?: string
  /** 布种类型编号 */
  type_grey_fabric_code?: string
  /** 布种类型id */
  type_grey_fabric_id?: number
  /** 布种类型名称 */
  type_grey_fabric_name?: string
  /** 修改时间 */
  update_time?: string
  /** 修改人 */
  update_user_name?: string
  /** 修改人 */
  updater_id?: number
  /** 仓库id */
  warehouse_id?: number
  /** 仓库id转义 */
  warehouse_name?: string
  /** 空差数量(公斤) */
  weight_error?: number
}
