<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { useMeasureWeightService } from './index'

// 测试配置
const testConfig = reactive({
  enabled: true,
  stableCount: 3,
  precision: 2,
})

// 模拟重量数据
const simulatedWeights = ref([
  10.15,
  10.16,
  10.15,
  10.15,
  10.15, // 应该在第5次时稳定
  15.23,
  15.24,
  15.23,
  15.23,
  15.23, // 切换到新重量
  20.00,
  20.01,
  20.00,
  20.00,
  20.00, // 再次切换
])

// 创建称重服务实例
const weightService = ref<any>(null)
const currentIndex = ref(0)
const isRunning = ref(false)

// 状态显示
const currentWeight = ref(0)
const isStable = ref(false)
const currentFrameData = ref(0)
const logMessages = ref<string[]>([])

// 初始化服务
function initService() {
  const stabilityConfig = {
    enabled: testConfig.enabled,
    stableCount: testConfig.stableCount,
    precision: testConfig.precision,
  }

  weightService.value = useMeasureWeightService(undefined, stabilityConfig)

  // 监听状态变化
  updateStatus()
}

// 更新状态显示
function updateStatus() {
  if (weightService.value) {
    currentWeight.value = Number.parseFloat(weightService.value.currentWeight.value)
    isStable.value = weightService.value.isStable.value
    currentFrameData.value = weightService.value.currentFrameData.value
    logMessages.value = [...weightService.value.logMessages.value]
  }
}

// 模拟重量数据输入
function simulateWeightInput(weight: number) {
  if (weightService.value) {
    // 模拟串口数据格式
    const weightStr = weight.toFixed(2)
    const mockData = `${weightStr}kg`

    // 直接调用解析函数（模拟串口数据接收）
    weightService.value.parseWeightData?.(mockData)

    // 更新显示状态
    updateStatus()
  }
}

// 开始自动测试
function startAutoTest() {
  if (isRunning.value)
    return

  isRunning.value = true
  currentIndex.value = 0

  const interval = setInterval(() => {
    if (currentIndex.value >= simulatedWeights.value.length) {
      stopAutoTest()
      return
    }

    const weight = simulatedWeights.value[currentIndex.value]
    simulateWeightInput(weight)
    currentIndex.value++
  }, 1000) // 每秒一个数据

  // 保存定时器引用以便停止
  ;(window as any).testInterval = interval
}

// 停止自动测试
function stopAutoTest() {
  isRunning.value = false
  if ((window as any).testInterval) {
    clearInterval((window as any).testInterval)
    ;(window as any).testInterval = null
  }
}

// 重置测试
function resetTest() {
  stopAutoTest()
  currentIndex.value = 0
  if (weightService.value) {
    weightService.value.clearLogMessages?.()
    updateStatus()
  }
}

// 更新配置
function updateConfig() {
  if (weightService.value) {
    const stabilityConfig = {
      enabled: testConfig.enabled,
      stableCount: testConfig.stableCount,
      precision: testConfig.precision,
    }

    weightService.value.updateStabilityConfig?.(stabilityConfig)
    updateStatus()
  }
}

// 手动输入重量
const manualWeight = ref(10.15)
function inputManualWeight() {
  simulateWeightInput(manualWeight.value)
}

// 计算进度
const progress = computed(() => {
  if (simulatedWeights.value.length === 0)
    return 0
  return Math.round((currentIndex.value / simulatedWeights.value.length) * 100)
})

// 初始化
initService()
</script>

<template>
  <div class="stability-test">
    <h2>重量稳定性检测测试</h2>

    <!-- 配置区域 -->
    <el-card title="稳定性配置">
      <el-form :model="testConfig" label-width="120px">
        <el-form-item label="启用稳定检测:">
          <el-switch v-model="testConfig.enabled" />
        </el-form-item>

        <el-form-item label="稳定次数:">
          <el-input-number
            v-model="testConfig.stableCount"
            :min="1"
            :max="10"
            style="width: 200px"
          />
          <span class="ml-2 text-gray-500">连续相同重量的次数</span>
        </el-form-item>

        <el-form-item label="重量精度:">
          <el-input-number
            v-model="testConfig.precision"
            :min="0"
            :max="4"
            style="width: 200px"
          />
          <span class="ml-2 text-gray-500">小数点后位数</span>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="updateConfig">
            更新配置
          </el-button>
          <el-button @click="initService">
            重新初始化
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 状态显示 -->
    <el-card title="当前状态" class="mt-4">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="status-item">
            <div class="label">
              当前重量
            </div>
            <div class="value">
              {{ currentWeight.toFixed(testConfig.precision) }} kg
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="label">
              稳定状态
            </div>
            <div class="value">
              <el-tag :type="isStable ? 'success' : 'warning'">
                {{ isStable ? '稳定' : '不稳定' }}
              </el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="label">
              输出重量
            </div>
            <div class="value">
              {{ currentFrameData.toFixed(testConfig.precision) }} kg
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="status-item">
            <div class="label">
              测试进度
            </div>
            <div class="value">
              {{ progress }}%
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 控制区域 -->
    <el-card title="测试控制" class="mt-4">
      <el-space>
        <el-button
          type="primary"
          :disabled="isRunning"
          @click="startAutoTest"
        >
          开始自动测试
        </el-button>
        <el-button
          type="danger"
          :disabled="!isRunning"
          @click="stopAutoTest"
        >
          停止测试
        </el-button>
        <el-button @click="resetTest">
          重置测试
        </el-button>
      </el-space>

      <div class="manual-input mt-4">
        <el-space>
          <span>手动输入重量:</span>
          <el-input-number
            v-model="manualWeight"
            :precision="testConfig.precision"
            :step="0.01"
            style="width: 150px"
          />
          <el-button @click="inputManualWeight">
            输入
          </el-button>
        </el-space>
      </div>
    </el-card>

    <!-- 测试数据预览 -->
    <el-card title="测试数据序列" class="mt-4">
      <div class="test-data">
        <span
          v-for="(weight, index) in simulatedWeights"
          :key="index"
          class="weight-item"
          :class="{
            current: index === currentIndex,
            processed: index < currentIndex,
          }"
        >
          {{ weight.toFixed(testConfig.precision) }}
        </span>
      </div>
    </el-card>

    <!-- 日志显示 -->
    <el-card title="检测日志" class="mt-4">
      <div class="log-container">
        <div v-for="(msg, index) in logMessages" :key="index" class="log-item">
          {{ msg }}
        </div>
        <div v-if="logMessages.length === 0" class="no-logs">
          暂无日志信息
        </div>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.stability-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.status-item {
  text-align: center;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;

  .label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
}

.test-data {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .weight-item {
    padding: 8px 12px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    border: 2px solid transparent;

    &.current {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
      font-weight: bold;
    }

    &.processed {
      background: #f6ffed;
      border-color: #52c41a;
      color: #52c41a;
    }
  }
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.no-logs {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
