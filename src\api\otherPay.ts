import { useRequest } from '@/use/useRequest'

// 获取列表
export const otherlist = () => {
  return useRequest({
    url: '/admin/v1/payable/other/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const otherdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/other/detail',
    method: 'get',
  })
}

// 审核
export const otherpass = () => {
  return useRequest({
    url: '/admin/v1/payable/other/pass',
    method: 'put',
  })
}

// 消审
export const othercancel = () => {
  return useRequest({
    url: '/admin/v1/payable/other/cancel',
    method: 'put',
  })
}

// 驳回
export const otherreject = () => {
  return useRequest({
    url: '/admin/v1/payable/other/reject',
    method: 'put',
  })
}

// 作废
export const othervoid = () => {
  return useRequest({
    url: '/admin/v1/payable/other/void',
    method: 'put',
  })
}

//  更新
export const otherput = () => {
  return useRequest({
    url: '/admin/v1/payable/other',
    method: 'put',
  })
}

//  新建
export const otherpost = () => {
  return useRequest({
    url: '/admin/v1/payable/other',
    method: 'post',
  })
}
