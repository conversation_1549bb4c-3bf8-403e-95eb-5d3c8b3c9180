import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmSaleOutOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/getFpmSaleOutOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmSaleOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/addFpmSaleOutOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmSaleOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/getFpmSaleOutOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmSaleOutOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmSaleOutOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmSaleOutOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmSaleOutOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmSaleOutOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrder',
    method: 'put',
  })
}

// 获取加工厂下拉
export const FactoryLogisticsEnum = () => {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/logistics_enum_list',
    method: 'get',
  })
}
