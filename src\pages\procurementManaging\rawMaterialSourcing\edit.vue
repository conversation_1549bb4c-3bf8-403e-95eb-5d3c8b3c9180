<script lang="ts" setup name="RawMaterialSourcingEdit">
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import AddressAdd from './components/AddressAdd/index.vue'
import SelectProductionNotice from './components/SelectProductionNotice/index.vue'
import SelectRawMaterial from './components/SelectRawMaterial/index.vue'
import { isConfigured } from './utils'
import { PurchaseOrderRawMaterialDetail, PurchaseOrderRawMaterialEdit } from '@/api/rawMaterialSourcing'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceDiv, formatPriceMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
const router = useRoute()
const state = reactive<any>({
  form: {
    sale_system_id: '',
    supplier_id: '',
    receipt_unit_id: null,
    purchase_date: '',
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    items: [] as any[],
    fapiao_title_id: 0,
    is_with_tax_rate: false,
    tax_rate: 0,
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    purchase_date: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
    receipt_date: '',
    fapiao_title: '',
    remark: '',
  },
})
const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

onMounted(async () => {
  getData()
})
const { fetchData: detailFetch, data: detalData, success } = PurchaseOrderRawMaterialDetail()
async function getData() {
  await detailFetch({
    id: router.params.id,
  })
  if (success.value) {
    state.form = detalData.value
    state.form.receipt_unit_id = detalData.value.receipt_unit_id ? detalData.value.receipt_unit_id : null
    state.form.tax_rate = formatPriceDiv(detalData.value.tax_rate)
    state.form.is_with_tax_rate = detalData.value.is_with_tax_rate
    state.tableData = detalData.value.items || []
    detalData.value?.items?.map((item) => {
      item.whole_piece_weight = formatWeightDiv(item.whole_piece_weight || 0)
      item.whole_piece_count = formatPriceDiv(item.whole_piece_count || 0)
      item.bulk_piece_count = formatPriceDiv(item.bulk_piece_count || 0)
      item.whole_weight = formatWeightDiv(item.whole_weight || 0)
      item.bulk_piece_weight = formatWeightDiv(item.bulk_piece_weight || 0)
      item.bulk_weight = formatWeightDiv(item.bulk_weight || 0)
      item.total_weight = formatWeightDiv(item.total_weight || 0)
      // item.push_weight = formatWeightDiv(item.push_weight || 0)
      item.push_weight = currency(item.push_weight || 0).divide(10000).value
      item.unit_price = formatUnitPriceDiv(item.unit_price) || ''
      item.package_price = formatPriceDiv(item.package_price || 0)
      item.total_price = formatPriceDiv(item.total_price || 0)
      item.tax_included = formatPriceDiv(item.tax_included || 0)
      item.logistics?.map((citem: any) => {
        citem.whole_piece_count = formatPriceDiv(citem?.whole_piece_count)
        citem.bulk_piece_count = formatPriceDiv(citem?.bulk_piece_count)
        citem.bulk_weight = currency(formatWeightDiv(citem.bulk_weight || 0)).value
        citem.weight = currency(formatWeightDiv(citem.weight || 0)).value
      })
    })
  }
}

watch(
  () => detalData.value,
  () => {
    if (detalData.value) {
      // supplierIdRef.value.inputLabel = detalData.value.supplier_name

    }
  },
)
const SelectProductionNoticeRef = ref()

const showAdd = ref(false)
const showAddress = ref(false)
const showProduct = ref(false)
const bulkShow = ref(false)
const selectAssociationsRow = ref()

function handAdd() {
  showAdd.value = true
}

const bulkFormRef = ref()
const multipleSelection = ref<any[]>([])

function handEdit() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

function getNotificationOrder(val: any) {
  if (SelectProductionNoticeRef.value.isBatch) {
    state.tableData.map((item: any) => {
      item.production_order_num = val.order_no
      item.blank_fabric_code = val.grey_fabric_code
      item.blank_fabric_name = val.grey_fabric_name
      item.blank_fabric_code_disabled = true
      item.blank_fabric_name_disabled = true
      item.blank_fabric_id = val.grey_fabric_id
      return item
    })
    bulkShow.value = false
    showProduct.value = false
    ElMessage.success('设置成功')
  }
  else {
    showProduct.value = false
    selectAssociationsRow.value.production_order_num = val.order_no
    selectAssociationsRow.value.blank_fabric_code = val.grey_fabric_code
    selectAssociationsRow.value.blank_fabric_code_disabled = true
    selectAssociationsRow.value.blank_fabric_name = val.grey_fabric_name
    selectAssociationsRow.value.blank_fabric_name_disabled = true
    selectAssociationsRow.value.blank_fabric_id = val.grey_fabric_id
  }
}

const columnList = ref([
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'raw_material_code',
    title: '原料编号',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    width: 130,
    soltName: 'customer_id',
    required: true,
  },
  {
    field: 'brand',
    title: '原料品牌',
    width: 100,
    soltName: 'brand',
  },
  {
    field: 'craft',
    title: '原料工艺',
    width: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    width: 140,
    soltName: 'color_scheme',
    // required: true,
  },
  {
    field: 'measurement_unit_id',
    title: '计量单位',
    width: 140,
    soltName: 'measurement_unit_id',
    required: true,
  },
  {
    field: 'production_date',
    title: '生产日期',
    width: 190,
    soltName: 'production_date',
  },
  {
    field: 'blank_fabric_code',
    title: '坯布编号',
    width: 130,
    soltName: 'blank_fabric_code',
  },
  {
    field: 'blank_fabric_name',
    title: '坯布名称',
    width: 130,
    soltName: 'blank_fabric_name',
  },
  {
    field: 'production_order_num',
    title: '生产通知单号',
    width: 160,
    soltName: 'production_order_num',
  },
  {
    field: 'level',
    title: '原料等级',
    width: 130,
    soltName: 'level',
  },
  {
    field: 'tax_included',
    title: '含税',
    width: 100,
    soltName: 'tax_included',
  },
  {
    field: 'package_price',
    title: '包装价格',
    width: 100,
    soltName: 'package_price',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    width: 100,
    soltName: 'spinning_type',
  },
  {
    field: 'remark',
    title: '备注',
    width: 100,
    soltName: 'remark',
  },
  {
    field: 'address',
    title: '收货地址',
    width: 130,
    soltName: 'address',
  },
  {
    field: 'whole_piece_count',
    title: '整件件数',
    width: 100,
    soltName: 'whole_piece_count',
  },
  {
    field: 'whole_piece_weight',
    title: '整件件重(kg)',
    width: 100,
    soltName: 'whole_piece_weight',
  },
  {
    field: 'whole_weight',
    title: '整件数量总计(kg)',
    width: 100,
    soltName: 'whole_weight',
  },
  {
    field: 'bulk_piece_count',
    title: '散件件数',
    width: 100,
    soltName: 'bulk_piece_count',
  },
  {
    field: 'bulk_weight',
    title: '散件数量',
    width: 140,
    soltName: 'bulk_weight',
  },
  {
    field: 'total_weight',
    title: '总数量',
    width: 100,
  },
  {
    field: 'push_weight',
    title: '下推数量',
    width: 100,
  },
  {
    field: 'unit_price',
    title: '单价(kg/元)',
    width: 140,
    soltName: 'unit_price',
    required: true,
  },
  {
    field: 'total_price',
    title: '金额',
    width: 100,
    soltName: 'total_price',
  },
])

const tablesRef = ref()

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['raw_material_code'].includes(column.field))
        return '合计'

      if (
        [
          'number',
          'whole_piece_count',
          'whole_piece_weight',
          'bulk_piece_count',
          'count_weight',
          'bulk_piece_weight',
          'bulk_count_weight',
          'bulk_weight',
          'total_price',
          'total_weight',
          'total_price',
        ].includes(column.field)
      )
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}

const selectRow = ref()
function openAddress(row: any) {
  // if (!row.whole_piece_count || !row.whole_piece_weight) {
  //   return ElMessage.warning('整件件数和件重不能为空')
  // }

  selectRow.value = row
  showAddress.value = true
}

function onAddress(row: any) {
  selectRow.value.logistics = row?.map((item: any) => {
    return {
      piece_count: Number.parseInt(item.piece_count || 0),
      receipt_address: item.receipt_address || '',
      receipt_person: item.receipt_person || '',
      receipt_phone: item.receipt_phone || '',
      receipt_unit_name: item.receipt_unit_name || '',
      weight: Number.parseFloat(item.weight || 0),
      receipt_unit_id: Number.parseInt(item.receipt_unit_id || 0),
      whole_piece_count: Number.parseInt(item.whole_piece_count || 0),
      bulk_piece_count: Number.parseInt(item.bulk_piece_count || 0),
      bulk_weight: Number.parseFloat(item.bulk_weight || 0),
      total_weight: Number.parseFloat(item.total_weight) || 0,
    }
  })
  showAddress.value = false
}
const saleSaleSystemInfo = ref()
const defaultAddress = ref<any>([])

function onSubmit(row: any) {
  showAdd.value = false
  const val = row?.map((item: any) => {
    return {
      raw_material_code: item.code,
      raw_material_name: item.name,
      raw_material_id: item.id,
      brand: '',
      craft: item.craft,
      color_scheme: '',
      production_date: '',
      blank_fabric_code: '',
      blank_fabric_name: '',
      production_order_num: '',
      level: '',
      tax_included: '',
      package_price: '',
      spinning_type: '',
      remark: '',
      whole_piece_count: '',
      whole_piece_weight: '',
      bulk_piece_count: '',
      bulk_piece_weight: '',
      bulk_weight: '',
      unit_price: '',
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      logistics: defaultAddress.value,
      color_id: '',
      measurement_unit_id: item.unit_id,
    }
  })
  state.tableData = [...state.tableData, ...val]
}

const changeRow = ref<any>()
function changeData(row: any, field = '') {
  row.selectField = field
  changeRow.value = row
}

watch(
  () => changeRow.value,
  () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

const { fetchData: fetchDataEdit, data: addData, success: successEdit, msg: msgEdit } = PurchaseOrderRawMaterialEdit()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const tableData = fomatData()
      if (!tableData)
        return false
      state.form.items = tableData
      state.form.tax_rate = formatPriceMul(state.form.tax_rate)
      await fetchDataEdit(
        {
          ...getFilterData({ ...state.form, purchase_date: formatDate(state.form.purchase_date), receipt_date: formatDate(state.form.receipt_date), id: Number.parseInt(router.params.id as string) }),
          is_with_tax_rate: state.form.is_with_tax_rate,
        },
      )
      if (successEdit.value) {
        getData()
        ElMessage.success('修改成功')
        routerList.push({
          name: 'RawMaterialSourcingDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgEdit.value)
      }
    }
  })
}

function openAssociations(row: any) {
  showProduct.value = true
  selectAssociationsRow.value = row
  SelectProductionNoticeRef.value.isBatch = false
}

function openAssociationsSlot() {
  showProduct.value = true
  SelectProductionNoticeRef.value.isBatch = true
}

function getFabricInfo(row: any, val: any) {
  row.blank_fabric_name = val.name
  row.blank_fabric_id = val.id
  row.blank_fabric_code = val.code
}

// 整理坯布信息
function fomatData() {
  const n_data: any[] = []
  const data = JSON.parse(JSON.stringify(state.tableData))
  for (const item of data) {
    const logistics = []
    for (let i = 0; i < item.logistics?.length; i++) {
      if (!item.logistics[i]?.receipt_unit_name) {
        ElMessage.error(`原料编号${item?.raw_material_code}-收货单位不能为空`)
        return false
      }
      if (!item.logistics[i]?.receipt_unit_id) {
        ElMessage.error(`原料编号${item?.raw_material_code}-收货单位不能为空`)
        return false
      }
      if (!item.logistics[i]?.receipt_person) {
        ElMessage.error(`原料编号${item?.raw_material_code}-收货人不能为空`)
        return false
      }
      if (!item.logistics[i]?.receipt_phone) {
        ElMessage.error(`原料编号${item?.raw_material_code}-收货电话不能为空`)
        return false
      }
      if (!item.logistics[i]?.receipt_address) {
        ElMessage.error(`原料编号${item?.raw_material_code}-收货地址不能为空`)
        return false
      }
      item.logistics[i].whole_piece_count = formatPriceMul(item.logistics[i]?.whole_piece_count)
      item.logistics[i].bulk_piece_count = formatPriceMul(item.logistics[i]?.bulk_piece_count)
      item.logistics[i].bulk_weight = formatWeightMul(item.logistics[i]?.bulk_weight)
      item.logistics[i].weight = formatWeightMul(item.logistics[i]?.weight)
      logistics.push(item.logistics[i])
    }
    n_data.push({
      ...item,
      bulk_piece_weight: formatWeightMul(item.bulk_piece_weight),
      whole_piece_weight: formatWeightMul(item.whole_piece_weight),
      unit_price: formatUnitPriceMul(item.unit_price),
      tax_included: item.tax_included ? formatPriceMul(item.tax_included) : 0,
      package_price: formatPriceMul(item.package_price),
      whole_piece_count: formatPriceMul(item.whole_piece_count),
      bulk_piece_count: formatPriceMul(item.bulk_piece_count),
      bulk_weight: formatWeightMul(item.bulk_weight || 0),
      whole_weight: formatWeightMul(item.whole_weight || 0),
      total_weight: formatWeightMul(item.total_weight || 0),
      push_weight: formatWeightMul(item.push_weight || 0),
      logistics,
      color_id: Number(item.color_id),
    })
  }
  return n_data
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.customer_id) {
        msg = `原料编号为${item.raw_material_code}的数据,所属客户不能为空`
        return true
      }
      if (!item.total_weight) {
        msg = `原料编号为${item.raw_material_code}的数据,总数量不能为空`
        return true
      }
      // if (item.color_id === '') {
      //   msg = `原料颜色不可为空`
      //   return true
      // }
      // if (!item.whole_piece_count) {
      //   msg = `原料编号为${item.raw_material_code}的数据,整件件数不能为空`
      //   return true
      // }
      // if (!item.whole_piece_weight) {
      //   msg = `原料编号为${item.raw_material_code}的数据,件重不能为空`
      //   return true
      // }
      // if (!item.bulk_piece_count) {
      //   msg = `原料编号为${item.raw_material_code}的数据,散件件数不能为空`
      //   return true
      // }
      // if (!item.bulk_weight) {
      //   msg = `原料编号为${item.raw_material_code}的数据,散件数量不能为空`
      //   return true
      // }
      if (!item.unit_price) {
        msg = `原料编号为${item.raw_material_code}的数据,单价不能为空`
        return true
      }
      let weight = 0
      item?.logistics?.map((item: any) => {
        weight = currency(item?.weight).add(weight).value
      })
      if (weight > item.total_weight) {
        msg = `原料编号为${item.raw_material_code}的数据,地址总数量不能大于对应原料行总数量`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm(`确认后商品将被删除`, '是否删除该商品', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      state.tableData.splice(rowIndex, 1)
    })
    .catch(() => {})
}
const addressRow = computed(() => {
  return {
    raw_material_code: selectRow.value?.raw_material_code,
    raw_material_name: selectRow.value?.raw_material_name,
    whole_piece_weight: selectRow.value?.whole_piece_weight,
    whole_piece_count: selectRow.value?.whole_piece_count,
    bulk_piece_count: selectRow.value?.bulk_piece_count,
    bulk_weight: selectRow.value?.bulk_weight,
    weight: selectRow.value?.weight,
    total_weight: selectRow.value?.total_weight,
  }
})

function getAddress(val: any, row: any) {
  row.receipt_unit_name = val.name
  row.recipient_entity_name = val.name
  row.receipt_person = val.contact_name
  row.receipt_phone = val.phone
  row.receipt_address = val.address
}

function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData(row)
}

const bulkSetting = ref<any>({
  address: {},
})
function handBulkClose() {
  bulkShow.value = false
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'brand',
    title: '原料品牌',
    component: 'input',
  },
  {
    field: 'color_id',
    title: '原料颜色',
    component: 'select',
    api: 'GetRawMaterialColor',
  },
  {
    field: 'production_date',
    title: '生产日期',
    component: 'selectDate',
    type: 'date',
  },
  {
    field: 'blank_fabric_code',
    title: '坯布编号',
    width: 100,
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthersMenu',
    labelField: 'code',
    // valueField: 'code',
  },
  {
    field: 'blank_fabric_name',
    title: '坯布名称',
    width: 100,
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthersMenu',
    labelField: 'name',
    // valueField: 'name',
  },
  // {
  //   field: 'production_order_num',
  //   title: '生产通知单号',
  //   component: 'select',
  //   api: 'GetGreyFabricInfoListUseByOthersMenu',
  // },
  {
    field: 'tax_included',
    title: '含税',
    component: 'input',
    type: 'float',
  },
  {
    field: 'package_price',
    title: '包装价格',
    component: 'input',
    type: 'float',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    component: 'input',
    type: 'text',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'whole_piece_count',
    title: '整件件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'whole_piece_weight',
    title: '整件件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'bulk_piece_count',
    title: '散件件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_weight',
    title: '散件数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价(kg/元)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'level_id',
    title: '原料等级',
    width: 100,
    component: 'select',
    api: 'GetInfoBaseRawMaterialLevelEnumList',
  },
  {
    field: 'production_order_num',
    title: '生产通知单号',
  },
  {
    field: 'address',
    title: '收货地址',
    hideTitle: true,
    rule: {
      receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
      receipt_person: [{ required: true, message: '请填写收货联系人', trigger: 'change' }],
      receipt_phone: [{ required: true, message: '请填写收货电话', trigger: 'change' }],
      receipt_address: [{ required: true, message: '请填写收货地址', trigger: 'change' }],
    },
  },
])

async function bulkSubmit({ row, value, selectData }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请填写批量修改的数据')

  let tf = false
  if (row.field !== 'address') {
    if (!value[row.field]) {
      ElMessage.error('请输入参数')
      tf = true
    }
  }
  else {
    await bulkFormRef.value.validate((valid: any) => {
      if (!valid)
        tf = true
    })
  }
  if (tf)
    return
  multipleSelection.value?.map((item: any) => {
    if (row.field === 'blank_fabric_code' || row.field === 'blank_fabric_name') {
      item.blank_fabric_name = selectData?.name
      item.blank_fabric_id = selectData?.id
      item.blank_fabric_code = selectData?.code
    }
    else if (row.field === 'address') {
      if (item.whole_piece_count && item.whole_piece_weight)
        item.logistics = [{ ...value[row.field], piece_count: item.whole_piece_count, weight: Number.parseFloat(item.whole_piece_count || 0) * Number.parseFloat(item.whole_piece_weight || 0) }]
      else
        ElMessage.warning('整件件数和整件数量不能为空')
    }
    else {
      item[row.field] = value[row.field]
      // ElMessage.success('设置成功')
    }
    computedData(item)
  })
  ElMessage.success('设置成功')
}

async function computedData(changeRow: any) {
  // 计算整件总数量
  changeRow.whole_weight = Number.parseFloat(
    Big(changeRow?.whole_piece_count || 0)
      .times(changeRow?.whole_piece_weight || 0)
      .toFixed(2),
  )
  // 计算总数量
  if (changeRow.selectField !== 'unit_price') {
    // 散件减重  bulk_weight 整件数量总计(kg) whole_weight
    changeRow.total_weight = Number.parseFloat(
      Big(changeRow?.whole_weight || 0)
        .plus(changeRow?.bulk_weight || 0)
        .toFixed(2),
    )
  }

  // 计算金额
  changeRow.total_price = Number.parseFloat(
    Big(changeRow.unit_price || 0)
      .times(changeRow?.total_weight || 0)
      .plus(changeRow?.package_price || 0)
      // .plus(changeRow?.tax_included || 0)
      .toFixed(2),
  )
  await tablesRef.value.tableRef.updateFooter()
}

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

const oneStatus = true
function clearData(row: any) {
  if (!oneStatus) {
    state.tableData?.map((item) => {
      item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
      item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
    })
  }
  bulkSetting.value.customer_id = ''
  bulkList[0].query = { sale_system_id: row.id }
}

function clearProductionOrderNum(row: any) {
  row.production_order_num = ''
  row.blank_fabric_code = ''
  row.blank_fabric_name = ''
}

function getBusinessUnitListInfo(val: any) {
  if (val) {
    bulkSetting.value.address = {
      receipt_unit_name: val?.name,
      receipt_unit_id: val?.id,
      receipt_person: val?.contact_name,
      receipt_phone: val?.phone,
      receipt_address: val?.address,
    }
    defaultAddress.value = [bulkSetting.value.address]
    state.tableData?.map((item: any) => {
      item.logistics = [{
        ...(item?.logistics?.[0] || {}),
        ...defaultAddress.value[0],
      }]
    })
  }
  else {
    defaultAddress.value = ''
  }
}

// 监听当前操作项，整件件数和整件件重
let fistStatus = true
watch(
  () => [changeRow.value?.whole_piece_count, changeRow.value?.whole_piece_weight, changeRow.value?.bulk_piece_count, changeRow.value?.bulk_weight, changeRow.value?.total_weight],
  () => {
    if (changeRow.value?.logistics?.length === 1 && !fistStatus) {
      changeRow.value.logistics[0].piece_count = changeRow.value.whole_piece_count
      changeRow.value.logistics[0].weight = changeRow.value.total_weight
      changeRow.value.logistics[0].bulk_piece_count = changeRow.value.bulk_piece_count
      changeRow.value.logistics[0].bulk_weight = changeRow.value.bulk_weight
      // if (changeRow.value.whole_piece_count && changeRow.value.whole_piece_weight) {
      //   changeRow.value.logistics[0].weight = parseFloat(
      //     Big(changeRow.value.whole_piece_count || 0)
      //       .times(changeRow.value.whole_piece_weight || 0)
      //       .toFixed(2)
      //   )
      // }
    }
    fistStatus = false
  },
)

const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  footerMethod,
  handAllSelect,
  handleSelectionChange,
  showCheckBox: true,
  fieldApiKey: 'RawMaterialSourcingEdit',
})

function handleChangeColor(value: any, row: any) {
  row.color_id = value.id
  row.color_name = value.name
  row.color_code = value.code
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  style="width: 200px"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @select="getSaleSystem"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="供应商名称:">
            <template #content>
              <el-form-item prop="supplier_id">
                <!-- <SelectComponents
                  style="width: 200px"
                  @select="val => (state.form.supplier_name = val?.name)"
                  :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial }"
                  v-model="state.form.supplier_id"
                  api="BusinessUnitSupplierEnumlist"
                  label-field="name"
                  value-field="id"
                ></SelectComponents> -->
                <SelectDialog
                  v-model="state.form.supplier_id"
                  :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial, name: componentRemoteSearch.name }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '供应商编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '供应商编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-value="val => (state.form.supplier_name = val?.name)"
                  @change-input="val => (componentRemoteSearch.name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货单位名称:">
            <template #content>
              <el-form-item>
                <SelectDialog
                  v-model="state.form.receipt_unit_id"
                  :label-name="detalData.receipt_unit_name"
                  :query="{ unit_type_id: `${BusinessUnitIdEnum.knittingFactory},${BusinessUnitIdEnum.dyeingMill}`, name: componentRemoteSearch.recipien_entity_name }"
                  api="GetBusinessUnitListApi"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-value="getBusinessUnitListInfo"
                  @on-input="val => (componentRemoteSearch.recipien_entity_name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="采购日期:">
            <template #content>
              <el-form-item prop="purchase_date">
                <SelectDate v-model="state.form.purchase_date" style="width: 200px" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货日期:">
            <template #content>
              <el-form-item prop="receipt_date">
                <SelectDate v-model="state.form.receipt_date" style="width: 200px" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="发票抬头:">
            <template #content>
              <el-form-item prop="fapiao_title">
                <SelectComponents
                  v-model="state.form.fapiao_title_id"
                  api="GetInfoPurchaseInvoiceHeaderListUseByOther"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.fapiao_title = row.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <vxe-textarea v-model="state.form.remark" maxlength="500" show-word-count />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="税率:">
            <template #content>
              <vxe-input v-model="state.form.tax_rate" type="float" clearable :disabled="!state.form.is_with_tax_rate" placeholder="税率" :min="0">
                <template #suffix>
                  %
                </template>
              </vxe-input>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="">
            <template #content>
              <el-checkbox v-model="state.form.is_with_tax_rate" label="是否含税" size="large" />
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据资料添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectDialog
          v-model="row.customer_id"
          :label-name="row.customer_name"
          :query="{ sale_system_id: state.form.sale_system_id, name: componentRemoteSearch.customer_name }"
          api="GetCustomerEnumList"
          :column-list="[
            {
              title: '客户编号',
              minWidth: 100,
              required: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '客户编号',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '客户名称',
              minWidth: 100,
              colGroupHeader: true,
              required: true,
              childrenList: [
                {
                  isEdit: true,
                  field: 'name',
                  title: '客户名称',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '电话',
              colGroupHeader: true,
              minWidth: 100,
              childrenList: [
                {
                  field: 'phone',
                  isEdit: true,
                  title: '电话',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '销售员',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'seller_name',
                  title: '销售员',
                  soltName: 'seller_name',
                  isEdit: true,
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="val => (componentRemoteSearch.customer_name = val)"
        />
      </template>
      <template #brand="{ row }">
        <vxe-input v-model="row.brand" size="mini" maxlength="200" />
      </template>
      <template #color_scheme="{ row }">
        <SelectDialog
          v-model="row.color_id"
          api="GetRawMaterialColor"
          :query="{ raw_matl_id: row.raw_material_id, name: row.color_name }"
          :column-list="[
            {
              field: 'name',
              title: '原料名称',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '原料名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              colGroupHeader: true,
              title: '原料编号',
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '原料编号',
                  minWidth: 100,
                },
              ],
              minWidth: 100,
            },
            {
              field: 'ingredient',
              title: '原料成分',
              childrenList: [
                {
                  field: 'ingredient',
                  isEdit: true,
                  title: '原料成分',
                  minWidth: 100,
                },
              ],
              colGroupHeader: true,
              minWidth: 100,
            },
          ]"
          :table-column="[
            {
              field: 'name',
              title: '原料名称',
              defaultData: {
                id: row.color_id,
                name: row.color_name,
                code: row.color_code,
              },
            },
          ]"
          @change-value="handleChangeColor($event, row)"
          @on-input="val => (row.color_name = val)"
        />
      </template>
      <template #measurement_unit_id="{ row }">
        <SelectComponents v-model="row.measurement_unit_id" disabled api="getInfoBaseMeasurementUnitEnumList" label-field="name" value-field="id" clearable />
      </template>
      <template #production_date="{ row }">
        <SelectDate v-model="row.production_date" type="date" size="small" style="width: 150px" />
      </template>
      <template #blank_fabric_code="{ row }">
        <SelectComponents
          v-model="row.blank_fabric_code"
          :disabled="!!row.production_order_num"
          size="small"
          api="GetGreyFabricInfoListUseByOthersMenu"
          label-field="code"
          value-field="code"
          @change-value="val => getFabricInfo(row, val)"
        />
      </template>
      <template #blank_fabric_name="{ row }">
        <SelectComponents
          v-model="row.blank_fabric_name"
          :disabled="!!row.production_order_num"
          size="small"
          api="GetGreyFabricInfoListUseByOthersMenu"
          label-field="name"
          value-field="name"
          @change-value="val => getFabricInfo(row, val)"
        />
      </template>
      <template #production_order_num="{ row }">
        <el-tag v-if="row.production_order_num" closable @close="clearProductionOrderNum(row)">
          {{ row.production_order_num }}
        </el-tag>
        <el-link v-else type="primary" @click="openAssociations(row)">
          关联
        </el-link>
      </template>
      <template #level="{ row }">
        <SelectComponents v-model="row.level_id" size="small" api="GetInfoBaseRawMaterialLevelEnumList" label-field="name" value-field="id" />
      </template>
      <template #tax_included="{ row }">
        <vxe-input v-model="row.tax_included" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #package_price="{ row }">
        <vxe-input v-model="row.package_price" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #spinning_type="{ row }">
        <vxe-input v-model="row.spinning_type" size="mini" maxlength="200" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" />
      </template>
      <!--      收货地址 -->
      <template #address="{ row }">
        <el-link type="primary" :disabled="row.whole_piece_count === '' && !Number(row.bulk_piece_count)" @click="openAddress(row)">
          编辑
        </el-link>
        <span class="text-black-40" :style="`color:${!isConfigured(row) && 'red'}`">
          {{ isConfigured(row) ? '（已配置）' : '（未配置）' }}
        </span>
      </template>
      <!--      整件件数 -->
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" size="mini" type="integer" :min="0" @input="changeData(row, 'whole_piece_count')" />
      </template>
      <template #whole_piece_weight="{ row }">
        <vxe-input v-model="row.whole_piece_weight" size="mini" type="float" :min="0" :digits="2" @input="changeData(row, 'whole_piece_weight')" />
      </template>
      <template #whole_weight="{ row }">
        {{ row.whole_weight }}
      </template>
      <!--      散件件数 -->
      <template #bulk_piece_count="{ row }">
        <vxe-input v-model="row.bulk_piece_count" size="mini" type="integer" :min="0" @input="changeData(row, 'bulk_piece_count')" />
      </template>
      <template #bulk_piece_weight="{ row }">
        <vxe-input v-model="row.bulk_piece_weight" size="mini" type="float" :min="0" @input="changeData(row, 'bulk_piece_weight')" />
      </template>
      <template #bulk_count_weight="{ row }">
        {{ row.bulk_count_weight }}
      </template>
      <template #bulk_weight="{ row }">
        <vxe-input v-model="row.bulk_weight" size="mini" type="float" :digits="2" :min="0" @input="changeData(row, 'bulk_weight')" />
      </template>
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" size="mini" type="float" :min="0" @input="changeData(row, 'unit_price')" />
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <AddressAdd v-model="showAddress" :row="addressRow" :default-list="[...(selectRow?.logistics || [])]" @submit="onAddress" />
  <SelectProductionNotice ref="SelectProductionNoticeRef" v-model="showProduct" :order_no="selectAssociationsRow?.production_order_num" @submit="getNotificationOrder" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
    <template #address="{ row }">
      <el-form ref="bulkFormRef" :model="bulkSetting[row.field]" :rules="row.rule" label-width="120px" label-position="left">
        <el-form-item prop="receipt_unit_id" label="收货单位">
          <SelectComponents
            v-model="bulkSetting[row.field].receipt_unit_id"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            @select="val => getAddress(val, bulkSetting[row.field])"
          />
        </el-form-item>
        <el-form-item prop="receipt_person" label="收货联系人">
          <el-input v-model="bulkSetting[row.field].receipt_person" />
        </el-form-item>
        <el-form-item prop="receipt_phone" label="收货电话">
          <el-input v-model="bulkSetting[row.field].receipt_phone" />
        </el-form-item>
        <el-form-item prop="receipt_address" label="收货地址">
          <el-input v-model="bulkSetting[row.field].receipt_address" />
        </el-form-item>
      </el-form>
    </template>
    <template #production_order_num>
      <el-button type="primary" @click="openAssociationsSlot">
        选择关联
      </el-button>
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped></style>
