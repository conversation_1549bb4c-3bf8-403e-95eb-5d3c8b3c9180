<script lang="ts" setup name="FinishedProductColorInformationDetail">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { GetFinishProductColor, GetProductCompositeDetails, GetProductDyeingColorDetails } from '@/api/finishedProductColorInformation'
import { formatTwoDecimalsDiv } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import ImageFileCard from '@/components/UploadFile/FileCard/index.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'

const route = useRoute()
const { fetchData, data: dataInfo } = GetFinishProductColor()
const { fetchData: fetchDataComposite, data: dataComposite } = GetProductCompositeDetails()
const { fetchData: fetchDataDyeingColor, data: dataDataDyeingColor } = GetProductDyeingColorDetails()
onActivated(() => {
  getData()
})
onMounted(getData)
async function getData() {
  await fetchData({ id: route.params.id })
  await fetchDataComposite({ id: route.params.id })
  await fetchDataDyeingColor({ id: route.params.id })
}

const state = reactive<any>({
  form: {
    finish_product_id: '',
    finish_product_code: '',
    finish_product_name: '',
    finish_product_craft: '',
    grey_fabric_code: '',
    grey_fabric_name: '',
    type_finished_product_kind_name: '',
    type_finished_product_kind_id: '',
    product_color_code: '',
    product_color_name: '',
    length_to_weight_rate: '',
    bulk_max_safe_amount: '',
    bulk_min_safe_amount: '',
    length_cut_max_safe_amount: '',
    length_cut_min_safe_amount: '',
    dyeing_color_item_data: [],
    composite_item_data: [],
  },
  activeName: 'color',
})

watch(
  () => [dataComposite.value?.list, dataDataDyeingColor.value?.list],
  () => {
    state.form.dyeing_color_item_data = dataDataDyeingColor.value?.list
    state.form.composite_item_data = dataComposite.value?.list
  },
)
const ruleFormRef = ref()

const columnList = ref([
  {
    field: 'finish_product_code',
    title: '成品编号',
    minWidth: 100,
    // soltName: 'finish_product_code',
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
    // soltName: 'product_color_code',
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'ratio',
    title: '比率',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'is_bottom_identify',
    title: '底布标识',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])

const columnColorList = ref([
  {
    field: 'dye_factory_color_code',
    title: '染厂颜色编号',
    minWidth: 100,
  },
  {
    field: 'dye_factory_color_name',
    title: '染厂颜色名称',
    minWidth: 100,
  },
  {
    field: 'dyelot_number',
    title: '对色缸号',
    minWidth: 100,
  },
  {
    field: 'product_kind_name',
    title: '颜色类别',
    minWidth: 100,
  },
  {
    field: 'dye_factory_name',
    title: '染厂名称',
    minWidth: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'dye_craft',
    title: '染整工艺',
    minWidth: 100,
  },
  {
    field: 'tape_specs',
    title: '胶袋规格',
    minWidth: 100,
  },
  {
    field: 'paper_tube_specs',
    title: '纸筒规格',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])

const tableConfig = ref({
  // showOperate: true,
})
const tableColorConfig = ref({
  // showOperate: true,
})
</script>

<template>
  <!-- 这里禁用/启用都能编辑 -->
  <StatusColumn
    v-if="route.params.id"
    :order_id="Number(route.params.id)"
    :status="1"
    order_no=""
    status_name=""
    permission_cancel_key="0"
    permission_reject_key="0"
    permission_pass_key="0"
    permission_wait_key="0"
    permission_deliver_key="0"
    permission_close_key="0"
    permission_edit_key="finishedProductColorInformationEdit"
    edit_router_name="finishedProductColorInformationEdit"
    edit_router_type="params"
  />
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '124px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            {{ dataInfo?.finish_product_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            {{ dataInfo?.finish_product_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品工艺:">
          <template #content>
            {{ dataInfo?.finish_product_craft }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            {{ dataInfo?.grey_fabric_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            {{ dataInfo?.grey_fabric_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色类别:">
          <template #content>
            {{ dataInfo?.type_finished_product_kind_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色编号:">
          <template #content>
            {{ dataInfo?.product_color_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色名称:">
          <template #content>
            {{ dataInfo?.product_color_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="数量转长度比率:">
          <template #content>
            {{ formatTwoDecimalsDiv(dataInfo?.length_to_weight_rate) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="大货安全库存数:">
          <template #content>
            {{
              dataInfo?.bulk_min_safe_amount === 0 && dataInfo?.bulk_max_safe_amount === 0
                ? ''
                : `${formatTwoDecimalsDiv(dataInfo?.bulk_min_safe_amount)}-${formatTwoDecimalsDiv(dataInfo?.bulk_max_safe_amount)}`
            }}
          </template>
        </DescriptionsFormItem>
        <!-- <DescriptionsFormItem label="剪版安全库存数量:">
          <template v-slot:content>
            {{
              dataInfo.length_cut_min_safe_amount === 0 && dataInfo.length_cut_max_safe_amount === 0
                ? ''
                : formatWeightDiv(dataInfo.length_cut_min_safe_amount) + '-' + formatWeightDiv(dataInfo.length_cut_max_safe_amount)
            }}
          </template>
        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="成分:">
          <template #content>
            {{ dataInfo?.finish_product_ingredient }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="纱支:">
          <template #content>
            {{ dataInfo?.yarn_count }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="密度:">
          <template #content>
            {{ dataInfo?.density }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="门幅:">
          <template #content>
            {{ dataInfo?.finish_product_width_and_unit_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="克重:">
          <template #content>
            {{ dataInfo?.finish_product_gram_weight_and_unit_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="漂染性:">
          <template #content>
            {{ dataInfo?.bleach_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织造组织:">
          <template #content>
            {{ dataInfo?.weaving_organization_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缩水率:">
          <template #content>
            {{ dataInfo?.shrinkage_warp }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            {{ dataInfo?.remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:">
          <template #content>
            {{ dataInfo?.supplier_name }}
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>

    <el-tabs v-model="state.activeName" class="demo-tabs">
      <el-tab-pane label="复合布" name="rabrics">
        <Table :config="tableConfig" :table-list="state.form.composite_item_data" :column-list="columnList" />
      </el-tab-pane>
      <el-tab-pane label="染厂颜色" name="color">
        <Table :config="tableColorConfig" :table-list="state.form.dyeing_color_item_data" :column-list="columnColorList" />
      </el-tab-pane>
    </el-tabs>

    <div class="mt-6">
      <h3 class="mb-2">
        纹理图:
      </h3>
      <ImageFileCard v-for="(item, index) in dataInfo.texture_url" :key="index" :file-url="item" clear-disabled />
    </div>
  </FildCard>
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}

.sale {
  margin-top: 40px;
  font-weight: 600;
}

.my-search {
  width: 100%;
}

::v-deep(.my-search .vxe-input--inner) {
  padding-right: 72px;
  border: 1px solid #dcdfe6;
}

::v-deep(.my-search .vxe-input--suffix) {
  width: 60px;
  height: 32px;
  top: 1px;
  text-align: center;
  border-left: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  font-size: 13px;
  cursor: pointer;
}
</style>
