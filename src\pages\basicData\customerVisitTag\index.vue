<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import AddTag from './components/AddTag.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { GetVisitTagList } from '@/api/customerVisit'

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange, msg, success } = GetVisitTagList()

const columnList = ref([
  {
    sortable: true,
    field: 'visiting_mode_names',
    soltName: 'visiting_mode_names',
    title: '标签类型',
    width: 250,
  },
  {
    sortable: true,
    field: 'name',
    title: '标签名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
  },
  {
    sortable: true,
    field: 'range',
    soltName: 'range',
    title: '提醒范围',
    width: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
  },
  {
    sortable: true,
    field: 'sort',
    title: '排序',
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    soltName: 'status',
    width: '5%',
  },
])
// 获取数据
async function getData() {
  await ApiCustomerList()
  if (!success.value)
    return ElMessage.error(msg.value)
}

onMounted(() => {
  getData()
})
const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '6%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  fieldApiKey: 'ContactUnit',
})
const isEdit = ref(false)
const AddDialogRef = ref()
function handEdit(row: any) {
  isEdit.value = true
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.form.visiting_modes = row.visiting_modes
  AddDialogRef.value.state.form.max_range_day = row.max_range_day
  AddDialogRef.value.state.form.min_range_day = row.min_range_day
  AddDialogRef.value.state.form.name = row.name
  AddDialogRef.value.state.form.remark = row.remark
  AddDialogRef.value.state.form.sort = row.sort
  AddDialogRef.value.state.form.status = row.status === 1
  AddDialogRef.value.state.ids = row.ids
}
function handleAdd() {
  isEdit.value = false
  AddDialogRef.value.state.showModal = true
}
</script>

<template>
  <div class="list-page">
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button v-has="'customerVisit_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建标签
        </el-button>
      </template>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #range="{ row }">
          {{ row.min_range_day }}~{{ row.max_range_day }} 天
        </template>
        <template #visiting_mode_names="{ row }">
          <el-space>
            <el-tag v-for="(item, index) in row.visiting_mode_names" :key="index">
              {{ item }}
            </el-tag>
          </el-space>
        </template>
        <template #status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-link v-has="'customerVisit_edit'" type="primary" :underline="false" @click="handEdit(row)">
            编辑
          </el-link>
        </template>
      </Table>
    </FildCard>
  </div>
  <AddTag ref="AddDialogRef" :type="isEdit ? 'edit' : 'add'" @handle-sure="getData" />
</template>

<style scoped>

</style>
