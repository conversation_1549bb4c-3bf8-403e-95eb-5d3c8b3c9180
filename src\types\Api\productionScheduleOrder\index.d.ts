declare namespace Api.AddProductionScheduleOrder {
  /**
   * produce.AddProductionScheduleOrderParam
   */
  export interface Request {
    /**
     * 款号
     */
    account_num?: string
    /**
     * 获取往来单位织造配置
     */
    bf_prefix?: string
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule?: number
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 布种后整
     */
    fabric_finishing?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布id
     */
    grey_fabric_id?: number
    /**
     * 机台号
     */
    machine_number?: string
    materialRatioList?: ProduceAddProductionScheduleOrderMaterialRatioParam[]
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 生产通知单id
     */
    production_notify_order_id?: number
    /**
     * 生产通知单号
     */
    production_notify_order_no?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 排产日期
     */
    schedule_date?: string
    /**
     * 排产条数
     */
    schedule_roll?: number
    /**
     * 总针数
     */
    total_needle_size?: string
    /**
     * 织厂id
     */
    weave_factory_id?: number
    /**
     * 纱批
     */
    yarn_batch?: string
    [property: string]: any
  }

  /**
   * produce.AddProductionScheduleOrderMaterialRatioParam
   */
  export interface ProduceAddProductionScheduleOrderMaterialRatioParam {
    /**
     * 原料损耗
     */
    material_loss?: number
    /**
     * 原料比例
     */
    material_ratio: number
    /**
     * 生产通知单详情id
     */
    production_notify_order_detail_id: number
    /**
     * 原料批号
     */
    raw_material_batch_number?: string
    /**
     * 原料品牌
     */
    raw_material_brand?: string
    /**
     * 原料颜色id
     */
    raw_material_color_id: number
    /**
     * 原料颜色名称
     */
    raw_material_color_name: string
    /**
     * 原料缸号
     */
    raw_material_dyelot_number?: string
    /**
     * 原料id
     */
    raw_material_id: number
    /**
     * 原料名称
     */
    raw_material_name: string
    /**
     * 供应商id
     */
    supplier_id?: number
    /**
     * 织造类别id
     */
    weaving_category_id?: number
    [property: string]: any
  }
  /**
   * produce.AddProductionScheduleOrderData
   */
  export interface Response {
    id?: number
    [property: string]: any
  }
}
declare namespace Api.GetFineCodeList {
  /**
   * produce.GetProductionScheduleOrderFineCodeListData
   */
  export interface Response {
    /**
     * 条形码
     */
    fabric_piece_code?: string
    /**
     * 坯布等级ID
     */
    grey_fabric_level_id?: number
    /**
     * 细码ID
     */
    id?: number
    /**
     * 进仓条数
     */
    in_warehouse_roll?: number
    /**
     * 进仓重量
     */
    in_warehouse_weight?: number
    /**
     * 验布条数
     */
    inspection_roll?: number
    /**
     * 验布重量
     */
    inspection_weight?: number
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 出仓条数
     */
    out_warehouse_roll?: number
    /**
     * 出仓重量
     */
    out_warehouse_weight?: number
    /**
     * 打印状态：1-未打印，2-已打印
     */
    print_status?: number
    /**
     * 打印时间
     */
    print_time?: string
    /**
     * 打印人ID
     */
    printer_id?: number
    /**
     * 打印人姓名
     */
    printer_name?: string
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 生产排产单ID
     */
    production_schedule_order_id?: number
    /**
     * 质量备注
     */
    quality_remark?: string
    /**
     * 库存条数
     */
    stock_roll?: number
    /**
     * 库存重量
     */
    stock_weight?: number
    /**
     * 卷号
     */
    volume_number?: number
    /**
     * 称重条数
     */
    weighing_roll?: number
    /**
     * 称重重量
     */
    weighing_weight?: number
    [property: string]: any
  }
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * 条形码
     */
    fabric_piece_code?: string
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * page
     */
    page?: number
    /**
     * 打印状态筛选（1未打印、2部分打印、3已打印）
     */
    print_status?: number
    /**
     * 生产排产单ID
     */
    production_schedule_order_id?: number
    /**
     * size
     */
    size?: number
    /**
     * 卷号
     */
    volume_number?: number
    [property: string]: any
  }
}
