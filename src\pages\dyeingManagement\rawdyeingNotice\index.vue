<script lang="ts" setup name="RawDyeingNotice">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  raw_process_orderoutlist,
  raw_process_orderoutputpass,
  raw_process_orderoutputwait,
} from '@/api/rawdyeingNotice'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate } from '@/common/format'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  getRecentDay_Date,
  resetData,
} from '@/common/util'

// import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'

// import PrintBtn from '@/components/PrintBtn/index.vue'

const state = reactive({
  filterData: {
    order_no: '',
    audit_statuss: '',
    dye_types: '',
    sale_system_id: '',
    dye_unit_id: '',
    creator_id: '',
    auditor_id: '',
    create_time: '',
    audit_time: '',
  },
  multipleSelection: [],
  information: false,
  isDyeing: true,
  moreItems: [],
})

const options = ref([
  {
    label: '坯纱染整',
    value: 1,
  },
  {
    label: '加工',
    value: 2,
  },
  {
    label: '回修',
    value: 3,
  },
])

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = raw_process_orderoutlist()

// 获取数据
const getData = debounce(() => {
  const obj = deepClone(state.filterData)
  if (state.filterData.audit_statuss.length)
    obj.audit_statuss = obj.audit_statuss.join(',')

  const query: any = {
    dye_date_start:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[0])
        : '',
    dye_date_end:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[1])
        : '',
    create_time_start:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    create_time_end:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    audit_time_start:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    audit_time_end:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
    audit_statuss: obj.audit_statuss,
  }
  delete query.audit_time
  delete query.create_time
  delete query.devierDate
  ApiCustomerList(getFilterData(query))
}, 400)

onMounted(() => {
  getData()
  state.filterData.create_time = getRecentDay_Date(1)
})
onActivated(() => {
  getData()
})
watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  fieldApiKey: 'RawDyeingNotice',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '12%',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  //   handAllSelect: (val: any) => handAllSelect(val),
  //   handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '9%',
    soltName: 'link',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'dye_type_name',
    title: '染整类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_unit_name',
    title: '染纱厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_unit_follower_name',
    title: '染纱厂跟单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_unit_follower_phone',
    title: '跟单电话',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_date',
    title: '染整日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'receive_unit_name',
    title: '收货单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receive_address',
    title: '收货地址',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '单据备注',
    minWidth: 120,
  },
  //   {
  //     sortable: true,
  //     field: 'piece_count',
  //     title: '匹数',
  //     minWidth: 120,
  //     isPrice: true,
  //   },
  //   {
  //     sortable: true,
  //     field: 'weight',
  //     title: '总数量',
  //     minWidth: 120,
  //     isWeight: true,
  //   },
  //   {
  //     sortable: true,
  //     field: 'change_roll',
  //     title: '变更匹数',
  //     minWidth: 120,
  //     soltName: 'change_roll',
  //   },
  //   {
  //     sortable: true,
  //     field: 'change_weight',
  //     title: '变更数量',
  //     minWidth: 120,
  //     soltName: 'change_weight',
  //   },
  //   {
  //     sortable: true,
  //     field: 'rtn_piece_count',
  //     title: '已返匹数',
  //     minWidth: 120,
  //     isPrice: true,
  //   },
  //   {
  //     sortable: true,
  //     field: 'rtn_weight',
  //     title: '已返数量',
  //     minWidth: 120,
  //     isWeight: true,
  //   },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 120,
    isDate: true,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    minWidth: 120,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    isDate: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '单据状态',
    fixed: 'right',
    soltName: 'audit_status',
    showOrder_status: true,
    width: '5%',
  },
])

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'RawDyeingNoticeAdd',
  })
}

function handDetail(row: any) {
  router.push({
    name: 'RawDyeingNoticeDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'RawDyeingNoticeEdit',
    query: { id: row.id },
  })
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = raw_process_orderoutputpass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = raw_process_orderoutputwait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整类型:">
          <template #content>
            <el-select v-model="state.filterData.dye_types" clearable class="m-2">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂名称:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.dye_unit_id"
              api="BusinessUnitSupplierEnumAll"
              :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整日期:" width="350">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.creator_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:" width="350">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.auditor_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核时间:" width="350">
          <template #content>
            <SelectDate v-model="state.filterData.audit_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_statuss"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <!-- <BottonExcel v-has="'Dyeing_and_finishinglistExport'" :loading="loadingExcel" @onClickExcel="handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          v-has="'RawDyeingNoticeAdd'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>

        <template #operate="{ row }">
          <el-space :size="10">
            <PrintPopoverBtn
              :id="row.id"
              print-btn-text="打印"
              print-btn-type="text"
              :print-type="PrintType.PrintTemplateTypeDNFNotify"
              :data-type="PrintDataType.Raw"
              api="raw_process_orderoutputdetail"
              style="width: auto"
            />
            <el-link
              v-has="'RawDyeingNoticeDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'RawDyeingNoticeEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'RawDyeingNotice_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'RawDyeingNotice_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        <!-- <PrintBtn type="dyeingNoticeDetail" api="dyeing_and_finishingdetail" btnType="text" :tid="1656364462948608" :id="row.id" /> -->
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style></style>
