<template>
  <FildCard :tool-bar="false">
    <div class="mb-[10px]">
      <el-input class="w-[200px] mr-[2px]" placeholder="输入场景值" v-model="state.screen"></el-input>
      <el-input class="w-[200px] mr-[2px]" placeholder="输入文件名称" v-model="state.name"></el-input>
      <el-button type="primary" @click="onUploadImage">上传测试环境</el-button>
      <el-button type="primary">上传正式环境</el-button>
    </div>
    <UploadFile :showSubmitBtn="false" :secene="state.screen" :fileName="state.name" @onUploadSuccess="handUpload" :autoUpload="false" ref="uploadFileRef" />
  </FildCard>
</template>
<script setup lang="ts" name="UpImage">
import FildCard from '@/components/FildCard.vue'
import UploadFile from '@/components/UploadFile/index.vue'
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
const state = reactive({
  screen: 'test',
  name: '',
})

const uploadFileRef = ref()
const handUpload = (row: any) => {}
const onUploadImage = () => {
  if (!state.screen) return ElMessage.warning('场景值不能为空')
  uploadFileRef.value.uploadFun()
}
</script>
<style lang="scss" scoped></style>
