<script setup lang="ts" name="orderPrefixSettings">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { GetOrderPrefix, UpdateOrderPrefix } from '@/api/orderPrefix/index'

const state = reactive({
  dateFormat: '20060102',
  numLength: 2,
})
const { fetchData: getOrderPrefixApi, success: getSuccess, data, msg } = GetOrderPrefix()
const { fetchData: updateOrderPrefixApi, success: updateSuccess, msg: updateMsg } = UpdateOrderPrefix()
const value1 = ref(true) // 营销体系开关
// 采购类单据数据
const purchaseDocuments = reactive([
  { type: '成品采购订单', prefix: 'CG-CP', example: 'CG-CP-202504300001', originalPrefix: 'FPP-CG', id: 1, field: 'fpm_purchase_order' },
  { type: '原料采购订单', prefix: 'CG-YL', example: 'CG-YL-202504300001', originalPrefix: 'RMP', id: 2, field: 'rm_purchase_order' },
  { type: '坯布采购订单', prefix: 'CG-PB', example: 'CG-PB-202504300001', originalPrefix: 'GCP', id: 3, field: 'gfm_purchase_order' },
])

// 计划类单据数据
const planDocuments = reactive([
  { type: '生产计划单', prefix: 'JH-SC', example: 'JH-SC-202504300001', originalPrefix: 'FPP', id: 1, field: 'product_plan_order' },
  { type: '物料计划单', prefix: 'JH-WL', example: 'JH-WL-202504300001', originalPrefix: 'PMC', id: 2, field: 'pmc_grey_plan_order' },
  { type: '销售计划单', prefix: 'JH-XS', example: 'JH-XS-202504300001', originalPrefix: 'ZSHT', id: 3, field: 'sale_plan_order' },
])

// 生产类单据数据
const productionDocuments = reactive([
  { type: '生产通知单', prefix: 'SC-TZ', example: 'SC-TZ-202504300001', originalPrefix: 'FPN', id: 1, field: 'production_notify_order' },
  { type: '生产欠重单', prefix: 'SC-QZ', example: 'SC-QZ-202504300001', originalPrefix: 'FSO', id: 2, field: 'production_shortage_order' },
  { type: '生产变更单', prefix: 'SC-BG', example: 'SC-BG-202504300001', originalPrefix: 'FPC', id: 3, field: 'production_change_order' },
  { type: '生产排产单', prefix: 'SC-PC', example: 'SC-PC-202504300001', originalPrefix: 'SCPC', id: 4, field: 'production_schedule_order' },
])

// 收货类单据数据
const receiveDocuments = reactive([
  { type: '原料采购收货单', prefix: 'SH-YLCG', example: 'SH-YLCG-202504300001', originalPrefix: 'RMPR', id: 1, field: 'rm_purchase_receive_order' },
  { type: '坯布采购收货单', prefix: 'SH-PBCG', example: 'SH-PBCG-202504300001', originalPrefix: 'GFPR', id: 2, field: 'gfm_purchase_receive_order' },
  { type: '坯布生产收货单', prefix: 'SH-PBSC', example: 'SH-PBSC-202504300001', originalPrefix: 'GFPDRC', id: 3, field: 'gfm_produce_receive_order' },
  { type: '原料加工收货单', prefix: 'SH-YLJG', example: 'SH-YLJG-202504300001', originalPrefix: 'PIO', id: 4, field: 'rm_process_in_order' },
  { type: '坯布其他收货单', prefix: 'SH-PBQT', example: 'SH-PBQT-202504300001', originalPrefix: 'GFOR', id: 5, field: 'gfm_other_receive_order' },
])

// 退货类单据数据
const returnDocuments = reactive([
  { type: '原料销售退货单', prefix: 'TH-YLXS', example: 'TH-YLXS-202504300001', originalPrefix: 'RMSR', id: 1, field: 'rm_sale_return_order' },
  { type: '坯布销售退货单', prefix: 'TH-PBXS', example: 'TH-PBXS-202504300001', originalPrefix: 'GFSR', id: 2, field: 'gfm_sale_return_order' },
  { type: '成品销售退货单', prefix: 'TH-CPXS', example: 'TH-CPXS-202504300001', originalPrefix: 'FPRYS', id: 3, field: 'fpm_sale_return_order' },
  { type: '原料采购退货单', prefix: 'TH-YLCG', example: 'TH-YLCG-202504300001', originalPrefix: 'RMRP', id: 4, field: 'rm_purchase_return_order' },
  { type: '坯布采购退货单', prefix: 'TH-PBCG', example: 'TH-PBCG-202504300001', originalPrefix: 'GFPRT', id: 5, field: 'gfm_purchase_return_order' },
  { type: '成品采购退货单', prefix: 'TH-CPCG', example: 'TH-CPCG-202504300001', originalPrefix: 'GCPRT', id: 6, field: 'fpm_purchase_return_order' },
  { type: '坯布生产退货单', prefix: 'TH-PBSC', example: 'TH-PBSC-202504300001', originalPrefix: 'GFPDRT', id: 7, field: 'gfm_produce_return_order' },
])

// 销售类单据数据
const salesDocuments = reactive([
  { type: '原料销售出货单', prefix: 'XS-YL', example: 'XS-YL-202504300001', originalPrefix: 'RMS', id: 1, field: 'rm_sale_order' },
  { type: '坯布销售出货单', prefix: 'XS-PB', example: 'XS-PB-202504300001', originalPrefix: 'GFSD', id: 2, field: 'gfm_sale_delivery_order' },
  { type: '成品销售单', prefix: 'XS-CP', example: 'XS-CP-202504300001', originalPrefix: 'XS', id: 3, field: 'fpm_sale_order' },
  { type: '成品欠货单', prefix: 'XS-CPQH', example: 'XS-CPQH-202504300001', originalPrefix: 'SP', id: 4, field: 'fpm_shortage_order' },
  { type: '调货销售单', prefix: 'XS-CPDH', example: 'XS-CPDH-202504300001', originalPrefix: 'DH', id: 5, field: 'sale_transfer_order' },
  { type: '销售计划变更单', prefix: 'XS-JHBG', example: 'XS-JHBG-202504300001', originalPrefix: 'ZSHT', id: 6, field: 'sale_plan_change_order' },
])

// 应收类单据数据
const receivableDocuments = reactive([
  { type: '成品销售送货单', prefix: 'YS-CPXS', example: 'YS-CPXS-202504300001', originalPrefix: 'FPSYS', id: 1, field: 'fpm_sale_delivery_order' },
  { type: '坯布销售应收单', prefix: 'YS-PBXS', example: 'YS-PBXS-202504300001', originalPrefix: 'BFSAR', id: 2, field: 'gfm_should_collect_order' },
  { type: '原料销售应收单', prefix: 'YS-YLXS', example: 'YS-YLXS-202504300001', originalPrefix: 'RMSAR', id: 3, field: 'rm_should_collect_order' },
  { type: '其他应收账', prefix: 'YS-QTXS', example: 'YS-QTXS-202504300001', originalPrefix: 'OSAR', id: 4, field: 'other_should_collect_order' },
])

// 应付类单据数据
const payableDocuments = reactive([
  { type: '原料采购应付账', prefix: 'YF-YLCG', example: 'YF-YLCG-202504300001', originalPrefix: 'PRMP', id: 1, field: 'rm_purchase_payable_order' },
  { type: '坯布采购应付账', prefix: 'YF-PBCG', example: 'YF-PBCG-202504300001', originalPrefix: 'PGFP', id: 2, field: 'gfm_purchase_payable_order' },
  { type: '成品采购应付账', prefix: 'YF-CPCG', example: 'YF-CPCG-202504300001', originalPrefix: 'PPCS', id: 3, field: 'fpm_purchase_payable_order' },
  { type: '加工费应付账', prefix: 'YF-JG', example: 'YF-JG-202504300001', originalPrefix: 'PPCS', id: 4, field: 'process_payable_order' },
  { type: '染整费应付账', prefix: 'YF-RZ', example: 'YF-RZ-202504300001', originalPrefix: 'PDNF', id: 5, field: 'daf_payable_order' },
  { type: '其他应付账', prefix: 'YF-QT', example: 'YF-QT-202504300001', originalPrefix: 'POTH', id: 6, field: 'other_payable_order' },
])

// 进仓类单据数据
const warehouseInDocuments = reactive([
  { type: '成品销售退货进仓单', prefix: 'JC-XSTH', example: 'JC-XSTH-202504300001', originalPrefix: 'FPSRW', id: 1, field: 'fpm_sale_return_in_order' },
  { type: '成品销售调拨进仓单', prefix: 'JC-XSDB', example: 'JC-XSDB-202504300001', originalPrefix: 'FPSAW', id: 2, field: 'fpm_sale_allocate_in_order' },
  { type: '成品采购进仓单', prefix: 'JC-CG', example: 'JC-CG-202504300001', originalPrefix: 'FPPW', id: 3, field: 'fpm_purchase_in_order' },
  { type: '成品加工进仓单', prefix: 'JC-JG', example: 'JC-JG-202504300001', originalPrefix: 'FPFW', id: 4, field: 'fpm_process_in_order' },
  { type: '成品加工退货进仓单', prefix: 'JC-JGTH', example: 'JC-JGTH-202504300001', originalPrefix: 'FPFRW', id: 5, field: 'fpm_process_return_in_order' },
  { type: '成品其他进仓单', prefix: 'JC-QT', example: 'JC-QT-202504300001', originalPrefix: 'FPOW', id: 6, field: 'fpm_other_in_order' },
  { type: '成品内部调拨进仓单', prefix: 'JC-NBDB', example: 'JC-NBDB-202504300001', originalPrefix: 'FPAW', id: 7, field: 'fpm_internal_allocate_in_order' },
])

// 出仓类单据数据
const warehouseOutDocuments = reactive([
  { type: '成品销售出仓单', prefix: 'CC-XS', example: 'CC-XS-202504300001', originalPrefix: 'FPSE', id: 1, field: 'fpm_sale_out_order' },
  { type: '成品销售调拨出仓单', prefix: 'CC-XSDB', example: 'CC-XSDB-202504300001', originalPrefix: 'FPSAE', id: 2, field: 'fpm_sale_allocate_out_order' },
  { type: '成品采购退货出仓单', prefix: 'CC-CGTH', example: 'CC-CGTH-202504300001', originalPrefix: 'FPPRE', id: 3, field: 'fpm_purchase_return_out_order' },
  { type: '成品加工出仓单', prefix: 'CC-JG', example: 'CC-JG-202504300001', originalPrefix: 'FPFE', id: 4, field: 'fpm_process_out_order' },
  { type: '成品扣款出仓单', prefix: 'CC-KK', example: 'CC-KK-202504300001', originalPrefix: 'FPOE', id: 5, field: 'fpm_deduction_out_order' },
  { type: '成品其他出仓单', prefix: 'CC-QT', example: 'CC-QT-202504300001', originalPrefix: 'FPOE', id: 6, field: 'fpm_other_out_order' },
  { type: '成品出仓预约单', prefix: 'CC-YY', example: 'CC-YY-202504300001', originalPrefix: 'FPRE', id: 7, field: 'fpm_reservation_out_order' },
  { type: '成品内部调拨出仓单', prefix: 'CC-NBDB', example: 'CC-NBDB-202504300001', originalPrefix: 'FPAE', id: 8, field: 'fpm_internal_allocateOut_order' },
])

// 盘点类单据数据
const inventoryDocuments = reactive([
  { type: '原料库存盘点单', prefix: 'PD-YL', example: 'PD-YL-202504300001', originalPrefix: 'RMIC', id: 1, field: 'rm_stock_check_order' },
  { type: '坯布库存盘点单', prefix: 'PD-PB', example: 'PD-PB-202504300001', originalPrefix: 'GFSC', id: 2, field: 'gfm_stock_check_order' },
  { type: '成品库存盘点单', prefix: 'PD-CP', example: 'PD-CP-202504300001', originalPrefix: 'FPIC', id: 3, field: 'fpm_stock_check_order' },
])

// 配布类单据数据
const distributionDocuments = reactive([
  { type: '现货配布单', prefix: 'PB', example: 'PB-202504300001', originalPrefix: 'FPD-PB', id: 1, field: 'fpm_arrange_order' },
  { type: '销售配布变更单', prefix: 'PB-BG', example: 'PB-BG-202504300001', originalPrefix: 'FFPEC', id: 2, field: 'fpm_arrange_change_order' },
])

// 加工类单据数据
const processingDocuments = reactive([
  { type: '染整通知单', prefix: 'JG-PBRZ', example: 'JG-PBRZ-202504300001', originalPrefix: 'DNFN', id: 1, field: 'daf_notice_order' },
  { type: '染整变更单', prefix: 'JG-PBBG', example: 'JG-PBBG-202504300001', originalPrefix: 'DNFC', id: 2, field: 'daf_change_order' },
  { type: '后整通知单', prefix: 'JG-PBHZ', example: 'JG-PBHZ-202504300001', originalPrefix: 'DNFN', id: 3, field: 'finish_notice_order' },
  { type: '复色通知单', prefix: 'JG--CPFS', example: 'JG--CPFS-202504300001', originalPrefix: 'DNFN', id: 4, field: 'redye_notice_order' },
  { type: '原料染整通知单', prefix: 'JG-YLRZ', example: 'JG-YLRZ-202504300001', originalPrefix: 'RMDO', id: 5, field: 'rm_dye_notice_order' },
  { type: '原料加工出货单', prefix: 'JG-YLCH', example: 'JG-YLCH-202504300001', originalPrefix: 'POO', id: 6, field: 'rm_process_out_order' },
  { type: '原料加工收货单', prefix: 'JG-YLSH', example: 'JG-YLSH-202504300001', originalPrefix: 'PIO', id: 7, field: 'rm_process_in_order' },
])

// 报价类单据数据
const quotationDocuments = reactive([
  { type: '染费报价单', prefix: 'BJ-PBRZ', example: 'BJ-PBRZ-202504300001', originalPrefix: 'DNFQ', id: 1, field: 'dye_quote_order' },
  { type: '后整报价单', prefix: 'BJ-PBHZ', example: 'BJ-PBHZ-202504300001', originalPrefix: 'DNFQ', id: 2, field: 'finishing_quote_order' },
  { type: '原料染整报价单', prefix: 'BJ-YLRZ', example: 'BJ-YLRZ-202504300001', originalPrefix: 'RDNFQ', id: 3, field: 'rm_dye_quote_order' },
])
// 调整类单据数据
const adjustmentDocuments = reactive([
  { type: '原料库存调整单', prefix: 'TZ-YL', example: 'TZ-YL-202504300001', originalPrefix: 'DNFQ', id: 1, field: 'rm_adjust_order' },
  { type: '坯布库存调整单', prefix: 'TZ-PB', example: 'TZ-PB-202504300001', originalPrefix: 'DNFQ', id: 2, field: 'gfm_stock_adjust_order' },
  { type: '成品库存调整单', prefix: 'TZ-CP', example: 'TZ-CP-202504300001', originalPrefix: 'RDNFQ', id: 3, field: 'fpm_adjust_order' },
])
async function getData() {
  await getOrderPrefixApi()
  if (!getSuccess.value)
    return ElMessage.error(msg.value)

  // if (data.value.id === 0) {
  //   await addOrderPrefixApi()
  //   if (!addSuccess.value)
  //     return ElMessage.error(addMsg.value)
  //   else
  //     ElMessage.success('初始化成功')
  // }
  // 获取数据成功后，更新所有单据类型的prefix和example
  if (data.value && getSuccess.value) {
    // 更新日期格式和流水号长度
    state.dateFormat = data.value.date_format || '20060102'
    state.numLength = data.value.num_length || 2
    value1.value = data.value.use_sale_system || false

    // 更新所有单据类型的前缀和示例
    const updateDocumentList = (documents: typeof quotationDocuments) => {
      documents.forEach((doc) => {
        doc.prefix = data.value[doc.field]
        // 更新示例，格式为：前缀-日期-流水号，如果开启营销体系则为：前缀-YX-日期-流水号
        const dateExample = state.dateFormat === '20060102'
          ? '20250430'
          : state.dateFormat === '060102' ? '250430' : '0430'
        const serialNumber = `${'0'.repeat(Number(state.numLength))}1`

        // 根据营销体系开关状态决定是否插入YX
        if (value1.value) {
          // 开启营销体系，在前缀和日期之间插入YX
          doc.example = `${doc.prefix}-YX-${dateExample}${serialNumber.slice(-state.numLength)}`
        }
        else {
          // 未开启营销体系，使用原格式
          doc.example = `${doc.prefix}-${dateExample}${serialNumber.slice(-state.numLength)}`
        }
      })
    }

    // 更新所有类型的单据
    updateDocumentList(purchaseDocuments)
    updateDocumentList(planDocuments)
    updateDocumentList(productionDocuments)
    updateDocumentList(receiveDocuments)
    updateDocumentList(returnDocuments)
    updateDocumentList(salesDocuments)
    updateDocumentList(receivableDocuments)
    updateDocumentList(payableDocuments)
    updateDocumentList(warehouseInDocuments)
    updateDocumentList(warehouseOutDocuments)
    updateDocumentList(inventoryDocuments)
    updateDocumentList(distributionDocuments)
    updateDocumentList(processingDocuments)
    updateDocumentList(quotationDocuments)
    updateDocumentList(adjustmentDocuments)
  }
}
onMounted(getData)
// 日期格式选择确认
async function handleSelectDateForm() {
  // 这里可以添加保存日期格式的逻辑
  await updateOrderPrefixApi({
    id: data.value.id,
    update_order: JSON.stringify({
      date_format: state.dateFormat,
    }),
  })
  if (!updateSuccess.value)
    ElMessage.error(updateMsg.value || '保存失败')

  getData()
}
async function handleSelectNum() {
  // 这里可以添加保存日期格式的逻辑
  await updateOrderPrefixApi({
    id: data.value.id,
    update_order: JSON.stringify({
      num_length: state.numLength,
    }),
  })
  if (!updateSuccess.value)
    ElMessage.error(updateMsg.value || '保存失败')

  getData()
}
async function handleChange() {
  // 这里可以添加保存日期格式的逻辑
  await updateOrderPrefixApi({
    id: data.value.id,
    update_order: JSON.stringify({
      use_sale_system: value1.value,
    }),
  })
  if (!updateSuccess.value)
    ElMessage.error(updateMsg.value || '保存失败')

  getData()
}
// 处理前缀输入，转换为大写并过滤非法字符
function handlePrefixInput(val: string, row: any) {
  if (val) {
    // 将输入转换为大写，并只保留字母和连字符
    const upperCaseVal = val.toUpperCase()
    const filteredVal = upperCaseVal.replace(/[^A-Z\-]/g, '')

    // 如果过滤后的值与输入不同，则更新模型值
    if (filteredVal !== val)
      row.prefix = filteredVal
  }
}
// 保存前缀设置
async function savePrefix(type: string, document: (typeof quotationDocuments)[number]) {
  if (!document.prefix)
    return ElMessage.warning('请输入前缀')
  // 验证前缀格式（只允许大写字母和连字符）
  const prefixRegex = /^[A-Z\-]+$/
  if (!prefixRegex.test(document.prefix))
    return ElMessage.warning('前缀只能包含大写字母和连字符')

  // 构造更新对象
  const updateObj: Api.UpdateOrderPrefix.Request = {}
  // 使用document中的field字段作为键，prefix作为值
  updateObj[document.field] = document.prefix

  try {
    // 调用更新API
    await updateOrderPrefixApi({
      id: data.value.id,
      update_order: JSON.stringify(updateObj),
    })

    if (updateSuccess.value)
      ElMessage.success(`${type}单据「${document.type}」前缀保存成功`)

    else
      ElMessage.error(updateMsg.value || '保存失败')
  }
  catch (error) {
    console.error('保存前缀设置出错:', error)
    ElMessage.error('保存失败，请稍后重试')
  }
  getData()
}
// 创建表格组件
function createTable(title: string, documents: typeof quotationDocuments) {
  return {
    title,
    documents,
  }
}
// 所有表格数据
const allTables = [
  createTable('采购类', purchaseDocuments),
  createTable('计划类', planDocuments),
  createTable('生产类', productionDocuments),
  createTable('收货类', receiveDocuments),
  createTable('退货类', returnDocuments),
  createTable('销售类', salesDocuments),
  createTable('应收类', receivableDocuments),
  createTable('应付类', payableDocuments),
  createTable('进仓类', warehouseInDocuments),
  createTable('出仓类', warehouseOutDocuments),
  createTable('盘点类', inventoryDocuments),
  createTable('配布类', distributionDocuments),
  createTable('加工类', processingDocuments),
  createTable('报价类', quotationDocuments),
  createTable('调整类', adjustmentDocuments),
]
</script>

<template>
  <div class="list-page">
    <!--    <h2 class="text-lg font-bold"> -->
    <!--      单据编号规则维护 -->
    <!--    </h2> -->
    <!--    <el-divider class="my-2" /> -->
    <div class="table-card-full">
      <div class="overflow-scroll">
        <FildCard :tool-bar="false" title="">
          <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
            <DescriptionsFormItem label="日期格式:">
              <template #content>
                <el-select v-model="state.dateFormat" placeholder="请选择日期格式" default-first-option class="w-full" @change="handleSelectDateForm">
                  <el-option label="YYYYMMDD" value="20060102" />
                  <el-option label="YYMMDD" value="060102" />
                  <el-option label="MMDD" value="0102" />
                </el-select>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="流水号:">
              <template #content>
                <el-select v-model="state.numLength" placeholder="请选择流水号占位数" default-first-option class="w-full" @change="handleSelectNum">
                  <el-option label="1" value="1" />
                  <el-option label="2" value="2" />
                  <el-option label="3" value="3" />
                  <el-option label="4" value="4" />
                </el-select>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="营销体系">
              <template #content>
                <el-switch
                  v-model="value1"
                  active-text="开启"
                  inactive-text="关闭"
                  @change="handleChange"
                />
              </template>
            </DescriptionsFormItem>
          </div>
        </FildCard>
        <!-- 使用CSS columns实现瀑布流布局 -->
        <div class="waterfall-container mt-2" style="column-count: 2; column-gap: 20px;">
          <div v-for="(table, index) in allTables" :key="index" style="break-inside: avoid;">
            <!-- 动态生成所有分类表格 -->
            <FildCard :tool-bar="false">
              <h2 class="text-lg font-medium mb-2 text-blue-500 flex items-center gap-2 titleBefore">
                {{ table.title }}
              </h2>
              <el-table :data="table.documents" style="width: 100%;height: 100%">
                <el-table-column prop="type" label="单据类型" width="180" />
                <el-table-column prop="prefix" label="前缀" width="180">
                  <template #default="{ row }">
                    <el-input
                      v-model="row.prefix" size="small"
                      maxlength="10"
                      placeholder="仅支持大写字母和连字符"
                      @input="(val) => handlePrefixInput(val, row)"
                      @change="savePrefix(table.title, row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="example" label="示例">
                  <template #default="{ row }">
                    <!-- 使用更健壮的方式处理示例字符串 -->
                    <template v-if="row.example">
                      <!-- 前缀部分（蓝色） -->
                      <span style="color: #3f84f6">{{ row.prefix }}</span>-

                      <!-- 根据营销体系状态显示YX部分（紫色） -->
                      <template v-if="value1">
                        <span style="color: #c084fa">YX</span>-
                      </template>

                      <!-- 日期部分（灰色） -->
                      <span style="color: #747b88">
                        {{
                          state.dateFormat === '20060102' ? '20250430'
                          : state.dateFormat === '060102' ? '250430' : '0430'
                        }}
                      </span>

                      <!-- 流水号部分（绿色） -->
                      <span style="color: #54d382">
                        {{ `${'0'.repeat(Number(state.numLength) - 1)}1` }}
                      </span>
                    </template>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="originalPrefix" label="原前缀" width="120" /> -->
                <!--            <el-table-column label="操作" width="120"> -->
                <!--              <template #default="{ row }"> -->
                <!--                <el-button type="primary" size="small" @click="savePrefix(table.title, row)"> -->
                <!--                  保存 -->
                <!--                </el-button> -->
                <!--              </template> -->
                <!--            </el-table-column> -->
              </el-table>
            </FildCard>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.titleBefore{
  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 20px;
    background: #3b82f6;
  }
}
</style>
