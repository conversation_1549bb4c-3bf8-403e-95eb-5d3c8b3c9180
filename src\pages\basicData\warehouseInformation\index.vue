<script setup lang="ts" name="WarehouseInformation">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Delete, Plus } from '@element-plus/icons-vue'
import AddDialog from '../components/AddStorehouseDialog.vue'
import { debounce, deleteRemark, deleteToast, getFilterData, resetData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { addPhysicalWarehouseList, deletePhysicalWarehouse, getPhysicalWarehouseList, updatePhysicalWarehouse, updatePhysicalWarehouseStatus } from '@/api/warehouseInformation'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

// 获取用户列表
const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getPhysicalWarehouseList()

const state = reactive({
  filterData: {
    name: '',
    status: '',
    warehouse_type_id: '',
  },
  tableConfig: {
    fieldApiKey: 'WarehouseInformation',
    loading,
    showPagition: true,
    showSlotNums: true,
    page,
    size,
    total,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '8%',
    height: 'auto',
    handleSizeChange: (val: number) => handleSizeChange(val),
    handleCurrentChange: (val: number) => handleCurrentChange(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    toolbarRef: '',
  },
  multipleSelection: [],
})

// 获取数据
const getData = debounce(() => {
  ApiCustomerList(getFilterData(state.filterData))
  state.multipleSelection = []
}, 400)

onMounted(() => {
  getData()
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const columnList = ref<any>([
  {
    sortable: true,
    field: 'code',
    title: '编号',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'name',
    title: '名称',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'warehouse_type_name',
    title: '仓库类型',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'manager_name',
    title: '联系人',
    width: 150,
  },
  {
    sortable: true,
    field: 'phone',
    title: '联系电话',
    width: 150,
  },
  {
    sortable: true,
    field: 'address',
    title: '联系地址',
    width: 250,
    soltName: 'address',
  },
  {
    sortable: true,
    field: 'statusBar',
    title: '启用条码管理',
    width: 100,
    soltName: 'statusBar',
  },
  {
    sortable: true,
    field: 'cutStatus',
    title: '剪版仓状态',
    width: 100,
    soltName: 'cutStatus',
  },
  {
    sortable: true,
    field: 'print_title',
    title: '销售配布单打印标题',
    width: 150,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    width: 200,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    fixed: 'right',
    width: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const AddDialogRef = ref()

// 新建仓库资料
function handAdd() {
  AddDialogRef.value.state.modalName = '新建仓库资料'
  AddDialogRef.value.state.form.id = -1
  AddDialogRef.value.state.form.code = ''
  AddDialogRef.value.state.form.name = ''
  AddDialogRef.value.state.form.type = ''
  AddDialogRef.value.state.form.system = ''
  // AddDialogRef.value.state.form.higherName = ''
  AddDialogRef.value.state.form.contacts = ''
  AddDialogRef.value.state.form.phone = ''
  AddDialogRef.value.state.form.address = ''
  AddDialogRef.value.state.form.addressDetail = ''
  AddDialogRef.value.state.form.isBar = true
  AddDialogRef.value.state.form.cutStatus = true
  AddDialogRef.value.state.form.status = true
  AddDialogRef.value.state.form.large_cargo_warehouse_status = true
  AddDialogRef.value.state.form.subscribe_warehouse_status = true
  AddDialogRef.value.state.form.warn_warehouse_status = true
  AddDialogRef.value.state.form.printTitle = ''
  AddDialogRef.value.state.form.remark = ''
  AddDialogRef.value.state.form.addressId = []
  AddDialogRef.value.state.showModal = true
}

// 编辑仓库资料

function handEdit(row: any) {
  // return
  AddDialogRef.value.state.modalName = '编辑仓库资料'

  AddDialogRef.value.state.form.id = row.id
  AddDialogRef.value.state.form.code = row.code
  AddDialogRef.value.state.form.name = row.name
  AddDialogRef.value.state.form.type = row.warehouse_type_id
  AddDialogRef.value.state.form.system = row.sale_system_id
  // AddDialogRef.value.state.form.higherName = row.superior_id
  AddDialogRef.value.state.form.contacts = row.manager_name
  AddDialogRef.value.state.form.phone = row.phone
  AddDialogRef.value.state.form.address = [row.province_id, row.city_id, row.district_id]
  AddDialogRef.value.state.form.addressDetail = row.detail_addr
  AddDialogRef.value.state.form.isBar = row.barcode_manage_status === 1
  AddDialogRef.value.state.form.cutStatus = row.cut_board_warehouse_status === 1
  AddDialogRef.value.state.form.status = row.status === 1
  AddDialogRef.value.state.form.printTitle = row.print_title
  AddDialogRef.value.state.form.remark = row.remark
  AddDialogRef.value.state.form.large_cargo_warehouse_status = row.large_cargo_warehouse_status === 1
  AddDialogRef.value.state.form.subscribe_warehouse_status = row.subscribe_warehouse_status === 1
  AddDialogRef.value.state.form.warn_warehouse_status = row.warn_warehouse_status === 1
  AddDialogRef.value.state.showModal = true
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

const { fetchData: AddFetch, msg: AddMsg, success: AddSuccess } = addPhysicalWarehouseList()

const { fetchData: putFetch, msg: putMsg, success: putSuccess } = updatePhysicalWarehouse()

async function handleSureAdd(form: any) {
  //
  // return
  const query = {
    id: form.id,
    barcode_manage_status: form?.isBar ? 1 : 2,
    cut_board_warehouse_status: form?.cutStatus ? 1 : 2,
    district_id: form?.address?.[2] || 0,
    city_id: form?.address?.[1] || 0,
    province_id: form.address?.[0] || 0,
    manager_name: form?.contacts,
    phone: form?.phone,
    print_title: form?.printTitle,
    remark: form?.remark,
    sale_system_id: form?.system || -1,
    status: form?.status ? 1 : 2,
    warehouse_type_id: form?.type || -1,
    detail_addr: form?.addressDetail,
    code: form?.code,
    name: form.name,
    large_cargo_warehouse_status: form?.large_cargo_warehouse_status ? 1 : 2,
    subscribe_warehouse_status: form?.subscribe_warehouse_status ? 1 : 2,
    warn_warehouse_status: form?.warn_warehouse_status ? 1 : 2,
  }
  form.id === -1 ? await AddFetch(query) : await putFetch(query)
  if (form.id === -1 ? AddSuccess.value : putSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    getData()
  }
  else {
    ElMessage.error(form.id === -1 ? AddMsg.value : putMsg.value)
  }
}

// 编辑状态
const { fetchData: statusFetch, msg: StatusMsg, success: StatusSuccess } = updatePhysicalWarehouseStatus()

async function handStatus(row: any) {
  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    await statusFetch({ id: row.id.toString(), status: row.status === 1 ? 2 : 1 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      AddDialogRef.value.state.showModal = false
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 批量操作
async function handAll(val: number) {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    await statusFetch({ id: ids.toString(), status: val === 1 ? 1 : 2 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 删除数据
const { fetchData: deleteFetch, success: deleteSuccess, msg: deleteMsg } = deletePhysicalWarehouse()

async function handDelete(row: any) {
  const res = await deleteRemark()
  await deleteFetch({ id: row.id.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteRemark()
  const ids: any[] = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  await deleteFetch({ id: ids.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

const router = useRouter()

function handDetail(row: any) {
  router.push({
    name: 'WarehouseInformationDetail',
    query: { id: row?.id },
  })
}
</script>

<template>
  <div class="h-full flex flex-col">
    <FildCard title="" :tool-bar="false">
      <slot>
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="仓库类型:">
            <template #content>
              <SelectComponents v-model="state.filterData.warehouse_type_id" style="width: 200px" api="AdminuenumwarehouseType" label-field="name" value-field="id" clearable />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="名称:">
            <template #content>
              <el-input v-model="state.filterData.name" placeholder="名称" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="状态:">
            <template #content>
              <SelectComponents v-model="state.filterData.status" style="width: 200px" api="StatusListApi" label-field="name" value-field="id" clearable />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="">
            <template #content>
              <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
                清除条件
              </el-button>
            </template>
          </DescriptionsFormItem>
        </div>
      </slot>
    </FildCard>
    <FildCard title="" class="mt-[5px] h-[85vh] flex flex-col">
      <template #right-top>
        <el-button v-has="'WarehouseInformation_add'" style="margin-right: 10px" type="primary" :icon="Plus" @click="handAdd">
          新建
        </el-button>
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button class="mr-[10px]">批量操作</el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <div v-has="'WarehouseInformation_del'">
                <el-dropdown-item @click="handAllDelete">
                  批量删除
                </el-dropdown-item>
              </div>
              <div v-has="'WarehouseInformation_status'">
                <el-dropdown-item @click="handAll(1)">
                  批量启用
                </el-dropdown-item>
              </div>
              <div v-has="'WarehouseInformation_status'">
                <el-dropdown-item @click="handAll(2)">
                  批量禁用
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <div class="grow">
        <Table :config="state.tableConfig" :table-list="data?.list" :column-list="columnList">
          <template #address="{ row }">
            {{ `${row.province_name}${row.city_name}${row.district_name}${row.detail_addr}` }}
          </template>
          <template #statusBar="{ row }">
            <div class="flex items-center">
              <div :class="row.barcode_manage_status === 1 ? 'yuan' : 'yuan_red'" />
              <div :class="row.barcode_manage_status === 1 ? 'yuan_font' : 'yuan_font_active'">
                {{ row.barcode_manage_status === 1 ? '启用' : '禁用' }}
              </div>
            </div>
          </template>
          <template #cutStatus="{ row }">
            <div class="flex items-center">
              <div :class="row.cut_board_warehouse_status === 1 ? 'yuan' : 'yuan_red'" />
              <div :class="row.cut_board_warehouse_status === 1 ? 'yuan_font' : 'yuan_font_active'">
                {{ row.cut_board_warehouse_status === 1 ? '启用' : '禁用' }}
              </div>
            </div>
          </template>
          <template #status="{ row }">
            <div class="flex items-center">
              <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
              <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </div>
            </div>
          </template>
          <template #operate="{ row }">
            <el-space :size="10">
              <el-link v-has="'WarehouseInformation_detail'" type="primary" :underline="false" @click="handDetail(row)">
                查看
              </el-link>
              <el-link v-has="'WarehouseInformation_edit'" type="primary" :underline="false" @click="handEdit(row)">
                编辑
              </el-link>
              <el-link v-has="'WarehouseInformation_del'" :underline="false" type="primary" @click="handDelete(row)">
                删除
              </el-link>
              <el-link v-has="'WarehouseInformation_status'" type="primary" :underline="false" @click="handStatus(row)">
                {{ row.status === 1 ? '禁用' : '启用' }}
              </el-link>
            </el-space>
          </template>
        </Table>
      </div>
    </FildCard>
  </div>
  <AddDialog ref="AddDialogRef" @handle-sure="handleSureAdd" />
</template>

<style lang="scss" scoped>
.yuan {
  width: 10px;
  height: 10px;
  background: #51c41b;
  border-radius: 50%;
  margin-right: 10px;
}

.yuan_red {
  width: 10px;
  height: 10px;
  background: #f5232d;
  border-radius: 50%;
  margin-right: 10px;
}

.yuan_font {
  color: #51c41b;
}

.yuan_font_active {
  color: #f5232d;
}
</style>
