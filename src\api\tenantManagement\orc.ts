import { useRequest } from '@/use/useRequest'

// 获取码单管理列表
export function GetCodeListOrcManagementList() {
  return useRequest({
    url: '/admin/v1/tenantManagement/codeListOrcManagement/getCodeListOrcManagementList',
    method: 'get',
    pagination: true,
  })
}
// 获取充值记录列表
export function GetRechargeHistoryList() {
  return useRequest({
    url: '/admin/v1/tenantManagement/codeListOrcManagement/getRechargeHistoryList',
    method: 'get',
    pagination: true,
  })
}

// 禁用码单管理
export function UpdateCodeListOrcStatusDisable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/codeListOrcManagement/updateCodeListOrcStatusDisable',
    method: 'put',
  })
}

// 启用码单管理
export function UpdateCodeListOrcStatusEnable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/codeListOrcManagement/updateCodeListOrcStatusEnable',
    method: 'put',
  })
}

// 启用码单管理
export function Recharge() {
  return useRequest({
    url: '/admin/v1/tenantManagement/codeListOrcManagement/recharge',
    method: 'post',
  })
}
