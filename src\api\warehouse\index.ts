import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'
// 获取坯布进出仓列表
export function GetGfmWarehouseInOutOrderList() {
  return useRequest<Api.GetGfmWarehouseInOutOrderList.Request, Api.GetGfmWarehouseInOutOrderList.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouseInOutOrder/getGfmWarehouseInOutOrderList',
    method: 'get',
  })
}
// 导出数据
export function GetGfmWarehouseInOutOrderListExport({ nameFile }: { nameFile: string }) {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouseInOutOrder/getGfmWarehouseInOutOrderList',
    method: 'get',
    nameFile,
    nameFileTime: false,
  })
}
// 坯布库存类型枚举(出库)
export function GetGfmWarehouseOutTypeEnum() {
  return useRequest<void, Api.GetGfmWarehouseOutTypeEnum.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseOutTypeEnum',
    method: 'get',
  })
}
// 坯布库存类型枚举(库存来源时候用：只有进仓)
export function GetGfmWarehouseTypeEnum() {
  return useRequest<void, Api.GetGfmWarehouseTypeEnum.Response>({
    url: '/admin/v1/grey_fabric_manage/gfmWarehouse/getGfmWarehouseTypeEnum',
    method: 'get',
  })
}
