t
<script setup lang="ts" name="SalesPriceAdjustmentOrderAdd">
import Big from 'big.js'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { AddSalePriceAdjustOrder, GetIdDataList } from '@/api/salesPriceAdjustmentOrder'
import { formatTime, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import useRouterList from '@/use/useRouterList'
import { useStorageForm } from '@/use/useStorageForm'

const routerList = useRouterList()

interface Params {
  weight_error: number // 空差减重
  id: number
  sale_level_id: number // 销售等级id
  source_bulk_sale_price: number // 原售价
  level_price_adjust: number // 调整
  bulk_sale_price: number // 新售价
  sale_level_name: string // 等级名称
}

const state = reactive<any>({
  tableData: [],
})

const formRules = ref({
  effective_time: [{ required: true, message: '生效时间不能为空', trigger: 'change' }],
})

const formData = ref({
  effective_time: new Date(),
  deadline_time: '',
  remark: '',
  item_data: [],
})

onMounted(() => {
  getList()
})

const { data: localData } = useStorageForm({}, 'salePriceParams')
// 获取列表
const { fetchData: fetchDataList, data: listData } = GetIdDataList()
async function getList() {
  await fetchDataList({ sale_price_color_kind_rel_id: localData.value })
  formatData()
}

// 整理数据
const levelPriceColumn = ref<Params[]>([])
function formatData() {
  state.tableData = listData.value.list?.map((item: any, index: any) => {
    if (index === 0)
      levelPriceColumn.value = item?.level_item

    return {
      ...item,
      target_length_cut_sale_price: formatUnitPriceDiv(item.target_length_cut_sale_price || 0),
      target_weight_cut_sale_price: formatUnitPriceDiv(item.target_weight_cut_sale_price || 0),
      weight_error: formatWeightDiv(item.weight_error || 0),
      source_bulk_sale_price: formatUnitPriceDiv(item.source_bulk_sale_price || 0),
      source_bulk_sale_limit_price: formatUnitPriceDiv(item.source_bulk_sale_limit_price || 0),
      adjust_bulk_sale_price: formatUnitPriceDiv(item.adjust_bulk_sale_price || 0),
      adjust_bulk_sale_limit_price: formatUnitPriceDiv(item.adjust_bulk_sale_limit_price || 0),
      target_bulk_sale_price: formatUnitPriceDiv(item.target_bulk_sale_price || 0),
      target_bulk_sale_limit_price: formatUnitPriceDiv(item.target_bulk_sale_limit_price || 0),
      product_paper_tube_weight: formatWeightDiv(item.product_paper_tube_weight || 0),
      product_weight_error: formatWeightDiv(item.product_weight_error || 0),

      level_item: item?.level_item?.map((level: any) => {
        return {
          ...level,
          weight_error: formatWeightDiv(level.weight_error || 0),
          source_bulk_sale_price: formatUnitPriceDiv(level.source_bulk_sale_price || 0),
          target_bulk_sale_price: formatUnitPriceDiv(level.target_bulk_sale_price || 0),
          price_adjust: formatUnitPriceDiv(level.level_price_adjust || 0),
          level_price_adjust: 0,
        }
      }),
    }
  })
}

const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = AddSalePriceAdjustOrder()
const ruleFormRef = ref()
function handleSure() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      const data = state.tableData?.map((item: any) => {
        const level_item = item.level_item?.map((citem: any) => {
          return {
            level_price_adjust: formatUnitPriceMul(citem.level_price_adjust),
            product_color_kind_id: item.product_color_kind_id,
            product_id: item.product_id,
            sale_level_id: citem.sale_level_id,
            target_bulk_sale_price: formatUnitPriceMul(citem.target_bulk_sale_price),
            weight_error: formatWeightMul(citem.weight_error),
            source_bulk_sale_price: formatUnitPriceMul(citem.source_bulk_sale_price),
          }
        })
        return {
          level_item,
          adjust_bulk_sale_limit_price: formatUnitPriceMul(item.adjust_bulk_sale_limit_price || 0),
          adjust_bulk_sale_price: formatUnitPriceMul(item.adjust_bulk_sale_price || 0),
          product_color_kind_id: item.product_color_kind_id || 0,
          product_id: item.product_id || 0,
          remark: item.remark || '',
          source_bulk_sale_limit_price: formatUnitPriceMul(item.source_bulk_sale_limit_price || 0),
          source_bulk_sale_price: formatUnitPriceMul(item.source_bulk_sale_price) || 0,
          target_length_cut_sale_price: formatUnitPriceMul(item.target_length_cut_sale_price || 0),
          target_weight_cut_sale_price: formatUnitPriceMul(item.target_weight_cut_sale_price || 0),
          weight_error: formatWeightMul(item.weight_error) || 0,
          product_kind_id: item.product_kind_id || 0,
        }
      })
      formData.value.effective_time = formData.value.effective_time ? formatTime(formData.value.effective_time) : ''
      formData.value.deadline_time = formData.value.deadline_time ? formatTime(formData.value.deadline_time) : ''
      await fetchDataAdd(getFilterData({ ...formData.value, item_data: data }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        routerList.push({
          name: 'SalesPriceAdjustmentOrderDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

const selectRow = ref()
function changeData(row: any) {
  selectRow.value = row
}

// 监听调整计算
watch(
  () => [selectRow.value?.adjust_bulk_sale_price, selectRow.value?.adjust_bulk_sale_limit_price],
  () => {
    selectRow.value.target_bulk_sale_price = Number.parseFloat(
      Big(selectRow.value.adjust_bulk_sale_price || 0)
        .plus(selectRow.value.source_bulk_sale_price)
        .toFixed(4),
    )
    selectRow.value.target_bulk_sale_limit_price = Number.parseFloat(
      Big(selectRow.value.adjust_bulk_sale_limit_price || 0)
        .plus(selectRow.value.source_bulk_sale_limit_price)
        .toFixed(4),
    )
  },
)

// 监听新标准计算
watch(
  () => [selectRow.value?.target_bulk_sale_price, selectRow.value?.target_bulk_sale_limit_price],
  () => {
    selectRow.value.adjust_bulk_sale_price = Number.parseFloat(
      Big(selectRow.value.target_bulk_sale_price || 0)
        .minus(selectRow.value.source_bulk_sale_price)
        .toFixed(4),
    )
    selectRow.value.adjust_bulk_sale_limit_price = Number.parseFloat(
      Big(selectRow.value.target_bulk_sale_limit_price || 0)
        .minus(selectRow.value.source_bulk_sale_limit_price)
        .toFixed(4),
    )
  },
)

function changeDataLevel(row: any, field: 'level_price_adjust' | 'target_bulk_sale_price') {
  if (field === 'level_price_adjust') {
    row.target_bulk_sale_price = Number.parseFloat(
      Big(row.level_price_adjust || 0)
        .plus(row.source_bulk_sale_price || 0)
        .toFixed(4),
    )
  }
  else {
    row.level_price_adjust = Number.parseFloat(
      Big(row.target_bulk_sale_price || 0)
        .minus(row.source_bulk_sale_price || 0)
        .toFixed(4),
    )
  }
}

function formatColorName(list: any) {
  return list
    ?.map((item: any) => {
      return item.product_color_code + item.product_color_name
    })
    .join(';')
}
</script>

<template>
  <FildCard :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="formData" :rules="formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="生效时间:" required>
          <template #content>
            <el-form-item prop="effective_time">
              <SelectDate v-model="formData.effective_time" type="datetime" clearable style="width: 200px" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="截止时间:">
          <template #content>
            <el-form-item prop="deadline_time">
              <SelectDate v-model="formData.deadline_time" type="datetime" clearable style="width: 200px" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="deadline_time">
              <el-input v-model="formData.remark" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="" class="mt-[5px]">
    <vxe-table show-overflow :column-config="{ resizable: true }" border height="500" :data="state.tableData" size="mini">
      <vxe-colgroup title="成品颜色类别">
        <vxe-column field="product_kind_name" title="布种类别" width="100px" fixed="left" />
        <vxe-column field="product_code" title="成品编号" width="100px" fixed="left" />
        <vxe-column field="product_name" title="成品名称" width="100px" fixed="left" />
        <vxe-column field="product_paper_tube_weight" title="成品纸筒重量" width="100" fixed="left">
          <template #default="{ row }">
            {{ row.product_paper_tube_weight }}
          </template>
        </vxe-column>
        <vxe-column field="product_weight_error" title="成品空差数量" width="100" fixed="left">
          <template #default="{ row }">
            {{ row.product_weight_error }}
          </template>
        </vxe-column>
        <vxe-column field="product_color_kind_name" title="颜色类别" width="100px" fixed="left" />
        <vxe-column field="product_color_list" title="色号颜色" width="100" fixed="left">
          <template #default="{ row }">
            {{ formatColorName(row.product_color_list) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="单位">
        <vxe-column field="measurement_unit_name" title="主单位" width="60" />
      </vxe-colgroup>
      <vxe-colgroup title="标准报价">
        <vxe-column field="target_length_cut_sale_price" title="剪版价-非主单位" width="120">
          <template #default="{ row }">
            <vxe-input v-model="row.target_length_cut_sale_price" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="target_weight_cut_sale_price" title="剪版价-主单位" width="120">
          <template #default="{ row }">
            <vxe-input v-model="row.target_weight_cut_sale_price" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="weight_error" title="空差减重" width="100">
          <template #default="{ row }">
            <vxe-input v-model="row.weight_error" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="source_bulk_sale_price" title="原标准售价" width="100" />
        <vxe-column field="adjust_bulk_sale_price" title="调整" width="100">
          <template #default="{ row }">
            <vxe-input v-model="row.adjust_bulk_sale_price" type="float" digits="2" @input="changeData(row)" />
          </template>
        </vxe-column>
        <vxe-column field="target_bulk_sale_price" title="新标准售价" width="100">
          <template #default="{ row }">
            <vxe-input v-model="row.target_bulk_sale_price" type="float" digits="2" @input="changeData(row)" />
          </template>
        </vxe-column>
        <vxe-column field="source_bulk_sale_limit_price" title="原最低售价" width="100">
          <template #default="{ row }">
            {{ row.source_bulk_sale_limit_price }}
          </template>
        </vxe-column>
        <vxe-column field="adjust_bulk_sale_limit_price" title="调整" width="100">
          <template #default="{ row }">
            <vxe-input v-model="row.adjust_bulk_sale_limit_price" type="float" digits="2" @input="changeData(row)" />
          </template>
        </vxe-column>
        <vxe-column field="target_bulk_sale_limit_price" title="新最低售价" width="100">
          <template #default="{ row }">
            <vxe-input v-model="row.target_bulk_sale_limit_price" type="float" digits="2" @input="changeData(row)" />
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup v-for="(item, index) in levelPriceColumn" :key="index" :title="item.sale_level_name">
        <vxe-column field="weight_error" title="空差减重" width="100">
          <template #default="{ row }">
            <vxe-input v-model="row.level_item[index].weight_error" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="level_source_bulk_sale_price" title="原售价" width="60">
          <template #default="{ row }">
            {{ row.level_item[index].source_bulk_sale_price }}
          </template>
        </vxe-column>
        <vxe-column field="level_price_adjust" title="调整" width="137px">
          <template #default="{ row }">
            <vxe-input v-model="row.level_item[index].level_price_adjust" type="float" digits="2" @input="changeDataLevel(row.level_item[index], 'level_price_adjust')" />
          </template>
        </vxe-column>
        <vxe-column field="bulk_sale_price" title="新售价" width="137px">
          <template #default="{ row }">
            <vxe-input v-model="row.level_item[index].target_bulk_sale_price" type="float" digits="2" @input="changeDataLevel(row.level_item[index], 'target_bulk_sale_price')" />
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="其它信息">
        <vxe-column field="product_ingredient" title="成分" width="100" />
      </vxe-colgroup>
    </vxe-table>
  </FildCard>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}
::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
</style>
