<script setup lang="ts">
import { computed, ref } from 'vue'
import type { FormInstance } from 'element-plus'

interface Props {
  modelValue: boolean
  rowData: any
  title: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '毛重单价',
})
const emit = defineEmits(['update:modelValue', 'success'])

const showEditGross = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

const ruleFormRef = ref<FormInstance>()
const form = ref({
  number: props.rowData?.GrossWeightCost || 0,
})

const fromRules = {
  number: [{ required: true, message: '请填写毛重单价', trigger: 'blur' }],
}

async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('success', form.value.number)
      showEditGross.value = false
    }
  })
}
defineExpose({
  form,
})
</script>

<template>
  <vxe-modal v-model="showEditGross" show-footer :title="`修改${props.title}`" width="500" height="200" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" size="default" :model="form" label-width="120px" label-position="left" :rules="fromRules">
      <el-form-item :label="props.title" prop="number" controls-position="right">
        <el-input-number v-model.trim="form.number" :min="0" clearable :precision="4" controls-position="right">
          <template #prefix>
            <span>￥</span>
          </template>
        </el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showEditGross = false">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style scoped>

</style>
