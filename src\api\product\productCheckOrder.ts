import { useRequest } from '@/use/useRequest'

/**
 * 拆详细库存生成盘点单-并且审核通过
 */
export function ProductCheckOrderStockDetailSplit() {
  return useRequest({
    url: `/admin/v1/product/productCheckOrder/stock_detail/split`,
    method: 'post',
  })
}
/**
 * 新增毛重成本
 */
export function AddFpmCostPrice() {
  return useRequest({
    url: `/admin/v1/product/fpmCostPrice`,
    method: 'post',
  })
}
/**
 * 编辑毛重成本
 */
export function EditFpmCostPrice() {
  return useRequest({
    url: `/admin/v1/product/fpmCostPrice`,
    method: 'put',
  })
}
