<script setup lang="ts" name="FinishedGoodsInventory">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import DetailModel from '../detailModel/index.vue'
import PickupModel from '../pickupModel/index.vue'
import OccupyStockDialog from '../../../../saleManagement/components/OccupyStockDialog.vue'
import { debounce, deepClone, deleteToast, getFilterData } from '@/common/util'
import EditGrossCost from '@/pages/dyeingManagement/rawStockTable/components/NoDyeingFabirc/EditGrossCost.vue'
import {
  GetStockProductDetailList,
} from '@/api/finishedGoodsInventory'
import {
  formatDate,
  formatHashTag,
  formatLengthMul,
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceDiv,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { AddFpmCostPrice, EditFpmCostPrice, ProductCheckOrderStockDetailSplit } from '@/api'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

export interface Props {
  type: 'aggregate' | 'cylinderNumber' | 'fineCode'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'aggregate',
})

const mainOptionsTablesRef = ref()
const filterData = ref<any>({
  stock_status: 1, // 默认显示正常
  warehouse_id: '',
  customer_id: '',
  product_color_id: '',
  product_id: '',
  product_code: '',
  product_name: '',
  product_level_id: '',
  dyelot_number: '',
  warehouse_bin_id: '',
  stock_show_type: '',
  start_warehouse_inTime: '',
  end_warehouse_inTime: '',
})
const warehouse_in_time = ref()

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  finish_product_code: '',
  finish_product_name: '',
  color_name: '',
  color_code: '',
})

function changePro(data: any) {
  componentRemoteSearch.finish_product_code = data.finish_product_code
  componentRemoteSearch.finish_product_name = data.finish_product_name

  filterData.value.product_color_id = null
}

const {
  fetchData: fetchDataList,
  data: mainDataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = GetStockProductDetailList()
const multipleSelection = ref<any>()
// 获取列表数据
async function getData() {
  await fetchDataList(getFilterData(filterData.value))
  multipleSelection.value = []
}

async function handleExport() {
  mainOptionsTablesRef.value.exportSelectEvent()
}

// const loadingExcel = ref(false)
// async function handleAllExport() {
//   if (!mainDataList.value?.list || !mainDataList.value?.total)
//     return ElMessage.warning('当前无数据可导出')
//   const name_str = '成品细码库存'
//   const {
//     fetchData: getFetch,
//     success: getSuccess,
//     msg: getMsg,
//   } = ExportStockProductDetailList(name_str)
//   loadingExcel.value = true
//   await getFetch({
//     ...getFilterData(filterData.value),
//     download: 1,
//   })
//   if (getSuccess.value) {
//     ElMessage({
//       type: 'success',
//       message: '成功',
//     })
//   }
//   else {
//     ElMessage({
//       type: 'error',
//       message: getMsg.value,
//     })
//   }
//   loadingExcel.value = false
// }

watch(
  () => props.type,
  (val) => {
    if (val === 'fineCode')
      getData()
  },
  { immediate: true },
)

watch(
  () => warehouse_in_time.value,
  (val) => {
    if (val === null) {
      filterData.value.start_warehouse_in_time = ''
      filterData.value.end_warehouse_in_time = ''
      return
    }
    filterData.value.start_warehouse_in_time = formatDate(val[0])
    filterData.value.end_warehouse_in_time = formatDate(val[1])
  },
)

// 表体框架
const mainOptions = reactive<any>({
  columnList: [
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'customer_name',
      title: '所属客户',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_code',
      title: '成品名称',
      minWidth: 120,
      soltName: 'product_code',
    },
    {
      sortable: true,
      field: 'product_color_code',
      title: '色号颜色',
      minWidth: 100,
      soltName: 'product_color_code',
    },
    // {
    //   sortable: true,
    //   field: 'product_color_name',
    //   title: '颜色',
    //   minWidth: 100,
    // },
    {
      sortable: true,
      field: 'dyelot_number',
      title: '染厂缸号',
      width: 100,
    },
    {
      sortable: true,
      field: 'product_level_name',
      title: '成品等级',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_remark',
      title: '成品备注', // 100
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'internal_remark',
      title: '内部备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'roll',
      title: '匹数',
      width: 70,
      isPrice: true,
    },
    {
      sortable: true,
      field: 'volume_number',
      title: '卷号',
      width: 70,
    },
    {
      sortable: true,
      field: 'warehouse_in_type_name',
      title: '来源类型',
      width: 80,
    },
    {
      sortable: true,
      field: 'warehouse_in_order_no',
      title: '来源单号',
      width: 100,
    },
    {
      sortable: true,
      field: 'arrange_order_no',
      title: '配布单号',
      width: 100,
    },
    {
      sortable: true,
      field: 'warehouse_bin_Name',
      title: '仓位',
      width: 80,
    },
    {
      sortable: true,
      field: 'finish_product_width',
      soltName: 'finish_product_width',
      title: '成品幅宽',
      width: 100,
    },
    {
      sortable: true,
      field: 'finish_product_gram_weight',
      soltName: 'finish_product_gram_weight',
      title: '成品克重',
      width: 80,
    },
    {
      sortable: true,
      field: 'dye_factory_color_code',
      title: '染厂色号',
      width: 80,
    },
    {
      sortable: true,
      field: 'shelf_no',
      title: '货架号',
      width: 80,
    },
    {
      sortable: true,
      field: 'remark',
      title: '库存备注',
      width: 100,
    },
    {
      sortable: true,
      field: 'customer_account_num',
      title: '款号',
      width: 100,
    },
    {
      sortable: true,
      field: 'contract_number',
      title: '合同号',
      width: 100,
    },
    {
      sortable: true,
      field: 'digital_code',
      title: '数字码',
      width: 100,
    },
    {
      sortable: true,
      field: 'finish_product_ingredient',
      title: '成分',
      width: 100,
    },
    {
      sortable: true,
      field: 'yarn_count',
      title: '纱支',
      width: 100,
    },
    {
      sortable: true,
      field: 'density',
      title: '密度',
      width: 100,
    },
    {
      sortable: true,
      field: 'weaving_organization_name',
      title: '组织',
      width: 100,
    },

    {
      sortable: true,
      field: 'available_roll',
      title: '可用匹数',
      width: 80,
      soltName: 'available_roll',
    },
    {
      sortable: true,
      field: 'available_weight',
      title: '可用数量',
      width: 80,
      isWeight: true,
    },
    {
      sortable: true,
      field: 'weight',
      title: '基本单位数量',
      width: 100,
      isWeight: true,
    },
    {
      sortable: true,
      field: 'paper_tube_weight',
      title: '纸筒重量',
      width: 100,
      isWeight: true,
    },
    {
      sortable: true,
      field: 'weight_error',
      title: '空差',
      width: 70,
      isWeight: true,
    },
    {
      sortable: true,
      field: 'measurement_unit_name',
      title: '单位',
      width: 70,
    },
    {
      sortable: true,
      field: 'length',
      title: '辅助数量',
      width: 80,
      isLength: true,
    },
    {
      sortable: true,
      field: 'warehouse_in_time',
      title: '入库日期',
      width: 100,
      isDate: true,
      formatTime: 'YYYY-MM-DD',
    },
    {
      sortable: true,
      field: 'stock_status_name',
      title: '库存状态',
      width: 100,
    },
    {
      sortable: true,
      field: 'check_time',
      title: '盘点时间',
      width: 100,
      isDate: true,
      formatTime: 'YYYY-MM-DD',
    },
    {
      sortable: true,
      field: 'check_user_name',
      title: '盘点人',
      width: 100,
    },
    {
      sortable: true,
      field: 'buoyant_weight_price',
      soltName: 'buoyant_weight_price',
      isUnitPrice: true,
      title: '毛重单价',
      width: 100,
      fixed: 'right',
    },
    {
      sortable: true,
      field: 'net_weight_price',
      soltName: 'net_weight_price',
      fixed: 'right',
      isUnitPrice: true,
      title: '净重单价',
      width: 100,
    },
  ],
})

watch(
  () => filterData.value,
  debounce(() => getData(), 400),
  {
    deep: true,
  },
)
const detailRef = ref()
const defaultId = ref(0)
const showModel = ref(false)
function handDetail(row: any) {
  defaultId.value = row.id
  showModel.value = true
}

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([1].includes(_columnIndex))
        return '总计'

      if (
        [
          'paper_tube_weight',
          'weight_error',
          'weight',
          'available_weight',
        ].includes(column.property)
      ) {
        return `${formatWeightDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      if (['roll', 'available_roll'].includes(column.property)) {
        return `${formatPriceDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      return null
    }),
  ]
}

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

const tableConfig = ref({
  fieldApiKey: 'FinishedGoodsInventory_G',
  footerMethod,
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '7%',
  height: '100%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
})

const customerRef1 = ref()
const customerRef2 = ref()

function changeColor(myref: string) {
  if (myref === 'customerRef2')
    customerRef1.value.inputLabel = customerRef2.value.item?.product_color_code
  else
    customerRef2.value.inputLabel = customerRef1.value.item?.product_color_name
}

const showPickupModel = ref(false)
const pickupRef = ref()
const {
  fetchData: addFetch,
  success: addSuccess,
  msg: addMsg,
  loading: splitLoading,
} = ProductCheckOrderStockDetailSplit()

function unpick() {
  if (multipleSelection.value.length === 0) {
    ElMessage.error('请选择数据以拆布')
  }
  else if (multipleSelection.value.length > 1) {
    ElMessage.error('拆布只能选择一条布进行拆分')
  }
  else {
    pickupRef.value.state.info = multipleSelection.value[0]
    showPickupModel.value = true
  }
}

// 确定拆布
async function handleSurePickup(val: any) {
  const res = await deleteToast('确认提交吗？')
  if (!res)
    return

  const list = deepClone(val.list)
  const query = {
    split_weight_param_list: list.map((item: any) => {
      return {
        split_roll: formatPriceMul(item.split_roll || 0),
        split_length: formatLengthMul(item.split_length || 0),
        split_volume_number: item.split_volume_number,
        split_weight: formatWeightMul(item.split_weight),
      }
    }),
  }
  query.split_weight_param_list[0].stock_detail_id = val.info.id
  query.split_weight_param_list[0].stock_sum_id = val.info.stock_product_id
  await addFetch(query)
  if (addSuccess.value) {
    ElMessage.success('成功')
    getData()
    showPickupModel.value = false
  }
  else {
    ElMessage.error(addMsg.value)
  }
}
const showEditGross = ref(false)
const EditGrossCostRef = ref()
const currentRow = ref<any>(null)
const costTitle = ref('毛重单价')
const { fetchData, success: editSuccess, msg: editMsg } = EditFpmCostPrice()
const { fetchData: addGfmCostPrice, success: addSuccess1, msg: addMsg1 } = AddFpmCostPrice()
async function handleEditGrossSuccess(val: any) {
  let buoyant_weight_price = formatUnitPriceDiv(currentRow.value.buoyant_weight_price)
  let net_weight_price = formatUnitPriceDiv(currentRow.value.net_weight_price)

  if (costTitle.value === '毛重单价')
    buoyant_weight_price = val

  else
    net_weight_price = val

  if (!currentRow.value.fpm_cost_price_id) {
    await addGfmCostPrice({
      buoyant_weight_price: formatWeightMul(buoyant_weight_price),
      net_weight_price: formatWeightMul(net_weight_price),
      warehouse_good_in_id: currentRow.value.warehouse_in_order_id,
      warehouse_good_in_type: currentRow.value.warehouse_in_type,
      dyelot_number: currentRow.value.dyelot_number,
      colour_id: currentRow.value.product_color_id,
      product_id: currentRow.value.product_id,
    })
    if (!addSuccess1.value)
      return ElMessage.error(addMsg1.value)
    getData()

    return
  }
  await fetchData({
    id: currentRow.value.fpm_cost_price_id,
    buoyant_weight_price: formatWeightMul(buoyant_weight_price),
    net_weight_price: formatWeightMul(net_weight_price),
  })
  if (!editSuccess.value)
    return ElMessage.error(editMsg.value)
  getData()
}
function handleEditGross(row: any, title: string) {
  costTitle.value = title
  EditGrossCostRef.value.form.number = title === '毛重单价' ? formatWeightDiv(row.buoyant_weight_price) : formatWeightDiv(row.net_weight_price)
  showEditGross.value = true
  currentRow.value = row
}

// 查看占用库存
const occupyStockRef = ref()
function handleShowOccupyStock(row: any) {
  occupyStockRef.value.state.baseData = row
  occupyStockRef.value.state.showModal = true
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="!p-0">
      <el-descriptions
        title=""
        :column="5"
        border
        size="small"
        class=""
      >
        <el-descriptions-item label="仓库名称">
          <SelectComponents
            v-model="filterData.warehouse_id"
            api="GetPhysicalWarehouseDropdownList"
            warehouse_type_id="finishProduction"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="所属客户">
          <SelectDialog
            v-model="filterData.customer_id"
            :query="{ name: componentRemoteSearch.customer_name }"
            api="GetCustomerEnumList"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="(val) => (componentRemoteSearch.customer_name = val)"
          />
        </el-descriptions-item>
        <el-descriptions-item label="进仓日期">
          <SelectDate v-model="warehouse_in_time" />
        </el-descriptions-item>
        <el-descriptions-item label="染厂缸号">
          <el-input
            v-model="filterData.dyelot_number"
            placeholder="请输入染厂缸号"
          />
        </el-descriptions-item>
        <el-descriptions-item label="库存状态">
          <SelectComponents
            v-model="filterData.stock_status"
            api="GetStockStatusEnum"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="成品编号">
          <SelectProductDialog
            v-model="filterData.product_id"
            :label-name="componentRemoteSearch.finish_product_code"
            field="finish_product_code"
            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }"
            @change-value="changePro"
            @on-input="val => (componentRemoteSearch.finish_product_code = val)"
          />
        </el-descriptions-item>
        <el-descriptions-item label="成品名称">
          <SelectProductDialog
            v-model="filterData.product_id"
            :label-name="componentRemoteSearch.finish_product_name"
            field="finish_product_name"
            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }"
            @change-value="changePro"
            @on-input="val => (componentRemoteSearch.finish_product_name = val)"
          />
        </el-descriptions-item>

        <el-descriptions-item label="色号">
          <SelectDialog
            ref="customerRef1"
            key="color2"
            v-model="filterData.product_color_id"
            :disabled="!filterData.product_id"
            :query="{
              finish_product_id: filterData.product_id,
              product_color_code: componentRemoteSearch.color_code,
            }"
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_code',
                title: '色号',
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            @on-input="(val) => (componentRemoteSearch.color_code = val)"
            @change-value="changeColor('customerRef1')"
          />
          <!-- <SelectComponents api="GetFinishProductColorDropdownList" label-field="product_color_code" value-field="id" v-model="filterData.product_color_id" clearable /> -->
        </el-descriptions-item>
        <el-descriptions-item label="颜色">
          <SelectDialog
            key="color1"
            ref="customerRef2"
            v-model="filterData.product_color_id"
            :disabled="!filterData.product_id"
            :query="{
              finish_product_id: filterData.product_id,
              product_color_name: componentRemoteSearch.color_name,
            }"
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_name',
                title: '色号',
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            @on-input="(val) => (componentRemoteSearch.color_name = val)"
            @change-value="changeColor('customerRef2')"
          />
        </el-descriptions-item>
        <el-descriptions-item label="成品等级">
          <SelectComponents
            v-model="filterData.product_level_id"
            api="GetInfoBaseFinishedProductLevelEnumList"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="显示方式">
          <SelectComponents
            v-model="filterData.stock_show_type"
            api="GetStockShowType"
            label-field="name"
            value-field="id"
            clearable
          />
        </el-descriptions-item>
        <el-descriptions-item label="仓位">
          <SelectComponents
            v-model="filterData.warehouse_bin_id"
            :query="{ physical_warehouse_id: filterData.warehouse_id }"
            api="GetPhysicalWarehouseBinListEnum"
            label-field="name"
            value-field="id"
          />
        </el-descriptions-item>
      </el-descriptions>
    </FildCard>
    <FildCard class="table-card-full !p-0" :tool-bar="true">
      <template #right-top>
        <el-space :size="10">
          <el-button v-has="'finishedGoodsInventoryPickup'" type="danger" plain @click="unpick">
            拆布
          </el-button>
          <!--      <BottonExcel class="!ml-2" plain :loading="loadingExcel" @onClickExcel="handleAllExport" title="全部导出"></BottonExcel> -->
          <PrintPopoverBtn
            :print-type="PrintType.PrintTemplateTypeStock"
            :data-type="PrintDataType.Product"
            :list="multipleSelection"
          />
          <BottonExcel
            plain
            title="导出文件"
            @on-click-excel="handleExport"
          />
        <!--   成品布细码标签   -->
        <!--      <el-popover placement="left" title="选择打印" :width="180" trigger="hover"> -->
        <!--        <template #reference> -->
        <!--          <el-button type="primary" plain :icon="Printer">细码标签打印</el-button> -->
        <!--        </template> -->
        <!--        &lt;!&ndash;   成品布细码标签   &ndash;&gt; -->
        <!--        <PrintBtn type="productionXiMa" btnText="打印标签1" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签一']" :list="multipleSelection" /> -->
        <!--        &lt;!&ndash;  成品布细码标签（二维码条形码）    &ndash;&gt; -->
        <!--        <PrintBtn btnText="打印标签2" class="!ml-0" :list="multipleSelection" type="productionXimaLabelCode" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签二']" /> -->
        <!--        <PrintBtn btnText="打印标签3" class="!ml-0" :list="multipleSelection" type="productionXiMa" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签三']" /> -->
        <!--      </el-popover> -->
        <!--   成品布细码标签   -->
        <!--      <PrintBtn type="productionXiMa" btnText="打印标签" :tid="1679373308416256" :list="multipleSelection" /> -->
        <!--      -->
        </el-space>
      </template>
      <Table
        ref="mainOptionsTablesRef"
        :config="tableConfig"
        :table-list="mainDataList.list"
        :column-list="mainOptions.columnList"
      >
        <template #buoyant_weight_price="{ row }">
          <span class="flex cursor-pointer items-center" @click="handleEditGross(row, '毛重单价')">
            ￥{{ formatWeightDiv(row.buoyant_weight_price) || 0 }}
            <el-link :icon="Edit" :underline="false" />
          </span>
        </template>
        <template #net_weight_price="{ row }">
          <span class="flex cursor-pointer items-center" @click="handleEditGross(row, '净重单价')">
            ￥{{ formatWeightDiv(row.net_weight_price) || 0 }}
            <el-link :icon="Edit" :underline="false" />
          </span>
        </template>
        <template #product_code="{ row }">
          {{ formatHashTag(row?.product_code, row?.product_name) }}
        </template>
        <template #product_color_code="{ row }">
          {{ formatHashTag(row?.product_color_code, row?.product_color_name) }}
        </template>
        <template #available_roll="{ row }">
          <div class="flex items-center justify-between w-full">
            {{ formatPriceDiv(row?.available_roll) }}
            <el-icon v-if="row.available_roll < row?.roll || row.available_weight < row?.weight" color="var(--el-color-primary)" size="16" class="ml-2 cursor-pointer" @click="handleShowOccupyStock(row)">
              <View />
            </el-icon>
          </div>
        </template>
        <template #total_price="{ row }">
          ￥{{ row?.total_price }}
        </template>
        <template #operate="{ row }">
          <el-button type="primary" text link @click="handDetail(row)">
            查看进出仓情况
          </el-button>
        </template>
        <template #finish_product_width="{ row }">
          {{ row?.finish_product_width }}
          {{ row?.finish_product_width_unit_name }}
        </template>
        <template #finish_product_gram_weight="{ row }">
          {{ row?.finish_product_gram_weight }}
          {{ row?.finish_product_gram_weight_unit_name }}
        </template>
      </Table>
    </FildCard>
  </div>
  <DetailModel
    :id="defaultId"
    ref="detailRef"
    v-model="showModel"
    :status="3"
  />
  <EditGrossCost
    ref="EditGrossCostRef"
    v-model="showEditGross"
    :title="costTitle"
    :row-data="currentRow"
    @success="handleEditGrossSuccess"
  />
  <PickupModel
    ref="pickupRef"
    v-model="showPickupModel"
    :loading="splitLoading"
    @handle-sure="handleSurePickup"
  />
  <OccupyStockDialog ref="occupyStockRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
