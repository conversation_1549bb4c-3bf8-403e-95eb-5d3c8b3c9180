<script setup lang="ts" name="DictionaryDetail">
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { onBeforeRouteUpdate, useRoute } from 'vue-router'
import { formatBoolean, formatDate } from '@/common/format'
import { AddDictionaryDetailApi, DelDictionaryDetailApi, GetDictionaryDetailApi, GetDictionaryDetailListApi, UpdateDictionaryDetailApi } from '@/api/system'
import { deepClone } from '@/common/util'

const route = useRoute()

const searchInfo = ref<any>({ dictionary_id: Number(route.params.id) })
onBeforeRouteUpdate((to) => {
  if (to.name === 'dictionaryDetail') {
    searchInfo.value.dictionary_id = to.params.id
    getTableData()
  }
})

const formData = ref<any>({
  label: null,
  value: null,
  status: 1,
  sort: null,
})
const rules = ref({
  label: [
    {
      required: true,
      message: '请输入展示值',
      trigger: 'blur',
    },
  ],
  value: [
    {
      required: true,
      message: '请输入字典值',
      trigger: 'blur',
    },
  ],
  sort: [
    {
      required: true,
      message: '排序标记',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '编号不能为空',
      trigger: 'blur',
    },
  ],
})

const tableData = ref<any>([])
function onReset() {
  searchInfo.value = { dictionary_id: Number(route.params.id) }
}

// 查询
const { fetchData: fetchDataList, data: dataList, success: listSuccess, total, page, size, handleSizeChange, handleCurrentChange } = GetDictionaryDetailListApi()
async function getTableData() {
  await fetchDataList({
    ...searchInfo.value,
  })
  if (listSuccess.value) {
    tableData.value = dataList.value.list
    total.value = dataList.value.total as number
  }
}

// 条件搜索前端看此方法
function onSubmit() {
  page.value = 1
  if (searchInfo.value.status === '')
    searchInfo.value.status = null

  getTableData()
}

getTableData()

const { fetchData: fetchDataInfo, data: dataInfo, success: infoSuccess } = GetDictionaryDetailApi()
const type = ref('')
const dialogFormVisible = ref(false)
async function updateSysDictionaryDetailFunc(row: any) {
  await fetchDataInfo({ id: row.id })
  type.value = 'update'
  if (infoSuccess.value) {
    formData.value = dataInfo.value
    dialogFormVisible.value = true
  }
}

function closeDialog() {
  dialogFormVisible.value = false
  formData.value = {
    label: null,
    value: null,
    status: true,
    sort: null,
    dictionary_id: '',
  }
}

// const { fetchData: fetchDataDel, success: delSuccess } = DelDictionaryDetailApi()
// async function deleteSysDictionaryDetailFunc(row: any) {
//   row.visible = false
//   await fetchDataDel({ id: row.id })
//   if (delSuccess.value) {
//     ElMessage({
//       type: 'success',
//       message: '删除成功',
//     })
//     if (tableData.value.length === 1 && page.value > 1)
//       page.value--
//     getTableData()
//   }
// }

const { fetchData: fetchDataUpdate, success: updateSuccess, msg: updateMsg } = UpdateDictionaryDetailApi()
const { fetchData: fetchDataAdd, success: addSuccess } = AddDictionaryDetailApi()

// 行的禁用/启用
async function changeRowStatus(row: any) {
  const params = deepClone(row)
  params.status = row.status === 1 ? 2 : 1
  await fetchDataUpdate(params)
  if (updateSuccess.value) {
    ElMessage({
      type: 'success',
      message: '操作成功',
    })
    closeDialog()
    getTableData()
  }
  else {
    ElMessage.error(updateMsg.value)
  }
}

const dialogForm = ref<any>(null)
async function enterDialog() {
  formData.value.dictionary_id = Number(route.params.id)
  dialogForm.value.validate(async (valid: any) => {
    if (!valid)
      return
    let success = false
    const data = { ...formData.value }
    switch (type.value) {
      case 'create':
        await fetchDataAdd(data)
        success = addSuccess.value
        break
      case 'update':
        await fetchDataUpdate(data)
        success = updateSuccess.value
        break
      default:
        await fetchDataAdd(data)
        success = addSuccess.value
        break
    }
    if (success) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功',
      })
      closeDialog()
      getTableData()
    }
    else {
      ElMessage.error(updateMsg.value)
    }
  })
}
function openDialog() {
  type.value = 'create'
  dialogFormVisible.value = true
}
</script>

<template>
  <div class="block">
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="展示值">
          <el-input v-model="searchInfo.label" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="字典值">
          <el-input v-model="searchInfo.value" type="number" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择">
            <el-option key="1" label="是" :value="1" />
            <el-option key="2" label="否" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">
            查询
          </el-button>
          <el-button icon="refresh" @click="onReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button v-has="'Dictionary_add'" type="primary" icon="plus" @click="openDialog">
          新增字典项
        </el-button>
      </div>
      <el-table :data="tableData" style="width: 100%" tooltip-effect="dark" row-key="ID">
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="日期" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="编号" prop="code" width="120" />

        <el-table-column align="left" label="展示值" prop="label" width="120" />

        <el-table-column align="left" label="字典值" prop="value" width="120" />

        <el-table-column align="left" label="启用状态" prop="status" width="120">
          <template #default="scope">
            {{ formatBoolean(scope.row.status) }}
          </template>
        </el-table-column>

        <el-table-column align="left" label="排序标记" prop="sort" width="120" />
        <el-table-column align="left" label="备注" prop="remark" width="120" />

        <el-table-column align="left" label="按钮组">
          <template #default="scope">
            <el-button type="primary" link icon="edit" @click="updateSysDictionaryDetailFunc(scope.row)">
              变更
            </el-button>
            <el-button type="primary" link @click="changeRowStatus(scope.row)">
              {{ scope.row.status === 1 ? '禁用' : '启用' }}

              <!-- <el-switch v-model="formData.status" active-text="开启" :active-value="1" :inactive-value="2" inactive-text="禁用" /> -->
            </el-button>
            <!-- <el-popover v-model="scope.row.visible" placement="top" width="160">
              <p>确定要删除吗？</p>
              <div style="text-align: right; margin-top: 8px">
                <el-button type="primary" link @click="scope.row.visible = false">
                  取消
                </el-button>
                <el-button type="primary" @click="deleteSysDictionaryDetailFunc(scope.row)">
                  确定
                </el-button>
              </div>
              <template #reference>
                <el-button type="primary" link icon="delete" @click="scope.row.visible = true">
                  删除
                </el-button>
              </template>
            </el-popover> -->
          </template>
        </el-table-column>
      </el-table>

      <div class="gva-pagination mt-3">
        <el-pagination
          :current-page="page"
          :page-size="size"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="dialogForm" :model="formData" :rules="rules" label-width="110px">
        <el-form-item label="编号" prop="code">
          <el-input v-model="formData.code" placeholder="请输入编号" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="展示值" prop="label">
          <el-input v-model="formData.label" placeholder="请输入展示值" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="字典值" prop="value">
          <el-input-number v-model.number="formData.value" step-strictly :step="1" placeholder="请输入字典值" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status" required>
          <el-switch v-model="formData.status" active-text="开启" :active-value="1" :inactive-value="2" inactive-text="禁用" />
        </el-form-item>
        <el-form-item label="排序标记" prop="sort">
          <el-input-number v-model.number="formData.sort" placeholder="排序标记" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">
            取 消
          </el-button>
          <el-button type="primary" @click="enterDialog">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style></style>
