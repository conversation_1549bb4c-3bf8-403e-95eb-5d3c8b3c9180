<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash-es'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { GetInfoSaleShipmentTypeList, type ShipmentInfosType } from '@/api/files/basicData/saleslssueType'

// 类型定义
interface Props {
  type?: 'Add' | 'Edit' | 'Detail' // 类型
  saleSystemIds?: number | undefined // 营销体系ID
  saleShipmentTypeCode?: string | undefined // 发货类型ID
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  type: 'Add',
  saleSystemIds: undefined, // 营销体系ID
  saleShipmentTypeCode: '', // 发货类型code
})

// 定义事件
const emit = defineEmits<{
  'shipment-changes': [shipment: ShipmentInfosType]
}>()

/**
 * 初始化空的发货信息对象
 * 用于重置和初始化数据时使用
 */
const initialShipmentInfo: ShipmentInfosType = {
  code: '',
  create_time: '',
  creator_id: undefined,
  creator_name: '',
  id: undefined,
  name: '',
  order_type: undefined,
  order_type_name: [],
  out_order_type: undefined,
  out_order_type_name: '',
  sale_system_id: [],
  sale_system_name: [],
  update_time: '',
  update_user_name: '',
  updater_id: undefined,
  ware_house_in_id: undefined,
  ware_house_in_name: '',
}

// 发货类型数据的响应式引用
const shipmentInfos = ref<ShipmentInfosType>()

// 表单数据
const formData = ref({
  code: props.saleShipmentTypeCode, // 使用props传入的code初始化
})
const ruleFormRef = ref()

// 表单验证规则
const rules = reactive({
  code: [{ required: true, message: '请选择发货类型', trigger: 'blur' }],
})

const selectRef = ref() // 用于强制刷新 SelectComponents 组件
function refreshData() {
  selectRef.value?.getFetch()
}

const { fetchData, data: shipmentList } = GetInfoSaleShipmentTypeList() // 获取发货类型数据源方法

// 添加一个标记，用于判断是否是首次初始化
const isFirstInit = ref(true)

/**
 * 监听组件类型变化
 * 当组件类型改变时，重置表单数据
 */
// watch(() => props.type, () => {
//   formData.value.code = ''
// }, { immediate: true })

/**
 * 监听营销体系ID变化
 * 处理数据获取和初始化逻辑
 */
watch(() => props.saleSystemIds, async (newVal, _oldVal) => {
  // 首次初始化的情况
  if (!newVal) {
    resetState()
    return
  }
  // 如果不是首次初始化（即手动切换营销体系），则清空所有数据
  if (!isFirstInit.value) {
    formData.value.code = props.saleShipmentTypeCode
    shipmentInfos.value = { ...initialShipmentInfo }

    // 如果没有新的营销体系ID，直接返回
    if (!newVal)
      return

    // 仅获取新的列表数据
    await fetchData({ sale_system_id: newVal })
    return
  }
  // 且存在 saleShipmentTypeCode 时进行首次数据匹配初始化
  if (props.saleShipmentTypeCode) {
    // 获取发货类型列表数据
    await fetchData({ sale_system_id: newVal })
    const matchedShipment = shipmentList.value?.list?.find(
      (item: ShipmentInfosType) => item.code === props.saleShipmentTypeCode,
    )
    if (matchedShipment) {
      formData.value.code = props.saleShipmentTypeCode
      shipmentInfos.value = { ...matchedShipment }
    }
  }
  // 将首次初始化标记设置为 false
  isFirstInit.value = false
}, { immediate: true, deep: true })

/**
 * 监听发货类型编码变化
 */
watch(() => props.saleShipmentTypeCode, async (newVal) => {
  if (!newVal) {
    // 没有值时重置
    resetState()
    return
  }
  // 如果有新值但没有列表数据，先获取列表数据
  if (newVal && (!shipmentList.value || !shipmentList.value.list) && props.saleSystemIds)
    await fetchData({ sale_system_id: props.saleSystemIds })

  // 有新值且有列表数据时初始化
  if (newVal && shipmentList.value?.list)
    initializeShipmentData()
}, { immediate: true, deep: true })

/**
 * 初始化发货类型数据
 * 根据当前的发货类型编码匹配对应的数据，并确保属于当前营销体系
 */
function initializeShipmentData() {
  if (!shipmentList.value?.list || !props.saleShipmentTypeCode || !props.saleSystemIds) {
    resetState()
    return
  }

  const matchedShipment = shipmentList.value.list.find((item: ShipmentInfosType) => {
    // 确保发货类型属于当前营销体系
    const belongsToCurrentSystem = props.saleSystemIds
      ? Array.isArray(item.sale_system_id)
        ? item.sale_system_id.includes(props.saleSystemIds)
        : item.sale_system_id === props.saleSystemIds
      : false
    return item.code === props.saleShipmentTypeCode && belongsToCurrentSystem
  })
  if (matchedShipment) {
    formData.value.code = props.saleShipmentTypeCode
    shipmentInfos.value = { ...matchedShipment }
  }
  else {
    resetState()
  }
}

/**
 * 计算是否为可编辑模式
 */
const isEditMode = computed(() => props.type === 'Add' || props.type === 'Edit')

// 重置状态
function resetState() {
  formData.value.code = ''
  shipmentInfos.value = cloneDeep(initialShipmentInfo)
}

/**
 * 处理选择值变化
 * @param val 选中的发货类型数据
 */
function changeSelectValue(val: ShipmentInfosType | null) {
  if (val)
    shipmentInfos.value = { ...val }
  else
    shipmentInfos.value = { ...initialShipmentInfo }

  const plainData = JSON.parse(JSON.stringify(shipmentInfos.value))
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('shipment-changes', plainData)
}

/**
 * 表单提交处理
 * @param formEl 表单实例
 * @returns 处理后的表单数据
 */
async function submitForm(formEl = ruleFormRef.value) {
  if (!formEl)
    return initialShipmentInfo

  try {
    await formEl.validate()
    const plainData = JSON.parse(JSON.stringify(shipmentInfos.value))
    return plainData
  }
  catch (error) {
    return initialShipmentInfo
  }
}

defineExpose({
  submitForm,
  resetState,
})
</script>

<template>
  <el-form
    ref="ruleFormRef"
    style="max-width: 500px;min-width: 350px;"
    class="form-wrapper"
    :model="formData"
    status-icon
    :rules="rules"
    hide-required-asterisk
  >
    <!-- 发货类型选择/显示 -->
    <el-form-item
      class="form-title-weight"
      :label="props.type === 'Detail' ? '发货类型:' : '发货类型'"
      prop="code"
    >
      <el-space wrap alignment="center">
        <!-- 编辑模式下的选择器 -->
        <template v-if="isEditMode">
          <SelectComponents
            v-if="props.saleSystemIds"
            ref="selectRef"
            v-model="formData.code"
            placeholder="请选择发货类型"
            :query="{
              sale_system_id: props.saleSystemIds,
            }"
            :immediate="Boolean(props.saleSystemIds)"
            api="GetInfoSaleShipmentTypeList"
            label-field="name"
            value-field="code"
            clearable
            style="width:200px;"
            @change-value="changeSelectValue"
          />
          <!-- 未选择营销体系时的禁用状态 -->
          <el-select
            v-else
            placeholder="请先选择营销体系"
            disabled
            style="width: 200px"
          >
            <el-option />
          </el-select>
          <el-icon class="hover-show cursor-pointer active:scale-90" size="20" color="#999" title="配置发货类型" @click="$router.push('/basicData/salesIssueType')">
            <Setting />
          </el-icon>
          <el-icon class="hover-show cursor-pointer active:scale-90" size="20" color="#999" title="刷新" @click="refreshData">
            <RefreshLeft />
          </el-icon>
        </template>

        <!-- 详情模式下的文本显示 -->
        <el-text v-else>
          {{ shipmentInfos?.name }}
        </el-text>
      </el-space>
    </el-form-item>

    <el-form-item :label="props.type === 'Detail' ? '出仓类型:' : '出仓类型'" class="form-init-items">
      <el-text>
        {{ shipmentInfos?.out_order_type_name }}
      </el-text>
    </el-form-item>

    <el-form-item :label="props.type === 'Detail' ? '调至仓库:' : '调至仓库'" class="form-init-items">
      <el-text>
        {{ shipmentInfos?.ware_house_in_name }}
      </el-text>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
.form-title-weight {
  :deep(.el-form-item__label) {
    font-weight: 600;
    font-size: 1em;
    color: #303133;
  }
}

.form-init-items {
  margin-bottom: 0;
}

.form-wrapper:hover {
  .hover-show {
    display: inline-block;
  }
}

.hover-show {
  display: none;
}
</style>
