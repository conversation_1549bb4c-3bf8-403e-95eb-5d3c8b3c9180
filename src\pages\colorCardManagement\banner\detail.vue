<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { GetCarouselBanner } from '@/api/colorCardManagement/banner'
import ImageFileCard from '@/components/UploadFile/FileCard/index.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { ColorCardJumpTypeEnum } from '@/enum/colorCardEnum'
import StatusColumn from '@/components/StatusColumn/index.vue'

const route = useRoute()
const { fetchData, success, msg, data } = GetCarouselBanner()
async function getData() {
  await fetchData({ id: Number(route.params.id) })
  if (!success.value) {
    return ElMessage({
      type: 'error',
      message: msg.value,
    })
  }
}
getData()
</script>

<template>
  <StatusColumn
    v-if="route.params.id"
    :order_id="Number(route.params.id)"
    :status="1"
    order_no=""
    status_name=""
    permission_cancel_key="0"
    permission_reject_key="0"
    permission_pass_key="0"
    permission_wait_key="0"
    permission_deliver_key="0"
    permission_close_key="0"
    permission_edit_key="ColorCardBannerEdit"
    edit_router_name="ColorCardBannerEdit"
    edit_router_type="params"
  />
  <FildCard title="基础资料" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="轮播标题:">
        <template #content>
          {{ data.title }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="跳转目标:">
        <template #content>
          {{ data?.link || '-' }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="跳转类型:">
        <template #content>
          {{ data.jump_type_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="排序:">
        <template #content>
          {{ data?.sort }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="备注:">
        <template #content>
          {{ data?.remark }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="状态:">
        <template #content>
          {{ data?.status_name }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="轮播图" :tool-bar="false" class="mt-[5px]">
    <ImageFileCard v-for="(item, index) in (data.prev_view_url ? data.prev_view_url.split(',') : [])" :key="index" :file-url="item" clear-disabled />
  </FildCard>
  <FildCard v-if="data?.jump_type === ColorCardJumpTypeEnum.IMG" title="预览图片" :tool-bar="false" class="mt-[5px]">
    <ImageFileCard v-for="(item, index) in (data.img_url ? data.img_url.split(',') : [])" :key="index" :file-url="item" clear-disabled />
  </FildCard>
  <FildCard v-if="data?.jump_type === ColorCardJumpTypeEnum.VIDEO" title="预览视频" :tool-bar="false" class="mt-[5px]">
    <ImageFileCard v-for="(item, index) in (data.video_url ? data.video_url.split(',') : [])" :key="index" :file-url="item" clear-disabled />
  </FildCard>
</template>

<style lang="scss" scoped></style>
