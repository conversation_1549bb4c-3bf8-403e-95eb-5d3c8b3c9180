<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { BindTenant, GetQywxSearch, GetTobeDevelopedApp } from '@/api'
import { getFilterData } from '@/common/util'
import type { TableColumnType } from '@/components/Table/type'

withDefaults(defineProps<{
  isEdit: boolean
}>(), {
  isEdit: false,
})

const emit = defineEmits(['handleSubmit'])
const state = reactive({
  showModal: false,
  modalName: '查看企微应用',
  form: {
    agent_id: '123',
    tobe_developed_app_id: 0, // 应用ID
    app_description: '123',
  },
})

const showAccountModal = ref(false)
const showAppModal = ref(false)
const { fetchData: getQywxSearchApi, loading: accountLoading, data: accountData, success: accountSuccess, msg: accountMsg } = GetQywxSearch()
const accountName = ref('')
async function getQywxData() {
  await getQywxSearchApi(getFilterData({ query_str: accountName.value }))
  if (!accountSuccess.value)
    return ElMessage.error(accountMsg.value)
}
function handleSelectAccount() {
  getQywxData()
  showAccountModal.value = true
}
const { fetchData: getAppApi, data: appData, loading: appLoading, msg: appMsg, success: appSuccess } = GetTobeDevelopedApp()

function handleSelectApp() {
  getAppData()
  showAppModal.value = true
}
interface AccountColumnItem {
  company_name: string
  contacts: string
  id: number
  phone: string
}
const accountColumnList = ref<AccountColumnItem[]>([])

const appName = ref('')
// 代开发应用绑定账套
const { fetchData: bindTenantApi, success: bindTenantSuccess, msg: bindMsg, loading: bindLoading } = BindTenant()
async function handleSubmit() {
  await bindTenantApi({
    tenant_management_id: accountColumnList.value.reduce((acc, cur) => [...acc, cur.id], []).join(','),
    tobe_developed_app_id: state.form.tobe_developed_app_id,
  })
  if (!bindTenantSuccess.value)
    return ElMessage.error(bindMsg.value)

  state.showModal = false
  ElMessage.success('绑定成功')
  emit('handleSubmit')
}

const appTableConfig = computed(() => ({
  showRadio: true,
  loading: appLoading.value,
  showPagition: true,
  height: '100%',
  rowConfig: {
    keyField: 'tobe_developed_app_id',
  },
  border: true,
  stripe: true,
}))
// 选择应用 table实例
const TablesRef1 = ref()
// 选择帐套 table实例
const TablesRef2 = ref()
const accountTableConfig = computed<TableColumnType>(() => ({
  showPagition: true,
  showRadio: true,
  rowConfig: {
    keyField: 'id',
  },
  loading: accountLoading.value,
  border: true,
  height: '100%',
  stripe: true,
}))
const columnList1 = ref([
  {
    field: 'corp_name',
    align: 'center',
    title: '企微名称',
    minWidth: 100,
  },
  {
    field: 'corp_id',
    align: 'center',
    title: '企微ID',
    minWidth: 100,
  },
  {
    field: 'app_name',
    align: 'center',
    title: '应用名称',
    minWidth: 100,
  },
  {
    field: 'tenant_management_status_name',
    align: 'center',
    title: '应用介绍',
    minWidth: 100,
  },
  {
    field: 'tenant_management_status_name',
    align: 'center',
    title: '应用模板名称',
    minWidth: 100,
  },
])
const columnList2 = ref([
  {
    field: 'id',
    align: 'center',
    title: '账套ID',
    minWidth: 150,
  },
  {
    field: 'company_name',
    align: 'center',
    title: '账套名称',
    minWidth: 120,
  },
  {
    field: 'contacts',
    align: 'center',
    title: '联系人',
    minWidth: 100,
  },
  {
    field: 'phone',
    align: 'center',
    title: '联系电话',
    minWidth: 120,
  },
])

// 选择应用
function handleSelectAppSubmit() {
  showAppModal.value = false
  const record = TablesRef1.value.tableRef.getRadioRecord()
  state.form.agent_id = record.agent_id
  state.form.tobe_developed_app_id = record.tobe_developed_app_id // 应用id
  state.form.app_description = '' // 应用介绍
}
// 选择账套
function handleSelectTenantSubmit() {
  showAccountModal.value = false
  const record = TablesRef2.value.tableRef.getRadioRecord()

  if (record) {
    accountColumnList.value = [{
      company_name: record.company_name,
      contacts: record.contacts,
      id: record.id,
      phone: record.phone,
    }]
  }
}
async function getAppData() {
  await getAppApi(getFilterData({ query_str: appName.value }))
  if (!appSuccess.value)
    return ElMessage.error(appMsg.value)
}
// 搜索应用
async function handleBlurAppName() {
  getAppData()
}
// 搜索帐套名称
async function handleBlurAccountName() {
  getQywxData()
}

defineExpose({
  state,
  accountColumnList,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" :title="state.modalName" width="500" height="300" :show-footer="isEdit" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex items-center">
      <h2 class="mr-2 font-bold">
        账套名称
      </h2>
      <el-link v-if="isEdit" type="primary" :underline="false" @click="handleSelectAccount">
        选择
      </el-link>
    </div>
    <template v-if="accountColumnList.length">
      <p v-for="(item, index) in accountColumnList" :key="index" class="text-sm mt-2 flex items-center justify-between">
        <el-text size="small" type="info">
          账套ID：{{ item.id }}
        </el-text>
        <el-text size="small" type="info">
          联系人：{{ item.contacts }}
        </el-text>
        <el-text size="small" type="info">
          联系电话：{{ item.phone }}
        </el-text>
      </p>
    </template>
    <p v-else class="text-red-600 text-sm">
      请先添加账套名称
    </p>
    <div class="mt-2 flex items-center">
      <h2 class="mr-2 font-bold">
        应用名称
      </h2>
      <el-link v-if="isEdit" type="primary" :underline="false" @click="handleSelectApp">
        选择
      </el-link>
    </div>
    <p class="text-sm mt-2">
      <el-text size="small" type="info">
        AgentID：{{ state.form.agent_id }}
      </el-text>
    </p>
    <p class="text-sm mt-2">
      <el-text size="small" type="info">
        应用介绍：{{ state.form.app_description }}
      </el-text>
    </p>
    <template #footer>
      <el-button type="primary" :loading="bindLoading" @click="handleSubmit">
        确定
      </el-button>
    </template>
    <vxe-modal v-if="isEdit" v-model="showAppModal" title="选择应用" width="800" height="500" show-footer :mask="false" :lock-view="false" :esc-closable="true" resize>
      <div class="flex flex-col h-full">
        <el-input v-model="appName" class="mb-2 w-[200px]" clearable placeholder="搜索企业名称或ID" @blur="handleBlurAppName" />
        <Table ref="TablesRef1" :config="appTableConfig" :table-list="appData?.list" :column-list="columnList1" />
      </div>
      <template #footer>
        <el-button type="primary" @click="handleSelectAppSubmit">
          确定
        </el-button>
      </template>
    </vxe-modal>
    <vxe-modal v-if="isEdit" v-model="showAccountModal" title="选择账套" width="800" height="500" show-footer :mask="false" :lock-view="false" :esc-closable="true" resize>
      <div class="flex flex-col h-full">
        <el-input v-model="accountName" class="mb-2 w-[200px]" clearable placeholder="搜索账套名称或电话" @blur="handleBlurAccountName" />

        <Table ref="TablesRef2" :config="accountTableConfig" :table-list="accountData?.list" :column-list="columnList2" />
      </div>
      <template #footer>
        <el-button type="primary" :disabled="!TablesRef2?.tableRef?.getRadioRecord()" @click="handleSelectTenantSubmit">
          确定
        </el-button>
      </template>
    </vxe-modal>
  </vxe-modal>
</template>

<style scoped>

</style>
