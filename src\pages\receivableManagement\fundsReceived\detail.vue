<script setup lang="ts">
import { computed, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import {
  getActuallyCollectOrder,
  updateActuallyCollectOrderStatusCancel,
  updateActuallyCollectOrderStatusPass,
  updateActuallyCollectOrderStatusReject,
  updateActuallyCollectOrderStatusWait,
} from '@/api/fundsReceived'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatWeightDiv, sumNum as sumNumCopy } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DetailPageInfo from '@/components/DetailPageInfo/index.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'

const { fetchData: DetailFetch, data: detailData } = getActuallyCollectOrder()
const percentage = computed(() => {
  if (
    (Number(formatPriceDiv(detailData.value.collect_price)) / (Number(formatPriceDiv(detailData.value.actually_collect_price)) + Number(formatPriceDiv(detailData.value.use_advance_price)))) * 100
    > 100
  ) {
    return 100
  }
  else {
    return (
      (Number(formatPriceDiv(detailData.value.collect_price)) / (Number(formatPriceDiv(detailData.value.actually_collect_price)) + Number(formatPriceDiv(detailData.value.use_advance_price))))
      * 100
    ).toFixed(2)
  }
})

const baseInfoKeys = [
  {
    key: 'sale_system_name',
    label: '营销体系名称',
  },
  {
    key: 'customer_name',
    label: '客户名称',
  },
  {
    key: 'settle_type_name',
    label: '收款账户',
  },
  {
    key: 'receive_collect_date',
    label: '收款日期',
    is_date: true,
  },
  {
    key: 'seller_name',
    label: '销售员',
  },
  {
    key: 'voucher_number',
    label: '收款凭证',
  },
  {
    key: 'actually_collect_price',
    label: '收款金额',
    preUnit: '￥',
  },
  // {
  //   key: 'use_advance_price',
  //   label: '使用预付款',
  //   preUnit: '￥',
  // },
  // {
  //   key: 'actually_collect_price',
  //   label: '实收金额',
  //   preUnit: '￥',
  // },
  // {
  //   key: 'write_off_price',
  //   label: '核销金额',
  //   preUnit: '￥',
  // },

  {
    key: 'remark',
    label: '备注',
    copies: 2,
  },
]
const state = reactive<any>({
  statusData: {
    order_no: '',
    audit_status: '',
    audit_status_name: '',
    src_order_no: '', // 来源单号
  },
  // 基础信息
  baseInfoData: {
    order_no: '',
    remark: '',
  },
})

// 表格信息
const advancesReceivedInfo = reactive<any>({
  tableConfig: {
    showSlotNums: false,
    footerMethod: (val: any) => advancesReceivedInfo.footerMethod(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['use_price'].includes(column.field))
          return sumNum(data, 'use_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'advance_collect_order_no',
      title: '预收款单号',
      minWidth: 150,
    },
    {
      field: 'advance_collect_date',
      title: '单据时间',
      minWidth: 100,
      is_date: true,
    },
    {
      field: 'total_advance_price',
      title: '预收金额',
      minWidth: 100,
    },
    {
      field: 'total_remain_price',
      title: '可用金额',
      minWidth: 100,
    },
    {
      field: 'use_price',
      title: '本次使用金额',
      minWidth: 100,
    },
    {
      field: 'remark',
      title: '备注',
      minWidth: 100,
    },
  ],
})

const route = useRoute()

// 获取数据
async function getData() {
  await DetailFetch({ id: route.query.id })
  state.baseInfoData = {
    ...detailData.value,
    receive_collect_date: formatDate(detailData.value.receive_collect_date),
    actually_collect_price: formatTwoDecimalsDiv(detailData.value.actually_collect_price),
    use_advance_price: formatTwoDecimalsDiv(detailData.value.use_advance_price),
    collect_price: formatTwoDecimalsDiv(detailData.value.collect_price),
    write_off_price: formatTwoDecimalsDiv(detailData.value.write_off_price),
  }
  state.statusData = {
    ...detailData.value,
  }
  advancesReceivedInfo.list
    = detailData.value?.adv_item_data?.map((item: any) => {
      return {
        ...item,
        total_advance_price: formatTwoDecimalsDiv(item.total_advance_price),
        total_remain_price: formatTwoDecimalsDiv(item.total_remain_price),
        use_price: formatTwoDecimalsDiv(item.use_price),
      }
    }) || []
  // eslint-disable-next-line ts/no-use-before-define
  fundsReceivedInfo.list
    = detailData.value?.act_item_data?.map((item: any) => {
      const settle_roll = formatTwoDecimalsDiv(item.settle_roll)
      const settle_weight = formatWeightDiv(item.settle_weight)
      const settle_length = formatTwoDecimalsDiv(item.settle_length)
      const settle_price = formatTwoDecimalsDiv(item.settle_price)
      const collected_price = formatTwoDecimalsDiv(item.collected_price)
      const uncollect_price = formatTwoDecimalsDiv(item.uncollect_price)
      // if (item.src_order_type_name.includes('退货')) {
      //   settle_roll = 0 - settle_roll
      //   settle_weight = 0 - settle_weight
      //   settle_length = 0 - settle_length
      //   settle_price = 0 - settle_price
      //   collected_price = 0 - collected_price
      //   uncollect_price = 0 - uncollect_price
      // }
      return {
        ...item,
        settle_roll,
        settle_weight,
        settle_length,
        settle_price,
        collected_price,
        uncollect_price,
        offset_price: formatTwoDecimalsDiv(item.offset_price),
        discount_price: formatTwoDecimalsDiv(item.discount_price),
        deduction_price: formatTwoDecimalsDiv(item.deduction_price),
        // should_collect_price: [2, 4, 6].includes(item.src_order_type) ? formatTwoDecimalsDiv(item.should_collect_price) : formatTwoDecimalsDiv(item.should_collect_price),
        should_collect_price: formatTwoDecimalsDiv(item.should_collect_price),
        actually_collect_price: formatTwoDecimalsDiv(item.actually_collect_price),
        write_off_price: formatTwoDecimalsDiv(item.write_off_price),
      }
    }) || []
}
onMounted(() => {
  getData()
})

// 实收信息
const fundsReceivedInfo = reactive<any>({
  tableConfig: {
    showSlotNums: false,
    footerMethod: (val: any) => fundsReceivedInfo.footerMethod(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['settle_roll'].includes(column.field))
          return sumNum(data, 'settle_roll', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['settle_length'].includes(column.field))
          return sumNum(data, 'settle_length', '')

        if (['settle_price'].includes(column.field))
          return sumNum(data, 'settle_price', '')

        if (['collected_price'].includes(column.field))
          return sumNum(data, 'collected_price', '')

        if (['uncollect_price'].includes(column.field))
          return sumNum(data, 'uncollect_price', '')

        if (['actually_collect_price'].includes(column.field))
          return sumNum(data, 'actually_collect_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'src_order_no',
      fixed: 'left',
      title: '单据编号',
      minWidth: '8%',
    },
    {
      field: 'src_order_type_name',
      fixed: 'left',
      title: '单据类型',
      minWidth: 100,
    },
    {
      field: 'src_order_date',
      fixed: 'left',
      title: '单据日期',
      minWidth: 100,
      is_date: true,
    },
    {
      field: 'settle_roll',
      title: '结算条数/件数',
      minWidth: 100,
    },
    {
      field: 'settle_weight',
      title: '结算数量',
      minWidth: 100,
    },
    {
      field: 'settle_length',
      title: '结算辅助数量',
      minWidth: 100,
    },
    {
      field: 'settle_price',
      title: '结算金额',
      minWidth: 100,
    },
    {
      field: 'offset_price',
      title: '优惠金额',
      minWidth: 100,
    },
    {
      field: 'discount_price',
      title: '折扣金额',
      minWidth: 100,
    },
    {
      field: 'deduction_price',
      title: '扣款金额',
      minWidth: 100,
    },
    {
      field: 'uncollect_price',
      title: '未收金额',
      minWidth: 100,
    },
    // {
    //   field: 'should_collect_price',
    //   title: '应收金额',
    //   minWidth: 100,
    // },
    {
      field: 'actually_collect_price',
      title: '本次收款金额',
      minWidth: 100,
    },
    // {
    //   field: 'write_off_price',
    //   title: '核销金额',
    //   minWidth: 100,
    // },
    {
      field: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      field: 'collected_price',
      title: '已收金额',
      minWidth: 100,
    },
  ],
})
// 操作-审核、驳回、作废、消审
async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateActuallyCollectOrderStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateActuallyCollectOrderStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateActuallyCollectOrderStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateActuallyCollectOrderStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}
</script>

<template>
  <StatusColumn
    :order_no="state.statusData.order_no"
    :order_id="state.statusData.id"
    :status="state.statusData.audit_status"
    :status_name="state.statusData.audit_status_name"
    permission_print_key=""
    permission_reject_key="FundsReceived_reject"
    permission_pass_key="FundsReceived_pass"
    permission_cancel_key="FundsReceived_cancel"
    permission_wait_key="FundsReceived_wait"
    permission_edit_key="FundsReceived_edit"
    edit_router_name="FundsReceivedEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <!--  -->
  <!-- 基础信息 -->
  <DetailPageInfo key="baseInfo" class="mt-[5px]" title="基础信息" :info-keys="baseInfoKeys" :info-data="state.baseInfoData" />
  <!-- 预收信息 -->
  <FildCard class="mt-[5px]" title="核销信息" :tool-bar="false">
    <div class="verification-box">
      <div>
        <div class="top-font">
          本次核销情况
        </div>
        <div class="flex-box">
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ formatPriceDiv(detailData.actually_collect_price) || 0 }}
            </div>
            <div class="font">
              收款金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ formatPriceDiv(detailData.use_advance_price) || 0 }}
            </div>
            <div class="font">
              预收金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ formatPriceDiv(detailData.collect_price) }}
            </div>
            <div class="font">
              已使用金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ detailData?.act_item_data?.length ? formatPriceDiv(sumNumCopy(detailData?.act_item_data, 'offset_price')) : 0 }}
            </div>
            <div class="font">
              优惠金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ detailData?.act_item_data?.length ? formatPriceDiv(sumNumCopy(detailData?.act_item_data, 'discount_price')) : 0 }}
            </div>
            <div class="font">
              折扣金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ detailData?.act_item_data?.length ? formatPriceDiv(sumNumCopy(detailData?.act_item_data, 'deduction_price')) : 0 }}
            </div>
            <div class="font">
              扣款金额
            </div>
          </div>
        </div>
      </div>
      <div class="bg-box">
        <el-progress class="custom-progress" type="dashboard" :percentage="percentage" color="#5599f1" />
      </div>
      <div class="shu" />
      <div>
        <div class="use-font">
          使用预收款
        </div>
        <Table
          v-if="detailData.adv_item_data?.length > 0"
          class="mt-[10px]"
          :config="advancesReceivedInfo.tableConfig"
          :table-list="advancesReceivedInfo.list"
          :column-list="advancesReceivedInfo.columnList"
        />
        <div v-if="!detailData.adv_item_data" class="no_data">
          <el-icon :size="80">
            <MessageBox />
          </el-icon>
          <div class="text">
            该客户暂无预收款
          </div>
        </div>
      </div>
    </div>
  </FildCard>
  <!-- 实收信息 -->
  <FildCard class="mt-[5px]" title="本次核销订单明细" :tool-bar="false">
    <Table :config="fundsReceivedInfo.tableConfig" :table-list="fundsReceivedInfo.list" :column-list="fundsReceivedInfo.columnList" />
  </FildCard>
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
.verification-box {
  display: flex;
  .top-font {
    font-weight: 500;
    font-size: 14px;
    margin-left: 30px;
  }
  .flex-box {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    width: 360px;
    height: 200px;
    .mony-box {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      font-weight: 500;
      font-size: 12px;
      margin-left: 50px;
    }
  }
  .bg-box {
    width: 150px;
    height: 150px;
    background: linear-gradient(to bottom, #4992f0, #ffffff);
    margin-top: 60px;
    margin-left: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
  }
  .shu {
    width: 2px;
    height: 200px;
    background: #d7d7d7;
    margin-left: 30px;
    margin-top: 30px;
    margin-right: 20px;
  }
  .use-font {
    font-weight: 500;
    font-size: 14px;
  }

  .no_data {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 40px 0;
    user-select: none;
    color: #999;
  }
}

::v-deep(.custom-progress) {
  .el-progress__text {
    color: #3e77e1;
    font-weight: 500;
  }
}
.my-btn {
  background-color: #fff;
  border-color: red;
  color: red;
}
</style>
