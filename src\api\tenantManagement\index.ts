import { useRequest } from '@/use/useRequest'

export function DisableTenantManagement() {
  return useRequest({
    url: '/admin/v1/tenantManagement/disableTenantManagement',
    method: 'put',
  })
}

export function EnableTenantManagement() {
  return useRequest({
    url: '/admin/v1/tenantManagement/enableTenantManagement',
    method: 'put',
  })
}

/**
 * 租户管理状态
 * @constructor
 */
export function GetTenantManagementStatusEnum() {
  return useRequest({
    url: '/admin/v1/tenantManagement/enum/getTenantManagementStatusEnum',
    method: 'get',
  })
}

/**
 * 获取状态枚举列表
 * @constructor
 */
export function GetTenantManagementStateEnum() {
  return useRequest({
    url: 'admin/v1/basic_data/enum/getStateEnum',
    method: 'get',
  })
}

/**
 * 启用租户套餐
 */
export function UpdateStatusEnable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/tenantPackage/updateStatusEnable',
    method: 'put',
  })
}

/**
 * 启用租户套餐
 */
export function UpdateStatusDisable() {
  return useRequest({
    url: '/admin/v1/tenantManagement/tenantPackage/updateStatusDisable',
    method: 'put',
  })
}

export function AddTenantPackage() {
  return useRequest({
    url: '/admin/v1/tenantManagement/tenantPackage/addTenantPackage',
    method: 'post',
  })
}

export function UpdateTenantPackage() {
  return useRequest({
    url: '/admin/v1/tenantManagement/tenantPackage/updateTenantPackage',
    method: 'put',
  })
}

export function GetTenantPackage() {
  return useRequest({
    url: '/admin/v1/tenantManagement/tenantPackage/getTenantPackage',
    method: 'get',
  })
}

// 更新用户菜单
export function SyncTenantPackageToUser() {
  return useRequest({
    url: '/admin/v1/tenantManagement/tenantPackage/syncTenantPackageToUser',
    method: 'post',
  })
}
export * from './orc'
