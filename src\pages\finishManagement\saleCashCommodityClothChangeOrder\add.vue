<script setup lang="ts" name="SaleCashCommodityClothChangeOrderAdd">
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import AccordingLibAdd from '../components/AccordingRepertoryAdd.vue'
import FineSizeAdd from '../components/FineSizeSaleCashCommodityClothAdd.vue'
import { addFpmChangeArrangeOrder, getFpmArrangeOrderEnum } from '@/api/saleCashCommodityClothChangeOrder'
import { formatDate, formatLengthDiv, formatLengthMul, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { deleteToast, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { BusinessUnitIdEnum } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
let uuid = 0
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const state = reactive<any>({
  formInline: {
    arrange_order_no: '',
    arrange_order_id: 0,
    after_out_order_type: '',
    sale_system_name: '',
    process_factory_name: '',
    receive_tag: '',
    before_biz_unit_name: '',
    after_biz_unit_id: '',
    receive_addr: '',
    receive_phone: '',
    warehouse_name: '',
    after_arrange_to_warehouse_id: '',
    sale_user_name: '',
    sale_follower_name: '',
    driver_name: '',
    logistics_company_name: '',
    internal_remark: '',
    sale_remark: '',
  },
})

const bulkShow = ref(false)
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    filterStatus: false,
    fieldApiKey: 'SaleCashCommodityClothChangeAdd',
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['arrange_weight'].includes(column.field))
          return sumNum(data, 'arrange_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['arrange_length'].includes(column.field))
          return sumNum(data, 'arrange_length', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'arrange_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
        {
          field: 'push_roll',
          title: '下推匹数',
          minWidth: 100,
        },
        {
          field: 'push_weight',
          title: '下推数量',
          minWidth: 100,
        },
        {
          field: 'push_length',
          title: '下推辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '出仓数量信息',
      childrenList: [
        {
          field: 'arrange_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      title: '出仓辅助数量信息',
      fixed: 'right',
      childrenList: [
        {
          field: 'arrange_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '单据备注信息',
      childrenList: [
        {
          field: 'arrange_item_remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '变更信息',
      fixed: 'right',
      childrenList: [
        {
          field: 'change_roll',
          soltName: 'change_roll',
          title: '变更匹数',
          minWidth: 100,
        },
        {
          field: 'change_weight',
          soltName: 'change_weight',
          title: '变更数量',
          minWidth: 100,
        },
        {
          field: 'change_length',
          soltName: 'change_length',
          title: '变更辅助数量',
          minWidth: 100,
        },
        // {
        //   field: 'change_send_roll',
        //   title: '变更出仓匹数',
        //   minWidth: 120,
        // },
        // {
        //   field: 'change_send_weight',
        //   title: '变更出仓数量',
        //   minWidth: 120,
        // },
        // {
        //   field: 'change_send_length',
        //   title: '变更出仓辅助数量',
        //   minWidth: 120,
        // },
      ],
    },
    {
      title: '最终数量',
      fixed: 'right',
      childrenList: [
        {
          field: 'result_roll',
          soltName: 'result_roll',
          title: '最终匹数',
          minWidth: 100,
        },
        {
          field: 'result_weight',
          soltName: 'result_weight',
          title: '最终数量',
          minWidth: 100,
        },
        {
          field: 'result_length',
          soltName: 'result_length',
          title: '最终辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
    // {
    //   title: '',
    //   fixed: 'right',
    //   childrenList: [
    //     {
    //       field: '',
    //       soltName: 'operate',
    //       title: '操作',
    //       width: 100,
    //     },
    //   ],
    // },
  ],
  handleSure: (list: any) => {
    // 成品成分
    // 取配布单不可修改
    list = list.map((item: any) => {
      return {
        // ...item,
        uuid: ++uuid,
        sum_stock_id: item.stock_product_id,
        quote_order_no: '',
        product_code: item.product_code,
        product_name: item.product_name,
        product_id: item.product_id,
        customer_name: item.customer_name,
        customer_id: item.customer_id,
        product_color_code: item.product_color_code,
        product_color_id: item.product_color_id,
        product_color_name: item.product_color_name,
        dye_factory_dyelot_number: item.dyelot_number,
        product_level_name: item.product_level_name,
        product_level_id: item.product_level_id,
        product_remark: item.product_remark,
        product_craft: item.finish_product_craft,
        sum_stock_roll: item.roll,
        sum_stock_weight: item.weight,
        sum_stock_length: item.length,
        unit_name: item.measurement_unit_name,
        unit_id: item.measurement_unit_id,
        product_ingredient: item.finish_product_ingredient,
        id: item?.id,
        warehouse_bin_id: item.warehouse_bin_id,
        item_fc_data: [],
        remark: '',
      }
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  handleSureFineSize: (list: any) => {
    let total_weight = 0
    let weight_error = 0
    let out_length = 0
    let settle_error_weight = 0
    let change_roll = 0
    let change_weight = 0
    let change_length = 0
    list.forEach((item: any) => {
      total_weight = currency(total_weight).add(item.base_unit_weight).value
      weight_error = currency(weight_error).add(item.weight_error).value
      out_length = currency(out_length).add(item.length).value
      settle_error_weight = currency(settle_error_weight).add(item.settle_error_weight).value
      item.item_fc_change_data.forEach((v: any) => {
        change_roll = currency(change_roll).add(v.change_roll).value
        change_weight = currency(change_weight).add(v.change_weight).value
        change_length = currency(change_length).add(v.change_length).value
      })
    })

    // const total_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)
    //   const settle_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_weight), 0)
    // const out_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    // const settle_error_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_error_weight), 0)
    const actually_weight = Number(total_weight) - Number(weight_error)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 出仓数量 = 细码数量之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_weight = Number(total_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].out_length = Number(out_length.toFixed(2))
    // 空差 = 细码空差之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    // 结算数量 = 总数量 - 空差
    //   finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = settle_weight.toFixed(2)
    //   码单数量 = 出仓数量 - 码单空差
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].actually_weight = Number(actually_weight.toFixed(2))
    // 结算空差
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_error_weight = Number(settle_error_weight.toFixed(2))
    // 结算数量
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number((Number(actually_weight) - settle_error_weight).toFixed(2))
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].change_roll = Number(change_roll.toFixed(2))
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].change_weight = Number(change_weight.toFixed(2))
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].change_length = Number(change_length.toFixed(2))

    computedKeys()
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.id === id)
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit,
})
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 判断当前行是否存在细码
const hasXima = computed(() => (row: any) => {
  if (row && !row?.item_fc_data?.length)
    return false
  return true
})

function computedKeys() {
  // 计算最终信息
  finishProductionOptions.datalist.forEach((item: any) => {
    // 没有细码--更改的是下推信息--不更新最数量
    if (!hasXima.value(item))
      return
    item.result_roll = currency(item.push_roll).add(item.change_roll).value
    item.result_length = currency(item.push_length).add(item.change_length).value
    item.result_weight = currency(item.push_weight).add(item.change_weight).value
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    // computedKeys()
    nextTick(() => {
      tablesRef.value.tableRef.updateFooter()
    })
  },
  { deep: true },
)

function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.isEdit = true
  FineSizeAddRef.value.showDialog(row)
}

/**
 * 最终匹数、最终数量、最终辅助数量都必须大于或者等于0
 *
 * 最终匹数>0时，需要判断细码里的总匹数要小于或者等于最终匹数
 *
 * 最终匹数=0时不需要判断细码匹数总数
 */
function checkResultData() {
  return finishProductionOptions.datalist.every((item: any) => {
    if (item.result_roll < 0 || item.result_weight < 0 || item.result_length < 0) {
      ElMessage.error(`成品编号为：${item.product_code}的成品的最终匹数、最终数量、最终辅助数量都必须大于或者等于0`)
      return false
    }
    if (item.result_roll > 0) {
      const sum = item.item_fc_data.reduce((pre: any, val: any) => currency(pre).add(val.change_roll).value, 0)
      if (sum > item.result_roll) {
        ElMessage.error(`成品编号为:${item.product_code}的细码匹数总数不能大于最终匹数`)
        return false
      }
    }
    return true
  })
}

const formRef = ref()
// 提交所有数据
const { fetchData: EditFetch, data: successData, success: EditSuccess, msg: EditMsg } = addFpmChangeArrangeOrder()
function submitAddAllData() {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid)
      return

    if (!checkResultData())
      return

    const query = {
      ...state.formInline,
      item_data: [],
    }
    query.item_data = finishProductionOptions.datalist.map((item: any) => {
      const item_fc_data = item.item_fc_data.map((v: any) => {
        const item_fc_change_data = v.item_fc_change_data.map((row: any) => {
          return {
            ...row,
            change_roll: formatTwoDecimalsMul(row.change_roll),
            change_weight: formatWeightMul(row.change_weight),
            change_actually_weight: formatWeightMul(row.change_actually_weight),
            weight_error: formatWeightMul(row.weight_error),
            settle_error_weight: formatWeightMul(row.settle_error_weight),
            change_settle_weight: formatWeightMul(row.change_settle_weight),
            change_length: formatLengthMul(row.change_length),
          }
        })
        return {
          ...v,
          roll: formatTwoDecimalsMul(Number(v.roll)),
          base_unit_weight: formatWeightMul(Number(v.base_unit_weight)),
          paper_tube_weight: formatWeightMul(Number(v.paper_tube_weight)),
          weight_error: formatWeightMul(Number(v.weight_error)),
          settle_weight: formatWeightMul(Number(v.settle_weight)),
          length: formatLengthMul(Number(v.length)),
          actually_weight: formatWeightMul(Number(v.actually_weight)),
          settle_error_weight: formatWeightMul(Number(v.settle_error_weight)),
          item_fc_change_data,
        }
      })
      return {
        ...item,
        out_roll: formatTwoDecimalsMul(Number(item.out_roll)),
        sum_stock_roll: formatTwoDecimalsMul(Number(item.sum_stock_roll)),
        sum_stock_weight: formatWeightMul(Number(item.sum_stock_weight)),
        sum_stock_length: formatLengthMul(Number(item.sum_stock_length)),
        total_weight: formatWeightMul(Number(item.total_weight)),
        weight_error: formatWeightMul(Number(item.weight_error)),
        settle_weight: formatWeightMul(Number(item.settle_weight)),
        unit_price: formatUnitPriceMul(Number(item.unit_price)),
        out_length: formatLengthMul(Number(item.out_length)),
        length_unit_price: formatUnitPriceMul(Number(item.length_unit_price)),
        other_price: formatTwoDecimalsMul(Number(item.other_price)),
        total_price: formatTwoDecimalsMul(Number(item.total_price)),
        actually_weight: formatWeightMul(Number(item.actually_weight)),
        settle_error_weight: formatWeightMul(Number(item.settle_error_weight)),
        arrange_roll: formatTwoDecimalsMul(Number(item.arrange_roll)),
        arrange_weight: formatWeightMul(Number(item.arrange_weight)),
        arrange_length: formatLengthMul(Number(item.arrange_length)),
        change_roll: formatTwoDecimalsMul(Number(item.change_roll)),
        change_weight: formatWeightMul(Number(item.change_weight)),
        change_length: formatLengthMul(Number(item.change_length)),
        result_roll: formatTwoDecimalsMul(Number(item.result_roll)),
        result_weight: formatWeightMul(Number(item.result_weight)),
        result_length: formatLengthMul(Number(item.result_length)),
        item_fc_data,
      }
    })

    await EditFetch(
      getFilterData({
        ...query,
      }),
    )

    if (!EditSuccess.value) {
      ElMessage.error(EditMsg.value)
      return
    }

    ElMessage.success('提交成功')
    routerList.push({
      name: 'SaleCashCommodityClothChangeOrderDetail',
      query: {
        id: successData.value.id,
      },
    })
  })
}
const route = useRoute()
const { fetchData: detailFetch, data: detailData } = getFpmArrangeOrderEnum()
async function getData() {
  await detailFetch({
    id: route.query.id,
  })
  state.formInline = {
    ...detailData.value,
    arrange_order_id: detailData.value.id || 0,
    store_keeper_id: detailData.value.store_keeper_id || '',
    sale_user_id: detailData.value.sale_user_id || '',
    process_factory_id: detailData.value.process_factory_id || '',
    sale_follower_id: detailData.value.sale_follower_id || '',
    arrange_user_id: detailData.value.arrange_user_id || '',
    logistics_company_id: detailData.value.logistics_company_id || '',
    arrange_time: formatDate(detailData.value.arrange_time),
    after_out_order_type: detailData.value.out_order_type,
    after_out_order_type_name: detailData.value.out_order_type_name,
    before_biz_unit_name: detailData.value.biz_unit_name,
    before_biz_unit_id: detailData.value.biz_unit_id,
    after_biz_unit_name: detailData.value.biz_unit_name,
    after_biz_unit_id: detailData.value.biz_unit_id || '',
    before_arrange_to_warehouse_name: detailData.value.warehouse_name || detailData.value.before_arrange_to_warehouse_name || '',
    before_arrange_to_warehouse_id: detailData.value.warehouse_id || detailData.value.before_arrange_to_warehouse_id || '',
    arrange_order_no: detailData.value.order_no,
    after_arrange_to_warehouse_id: detailData.value.warehouse_id || detailData.value.before_arrange_to_warehouse_id || '',
  }
  finishProductionOptions.datalist = detailData.value?.item_data?.map((item: any) => {
    let change_send_roll = 0
    let change_send_weight = 0
    let change_send_length = 0

    const item_fc_data
      = item.item_fc_data?.map((v: any) => {
      // 自动计算变更出仓数量，来源是细码框内的总变更数
        change_send_roll = currency(change_send_roll).add(v.change_roll).value
        change_send_weight = currency(change_send_weight).add(v.change_weight).value
        change_send_length = currency(change_send_length).add(v.change_length).value

        return {
          ...v,
          roll: formatTwoDecimalsDiv(Number(v.roll)),
          base_unit_weight: formatWeightDiv(Number(v.base_unit_weight)),
          paper_tube_weight: formatWeightDiv(Number(v.paper_tube_weight)),
          weight_error: formatWeightDiv(Number(v.weight_error)),
          settle_weight: formatWeightDiv(Number(v.settle_weight)),
          length: formatLengthDiv(Number(v.length)),
          actually_weight: formatWeightDiv(Number(item.actually_weight)),
          settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)),
          item_fc_change_data: [],
        }
      }) || []
    return {
      ...item,
      arrange_roll: formatTwoDecimalsDiv(Number(item.arrange_roll)),
      sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)),
      arrange_weight: formatWeightDiv(Number(item.arrange_weight)),
      sum_stock_length: formatLengthDiv(Number(item.sum_stock_length)),
      total_weight: formatWeightDiv(Number(item.total_weight)),
      weight_error: formatWeightDiv(Number(item.weight_error)),
      settle_weight: formatWeightDiv(Number(item.settle_weight)),
      unit_price: formatUnitPriceDiv(Number(item.unit_price)),
      arrange_length: formatLengthDiv(Number(item.arrange_length)),
      length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
      other_price: formatTwoDecimalsDiv(Number(item.other_price)),
      total_price: formatTwoDecimalsDiv(Number(item.total_price)),
      actually_weight: formatWeightDiv(Number(item.actually_weight)),
      settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)),
      change_length: 0,
      change_roll: 0,
      change_weight: 0,
      result_roll: formatTwoDecimalsDiv(Number(item.push_roll)),
      result_length: formatLengthDiv(Number(item.push_length)),
      result_weight: formatWeightDiv(Number(item.push_weight)),
      arrange_item_remark: item.remark,
      push_weight: formatWeightDiv(Number(item.push_weight)),
      push_length: formatLengthDiv(Number(item.push_length)),
      push_roll: formatTwoDecimalsDiv(Number(item.push_roll)),
      item_fc_data,

      change_send_roll: formatTwoDecimalsDiv(Number(change_send_roll)),
      change_send_weight: formatWeightDiv(Number(change_send_weight)),
      change_send_length: formatLengthDiv(Number(change_send_length)),
    }
  })
}
onMounted(() => {
  getData()
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="!finishProductionOptions.datalist.length" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
        <DescriptionsFormItem label="配布单号:">
          <template #content>
            {{ state.formInline.arrange_order_no }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货类型:">
          <template #content>
            <SelectComponents
              v-if="[2, 3].includes(state.formInline.after_out_order_type)"
              v-model="state.formInline.after_out_order_type"
              api="GetWarehouseGoodOutChangeTypeEnum"
              label-field="name"
              value-field="id"
              clearable
            />
            <span v-else>
              {{ state.formInline.after_out_order_type_name }}
            </span>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系名称:">
          <template #content>
            {{ state.formInline.sale_system_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="配布日期:">
          <template #content>
            {{ state.formInline.arrange_time }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工厂名称:">
          <template #content>
            {{ state.formInline.process_factory_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货标签:">
          <template #content>
            {{ state.formInline.receive_tag }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原往来单位名称:">
          <template #content>
            {{ state.formInline.before_biz_unit_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="变更往来单位名称:">
          <template #content>
            <el-form-item>
              <!-- 2,3销售相关取客户 -->
              <!-- 4取成品采购 -->
              <!-- 5 取染厂和后整厂 -->
              <SelectComponents
                v-if="[2, 3].includes(state.formInline.after_out_order_type)"
                v-model="state.formInline.after_biz_unit_id"
                api="GetCustomerEnumList"
                label-field="name"
                value-field="id"
                clearable
              />
              <SelectComponents
                v-else-if="[4].includes(state.formInline.after_out_order_type)"
                v-model="state.formInline.after_biz_unit_id"
                :query="{
                  or_unit_type_id: BusinessUnitIdEnum.finishedProduct,
                }"
                api="business_unitlist"
                label-field="name"
                value-field="id"
                clearable
              />
              <SelectComponents
                v-else-if="[5, 8].includes(state.formInline.after_out_order_type)"
                v-model="state.formInline.after_biz_unit_id"
                :query="{
                  or_unit_type_id: `${BusinessUnitIdEnum.dyeFactory},${BusinessUnitIdEnum.laterWholePlant}`,
                }"
                api="business_unitlist"
                label-field="name"
                value-field="id"
                clearable
              />
              <SelectComponents
                v-else-if="[7].includes(state.formInline.after_out_order_type)"
                v-model="state.formInline.after_biz_unit_id"
                :query="{
                  or_unit_type_id: `${BusinessUnitIdEnum.dyeFactory},${BusinessUnitIdEnum.finishedProduct}`,
                }"
                style="width: 200px"
                api="business_unitlist"
                label-field="name"
                value-field="id"
                clearable
              />
              <SelectComponents
                v-else-if="[6].includes(state.formInline.after_out_order_type)"
                v-model="state.formInline.after_biz_unit_id"
                style="width: 200px"
                api="business_unitlist"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址:">
          <template #content>
            {{ state.formInline.receive_addr }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货电话:">
          <template #content>
            {{ state.formInline.receive_phone }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原仓库名称:">
          <template #content>
            {{ state.formInline.warehouse_name || state.formInline.before_arrange_to_warehouse_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="变更仓库名称:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.after_arrange_to_warehouse_id"
                :disabled="state.formInline.after_out_order_type === 3"
                api="GetPhysicalWarehouseDropdownList"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            {{ state.formInline.sale_user_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单员:">
          <template #content>
            {{ state.formInline.sale_follower_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="司机名称:">
          <template #content>
            {{ state.formInline.driver_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流公司:">
          <template #content>
            {{ state.formInline.logistics_company_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="内部备注:" copies="2">
          <template #content>
            {{ state.formInline.internal_remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售备注:" copies="2">
          <template #content>
            {{ state.formInline.sale_remark }}
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table
      v-if="finishProductionOptions.datalist.length"
      ref="tablesRef"
      :config="finishProductionOptions.tableConfig"
      :table-list="finishProductionOptions.datalist"
      :column-list="finishProductionOptions.columnList"
    >
      <!-- 变更匹数 -->
      <template #change_roll="{ row }">
        <vxe-input
          v-model="row.change_roll"
          type="float"
          :min="0 - row.push_roll"
          @change="computedKeys()"
        />
      </template>
      <!-- 变更辅助数量 -->
      <template #change_length="{ row }">
        <vxe-input
          v-model="row.change_length"
          type="float"
          :min="0 - row.push_length"
          @change="computedKeys()"
        />
      </template>
      <!-- 变更数量 -->
      <template #change_weight="{ row }">
        <vxe-input
          v-model="row.change_weight"
          type="float"
          :min="0 - row.push_weight"
          @change="computedKeys()"
        />
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" type="text" />
      </template>
      <!-- 最终匹数 -->
      <template #result_roll="{ row }">
        <span v-if="Number(row.change_roll) && hasXima(row)" class="green">{{ row.result_roll }}({{ Number(row.change_roll) }})</span>
        <span v-else>
          {{ row.result_roll }}
        </span>
      </template>
      <!-- 最终辅助数量 -->
      <template #result_length="{ row }">
        <span v-if="Number(row.change_length) && hasXima(row)" class="green">{{ row.result_length }}({{ Number(row.change_length) }})</span>
        <span v-else>
          {{ row.result_length }}
        </span>
      </template>
      <!-- 最终数量 -->
      <template #result_weight="{ row }">
        <span v-if="Number(row.change_weight) && hasXima(row)" class="green">{{ row.result_weight }}({{ Number(row.change_weight) }})</span>
        <span v-else>
          {{ row.result_weight }}
        </span>
      </template>
      <!-- 细码 -->
      <template #xima="{ row, rowIndex }">
        <!-- 进仓数量 -->
        <el-button v-if="hasXima(row)" type="text" @click="showFineSizeDialog(row, rowIndex)">
          变更
        </el-button>
      </template>
      <!-- 操作 -->
      <template #operate="{ row }">
        <el-button type="text" @click="finishProductionOptions.handleRowDel(row)">
          删除
        </el-button>
      </template>
    </Table>
    <div v-else class="no_data" style="color: #999">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <AccordingLibAdd ref="AccordingLibAddRef" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

::v-deep(.green) {
  color: green;
}

::v-deep(.red) {
  color: red;
}
</style>
