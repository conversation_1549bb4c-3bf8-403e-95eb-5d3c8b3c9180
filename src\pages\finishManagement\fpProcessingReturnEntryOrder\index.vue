<script setup lang="ts" name="FpProcessingReturnEntryOrder">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import FineSizeRepertoryDetail from '../components/FineSizeDeliveryOrderDetail.vue'
import {
  getFpmProcessReturnInOrder,
  getFpmProcessReturnInOrderList,
  updateFpmProcessReturnInOrderStatusPass,
  updateFpmProcessReturnInOrderStatusWait,
} from '@/api/fpProcessingReturnEntryOrder'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { usePageQuery } from '@/use/usePageQuery'
import { MonthTransferOrderEnum } from '@/enum/orderEnum'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const { formatFilterObj, formatDateRange } = usePageQuery()

const router = useRouter()
const mainOptionsTablesRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  biz_unit_id: '',
  warehouse_id: '',
  audit_status: [],
}))
const warehousing_date = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmProcessReturnInOrderStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmProcessReturnInOrderStatusWait()
// 驳回
// const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = updateFpmProcessReturnInOrderStatusReject()

const {
  fetchData: fetchDataList,
  data: mainDataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = getFpmProcessReturnInOrderList()
// 获取列表数据
async function getData() {
  const query = {
    ...filterData,
  }

  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (warehousing_date?.value?.length) {
    query.in_time_begin = formatDate(warehousing_date.value[0])
    query.in_time_end = formatDate(warehousing_date.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainDataList.value?.list)
    showFinishProductionDetail(mainDataList.value.list[0])
}

const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '9%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'biz_unit_name',
      title: '加工单位名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_in_time',
      title: '进仓日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'store_keeper_name',
      title: '仓管员',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_price',
      soltName: 'total_price',
      title: '单据金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  //   导出
  handleExport: async () => {
    // if (mainOptions.multipleSelection.length < 1) return ElMessage.warning('请勾选要导出的数据')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSheetOfProductionPlanList()
    // mainOptions.exportOptions.loadingExcel = true
    // exportExcel()
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //
    // ElMessage({
    //   type: 'success',
    //   message: '成功',
    // })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

watch(
  () => mainDataList.value,
  () => {
    mainOptions.mainList
          = mainDataList.value?.list?.map((item: any) => {
        const item_data
            = item.item_data?.map((v: any) => {
              return {
                ...v,
                out_roll: formatTwoDecimalsDiv(Number(item.out_roll)),
                in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
                sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)),
                sum_stock_weight: formatWeightDiv(Number(item.sum_stock_weight)),
                sum_stock_length: formatLengthDiv(
                  Number(item.sum_stock_length),
                ),
                total_weight: formatWeightDiv(Number(item.total_weight)),
                weight_error: formatWeightDiv(Number(item.weight_error)),
                settle_weight: formatWeightDiv(Number(item.settle_weight)),
                unit_price: formatUnitPriceDiv(Number(item.unit_price)),
                out_length: formatLengthDiv(Number(item.out_length)),
                length_unit_price: formatUnitPriceDiv(
                  Number(item.length_unit_price),
                ),
                other_price: formatTwoDecimalsDiv(Number(item.other_price)),
                total_price: formatTwoDecimalsDiv(Number(item.total_price)),
                out_weight: formatWeightDiv(Number(item.out_weight)),
                in_length: formatLengthDiv(Number(item.in_length)),
              }
            }) || []
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(item.total_roll), // 匹数总计
          total_weight: formatWeightDiv(item.total_weight), // 数量总计
          total_length: formatLengthDiv(item.total_length), // 辅助数量总计
          total_price: formatTwoDecimalsDiv(item.total_price), // 单据金额
          item_data,
        }
      }) || []
  },
  { deep: true },
)
// 表格选中事件
function handAllSelect({ records }: any) {
  mainOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  mainOptions.multipleSelection = records
}
function changeDate() {
  // warehousing_date.value = [row.date_min, row.date_max]
  getData()
}

// 导出勾选的数据
// const exportExcel = () => {
//   mainOptionsTablesRef.value.tableRef.exportData({
//     filename: `成品采购进仓单列表${formatTime(new Date())}`,
//     // isFooter: true,
//     data: mainOptions.mainList,
//     columns: mainOptions.columnList.map((item: any) => {
//       return {
//         ...item,
//         field: item.field,
//       }
//     }),
//   })
// }

onMounted(() => {
  getData()
})
onActivated(getData)

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
// 表格操作列功能
// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 驳回
// const handReject = async (row: any) => {
//   const res = await deleteToast('确认驳回嘛？')
//   if (res) {
//     await rejectFetch({ audit_status: 1, id: row.id.toString() })
//     if (rejectSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(rejectMsg.value)
//     }
//   }
// }

function handDetail(row: any) {
  router.push({
    name: 'FpProcessingReturnEntryOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpProcessingReturnEntryOrderEdit',
    query: {
      id: row.id,
    },
  })
}
function handAdd() {
  router.push({
    name: 'FpProcessingReturnEntryOrderAdd',
  })
}

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '')

        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '')

        if (['weight_error'].includes(column.field))
          return sumNum(data, 'weight_error', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['in_length'].includes(column.field))
          return sumNum(data, 'in_length', '')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      childrenList: [
        {
          sortable: true,
          field: 'parent_order_no',
          title: '加工出仓单单号',
          minWidth: 150,
        },
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dye_factory_dyelot_number',
          title: '染厂色号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'in_roll',
          title: '进仓匹数',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '出仓信息',
      childrenList: [
        {
          sortable: true,
          field: 'out_roll',
          title: '出仓匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'out_weight',
          title: '出仓数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'out_length',
          title: '出仓辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '进仓数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'total_weight',
          title: '进仓数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'weight_error',
          title: '空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_weight',
          title: '结算数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '进仓辅助数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'in_length',
          title: '进仓辅助数量',
          minWidth: '5%',
        },
      ],
    },

    {
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          minWidth: 100,
        },
      ],
    },
  ],
})
// 获取成品信息
function showFinishProductionDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

const { fetchData: DetailFetch, data: finishProData }
  = getFpmProcessReturnInOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
        out_roll: formatTwoDecimalsDiv(Number(item.out_roll)),
        out_weight: formatWeightDiv(Number(item.out_weight)),
        total_weight: formatWeightDiv(Number(item.total_weight)),
        weight_error: formatWeightDiv(Number(item.weight_error)),
        settle_weight: formatWeightDiv(Number(item.settle_weight)),
        paper_tube_weight: formatWeightDiv(Number(item.paper_tube_weight)),
        unit_price: formatUnitPriceDiv(Number(item.unit_price)),
        out_length: formatLengthDiv(Number(item.out_length)),
        in_length: formatLengthDiv(Number(item.in_length)),
        length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
      }
    },
  )
}
const FineSizeRepertoryDetailRef = ref()
function showDialog(row: any) {
  row.billType = MonthTransferOrderEnum.FpProcessingReturnEntryOrder
  FineSizeRepertoryDetailRef.value.showDialog(row)
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input
              v-model="filterData.order_no"
              style="width: 100%"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工单位名称:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.biz_unit_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.dyeFactory,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 100%"
              api="GetPhysicalWarehouseDropdownList"
              :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进仓日期:" width="310">
          <template #content>
            <SelectDate
              v-model="warehousing_date"
              style="width: 100%"
              @change-date="changeDate"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              multiple
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          v-has="'FpProcessingReturnEntryOrder_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        ref="mainOptionsTablesRef"
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showFinishProductionDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpProcessingReturnEntryOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpProcessingReturnEntryOrder_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpProcessingReturnEntryOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpProcessingReturnEntryOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>

    <FildCard
      :tool-bar="false"
      title=""
      class="table-card-bottom"
    >
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #unit_price="{ row }">
          ￥{{ row.unit_price }}
        </template>
        <template #length_unit_price="{ row }">
          ￥{{ row.length_unit_price }}
        </template>
        <template #other_price="{ row }">
          ￥{{ row.other_price }}
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #xima="{ row }">
          <el-link @click="showDialog(row)">
            查看
          </el-link>
        </template>
      </Table>
    </FildCard>
  </div>
  <FineSizeRepertoryDetail ref="FineSizeRepertoryDetailRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
