<script setup lang="ts" name="FinishedProductColorInformation">
import { Delete, Document, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { isEqual } from 'arcdash'
import DyeingColor from './compnents/dyeingColor/index.vue'
import {
  DeleteFinishProductColor,
  GetFinishProductColorList,
  GetKindAndProductList,
  GetProductCompositeDetails,
  GetProductDyeingColorDetails,
  UpdateFinishProductColorStatus,
} from '@/api/finishedProductColorInformation'
import { formatHashTag, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import {
  debounce,
  deleteRemark,
  deleteToastWithRiskWarning,
  disabledConfirmBox,
  getFilterData,
  resetData,
} from '@/common/util'
import Accordion from '@/components/Accordion/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import ProductCard from '@/components/ListCard/index.vue'
import ProductItem from '@/components/ListCard/item.vue'
import SelectCascader from '@/components/SelectCascader/productType.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import CoverImage from '@/components/UploadFile/CoverImage/index.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const state = reactive({
  tableData: [],
  showType: 'list' as 'list' | 'card',
  filterData: {
    type_grey_fabric_id: '',
    finish_product_id: '',
    type_finished_product_kind_id: '',
    product_color_id: '',
    status: '',
    product_color_code: '',
    product_color_name: '',
    field_search: '',
  },
  filterDataSelect: {
    type_grey_fabric_id: '',
    finish_product_code: '',
  },
  selectType: 1, // 1表示顶部条件筛选，2表示左侧筛选
  selectProductIds: [] as any[],
  // selectIds: [] as any[],
  multipleSelection: [] as any[],
  multipleSelectionFilter: [] as any[],
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = GetFinishProductColorList()
const {
  fetchData: ApiCustomerListSelect,
  data: dataSelect,
  total: totalSelect,
  loading: loadingSelect,
  page: pageSelect,
  size: sizeSelect,
  handleSizeChange: handleSizeChangeSelect,
  handleCurrentChange: handleCurrentChangeSelect,
} = GetKindAndProductList()

const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
})

function changePro(data: any) {
  componentRemoteSearch.finish_product_code = data.finish_product_code
  componentRemoteSearch.finish_product_name = data.finish_product_name
}

// 获取数据
const getData = debounce(async () => {
  const status = ((state.filterData.status as unknown as []) || []).join(',')

  await ApiCustomerList(
    getFilterData({
      ...state.filterData,
      status,
      product_id: state.selectProductIds.join(','),
      type_grey_fabric_id: state.filterData.type_grey_fabric_id?.at(-1),
      type_finished_product_kind_id:
        state.filterData.type_finished_product_kind_id,
    }),
  )
  state.selectType = 1
}, 400)

// 获取筛选数据
const getDataSelect = debounce(() => {
  const status = ((state.filterData.status as unknown as []) || []).join(',')
  ApiCustomerListSelect(
    getFilterData({
      ...state.filterDataSelect,
      status,
      type_grey_fabric_id: state.filterDataSelect.type_grey_fabric_id?.at(-1),
    }),
  )
}, 400)

onMounted(() => {
  getData()
  getDataSelect()
})
const tableConfig = ref({
  fieldApiKey: 'FinishedProductColorInformation',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  height: '100%',
  showSort: false,
  show_footer: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => toggleActiveRow(val),
})

const tableConfigSelect = ref({
  loading: loadingSelect,
  showPagition: true,
  page: pageSelect,
  size: sizeSelect,
  total: totalSelect,
  showCheckBox: true,
  showSort: false,
  show_footer: false,
  handleSizeChange: (val: number) => handleSizeChangeSelect(val),
  handleCurrentChange: (val: number) => handleCurrentChangeSelect(val),
  handleSelectionChange: (val: any) => filterHandleSelectionChange(val),
  pageLayout: 'prev, pager, next',
})

watch(
  () => state.filterData,
  () => {
    if (state.selectType === 1) {
      clearSelectEvent()
      getData()
    }
  },
  {
    deep: true,
  },
)

watch(
  () => state.filterDataSelect,
  () => {
    getDataSelect()
    if (state.selectProductIds.length > 0) {
      state.selectProductIds = []
      getData()
    }
    clearSelectEvent()
  },
  {
    deep: true,
  },
)

const showColor = ref(false)
const defaultData = ref<any>()
function openColor(row: any) {
  showColor.value = true
  defaultData.value = row
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnListSelect = ref([
  {
    sortable: true,
    field: 'type_grey_fabric_name',
    title: '布种类型',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    minWidth: 100,
  },
])

const columnList = ref([
  {
    sortable: true,
    title: '图片',
    fixed: 'left',
    soltName: 'image_code',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'type_grey_fabric_name',
    title: '布种类型',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'finish_product_name',
    title: '成品名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'finish_product_full_name',
    title: '成品全称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'type_finished_product_kind_name',
    title: '颜色类别',
    minWidth: '6%',
  },
  {
    sortable: true,
    field: 'product_color_code',
    title: '颜色编号',
    minWidth: '6%',
  },
  {
    sortable: true,
    field: 'product_color_name',
    title: '颜色名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    width: 125,
    sortable: true,
  },
  {
    sortable: true,
    field: 'bulk_max_safe_amount',
    title: '大货安全库存数（条）',
    minWidth: 150,
    soltName: 'bulk_max_safe_amount',
  },
  {
    sortable: true,
    field: 'length_cut_max_safe_amount',
    title: '剪版安全库存数（公斤）',
    minWidth: 160,
    soltName: 'length_cut_max_safe_amount',
  },
  {
    sortable: true,
    field: 'length_to_weight_rate',
    title: '数量转长度比率（米/公斤）',
    minWidth: 180,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'dyeing_color_item_data',
    title: '染厂颜色',
    minWidth: 120,
    soltName: 'dyeing_color_item_data',
  },
  {
    sortable: true,
    field: 'yarn_count',
    title: '纱支',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'density',
    title: '密度',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'finish_product_ingredient',
    title: '成分',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'weaving_organization_name',
    title: '织造组织',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'finish_product_width_and_unit_name',
    title: '门幅',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'finish_product_gram_weight_and_unit_name',
    title: '克重',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 120,
  },
  {
    field: 'creator_name',
    title: '创建人',
    minWidth: 120,
  },
  {
    field: 'create_time',
    title: '创建时间',

    isDate: true,
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    isDate: true,
    minWidth: 140,
  },
  {
    field: 'update_user_name',
    title: '修改人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    soltName: 'status',
    width: '5%',
    showStatus: true,
    fixed: 'right',
  },
])

function handShowSort() {
  tableConfig.value.showSort = true
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function filterHandleSelectionChange({ records, checked, row }: any) {
  if (checked) {
    state.multipleSelectionFilter = [
      ...state.multipleSelectionFilter,
      ...records,
    ]
  }
  else {
    state.multipleSelectionFilter = state.multipleSelectionFilter.filter(
      (item: any) => {
        return item.id !== row.id
      },
    )
  }
  getSelectId([...new Set(state.multipleSelectionFilter)])
}
function getSelectId(records: any) {
  state.selectType = 2
  state.selectProductIds = []
  records?.map((item: any) => {
    state.selectProductIds = [...new Set([...state.selectProductIds, item.id])]
  })
  state.filterData = {
    type_grey_fabric_id: '',
    finish_product_id: '',
    type_finished_product_kind_id: '',
    status: '',
    product_color_code: '',
    product_color_name: '',
  }
  getData()
}

onActivated(() => {
  getData()
})
const router = useRouter()

function handleAdd() {
  router.push({
    name: 'FinishedProductColorInformationAdd',
  })
}

function handEdit(row: any) {
  router.push({
    name: 'finishedProductColorInformationEdit',
    params: {
      id: row.id,
    },
  })
}

function handDetail(row: any) {
  router.push({
    name: 'FinishedProductColorInformationDetail',
    params: {
      id: row.id,
    },
  })
}

// 删除数据
const {
  fetchData: deleteFetch,
  success: deleteSuccess,
  msg: deleteMsg,
} = DeleteFinishProductColor()

async function handDelete(row: any) {
  const res = await deleteToastWithRiskWarning(`${formatHashTag(row.finish_product_code, row.finish_product_name)} ${formatHashTag(row.product_color_code, row.product_color_name)}`)
  if (res) {
    const res = await deleteRemark()
    await deleteFetch({ id: row.id.toString(), delete_remark: res })
    if (deleteSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteMsg.value)
    }
  }
}

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteRemark()
  const ids: any[] = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  await deleteFetch({ id: ids.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

// 编辑状态
async function handStatus(row: any) {
  disabledConfirmBox({ row, api: UpdateFinishProductColorStatus }).then(() => {
    getData()
  })
}

// 批量修改状态
async function handAll(val: 1 | 2) {
  if (state.multipleSelection.length <= 0)
    return ElMessage.error('请先勾选数据')
  disabledConfirmBox({
    row: state.multipleSelection,
    api: UpdateFinishProductColorStatus,
    allStatus: val,
  }).then(() => {
    getData()
    state.multipleSelection = []
  })
}

const tableRefSelect = ref()
function clearSelectEvent() {
  state.selectProductIds = []
  const $table = tableRefSelect.value
  if ($table)
    $table.tableRef.clearCheckboxRow()
}

watch(
  () => dataSelect.value?.list,
  () => {
    if (
      state.multipleSelectionFilter
      && state.multipleSelectionFilter.length > 0
    ) {
      nextTick(() => {
        const $table = tableRefSelect.value
        if ($table) {
          const res = dataSelect.value.list?.filter((item: any) => {
            return state.selectProductIds.includes(item.id)
          })
          $table.tableRef.setCheckboxRow(res, true)
        }
      })
    }
  },
)

function changeShowType() {
  state.showType = state.showType === 'list' ? 'card' : 'list'
}

const pageConfig = ref({
  page,
  size,
  total,
  handleSizeChange,
  handleCurrentChange,
})

const tablesRef = ref()
function exportSelectEvent() {
  tablesRef.value.exportSelectEvent()
}

function cardClick(row: any) {
  router.push({
    name: 'FinishedProductColorInformationDetail',
    params: {
      id: row.id,
    },
  })
}

const activeRow = ref<null | any>(null)
const activeRowName = ref('color')
const { fetchData: fetchDataComposite, data: dataComposite, loading: compositeLoading } = GetProductCompositeDetails()
const { fetchData: fetchDataDyeingColor, data: dataDataDyeingColor, loading: colorLoading } = GetProductDyeingColorDetails()

async function toggleActiveRow(val: any) {
  const row = val?.row
  if (isEqual(activeRow.value, row))
    return

  const isFirst = activeRow.value === null

  activeRow.value = null

  await fetchDataComposite({
    id: row.id,
  })
  await fetchDataDyeingColor({
    id: row.id,
  })

  // 反正页面都卡，不如加点延迟和动画，显得更有动态效果
  setTimeout(() => {
    activeRow.value = row
  }, isFirst ? 0 : 200)
}

const tableConfigComposite = ref({
  loading: compositeLoading,
  showSort: false,
  height: 240,
})

const compositeColumnList = ref([
  {
    field: 'finish_product_code',
    title: '成品编号',
    minWidth: 100,
    // soltName: 'finish_product_code',
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
    // soltName: 'product_color_code',
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'ratio',
    title: '比率',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'is_bottom_identify',
    title: '底布标识',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])

const tableConfigDyeingColor = ref({
  loading: colorLoading,
  showSort: false,
  height: 240,
})

const colorColumnList = ref([
  {
    field: 'dye_factory_color_code',
    title: '染厂颜色编号',
    minWidth: 100,
  },
  {
    field: 'dye_factory_color_name',
    title: '染厂颜色名称',
    minWidth: 100,
  },
  {
    field: 'dyelot_number',
    title: '对色缸号',
    minWidth: 100,
  },
  {
    field: 'product_kind_name',
    title: '颜色类别',
    minWidth: '6%',
  },
  {
    field: 'dye_factory_name',
    title: '染厂名称',
    minWidth: 100,
  },
  {
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'dye_craft',
    title: '染整工艺',
    minWidth: 100,
  },
  {
    field: 'tape_specs',
    title: '胶袋规格',
    minWidth: 100,
  },
  {
    field: 'paper_tube_specs',
    title: '纸筒规格',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
        <DescriptionsFormItem label="布种类型:">
          <template #content>
            <SelectCascader
              v-model="state.filterData.type_grey_fabric_id"
              style="width: 100%"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.finish_product_id"
              field="finish_product_code"
              :label-name="componentRemoteSearch.finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @on-input="(inputValue) => (componentRemoteSearch.finish_product_code = inputValue)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef2" -->
            <!--              v-model="state.filterData.finish_product_id" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.finish_product_code, -->
            <!--              }" -->
            <!--              value-field="id" -->
            <!--              label-field="finish_product_code" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="(inputValue) => handleSearch('proRef2', inputValue)" -->
            <!--              @change-value="changePro('proRef2')" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.finish_product_id"
              field="finish_product_name"
              :label-name="componentRemoteSearch.finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @on-input="(inputValue) => (componentRemoteSearch.finish_product_name = inputValue)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef1" -->
            <!--              v-model="state.filterData.finish_product_id" -->
            <!--              label-field="finish_product_name" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.finish_product_name, -->
            <!--              }" -->
            <!--              value-field="id" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="(inputValue) => handleSearch('proRef1', inputValue)" -->
            <!--              @change-value="changePro('proRef1')" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色编号:">
          <template #content>
            <el-input
              v-model="state.filterData.product_color_code"
              clearable
              placeholder="颜色编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色名称:">
          <template #content>
            <el-input
              v-model="state.filterData.product_color_name"
              clearable
              placeholder="颜色名称"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色类别:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.type_finished_product_kind_id"
              api="GetTypeFinishedProductColorEnumList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              api="StatusListApi"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="全文本筛选:">
          <template #content>
            <el-input
              v-model="state.filterData.field_search"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-1 overflow-hidden">
      <Accordion :open-status="true">
        <div class="flex mb-2">
          <SelectCascader
            v-model="state.filterDataSelect.type_grey_fabric_id"
            style="width: 100%"
          />
          <el-input
            v-model="state.filterDataSelect.finish_product_code"
            size="small"
            placeholder="请填写成品编号"
            class="ml-[5px]"
            clearable
          />
        </div>
        <Table
          ref="tableRefSelect"
          :config="tableConfigSelect"
          :table-list="dataSelect?.list"
          :column-list="columnListSelect"
        />
      </Accordion>

      <FildCard
        title=""
        class="table-card-full"
        tool-bar
      >
        <template #right-top>
          <el-button
            v-has="'GreyFabricInformation_add'"
            style="margin-left: 10px"
            type="primary"
            :icon="Plus"
            @click="handleAdd"
          >
            新建
          </el-button>
          <el-button style="margin-right: 10px" @click="changeShowType">
            {{ state.showType === "card" ? "切换为列表" : "切换为卡片" }}
          </el-button>
          <el-tooltip
            class="box-item"
            effect="dark"
            content="点击出现排序按钮"
            placement="top-start"
          >
            <el-button style="margin-right: 10px" @click="handShowSort">
              排序
            </el-button>
          </el-tooltip>
          <el-dropdown>
            <span class="el-dropdown-link">
              <el-button class="mr-[10px]">批量操作</el-button>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <div v-has="'GreyFabricInformation_del'">
                  <el-dropdown-item @click="handAllDelete">
                    批量删除
                  </el-dropdown-item>
                </div>
                <div v-has="'GreyFabricInformation_status'">
                  <el-dropdown-item @click="handAll(1)">
                    批量启用
                  </el-dropdown-item>
                </div>
                <div v-has="'GreyFabricInformation_status'">
                  <el-dropdown-item @click="handAll(2)">
                    批量禁用
                  </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <PrintPopoverBtn
            :print-type="PrintType.PrintTemplateTypeColorLabel"
            :data-type="PrintDataType.Product"
            :list="state.multipleSelection"
          />
          <!--          <PrintBtn type="productionColor" :tid="1653814285791488" :list="state.multipleSelection" /> -->
          <el-button
            style="margin-left: 10px"
            type="info"
            :icon="Document"
            text
            link
            @click="exportSelectEvent"
          >
            导出文件
          </el-button>
        </template>
        <Table
          v-if="state.showType === 'list'"
          ref="tablesRef"
          :config="tableConfig"
          :table-list="data?.list"
          :column-list="columnList"
        >
          <template #image_code="{ row }">
            <CoverImage :file-url="row.main_texture_url" />
          </template>
          <template #dyeing_color_item_data="{ row }">
            <el-button type="primary" text link size="small" @click="openColor(row)">
              查看
            </el-button>
          </template>
          <template #bulk_max_safe_amount="{ row }">
            {{
              `${formatTwoDecimalsDiv(row?.bulk_min_safe_amount)
              }-${
                formatTwoDecimalsDiv(row?.bulk_max_safe_amount)}`
            }}
          </template>
          <template #length_cut_max_safe_amount="{ row }">
            {{
              `${formatWeightDiv(row?.length_cut_min_safe_amount)
              }-${
                formatWeightDiv(row?.length_cut_max_safe_amount)}`
            }}
          </template>
          <template #operate="{ row }">
            <el-space :size="10">
              <el-link
                v-has="'FinishedProductColorInformationDetail'"
                :underline="false"
                type="primary"
                @click="handDetail(row)"
              >
                查看
              </el-link>
              <el-link
                v-has="'finishedProductColorInformationEdit'"
                :underline="false"
                type="primary"
                @click="handEdit(row)"
              >
                编辑
              </el-link>
              <el-link
                v-has="'FinishedProductColorInformationDel'"
                :underline="false"
                type="primary"
                @click.stop="handDelete(row)"
              >
                删除
              </el-link>
              <el-link
                v-has="'FinishedProductColorInformationStatus'"
                :underline="false"
                type="primary"
                @click="handStatus(row)"
              >
                {{ row?.status === 1 ? "禁用" : "启用" }}
              </el-link>
            </el-space>
          </template>
          <template #finish_product_width="{ row }">
            {{ row?.finish_product_width + row?.finish_product_width_unit_name }}
          </template>
          <template #finish_product_gram_weight="{ row }">
            {{
              row?.finish_product_gram_weight
                + row?.finish_product_gram_weight_unit_name
            }}
          </template>
        </Table>
        <div v-else class="grow overflow-y-auto">
          <ProductCard
            :list="data?.list"
            :page-config="pageConfig"
            @click="cardClick"
          >
            <template #header="{ row }">
              <div>{{ row?.finish_product_code }}</div>
              <div>{{ row?.finish_product_name }}</div>
            </template>
            <template #body="{ row }">
              <ProductItem label="成品全称">
                {{
                  row?.finish_product_full_name
                }}
              </ProductItem>
              <ProductItem label="颜色类别">
                {{
                  row?.type_finished_product_kind_name
                }}
              </ProductItem>
              <ProductItem label="颜色编号">
                {{
                  row?.product_color_code
                }}
              </ProductItem>
              <ProductItem label="颜色名称">
                {{
                  row?.product_color_name
                }}
              </ProductItem>
              <ProductItem label="染厂颜色">
                {{
                  row?.dyeing_color_item_data
                }}
              </ProductItem>
            </template>
          </ProductCard>
        </div>
      </FildCard>
    </div>
    <DyeingColor :show="showColor" :default-data="defaultData" />
  </div>

  <div class="mt-[5px] overflow-x-hidden">
    <transition name="fade-slide">
      <FildCard v-if="activeRow !== null" :tool-bar="false">
        <div class="flex my-[5px] text-[13px]">
          <div class="flex mr-[20px]">
            <div class="mr-[10px] font-bold">
              布种类型:
            </div>
            <div>{{ activeRow?.type_grey_fabric_name }}</div>
          </div>
          <div class="flex mr-[20px]">
            <div class="mr-[10px] font-bold">
              成品编号:
            </div>
            <div>{{ activeRow?.finish_product_code }}</div>
          </div>
          <div class="flex mr-[20px]">
            <div class="mr-[10px] font-bold">
              成品名称:
            </div>
            <div>{{ activeRow?.finish_product_name }}</div>
          </div>
        </div>

        <el-tabs v-model="activeRowName">
          <el-tab-pane label="复合布" name="rabrics">
            <Table
              :config="tableConfigComposite"
              :table-list="dataComposite?.list"
              :column-list="compositeColumnList"
            />
          </el-tab-pane>
          <el-tab-pane label="染厂颜色" name="color">
            <Table
              :config="tableConfigDyeingColor"
              :table-list="dataDataDyeingColor?.list"
              :column-list="colorColumnList"
            />
          </el-tab-pane>
        </el-tabs>
      </FildCard>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}
:deep(.vxe-cell){
  display: flex;
  align-items: center;
}
.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
