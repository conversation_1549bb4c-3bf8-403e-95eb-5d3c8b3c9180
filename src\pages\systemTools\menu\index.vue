<template>
  <FildCard :="false" :toolBar="false">
    <el-button type="primary" :icon="Plus" class="mb-2" @click="addMenu">添加菜单</el-button>
    <el-table :indent="20" :data="state.tableData" style="width: 100%; margin-bottom: 20px" :tree-props="{ children: 'sub_menu' }" row-key="id">
      <el-table-column prop="name" label="菜单名称" />
      <el-table-column prop="avatar_url" label="菜单图标">
        <template #default="{ row }">
          <svg-icon :name="row.avatar_url" size="26px" color="#000"></svg-icon>
        </template>
      </el-table-column>
      <el-table-column prop="resource_name" label="资源名称" />
      <el-table-column prop="sort" label="序号"></el-table-column>
      <el-table-column prop="is_hide" label="是否隐藏">
        <template #default="{ row }">
          <el-tag :type="row.is_hide ? 'warning' : ''">{{ row.is_hide ? '隐藏' : '显示' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="350">
        <template #default="{ row }">
          <el-button-group class="ml-4">
            <el-button type="primary" text :avatar_url="Plus" @click="addChildren(row)">添加子菜单</el-button>
            <el-button type="primary" text :avatar_url="Setting" @click="editChildren(row)">编辑</el-button>
            <el-button type="primary" text :avatar_url="Delete" @click="delChildren(row)">删除</el-button>
            <el-button :type="row.is_hide ? 'primary' : 'warning'" text :avatar_url="Delete" @click="changeStatus(row)">{{ row.is_hide ? '显示' : '隐藏' }}</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
  </FildCard>
  <AddModal :menu-list="state.tableData" v-model="baseData.show" :title="baseData.title" :row="modal" :type="baseData.type" @add-success="onSuccess" @edit-success="onSuccess"></AddModal>
</template>
<script setup lang="ts" name="Menu">
import { DelMenuApi, GetMenuListApi, UpdateMenuStatusApi } from '@/api/menu'
import FildCard from '@/components/FildCard.vue'
import { Delete, Plus, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import AddModal, { Row } from './components/AddModal.vue'

const state = reactive<any>({
  tableData: [],
})

onMounted(() => {
  getList()
})

const { fetchData: fetchDataMenu, data: dataMenu } = GetMenuListApi()
const getList = async () => {
  await fetchDataMenu({ show_children: true })
  state.tableData = dataMenu.value.list
}

const baseData = ref({
  show: false,
  title: '添加菜单',
  type: 'add',
})
const modal = ref({
  parent_id: 0,
  parent_name: '',
  id: 0,
  name: '',
  avatar_url: '',
  resource_id: 0,
  sort: 0,
  is_hide: false,
})

const addMenu = () => {
  baseData.value.show = true
  baseData.value.title = '添加根菜单'
  modal.value.parent_id = 0
  modal.value.parent_name = '根菜单'
  baseData.value.type = 'add'
}

const addChildren = (row: Row) => {
  modal.value = { ...row, parent_id: row.id }
  baseData.value.show = true
  baseData.value.title = '添加子菜单'
  baseData.value.type = 'add'
}

const editChildren = (row: Row) => {
  modal.value = { ...row }
  baseData.value.title = '编辑菜单'
  baseData.value.show = true
  baseData.value.type = 'edit'
}

const { fetchData: fetchDataDel, success: successDel, msg: msgDel } = DelMenuApi()
const delChildren = (row: Row) => {
  ElMessageBox.confirm(`确认删除该菜单吗？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      await fetchDataDel({ id: row.id })
      if (successDel.value) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(msgDel.value)
      }
    })
    .catch(() => {})
}

const onSuccess = () => {
  baseData.value.show = false
  getList()
}

const { fetchData: fetchDataUpdate, success: successUpdate, msg: msgUpdate } = UpdateMenuStatusApi()
const changeStatus = (row: Row) => {
  ElMessageBox.confirm(`确认${row.is_hide ? '显示' : '隐藏'}该数据吗？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      await fetchDataUpdate({ id: row.id.toString(), is_hide: !row.is_hide })
      if (successUpdate.value) {
        ElMessage.success('修改成功')
        getList()
      } else {
        ElMessage.error(msgUpdate.value)
      }
    })
    .catch(() => {})
}
</script>
