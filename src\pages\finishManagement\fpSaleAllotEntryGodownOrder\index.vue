<script setup lang="ts" name="FpSaleAllotEntryGodownOrder">
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import FineSizeSaleAllotEntryDetail from '../components/FineSizeSaleAllotEntryDetail.vue'
import {
  getFpmSaleAllocateInOrder,
  getFpmSaleAllocateInOrderList,
  updateFpmSaleAllocateInOrderStatusPass,
  updateFpmSaleAllocateInOrderStatusWait,
} from '@/api/fpSaleAllotEntryGodownOrder'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { sumNum } from '@/util/tableFooterCount'
import { usePageQuery } from '@/use/usePageQuery'

const { formatFilterObj, formatDateRange } = usePageQuery()

const FineSizeSaleAllotEntryDetailRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  biz_unit_id: '',
  warehouse_id: '',
  audit_status: [],
}))
const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

const warehousing_time = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmSaleAllocateInOrderStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmSaleAllocateInOrderStatusWait()
const {
  fetchData: fetchDataList,
  data: mainList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = getFpmSaleAllocateInOrderList()
// 静态变量
const mainOptions = reactive<any>({
  tableConfig: {
    fieldApiKey: 'FpSaleAllotEntryGodownOrder',
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '10%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '10%',
      sortable: true,
    },
    {
      sortable: true,
      field: 'customer_name',
      title: '客户名称',
      minWidth: 100,
      sortable: true,
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
      sortable: true,
    },
    {
      sortable: true,
      field: 'warehouse_out_name',
      title: '调出单位名称',
      minWidth: 100,
      sortable: true,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
      sortable: true,
    },
    {
      sortable: true,
      field: 'warehouse_in_time',
      title: '进仓日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'store_keeper_name',
      title: '仓管员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_price',
      title: '单据金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  showToolBar: true,
  //   导出
  handleExport: async () => {
    //   if (mainOptions.mainList.length < 1) return ElMessage.warning('当前无数据可导出')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getFpmSaleAllocateInOrderList()
    // mainOptions.exportOptions.loadingExcel.value = true
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //   exportExcel()
    //   ElMessage({
    //     type: 'success',
    //     message: '成功',
    //   })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel.value = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

// const tableRef = ref<any>()
// const exportExcel = () => {
//   tableRef.value.tableRef.exportData({
//     filename: `成品采购退货出仓单列表${formatTime(new Date())}`,
//     // isFooter: true,
//   })
// }
async function getData() {
  const query = {
    ...filterData,
  }
  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (warehousing_time?.value?.length) {
    query.in_time_begin = formatDate(warehousing_time.value[0])
    query.in_time_end = formatDate(warehousing_time.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainList.value?.list)
    showDetail(mainList.value.list[0])
}

watch(
  () => mainList.value,
  () => {
    mainOptions.mainList
      = mainList.value?.list?.map((item: any) => {
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(Number(item.total_roll)),
          total_weight: formatWeightDiv(Number(item.total_weight)),
          total_length: formatLengthDiv(Number(item.total_length)),
          total_price: formatTwoDecimalsDiv(Number(item.total_price)),
        }
      }) || []
  },
  { deep: true },
)

onMounted(() => {
  getData()
})
onActivated(getData)

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

function handDetail(row: any) {
  router.push({
    name: 'FpSaleAllotEntryGodownOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpSaleAllotEntryGodownOrderEdit',
    query: {
      id: row.id,
    },
  })
}

// 成品信息
const finishProductionOptions = reactive({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'FpSaleAllotEntryGodownOrder_B',
    showSlotNums: false,
    height: '100%',
    showSpanHeader: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 出仓数量 结算数量 辅助数量 其他金额 结算金额
        if (['in_roll'].includes(column.field))
          return sumNum(data, 'in_roll', '', 'float')

        if (['quote_roll'].includes(column.field))
          return sumNum(data, 'quote_roll', '', 'float')

        if (['quote_weight'].includes(column.field))
          return sumNum(data, 'quote_weight', '', 'float')

        if (['quote_length'].includes(column.field))
          return sumNum(data, 'quote_length', '', 'float')

        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '', 'float')

        if (['actually_weight'].includes(column.field))
          return sumNum(data, 'actually_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['in_length'].includes(column.field))
          return sumNum(data, 'in_length', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      field: 'B',
      childrenList: [
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'in_roll',
          title: '进仓匹数',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'C',
      title: '调拨信息',
      childrenList: [
        {
          sortable: true,
          field: 'quote_roll',
          title: '调拨匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'quote_weight',
          title: '调拨数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'quote_length',
          title: '调拨辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'D',
      title: '进仓数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'total_weight',
          title: '进仓数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'weight_error',
          title: '码单空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'actually_weight',
          title: '码单数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_weight',
          title: '结算数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'E',
      title: '进仓辅助数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'in_length',
          title: '进仓辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'F',
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'G',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
  showXima: (row: any) => {
    FineSizeSaleAllotEntryDetailRef.value.showDialog(row)
  },
})

// 获取成品信息
function showDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

const { fetchData: DetailFetch, data: finishProData }
  = getFpmSaleAllocateInOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        out_roll: formatTwoDecimalsDiv(Number(item.out_roll)),
        total_weight: formatWeightDiv(Number(item.total_weight)),
        weight_error: formatWeightDiv(Number(item.weight_error)),
        settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)),
        settle_weight: formatWeightDiv(Number(item.settle_weight)),
        unit_price: formatUnitPriceDiv(Number(item.unit_price)),
        out_length: formatLengthDiv(Number(item.out_length)),
        length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
        other_price: formatTwoDecimalsDiv(Number(item.other_price)),
        total_price: formatTwoDecimalsDiv(Number(item.total_price)),
        in_length: formatLengthDiv(Number(item.in_length)),
        in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
        actually_weight: formatWeightDiv(Number(item.actually_weight)),
        quote_roll: formatTwoDecimalsDiv(Number(item.quote_roll)),
        quote_length: formatLengthDiv(Number(item.quote_length)),
        quote_weight: formatWeightDiv(Number(item.quote_weight)),
      }
    },
  )
}

function changeDate() {
  // warehousing_time.value = [row.date_min, row.date_max]
  getData()
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectDialog
              v-model="filterData.customer_id"
              :query="{ name: componentRemoteSearch.customer_name }"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="
                (val) => (componentRemoteSearch.customer_name = val)
              "
            />
            <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="filterData.customer_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              api="GetPhysicalWarehouseDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进仓日期:" width="310">
          <template #content>
            <SelectDate v-model="warehousing_time" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard :tool-bar="mainOptions.showToolBar" class="table-card-full">
      <Table
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpSaleAllotEntryGodownOrder_detail'"
              type="primary" :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpSaleAllotEntryGodownOrder_edit'"
              type="primary" :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpSaleAllotEntryGodownOrder_pass'"
              type="primary" :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpSaleAllotEntryGodownOrder_wait'"
              type="primary" :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      title=""
      class="table-card-bottom"
    >
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #xima="{ row }">
          <el-link @click="finishProductionOptions.showXima(row)">
            查看
          </el-link>
        </template>
      </Table>
    </FildCard>
    <FineSizeSaleAllotEntryDetail ref="FineSizeSaleAllotEntryDetailRef" />
  </div>
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
