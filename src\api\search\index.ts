import { useRequest } from '@/use/useRequest'

// 获取成品资料列表(只获取一些字段，填编号时候用)
export const SearchForSomeProductField = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/searchForSomeProductField',
    method: 'get',
  })
}

// 原料资料列表（填编号的时候用）
export const SearchForSomeProductFieldRawMaterial = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/searchForSomeProductField',
    method: 'get',
  })
}

// 原料颜色列表（填编号时候用）
export const SearchForSomeProductFieldRawMaterialColor = () => {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material_color/searchSomeRawMaterialField',
    method: 'get',
  })
}

// 获取坯布资料列表（填编号的时候用）
export const SearchForGreyFabricField = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/searchForGreyFabricField',
    method: 'get',
  })
}

// 获取坯布颜色枚举列表(填编号的时候用的)
export const SearchForInfoProductGrayFabricColorField = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoProductGrayFabricColor/searchForInfoProductGrayFabricColorField',
    method: 'get',
  })
}

// 获取成品颜色下拉列表(填编号的时候用)
export const SearchSomeFinishProductColorField = () => {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/searchSomeFinishProductColorField',
    method: 'get',
  })
}

// 获取供应商列表(填编号的时候用)
export const SearchForSomeSupplierField = () => {
  return useRequest({
    url: '/admin/v1/business_unit/getSomeBizUnitList',
    method: 'get',
  })
}
