<script setup lang="ts" name="ProductionNotice">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import RelevanceSalesPlanOrder from '../components/RelevanceSalesPlanOrder.vue'
import {
  byIdGreyInfoProductionNotice,
  byIdMaterialProductionNotice,
  byIdOrderChangeLogItemsProductionNotice,
  byIdTechnologicalRequirementProductionNotice,
  cancelApprovedSheetOfProductionNotify,
  checkSheetOfProductionNotify,
  getProductionNotice,
} from '@/api/productionNotice'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'

// import BottonExcel from '@/components/BottonExcel/index.vue'
import { formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { sumNum } from '@/util/tableFooterCount'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { BusinessUnitIdEnum } from '@/common/enum'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import type { TableColumn } from '@/components/Table/type'

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeProduceNotify,
  dataType: PrintDataType.Product,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const router = useRouter()

interface formOptionsType {
  text: string
  key: string
  copies?: number
  unit?: string
}

const form_options: formOptionsType[] = [
  {
    text: '织造规格',
    key: 'weaving_specifications_name_list',
  },
  {
    text: '针寸数',
    key: 'needle_size',
  },
  {
    text: '总针数',
    key: 'total_needle_size',
  },
  {
    text: '制造损耗',
    key: 'weaving_loss',
    unit: '%',
  },
  {
    text: '织机品牌',
    key: 'loom_brand',
  },
  {
    text: '织机机型',
    key: 'loom_model_name',
  },
  {
    text: '安排机台数',
    key: 'machines_num',
  },
  {
    text: '上针',
    key: 'upper_needle',
    copies: 2,
  },
  {
    text: '下针',
    key: 'lower_needle',
    copies: 2,
  },
  {
    text: '纱批',
    key: 'yarn_batch',
    copies: 2,
  },
  {
    text: '纱长',
    key: 'yarn_length',
    copies: 2,
  },
  {
    text: '包装要求',
    key: 'packaging_require',
    copies: 2,
  },
]
const state = reactive<any>({
  tableData: [],
  filterData: {
    order_no: '',
    production_plan_order_no: '',
    weaving_mill_id: '',
    status: [],
  },
  tabs: {
    id: 0,
    greyList: [], // 坯布信息
    materialList: [], // 用料比例
    technologicalRequirementForm: {}, // 工艺要求
    orderChangeLogItemsList: [], // 变更记录
    fn: {}, // tabs下对应函数
  },
  multipleSelection: [],
})
const activeName = ref<string>('one')

const {
  fetchData: ApiCustomerList,
  data: datalist,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
}: any = getProductionNotice()
// 首次加载数据
onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})

// 获取数据
async function getData() {
  await ApiCustomerList(
    getFilterData({
      ...state.filterData,
      status: state.filterData.status.join(','),
    }),
  )
  if (datalist.value?.list)
    getTabsData(datalist.value.list[0].id)
}

watch(
  () => datalist.value,
  () => {
    state.tableData
          = datalist.value?.list?.map((item: any) => {
        return {
          ...item,
          scheduling_roll: formatTwoDecimalsDiv(Number(item.scheduling_roll)),
          total_scheduling_roll: formatTwoDecimalsDiv(Number(item.total_scheduling_roll)),
          scheduling_weight: formatWeightDiv(item.scheduling_weight),
          total_scheduling_weight: formatWeightDiv(item.total_scheduling_weight),
        }
      }) || []
  },
  { deep: true },
)

// 实时过滤数据
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)
// 生产通知单表格列配置
const tableConfig = ref({
  fieldApiKey: 'ProductionNotice',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '12%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
// 生产通知单表格列配置
const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    soltName: 'order_no',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'production_plan_order_no',
    title: '生产计划单号',
    soltName: 'production_plan_order_no',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaving_mill_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaving_mill_order_follower_name',
    title: '织厂跟单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'total_scheduling_roll',
    title: '排产匹数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'total_scheduling_weight',
    title: '排产数量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receipt_grey_fabric_date',
    title: '交坯日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'order_remark',
    title: '备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '通知日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '审核时间',
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'auditor_name',
    title: '审核人',
    width: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'update_user_name',
    title: '修改人',
    width: 100,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    soltName: 'status',
    fixed: 'right',
    showOrder_status: true,
    width: '5%',
  },
])

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}

// 坯布信息表格配置
const columnList_fabic_config = ref({
  showSlotNums: false,
  height: '100%',
})
// 坯布信息表格列配置
const columnList_fabic = ref([
  // {
  //   sortable: true,
  //   field: 'sale_plan_order_no',
  //   soltName: 'sale_plan_order_no',
  //   title: '销售计划单号',
  //   minWidth: 100,
  // },
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'process_price',
    title: '加工单价',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'scheduling_roll',
    title: '排产匹数',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'scheduling_weight',
    title: '排产数量',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'grey_fabric_remark',
    title: '备注',
    minWidth: '100',
  },
  {
    sortable: true,
    field: 'produced_roll',
    title: '已产匹数',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'produced_weight',
    title: '已产数量',
    minWidth: '5%',
  },

  {
    sortable: true,
    field: 'change_roll',
    soltName: 'change_roll',
    title: '变更匹数',
    minWidth: '5%',
  },
  {
    field: 'change_weight',
    soltName: 'change_weight',
    title: '变更数量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'producing_roll',
    title: '未产匹数',
    minWidth: '5%',
  },
  {
    field: 'producing_weight',
    soltName: 'producing_weight',
    title: '未产数量',
    minWidth: 100,
  },
])

// 用料比例表格配置
const columnList_material_config = ref({
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['use_yarn_quantity', 'final_use_yarn_quantity'].includes(column.field))
        return sumNum(data, column.field, data[0] && data[0]?.unit_name, 'float')

      if (['send_yarn_quantity', 'final_send_yarn_quantity'].includes(column.field))
        return sumNum(data, column.field, data[0] && data[0]?.unit_name, 'float')

      return null
    }),
  ]
}

// 用料比例表格列配置
const columnList_material = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'weaving_category',
    title: '织造类别',
    minWidth: 100,
  },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '坯布颜色',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方',
    minWidth: 100,
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
  },
  {
    field: 'final_use_yarn_quantity',
    soltName: 'final_use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
  },
  {
    field: 'final_send_yarn_quantity',
    soltName: 'final_send_yarn_quantity',
    title: '发纱量',
    minWidth: 100,
  },
  {
    field: 'change_send_yarn_quantity',
    soltName: 'change_send_yarn_quantity',
    title: '变更发纱量',
    minWidth: 100,
  },
  {
    field: 'mill_private_yarn',
    soltName: 'mill_private_yarn',
    title: '织厂出料',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])

// 变更记录表格配置
const columnList_orderChangeLogItems_config = ref({
  showSlotNums: false,
  height: '100%',
})
// 变更记录表格列配置
const columnList_orderChangeLogItems = ref([
  {
    field: 'production_change_order_no',
    title: '生产变更单号',
    minWidth: 100,
  },
  {
    field: 'total_roll',
    soltName: 'total_roll',
    title: '匹数',
    minWidth: 100,
  },
  {
    field: 'total_weight',
    soltName: 'total_weight',
    title: '数量',
    minWidth: 100,
  },
  {
    field: 'created_date',
    title: '创建时间',
    minWidth: 100,
    isDate: true,
  },
  {
    field: 'audit_date',
    title: '审核时间',
    minWidth: 100,
    isDate: true,
  },
  {
    field: 'status',
    soltName: 'status',
    title: '单据状态',
    showOrder_status: true,
    width: '5%',
  },
])

// tabs请求数据
const activeOptionFn: any = {
  one: 'getGreyInfoData',
  two: 'getMaterialData',
  three: 'getTechnologicalRequirementData',
  four: 'getOrderChangeLogItemsData',
}
watch(
  [() => activeName.value, () => state.tabs.id],
  () => {
    if (state.tabs.id)
      state.tabs.fn[activeOptionFn[activeName.value]](state.tabs.id)
  },
  { deep: true },
)

function getTabsData(id: string | number) {
  state.tabs.id = id
}

// 获取坯布信息
const {
  fetchData: GreyInfoFetch,
  data: greyData,
  success: GreyInfoSuccess,
  msg: GreyInfoMsg,
} = byIdGreyInfoProductionNotice()
state.tabs.fn.getGreyInfoData = async (id: number | string) => {
  await GreyInfoFetch({ id })
  if (GreyInfoSuccess.value) {
    state.tabs.greyList = [greyData.value].map((item: any) => {
      return {
        ...item,
        grey_fabric_gram_weight: item.grey_fabric_gram_weight,
        finish_product_gram_weight: item.finish_product_gram_weight,
        weight_of_fabric: `${formatWeightDiv(Number(item.weight_of_fabric))}kg`,
        process_price: `${formatUnitPriceDiv(Number(item.process_price))}元/kg`,
        scheduling_weight:
            `${formatWeightDiv(Number(item.scheduling_weight))}`,
        total_scheduling_weight:
            `${formatWeightDiv(Number(item.total_scheduling_weight))}`,
        produced_weight: `${formatWeightDiv(Number(item.produced_weight))}${item.unit_name}`,
        scheduling_roll: formatTwoDecimalsDiv(Number(item.scheduling_roll)),
        total_scheduling_roll: formatTwoDecimalsDiv(Number(item.total_scheduling_roll)),
        produced_roll: formatTwoDecimalsDiv(Number(item.produced_roll)),
        change_roll: formatTwoDecimalsDiv(Number(item.change_roll)),
        change_weight: formatWeightDiv(Number(item.change_weight)),
        producing_roll: formatTwoDecimalsDiv(Number(item.producing_roll)),
        producing_weight: formatWeightDiv(Number(item.producing_weight)),
        sale_plan_order_no:
            item.production_notify_grey_fabric_detail
            && item.production_notify_grey_fabric_detail[0].sale_plan_order_no,
      }
    })
  }
  else {
    ElMessage.error(GreyInfoMsg.value)
  }
}

// 获取用料比例
const {
  fetchData: MaterialFetch,
  data: materialData,
  success: MaterialSuccess,
  msg: MaterialMsg,
} = byIdMaterialProductionNotice()
state.tabs.fn.getMaterialData = async (id: number | string) => {
  await MaterialFetch({ id })
  if (MaterialSuccess.value) {
    state.tabs.materialList = (materialData.value?.list || []).map(
      (item: any) => {
        return {
          ...item,
          yarn_loss: `${formatTwoDecimalsDiv(item.yarn_loss)}%`,
          yarn_ratio: `${formatTwoDecimalsDiv(item.yarn_ratio)}%`,
          use_yarn_quantity: formatWeightDiv(Number(item.use_yarn_quantity)),
          send_yarn_quantity: formatWeightDiv(Number(item.send_yarn_quantity)),
          change_use_yarn_quantity: formatWeightDiv(item.change_use_yarn_quantity),
          change_send_yarn_quantity: formatWeightDiv(item.change_send_yarn_quantity),
          final_send_yarn_quantity: formatWeightDiv(item.final_send_yarn_quantity),
          final_use_yarn_quantity: formatWeightDiv(item.final_use_yarn_quantity),
        }
      },
    )
  }
  else {
    ElMessage.error(MaterialMsg.value)
  }
}

// 获取工艺要求
const {
  fetchData: TechnologicalRequirementFetch,
  data: TechnologicalRequirementData,
  success: TechnologicalRequirementSuccess,
  msg: TechnologicalRequirementMsg,
} = byIdTechnologicalRequirementProductionNotice()
state.tabs.fn.getTechnologicalRequirementData = async (id: number | string) => {
  await TechnologicalRequirementFetch({ id })
  if (TechnologicalRequirementSuccess.value) {
    state.tabs.technologicalRequirementForm = {
      ...TechnologicalRequirementData.value,
      // 织造损耗
      weaving_loss: formatTwoDecimalsDiv(
        TechnologicalRequirementData.value.weaving_loss,
      ),
      weaving_specifications_name_list:
          TechnologicalRequirementData.value.weaving_specifications
            .map((v: any) => v.weaving_specifications_name)
            .join(','),
    }
  }
  else {
    ElMessage.error(TechnologicalRequirementMsg.value)
  }
}

// 获取变更记录
const {
  fetchData: OrderChangeLogItemsFetch,
  data: OrderChangeLogItemsData,
  success: OrderChangeLogItemsSuccess,
  msg: OrderChangeLogItemsMsg,
} = byIdOrderChangeLogItemsProductionNotice()
state.tabs.fn.getOrderChangeLogItemsData = async (id: number | string) => {
  await OrderChangeLogItemsFetch({ id })
  if (OrderChangeLogItemsSuccess.value) {
    state.tabs.orderChangeLogItemsList
        = OrderChangeLogItemsData.value?.list?.map((item: any) => {
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(Number(item.total_roll)),
          change_roll: formatTwoDecimalsDiv(Number(item.change_roll)),
          weight: formatWeightDiv(Number(item.weight)),
          change_weight: formatWeightDiv(Number(item.change_weight)),
        }
      }) || []
  }
  else {
    ElMessage.error(OrderChangeLogItemsMsg.value)
  }
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
// const loadingExcel = ref(false)
// const handleExport = async () => {
//   if (datalist.value?.list?.length < 1) return ElMessage.warning('当前无数据可导出')
//   const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getProductionNotice()
//   loadingExcel.value = true
//   await getFetch({
//     ...getFilterData(state.filterData),
//     download: 1,
//   })
//   if (getSuccess.value) {
//     exportExcel()
//     ElMessage({
//       type: 'success',
//       message: '成功',
//     })
//   } else {
//     ElMessage({
//       type: 'error',
//       message: getMsg.value,
//     })
//   }
//   loadingExcel.value = false
// }
const tableRef = ref<any>()
// const exportExcel = () => {
//   tableRef.value.tableRef.exportData({
//     filename: `生产通知单${formatTime(new Date())}`,
//     // isFooter: true,
//   })
// }
// 新建
function handleAdd() {
  router.push({ name: 'ProductionNoticeAdd' })
}

// 查看
function handDetail(row: any) {
  router.push({
    name: 'ProductionNoticeDetail',
    query: { id: row?.id },
  })
}

// 编辑
function handEdit(row: any) {
  router.push({
    name: 'ProductionNoticeEdit',
    query: { id: row?.id },
  })
}

// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = checkSheetOfProductionNotify()

async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}

// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = cancelApprovedSheetOfProductionNotify()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

// 跳转计划单详情
async function jumpPlanDetail(id: number) {
  router.push({
    name: 'SheetOfProductionPlanDetail',
    query: {
      id,
    },
  })
}

// 查看销售关联
const SaleAllocationRef = ref()

function customSaleShow(row: any) {
  SaleAllocationRef.value.showDialog({
    ...row,
    production_notify_grey_fabric_detail:
        row?.production_notify_grey_fabric_detail?.map((item: any) => {
          return {
            ...item,
            roll: formatTwoDecimalsDiv(item.roll),
            weight: formatWeightDiv(item.weight),
            planed_roll: formatTwoDecimalsDiv(item.planed_roll),
            planed_weight: formatWeightDiv(item.planed_weight),
            scheduling_roll: formatTwoDecimalsDiv(item.scheduling_roll),
            total_scheduling_roll: formatTwoDecimalsDiv(item.total_scheduling_roll),
            scheduling_weight: formatWeightDiv(item.scheduling_weight),
            total_scheduling_weight: formatWeightDiv(item.total_scheduling_weight),
            produced_roll: formatTwoDecimalsDiv(item.produced_roll),
            produced_weight: formatWeightDiv(item.produced_weight),
            use_stock_roll: formatTwoDecimalsDiv(item.use_stock_roll),
            can_scheduling_roll: formatTwoDecimalsDiv(item.can_scheduling_roll),
            this_scheduling_roll: formatTwoDecimalsDiv(item.this_scheduling_roll),
          }
        }) || [],
  })
}

function handChange(row: any) {
  router.push({
    name: 'ProductionChangeAdd',
    query: {
      id: row.id,
    },
  })
}

const customActiveName = ref('first')
const tableConfig1 = ref({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: false,
  showOperate: false,
  height: 300,
  operateWidth: '100',
  showSort: false,
})
const columnList1 = ref([
  {
    field: 'remark',
    title: '经纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
const columnList2 = ref([
  {
    field: 'remark',
    title: '纬纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生产计划单号:">
          <template #content>
            <el-input
              v-model="state.filterData.production_plan_order_no"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.weaving_mill_id"
              api="business_unitlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.knittingFactory,
                name: componentRemoteSearch.name,
              }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              multiple
              api="getAuditStatusEnums"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <!-- <BottonExcel :loading="loadingExcel" @onClickExcel="handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          v-has="'ProductionNotice_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        ref="tableRef"
        :config="tableConfig"
        :table-list="state.tableData"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="getTabsData(row.id)">
            {{
              row?.order_no
            }}
          </el-link>
        </template>
        <template #production_plan_order_no="{ row }">
          <el-link
            type="primary"
            @click="jumpPlanDetail(row.production_plan_order_id)"
          >
            {{ row?.production_plan_order_no }}
          </el-link>
        </template>
        <template #status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row?.status === 1 ? "启用" : "禁用" }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'ProductionNotice_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="!(row.status === 2 || row.status === 4)"
              v-has="'ProductionNotice_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="'ProductionNotice_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <!-- <el-link v-if="row.status == 1" text="danger" type="danger" @click="handReject(row)">驳回</el-link> -->
            <el-link
              v-if="row.status === 2"
              v-has="'ProductionNotice_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="'ProductionNotice_change'"
              type="primary"
              :underline="false"
              @click="handChange(row)"
            >
              变更
            </el-link>
            <PrintPopoverBtn
              :id="row.id"
              :key="row.id"
              style="width: auto"
              print-btn-text="打印"
              print-btn-type="text"
              api="getProductionNotifyOrder"
              :options="options"
            />
          </el-space>
        <!--        <PrintBtn btnType="text" type="productNotice" :tid="1719290605719552" api="getProductionNotifyOrder" :id="row.id" /> -->
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom" :tool-bar="false">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="坯布信息" name="one">
          <Table
            :config="columnList_fabic_config"
            :table-list="state.tabs.greyList"
            :column-list="columnList_fabic"
          >
            <!-- 销售计划单号 -->
            <template #sale_plan_order_no="{ row }">
              <el-link
                v-if="row?.production_notify_grey_fabric_detail?.length"
                @click="customSaleShow(row)"
              >
                查看
              </el-link>
            </template>
            <!-- 排产匹数 -->
            <template #scheduling_roll="{ row }">
              <span>{{ row.total_scheduling_roll }}</span>
              <span v-if="row.change_roll">
                (
                <span v-if="row.change_roll > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_roll }}</span>
                <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon>
                  {{ Math.abs(row?.change_roll) }}
                </span>
                )</span>
            </template><!-- 排产数量 -->
            <template #scheduling_weight="{ row }">
              <span>{{ row.total_scheduling_weight }}</span>
              <span v-if="row.change_weight">
                (
                <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
                <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon>
                  {{ Math.abs(row?.change_weight) }}
                </span>
                )</span>
              {{ row.unit_name }}
            </template>
            <template #producing_weight="{ row }">
              {{ row?.producing_weight }}{{ row.unit_name }}
            </template>
            <template #grey_fabric_width="{ row }">
              {{ row?.grey_fabric_width }}
              {{ row?.grey_fabric_width_unit_name }}
            </template>
            <template #grey_fabric_gram_weight="{ row }">
              {{ row?.grey_fabric_gram_weight }}
              {{ row?.grey_fabric_gram_weight_unit_name }}
            </template>
            <template #finish_product_width="{ row }">
              {{ row?.finish_product_width }}
              {{ row?.finish_product_width_unit_name }}
            </template>
            <template #finish_product_gram_weight="{ row }">
              {{ row?.finish_product_gram_weight }}
              {{ row?.finish_product_gram_weight_unit_name }}
            </template>
          </Table>
        </el-tab-pane>
        <el-tab-pane label="用料比例" name="two">
          <Table
            :config="columnList_material_config"
            :table-list="state.tabs.materialList"
            :column-list="columnList_material"
          >
            <!-- 用纱量 -->
            <template #final_use_yarn_quantity="{ row }">
              <span>{{ row?.final_use_yarn_quantity }}</span>
              <span v-if="row.change_use_yarn_quantity">
                (
                <span v-if="row.change_use_yarn_quantity > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_use_yarn_quantity }}</span>
                <span v-else-if="row.change_use_yarn_quantity < 0" class="negative">
                  <el-icon><Bottom /></el-icon>
                  {{ Math.abs(row?.change_use_yarn_quantity) }}
                </span>
                )
              </span>
              {{ row.unit_name }}
            </template>
            <!-- 发纱量 -->
            <template #final_send_yarn_quantity="{ row }">
              <span>{{ row?.final_send_yarn_quantity }}</span>
              <span v-if="row.change_send_yarn_quantity">
                (
                <span v-if="row.change_send_yarn_quantity > 0" class="positive_number">
                  <el-icon><Top /></el-icon>
                  {{ row?.change_send_yarn_quantity }}
                </span>
                <span v-else-if="row.change_send_yarn_quantity < 0" class="negative">
                  <el-icon><Bottom /></el-icon>
                  {{ Math.abs(row?.change_send_yarn_quantity) }}
                </span>
                )
              </span>
              {{ row.unit_name }}
            </template>
            <!-- 织厂出料 -->
            <template #mill_private_yarn="{ row }">
              <el-checkbox v-model="row.mill_private_yarn" disabled />
            </template>
          </Table>
        </el-tab-pane>
        <el-tab-pane label="工艺要求" name="three">
          <el-tabs v-model="customActiveName" class="demo-tabs">
            <el-tab-pane label="织造工艺" name="first">
              <div
                class="descriptions_row"
                :style="{ '--minLabelWidth': '74px' }"
              >
                <DescriptionsFormItem
                  v-for="(item, index) in form_options"
                  :key="index"
                  :label="`${item.text}:`"
                  :copies="item.copies || 1"
                >
                  <template #content>
                    {{
                      state?.tabs.technologicalRequirementForm[item.key]
                    }}{{
                      String(state?.tabs.technologicalRequirementForm[item.key])
                        && (item.unit || "")
                    }}
                  </template>
                </DescriptionsFormItem>
              </div>
            </el-tab-pane>
            <el-tab-pane label="梭织工艺" name="second">
              <div
                class="descriptions_row"
                :style="{ '--minLabelWidth': '74px' }"
              >
                <DescriptionsFormItem label="经密:">
                  <template #content>
                    {{
                      formatPriceDiv(TechnologicalRequirementData?.warp_density)
                    }}根/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="纬密:">
                  <template #content>
                    {{
                      formatPriceDiv(TechnologicalRequirementData?.weft_density)
                    }}根/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="钢筘内幅:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.reed_inner_width,
                      )
                    }}cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="钢筘边幅:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.reed_outer_width,
                      )
                    }}cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="筘号:">
                  <template #content>
                    {{
                      formatPriceDiv(TechnologicalRequirementData?.reed_no)
                    }}齿/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="穿入数:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.penetration_number,
                      )
                    }}穿
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="上机纬密:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.upper_weft_density,
                      )
                    }}牙
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="下机纬密:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.lower_weft_density,
                      )
                    }}根/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="理论坯布克重:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.gf_theory_gram_width,
                      )
                    }}g/m
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="总经根数:">
                  <template #content>
                    {{
                      formatPriceDiv(
                        TechnologicalRequirementData?.total_warp_pieces,
                      )
                    }}根
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="经纱排列:" copies="2">
                  <template #content>
                    {{ TechnologicalRequirementData?.warp_arrangement }}
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="纬纱排列:" copies="2">
                  <template #content>
                    {{ TechnologicalRequirementData?.weft_arrangement }}
                  </template>
                </DescriptionsFormItem>
              </div>
              <Table
                :config="tableConfig1"
                :table-list="TechnologicalRequirementData.warp_datas"
                :column-list="columnList1"
              />
              <Table
                :config="tableConfig1"
                :table-list="TechnologicalRequirementData.weft_datas"
                :column-list="columnList2"
              />
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="变更记录" name="four">
          <Table
            :config="columnList_orderChangeLogItems_config"
            :table-list="state.tabs.orderChangeLogItemsList"
            :column-list="columnList_orderChangeLogItems"
          >
            <!-- 匹数 -->
            <template #total_roll="{ row }">
              <div>
                {{ row?.total_roll }}
                <span v-if="row?.change_roll !== 0">
                  (
                  <span v-if="row.change_roll > 0" class="positive_number"><el-icon>
                    <Top /></el-icon>{{ row?.change_roll }}</span>
                  <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon>

                    {{ Math.abs(row?.change_roll) }}
                  </span>
                  )
                </span>
              </div>
            </template>
            <!-- 数量 -->
            <template #total_weight="{ row }">
              <div>
                {{ row?.weight }}
                <span v-if="row?.change_weight !== 0">
                  (
                  <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
                  <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon>{{ Math.abs(row?.change_weight) }}</span>
                  )
                </span>
              </div>
            </template>
          </Table>
        </el-tab-pane>
      </el-tabs>
    </FildCard>
  </div>
  <RelevanceSalesPlanOrder
    ref="SaleAllocationRef"
    type="detail"
    @handle-sure="() => {}"
  />
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

::v-deep(.el-button.el-button--danger.is-text) {
  padding: 0;
  font-size: 14px;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;
  font-size: 14px;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 80px;
      text-align: right;
    }
  }
}

.positive_number {
  color: green;
}

.negative {
  color: red;
}
</style>
