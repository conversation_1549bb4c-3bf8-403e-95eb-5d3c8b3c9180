import { useRequest } from '@/use/useRequest'

// 获取列表
export function processinglist() {
  return useRequest({
    url: '/admin/v1/payable/processing/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export function processingdetail() {
  return useRequest({
    url: '/admin/v1/payable/processing/detail',
    method: 'get',
  })
}

// 审核
export function processingpass() {
  return useRequest({
    url: '/admin/v1/payable/processing/pass',
    method: 'put',
  })
}

// 消审
export function processingcancel() {
  return useRequest({
    url: '/admin/v1/payable/processing/cancel',
    method: 'put',
  })
}

// 驳回
export function processingreject() {
  return useRequest({
    url: '/admin/v1/payable/processing/reject',
    method: 'put',
  })
}

// 作废
export function processingvoid() {
  return useRequest({
    url: '/admin/v1/payable/processing/void',
    method: 'put',
  })
}

//  更新
export function processingput() {
  return useRequest({
    url: '/admin/v1/payable/processing',
    method: 'put',
  })
}

// 坯布生产单用纱比例
export function getPRCUseYarnDataUseShouldPayListByItemID() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getPRCUseYarnDataUseShouldPayListByItemID',
    method: 'get',
  })
}

// 坯布退货单单用纱比例
export function getPRTUseYarnDataUseShouldPayListByItemID() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReturnOrder/getPRTUseYarnDataUseShouldPayListByItemID',
    method: 'get',
  })
}

// 自动动更新原料毛重成本
export function AutoUpdateBuoyantWeightPrice() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/autoUpdateBuoyantWeightPrice',
    method: 'put',
  })
}

// 自动动更新原料毛重成本
export function UpdateBuoyantWeightPrice() {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateBuoyantWeightPrice',
    method: 'put',
  })
}
