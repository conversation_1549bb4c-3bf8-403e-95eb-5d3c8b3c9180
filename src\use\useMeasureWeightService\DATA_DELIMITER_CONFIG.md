# useMeasureWeightService 数据头尾配置说明

## 概述

`useMeasureWeightService` 现在支持自定义数据头和数据尾配置，用于分割串口传输的数据包。这个功能类似于传统的 `\r\n` 分割符，但提供了更灵活的配置选项。

## 配置接口

```typescript
interface DataDelimiterConfig {
  /** 数据头，用于标识数据包开始 */
  dataHeader?: number[]
  /** 数据尾，用于标识数据包结束 */
  dataFooter?: number[]
  /** 是否启用数据头尾分割模式，默认使用 \r\n */
  useCustomDelimiter?: boolean
}
```

## 基本使用

### 1. 默认模式（使用 \r\n）

```typescript
const weightService = useMeasureWeightService()
// 默认使用 \r\n (CR LF) 分割数据包
```

### 2. 初始化时配置

```typescript
const weightService = useMeasureWeightService({
  dataHeader: [0x02], // STX (Start of Text)
  dataFooter: [0x03], // ETX (End of Text)
  useCustomDelimiter: true,
})
```

### 3. 运行时动态配置

```typescript
const weightService = useMeasureWeightService()

// 设置数据头（支持字符串或字节数组）
weightService.setDataHeader('START')
weightService.setDataHeader([0xAA, 0xBB])

// 设置数据尾
weightService.setDataFooter('END')
weightService.setDataFooter([0xCC, 0xDD])

// 启用自定义分割模式
weightService.toggleCustomDelimiter(true)
```

## API 方法

### 配置管理方法

- `updateDelimiterConfig(config: Partial<DataDelimiterConfig>)` - 更新完整配置
- `setDataHeader(header: number[] | string)` - 设置数据头
- `setDataFooter(footer: number[] | string)` - 设置数据尾
- `toggleCustomDelimiter(enabled: boolean)` - 启用/禁用自定义分割模式
- `getDelimiterConfig()` - 获取当前配置

## 使用场景

### 场景1: 使用控制字符分割

```typescript
// 使用 STX/ETX 控制字符
weightService.setDataHeader([0x02]) // STX
weightService.setDataFooter([0x03]) // ETX
weightService.toggleCustomDelimiter(true)
```

### 场景2: 使用自定义字符串

```typescript
// 使用字符串作为分割符
weightService.setDataHeader('BEGIN')
weightService.setDataFooter('FINISH')
weightService.toggleCustomDelimiter(true)
```

### 场景3: 只使用数据尾

```typescript
// 不使用数据头，只用数据尾分割
weightService.setDataHeader([]) // 空数据头
weightService.setDataFooter([0x0A]) // 只使用 LF
weightService.toggleCustomDelimiter(true)
```

### 场景4: 十六进制分割符

```typescript
// 使用十六进制字节序列
weightService.setDataHeader([0xAA, 0xBB, 0xCC])
weightService.setDataFooter([0xDD, 0xEE, 0xFF])
weightService.toggleCustomDelimiter(true)
```

## 数据处理流程

1. **数据接收**: 串口数据被添加到内部缓冲区
2. **分割判断**: 根据配置的分割模式处理数据
   - 默认模式: 查找 `\r\n` 序列
   - 自定义模式: 查找配置的数据头尾序列
3. **数据提取**: 提取完整的数据包
4. **数据解析**: 解析重量数据和状态信息

## 常见配置示例

```typescript
// 常见分割符配置
const DELIMITERS = {
  // 默认 CR LF
  CRLF: { dataFooter: [13, 10], useCustomDelimiter: false },

  // 只使用 LF
  LF: { dataFooter: [10], useCustomDelimiter: true },

  // STX/ETX 控制字符
  STX_ETX: {
    dataHeader: [0x02],
    dataFooter: [0x03],
    useCustomDelimiter: true
  },

  // 自定义字符串
  CUSTOM: {
    dataHeader: [83, 84, 65, 82, 84], // "START"
    dataFooter: [69, 78, 68], // "END"
    useCustomDelimiter: true
  },
}
```

## 注意事项

1. **字节序列**: 数据头尾使用字节数组 `number[]` 表示
2. **字符串转换**: 字符串会自动转换为对应的 ASCII 字节数组
3. **缓冲区管理**: 系统会自动管理数据缓冲区，移除已处理的数据
4. **兼容性**: 默认模式保持与原有 `\r\n` 分割的完全兼容
5. **性能**: 自定义分割符会增加少量计算开销，但对性能影响很小

## 调试

使用 `getDelimiterConfig()` 方法可以查看当前的配置状态，日志消息会显示配置更新的详细信息。

## 与现有协议的兼容性

根据现有的电子秤通信协议（见 README.md），不同地址模式有不同的数据格式：

- **Adr = 00**: 使用 CR LF 结尾，可以使用默认配置
- **Adr = 97-98**: 没有 CR LF，可以配置自定义数据头尾
- **Adr = 99**: 使用 CR LF 结尾，可以使用默认配置

示例配置：

```typescript
// 针对 Adr = 97 格式：= 开头，% 结尾
weightService.setDataHeader([0x3D]) // "="
weightService.setDataFooter([0x25]) // "%"
weightService.toggleCustomDelimiter(true)

// 针对 Adr = 98 格式：= 开头，无特定结尾
weightService.setDataHeader([0x3D]) // "="
weightService.setDataFooter([]) // 无数据尾
weightService.toggleCustomDelimiter(true)
```
