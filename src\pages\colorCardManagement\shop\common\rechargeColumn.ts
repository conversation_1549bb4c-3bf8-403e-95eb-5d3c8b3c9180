import { ElImage } from 'element-plus'
import { h } from 'vue'
import { formatDate, formatTime } from '@/common/format'

export function createColumnList() {
  return [
    {
      sortable: true,
      field: 'deadline',
      title: '截至有效期',
      slots: {
        default: ({ row }: any) => {
          return formatDate(row.deadline)
        },
      },
    },
    {
      sortable: true,
      field: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'voucher',
      title: '凭证',
      slots: {
        default: ({ row }: any) => {
          const imgList = row?.voucher ? row.voucher.split(',') : []
          const imgContent = imgList.map((e: any) => {
            return h(ElImage, {
              src: e,
              previewTeleported: true,
              style: {
                width: '30px',
                height: '30px',
                margin: '2px 5px',
              },
              fit: 'scale-down',
              previewSrcList: imgList,
            })
          })

          return imgContent
        },

      },
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '操作人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '操作时间',
      minWidth: 100,
      slots: {
        default: ({ row }: any) => {
          return formatTime(row.update_time)
        },
      },
    },
  ]
}
