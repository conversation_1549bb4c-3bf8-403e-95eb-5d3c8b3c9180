<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import FildCard from '@/components/FildCard.vue'
import {
  editQuickScheduleProduction,
  getMachineDropdownList,
  getQuickScheduleProductionDetail,
  getWeaverDropdownList,
} from '@/api/quickScheduleProduction'

const route = useRoute()
const router = useRouter()

// 优先级选项
const priorityOptions = [
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 },
  { label: '紧急', value: 4 },
]

// 表单数据
const formData = reactive({
  id: 0,
  machine_id: '',
  weaver_id: '',
  plan_start_time: '',
  plan_end_time: '',
  plan_quantity: 0,
  priority: 2,
  remark: '',
})

// 表单验证规则
const formRules = {
  plan_quantity: [
    { required: true, message: '请输入计划产量', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '计划产量必须大于0', trigger: 'blur' },
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' },
  ],
}

const formRef = ref()
const loading = ref(false)
const detailLoading = ref(false)

// 详情数据
const detailData = ref<any>({})

// 获取详情API
const { fetchData: detailFetch } = getQuickScheduleProductionDetail()

// 编辑API
const { fetchData: editFetch, success: editSuccess, msg: editMsg } = editQuickScheduleProduction()

// 获取详情
async function getDetail() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少ID参数')
    router.back()
    return
  }

  detailLoading.value = true
  try {
    const res = await detailFetch({ id: Number(id) })
    if (res) {
      detailData.value = res
      // 填充表单数据
      formData.id = res.id
      formData.machine_id = res.machine?.id || ''
      formData.weaver_id = res.weaver?.id || ''
      formData.plan_start_time = res.plan_start_time || ''
      formData.plan_end_time = res.plan_end_time || ''
      formData.plan_quantity = res.plan_quantity || 0
      formData.priority = res.priority || 2
      formData.remark = res.remark || ''
    }
  }
  catch (error) {
    ElMessage.error('获取详情失败')
  }
  finally {
    detailLoading.value = false
  }
}

// 保存
async function handleSave() {
  try {
    await formRef.value?.validate()
    loading.value = true

    await editFetch(formData)
    if (editSuccess.value) {
      ElMessage.success('编辑成功')
      router.back()
    }
    else {
      ElMessage.error(editMsg.value || '编辑失败')
    }
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 取消
function handleCancel() {
  router.back()
}

onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="quick-schedule-production-edit">
    <FildCard title="编辑快速排产单" :loading="detailLoading">
      <!-- 生产通知单信息展示 -->
      <div v-if="detailData.production_notice" class="notice-info-card">
        <el-card>
          <template #header>
            <span>生产通知单信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <DescriptionsFormItem label="排产单号">
                {{ detailData.schedule_no }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="6">
              <DescriptionsFormItem label="通知单号">
                {{ detailData.production_notice.order_no }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="6">
              <DescriptionsFormItem label="坯布名称">
                {{ detailData.production_notice.grey_fabric_name }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="6">
              <DescriptionsFormItem label="客户名称">
                {{ detailData.production_notice.customer_name }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="form-container"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="机台">
              <SelectComponents
                v-model="formData.machine_id"
                :api="getMachineDropdownList"
                placeholder="请选择机台"
                clearable
                label-key="name"
                value-key="id"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="织工">
              <SelectComponents
                v-model="formData.weaver_id"
                :api="getWeaverDropdownList"
                placeholder="请选择织工"
                clearable
                label-key="name"
                value-key="id"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划开始时间">
              <el-date-picker
                v-model="formData.plan_start_time"
                type="datetime"
                placeholder="请选择计划开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划结束时间">
              <el-date-picker
                v-model="formData.plan_end_time"
                type="datetime"
                placeholder="请选择计划结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划产量" prop="plan_quantity">
              <el-input-number
                v-model="formData.plan_quantity"
                :min="0.01"
                :precision="2"
                placeholder="请输入计划产量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select
                v-model="formData.priority"
                placeholder="请选择优先级"
                style="width: 100%"
              >
                <el-option
                  v-for="item in priorityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" style="text-align: center">
            <el-button @click="handleCancel">
              取消
            </el-button>
            <el-button
              type="primary"
              :loading="loading"
              @click="handleSave"
            >
              保存
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.quick-schedule-production-edit {
  .form-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .notice-info-card {
    margin-bottom: 20px;
  }
}
</style>
