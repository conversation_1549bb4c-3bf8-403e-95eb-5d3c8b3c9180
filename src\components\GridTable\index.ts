import type { VxeGridDefines, VxeGridPropTypes, VxeGridProps, VxeTableEvents, VxeTablePropTypes } from 'vxe-table'
import type { ElPaginationContext } from 'element-plus'
import GridTable from '@/components/GridTable/index.vue'

export interface GridConfig extends VxeGridProps {
  showSeq: boolean

  fieldApiKey: string

  showCheckBox: boolean

  showRadio: boolean
  handAllSelect: VxeTableEvents.CheckboxAll

  handleSelectionChange: VxeTableEvents.CheckboxChange

  radioChangeEvent: VxeTableEvents.RadioChange
  showCheckListAll: boolean // 是否显示列表数据全选（不同于分页的全选）
}

export interface Columns extends VxeGridPropTypes.Columns {
  filterStatus: boolean
}

export type GridTableColumns = Partial<Columns>
export type GridTableConfig = Partial<GridConfig>

export interface GridPropsType {
  // 展示log信息
  showLog?: boolean
  // 表格单元格单击
  tableHeaderClick?: (
    params: VxeGridDefines.HeaderCellClickEventParams<any>,
  ) => void
  // 表头单元格双击
  tableHeaderDbClick?: (
    params: VxeGridDefines.HeaderCellDblclickEventParams<any>,
  ) => void
  // 表头单元格右键
  tableHeaderCellMenu?: (
    params: VxeGridDefines.HeaderCellMenuEventParams<any>,
  ) => void
  // 单元格单击
  cellClick?: (param: VxeGridDefines.CellClickEventParams<any>) => void
  // 单元格双击
  cellDblclick?: (params: VxeGridDefines.CellDblclickEventParams<any>) => void
  // 单元格右键行
  cellMenu?: (params: VxeGridDefines.CellMenuEventParams<any>) => void
  // 表尾单元格单击
  footerCellClick?: (
    params: VxeGridDefines.FooterCellClickEventParams<any>,
  ) => void
  // 表尾单元格双击
  footerCellDblclick?: (
    params: VxeGridDefines.FooterCellDblclickEventParams<any>,
  ) => void
  // 表尾单元格右键
  footerCellMenu?: (
    params: VxeGridDefines.FooterCellMenuEventParams<any>,
  ) => void
  // 复选框切换
  checkboxChange?: (
    params: VxeGridDefines.CheckboxChangeEventParams<any>,
  ) => void
  // 复选框全选切换
  checkboxAll?: (params: VxeGridDefines.CheckboxAllEventParams<any>) => void
  // 滚动事件
  scroll?: (params: VxeGridDefines.ScrollEventParams<any>) => void
  // 表格全屏
  zoom?: (params: VxeGridDefines.ZoomEventParams<any>) => void
  // 表格自定义列表
  custom?: (params: VxeGridDefines.CustomEventParams<any>) => void
  // 表尾合计方法
  footerMethod?: VxeGridProps<any>['footerMethod']
  keyboardConfig?: VxeTablePropTypes.KeyboardConfig
  // 表格高度
  height?: number | 'auto' | string
  // 列
  columns: GridTableColumns
  // 数据
  data: any[]
  showPagition: boolean
  elPaginationConfig?: {
    defaultPageSize: number | string
    page: number
    pageSizes: number[]
    size: number
    pageLayout: string
    total: number
    handleSizeChange: ElPaginationContext['handleSizeChange']
    handleCurrentChange: (current: number) => void
  }
  config: GridTableConfig
}

export default GridTable
