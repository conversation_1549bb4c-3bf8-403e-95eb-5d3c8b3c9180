<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useToggle } from '@vueuse/core'
import { ElNotification } from 'element-plus'
import Header from './modules/gloableHeader/index.vue'
import NewTabs from './modules/gloableTab/index.vue'
import Menu from './modules/gloableSide/index.vue'
import { useKeepAliveStore } from '@/stores/keepAlive'
import { Updater } from '@/util/updater'

const keepAliveStore: any = useKeepAliveStore()

const isShow = ref<boolean>(false)

function handEnter() {
  const myDiv = document.getElementById('myDiv')

  myDiv?.addEventListener(
    'click',
    () => {
      isShow.value = false
    },
    false,
  )
}

function handLeave() {
  isShow.value = true
}
const [reloadFlag, toggle] = useToggle(true)
async function reload(duration = 0) {
  toggle(false)

  await new Promise((resolve) => {
    setTimeout(resolve, duration)
  })

  toggle(true)
}

async function reCachePage(name: string) {
  const isCached = keepAliveStore.list.includes(name)

  if (isCached)
    keepAliveStore.removeCacheRoute(name)

  await reload()

  if (isCached)
    keepAliveStore.addCacheRoute(name)
}

keepAliveStore.setReCachePage(reCachePage)

const dialogTableVisible = ref(false)

function closeDialog() {
  dialogTableVisible.value = false
}

onMounted(() => {
  // 判断账套是否过期
  const deadline = JSON.parse(localStorage.getItem('user') || '{}')?.user
    ?.deadline
  if (checkDeadline(deadline))
    dialogTableVisible.value = true
})
/**
 * 校验账套是否过期
 * @param time 账套过期时间
 * @return boolean true: 过期 false: 未过期
 */
function checkDeadline(time: string): boolean {
  const timeInMs = Date.parse(time)
  const nowInMs = Date.now()
  // 过期时间在过去，那么现在就是过期状态
  return timeInMs < nowInMs
}

// 实例化更新器
const up = new Updater({
  timer: 1 * 60 * 1000,
})
const showNotification = ref(false)
// 更新通知
up.on('update', () => {
  if (!showNotification.value) {
    ElNotification({
      title: '提示',
      dangerouslyUseHTMLString: true,
      showClose: false,
      duration: 0,
      position: 'top-left',
      customClass: 'w-[300px] p-1',
      message: `
      <div class="flex flex-col w-full">
        <strong>当前页面有更新，请在操作完成后点击确认以更新页面</strong>
        <div class="flex justify-end mt-2">
          <button class="bg-[#12acf0] text-white p-1 px-4 rounded-md" onclick="location.reload()">确认</button>
        </div>
      </div>
    `,
    })
    showNotification.value = true
  }
})
</script>

<template>
  <div class="common-layout">
    <el-container>
      <!-- 侧边栏菜单 -->
      <!-- <el-aside width="auto"> -->
      <Menu :show-children-popup="isShow" />
      <!-- </el-aside> -->
      <el-container id="myDiv" @mouseenter="handEnter" @mouseleave="handLeave">
        <el-header height="auto">
          <Header />
          <NewTabs />
        </el-header>
        <el-scrollbar height="100%">
          <el-main class="!p-[8px] main-wrapper">
            <router-view v-slot="{ Component, route }">
              <KeepAlive :include="keepAliveStore.list" :max="12">
                <component
                  :is="Component"
                  v-if="reloadFlag"
                  :key="route.fullPath"
                />
              </KeepAlive>
            </router-view>
          </el-main>
        </el-scrollbar>
      </el-container>
    </el-container>

    <!--  账套过期提示 -->
    <el-dialog
      v-model="dialogTableVisible"
      width="400"
      class="border-[#12acf0] border-2 bg-[#eef9fe]"
      @close="closeDialog"
    >
      <section
        class="w-full flex flex-col items-center justify-center gap-4 px-9"
      >
        <!-- 标题 -->
        <div class="text-2xl font-bold text-black">
          到期提醒
        </div>
        <div>
          尊敬的用户，你的科顿易布云服务已到期，如需继续使用联系企业客服经理或来电咨询。
        </div>
        <div class="flex w-full">
          客服电话：13630170313
        </div>
        <div class="flex flex-col items-center gap-2">
          <img src="@/assets/yuzongQRCode.png" alt="客服二维码" class="w-44">
          <p>添加好友咨询</p>
        </div>
        <div class="w-full mt-12 flex justify-end">
          科顿易布团队
        </div>
      </section>
    </el-dialog>
  </div>
</template>

<style scoped>
.common-layout {
  width: 100vw;
  height: 100vh;
}

.el-container {
  height: 100%;
}

.el-container.is-vertical {
  background: #f1f2f5 !important;
}

.el-header {
  --el-header-padding: none !important;
}

:deep(.el-scrollbar__view) {
  height: 100%;
}
.el-main {
  height: 100%;
}
</style>
