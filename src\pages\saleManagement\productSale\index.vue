<script setup lang="ts">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
// import router from '@/router'
import {
  getSaleProductOrderList,
  getSaleProductOrderListExport,
  updateSaleProductOrderAuditStatusPass,
  updateSaleProductOrderAuditStatusWait,
} from '@/api/productSale'
import { EmployeeType } from '@/common/enum'
import { formatDate } from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import type { TableColumn } from '@/components/Table/type'
import { usePageQuery } from '@/use/usePageQuery'

const { formatFilterObj, formatDateRange } = usePageQuery()

const state = reactive({
  tableData: [],
  filterData: formatFilterObj({
    order_no: '',
    sale_system_id: '',
    sale_order_no: '',
    src_order_no: '',
    order_date: formatDateRange(),
    auditor_id: '',
    updater_id: '',
    create_time: '',
    audit_time: '',
    edit_time: '',
    audit_status: '',
    voucher_number: '',
    sale_user_id: '',
    customer_id: '',
    customer_code: '',
  }),
  multipleSelection: [],
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getSaleProductOrderList()
// 获取数据
const getData = debounce(() => {
  const query: any = {
    start_order_time:
      state.filterData.order_date
      && state.filterData.order_date !== ''
      && state.filterData.order_date.length
        ? formatDate(state.filterData.order_date[0])
        : '',
    end_order_time:
      state.filterData.order_date
      && state.filterData.order_date !== ''
      && state.filterData.order_date.length
        ? formatDate(state.filterData.order_date[1])
        : '',
    start_create_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    end_create_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    start_update_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[0])
        : '',
    end_update_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[1])
        : '',
    start_audit_date:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    end_audit_date:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
  }
  delete query.audit_time
  delete query.create_time
  delete query.edit_time
  delete query.order_date
  query.audit_status = query.audit_status?.length ? query.audit_status.join(',') : ''
  ApiCustomerList(getFilterData(query))
}, 400)

onMounted(() => {
  getData()
})

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '8%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  fieldApiKey: 'ProductSale',
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

// function handReset() {
//   state.filterData = resetData(state.filterData)
// }
// 选择客户
function selectCustomerValueChange(val: any) {
  state.filterData.customer_name = val.name
  state.filterData.customer_code = val.code
  state.filterData.customer_id = val.id
}

const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'number',
    title: '单据编号',
    width: '8%',
    soltName: 'link',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_mode_name',
    title: '订单类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_time',
    title: '订单日期',
    minWidth: 139,
    is_date: true,
  },
  {
    sortable: true,
    field: 'send_product_type_name',
    title: '出货类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'warehouse_name',
    title: '调入仓库',
    minWidth: 100,
  },
  //   {
  //     field: '',
  //     title: '客户编号',
  //     minWidth: 100,
  //   },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'sale_follower_name',
    title: '销售跟单',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'process_factory_name',
    title: '加工厂名称',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'print_tag',
    title: '出货标签',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'contacts',
    title: '联系人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'contact_phone',
    title: '联系电话',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'logistics_company_name',
    title: '物流公司',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'logistics_area',
    title: '物流区域',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'receipt_address',
    title: '收货地址',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'info_sale_taxable_item_name',
    title: '含税项目',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'sale_tax_rate',
    title: '税率',
    minWidth: 120,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'postage_items_name',
    title: '邮费项目',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'internal_remark',
    title: '内部备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'send_product_remark',
    title: '出货备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 139,
    isDate: true,
  },
  {
    field: 'audit_date',
    title: '审核时间',
    sortable: true,
    minWidth: 139,
    isDate: true,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 139,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },

  {
    sortable: true,
    field: 'orer_status',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '成品销售单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = getSaleProductOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'ProductSaleAdd',
  })
}

// const componentRemoteSearch = reactive({
//   name: '',
// })

function handEdit(row: any) {
  router.push({
    name: 'ProductSaleEdit',
    query: { id: row.id },
  })
}

function handDetail(row: any) {
  router.push({
    name: 'ProductSaleDetail',
    query: { id: row.id },
  })
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateSaleProductOrderAuditStatusPass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateSaleProductOrderAuditStatusWait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

onActivated(() => {
  getData()
})
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable placeholder="请输入" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="GetSaleSystemDropdownListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户编号:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" :default-value="{ id: state.filterData.customer_id, code: state.filterData.customer_code, name: state.filterData.customer_name }" field="code" @change-value="selectCustomerValueChange" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" :default-value="{ id: state.filterData.customer_id, code: state.filterData.customer_code, name: state.filterData.customer_name }" field="name" @change-value="selectCustomerValueChange" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_user_id"
              :query="{ duty: EmployeeType.salesman }"
              api="Adminemployeelist"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input
              v-model="state.filterData.voucher_number"
              clearable
              placeholder="请输入"
            />
          </template>
        </DescriptionsFormItem>
        <!-- <DescriptionsFormItem label="最后修改人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.updater_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="订单日期:" copies="2">
          <template #content>
            <SelectDate v-model="state.filterData.order_date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:" :copies="2">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <!-- <DescriptionsFormItem label="审核时间:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.audit_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="最后修改时间:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.edit_time" />
          </template>
        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="审核人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.auditor_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              multiple
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="mt-[5px] flex flex-col overflow-hidden h-full">
      <template #right-top>
        <el-button
          v-has="'ProductSaleAdd'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="'ProductSaleExport'"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'ProductSaleDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'ProductSaleEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'ProductSalePass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'ProductSaleWait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
