<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 25</title>
    <defs>
        <linearGradient x1="32.5140515%" y1="0%" x2="66.8419282%" y2="85.9837869%" id="linearGradient-1">
            <stop stop-color="#558DE7" offset="0%"></stop>
            <stop stop-color="#0968F4" offset="100%"></stop>
        </linearGradient>
        <path d="M39,34 L39,35.3333333 C39,36.8 37.56,38 35.8,38 L12.2,38 C10.44,38 9,36.8 9,35.3333333 L9,34 L39,34 Z M24,6 C28.4166667,6 32,9.58333333 32,14 C32.0011612,16.8283462 30.5080284,19.4468739 28.0733333,20.88625 L29.3333333,24.6666667 L35.8,24.6666667 C37.56,24.6666667 39,25.8666667 39,27.3333333 L39,32 L9,32 L9,27.3333333 C9,25.9190476 10.3389796,24.7527211 12.0127041,24.671216 L12.2,24.6666667 L18.6666667,24.6666667 L19.9266667,20.88625 C17.4919716,19.4468739 15.9988388,16.8283462 16,14 C16,9.58333333 19.5833333,6 24,6 Z" id="path-2"></path>
        <filter x="-23.3%" y="-15.6%" width="146.7%" height="143.8%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.211764706   0 0 0 0 0.494117647   0 0 0 0 0.925490196  0 0 0 0.222321624 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="9.04016821%" y1="31.4514505%" x2="76.4379839%" y2="62.8520674%" id="linearGradient-4">
            <stop stop-color="#F5FBFE" stop-opacity="0.755896935" offset="0%"></stop>
            <stop stop-color="#1C72F1" stop-opacity="0.342189549" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-256.000000, -503.000000)" id="编组-25">
            <g transform="translate(256.000000, 503.000000)" id="编组-16">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="48" height="48"></rect>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-4)" stroke-width="0.5" x="26.75" y="29.75" width="17.5" height="11.5" rx="2"></rect>
                <polygon id="矩形" fill="#FFFFFF" points="39 37 42 37 41 39 38 39"></polygon>
            </g>
        </g>
    </g>
</svg>