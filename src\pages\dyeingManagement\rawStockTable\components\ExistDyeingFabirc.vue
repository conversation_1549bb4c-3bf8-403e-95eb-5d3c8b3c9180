<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import EditFactoryRemark from './NoDyeingFabirc/EditFactoryRemark.vue'
import RemarkEditLog from './NoDyeingFabirc/RemarkEditLog.vue'
import StockInOutDetail from './NoDyeingFabirc/StockInOutDetail.vue'
import EditGrossCost from './NoDyeingFabirc/EditGrossCost.vue'
import {
  EditRawMaterialCostPrice,
  getRawMaterialListWaitDyeYarn,
  getRawMaterialListWaitDyeYarnExport,
} from '@/api/rawStockTable'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import Table from '@/components/Table.vue'
import { AddRmCostPrice } from '@/api/dyeingFactoryTable'

const state = reactive<any>({
  multipleSelection: [],
  filterData: {
    unit_id: '',
    supplier_id: '',
    customer_id: '',
    level: '',
    raw_material_id: '',
    color_id: '',
    remark: '',
    spinning_type: '',
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const { loading, fetchData: ApiCustomerList, data, total: totalList, page: pageList, size: sizeList, handleSizeChange, handleCurrentChange } = getRawMaterialListWaitDyeYarn()

const tableConfig = ref({
  fieldApiKey: 'RawStockTable_B',
  showSlotNums: true,
  height: 600,
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
  loading,
  showOperate: false,
  // operateWidth: '6%',
  handleSizeChange,
  handleCurrentChange,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
})

const arrange_time = ref([])
const production_date = ref([])
// 获取请求参数
function getQuery() {
  const query = {
    ...state.filterData,
  }
  if (arrange_time.value?.length) {
    query.change_start_date = formatDate(arrange_time.value[0]) || ''
    query.change_end_date = formatDate(arrange_time.value[1]) || ''
  }
  if (production_date.value?.length) {
    query.production_start_date = formatDate(production_date.value[0]) || ''
    query.production_end_date = formatDate(production_date.value[1]) || ''
  }
  return getFilterData(query)
}
// 获取数据
const getData = debounce(() => {
  ApiCustomerList(getQuery())
}, 400)
watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

onMounted(() => {
  getData()
  // state.filterData.date = getRecentDay_Date(1)
})
function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (
        [
          'before_whole_piece_count',
          'before_bulk_piece_count',
          'in_whole_piece_count',
          'in_bulk_piece_count',
          'return_whole_piece_count_out',
          'return_bulk_piece_count_out',
          'produce_use_whole_piece_count_out',
          'produce_use_bulk_piece_count_out',
          'allo_whole_piece_count_out',
          'allo_bulk_piece_count_out',
          'deduction_whole_piece_count_out',
          'deduction_bulk_piece_count_out',
          'check_whole_piece_count_out',
          'check_bulk_piece_count_out',
          'adjust_whole_piece_count_out',
          'adjust_bulk_piece_count_out',
          'whole_piece_count_out',
          'bulk_piece_count_out',
          'dye_whole_piece_count_out',
          'dye_bulk_piece_count_out',
          'change_whole_piece_count_out',
          'change_bulk_piece_count_out',
        ].includes(column.property)
      )
        return `${sumNum(data, column.property) as any}`

      if (
        [
          'before_total_weight',
          'in_total_weight',
          'return_total_weight',
          'dye_total_weight',
          'change_total_weight',
          'allo_total_weight',
          'deduction_total_weight',
          'check_total_weight',
          'adjust_total_weight',
          'total_weight',
        ].includes(column.property)
      )
        return `${formatWeightDiv(sumNum(data, column.property) as any)}`

      return null
    }),
  ]
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const EditFactoryRemarkRef = ref()
const EditGrossCostRef = ref()
const showEditGross = ref(false)
const { fetchData, success, msg } = EditRawMaterialCostPrice()
const currentRow = ref<any>(null)
const { fetchData: addGfmCostPrice, success: addSuccess, msg: addMsg } = AddRmCostPrice()
async function handleEditGrossSuccess(val: any) {
  if (!currentRow.value.rm_cost_price_id) {
    await addGfmCostPrice({
      buoyant_weight_price: formatWeightMul(val),
      buoyant_weight: currentRow.value.total_weight,
      cost_calculate_date: formatDate(Date.now()),
      // grey_fabric_id: currentRow.value.grey_fabric_id,
      // grey_fabric_color_id: currentRow.value.gray_fabric_color_id,
      rm_stock_id: currentRow.value.id,
    })
    if (!addSuccess.value)
      return ElMessage.error(addMsg.value)
    getData()

    return
  }
  await fetchData({
    id: currentRow.value.rm_cost_price_id,
    buoyant_weight_price: formatWeightMul(val),
  })
  if (!success.value)
    return ElMessage.error(msg.value)
  getData()
}
function handleEditGross(row: any) {
  EditGrossCostRef.value.form.number = formatWeightDiv(row.buoyant_weight_price)
  showEditGross.value = true
  currentRow.value = row
}
function handEditRemark(row: any) {
  // if (!state.multipleSelection.length)
  //   return ElMessage.error('至少选择一条数据')
  state.multipleSelection = [row]
  EditFactoryRemarkRef.value.state.showModal = true
  EditFactoryRemarkRef.value.state.weave_factory_remark = row.weave_factory_remark
}

const RemarkEditLogRef = ref()

function handEditDialog() {
  if (state.multipleSelection.length > 1)
    return ElMessage.error('只能勾选一条数据')

  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')

  RemarkEditLogRef.value.state.showModal = true
}
const StockInOutDetailRef = ref()
function handAccount() {
  if (state.multipleSelection.length > 1)
    return ElMessage.error('只能勾选一条数据')

  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')

  StockInOutDetailRef.value.state.showModal = true
}

const table_coloumn = ref([
  {
    field: 'A',
    title: '',
    childrenList: [
      {
        sortable: true,
        field: 'raw_material_code',
        title: '原料编号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'raw_material_name',
        title: '原料名称',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'unit_name',
        title: '染纱厂名称',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供应商',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'brand',
        title: '原料品牌',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'batch_num',
        title: '原料批号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'color_code',
        title: '原料色号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'color_name',
        title: '原料颜色',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'dyelot_number',
        title: '缸号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'level_name',
        title: '原料等级',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'remark',
        title: '原料备注',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'production_date',
        title: '生产日期',
        minWidth: 100,
        isDate: true,
        formatTime: 'YYYY-MM-DD',
      },
      {
        sortable: true,
        field: 'spinning_type',
        title: '纺纱类型',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'cotton_origin',
        title: '棉花产地',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'yarn_origin',
        title: '棉纱产地',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'carton_num',
        title: '装箱单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'fapiao_num',
        title: '发票号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'weave_factory_remark',
        soltName: 'weave_factory_remark',
        title: '染纱厂备注',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 100,
      },
    ],
  },
  {
    field: 'B',
    title: '上期结余',
    childrenList: [
      {
        sortable: true,
        field: 'before_whole_piece_count',
        title: '整件件数',
        isPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'before_bulk_piece_count',
        title: '散件件数',
        isPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'before_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'C',
    title: '本期进仓',
    childrenList: [
      {
        sortable: true,
        field: 'in_whole_piece_count',
        isPrice: true,
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'in_bulk_piece_count',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'in_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'D',
    title: '本期退纱',
    childrenList: [
      {
        sortable: true,
        field: 'return_whole_piece_count_out',
        isPrice: true,
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'return_bulk_piece_count_out',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'return_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'E',
    title: '本期染整',
    childrenList: [
      {
        sortable: true,
        field: 'dye_whole_piece_count_out',
        isPrice: true,
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'dye_bulk_piece_count_out',
        title: '散件件数',
        isPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'dye_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'K',
    title: '本期变更',
    childrenList: [
      {
        sortable: true,
        isPrice: true,
        field: 'change_whole_piece_count_out',
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'change_bulk_piece_count_out',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'change_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'F',
    title: '本期调拨',
    childrenList: [
      {
        sortable: true,
        field: 'allo_whole_piece_count_out',
        isPrice: true,
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'allo_bulk_piece_count_out',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'allo_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'G',
    title: '本期扣款',
    childrenList: [
      {
        sortable: true,
        field: 'deduction_whole_piece_count_out',
        isPrice: true,
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'deduction_bulk_piece_count_out',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'deduction_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'H',
    title: '本期盘点',
    childrenList: [
      {
        sortable: true,
        isPrice: true,
        field: 'check_whole_piece_count_out',
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'check_bulk_piece_count_out',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'check_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'I',
    title: '本期调整',
    childrenList: [
      {
        sortable: true,
        field: 'adjust_whole_piece_count_out',
        isPrice: true,
        title: '整件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'adjust_bulk_piece_count_out',
        isPrice: true,
        title: '散件件数',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'adjust_total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    field: 'J',
    title: '本期结存',
    childrenList: [
      {
        sortable: true,
        field: 'whole_piece_count',
        title: '整件件数',
        isPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'bulk_piece_count',
        title: '散件件数',
        isPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'total_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'buoyant_weight_price',
        fixed: 'right',
        title: '毛重单价',
        minWidth: 100,
        soltName: 'buoyant_weight_price',
      },
      {
        sortable: true,
        field: 'stock_cost',
        fixed: 'right',
        title: '库存成本',
        minWidth: 100,
        isPrice: true,
      },
    ],
  },
])

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '染纱厂坯纱库存'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = getRawMaterialListWaitDyeYarnExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch(getQuery())
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

defineExpose({
  state,
  getData,
})

function changeDate() {
  getData()
}
</script>

<template>
  <div class="list-page">
    <FildCard title="" class="!p-0" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="染纱厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.unit_id"
              :query="{ unit_type_id: `${BusinessUnitIdEnum.knittingFactory},${BusinessUnitIdEnum.dyeingMill}`, name: componentRemoteSearch.unit_name }"
              api="GetBusinessUnitListApi"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.customer_id"
              api="GetCustomerEnumList"
              :query="{ name: componentRemoteSearch.customer_name }"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.customer_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料等级:">
          <template #content>
            <SelectComponents v-model="state.filterData.level" api="GetInfoBaseRawMaterialLevelEnumList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="日期范围:">
          <template #content>
            <SelectDate v-model="arrange_time" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生产日期:">
          <template #content>
            <SelectDate v-model="production_date" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料编号:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              api="rawmaterialMenu"
              label-field="code"
              :query="{ code: componentRemoteSearch.raw_code }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '原料编号',
                  minWidth: 100,
                },
              ]"
              @change-input="val => (componentRemoteSearch.raw_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              api="rawmaterialMenu"
              label-field="name"
              :query="{ name: componentRemoteSearch.raw_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.raw_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料色号:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.color_id"
              api="GetRawMaterialColor"
              label-field="code"
              :query="{ raw_matl_id: state.filterData.raw_material_id, code: componentRemoteSearch.color_code }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '原料色号',
                  minWidth: 100,
                },
              ]"
              @change-input="val => (componentRemoteSearch.color_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料颜色:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.color_id"
              api="GetRawMaterialColor"
              label-field="name"
              :query="{ raw_matl_id: state.filterData.raw_material_id, name: componentRemoteSearch.color_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.color_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料备注:">
          <template #content>
            <el-input v-model="state.filterData.remark" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="纺纱类型:">
          <template #content>
            <el-input v-model="state.filterData.spinning_type" />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full !p-0">
      <template #right-top>
        <el-button type="primary" @click="handAccount">
          查看进出仓明细
        </el-button>
        <el-button type="primary" plain @click="handEditDialog">
          查看修改记录
        </el-button>
        <!--        <el-button type="primary" plain @click="handEditRemark"> -->
        <!--          编辑染纱厂备注 -->
        <!--        </el-button> -->
        <BottonExcel
          v-has="`RawStockTableExistDyeingFabircExport`"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table :config="tableConfig" :table-list="data?.list || [{}]" :column-list="table_coloumn">
        <template #buoyant_weight_price="{ row }">
          <span class="flex cursor-pointer items-center" @click="handleEditGross(row)">
            ￥{{ formatWeightDiv(row.buoyant_weight_price) || 0 }}
            <el-link :icon="Edit" :underline="false" />
          </span>
        </template>
        <template #weave_factory_remark="{ row }">
          <span class="flex cursor-pointer items-center" @click="handEditRemark(row)">
            {{ row.weave_factory_remark }}
            <el-link :icon="Edit" :underline="false" />
          </span>
        </template>
        <!--        <template #operate="{ row }"> -->
        <!--          <el-link type="primary" :underline="false" @click="handEditRemark(row)"> -->
        <!--            编辑备注 -->
        <!--          </el-link> -->
        <!--        </template> -->
      </Table>
    </FildCard>
  </div>
  <EditGrossCost
    ref="EditGrossCostRef"
    v-model="showEditGross"
    :row-data="currentRow"
    @success="handleEditGrossSuccess"
  />
  <StockInOutDetail ref="StockInOutDetailRef" :row-item="state.multipleSelection[0]" />
  <RemarkEditLog ref="RemarkEditLogRef" :row-item="state.multipleSelection[0]" />
  <EditFactoryRemark ref="EditFactoryRemarkRef" :row-item="state.multipleSelection" @on-success="getData" />
</template>

<style></style>
