<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue'
import { formatDate, formatTime } from '@/common/format'
import FildCard from '@/components/FildCard.vue'
import GridTable from '@/components/GridTable/index.vue'
import { GetFineCodePrintRecord } from '@/api/scheduleProduction'

interface Props {
  modelValue: boolean
  fineCodeId?: number
  fineCodeName?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  fineCodeId: 0,
  fineCodeName: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const showModal = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 获取打印记录数据
const { fetchData, data, loading } = GetFineCodePrintRecord()

// 表格配置
const tableConfig = reactive({
  loading,
  showPagition: false,
  showSlotNums: false,
  showCheckBox: false,
  showOperate: false,
  height: '90%',
  showSort: false,
})

// 列配置
const columnList = ref([
  {
    field: 'printer_name',
    title: '打印人',
    minWidth: 120,
  },
  {
    field: 'print_time',
    title: '打印时间',
    minWidth: 180,
    slots: {
      default: ({ row }: any) => {
        return formatTime(row.print_time)
      },
    },
  },
  {
    field: 'print_count',
    title: '打印数量',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 200,
  },
])

// 监听弹窗打开，获取数据
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal && props.fineCodeId) {
      await fetchData({
        production_schedule_order_fine_code_id: props.fineCodeId,
      })
    }
  },
)

function handleClose() {
  emit('update:modelValue', false)
}
</script>

<template>
  <vxe-modal
    v-model="showModal"
    :title="`打印记录 - ${fineCodeName || '细码'}`"
    width="70vw"
    height="60vh"
    :mask="false"
    :esc-closable="true"
    resize
    @close="handleClose"
  >
    <FildCard title="" class="h-full flex flex-col" :tool-bar="false">
      <GridTable
        :columns="columnList"
        :data="data?.list || []"
        :config="tableConfig"
        height="90%"
      />
    </FildCard>
  </vxe-modal>
</template>

<style scoped>
</style>
