<script setup lang="ts" name="ColorCardShop">
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { pick } from 'lodash-es'
import FildCard from '@/components/FildCard.vue'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import { arrayToString, copyToClipboard, stringToArray } from '@/common/util'
import { imgTypeAccept } from '@/common/uploadImage'
import { GetGenerateAccountSetId, GetMerchantInfo, GetMerchantInfoType, GetServiceExpire, UpdateMerchantInfo } from '@/api/colorCardManagement/merchant'

const defaulContact = {
  phone: '',
  name: '',
}
const formData = ref<Api.ColorCard.MerchantInfo>({
  logo_urls: [],
  merchant_name: '',
  merchant_addr: '',
  main_products: '',
  contactList: [defaulContact],
  business_type: [],
  company_urls: [],
})
// 添加联系人方法
function addContact() {
  formData.value.contactList.push({
    phone: '',
    name: '',
  })
}

// 删除联系人方法
function removeContact(index: number) {
  formData.value.contactList.splice(index, 1)
}

// 获取数据
const { fetchData, data: detailData } = GetMerchantInfo()
async function getData() {
  await fetchData()
  const contactList = JSON.parse(detailData.value?.phone_contacts || '[]')
  formData.value = {
    ...formData.value,
    ...detailData.value,
    logo_urls: stringToArray(detailData.value?.logo_url),
    company_urls: stringToArray(detailData.value?.company_url),
    business_type: stringToArray(detailData.value?.business_type),
    contactList: contactList.length ? contactList : [defaulContact],
  }
}
// 获取到期时间
const { fetchData: getExpireFetch, data: expireData } = GetServiceExpire()

// 获取企业类型标签
const { fetchData: getBusinessTypeFetch, data: businessTypeData } = GetMerchantInfoType()
onMounted(() => {
  getData()
  getExpireFetch()
  getBusinessTypeFetch()
})

// 提交所有数据
const { fetchData: updateFetch, success: updateSuccess, msg: updateMsg, loading: updateLoading } = UpdateMerchantInfo()
async function submitAddAllData() {
  try {
    validateForm()
    const query = {
      ...pick(formData.value, ['id', 'merchant_name', 'merchant_addr', 'main_products']),
      logo_url: arrayToString(formData.value.logo_urls),
      company_url: arrayToString(formData.value.company_urls),
      business_type: arrayToString(formData.value.business_type),
      phone_contacts: JSON.stringify(formData.value.contactList || '[]'),
    }

    await updateFetch({
      ...query,
    })
    if (updateSuccess.value) {
      ElMessage.success('提交成功')
      getData()
    }
    else {
      ElMessage.error(updateMsg.value)
    }
  }
  catch (error) {

  }
}

// 校验表单
function validateForm() {
  const { merchant_name, contactList, business_type } = formData.value
  let errTip = ''
  // 校验商家名称是否为空
  if (!merchant_name)
    errTip = '请填写商家名称'
  // 校验联系电话
  for (let i = 0; i < contactList.length; i++) {
    const { phone, name } = contactList[i]
    const phoneRegex = /^[0-9\p{P}]+$/u
    // 联系人和联系电话需同时填写，不能留有空行（除第一行外）
    if ((phone && !name) || (!phone && name) || (i > 0 && !phone && !name)) {
      errTip = `联系电话：第 ${i + 1} 行信息请补充完整`
      break
    }
    else if (phone && !phoneRegex.test(phone)) {
      errTip = `联系电话：第 ${i + 1} 行联系人的电话号码只能包含数字和标点符号`
      break
    }
  }
  // 校验标签类型
  if (!errTip && !business_type?.length)
    errTip = '请选择标签类型'

  if (errTip) {
    ElMessage.error(errTip)
    throw new Error(errTip)
  }
}

// 生成链接
const { fetchData: saveLinkFetch, success: saveLinkSuccess, msg: saveLinkMsg, loading: saveLinkLoading } = GetGenerateAccountSetId()
async function handleSaveLink() {
  const res = await saveLinkFetch()

  if (!saveLinkSuccess.value)
    return ElMessage.error(saveLinkMsg.value)

  const id = res?.data?.encrypted_account_set_id

  const url = `https://hcscmtest.zzfzyc.com/qywx_back/colorCardShopIndex?id=${id}`
  // const url = `http://************:10086/qywx_back/colorCardShopIndex?id=${id}`
  copyToClipboard(url)
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <div>
        <el-tag type="info" size="default" class="mr-2">
          <div class="flex align--center">
            <el-icon color="var(--el-color-primary)" :size="20">
              <Calendar />
            </el-icon>
            <span class="text-black ml-1">服务有效期至 {{ expireData.service_expire }}</span>
          </div>
        </el-tag>
        <el-button :loading="updateLoading" type="primary" @click="submitAddAllData">
          保存
        </el-button>
        <el-button icon="Link" :loading="saveLinkLoading" @click="handleSaveLink">
          生成链接
        </el-button>
      </div>
    </template>
    <div class="flex align--center">
      <div class="main_products-left mr-[8%]">
        <el-form-item label="logo上传" label-position="top" class="form-item-logo" size="default">
          <TextureMapWall
            v-model:image-list="formData.logo_urls"
            :text-show="false"
            :auto-upload="true"
            :multiple="false"
            additional-text="建议尺寸：200*200px"
            :accept="imgTypeAccept"
          />
        </el-form-item>
      </div>

      <div class="main_products-right w-[50%]">
        <el-form :model="formData" label-position="top" label-width="auto" style="max-width: 600px" size="default">
          <el-form-item label="商家名称" required>
            <el-input v-model="formData.merchant_name" placeholder="请输入商家名称" />
          </el-form-item>
          <el-form-item label="商家地址">
            <el-input v-model="formData.merchant_addr" type="textarea" placeholder="请输入商家详细地址" />
          </el-form-item>
          <el-form-item label="主营产品">
            <el-input v-model="formData.main_products" type="textarea" placeholder="请输入主营产品" :autosize="{ minRows: 2, maxRows: 6 }" show-word-limit :maxlength="200" />
          </el-form-item>
          <el-form-item label="联系电话">
            <template v-for="(item, index) in formData.contactList" :key="index">
              <el-row :gutter="10" class="mt-2">
                <el-col :span="11">
                  <el-input v-model="item.phone" placeholder="电话号码" />
                </el-col>
                <el-col :span="11">
                  <el-input v-model="item.name" placeholder="联系人" />
                </el-col>
                <el-col v-if="index > 0" :span="2">
                  <el-button type="danger" @click="removeContact(index)">
                    删除
                  </el-button>
                </el-col>
                <el-col v-if="index === 0" :span="2">
                  <el-button type="primary" @click="addContact">
                    添加
                  </el-button>
                </el-col>
              </el-row>
            </template>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </FildCard>
  <FildCard class="mt-[5px]" :tool-bar="false">
    <template #title>
      企业类型标签<span class="text-[var(--el-color-danger)]">*</span>
    </template>
    <el-checkbox-group v-model="formData.business_type" class="pb-3" size="default">
      <el-checkbox v-for="e in businessTypeData.list || []" :key="e.id" :label="e.name" :value="e.id" />
    </el-checkbox-group>
  </FildCard>

  <FildCard title="企业详情图" class="mt-[5px]" :tool-bar="false">
    <TextureMapWall
      v-model:image-list="formData.company_urls"
      :accept="imgTypeAccept" :text-show="false"
    />
  </FildCard>
</template>

<style lang="scss" scoped></style>
