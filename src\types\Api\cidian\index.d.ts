declare namespace Api.DefectBasicInfo {
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * limit
     */
    limit?: number
    /**
     * 名称
     */
    name?: string
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    /**
     * 状态
     */
    status?: number
    [property: string]: any
  }
  /**
   * info_basic_data.GetInfoBasicDefectData
   */
  export interface Response {
  /**
   * 编号
   */
    code?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 排序
     */
    sort?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 状态名
     */
    status_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
