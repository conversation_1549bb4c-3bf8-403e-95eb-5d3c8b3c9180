<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import QyRobot from './components/QyRobot.vue'
import ABuRobot from './components/ABuRobot.vue'
import RechargeRecord from './components/RechargeRecord.vue'
import AppDetail from './components/appDetail.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'
import { GetQywxConfigList } from '@/api'
import { CancelBindTenant } from '@/api/qywx'
import { throttle } from '@/util'
import { getFilterData } from '@/common/util'
import type { TableColumn } from '@/components/Table/type'

const filterData = ref({
  page: 1,
  size: 50,
  tenant_name: '', // 账套名称
  contact_person: '', // 联系人
  phone: null,
  tobe_developed_app_name: null, // 应用名称
})
const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'tenant_id',
    title: '账套ID',
    align: 'center',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'tenant_name',
    title: '账套名称',
    align: 'center',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'contact_person',
    align: 'center',
    title: '联系人',
    minWidth: 150,
  },
  {
    sortable: true,
    align: 'center',
    field: 'phone',
    title: '联系电话',
    minWidth: 150,
  },
  // {
  //   sortable: true,
  //   field: 'create_time',
  //   align: 'center',
  //   title: '应用有效期',
  //   minWidth: 150,
  //   isDate: true,
  // },
  // {
  //   sortable: true,
  //   field: 'tenant_management_status_name',
  //   align: 'center',
  //   title: '状态',
  //   minWidth: 100,
  //   soltName: 'statusName',
  // },
  {
    sortable: true,
    field: 'robot_code',
    align: 'center',
    title: '机器人编码',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'robot_effect_time',
    align: 'center',
    title: '机器人有效截至日期',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'update_time',
    align: 'center',
    title: '最后修改时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'updater',
    align: 'center',
    title: '最后修改人',
    minWidth: 150,
  },
])
const { fetchData: getData, data, total, success, msg, loading, page, size, handleSizeChange, handleCurrentChange } = GetQywxConfigList()
const tableConfig = ref({
  fieldApiKey: 'setOfAccountsManagement',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '300',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})
async function fetchData() {
  await getData(getFilterData(filterData.value))
  if (!success.value)
    return ElMessage.error(msg.value)
}
const tableRef = ref()
const fetchDataThrottled = throttle(fetchData, 500)
watch(
  () => filterData.value,
  () => {
    fetchDataThrottled()
  },
  {
    deep: true,
  },
)

onMounted(async () => {
  await fetchData()
})
// const RechargeRef = ref()
const QyRobotRef = ref()
const ABuRobotRef = ref()
const AppDetailRef = ref()
const RechargeRecordRef = ref()
const isBindingEdit = ref(false)
// 查看绑定关系
function bindingRelationship(row: Api.GetQywxConfigList.QywxGetQYWXBoundListResponseItem) {
  isBindingEdit.value = false
  AppDetailRef.value.state.showModal = true
  AppDetailRef.value.state.modalName = '查看企微应用'
  AppDetailRef.value.accountColumnList = [{
    company_name: row.tenant_name,
    contacts: row.contact_person,
    id: row.tenant_id,
    phone: row.phone,
  }]
  AppDetailRef.value.state.form.agent_id = row.agent_id
  AppDetailRef.value.state.form.tobe_developed_app_id = row.tobe_developed_app_id
  AppDetailRef.value.state.form.app_description = row.app_description
}
const { fetchData: cancelBindTenant, success: cancelBindTenantSuccess, msg: cancelBindTenantMsg } = CancelBindTenant()
// 删除绑定关系
function RemoveBindingRelationship(row: Api.GetQywxConfigList.QywxGetQYWXBoundListResponseItem) {
  ElMessageBox.confirm('确定要删除绑定关系吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async (res: any) => {
    if (res === 'confirm') {
      await cancelBindTenant({
        tenant_management_id: row.tenant_id,
        tobe_developed_app_id: row.tobe_developed_app_id,
      })
      if (!cancelBindTenantSuccess.value)
        return ElMessage.error(cancelBindTenantMsg.value)
      getData()
    }
  })
}
function editABuRobot(row: Api.GetQywxConfigList.QywxGetQYWXBoundListResponseItem) {
  ABuRobotRef.value.state.form.tobe_developed_app_id = row.tobe_developed_app_id
  ABuRobotRef.value.state.showModal = true
}
function editQyRobot(row: Api.GetQywxConfigList.QywxGetQYWXBoundListResponseItem) {
  QyRobotRef.value.state.showModal = true
  QyRobotRef.value.state.form.tobe_developed_app_id = row.tobe_developed_app_id
}
function handleAdd() {
  isBindingEdit.value = true
  AppDetailRef.value.state.showModal = true
  AppDetailRef.value.state.modalName = '绑定企微应用'
  AppDetailRef.value.accountColumnList = []

  AppDetailRef.value.state.form.agent_id = ''
  AppDetailRef.value.state.form.tobe_developed_app_id = ''
  AppDetailRef.value.state.form.app_description = ''
}
// function recharge() {
//   RechargeRef.value.state.showModal = true
// }
// function rechargeRecord() {
//   RechargeRecordRef.value.state.showModal = true
// }
function handleSubmitAdd() {
  getData()
}
</script>

<template>
  <section class="flex flex-col h-full overflow-hidden">
    <FildCard :tool-bar="false" :too-tips="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="账套名称:">
          <template #content>
            <el-input v-model="filterData.tenant_name" clearable placeholder="输入账套ID\名称" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人:">
          <template #content>
            <el-input v-model="filterData.contact_person" clearable placeholder="输入联系人" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话:">
          <template #content>
            <el-input v-model="filterData.phone" clearable placeholder="输入联系电话" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="应用名称">
          <template #content>
            <el-input v-model="filterData.tobe_developed_app_name" clearable placeholder="输入应用名称" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button type="primary" @click="handleAdd">
              新增
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="mt-[5px] flex flex-1 overflow-hidden flex-col h-full">
      <Table ref="tableRef" :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #statusName="{ row }">
          <div class="flex justify-center">
            <el-switch v-model="row.tenant_management_status" />
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="8">
            <el-link type="primary" :underline="false" @click="bindingRelationship(row)">
              查看绑定信息
            </el-link>
            <el-link type="danger" :underline="false" @click="RemoveBindingRelationship(row)">
              删除绑定
            </el-link>
            <el-link type="primary" :underline="false" @click="editABuRobot(row)">
              阿布机器人
            </el-link>
            <el-link type="primary" :underline="false" @click="editQyRobot(row)">
              群机器人
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <RechargeRecord ref="RechargeRecordRef" />
    <AppDetail ref="AppDetailRef" :is-edit="isBindingEdit" @handle-submit="getData" />
    <ABuRobot ref="ABuRobotRef" @handle-submit="handleSubmitAdd" />
    <!--    <Recharge ref="RechargeRef" @handle-submit="handleSubmitAdd" /> -->
    <QyRobot ref="QyRobotRef" @handle-submit="handleSubmitAdd" />
  </section>
</template>

<style scoped>

</style>
