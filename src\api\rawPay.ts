import { useRequest } from '@/use/useRequest'

// 获取列表
export const raw_matl_purlist = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const raw_matl_purdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur/detail',
    method: 'get',
  })
}

// 审核
export const raw_matl_purpass = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur/pass',
    method: 'put',
  })
}

// 消审
export const raw_matl_purcancel = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur/cancel',
    method: 'put',
  })
}

// 驳回
export const raw_matl_purreject = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur/reject',
    method: 'put',
  })
}

// 作废
export const raw_matl_purvoid = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur/void',
    method: 'put',
  })
}

//  更新
export const raw_matl_purput = () => {
  return useRequest({
    url: '/admin/v1/payable/raw_matl_pur',
    method: 'put',
  })
}
