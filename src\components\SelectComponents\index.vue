<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import selectApi from '@/api/selectInit'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import eventBus from '@/common/evenBus'
import SelectOptions from '@/components/SelectOptions/index.vue'
import SelectVirtualOptions from '@/components/SelectVirtualOptions/index.vue'
import { useComponentsRequestStore } from '@/stores/requestCaching'
import { generateReqKey } from '@/util/commonFuns'
import { getFilterData } from '@/common/util'
import type { RequestOptions } from '@/use/useRequest'

defineOptions({
  name: 'SelectComponents',
})

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  virtual: false,
  placeholder: '请选择内容',
  modelValue: '',
  multiple: false,
  size: '',
  disabled: false,
  labelField: 'label',
  valueField: 'value',
  labelValue: '',
  soltLeftLabelField: 'code',
  soltRightLabelField: 'name',
  query: () => ({}),
  idSelectDefaultValue: false,
  defaultStatus: false,
  duty: '',
  warehouse_type_id: '',
  business_unit_id: '',
  style: { minWidth: '100px', width: '100%' },
  visibleChangeClose: false,
  watchStatus: false,
  clearable: true,
  showSlot: false,
  queryIsData: false, // 只有带出请求参数才会请求数据
})

const emits = defineEmits([
  'update:modelValue',
  'select',
  'changeValue',
  'update:currentItem',
])

export interface Props {
  virtual?: boolean
  options: []
  placeholder?: string
  modelValue: number | string
  allowCreate?: boolean
  currentItem?: any[] // 当前选中的 枚举 数据
  multiple?: boolean
  size?: string
  disabled?: boolean
  labelField?: string
  valueField?: string
  labelValue?: string
  soltLeftLabelField: string
  soltRightLabelField: string
  api?: keyof typeof selectApi
  query?: object
  queryConfig?: RequestOptions // 请求接口的useRequery配置
  idSelectDefaultValue: boolean
  defaultStatus?: boolean
  exclude?: number[]
  duty: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | '' //  0: 未知职务  1:销售员 2:打单员 3:搬运工 4:查布员 5:仓管员 6:业务经理 7:跟单员 8:跟单QC员 9:司机  10:配布员 '':默认值
  // eslint-disable-next-line vue/prop-name-casing
  warehouse_type_id: 'finishProduction' | 'rawMaterial' | 'grey' | '' // 当下拉为仓库时生效
  // warehouse_type_id: 成品 | 原料 | 坯布
  // eslint-disable-next-line vue/prop-name-casing
  business_unit_id:
    | 'rawMaterial'
    | 'knittingFactory'
    | 'dyeFactory'
    | 'purchase'
    | 'blankFabric'
    | '' // 当下拉为往来单位时
  // business_unit_id: 原料供应商 | 织厂 | 染厂 | 成品采购供应商
  style: any
  visibleChangeClose: boolean
  watchStatus: boolean
  clearable: boolean
  showSlot: boolean
  queryIsData: boolean
  quickAddLink?: string // 快速添加链接
  quickAddPremission?: string // 快速添加权限
}

const componentsRequestStore = useComponentsRequestStore()
const key = ref('')
const filterData = ref<any>({})
function formatFiterData() {
  filterData.value = props.query
  if (props.duty !== '')
    filterData.value.duty = props.duty
  if (props.warehouse_type_id) {
    filterData.value.warehouse_type_id
      = WarehouseTypeIdEnum[props.warehouse_type_id]
  }
  if (props.business_unit_id)
    filterData.value.unit_type_id = BusinessUnitIdEnum[props.business_unit_id]
  const config = {
    data: {},
    url: props.api,
    method: 'get',
    params: filterData,
  }
  key.value = generateReqKey(config)
}

const options = ref<any>([])

let fetchApi = selectApi[props.api]

if (!fetchApi) {
  fetchApi = () => {
    return {
      fetchData: () => {},
      data: [],
      size: 0,
      page: 0,
      total: 0,
    }
  }
}

const { fetchData, data, size, page, total } = fetchApi(props.queryConfig)
// 再次执行一遍 getFetchData 用于解决在同一个页面中 有多个 SelectComponents时，第二个SelectComponents的无数据的问题
function doOnceGetFetchData() {
  setTimeout(() => {
    getFetchData()
  }, 500)
}

async function getFetchData() {
  formatFiterData()

  if (props.options.length > 0)
    return
  if (!componentsRequestStore.count[key.value]) {
    componentsRequestStore.addCount(key.value)
    await fetchData(filterData.value)
    options.value = data.value?.list || data.value?.sub_department
    if (props.exclude != null && Array.isArray(props.exclude))
      options.value = options.value?.filter((v: any) => !props.exclude!.includes(v.id)) || []
    else
      options.value = options.value || []
    componentsRequestStore.setRequestData(key.value, {
      data: options.value,
      size: size.value,
      page: page.value,
      total: total.value,
    })
  }
  else {
    const targetStore = componentsRequestStore.list[key.value]
    if (targetStore) {
      const {
        data,
        size: cacheSize,
        page: cachePage,
        total: cacheTotal,
      } = targetStore

      options.value = data
      size.value = cacheSize
      page.value = cachePage
      total.value = cacheTotal
    }
    else {
      doOnceGetFetchData()
    }
  }
  // 设置默认值
  if (props.idSelectDefaultValue && Array.isArray(options.value)) {
    emits('update:modelValue', options.value[0]?.[props.valueField])

    emits('update:currentItem', options.value[0])
  }
  else if (props.idSelectDefaultValue && !Array.isArray(options.value)) {
    emits('update:modelValue', 0)
    emits('update:currentItem', null)
  }
}

// watch(
//   () => componentsRequestStore.list,
//   (val: any) => {
//

//     if (key.value && val) options.value = val[key.value]
//   },
//   { deep: true }
// )

watch(
  () => props.query,
  async () => {
    if (!props.queryIsData)
      getFetchData()
    else if (
      props.queryIsData
      && Object.keys(getFilterData(props.query)).length
    )
      getFetchData()
  },
  {
    deep: true,
    immediate: true,
  },
)

watch(
  () => props.options,
  (newList) => {
    if (!props.api && newList && newList.length > 0)
      options.value = newList
  },
  {
    deep: true,
    immediate: true,
  },
)

let initFirst = true

const currentOptionItem = computed(() => {
  return options.value?.filter(
    (item: any) => item[props.valueField] === props.modelValue,
  )[0]
})

watch(
  () => [props.modelValue, options.value],
  ([newModelValue, optionsValue]) => {
    // 获取默认值()
    if (
      newModelValue
      && (initFirst || props.watchStatus)
      && props.defaultStatus
      && Array.isArray(optionsValue)
      && optionsValue.length
    ) {
      const res = currentOptionItem.value
      emits('select', { ...res, initFirst })
      emits('changeValue', { ...res, initFirst })
      initFirst = false

      emits('update:currentItem', currentOptionItem.value)
    }
  },
  { immediate: true },
)

function handChange(val: any) {
  // TODO:根据传进的valueField值查询指定数据
  const res
    = options?.value?.filter((item: any) => {
      return item[props.valueField] === val
    }) || []

  const currentItem = res.length > 0 ? res[0] : val

  emits('update:modelValue', val)
  emits('update:currentItem', currentItem)
  emits('select', currentItem)
  emits('changeValue', currentItem || '')
}

const SelectOptionsRef = ref()

nextTick(() => {
  eventBus.on('handleScrollRef', () => {
    SelectOptionsRef.value?.selectRef?.blur()
  })
})

// 默认加工厂
async function setDefaultFactory() {
  formatFiterData()
  // 判断是否已经拿到数据
  if (!Array.isArray(componentsRequestStore.list[key.value])) {
    componentsRequestStore.addCount(key.value)
    await fetchData(filterData.value)
    options.value = data.value?.list || data.value?.sub_department
    componentsRequestStore.setRequestData(key.value, options.value)
  }
  else {
    options.value = componentsRequestStore.list[key.value]
  }
  const index = options.value.findIndex((item: any) => item.is_default)
  if (index !== -1) {
    const currentItem = options.value[index]
    emits(
      'update:modelValue',
      index !== -1 ? currentItem[props.valueField] : undefined,
    )
    emits('update:currentItem', currentItem)
    emits('select', currentItem)
    emits('changeValue', currentItem)
  }
}

async function getFetch() {
  await fetchData(filterData.value)
  options.value = data.value?.list || data.value?.sub_department
  if (props.exclude != null && Array.isArray(props.exclude))
    options.value = options.value?.filter((v: any) => !props.exclude!.includes(v.id)) || []
  else
    options.value = options.value || []
  componentsRequestStore.setRequestData(key.value, {
    data: options.value,
    size: size.value,
    page: page.value,
    total: total.value,
  })
}

defineExpose({
  fetchData,
  setDefaultFactory,
  SelectOptionsRef,
  current: currentOptionItem,
  getFetch,
})
</script>

<template>
  <template v-if="!virtual">
    <SelectOptions
      ref="SelectOptionsRef"
      :style="props.style"
      :label-field="props.labelField"
      :value-field="props.valueField"
      :label-value="props.labelValue"
      :size="props.size"
      :disabled="props.disabled"
      :placeholder="props.placeholder"
      :multiple="props.multiple"
      :options="options"
      :model-value="props.modelValue"
      :allow-create="allowCreate"
      :visible-change-close="props.visibleChangeClose"
      :clearable="props.clearable"
      :show-slot="props.showSlot"
      :solt-left-label-field="props.soltLeftLabelField"
      :solt-right-label-field="props.soltRightLabelField"
      :quick-add-link="props.quickAddLink"
      :quick-add-premission="props.quickAddPremission"
      :api="props.api"
      :query="props.query"
      @change="handChange"
      @refresh="getFetch"
    />
  </template>
  <template v-else>
    <SelectVirtualOptions
      ref="SelectOptionsRef"
      :quick-add-link="props.quickAddLink"
      :quick-add-premission="props.quickAddPremission"
      :style="props.style"
      :label-field="props.labelField"
      :value-field="props.valueField"
      :label-value="props.labelValue"
      :size="props.size"
      :disabled="props.disabled"
      :placeholder="props.placeholder"
      :multiple="props.multiple"
      :options="options"
      :model-value="props.modelValue"
      :visible-change-close="props.visibleChangeClose"
      :clearable="props.clearable"
      :show-slot="props.showSlot"
      :solt-left-label-field="props.soltLeftLabelField"
      :solt-right-label-field="props.soltRightLabelField"
      :api="props.api"
      :query="props.query"
      @change="handChange"
      @refresh="getFetch"
    />
  </template>
</template>
