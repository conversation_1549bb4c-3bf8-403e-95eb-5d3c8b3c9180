<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import ProductColorDialog from '../../dyeingNotice/components/productColorDialog.vue'
import SelectAdjustmentDialog from '../../components/SelectAdjustmentDialog.vue'
import ProductFabricDialog from './ProductFabricDialog.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { deepClone } from '@/common/util'
import { price_by_items } from '@/api/dyeingPrice'
import SelectDialog from '@/components/SelectDialog/index.vue'

interface Props {
  modelValue: any
  info?: { [key: string]: any }
  selectList?: any
  isInformation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: [],
  selectList: [],
  info: () => {
    return {}
  },
  isInformation: true,
})

const emits = defineEmits(['update:modelValue', 'update:selectList'])

const state = reactive<any>({
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

const tableConfig = ref({
  fieldApiKey: 'ColorNoticeProductTable',
  showSlotNums: true,
  // showOperate: true,
  // operateWidth: '160',
  height: 400,
  expand: true,
  expandName: 'expandShow',
  //   footerMethod: (val: any) => FooterMethod(val),
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  toggleExpandChangeEvent: (val: any) => toggleExpandChangeEvent(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
  emits('update:selectList', state.multipleSelection)
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
  emits('update:selectList', state.multipleSelection)
}

// 选择客户
function handCustomer(val: any, rowIndex: number) {
  const arr = props.modelValue
  arr.map((item: any, index: number) => {
    if (index === rowIndex)
      item.customer_name = val.name

    return item
  })
  emits('update:modelValue', arr)
}

const productColorDialogRef = ref()

const SelectComponentsRef = ref()
function focus(row: any, rowIndex: number) {
  if (props.info.factory_id === '')
    return ElMessage.error('请先选择染厂名称')

  productColorDialogRef.value.state.showModal = true
  productColorDialogRef.value.state.productId = row.finish_product_id
  productColorDialogRef.value.state.dye_factory_id = props.info.factory_id

  productColorDialogRef.value.state.info.factory_name = props.info.factory_name
  productColorDialogRef.value.state.info.finish_product_code = row.code
  productColorDialogRef.value.state.info.finish_product_name = row.name

  productColorDialogRef.value.state.rowIndex = rowIndex
}

// 选择色号
function handSelectColor(list: any, rowIndex: number) {
  const arr = props.modelValue
  arr.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.color_no = list[0]?.product_color_code
      item.color_name = list[0]?.product_color_name
      item.df_color_no = list[0]?.dye_factory_color_code
      item.dyelot = list[0]?.dyelot_number
      item.dnf_craft = list[0]?.dye_craft
      item.paper_tube_specs = list[0]?.paper_tube_specs
      item.tape_specs = list[0]?.tape_specs
      item.color_id = list[0]?.id
      item.plastics_bag_ids = list[0]?.tape_specs_ids
      item.paper_tube_ids = list[0]?.paper_tube_specs_ids
      item.dnf_craft_ids = list[0]?.dye_craft_ids
    }
    return item
  })
  productColorDialogRef.value.state.showModal = false
  emits('update:modelValue', arr)
}

// 选择成品等级
function handLevel(val: any, rowIndex: number) {
  const arr = props.modelValue
  arr.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.level = val.name
      item.level_id = val.id
      item.level_code = val.code
    }
    return item
  })
  emits('update:modelValue', arr)
}

function handDelete(index: number) {
  const arr = props.modelValue
  arr.splice(index, 1)
  emits('update:modelValue', arr)
}

const ProductFabricDialogRef = ref()

// const handSee = async (row: any, rowIndex: number) => {
//

//   // ProductFabricDialogRef.value.state.showModal = true
//   if (row.order_type === 1) {
//     ProductFabricDialogRef.value.state.isDyeing = true
//   } else {
//     ProductFabricDialogRef.value.state.isDyeing = false
//   }
//   ProductFabricDialogRef.value.state.rowIndex = rowIndex
//   const arr = row.use_fabric?.map((item: any) => {
//     if (row.order_type === 1) {
//       item = {
//         ...item.gf_stock_info,
//         ...item,
//       }
//     } else {
//       item = {
//         ...item.f_out_info,
//         ...item,
//       }
//     }

//     return item
//   })
//

//   ProductFabricDialogRef.value.state.tableList = arr
// }

const tableRef = ref()

function toggleExpandChangeEvent({ expanded, row, rowIndex }: any) {
  const arr = row.use_fabric?.map((item: any) => {
    if (row.order_type === 1) {
      item = {
        ...item.gf_stock_info,
        ...item,
      }
    }
    else {
      item = {
        ...item.f_out_info,
        ...item,
      }
    }

    return item
  })

  const list = props.modelValue
  if (expanded) {
    list?.map((it: any, index: number) => {
      if (rowIndex === index) {
        it.show = true
        it.use_fabric = arr
        tableRef.value.tableRef.setRowExpand([list[index]], true)
      }
      else {
        it.show = false
        tableRef.value.tableRef.setRowExpand([list[index]], false)
      }
      return it
    })
  }
  else {
    tableRef.value.tableRef.setRowExpand([list[rowIndex]], false)
  }

  //

  emits('update:modelValue', list)
}

// const handAdd = (val: any) => {
//   const arr = props.modelValue
//   arr.map((item: any, index: number) => {
//     if (index === val.rowIndex) {
//       item.use_fabric = val.tableList
//     }
//     return item
//   })
//   ProductFabricDialogRef.value.state.showModal = false
//   emits('update:modelValue', arr)
// }

const SelectAdjustmentDialogRef = ref()

const { fetchData: itemsFetch, data: itemsData } = price_by_items()

async function cellDBLClickEvent(val: any) {
  if (val?.columnIndex === 18) {
    if (props.info.factory_id === '')
      return ElMessage.error('请先选择染厂')

    const idsOne = val.row?.plastics_bag_ids || []

    const idsTwo = val.row?.paper_tube_ids || []

    const idsThree = val.row?.dnf_craft_ids || []

    const idsArr = [...idsOne, ...idsTwo, ...idsThree]

    await itemsFetch({ item_ids: idsArr.join(',') })

    itemsData.value.list.map((item: any) => {
      item.selected = true
      return item
    })

    SelectAdjustmentDialogRef.value.state.multipleSelection = deepClone(!val.row?.select_multList?.length ? itemsData.value?.list || [] : val.row?.select_multList || [])
    SelectAdjustmentDialogRef.value.state.showModal = true
    SelectAdjustmentDialogRef.value.state.filterData.isChecked = false
    SelectAdjustmentDialogRef.value.state.dye_factory_id = props.info.factory_id
    SelectAdjustmentDialogRef.value.state.dye_name = props.info.dye_factory_name
    SelectAdjustmentDialogRef.value.state.rowIndex = val.rowIndex
    SelectAdjustmentDialogRef.value.state.product_color_code = val?.row?.code
    SelectAdjustmentDialogRef.value.state.product_color_name = val?.row?.name
  }
}

function handSure(val: any) {
  const arr = props.modelValue

  const dye_str: any = []
  const dye_ids: any = []

  const tape_str: any = []
  const tape_ids: any = []

  const tube_str: any = []
  const tube_ids: any = []
  val.multipleSelection.forEach((item: any) => {
    if (item.project === '1') {
      dye_str.push(item.project_name)
      dye_ids.push(item.item_id)
    }
    if (item.project === '3') {
      tape_str.push(item.project_name)
      tape_ids.push(item.item_id)
    }
    if (item.project === '2,4' || item.project === '2' || item.project === '4') {
      tube_str.push(item.project_name)
      tube_ids.push(item.item_id)
    }
  })
  arr.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.dnf_craft = dye_str.join('+')
      item.tape_specs = tape_str.join('+')
      item.paper_tube_specs = tube_str.join('+')
      item.plastics_bag_ids = tape_ids
      item.paper_tube_ids = tube_ids
      item.dnf_craft_ids = dye_ids
      item.select_multList = val.multipleSelection

      return item
    }
  })
  SelectAdjustmentDialogRef.value.state.showModal = false
  emits('update:modelValue', arr)
}

const columnList = ref([
  {
    field: 'order_no',
    title: '通知单号',
    minWidth: 100,
    // fixed: 'left',
  },
  {
    field: 'dnf_date',
    title: '染整日期',
    minWidth: 100,
  },
  {
    field: 'sale_plan_order_no',
    title: '销售计划单号',
    minWidth: 100,
  },

  {
    field: 'customer_id',
    title: '客户名称',
    minWidth: 100,
    soltName: 'customer_id',
    required: true,
  },
  {
    field: 'name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'src_color_no',
    title: '原色号',
    minWidth: 100,
  },
  {
    field: 'src_color_name',
    title: '原颜色',
    minWidth: 100,
  },
  {
    field: 'src_df_color_no',
    title: '原染厂色号',
    minWidth: 100,
  },
  {
    field: 'src_dyelot',
    title: '原缸号',
    minWidth: 100,
  },
  {
    field: 'color_no',
    title: '色号',
    minWidth: 100,
    soltName: 'color_num',
    required: true,
  },
  {
    field: 'color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'df_color_no',
    title: '染厂色号',
    minWidth: 100,
  },
  {
    field: 'dyelot',
    title: '对色缸号',
    minWidth: 100,
  },
  //   {
  //     field: 'tape_specs',
  //     title: '订单类型',
  //     minWidth: 100,
  //   },
  {
    field: 'level',
    title: '成品等级',
    minWidth: 100,
    soltName: 'product_grade',
  },
  {
    field: 'level_code',
    title: '等级编号',
    minWidth: 100,
  },
  {
    field: 'craft',
    title: '成品工艺',
    minWidth: 100,
  },
  {
    field: 'dnf_craft',
    title: '染整工艺',
    minWidth: 100,
    // soltName: 'dnf_craft',
  },
  {
    field: 'paper_tube_specs',
    title: '纸筒规格',
    minWidth: 100,
  },
  {
    field: 'tape_specs',
    title: '胶袋规格',
    minWidth: 100,
  },
  {
    field: 'hand_feeling',
    title: '手感风格',
    minWidth: 100,
  },
  {
    field: 'increase_weight',
    title: '加重',
    minWidth: 100,
    soltName: 'aggravate',
  },
  //   {
  //     field: 'style_no',
  //     title: '款号',
  //     minWidth: 100,
  //   },
  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'dnf_loss',
    title: '染损',
    minWidth: 100,
  },
  //   {
  //     field: 'shrinkage_rate',
  //     title: '缩水率',
  //     minWidth: 100,
  //   },
  {
    field: 'piece_count',
    title: '匹数',
    minWidth: 120,
    soltName: 'roll',
    required: true,
  },
  {
    field: 'unit',
    title: '单位',
    minWidth: 80,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 120,
    soltName: 'weight',
    required: true,
  },
  {
    field: 'delivery_date',
    title: '交期',
    minWidth: 100,
    soltName: 'delivery_time',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
  {
    field: '',
    title: '操作',
    minWidth: 100,
    soltName: 'operation',
    fixed: 'right',
  },
])
</script>

<template>
  <Table ref="tableRef" :config="tableConfig" :table-list="props.modelValue" :column-list="columnList">
    <template #customer_id="{ row, rowIndex }">
      <!-- <SelectComponents
        :query="{ sale_system_id: props.info.sale_system_id }"
        @change-value="val => handCustomer(val, rowIndex)"
        placeholder="必选"
        api="GetCustomerEnumList"
        label-field="name"
        value-field="id"
        v-model="row.customer_id"
        clearable
      /> -->
      <SelectDialog
        v-model="row.customer_id"
        :label-name="row.customer_name"
        :query="{ sale_system_id: props.info.sale_system_id, name: componentRemoteSearch.customer_name }"
        api="GetCustomerEnumList"
        :column-list="[
          {
            title: '客户编号',
            minWidth: 100,
            required: true,
            colGroupHeader: true,
            childrenList: [
              {
                field: 'code',
                isEdit: true,
                title: '客户编号',
                minWidth: 100,
              },
            ],
          },
          {
            title: '客户名称',
            minWidth: 100,
            colGroupHeader: true,
            required: true,
            childrenList: [
              {
                isEdit: true,
                field: 'name',
                title: '客户名称',
                minWidth: 100,
              },
            ],
          },
          {
            title: '电话',
            colGroupHeader: true,
            minWidth: 100,
            childrenList: [
              {
                field: 'phone',
                isEdit: true,
                title: '电话',
                minWidth: 100,
              },
            ],
          },
          {
            title: '销售员',
            minWidth: 100,
            colGroupHeader: true,
            childrenList: [
              {
                field: 'seller_name',
                title: '销售员',
                soltName: 'seller_name',
                isEdit: true,
                minWidth: 100,
              },
            ],
          },
        ]"
        @change-value="val => handCustomer(val, rowIndex)"
        @change-input="val => (componentRemoteSearch.customer_name = val)"
      />
    </template>
    <template #color_num="{ row, rowIndex }">
      <SelectComponents
        ref="SelectComponentsRef"
        v-model="row.color_no"
        visible-change-close
        placeholder="必选"
        api="BusinessUnitSupplierEnumAll"
        label-field="product_color_name"
        value-field="product_color_code"
        clearable
        @focus="focus(row, rowIndex)"
      />
    </template>
    <template #product_grade="{ row, rowIndex }">
      <SelectComponents
        v-model="row.level_code"
        placeholder="成品等级"
        api="GetInfoBaseFinishedProductLevelEnumList"
        label-field="name"
        value-field="code"
        clearable
        @change-value="val => handLevel(val, rowIndex)"
      />
    </template>
    <template #aggravate="{ row }">
      <vxe-input v-model="row.increase_weight" :min="0" type="float" clearable>
        <template #suffix>
          Kg
        </template>
      </vxe-input>
    </template>
    <template #roll="{ row }">
      <vxe-input v-model="row.piece_count" :min="0" type="float" clearable />
    </template>
    <template #weight="{ row }">
      <vxe-input v-model="row.weight" :min="0" type="float" clearable />
    </template>
    <template #dnf_craft="{ row }">
      <vxe-input v-model="row.dnf_craft" clearable />
    </template>
    <template #delivery_time="{ row }">
      <vxe-input v-model="row.delivery_date" type="date" placeholder="" transfer />
    </template>
    <template #remark="{ row }">
      <vxe-input v-model="row.remark" clearable />
    </template>
    <template #operation="{ rowIndex }">
      <!-- <el-button type="text" @click="handSee(row, rowIndex)">查看</el-button> -->
      <el-button text type="danger" @click="handDelete(rowIndex)">
        删除
      </el-button>
    </template>
    <template #expandShow="{ row }">
      <div v-if="row?.show" style="padding: 30px 140px 30px 140px">
        <ProductFabricDialog ref="ProductFabricDialogRef" v-model="row.use_fabric" :is-dyeing="row.order_type === 1" />
      </div>
    </template>
  </Table>
  <ProductColorDialog ref="productColorDialogRef" @handle-sure="handSelectColor" />
  <!-- <ProductFabricDialog ref="ProductFabricDialogRef" @handle-sure="handAdd"></ProductFabricDialog> -->
  <SelectAdjustmentDialog ref="SelectAdjustmentDialogRef" @handle-sure="handSure" />
</template>
