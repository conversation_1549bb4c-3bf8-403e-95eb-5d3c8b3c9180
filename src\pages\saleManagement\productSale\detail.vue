<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { computed, onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getSaleProductOrder,
  updateSaleProductOrderAuditStatusCancel,
  updateSaleProductOrderAuditStatusPass,
  updateSaleProductOrderAuditStatusReject,
  updateSaleProductOrderAuditStatusWait,
} from '@/api/productSale'
import FildCard from '@/components/FildCard.vue'
import { formatDate, formatHashTag, formatLengthDiv, formatPriceDiv, formatRateDiv, formatUnitPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import Table from '@/components/Table.vue'
import { isMainUnit, orderStatusConfirmBox } from '@/common/util'
import StatusColumn from '@/components/StatusColumn/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import AddressCard from '@/components/AddressCard/index.vue'

const rourte = useRoute()

const { fetchData, data, success, msg } = getSaleProductOrder()
async function getData() {
  await fetchData({ id: rourte.query.id })
  if (!success.value)
    return ElMessage.error(msg.value)
  // data.value.item_data = processDataOut(data.value.item_data)
}
onMounted(() => {
  getData()
})

const tableConfig = ref({
  showSlotNums: true,
  operateWidth: '80',
  fieldApiKey: 'productSaleDetail',
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
  showSpanHeader: true,
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['book_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'book_roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length') as any)}`

      if (['other_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['purchase_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'purchase_roll') as any)}`

      if (['purchase_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'purchase_weight') as any)}`

      if (['purchase_length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'purchase_length') as any)}`

      if (['shortage_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'shortage_roll') as any)}`

      if (['shortage_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'shortage_weight') as any)}`

      if (['shortage_length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'shortage_length') as any)}`

      return null
    }),
  ]
}

const columnList = ref([
  {
    field: 'A-1',
    title: '',
    fixed: 'left',
    childrenList: [
      {
        field: 'product_code',
        title: '成品信息',
        minWidth: 100,
        fixed: 'left',
        soltName: 'product_code',
      },
      {
        field: 'product_color_code',
        title: '颜色色号',
        minWidth: 100,
        fixed: 'left',
        soltName: 'product_color_code',
      },
      // {
      //   field: 'product_name',
      //   title: '成品名称',
      //   minWidth: 100,
      //   fixed: 'left',
      // },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
        fixed: 'left',
      },
      // {
      //   field: 'product_color_name',
      //   title: '颜色',
      //   minWidth: 60,
      //   fixed: 'left',
      // },
    ],
  },
  {
    title: '',
    field: 'A',
    childrenList: [
      {
        field: 'customer_account_num',
        title: '款号',
        minWidth: 100,
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        minWidth: 100,
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        minWidth: 100,
      },
      {
        field: 'product_level_name',
        title: '成品等级',
        minWidth: 100,
      },
      {
        field: 'product_remark',
        title: '成品备注',
        minWidth: 100,
      },
      {
        field: 'stock_remark',
        title: '库存备注',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 70,
      },
    ],
  },
  {
    title: '结算单位',
    field: 'F',
    fixed: 'left',
    childrenList: [
      {
        field: 'auxiliary_unit_name',
        title: '结算单位',
        width: 100,
      },
    ],
  },
  {
    title: '数量单价',
    field: 'B',
    childrenList: [
      {
        field: 'roll',
        title: '匹数',
        width: 90,
        isPrice: true,
      },
      {
        field: 'weight',
        title: '数量',
        width: 100,
        isWeight: true,
      },
      {
        field: 'standard_sale_price',
        title: '销售报价',
        width: 90,
        // isUnitPrice: true,
        soltName: 'standard_sale_price',
      },
      // {
      //   field: 'sale_level_name',
      //   title: '优惠等级',
      //   width: 100,
      // },
      // {
      //   field: 'offset_sale_price',
      //   title: '优惠单价',
      //   width: 100,
      //   isUnitPrice: true,
      // },
      {
        field: 'sale_price',
        title: '销售单价',
        width: 80,
        isUnitPrice: true,
      },
      {
        field: 'weight_error',
        title: '标准空差',
        width: 70,
        isWeight: true,
      },
      {
        field: 'offset_weight_error',
        title: '优惠空差',
        width: 70,
        isWeight: true,
      },
      {
        field: 'adjust_weight_error',
        title: '调整空差',
        width: 70,
        isUnitPrice: true,
      },
      {
        field: 'settle_weight_error',
        title: '结算空差',
        width: 70,
        isWeight: true,
      },
    ],
  },
  {
    title: '辅助数量单价',
    field: 'C',
    childrenList: [
      {
        field: 'length',
        title: '辅助数量',
        width: 80,
        isLength: true,
      },
      {
        field: 'standard_length_cut_sale_price',
        title: '销售报价',
        width: 90,
        soltName: 'standard_length_cut_sale_price',
        // isUnitPrice: true,
      },
      // {
      //   field: 'offset_length_cut_sale_price',
      //   title: '优惠单价',
      //   width: 100,
      //   isUnitPrice: true,
      // },
      {
        field: 'length_cut_sale_price',
        title: '销售单价',
        width: 80,
        isUnitPrice: true,
      },
    ],
  },
  {
    title: '其他金额',
    field: 'D',
    childrenList: [
      {
        field: 'other_price',
        title: '其他金额',
        width: 80,
        isPrice: true,
      },
    ],
  },
  {
    title: '库存信息',
    field: 'E',
    childrenList: [
      {
        field: 'warehouse_name',
        title: '出货仓库',
        width: 100,
      },
      {
        field: 'stock_roll',
        title: '可用匹数',
        width: 75,
        isPrice: true,
      },
      {
        field: 'available_weight',
        title: '可用数量',
        width: 75,
        isWeight: true,
      },
      {
        field: 'book_roll',
        title: '预约匹数',
        width: 75,
        isPrice: true,
      },
    ],
  },
  // {
  //   title: '采购信息',
  //   field: 'G',
  //   childrenList: [
  //     {
  //       field: 'purchase_roll',
  //       title: '匹数',
  //       width: 100,
  //       isPrice: true,
  //     },
  //     {
  //       field: 'purchase_weight',
  //       title: '数量',
  //       width: 100,
  //       isWeight: true,
  //     },
  //     {
  //       field: 'purchase_length',
  //       title: '辅助数量',
  //       width: 100,
  //       isLength: true,
  //     },
  //   ],
  // },
  {
    title: '欠货信息',
    field: 'H',
    childrenList: [
      {
        field: 'shortage_roll',
        title: '匹数',
        width: 75,
        isPrice: true,
      },
      // {
      //   field: 'shortage_weight',
      //   title: '数量',
      //   width: 100,
      //   isWeight: true,
      // },
      // {
      //   field: 'shortage_length',
      //   title: '辅助数量',
      //   width: 100,
      //   isLength: true,
      // },
    ],
  },
  {
    title: '其他信息',
    field: 'Y',
    childrenList: [
      {
        field: 'customer_account_num',
        title: '款号',
        width: 75,
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        width: 75,
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 75,
      },
      {
        field: 'remark',
        title: '备注',
        width: 100,
      },
    ],
  },
])

async function updateStatus(audit_status: number) {
  const id: any = Number(rourte.query.id)
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: updateSaleProductOrderAuditStatusCancel })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: updateSaleProductOrderAuditStatusReject })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: updateSaleProductOrderAuditStatusPass })

  if (audit_status === 1)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: updateSaleProductOrderAuditStatusWait })

  fetchData({ id })
}

// 计算属性处理地址数据
const addressDataProps = computed(() => {
  if (!data.value && !data.value.id)
    return null
  //
  return {
    id: 0,
    location: data.value.receipt_address ? data.value.receipt_address.split(',') : [], // 省市区地址
    address: data.value.receipt_address_detail, // 详细地址
    biz_uint_id: data.value.customer_id, // 客户id
    is_default: false, // 是否默认地址 (没有默认选择)
    logistics_company: data.value.logistics_company, // 物流公司
    logistics_area: data.value.logistics_area, // 物流区域
    contact_name: data.value.contacts, // 联系人名称
    phone: data.value.contact_phone, // 手机号
    print_tag: data.value.print_tag, // 打印打印标签
    name: data.value.process_factory, // 加工厂名称
  }
})
</script>

<template>
  <StatusColumn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    edit_router_name="ProductSaleEdit"
    permission_wait_key="ProductSaleWait"
    permission_reject_key="ProductSaleReject"
    permission_pass_key="ProductSalePass"
    permission_cancel_key="ProductSaleCancel"
    permission_edit_key="ProductSaleEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="客户编号:">
        <template #content>
          {{ data?.customer_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="客户名称:">
        <template #content>
          {{ data?.customer_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售员:">
        <template #content>
          {{ data?.sale_user_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单日期:">
        <template #content>
          {{ formatDate(data?.order_time) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="凭证单号:">
        <template #content>
          {{ data?.voucher_number }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售群体:">
        <template #content>
          {{ data?.sale_group_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="销售跟单:">
        <template #content>
          {{ data?.sale_follower_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="结算类型:">
        <template #content>
          {{ data?.settle_type_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="邮费项目:">
        <template #content>
          {{ data?.postage_items_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="是否含税:">
        <template #content>
          {{ data?.is_with_tax_rate ? "是" : "否" }}
          <span v-if="data.is_with_tax_rate">，税率{{ formatRateDiv(data?.sale_tax_rate) }}%</span>
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem v-if="data.is_with_tax_rate" label="含税项目:">
        <template #content>
          {{ data?.info_sale_taxable_item_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货备注:" copies="2">
        <template #content>
          {{ data?.send_product_remark }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货标签:">
        <template #content>
          {{ data?.print_tag }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="内部备注:" copies="2">
        <template #content>
          {{ data?.internal_remark }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单要求:">
        <template #content>
          <div class="flex disabled-checkbox-defalut">
            <el-checkbox v-model="data.pick_up_goods_in_order" label="齐单提货" disabled size="small" />
            <el-checkbox v-model="data.same_color_same_dyelot" label="同色同缸" disabled size="small" />
          </div>
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单类型:">
        <template #content>
          {{ data?.sale_mode_name }}
        </template>
      </DescriptionsFormItem>
    </div>
    <div class="m-[10px]">
      <AddressCard
        v-if="data.id"
        type="Detail"
        :sale-shipment-type-code="data.sale_shipment_type_code"
        :sale-system-ids="data.sale_system_id"
        :customer-ids="data.customer_id"
        :customer-name="data.customer_name"
        :address-data="addressDataProps"
      />
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table :config="tableConfig" :table-list="data.item_data" :column-list="columnList">
      <template #product_code="{ row }">
        {{ formatHashTag(row.product_code, row.product_name) }}
      </template>
      <template #product_color_code="{ row }">
        {{ formatHashTag(row.product_color_code, row.product_color_name) }}
      </template>
      <template #customer_account_num="{ row }">
        {{ row.customer_account_num }}
      </template>
      <template #roll="{ row }">
        {{ row.roll }}
      </template>
      <template #weight="{ row }">
        {{ row.weight }}
      </template>
      <template #length="{ row }">
        {{ row.length }}
      </template>
      <template #other_price="{ row }">
        {{ row.other_price }}
      </template>
      <!--      预约匹数 -->
      <template #book_roll="{ row }">
        {{ row.book_roll }}
      </template>
      <template #purchase_roll="{ row }">
        {{ row.purchase_roll }}
      </template>
      <template #purchase_weight="{ row }">
        {{ row.purchase_weight }}
      </template>
      <template #purchase_length="{ row }">
        {{ row.purchase_length }}
      </template>
      <template #shortage_roll="{ row }">
        {{ row.shortage_roll }}
      </template>
      <template #adjust_weight_error="{ row }">
        {{ row.adjust_weight_error }}
      </template>
      <template #shortage_weight="{ row }">
        {{ row.shortage_weight }}
      </template>
      <template #shortage_length="{ row }">
        {{ row.shortage_length }}
      </template>
      <template #remark="{ row }">
        {{ row.remark }}
      </template>
      <!-- 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        {{ row.auxiliary_unit_name }}
      </template>
      <!-- 数量-销售单价 -->
      <template #sale_price="{ row }">
        {{ row.sale_price }}
      </template>
      <!--      数量 上次价 -->
      <template #quantity_price="{ row }">
        <span>{{
          row.quantity_price !== "" ? row.quantity_price : "-"
        }}</span>
      </template>
      <!--      数量 价格浮动 -->
      <template #quantity_price_fluctuation>
        <span>-</span>
      </template>
      <!--      辅助数量 上次价 -->
      <template #length_price="{ row }">
        <span>{{
          row.length_price !== "" ? row.length_price : "-"
        }}</span>
      </template>
      <!--      辅助数量 价格浮动 -->
      <template #length_price_fluctuatio>
        <span>-</span>
      </template>
      <template #length_cut_sale_price="{ row }">
        {{ row.length_cut_sale_price }}
      </template>
      <template #standard_sale_price="{ row }">
        {{ formatUnitPriceDiv(row.standard_sale_price) }}
        {{ row.measurement_unit_name }}/元
      </template>
      <template #standard_length_cut_sale_price="{ row }">
        {{ formatUnitPriceDiv(row.standard_length_cut_sale_price) }}
        <span v-if="!isMainUnit(row)">{{ row.auxiliary_unit_name }}/元</span>
      </template>
    </Table>
  </FildCard>
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
