import { useRequest } from '@/use/useRequest'

// 获取菜单列表
export const GetMenuListApi = () => {
  return useRequest({
    url: '/admin/v1/menu/getMenuList',
    method: 'get',
  })
}

// 添加菜单
export const AddMenuListApi = () => {
  return useRequest({
    url: '/admin/v1/menu/addMenu',
    method: 'post',
  })
}

// 更新菜单
export const UpdateMenuApi = () => {
  return useRequest({
    url: '/admin/v1/menu/updateMenu',
    method: 'put',
  })
}

// 删除菜单
export const DelMenuApi = () => {
  return useRequest({
    url: '/admin/v1/menu/deleteMenu',
    method: 'delete',
  })
}

// 更新菜单状态
export const UpdateMenuStatusApi = () => {
  return useRequest({
    url: '/admin/v1/menu/updateMenuStatus',
    method: 'put',
  })
}

// 获取菜单列表（包含资源树）
export const GetMenuResourceListApi = () => {
  return useRequest({
    url: '/admin/v1/menu/getMenuResourceList',
    method: 'get',
  })
}

/**
 * 获取菜单列表，不分账套
 */
export const GetMenus = () => {
  return useRequest({
    url: '/admin/v1/getMenus',
    method: 'get',
  })
}
