import { useRequest } from '@/use/useRequest'

// 获取列表
export const getInfoDyeingFinishingProcessDataList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/getInfoDyeingFinishingProcessDataList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加数据
export const addInfoDyeingFinishingProcessData = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/addInfoDyeingFinishingProcessData',
    method: 'post',
  })
}

// 修改数据

export const updateInfoDyeingFinishingProcessData = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/updateInfoDyeingFinishingProcessData',
    method: 'put',
  })
}

// 删除数据

export const deleteInfoDyeingFinishingProcessData = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/deleteInfoDyeingFinishingProcessData',
    method: 'delete',
  })
}

// 更新状态

export const updateInfoDyeingFinishingProcessDataStatus = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessData/updateInfoDyeingFinishingProcessDataStatus',
    method: 'put',
  })
}

// 删除染整工艺分类

export const deleteInfoDyeingFinishingProcessDataType = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoDyeingFinishingProcessDataType/deleteInfoDyeingFinishingProcessDataType',
    method: 'delete',
  })
}
