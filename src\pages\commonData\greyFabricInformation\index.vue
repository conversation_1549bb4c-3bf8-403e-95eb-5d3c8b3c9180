<script setup lang="ts" name="GreyFabricInformation">
import { Delete, Document, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  deleteGreyFabricInfo,
  getGreyFabricInfoDetail,
  getGreyFabricInfoList,
  updateGreyFabricInfoStatus,
} from '@/api/greyFabricInformation'
import {
  debounce,
  deepClone,
  deleteRemark,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import Accordion from '@/components/Accordion/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import ProductCard from '@/components/ListCard/index.vue'
import ProductItem from '@/components/ListCard/item.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SideTree from '@/components/SideTree.vue'
import Table from '@/components/Table.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import CoverImage from '@/components/UploadFile/CoverImage/index.vue'
import SelectCascader from '@/components/SelectCascader/productType.vue'

const state = reactive<any>({
  tableData: [],
  showType: 'list' as 'list' | 'card',
  filterData: {
    name: '',
    code: '',
    status: '',
    type_grey_fabric_id: [],
  },
  multipleSelection: [],
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getGreyFabricInfoList()

const SideTreeRef = ref()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.type_grey_fabric_id.length)
    obj.type_grey_fabric_id = obj.type_grey_fabric_id.join(',')
  else
    SideTreeRef.value.resetChecked()

  const query = {
    ...state.filterData,
    type_grey_fabric_id: obj.type_grey_fabric_id,
  }
  await ApiCustomerList(getFilterData(query))
  if (data.value?.list)
    handleCellClick({ row: data.value.list[0] })

  state.multipleSelection = []
}, 400)

onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})
const { fetchData: getGreyFetch, data: detailGreyData }
  = getGreyFabricInfoDetail()
function handleCellClick({ row }: any) {
  getGreyFetch({ id: row.id })
}

const tableConfig = ref({
  fieldApiKey: 'GreyFabricInformation2',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  height: 'auto',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => handleCellClick(val),
})

const tableConfig2 = ref({
  loading,
  operateWidth: '230',
  showSort: false,
  height: '100%',
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

// const AddDialogRef = ref()

const columnList = ref([
  {
    sortable: true,
    title: '图片',
    field: 'image_code',
    fixed: 'left',
    soltName: 'image_code',
    minWidth: 100,
  },
  {
    field: 'code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'full_name',
    title: '坯布全称',
    fixed: 'left',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'type_grey_fabric_name',
    title: '布种类型',
    fixed: 'left',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'unit_name',
    title: '单位',
    fixed: 'left',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'type_grey_fabric_order_name',
    title: '坯布订单类型',
    fixed: 'left',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'grey_fabric_composition',
    title: '坯布成分',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
    soltName: 'grey_fabric_width',
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    width: '120',
  },
  {
    sortable: true,
    field: 'yarn_count',
    title: '纱支',
    width: '120',
  },
  {
    sortable: true,
    field: 'density',
    title: '密度',
    width: '120',
  },
  {
    sortable: true,
    field: 'weaving_organization_name',
    title: '织造组织',
    width: '120',
  },
  {
    field: 'shrinkage_warp',
    title: '缩水率',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
    soltName: 'grey_fabric_gram_weight',
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
    sortable: true,
    soltName: 'finish_product_width',
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
    sortable: true,
    soltName: 'finish_product_gram_weight',
  },
  // {
  //   field: 'remark',
  //   title: '备注',
  //   minWidth: 100,
  // },
  {
    field: 'create_time',
    title: '创建时间',
    sortable: true,
    isDate: true,
    minWidth: 100,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'status',
    title: '状态',
    soltName: 'status',
    width: '5%',
    showStatus: true,
    fixed: 'right',
    sortable: true,
  },
])

const columnList2 = ref([
  {
    field: 'code',
    title: '原料编号',
  },
  {
    field: 'name',
    title: '原料名称',
  },
  {
    field: 'brand',
    title: '原料品牌',
  },
  {
    field: 'batch_num',
    title: '原料批号',
  },
  {
    field: 'raw_material_color_code',
    title: '原料色号',
  },
  {
    field: 'raw_material_color_name',
    title: '原料颜色',
  },
  {
    field: 'material_ratio',
    title: '用料比例（%）',
    isPrice: true,
  },
  {
    field: 'material_loss',
    title: '用料损耗（%）',
    isPrice: true,
  },
])
// eslint-disable-next-line unused-imports/no-unused-vars
function handShowSort() {
  tableConfig.value.showSort = true
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'GreyFabricInformationAdd',
  })
}

function handEdit(row: any) {
  router.push({
    name: 'GreyFabricInformationEdit',
    query: {
      id: row.id,
    },
  })
}

function handDetail(row: any) {
  router.push({
    name: 'GreyFabricInformationDetail',
    query: {
      id: row.id,
    },
  })
}

// 删除数据
const {
  fetchData: deleteFetch,
  success: deleteSuccess,
  msg: deleteMsg,
} = deleteGreyFabricInfo()

async function handDelete(row: any) {
  const res = await deleteRemark()
  await deleteFetch({ id: row.id.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteRemark()
  const ids: any[] = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  await deleteFetch({ id: ids.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

// 编辑状态
const {
  fetchData: statusFetch,
  msg: StatusMsg,
  success: StatusSuccess,
} = updateGreyFabricInfoStatus()

async function handStatus(row: any) {
  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    await statusFetch({
      id: row.id.toString(),
      status: row.status === 1 ? 2 : 1,
    })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 批量修改状态
async function handAll(val: number) {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    await statusFetch({ id: ids.toString(), status: val === 1 ? 1 : 2 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

function changeShowType() {
  state.showType = state.showType === 'list' ? 'card' : 'list'
}

const tablesRef = ref()
function exportSelectEvent() {
  tablesRef.value.exportSelectEvent()
}

const pageConfig = ref({
  page,
  size,
  total,
  handleSizeChange,
  handleCurrentChange,
})

function cardClick(row: any) {
  router.push({
    name: 'GreyFabricInformationDetail',
    query: {
      id: row.id,
    },
  })
}
function handleClick() {
  router.push({
    name: 'ClothType',
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="布种类型:">
          <template #content>
            <SelectCascader @change-value="({ ids }) => state.filterData.type_grey_fabric_id = ids?.[ids.length - 1] || ''" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.code" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              api="StatusListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="全文本筛选:">
          <template #content>
            <el-input v-model="state.filterData.field_search" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-1 overflow-hidden">
      <Accordion :open-status="true">
        <SideTree
          ref="SideTreeRef"
          v-model="state.filterData.type_grey_fabric_id"
          api="getTreeEnumList"
          type="checkbox"
          check-strictly
          highlight-current
          default-expand-all
          @select="() => (state.filterData.type_grey_fabric_id = [])"
        >
          <template #empty>
            <div class="empty-text">
              暂无布料类型，<el-link type="primary" :underline="false" @click="handleClick">
                前往新增>
              </el-link>
            </div>
          </template>
          <template #footer>
            <el-link type="primary" :underline="false" @click="handleClick">
              补充布料类型>
            </el-link>
          </template>
        </SideTree>
      </Accordion>
      <div class="list-page flex-1">
        <FildCard
          title=""
          :class="`table-card-full ${state.showType === 'list' ? '' : '!overflow-y-scroll'}`"
          tool-bar
        >
          <template #right-top>
            <el-button
              v-has="'GreyFabricInformation_add'"
              style="margin-left: 10px"
              type="primary"
              :icon="Plus"
              @click="handleAdd"
            >
              新建
            </el-button>
            <el-button style="margin-right: 10px" @click="changeShowType">
              {{
                state?.showType === "card" ? "切换为列表" : "切换为卡片"
              }}
            </el-button>
            <!-- <el-tooltip class="box-item" effect="dark" content="点击出现排序按钮" placement="top-start">
          <el-button @click="handShowSort" style="margin-right: 10px">排序</el-button>
        </el-tooltip> -->
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button class="mr-[10px]">批量操作</el-button>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <div v-has="'GreyFabricInformation_del'">
                    <el-dropdown-item @click="handAllDelete">
                      批量删除
                    </el-dropdown-item>
                  </div>
                  <div v-has="'GreyFabricInformation_status'">
                    <el-dropdown-item @click="handAll(1)">
                      批量启用
                    </el-dropdown-item>
                  </div>
                  <div v-has="'GreyFabricInformation_status'">
                    <el-dropdown-item @click="handAll(2)">
                      批量禁用
                    </el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <PrintPopoverBtn
              :print-type="PrintType.PrintTemplateTypeLabel"
              :data-type="PrintDataType.Grey"
              :list="state.multipleSelection"
            />
            <!--        <PrintBtn -->
            <!--          type="greyFabricinformation" -->
            <!--          :tid="1656287774744832" -->
            <!--          :list="state.multipleSelection" -->
            <!--        /> -->
            <el-button
              style="margin-left: 10px"
              text
              link
              type="info"
              :icon="Document"
              @click="exportSelectEvent"
            >
              导出文件
            </el-button>
          </template>
          <template v-if="state.showType === 'list'">
            <Table
              ref="tablesRef"
              :config="tableConfig"
              :table-list="data?.list"
              :column-list="columnList"
            >
              <template #image_code="{ row }">
                <CoverImage :file-url="row.main_texture_url" />
              </template>
              <template #status="{ row }">
                <div class="flex items-center">
                  <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
                  <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
                    {{ row?.status === 1 ? "启用" : "禁用" }}
                  </div>
                </div>
              </template>
              <template #grey_fabric_width="{ row }">
                {{ row?.grey_fabric_width
                }}{{ row?.grey_fabric_width_unit_name }}
              </template>
              <template #grey_fabric_gram_weight="{ row }">
                {{ row?.grey_fabric_gram_weight
                }}{{ row?.grey_fabric_gram_weight_unit_name }}
              </template>
              <template #finish_product_width="{ row }">
                {{ row?.finish_product_width
                }}{{ row?.finish_product_width_unit_name }}
              </template>
              <template #finish_product_gram_weight="{ row }">
                {{ row?.grey_fabric_width
                }}{{ row?.finish_product_gram_weight_unit_name }}
              </template>

              <template #operate="{ row }">
                <el-space :size="10">
                  <el-link :underline="false" type="primary" @click="handDetail(row)">
                    查看
                  </el-link>

                  <el-link
                    v-has="'GreyFabricInformation_edit'"
                    :underline="false"
                    type="primary"
                    @click="handEdit(row)"
                  >
                    编辑
                  </el-link>
                  <el-link
                    v-has="'GreyFabricInformation_del'"
                    :underline="false"
                    type="primary"
                    @click="handDelete(row)"
                  >
                    删除
                  </el-link>
                  <el-link
                    v-has="'GreyFabricInformation_status'"
                    :underline="false"
                    type="primary"
                    @click="handStatus(row)"
                  >
                    {{ row?.status === 1 ? "禁用" : "启用" }}
                  </el-link>
                </el-space>
              </template>
            </Table>
          </template>
          <template v-else>
            <ProductCard
              :list="data?.list"
              :page-config="pageConfig"
              @click="cardClick"
            >
              <template #header="{ row }">
                <div>{{ row?.code }}</div>
                <div>{{ row?.name }}</div>
              </template>
              <template #body="{ row }">
                <ProductItem label="坯布幅宽">
                  {{
                    row?.grey_fabric_width
                  }}
                </ProductItem>
                <ProductItem label="坯布克重">
                  {{
                    row?.grey_fabric_gram_weight
                  }}
                </ProductItem>
                <ProductItem label="织造组织">
                  {{
                    row?.weaving_organization_name
                  }}
                </ProductItem>
                <ProductItem label="纱支">
                  {{ row?.yarn_count }}
                </ProductItem>
                <ProductItem label="密度">
                  {{ row?.density }}
                </ProductItem>
                <ProductItem label="坯布成分" self>
                  {{
                    row?.grey_fabric_composition
                  }}
                </ProductItem>
              </template>
            </ProductCard>
          </template>
        </FildCard>
        <FildCard :tool-bar="false" class="table-card-bottom">
          <div class="flex my-[10px] text-[13px]">
            <div class="flex mr-[20px]">
              <div class="mr-[10px] font-bold">
                布种类型:
              </div>
              <div>{{ detailGreyData?.type_grey_fabric_name }}</div>
            </div>
            <div class="flex mr-[20px]">
              <div class="mr-[10px] font-bold">
                坯布编号:
              </div>
              <div>{{ detailGreyData.code }}</div>
            </div>
            <div class="flex mr-[20px]">
              <div class="mr-[10px] font-bold">
                坯布名称:
              </div>
              <div>{{ detailGreyData.name }}</div>
            </div>
          </div>

          <Table
            :config="tableConfig2"
            :table-list="detailGreyData?.raw_material_info"
            :column-list="columnList2"
          />
        </FildCard>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}
:deep(.vxe-cell){
  display: flex;
  align-items: center;
}
.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
