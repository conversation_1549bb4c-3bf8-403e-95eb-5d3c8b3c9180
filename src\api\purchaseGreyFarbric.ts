import { useRequest } from '@/use/useRequest'

// 添加坯布
export function GetPhysicalWarehouseList() {
  return useRequest({
    url: '/admin/v1/purchase/purchaseGreyFarbric/addPurchaseGreyFarbric',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 往来单位
export function GetBusinessUnitListApi() {
  return useRequest({
    url: '/admin/v1/business_unit/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
