import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const getShortageProductOrderList = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/getShortageProductOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getShortageProductOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/sale/shortageProductOrder/getShortageProductOrderList',
    method: 'get',
    nameFile,
  })
}

// 新增数据
export const addShortageProductOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/addShortageProductOrder',
    method: 'post',
  })
}

// 更新数据
export const updateSaleProductOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/updateShortageProductOrder',
    method: 'put',
  })
}

// 详情
export const getShortageProductOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/getShortageProductOrder',
    method: 'get',
  })
}

// 审核
export const updateShortageProductOrderAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/updateShortageProductOrderAuditStatusPass',
    method: 'put',
  })
}

// 消审
export const updateShortageProductOrderAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/updateShortageProductOrderAuditStatusWait',
    method: 'put',
  })
}

// 作废
export const updateShortageProductOrderAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/updateShortageProductOrderAuditStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateShortageProductOrderAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/updateShortageProductOrderAuditStatusReject',
    method: 'put',
  })
}

// 获取成品库存汇总下拉列表
export const getStockProductDropdownList = () => {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
