<script setup lang="ts">
import { reactive, ref } from 'vue'

const emits = defineEmits(['handleSure'])

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^(?:(?:\+|00)86)?1\d{10}$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}

const state = reactive({
  showModal: false,
  modalName: '',
  form: {
    factoryName: '',
    name: '',
    userPhone: '',
    userAddress: '',
    addressDetail: '',
    isDefault: false,
    tagName: '',
    userName: '',
    id: -1,
  },
  fromRules: {
    factoryName: [{ required: true, message: '请填写加工厂名称', trigger: 'blur' }],
    userName: [{ required: true, message: '请填写联系人名称', trigger: 'blur' }],
    userPhone: [{ required: true, trigger: 'blur', validator: checkPhone }],
    userAddress: [{ required: true, message: '请填写地图定位', trigger: 'blur' }],
    addressDetail: [{ required: true, message: '请填写详细地址', trigger: 'blur' }],
  },
})

function handCancel() {
  state.showModal = false
}

const ruleFormRef = ref()
async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: any) => {
    if (valid)
      emits('handleSure', state.form)
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="900" height="600" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" size="default" :model="state.form" label-width="140px" label-position="left" :rules="state.fromRules">
      <el-form-item label="加工厂名称" prop="factoryName">
        <el-input v-model="state.form.factoryName" />
      </el-form-item>
      <el-form-item label="联系人名称" prop="userName">
        <el-input v-model="state.form.userName" />
      </el-form-item>
      <el-form-item label="联系人电话" prop="userPhone">
        <el-input v-model="state.form.userPhone" type="number" maxlength="11" />
      </el-form-item>
      <el-form-item label="地图定位" prop="userAddress">
        <el-input v-model="state.form.userAddress" />
      </el-form-item>
      <el-form-item label="详细地址" prop="addressDetail">
        <el-input v-model="state.form.addressDetail" />
      </el-form-item>
      <el-form-item label="是否设置为默认地址" prop="isDefault">
        <el-switch v-model="state.form.isDefault" />
      </el-form-item>
      <el-form-item label="打印标签" prop="tagName">
        <el-input v-model="state.form.tagName" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
</style>
