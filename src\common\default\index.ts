import { isDefined, isEmpty } from 'arcdash'

const USERKEY = 'user'
const CUSTOMERIDKEY = 'default_customer_id'
const CUSTOMERNAMEKEY = 'default_customer_name'

interface Customer {
  customer_id: number | string
  customer_name?: string
}

function userEmpty() {
  console.error('本地储存里没找到有user')
  return {
    customerId: '',
    customerName: '',
  }
}

export function getDefaultCustomer(customer_id: number): Customer {
  if (isDefined(customer_id) && customer_id !== 0) {
    return {
      customer_id,
    }
  }

  const user = JSON.parse(localStorage.getItem(USERKEY) || '{}')

  if (isEmpty(user)) {
    return userEmpty()
  }
  const userInfo = user?.user

  if (isEmpty(userInfo)) {
    return userEmpty()
  }

  if (isEmpty(userInfo[CUSTOMERIDKEY])) {
    return userEmpty()
  }

  return {
    customer_id: userInfo?.[CUSTOMERIDKEY],
    customer_name: userInfo?.[CUSTOMERNAMEKEY],
  }
}

export function setDefaultCustomer(customer: Customer) {
  const user = JSON.parse(localStorage.getItem(USERKEY) || '{}')

  if (isEmpty(user) || isEmpty(user?.user)) {
    return
  }

  user.user[CUSTOMERIDKEY] = customer.customer_id
  user.user[CUSTOMERNAMEKEY] = customer.customer_name

  localStorage.setItem(USERKEY, JSON.stringify(user))
}
