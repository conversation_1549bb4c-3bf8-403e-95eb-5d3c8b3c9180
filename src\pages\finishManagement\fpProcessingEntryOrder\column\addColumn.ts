export const AddColumn = [
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    minWidth: 150,
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
    required: true,
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
    required: true,
  },
  {
    field: 'quote_order_no',
    soltName: 'quote_order_no',
    title: '染整单号',
    minWidth: 170,
    fixed: 'left',
  },
  {
    field: 'quote_order_type_name',
    soltName: 'quote_order_type_name',
    title: '单据类型',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    soltName: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    soltName: 'product_color_code',
    title: '色号',
    minWidth: 100,
    required: true,
  },
  {
    field: 'product_color_id',
    soltName: 'product_color_id',
    title: '颜色',
    minWidth: 100,
    required: true,
  },
  {
    field: 'dye_factory_color_code',
    soltName: 'dye_factory_color_code',
    title: '染厂色号',
    minWidth: 100,
  },
  {
    field: 'dye_factory_dyelot_number',
    soltName: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    minWidth: 100,
    required: true,
  },
  {
    field: 'product_width',
    soltName: 'product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'product_gram_weight',
    soltName: 'product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'product_level_id',
    soltName: 'product_level_id',
    title: '成品等级',
    minWidth: 100,
  },
  {
    field: 'product_remark',
    soltName: 'product_remark',
    title: '成品备注',
    minWidth: 100,
  },
  {
    field: 'product_craft',
    title: '成品工艺',
    minWidth: 100,
  },
  {
    field: 'product_ingredient',
    title: '成品成分',
    minWidth: 100,
  },
  {
    field: 'wait_deducted_roll',
    title: '未返匹数',
    minWidth: 100,
  },
  {
    field: 'able_use_gf_roll',
    title: '可分配匹数',
    minWidth: 100,
  },
  {
    field: 'able_use_gf_weight',
    title: '可分配数量',
    minWidth: 100,
  },
  {
    field: 'in_roll',
    soltName: 'in_roll',
    title: '进仓匹数',
    minWidth: 100,
    required: true,
  },
  {
    field: 'customer_account_num',
    soltName: 'customer_account_num',
    title: '款号',
    minWidth: 100,
  },
  {
    field: 'contract_number',
    soltName: 'contract_number',
    title: '合同号',
    minWidth: 100,
  },
  {
    field: 'voucher_number',
    soltName: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    field: 'dye_delivery_order_no',
    soltName: 'dye_delivery_order_no',
    title: '染厂送货单号',
    minWidth: 100,
  },
  {
    field: 'use_gf_roll',
    soltName: 'use_gf_roll',
    title: '用坯匹数',
    minWidth: 100,
    required: true,
  },
  {
    field: 'use_gf_weight',
    soltName: 'use_gf_weight',
    title: '用坯数量',
    minWidth: 100,
    required: true,
  },
  {
    field: 'in_weight',
    title: '进仓数量',
    minWidth: 100,
    required: true,
  },
  {
    field: 'weight_error',
    title: '空差',
    minWidth: 100,
  },
  {
    field: 'settle_weight',
    title: '结算数量',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'dye_delivery_order_weight',
    soltName: 'dye_delivery_order_weight',
    title: '染厂单据数量',
    minWidth: 100,
  },
  {
    field: 'in_length',
    soltName: 'in_length',
    title: '进仓辅助数量',
    minWidth: 100,
  },
  {
    field: 'remark',
    soltName: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'xima',
    soltName: 'xima',
    title: '细码',
    fixed: 'right',
    width: 120,
  },
  {
    fixed: 'right',
    field: 'operate',
    soltName: 'operate',
    title: '操作',
    width: 100,
  },
]
