import { ref } from 'vue'
import { throttle } from 'lodash-es'
import { SearchForSomeSupplierField } from '@/api/search'

/**
 * 邮箱验证函数
 */
export function checkEmail(rule: any, value: any, callback: any) {
  const reg = /^[A-Za-z0-9\u4E00-\u9FA5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  if (reg.test(value) || value === '')
    callback()
  else
    callback(new Error('请输入正确的邮箱地址'))
}

/**
 * 数字验证函数
 */
export function validateNumber(rule: any, value: any, callback: any) {
  if (value <= 0)
    callback(new Error('请输入大于0数字'))
  else
    callback()
}

/**
 * 供应商编号搜索逻辑
 */
export function useSupplierCodeSearch() {
  const { fetchData: searchCodes, data: searchedData } = SearchForSomeSupplierField()
  const filterDatas = ref([])

  const getSearchedCodes = (val: string) => {
    // 节流
    throttle(async () => {
      await searchCodes({
        category: 1,
        code: val,
      })
      filterDatas.value = searchedData.value.list
    }, 500)()
  }

  const selectConfirm = (e: any) => {
    const value = e.target.value
    return value
  }

  return {
    filterDatas,
    getSearchedCodes,
    selectConfirm,
  }
}

/**
 * 供应商表单的基础配置
 */
export function getSupplierFormRules() {
  return {
    supplerName: [{ required: true, message: '请填写供应商名称', trigger: 'blur' }],
    address: [{ required: true, message: '请填写地址', trigger: 'blur' }],
    supplerType: [{ required: true, message: '请选择供应商类型', trigger: 'blur' }],
    marketingSystem: [{ required: true, message: '请选择所属营销体系', trigger: 'blur' }],
    userPhone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
    contact_name: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
    userEmail: [{ trigger: 'blur', validator: checkEmail }],
  }
}

/**
 * 供应商表单的初始数据
 */
export function getInitialSupplierForm() {
  return {
    supplerName: '',
    supplerAllName: '',
    supplerNums: '',
    supplerType: [],
    remark: '',
    marketingSystem: '', // 所属营销体系
    userPhone: '',
    fax_number: '',
    address: '',
    saleUser: '',
    merchandiser: '', // 跟单员
    order_qc_user_id: '',
    saleArea: '',
    salePopulation: '', // 销售群体
    settlementType: 1, // 默认结算类型
    settlementDay: '', // 默认结算天数：
    creditLimit: '', // 信用额度：
    credit_level: '', // 信用等级
    userEmail: '',
    settle_type: '',
    settle_cycle: '',
    dnf_charging_method: 1,
    contact_name: '',
    // 织造配置字段
    fabric_prefix: '', // 织厂布飞前缀
    weighing_inspection_process: '', // 称重验布流程
    weaving_storage_method: '', // 织造入库方式
    fabric_roll_number_rule: '', // 布飞卷号规则
  }
}

/**
 * 将表单数据转换为API请求格式
 */
export function transformFormToApiRequest(formData: any, wechatContacts: any[], boundQyGroups: any[]): Api.Supplier.Request {
  const baseData: Api.Supplier.Request = {
    address: formData.address,
    fax_number: formData.fax_number,
    code: formData.supplerNums,
    credit_level: formData.credit_level,
    credit_limit: formData.creditLimit,
    email: formData.userEmail,
    full_name: formData.supplerAllName,
    name: formData.supplerName,
    order_follower_id: formData.merchandiser || undefined,
    order_qc_user_id: formData.order_qc_user_id || undefined,
    phone: formData.userPhone,
    contact_name: formData.contact_name,
    remark: formData.remark,
    sale_area_id: formData.saleArea || undefined,
    sale_group_id: formData.salePopulation || undefined,
    sale_system_ids: formData.marketingSystem || undefined,
    seller_id: formData.saleUser || undefined,
    settle_cycle: formData.settle_cycle ? Number(formData.settle_cycle) : undefined,
    settle_type: formData.settle_type ? Number(formData.settle_type) : undefined,
    unit_type_id: formData.supplerType || undefined,
    main_unit_type_id: formData.supplerType?.[0] || undefined,
    dnf_charging_method: formData.dnf_charging_method ? Number(formData.dnf_charging_method) : undefined,
    // 织造配置字段 - 根据API类型定义映射
    bf_prefix: formData.fabric_prefix, // 布飞前缀
    wfi_process: formData.weighing_inspection_process, // 称重验布流程
    weaving_storage_method: formData.weaving_storage_method, // 织造入库方式
    bf_sequence_number_rule: formData.fabric_roll_number_rule, // 布飞卷号规则
    qywx_customers: wechatContacts.map(item => ({
      id: item.id,
      name: item.name,
      type: item.type,
    })),
    qywx_groups: boundQyGroups.map(item => ({
      id: item.id,
      name: item.name,
      notify_type: item.notify_type,
      notify_type_name: item.notify_type_name,
    })),
  }

  return baseData
}

/**
 * 将API响应数据转换为表单格式
 */
export function transformApiResponseToForm(apiData: any) {
  return {
    supplerName: apiData?.name || '',
    supplerAllName: apiData?.full_name || '',
    supplerNums: apiData?.code || '',
    supplerType: apiData?.unit_type_id || [],
    remark: apiData?.remark || '',
    marketingSystem: apiData?.sale_system_ids || '',
    userPhone: apiData?.phone || '',
    fax_number: apiData?.fax_number || '',
    address: apiData?.address || '',
    saleUser: apiData?.seller_id || '',
    merchandiser: apiData?.order_follower_id || '',
    order_qc_user_id: apiData?.order_qc_user_id || '',
    saleArea: apiData?.sale_area_id || '',
    salePopulation: apiData?.sale_group_id || '',
    settle_type: apiData?.settle_type || '',
    settle_cycle: apiData?.settle_cycle || '',
    creditLimit: apiData?.credit_limit || '',
    credit_level: apiData?.credit_level || '',
    userEmail: apiData?.email || '',
    dnf_charging_method: apiData?.dnf_charging_method || 1,
    contact_name: apiData?.contact_name || '',
    // 织造配置字段 - 从API字段映射回表单字段
    fabric_prefix: apiData?.bf_prefix || '',
    weighing_inspection_process: apiData?.wfi_process || '',
    weaving_storage_method: apiData?.weaving_storage_method || '',
    fabric_roll_number_rule: apiData?.bf_sequence_number_rule || '',
  }
}
