<script setup lang="ts">
import {
  computed,
  h,
  nextTick,
  reactive,
  ref,
  watch,
} from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { VxeInput } from 'vxe-pc-ui'
import currency from 'currency.js'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'
import GridTable from '@/components/GridTable/index.vue'
import type { Columns } from '@/components/GridTable'

const emits = defineEmits(['handleSure'])

const TablesRef = ref()
const form_options = [
  {
    text: '成品编号',
    key: 'product_code',
  },
  {
    text: '成品名称',
    key: 'product_name',
  },
  {
    text: '颜色',
    key: 'product_color_name',
  },
  {
    text: '染厂缸号',
    key: 'dye_factory_dyelot_number',
  },
]
const list = ref<any[]>([])

const state = reactive<any>({
  baseDataForm: {},
  showModal: false,
  modalName: '细码变更',
  multipleSelection: [],
  isEdit: true,
})

function handleSelectAllList(checked: boolean) {
  if (checked)
    state.multipleSelection = list.value
  else
    state.multipleSelection = []
}
const loading = ref(false)

function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['roll', 'change_roll', 'change_weight', 'change_length', 'change_actually_weight', 'actually_weight', 'change_settle_weight', 'base_unit_weight', 'weight', 'settle_weight', 'paper_tube_weight', 'weight_error', 'settle_error_weight'].includes(column.field))
        return sumNum(data, column.field, '')

      return null
    }),
  ]
  return footerData
}
function footerCellClassName(row: any) {
  if (['change_roll', 'change_weight', 'change_length'].includes(row.column.field)) {
    const sum = row.data[row.$rowIndex][row.$columnIndex]
    if (currency(sum).value === 0)
      return
    return currency(sum).value > 0 ? 'col-red' : 'col-green'
  }
}

watch(
  () => list.value,
  () => {
    nextTick(() => {
      TablesRef.value?.TableRef?.updateFooter()
    })
  },
  { deep: true },
)
watch(
  () => state.showModal,
  (newVal) => {
    if (newVal)
      getColumn()
  },
  { deep: true },
)

const { setFieldFocusOnGridTable } = useTableEnterAutoFocus(
  TablesRef,
  list,
)
const columnList = ref<Columns>()

function getColumn() {
  columnList.value = [
    {
      field: 'change_after',
      title: '变更信息',
      children: [
        {
          field: 'change_roll',
          title: '匹数',
          minWidth: 100,
          editRender: state.isEdit ? { autofocus: '.vxe-input--inner' } : undefined,
          slots: {
            default: ({ row }: any) => {
              return row.change_roll
            },
            edit: ({ row, rowIndex }: any) => {
              return h(VxeInput, {
                type: 'float',
                id: `roll${rowIndex}`,
                onKeydown: $event =>
                  setFieldFocusOnGridTable(rowIndex, $event, 'change_roll'),
                modelValue: row.change_roll,
                onInput: (val) => {
                  row.change_roll = val.value
                },
              })
            },
          },
        },
        {
          field: 'change_weight',
          title: '基本单位数量',
          minWidth: 150,
          editRender: state.isEdit ? { autofocus: '.vxe-input--inner' } : undefined,
          slots: {
            default: ({ row }: any) => {
              return row.change_weight
            },
            edit: ({ row, rowIndex }: any) => {
              return h(VxeInput, {
                type: 'float',
                id: `roll${rowIndex}`,
                onKeydown: $event =>
                  setFieldFocusOnGridTable(rowIndex, $event, 'change_weight'),
                modelValue: row.change_weight,
                onInput: (val) => {
                  row.change_weight = val.value
                  row = computedKeys(row)
                },
              })
            },
          },
        },
        {
          field: 'change_length',
          title: '辅助数量',
          minWidth: 100,
          editRender: state.isEdit ? { autofocus: '.vxe-input--inner' } : undefined,
          slots: {
            default: ({ row }: any) => {
              return row.change_length
            },
            edit: ({ row, rowIndex }: any) => {
              return h(VxeInput, {
                type: 'float',
                id: `roll${rowIndex}`,
                onKeydown: $event =>
                  setFieldFocusOnGridTable(rowIndex, $event, 'change_length'),
                modelValue: row.change_length,
                onInput: (val) => {
                  row.change_length = val.value
                },
              })
            },
          },
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'change_actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'change_settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'change_before',
      title: '变更前',
      children: [
        {
          field: 'roll',
          title: '匹数',
          minWidth: 100,
        },
        // {
        //   field: 'selected_roll',
        //   title: '已选匹数',
        //   minWidth: 100,
        // },
        {
          field: 'volume_number',
          title: '卷号',
          minWidth: 100,
        },
        {
          field: 'warehouse_bin_name',
          title: '仓位',
          minWidth: 100,
        },
        {
          field: 'base_unit_weight',
          title: '基本单位数量',
          minWidth: 120,
        },
        {
          field: 'paper_tube_weight',
          title: '纸筒重量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'length',
          title: '辅助数量',
          minWidth: 100,
        },
        {
          field: 'finish_product_width_and_unit_name',
          title: '成品幅宽',
          minWidth: 100,
        },
        {
          field: 'finish_product_gram_weight_and_unit_name',
          title: '成品克重',
          minWidth: 100,
        },
        {
          field: 'dye_factory_color_code',
          title: '染厂色号',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'shelf_no',
          title: '货架号',
          minWidth: 100,
        },
        {
          field: 'stock_remark',
          title: '库存备注',
          minWidth: 100,
        },
        {
          field: 'internal_remark',
          title: '内部备注',
          minWidth: 100,
        },
        {
          field: 'account_num',
          title: '款号',
          minWidth: 100,
        },
        {
          field: 'contract_number',
          title: '合同号',
          minWidth: 100,
        },
        {
          field: 'digital_code',
          title: '数字码',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
        {
          field: 'scan_user_name',
          title: '扫描人',
          minWidth: 100,
        },
        {
          field: 'scan_time',
          title: '扫描时间',
          minWidth: 100,
          isDate: true,
        },
      ],
    },
  ]
}

const bulkShow = ref(false)
function handEdit() {
  if (state.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}
// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'change_roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'change_weight',
    title: '基本单位数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'change_length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
])
async function bulkSubmit({ row, value, quickInputResult }: any) {
  list.value.map((item: any, index) => {
    if (item.selected) {
      if (row.quickInput && quickInputResult?.[index]) {
        item[row.field] = quickInputResult[index]
        return item
      }
      item[row.field] = value[row.field]
      item[row.field_name] = value[row.field_name]
    }
  })

  ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

async function showDialog(row: any) {
  loading.value = true

  state.baseDataForm.dye_factory_dyelot_number = row.dye_factory_dyelot_number
  state.baseDataForm.product_code = row.product_code
  state.baseDataForm.product_name = row.product_name
  state.baseDataForm.product_color_name = row.product_color_name
  state.baseDataForm.arrange_roll = row.arrange_roll
  const item_fc_data = Array.isArray(row.item_fc_data)
    ? row.item_fc_data
    : []

  // 数据回显
  list.value = item_fc_data.map((item: any) => {
    let temp = {
      ...item,
      // 变更后数据
      change_roll: item?.item_fc_change_data?.[0]?.change_roll || 0,
      change_weight: item?.item_fc_change_data?.[0]?.change_weight || 0,
      change_length: item?.item_fc_change_data?.[0]?.change_length || 0,
    }
    temp = computedKeys(temp)
    return temp
  })
  state.multipleSelection = list.value.map((item) => {
    nextTick(() => {
      TablesRef.value.TableRef.setCheckboxRow(item, true)
    })
    return item
  })
  state.showModal = true
  loading.value = false
}

// 计算数据
function computedKeys(row: any) {
  const { change_weight, weight_error, settle_error_weight } = row
  const change_actually_weight = currency(change_weight).add(row.base_unit_weight).subtract(weight_error).value // 变更码单数量=(变更基本单位数量+原基本单位数量-码单空差)
  row.change_actually_weight = change_actually_weight
  row.change_settle_weight = currency(change_actually_weight).subtract(settle_error_weight).value// 变更结算数量=(变更码单数量-结算空差)

  return row
}

async function handleSure() {
  if (!list.value.length)
    return ElMessage.error('暂无可变更数据')

  // for (let i = 0; i < list.value.length; i++) {
  //   if (list.value[i].change_roll === '')
  //     return ElMessage.error('匹数为必填项')
  //   if (list.value[i].change_weight === '')
  //     return ElMessage.error('基本单位数量为必填项')
  // }

  const submitList = list.value.map((item: any) => {
    delete item.item_fc_change_data
    return {
      ...item,
      item_fc_change_data: [{
        ...item,
      }],
    }
  })
  emits(
    'handleSure',
    submitList,
  )
  state.showModal = false
}
function handCancel() {
  state.showModal = false
}

defineExpose({
  showDialog,
  state,
})

const columnList_config = computed(() => ({
  showSeq: true,
  showCheckListAll: true,
  checkboxConfig: {
    checkField: 'selected',
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    showStatus: true,
    enabled: state.isEdit,
  },
  filterConfig: {
    showIcon: false,
  },
  loading: loading.value,
  showCheckBox: state.isEdit,
  height: '100%',
  scrollY: { enabled: true },
  footerMethod: FooterMethod,
  footerCellClassName: (row: any) => footerCellClassName(row),
  handAllSelect,
  handleSelectionChange,
}))
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    show-footer
    :title="state.modalName"
    width="80vw"
    height="80vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
  >
    <div class="flex flex-col h-full">
      <div
        class="descriptions_row"
        :style="{ '--minLabelWidth': '84px' }"
      >
        <DescriptionsFormItem
          v-for="(item, index) in form_options"
          :key="index"
          :label="`${item.text}:`"
        >
          <template #content>
            <span>{{ state.baseDataForm[item.key] }}</span>
          </template>
        </DescriptionsFormItem>
      </div>
      <FildCard
        title=""
        class="mt-[10px] flex-1 flex flex-col overflow-hidden"
        no-shadow
        :tool-bar="false"
      >
        <template v-if="state.isEdit" #right-top>
          <ElButton type="primary" @click="handEdit">
            批量操作
          </ElButton>
        </template>
        <div class="h-auto max-h-[60vh]">
          <GridTable
            ref="TablesRef"
            :columns="columnList"
            :data="list"
            :config="columnList_config"
            @change-select-all-list-data="handleSelectAllList"
          />
        </div>
      </FildCard>
    </div>
    <template v-if="state.isEdit" #footer>
      <ElButton type="primary" @click="handCancel">
        取消
      </ElButton>
      <ElButton type="primary" @click="handleSure">
        确认
      </ElButton>
    </template>
  </vxe-modal>
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  >
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 100px;
      text-align: right;
    }
  }
}

::v-deep(.col-green) {
  color: green;
}

::v-deep(.col-red) {
  color: red;
}
</style>
