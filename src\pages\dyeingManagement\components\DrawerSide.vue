<script lang="ts" setup>
import { reactive, ref, watch, watchEffect } from 'vue'
import { cloneDeep } from 'lodash-es'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import Autocomplete from './components/Autocomplete.vue'
import {
  getDyeingFinishingProcessDataEnumList,
} from '@/api/dyeingNotice'
import useRouterList from '@/use/useRouterList'

interface Props {
  info?: any
  isEdit?: boolean
  // eslint-disable-next-line vue/prop-name-casing
  dye_factory_id?: number
  activeFirstProductId?: number
}

const props = withDefaults(defineProps<Props>(), {
  info: '',
  isEdit: false,
  dye_factory_id: 0,
  activeFirstProductId: 0,
})

const emits = defineEmits(['update:info'])

// const { fetchData, data } = getInfoDyeingFinishingProcessDataEnumList()
const { fetchData, data, success, msg } = getDyeingFinishingProcessDataEnumList()

const state = reactive<any>({
  showModal: false,
  checkList: [],
  selectList: [],
  query: {},
  is_length_cut: false,
  info: {},
})

function updateData(list: any[], source: any[], key: string) {
  list?.map((item: any) => {
    source?.forEach((it: any) => {
      if (item.id === it.id)
        item[key] = it[key]

      return item
    })
  })
}

function filterData(list: any[], type: number) {
  if (!list)
    return []
  return [
    ...list.filter((item: any) => {
      return item.show_type === type
    }),
  ]
}

let comparativeData: any = null
function checkNewData() {
  const list = data.value.list || []
  // 检查数组长度是否一致
  if (comparativeData.length !== list.length)
    return false

  // 遍历比较每个元素的 type_data 是否相同
  for (let i = 0; i < comparativeData.length; i++) {
    const compTypeData = comparativeData[i].type_data
    const dataTypeData = list[i].type_data

    // 检查 type_data 数组长度是否一致
    if (compTypeData.length !== dataTypeData.length)
      return false

    // 比较每个对象的内容
    for (let j = 0; j < compTypeData.length; j++) {
      const compObj = compTypeData[j]
      const dataObj = dataTypeData[j]

      // 也就三个字符串的键值对，直接使用 JSON.stringify 来比较对象是否相同
      if (JSON.stringify(compObj) !== JSON.stringify(dataObj))
        return false
    }
  }

  // 如果上述条件都满足，则认为数据一致
  return true
}

async function getData(dye_factory_id: number, activeFirstProductId: number) {
  await fetchData({
    dye_factory_id: Number(dye_factory_id),
    product_id: Number(activeFirstProductId),
  })

  if (comparativeData !== null) {
    if (checkNewData()) {
      // 数据一致
      return
    }
    // 数据一致，把新的数据赋值给 comparativeData
    comparativeData = cloneDeep(data.value.list || [])
  }
  else { comparativeData = cloneDeep(data.value.list || []) }

  if (!success.value)
    return ElMessage.error(msg.value)

  if (props.isEdit) {
    // 保持数据新鲜度
    updateData(data.value.list, props.info.checkList, 'selectValue')
    updateData(data.value.list, props.info.selectList, 'selectValue')
    state.checkList = filterData(data.value.list, 1)
    state.selectList = filterData(data.value.list, 2)
    state.is_length_cut = props.info.is_length_cut
  }
  else {
    // 用新的数据覆盖
    if (data.value?.list) {
      data.value.list?.map((item: any) => {
        if (item.show_type === 1)
          item.checked = false
        else
          item.selectValue = []

        return item
      })
      state.checkList = filterData(data.value.list, 1)
      state.selectList = filterData(data.value.list, 2)
      state.is_length_cut = false
    }
    else {
      state.checkList = []
      state.selectList = []
      state.is_length_cut = false
    }
  }
  state.query = {
    checkList: state.checkList,
    selectList: state.selectList,
    is_length_cut: state.is_length_cut,
  }
  emits('update:info', state.query)
}

watch(
  () => [state.checkList, state.selectList, state.is_length_cut],
  () => {
    state.query = {
      checkList: state.checkList,
      selectList: state.selectList,
      is_length_cut: state.is_length_cut,
    }

    emits('update:info', state.query)
  },
  { deep: true },
)

watchEffect(() => {
  // 在新增模式下，有染厂id就可以去请求了，成品可以有也可以没有
  if (props.dye_factory_id) {
    getData(props.dye_factory_id, props.activeFirstProductId)
    return
  }
  state.checkList = props.info.checkList
  state.selectList = props.info.selectList
  state.is_length_cut = props.info.is_length_cut
})

function removeSelectValue(item: any, value: any) {
  item.selectValue = item.selectValue.filter((it: any) => it !== value)
}

const editingList = ref<any>([])
const canClose = ref<boolean>(false)

function selectAdd(newValue: string, item: any, oldValue: string | number) {
  if (item) {
    // 如果新的值和旧的值不一样，就替换
    // 去掉抛回来的前面的-，假设old是id
    if (newValue.substring(1) !== getNameById(Number(oldValue), item.type_data)) {
      item.selectValue = item.selectValue.map((it: any) =>
        it === oldValue ? newValue : it,
      )
    }
  }
  canClose.value = true
}
function cancelAdd() {
  canClose.value = true
}
function openUpdateSelectValue(item: any, value: any) {
  if (!props.isEdit)
    return
  // 如果editingList里不存在这个值，就添加进去
  if (!editingList.value.includes(value)) {
    editingList.value.splice(editingList.value.length, 0, value)
    return
  }
  if (canClose.value) {
    const index = editingList.value.indexOf(value)
    if (index > -1) {
      editingList.value.splice(index, 1)
      canClose.value = false
    }
  }
}

function valueIsEditing(value: any) {
  return props.isEdit && editingList.value.includes(value)
}

function getNameById(id: number, list: any) {
  const name = list.find((item: any) => item.id === id)?.name
  if (name)
    return name

  return id
}

defineExpose({
  state,
})
const routerList = useRouterList()
function goNew() {
  routerList.push({
    name: 'DyeingInformation',
    close_self: false,
  })
}
const checkboxRef = ref()
const visible = ref(false)
const tooltipsContent = ref('')
let prevCheckboxIndex: number | null = null
function handleMouseover(e: any, name: string, index: number) {
  if (prevCheckboxIndex === index)
    return
  if (name.length < 10)
    return
  prevCheckboxIndex = index
  checkboxRef.value = e.currentTarget
  tooltipsContent.value = name
  // 延迟100ms再显示
  setTimeout(() => {
    visible.value = true
  }, 1000)
}
</script>

<template>
  <!--  需要配合 handleCraftRequirement 在外部处理了数据后再使用 -->
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex items-center justify-between mb-[5px]">
      <span>
        工艺要求：
      </span>
      <el-link :icon="Plus" type="primary" :underline="false" @click="goNew">
        新增工艺
      </el-link>
    </div>
    <div class="flex-auto overflow-hidden">
      <el-scrollbar height="100%">
        <!-- <el-drawer v-model="state.showModal" title="工艺要求" :before-close="handleClose" @close="handleClose"> -->
        <el-form

          label-position="top"
          label-width="auto"
          class="flex-wrap flex"
        >
          <el-form-item
            v-for="(item, index) in props.info.checkList"
            :key="index"
            class="mb-[5px] w-full"
            :label="item.name"
          >
            <el-checkbox-group v-model="item.selectValue" size="small" :disabled="!isEdit">
              <el-checkbox v-for="(it, idx) in item.type_data" :key="idx" :value="it.id" border @mouseover="handleMouseover($event, it.name, idx)">
                {{ it.name }}
              </el-checkbox>
            </el-checkbox-group>
            <el-tooltip
              v-model:visible="visible"
              class="box-item"
              effect="dark"
              :content="tooltipsContent"
              :virtual-ref="checkboxRef"
              virtual-triggering
              placement="top-start"
            />
          </el-form-item>
        </el-form>
        <el-form
          label-position="top"
          label-width="auto"
        >
          <el-form-item
            v-for="(item, index) in props.info.selectList"
            :key="index"
            :label="item.name"
            class="flex items-center mb-[5px]"
          >
            <el-input
              v-if="item.show_type === 3"
              v-model="item.selectValue"
              class="w-[300px]"
              clearable
            />
            <!-- <SelectComponents :options="item.type_data" v-else style="width: 300px" label-field="name" value-field="id" v-model="item.selectValue" clearable /> -->
            <el-select
              v-else
              v-model="item.selectValue"
              class="w-[300px]"
              multiple
              filterable
              clearable
              :disabled="!isEdit"
            >
              <el-option
                v-for="(it, inx) in item.type_data"
                :key="inx"
                :label="it.name"
                :value="it.id"
              />
              <template #tag>
                <el-tag
                  v-for="value in item.selectValue"
                  :key="value"
                  :closable="isEdit"
                  :disable-transitions="true"
                  type="info"
                  style="height: auto"
                  class="w-auto h-auto whitespace-normal break-words py-1"
                  @close="removeSelectValue(item, value)"
                  @click="openUpdateSelectValue(item, value)"
                >
                  <span v-if="!valueIsEditing(value)">
                    {{
                      String(value)?.startsWith("-")
                        ? value.substring(1)
                        : getNameById(value, item.type_data)
                    }}
                  </span>
                  <div v-else>
                    <Autocomplete
                      :value-name="getNameById(value, item.type_data)"
                      :option-list="item.type_data"
                      @confirm-add="(newValue) => selectAdd(newValue, item, value)"
                      @cancel-add="cancelAdd"
                    />
                  </div>
                </el-tag>
              </template>
            </el-select>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </div>
  <!-- </el-drawer> -->
  </div>
</template>

<style scoped>
:deep(.el-checkbox__label){
  @apply truncate max-w-[230px];
}
/* 隐藏 webkit 浏览器的滚动条 */
.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

/* 兼容 Firefox */
.hide-scrollbar {
    scrollbar-width: none;
}

/* 兼容 IE */
.hide-scrollbar {
    -ms-overflow-style: none;
}
</style>
