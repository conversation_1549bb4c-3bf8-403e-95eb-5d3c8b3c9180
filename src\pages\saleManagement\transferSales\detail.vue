<script lang="ts" setup>
import { nextTick, onActivated, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import XimaDialog from './components/XimaDialog.vue'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import {
  formatDate,
  formatDateTime,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import { deepClone, orderStatusConfirmBox } from '@/common/util'
import {
  getSaleTransferOrder,
  saleTransferOrderauditCancel,
  saleTransferOrderauditPass,
  saleTransferOrderauditReject,
  saleTransferOrderauditWait,
} from '@/api/transferSales'
import StatusColumn from '@/components/StatusColumn/index.vue'
import GridTable from '@/components/GridTable/index.vue'
import { GetBySrcId } from '@/api'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'

const tableConfig = ref({
  showSlotNums: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: false,
  fieldApiKey: 'TransferSalesDetail',
})
const writeOffTableRef = ref()
const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeSaleTransferOrder,
  dataType: PrintDataType.Product,
})
const writeOffRecords = ref<any>([])
const { fetchData: getFetch, data, success, msg } = getSaleTransferOrder()
const { fetchData: getBySrcId, data: srcData } = GetBySrcId()

const route = useRoute()

onMounted(() => {
  getInfomation()
})

onActivated(() => {
  getInfomation()
})

async function getInfomation() {
  await getFetch({ id: route.query.id, order_type: 1 })
  if (!success.value) {
    ElMessage.error(msg.value)
    return
  }
  await getBySrcId({ src_id: route.query.id, src_type: 1, order_type: 1 })
  writeOffRecords.value = srcData.value?.collect_records || []
  nextTick(() => {
    writeOffTableRef.value?.TableRef.loadData(writeOffRecords.value)
  })
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['sale_settle_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'sale_settle_weight') as any)}`

      if (['gross_profit'].includes(column.property))
        return `${formatUnitPriceDiv(sumNum(data, 'gross_profit') as any)}`

      if (['total_sale_amount'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'total_sale_amount') as any)}`

      if (['total_cost_amount'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'total_cost_amount') as any)}`

      if (['total_gross_profit'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'total_gross_profit') as any)}`

      if (['other_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      return null
    }),
  ]
}

const XimaDialogRef = ref()

function handWrite(row: any, rowIndex: number) {
  XimaDialogRef.value.state.showModal = true
  XimaDialogRef.value.state.rowIndex = rowIndex
  XimaDialogRef.value.state.horsepower = formatPriceDiv(row.roll)
  XimaDialogRef.value.state.isDisabled = true

  XimaDialogRef.value.state.info = {
    finish_product_code: row.product_code,
    finish_product_name: row.product_name,
    product_color_code: row.product_color_code,
    product_color_name: row.product_color_name,
    dyelot: row.dyelot_number,
    supplier_name: row.supplier_name,
    measurement_unit_name: row.measurement_unit_name,
  }

  const list = deepClone(row?.sale_transfer_order_weight_info_list || [])

  list.map((item: any) => {
    item.sale_weight = formatWeightDiv(item.sale_weight)
    item.sale_weight_error = formatWeightDiv(item.sale_weight_error)
    item.sale_settle_weight = formatWeightDiv(item.sale_settle_weight)
    item.supplier_weight = formatWeightDiv(item.supplier_weight)
    item.supplier_weight_error = formatWeightDiv(item.supplier_weight_error)
    item.supplier_selltle_weight = formatWeightDiv(item.supplier_settle_weight)
    return item
  })

  XimaDialogRef.value.tableData = list
}

async function updateStatus(audit_status: number) {
  const id: any = Number(route.query.id)
  const order_type = Number(data.value.order_type)
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: saleTransferOrderauditCancel,
      order_type,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: saleTransferOrderauditReject,
      order_type,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: saleTransferOrderauditPass,
      order_type,
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: {
        desc: '点击消审后订单将变为待审核状态',
        title: '是否消审该订单？',
      },
      api: saleTransferOrderauditWait,
      order_type,
    })
  }
  getInfomation()
}

const columnList = ref([
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    field: 'sale_price',
    title: '销售单价',
    minWidth: 100,
    isUnitPrice: true,
  },
  {
    field: 'cost_price',
    title: '采购价',
    minWidth: 100,
    isUnitPrice: true,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'sale_settle_weight',
    title: '结算数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  {
    field: 'gross_profit',
    title: '单位毛利',
    minWidth: 100,
    isUnitPrice: true,
    soltName: 'gross_profit',
  },
  {
    field: 'total_sale_amount',
    title: '销售总额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'total_cost_amount',
    title: '采购总额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'total_gross_profit',
    title: '总毛利',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: '',
    title: '细码',
    minWidth: 120,
    soltName: 'xima',
    fixed: 'right',
  },
])

const writeOffColumn = [
  { type: 'seq', width: 50, title: '序号' },
  {
    field: 'actually_collect_order_no',
    title: '单据编号',
    slots: {
      default: ({ row }) => {
        return row.actually_collect_order_no
      },
    },
  },
  {
    field: 'settle_type_name',
    title: '结算类型',
    slots: {
      default: ({ row }) => {
        return row.settle_type_name
      },
    },
  },
  {
    field: 'collect_price',
    title: '收款金额',
    slots: {
      default: ({ row }) => {
        return formatPriceDiv(row.collect_price)
      },
    },
  },
  {
    field: 'actually_collect_order_no',
    title: '单据编号',
    slots: {
      default: ({ row }) => {
        return row.actually_collect_order_no
      },
    },
  },
  {
    field: 'offset_price',
    title: '优惠金额',
    slots: {
      default: ({ row }) => {
        return formatPriceDiv(row.offset_price)
      },
    },
  },
  {
    field: 'discount_price',
    title: '折扣金额',
    slots: {
      default: ({ row }) => {
        return formatPriceDiv(row.discount_price)
      },
    },
  },
  {
    field: 'deduction_price',
    title: '扣款金额',
    slots: {
      default: ({ row }) => {
        return formatPriceDiv(row.deduction_price)
      },
    },
  },
  {
    field: 'operator_name',
    title: '操作人',
    slots: {
      default: ({ row }) => {
        return row.operator_name
      },
    },
  },
  {
    field: 'auditor_name',
    title: '审核人',
    slots: {
      default: ({ row }) => {
        return row.auditor_name
      },
    },
  },
  {
    field: 'actually_collect_date',
    title: '收款时间',
    slots: {
      default: ({ row }) => {
        return formatDateTime(row.actually_collect_date)
      },
    },
  },
]
</script>

<template>
  <StatusColumn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="TransferSales_wait"
    permission_reject_key="TransferSales_reject"
    permission_pass_key="TransferSales_pass"
    permission_cancel_key="TransferSales_cancel"
    permission_edit_key="TransferSalesEdit"
    edit_router_name="TransferSalesEdit"
    :edit_query="{ order_type: data.order_type }"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  >
    <template #print>
      <PrintPopoverBtn
        :id="Number(route.query.id)"
        api="getSaleTransferOrder"
        :options="options"
        :order_type="1"
      />
      <!--      <PrintBtn type="transferSales" :tid="1676783992639744" api="getSaleTransferOrder" :order_type="1" :id="Number(route.query.id)" /> -->
    </template>
  </StatusColumn>
  <FildCard title="客户信息" :tool-bar="false">
    <div class="pt-4">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户名称">
          <template #content>
            {{ data?.customer_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期">
          <template #content>
            {{ formatDate(data?.order_time) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员">
          <template #content>
            {{ data?.sale_user_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人员">
          <template #content>
            {{ data?.contact_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话">
          <template #content>
            {{ data?.phone }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型">
          <template #content>
            {{ data?.sale_mode_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单备注" copies="2">
          <template #content>
            {{ data?.order_remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址" copies="2">
          <template #content>
            {{ data?.address }}
          </template>
        </DescriptionsFormItem>
      </div>
    </div>
  </FildCard>

  <!--  调货信息 -->
  <FildCard title="调货信息" class="mt-[5px]">
    <Table
      :config="tableConfig"
      :table-list="data.sale_transfer_order_detail_info"
      :column-list="columnList"
    >
      <template #gross_profit="{ row }">
        <div
          v-if="Number(formatUnitPriceDiv(row.gross_profit)) < 0"
          class="red_font"
        >
          {{ formatUnitPriceDiv(row?.gross_profit) }}
        </div>
        <div v-else>
          {{ formatUnitPriceDiv(row?.gross_profit) }}
        </div>
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            查看
          </el-button>
        </div>
      </template>
    </Table>
  </FildCard>
  <XimaDialog ref="XimaDialogRef" />

  <!--  结算信息 -->
  <FildCard
    v-if="data?.audit_status_name === '已审核'"
    title="结算信息"
    :tool-bar="false"
    class="mt-[5px]"
  >
    <div class="pt-4">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="应收金额">
          <template #content>
            {{ formatPriceDiv(srcData?.total_settle_money) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="优惠金额">
          <template #content>
            {{ formatPriceDiv(srcData?.total_remove_money) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="折扣金额">
          <template #content>
            {{ formatPriceDiv(srcData?.total_discount_money) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="扣款金额">
          <template #content>
            {{ formatPriceDiv(srcData?.total_chargeback_money) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="已收金额">
          <template #content>
            {{ formatPriceDiv(srcData?.total_collected_money) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="未收金额">
          <template #content>
            {{ formatPriceDiv(srcData?.total_uncollect_money) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="累计欠款">
          <template #content>
            {{ formatPriceDiv(srcData?.total_arrears_amount) }}
          </template>
        </DescriptionsFormItem>
      </div>
    </div>
  </FildCard>

  <!--  核销记录 -->
  <FildCard
    v-if="data?.audit_status_name === '已审核'"
    title="核销记录"
    :tool-bar="false"
    class="mt-[5px]"
  >
    <div class="pt-4">
      <GridTable
        ref="writeOffTableRef"
        :columns="writeOffColumn"
        :data="writeOffRecords"
        height="257px"
      />
    </div>
  </FildCard>
</template>

<style lang="scss" scoped>
.red_font {
  color: red;
  font-weight: 500;
}
</style>
