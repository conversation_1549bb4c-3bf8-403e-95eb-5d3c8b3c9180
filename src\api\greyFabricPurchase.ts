import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const getGfmPurchaseReceiveList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/getGfmPurchaseReceiveList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

//  从资料中添加
export const GetGreyFabricInfoListUseByOthers = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfoListUseByOthers',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

//  从坯布采购单中添加
export const GetPurchaseGreyFabricItemList = () => {
  return useRequest({
    url: '/admin/v1/purchase/purchaseGreyFarbric/getItemAddrEnumList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新增数据
export const addGFMPurchaseReceive = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/addGFMPurchaseReceive',
    method: 'post',
  })
}

// 获取详情
export const getPurchaseGreyFarbric = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/getGFMPurchaseReceive',
    method: 'get',
  })
}

// 导出数据
export const getGfmPurchaseReceiveListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/getGfmPurchaseReceiveList',
    method: 'get',
    nameFile,
  })
}

// 更新数据
export const updateGFMPurchaseReceive = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/updateGFMPurchaseReceive',
    method: 'put',
  })
}

// 审核
export const updateGFMPurchaseReceiveStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/updateGFMPurchaseReceiveStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGFMPurchaseReceiveStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/updateGFMPurchaseReceiveStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGFMPurchaseReceiveStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/updateGFMPurchaseReceiveStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGFMPurchaseReceiveStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmPurchaseReceive/updateGFMPurchaseReceiveStatusReject',
    method: 'put',
  })
}
