<script lang="ts" setup name="DyeingSchedule">
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import {
  computed,
  nextTick,
  onActivated,
  onMounted,
  reactive,
  ref,
  watch,
} from 'vue'
import { createColumnList } from './common/columnList'
import RechargeDialog from './components/RechargeDialog.vue'
import RechargeRecordDialog from './components/RechargeRecordDialog.vue'
import ConsumeRecordDialog from './components/ConsumeRecordDialog.vue'
import {
  debounce,
  deepClone,
  getFilterData,
  resetData,
} from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import GridTable from '@/components/GridTable/index.vue'
import { processDataOut } from '@/common/handBinary'
import { GetElectronicColorCardList, UpdateElectronicColorCardStatusDisable, UpdateElectronicColorCardStatusEnable } from '@/api/colorCardManagement/serve'

const state = reactive<any>({
  filterData: {
    id_or_name: '',
    tenant_contacts: '',
    phone: '',
  },
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = GetElectronicColorCardList()

const TableRef = ref<InstanceType<typeof GridTable> | null>()
// 获取数据
const getQuery = function () {
  let query = deepClone(state.filterData)
  query = getFilterData(query)
  return query
}
const getData = debounce(async () => {
  const query = getQuery()
  await ApiCustomerList(getFilterData(query))
}, 400)

onActivated(getData)

onMounted(() => {
  getData()
})

const resultData = ref([])
watch(() => data.value.list, () => {
  resultData.value = data.value?.list?.map((item: any) => {
    return processDataOut(item)
  })
  nextTick(() => {
    TableRef.value?.TableRef?.loadData(resultData.value)
  })
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  showSeq: true,
  fieldApiKey: 'ColorCardManagementIndex',
  loading,
  showPagition: true,
  showSlotNums: true,
  operateWidth: '140',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

// 启用/禁用
async function changeStatus(row: any, val: boolean) {
  const api = val ? UpdateElectronicColorCardStatusEnable : UpdateElectronicColorCardStatusDisable
  const { fetchData, success, msg } = api()
  ElMessageBox.confirm(`确认${val ? '启用' : '禁用'}该数据？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      await fetchData({ id: row.id })
      if (success.value) {
        ElMessage.success('修改成功')
        getData()
      }

      else { ElMessage.error(msg.value) }
    })
}

const elPaginationConfig = computed(() => ({
  defaultPageSize: 500,
  page: page.value,
  pageSizes: [50, 100, 500, 1000],
  size: size.value,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: total.value,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))

const detailItem = ref('')
// 充值
const showRechargeDialog = ref(false)
function reCharge(row: any) {
  detailItem.value = row
  showRechargeDialog.value = true
}
// 充值记录
const showRechargeRecordDialog = ref(false)
function reChargeRecord(row: any) {
  detailItem.value = row
  showRechargeRecordDialog.value = true
}

// 消耗记录
const showConsumeRecordDialog = ref(false)
function consumeRecord(row: any) {
  detailItem.value = row
  showConsumeRecordDialog.value = true
}
const columnList = createColumnList({
  reCharge,
  reChargeRecord,
  consumeRecord,
  changeStatus,
})
</script>

<template>
  <div class="flex flex-col h-full">
    <FildCard :tool-bar="false" title="条件筛选">
      <template #right-top>
        <ElButton v-btnAntiShake="handReset" type="primary">
          重置
        </ElButton>
      </template>
      <div class="descriptions_row" :style="{ '--minLabelWidth': '96px' }">
        <DescriptionsFormItem label="账套名称:">
          <template #content>
            <el-input v-model="state.filterData.id_or_name" placeholder="请输入账套名称" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人:">
          <template #content>
            <el-input v-model="state.filterData.tenant_contacts" placeholder="请输入联系人" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话:">
          <template #content>
            <el-input v-model="state.filterData.phone" placeholder="请输入联系电话" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="mt-[5px] flex-col flex flex-1 overflow-y-hidden">
      <GridTable
        ref="TableRef"
        :el-pagination-config="elPaginationConfig"
        :columns="columnList"
        :data="data?.list"
        :config="tableConfig"
        show-pagition
        height="100%"
      />
    </FildCard>
    <RechargeDialog v-model="showRechargeDialog" :row="detailItem" @handle-sure="getData" />
    <RechargeRecordDialog v-model="showRechargeRecordDialog" :row="detailItem" />
    <ConsumeRecordDialog v-model="showConsumeRecordDialog" :row="detailItem" />
  </div>
</template>

<style lang="scss" scoped>
</style>
