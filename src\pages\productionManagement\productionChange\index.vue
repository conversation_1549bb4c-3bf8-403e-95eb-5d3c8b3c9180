<script setup lang="ts" name="ProductionChange">
import { useRouter } from 'vue-router'
import { Delete } from '@element-plus/icons-vue'
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import RelevanceSalesPlanOrder from '../components/RelevanceSalesPlanOrder.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
// import BottonExcel from '@/components/BottonExcel/index.vue'
import { formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import {
  byIdGreyInfoProductionChange,
  byIdMaterialProductionChange,
  byIdTechnologicalRequirementProductionChange,
  cancelApprovedSheetOfProductionChange,
  checkSheetOfProductionChange,
  getProductionChangeList,
} from '@/api/productionChange'
import { BusinessUnitIdEnum } from '@/common/enum'
import SelectDialog from '@/components/SelectDialog/index.vue'
import type { TableColumn } from '@/components/Table/type'

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const router = useRouter()
const form_options = [
  {
    text: '织造规格',
    key: 'weaving_specifications_name_list',
  },
  {
    text: '针寸数',
    key: 'needle_size',
  },
  {
    text: '总针数',
    key: 'total_needle_size',
  },
  {
    text: '织造损耗',
    key: 'weaving_loss',
    unit: '%',
  },
  {
    text: '织机品牌',
    key: 'loom_brand',
  },
  {
    text: '织机机型',
    key: 'loom_model_name',
  },
  {
    text: '安排机台数',
    key: 'machines_num',
  },
  {
    text: '上针',
    key: 'upper_needle',
    copies: 2,
  },
  {
    text: '纱批',
    key: 'yarn_batch',
    copies: 2,
  },
  {
    text: '下针',
    key: 'lower_needle',
    copies: 2,
  },
  {
    text: '排纱',
    key: 'yarn_arrange',
    copies: 2,
  },
  {
    text: '纱长',
    key: 'yarn_length',
    copies: 2,
  },
  {
    text: '包装要求',
    key: 'packaging_require',
    copies: 2,
  },
]
const state = reactive<any>({
  tableData: [],
  filterData: {
    order_no: '',
    production_plan_order_no: '',
    weaving_mill_id: '',
    status: [],
  },
  tabs: {
    id: 0,
    greyList: [], // 坯布信息
    materialList: [], // 用料比例
    technologicalRequirementForm: {}, // 工艺要求
    fn: {}, // tabs下对应函数
  },
  multipleSelection: [],
})
const activeName = ref<string>('one')

const { fetchData: ApiCustomerList, data: datalist, total, loading, page, size, handleSizeChange, handleCurrentChange }: any = getProductionChangeList()
// 获取数据
const getData = debounce(async () => {
  await ApiCustomerList(
    getFilterData({
      ...state.filterData,
      status: state.filterData.status.join(','),
    }),
  )
  if (datalist.value?.list)
    getTabsData(datalist.value.list[0].id)
}, 400)

// 首次加载数据
onMounted(() => {
  getData()
})

watch(
  () => datalist.value,
  () => {
    state.tableData
      = datalist.value?.list?.map((item: any) => {
        return {
          ...item,
          scheduling_roll: formatTwoDecimalsDiv(Number(item.scheduling_roll)),
          change_roll: formatTwoDecimalsDiv(Number(item.change_roll)),
          final_roll: formatTwoDecimalsDiv(Number(item.final_roll)),
          scheduling_weight: `${formatWeightDiv(Number(item.scheduling_weight))}${item.unit_name}`,
          produced_weight: `${formatWeightDiv(Number(item.produced_weight))}${item.unit_name}`,
          change_weight: formatWeightDiv(Number(item.change_weight)),
        }
      }) || []
    //
  },
  { deep: true },
)

// 实时过滤数据
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)
// 生产通知单表格列配置
const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '9%',
  showSort: false,
  height: '100%',
  fieldApiKey: 'ProductionChange',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
// 生产变更单表格列配置
const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'order_no',
    soltName: 'order_no',
    title: '单据编号',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'production_notify_order_no',
    soltName: 'production_notify_order_no',
    title: '生产通知单号',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaving_mill_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaving_mill_order_follower_name',
    title: '织厂跟单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'scheduling_roll',
    title: '原匹数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'change_roll',
    soltName: 'change_roll',
    title: '变更匹数',
    minWidth: 100,
  },
  {
    field: 'scheduling_weight',
    title: '原数量',
    minWidth: 150,
  },
  {
    field: 'change_weight',
    soltName: 'change_weight',
    title: '变更数量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receipt_grey_fabric_date',
    title: '交坯日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'order_remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'notify_date',
    title: '通知日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '审核时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    soltName: 'status',
    fixed: 'right',
    showOrder_status: true,
    width: '5%',
  },
])
// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}

// 坯布信息表格配置
const columnList_fabic_config = ref({
  showSlotNums: false,
  height: '100%',
})
// 坯布信息表格列配置
const columnList_fabic = ref([
  {
    sortable: true,
    field: 'sale_plan_order_no',
    soltName: 'sale_plan_order_no',
    title: '销售计划单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'process_price',
    title: '加工单价',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'scheduling_roll',
    title: '原匹数',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'change_roll',
    soltName: 'change_roll',
    title: '变更匹数',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'scheduling_weight',
    title: '数量',
    minWidth: 150,
  },
  {
    field: 'change_weight',
    soltName: 'change_weight',
    title: '变更数量',
    minWidth: '5%',
  },
])

// 用料比例表格配置
const columnList_material_config = ref({
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
})
function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['use_yarn_quantity'].includes(column.field))
        return sumNum(data, 'use_yarn_quantity', data?.[0] && data?.[0].unit_name, 'float')

      if (['send_yarn_quantity'].includes(column.field))
        return sumNum(data, 'send_yarn_quantity', data?.[0] && data?.[0].unit_name, 'float')
      return null
    }),
  ]
  return footerData
}
// 用料比例表格列配置
const columnList_material = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'weaving_category',
    title: '织造类别',
    minWidth: 100,
  },
  // {
  //   field: 'yarn_length',
  //   title: '纱长',
  //   minWidth: 100,
  // },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '坯布颜色',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方',
    minWidth: 100,
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
  },
  {
    field: 'use_yarn_quantity',
    soltName: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
  },
  {
    field: 'change_use_yarn_quantity',
    soltName: 'change_use_yarn_quantity',
    title: '变更用纱量',
    minWidth: 100,
  },
  {
    field: 'send_yarn_quantity',
    soltName: 'send_yarn_quantity',
    title: '发纱量',
    minWidth: 100,
  },
  {
    field: 'change_send_yarn_quantity',
    soltName: 'change_send_yarn_quantity',
    title: '变更发纱量',
    minWidth: 100,
  },
  {
    field: 'mill_private_yarn',
    soltName: 'mill_private_yarn',
    title: '织厂出料',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])

// tabs请求数据
const activeOptionFn: any = {
  one: 'getGreyInfoData',
  two: 'getMaterialData',
  three: 'getTechnologicalRequirementData',
}
watch(
  [() => activeName.value, () => state.tabs.id],
  () => {
    if (state.tabs.id)
      state.tabs.fn[activeOptionFn[activeName.value]](state.tabs.id)
  },
  { deep: true },
)
function getTabsData(id: string | number) {
  state.tabs.id = id
}
// 获取坯布信息
const { fetchData: GreyInfoFetch, data: greyData, success: GreyInfoSuccess, msg: GreyInfoMsg } = byIdGreyInfoProductionChange()
state.tabs.fn.getGreyInfoData = async (id: number | string) => {
  await GreyInfoFetch({ id })
  if (GreyInfoSuccess.value) {
    state.tabs.greyList = [greyData.value].map((item: any) => {
      return {
        ...item,
        grey_fabric_gram_weight: item.grey_fabric_gram_weight,
        finish_product_gram_weight: item.finish_product_gram_weight,
        weight_of_fabric: formatWeightDiv(Number(item.weight_of_fabric)),
        process_price: `${formatUnitPriceDiv(Number(item.process_price))}元/kg`,
        scheduling_weight: `${formatWeightDiv(Number(item.scheduling_weight))}${item.unit_name}`,
        produced_weight: `${formatWeightDiv(Number(item.produced_weight))}${item.unit_name}`,
        change_roll: formatTwoDecimalsDiv(Number(item.change_roll)),
        change_weight: formatWeightDiv(Number(item.change_weight)),
        scheduling_roll: formatTwoDecimalsDiv(Number(item.scheduling_roll)),
        sale_plan_order_no: item.production_notify_grey_fabric_detail && item.production_notify_grey_fabric_detail[0].sale_plan_order_no,
      }
    })
  }
  else {
    ElMessage.error(GreyInfoMsg.value)
  }
}

// 获取用料比例
const { fetchData: MaterialFetch, data: materialData, success: MaterialSuccess, msg: MaterialMsg } = byIdMaterialProductionChange()
state.tabs.fn.getMaterialData = async (id: number | string) => {
  await MaterialFetch({ id })
  if (MaterialSuccess.value) {
    state.tabs.materialList = (materialData.value?.list || [])?.map((item: any) => {
      return {
        ...item,
        yarn_ratio: `${formatTwoDecimalsDiv(item.yarn_ratio)}%`,
        yarn_loss: `${formatTwoDecimalsDiv(item.yarn_loss)}%`,
        use_yarn_quantity: formatWeightDiv(Number(item.use_yarn_quantity)),
        send_yarn_quantity: formatWeightDiv(Number(item.send_yarn_quantity)),
        change_use_yarn_quantity: formatWeightDiv(item.change_use_yarn_quantity),
        change_send_yarn_quantity: formatWeightDiv(item.change_send_yarn_quantity),
      }
    })
  }
  else {
    ElMessage.error(MaterialMsg.value)
  }
}

// 获取工艺要求
const {
  fetchData: TechnologicalRequirementFetch,
  data: TechnologicalRequirementData,
  success: TechnologicalRequirementSuccess,
  msg: TechnologicalRequirementMsg,
} = byIdTechnologicalRequirementProductionChange()
state.tabs.fn.getTechnologicalRequirementData = async (id: number | string) => {
  await TechnologicalRequirementFetch({ id })
  if (TechnologicalRequirementSuccess.value) {
    state.tabs.technologicalRequirementForm = {
      ...TechnologicalRequirementData.value,
      weaving_loss: formatTwoDecimalsDiv(TechnologicalRequirementData.value.weaving_loss),
      weaving_specifications_name_list: TechnologicalRequirementData.value.weaving_specifications.map((v: any) => v.weaving_specifications_name).join(','),
    }
  }
  else {
    ElMessage.error(TechnologicalRequirementMsg.value)
  }
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
// const loadingExcel = ref(false)
// const handleExport = async () => {
// if (datalist.value?.list?.length < 1) return ElMessage.warning('当前无数据可导出')
// const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getProductionChangeList()
// loadingExcel.value = true
// await getFetch({
//   ...getFilterData(state.filterData),
//   download: 1,
// })
// if (getSuccess.value) {
//   exportExcel()
//   ElMessage({
//     type: 'success',
//     message: '成功',
//   })
// } else {
//   ElMessage({
//     type: 'error',
//     message: getMsg.value,
//   })
// }
// loadingExcel.value = false
// }
const tableRef = ref<any>()
// const exportExcel = () => {
//   tableRef.value.tableRef.exportData({
//     filename: `生产变更单${formatTime(new Date())}`,
//     // isFooter: true,
//   })
// }
// 新建
// const handleAdd = () => {
//   router.push({ name: 'ProductionChangeAdd', query: { id: 1622712827433216 } })
// }
// 查看
function handDetail(row: any) {
  router.push({
    name: 'ProductionChangeDetail',
    query: { id: row?.id },
  })
}
// 编辑
function handEdit(row: any) {
  router.push({
    name: 'ProductionChangeEdit',
    query: { id: row?.id },
  })
}
// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = checkSheetOfProductionChange()
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = cancelApprovedSheetOfProductionChange()
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 跳转计划单详情
async function jumpNotifyDetail(id: number) {
  router.push({
    name: 'ProductionNoticeDetail',
    query: {
      id,
    },
  })
}

// 查看销售关联
const SaleAllocationRef = ref()
function customSaleShow(row: any) {
  SaleAllocationRef.value.showDialog({
    ...row,
    production_notify_grey_fabric_detail:
      row?.production_notify_grey_fabric_detail?.map((item: any) => {
        return {
          ...item,
          roll: formatTwoDecimalsDiv(item.roll),
          weight: formatWeightDiv(item.weight),
          planed_roll: formatTwoDecimalsDiv(item.planed_roll),
          planed_weight: formatWeightDiv(item.planed_weight),
          scheduling_roll: formatTwoDecimalsDiv(item.scheduling_roll),
          scheduling_weight: formatWeightDiv(item.scheduling_weight),
          produced_roll: formatTwoDecimalsDiv(item.produced_roll),
          produced_weight: formatWeightDiv(item.produced_weight),
          use_stock_roll: formatTwoDecimalsDiv(item.use_stock_roll),
          can_scheduling_roll: formatTwoDecimalsDiv(item.can_scheduling_roll),
          this_scheduling_roll: formatTwoDecimalsDiv(item.this_scheduling_roll),
        }
      }) || [],
  })
}

const customActiveName = ref('first')
const tableConfig1 = ref({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: false,
  showOperate: false,
  height: 300,
  operateWidth: '100',
  showSort: false,
})
const columnList1 = ref([
  {
    field: 'remark',
    title: '经纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
const columnList2 = ref([
  {
    field: 'remark',
    title: '纬纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生产通知单号:">
          <template #content>
            <el-input v-model="state.filterData.production_notify_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.weaving_mill_id"
              api="business_unitlist"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-value="val => (state.filterData.unit_name = val.name)"
              @change-input="val => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents v-model="state.filterData.status" multiple api="getAuditStatusEnums" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
      <!-- <BottonExcel :loading="loadingExcel" @onClickExcel="handleExport" title="导出文件"></BottonExcel> -->
      <!-- <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">新建</el-button> -->
      </template>
      <Table ref="tableRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
        <!-- 单据编号 -->
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="getTabsData(row.id)">
            {{ row.order_no }}
          </el-link>
        </template>
        <!-- 生产通知单号 -->
        <template #production_notify_order_no="{ row }">
          <el-link type="primary" @click="jumpNotifyDetail(row.production_notify_order_id)">
            {{ row.production_notify_order_no }}
          </el-link>
        </template>
        <!-- 变更匹数 -->
        <template #change_roll="{ row }">
          <span v-if="row.change_roll > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_roll }}</span>
          <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon> {{ Math.abs(row?.change_roll) }}</span>
          <span v-else>0</span>
        </template>
        <!-- 变更数量 -->
        <template #change_weight="{ row }">
          <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
          <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon> {{ Math.abs(row?.change_weight) }}</span>
          <span v-else>0</span>
          {{ row.unit_name }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'ProductionChange_detail'" type="primary" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="!(row.status === 2 || row.status === 4)" v-has="'ProductionChange_edit'" type="primary" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.status === 1" v-has="'ProductionChange_pass'" type="primary" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.status === 2" v-has="'ProductionChange_wait'" type="primary" @click="handApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom" :tool-bar="false">
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane label="坯布信息" name="one">
          <Table :config="columnList_fabic_config" :table-list="state.tabs.greyList" :column-list="columnList_fabic">
            <!-- 销售计划单号 -->
            <template #sale_plan_order_no="{ row }">
              <el-link v-if="row?.production_notify_grey_fabric_detail?.length" @click="customSaleShow(row)">
                查看
              </el-link>
            </template>
            <!-- 变更匹数 -->
            <template #change_roll="{ row }">
              <span v-if="row.change_roll > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_roll }}</span>
              <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon>
                {{ Math.abs(row?.change_roll) }}
              </span>
              <span v-else>0</span>
            </template>
            <!-- 变更数量 -->
            <template #change_weight="{ row }">
              <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
              <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon>
                {{ Math.abs(row?.change_weight) }}
              </span>
              <span v-else>0</span>
              {{ row.unit_name }}
            </template>
            <!-- 最终匹数 -->
            <template #final_roll="{ row }">
              <span>{{ row.scheduling_roll + Number(row.change_roll) }}</span>
              <span v-if="row.change_roll">
                <span v-if="row.change_roll > 0">
                  （
                  <span style="color: green">
                    <el-icon><Top /></el-icon>
                    {{ Number(row.change_roll) }}
                  </span>
                  ）
                </span>
                <span v-if="row.change_roll < 0">
                  （
                  <span style="color: red">
                    <el-icon><Bottom /></el-icon>
                    {{ Number(row.change_roll) }}
                  </span>
                  ）
                </span>
              </span>
            </template>
            <!-- 排产数量 -->
            <template #scheduling_weight="{ row }">
              <span>{{ ((row.scheduling_roll + Number(row.change_roll)) * Number(row.weight_of_fabric)).toFixed(2) }}kg</span>
              <span v-if="row.change_roll">
                <span v-if="row.change_roll > 0">
                  （
                  <span style="color: green">
                    <el-icon><Top /></el-icon>
                    {{ (Number(row.weight_of_fabric) * Number(row.change_roll)).toFixed(2) }}kg
                  </span>
                  ）
                </span>
                <span v-if="row.change_roll < 0">
                  （
                  <span style="color: red">
                    <el-icon><Bottom /></el-icon>
                    {{ (Number(row.weight_of_fabric) * Number(row.change_roll)).toFixed(2) }}kg
                  </span>
                  ）
                </span>
              </span>
            </template>
            <template #grey_fabric_width="{ row }">
              {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
            </template>
            <template #grey_fabric_gram_weight="{ row }">
              {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
            </template>
            <template #finish_product_width="{ row }">
              {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
            </template>
            <template #finish_product_gram_weight="{ row }">
              {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
            </template>
          </Table>
        </el-tab-pane>
        <el-tab-pane label="用料比例" name="two">
          <Table :config="columnList_material_config" :table-list="state.tabs.materialList" :column-list="columnList_material">
            <!-- 用纱量 -->
            <template #use_yarn_quantity="{ row }">
              <div>
                {{ row.use_yarn_quantity }}{{ row.unit_name }}
              </div>
            </template>
            <!-- 发纱量 -->
            <template #send_yarn_quantity="{ row }">
              <div>{{ row.send_yarn_quantity }}{{ row.unit_name }}</div>
            </template>
            <!-- 变更发纱量 -->
            <template #change_send_yarn_quantity="{ row }">
              <span v-if="row.change_send_yarn_quantity > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_send_yarn_quantity }}{{ row.unit_name }}</span>
              <span v-else-if="row.change_send_yarn_quantity < 0" class="negative">
                <el-icon><Bottom /></el-icon>
                {{ Math.abs(row?.change_send_yarn_quantity) }}
                {{ row.unit_name }}
              </span>
              <span v-else>0{{ row.unit_name }}</span>
            </template>
            <!-- 变更用纱量 -->
            <template #change_use_yarn_quantity="{ row }">
              <span v-if="row.change_use_yarn_quantity > 0" class="positive_number">
                <el-icon><Top /></el-icon>
                {{ row?.change_use_yarn_quantity }}{{ row.unit_name }}
              </span>
              <span v-else-if="row.change_use_yarn_quantity < 0" class="negative">
                <el-icon><Bottom /></el-icon>
                {{ Math.abs(row?.change_use_yarn_quantity) }}
                {{ row.unit_name }}</span>
              <span v-else>0{{ row.unit_name }}</span>
            </template>
            <!-- 织厂出料 -->
            <template #mill_private_yarn="{ row }">
              <el-checkbox v-model="row.mill_private_yarn" disabled />
            </template>
          </Table>
        </el-tab-pane>
        <el-tab-pane label="工艺要求" name="three">
          <el-tabs v-model="customActiveName" class="demo-tabs">
            <el-tab-pane label="织造工艺" name="first">
              <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
                <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :label="`${item.text}:`" :copies="item.copies || 1">
                  <template #content>
                    {{ state.tabs.technologicalRequirementForm[item.key] }}{{ String(state.tabs.technologicalRequirementForm[item.key]) && (item.unit || '') }}
                  </template>
                </DescriptionsFormItem>
              </div>
            </el-tab-pane>
            <el-tab-pane label="梭织工艺" name="second">
              <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
                <DescriptionsFormItem label="经密:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.warp_density) }}根/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="纬密:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.weft_density) }}根/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="钢筘内幅:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.reed_inner_width) }}cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="钢筘边幅:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.reed_outer_width) }}cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="筘号:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.reed_no) }}齿/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="穿入数:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.penetration_number) }}穿
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="上机纬密:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.upper_weft_density) }}牙
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="下机纬密:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.lower_weft_density) }}根/cm
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="理论坯布克重:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.gf_theory_gram_width) }}g/m
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="总经根数:">
                  <template #content>
                    {{ formatPriceDiv(TechnologicalRequirementData.total_warp_pieces) }}根
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="经纱排列:" copies="2">
                  <template #content>
                    {{ TechnologicalRequirementData.warp_arrangement }}
                  </template>
                </DescriptionsFormItem>
                <DescriptionsFormItem label="纬纱排列:" copies="2">
                  <template #content>
                    {{ TechnologicalRequirementData.weft_arrangement }}
                  </template>
                </DescriptionsFormItem>
              </div>
              <Table :config="tableConfig1" :table-list="TechnologicalRequirementData.warp_datas" :column-list="columnList1" />
              <Table :config="tableConfig1" :table-list="TechnologicalRequirementData.weft_datas" :column-list="columnList2" />
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>
    </FildCard>
  </div>
  <RelevanceSalesPlanOrder ref="SaleAllocationRef" type="detail" @handle-sure="() => {}" />
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

::v-deep(.el-button.el-button--danger.is-text) {
  padding: 0;
  font-size: 14px;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;
  font-size: 14px;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 80px;
      text-align: right;
    }
  }
}

.positive_number {
  color: green;
}

.negative {
  color: red;
}
</style>
