import { useRequest } from '@/use/useRequest'

// 获取列表
export const getProductionShortageOrderList = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/getProductionShortageOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addProductionShortageOrder = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/addProductionShortageOrder',
    method: 'post',
  })
}

// 获取详情
export const getProductionShortageOrder = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/getProductionShortageOrder',
    method: 'get',
  })
}

// 作废
export const updateProductionShortageOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/updateProductionShortageOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateProductionShortageOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/updateProductionShortageOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateProductionShortageOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/updateProductionShortageOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateProductionShortageOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/updateProductionShortageOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateProductionShortageOrder = () => {
  return useRequest({
    url: '/admin/v1/produce/productionShortageOrder/updateProductionShortageOrder',
    method: 'put',
  })
}

// 获取采购收货单列表
export const getPurchaseReceiveOrderList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_receive_order/item_list_enum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
