import { useRequest } from '@/use/useRequest'

// 获取档案/基础资料/销售发货类型的列表数据 /admin/v1/info_basic_data/infoSaleShipmentType/list
export function GetInfoSaleShipmentTypeList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleShipmentType/list',
    method: 'get',
  })
}

// 获取档案/基础资料/销售发货类型的新增 /admin/v1/info_basic_data/infoSaleShipmentType/addInfoSaleShipmentType
export function AddInfoSaleShipmentType() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleShipmentType/addInfoSaleShipmentType',
    method: 'post',
  })
}

// 获取档案/基础资料/销售发货类型的删除 /hcscm/admin/v1/info_basic_data/infoBasicDefect
export function DeleteInfoSaleShipmentType() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleShipmentType/deleteInfoSaleShipmentType',
    method: 'delete',
  })
}

// 获取档案/基础资料/销售发货类型的详情 /admin/v1/info_basic_data/infoSaleShipmentType/getInfoSaleShipmentType
export function GetInfoSaleShipmentType() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleShipmentType/getInfoSaleShipmentType',
    method: 'get',
  })
}

// 获取档案/基础资料/销售发货类型的编辑 /admin/v1/info_basic_data/infoSaleShipmentType/updateInfoSaleShipmentType
export function UpdateInfoSaleShipmentType() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleShipmentType/updateInfoSaleShipmentType',
    method: 'put',
  })
}

// 发货类型的类型
export interface ShipmentInfosType {
  code: string | undefined // 发货类型编码
  create_time: string | undefined // 创建时间
  creator_id: number | undefined // 创建人id
  creator_name: string | undefined // 创建人
  id: number | undefined // id
  name: string | undefined // 发货类型名称
  order_type: number[] | undefined // 订单类型
  order_type_name: string[] // 订单类型名称
  out_order_type: number | undefined // 出仓单类型 id
  out_order_type_name: string | undefined // 出仓单类型名称
  sale_system_id: number[] // 营销体系id
  sale_system_name: string[] // 营销体系名称
  update_time: string // 更新时间
  update_user_name: string | undefined // 更新人
  updater_id: number | undefined // 更新人id
  ware_house_in_id: number | undefined // 入库单类型id
  ware_house_in_name: string | undefined // 入库单类型名称
}
