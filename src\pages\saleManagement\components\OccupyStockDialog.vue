<!--
  @LastEditTime: 2025-05-09 09:42:37
  @Description: 查看占用库存弹窗
 -->

<script setup lang="ts">
import { ElLink, ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { formatHashTag } from '@/common/format'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import type { TableColumn } from '@/components/Table/type'
import { GetStockProductDetailInfo } from '@/api/finishedGoodsInventory'
import { BookOrderTypeEnum } from '@/enum/orderEnum'

const state = reactive<any>({
  filterData: {
    product_id: '',
    product_color_id: '',
    customer_id: '',
    warehouse_id: '',
    dyelot_number: '',
    product_level_id: '',
    product_remark: '',
  },
  showModal: false,
  baseData: {},
  modalName: '查看已占用的库存',
  list: [],
})

const form_options = [
  {
    text: '成品名称',
    key: 'product_name',
    formatMethod: (val: any) => {
      return formatHashTag(val?.product_code, val?.product_name)
    },
  },
  {
    text: '色号名称',
    key: 'product_color_name',
    formatMethod: (val: any) => {
      return formatHashTag(val?.product_color_code, val?.product_color_name)
    },
  },
  {
    text: '所属客户',
    key: 'customer_name',
  },
  {
    text: '所属仓库',
    key: 'warehouse_name',
  },
  {
    text: '染厂缸号',
    key: 'dyelot_number',
  },
  {
    text: '成品等级',
    key: 'product_level_name',
  },
  {
    text: '成品备注',
    key: 'product_remark',
  },
]

const { fetchData, data, success, msg } = GetStockProductDetailInfo()

const tableConfig = reactive<any>({
  showSlotNums: true,
  filterStatus: false,
  height: '100%',
})

async function getData() {
  await fetchData({
    stock_id: state.baseData.id,
    warehouse_id: state.baseData.warehouse_id,
  })
  if (!success.value)
    ElMessage.error(msg.value || '获取失败')
}
watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

watch(
  () => data.value.list,
  () => {
    state.list = data.value.list
  },
)
const router = useRouter()
// 跳转预约/销售单
function handleSaleOrder(row: any) {
  const isSaleOrder = [BookOrderTypeEnum.ProductSalePass].includes(row.order_type)
  const routerName = isSaleOrder ? 'ProductSaleDetail' : 'FpSubscribeWarehouseOrderDetail'
  if (!row.order_id)
    return
  router.push({
    name: routerName,
    query: {
      id: row.order_id,
      isClearOldPage: '0',
    },
  })
}

// 跳转配布单
function handleArrangeOrder(row: any) {
  if (!row.arrange_order_id)
    return
  router.push({
    name: 'CashCommodityClothOrderDetail',
    query: {
      id: row.arrange_order_id,
    },
  })
}

const columnList = ref<TableColumn[]>([
  {
    field: 'create_time',
    minWidth: 100,
    title: '下单时间',
    isDate: true,
  },
  {
    field: 'order_no',
    minWidth: 100,
    title: '预约/销售单',
    soltName: 'order_no',
  },
  {
    field: 'arrange_order_no',
    minWidth: 100,
    title: '配布单',
    soltName: 'arrange_order_no',
  },
  {
    field: 'sale_user_name',
    minWidth: 100,
    title: '销售员',
  },
  {
    field: 'sale_follow_name',
    minWidth: 100,
    title: '销售跟单',
  },
  {
    field: 'book_roll',
    minWidth: 100,
    title: '占用条数',
    isPrice: true,
  },
  {
    field: 'book_weight',
    minWidth: 100,
    title: '占用数量',
    isWeight: true,
  },

])

function handCancel() {
  state.showModal = false
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal
    v-model="state.showModal" show-footer :title="state.modalName" width="65vw" height="70vh" :mask="false"
    :lock-view="false" :esc-closable="true" resize :z-index="9999"
  >
    <div class="list-page">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :label="`${item.text}:`">
          <template #content>
            <template v-if="item.formatMethod">
              {{ item.formatMethod(state?.baseData) }}
            </template>
            <template v-else>
              {{ state?.baseData[item.key] }}
            </template>
          </template>
        </DescriptionsFormItem>
      </div>

      <FildCard title="" :tool-bar="false" class="table-card-full">
        <Table :config="tableConfig" :table-list="data?.book_details || []" :column-list="columnList" class="flex-1 overflow-hidden">
          <template #order_no="{ row }">
            <ElLink type="primary" @click="handleSaleOrder(row)">
              {{ row.order_no }}
            </ElLink>
          </template>
          <template #arrange_order_no="{ row }">
            <ElLink type="primary" @click="handleArrangeOrder(row)">
              {{ row.arrange_order_no }}
            </ElLink>
          </template>
        </Table>
      </FildCard>
    </div>
    <template #footer>
      <el-button type="primary" plain @click="handCancel">
        我知道了
      </el-button>
    </template>
  </vxe-modal>
</template>
