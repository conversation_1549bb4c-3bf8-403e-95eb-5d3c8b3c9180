<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'

// Props 定义
interface Props {
  modelValue: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '停机备注',
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: ShutdownRemarkData]
}>()

// 停机备注数据接口
export interface ShutdownRemarkData {
  shutdownDate: string // 停机日期
  isShutdown: boolean // 停机状态
  remark: string // 停机备注
}

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 表单数据
const formData = reactive<ShutdownRemarkData>({
  shutdownDate: '',
  isShutdown: true,
  remark: '',
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  shutdownDate: [
    { required: true, message: '请选择停机日期', trigger: 'change' },
  ],
  remark: [
    { required: true, message: '请输入停机备注', trigger: 'blur' },
    { min: 1, max: 200, message: '备注长度在 1 到 200 个字符', trigger: 'blur' },
  ],
}

// 重置表单
function resetForm() {
  formData.shutdownDate = ''
  formData.isShutdown = true
  formData.remark = ''
  formRef.value?.clearValidate()
}

// 确定按钮处理
async function handleConfirm() {
  try {
    await formRef.value?.validate()

    // 发送确认事件
    emit('confirm', {
      shutdownDate: formData.shutdownDate,
      isShutdown: formData.isShutdown,
      remark: formData.remark,
    })

    // 关闭弹窗
    visible.value = false

    // 重置表单
    resetForm()
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消按钮处理
function handleCancel() {
  visible.value = false
  resetForm()
}

// 监听弹窗关闭，重置表单
function handleClose() {
  resetForm()
}
</script>

<template>
  <vxe-modal
    v-model="visible"
    :title="title"
    width="500px"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    destroy-on-close
    show-footer
    @close="handleClose"
  >
    <div class="shutdown-remark-dialog">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <!-- 停机日期 -->
        <el-form-item label="停机日期" prop="shutdownDate">
          <el-date-picker
            v-model="formData.shutdownDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <!-- 停机状态 -->
        <el-form-item label="停机状态">
          <el-switch
            v-model="formData.isShutdown"
            active-text="已停机"
            inactive-text="未停机"
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>

        <!-- 停机备注 -->
        <el-form-item label="停机备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入停机备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </vxe-modal>
</template>

<style scoped>
.shutdown-remark-dialog {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-switch__label) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-switch__label.is-active) {
  color: #409eff;
}
</style>
