# 织造配置枚举使用说明

## 概述

本文档说明了织造配置中使用的枚举类型，包括坯布位数（DecimalPoint）和进位方式（Carry）。

## 枚举定义

### DecimalPoint - 坯布位数枚举

```typescript
export enum DecimalPoint {
  DecimalPointOne = 0,   // 一位小数
  DecimalPointTwo = 1,   // 两位小数
  DecimalPointThree = 2, // 三位小数
  DecimalPointFour = 3,  // 四位小数
}

export const DecimalPointLabels: Record<DecimalPoint, string> = {
  [DecimalPoint.DecimalPointOne]: '1位小数',
  [DecimalPoint.DecimalPointTwo]: '2位小数',
  [DecimalPoint.DecimalPointThree]: '3位小数',
  [DecimalPoint.DecimalPointFour]: '4位小数',
}
```

### Carry - 进位方式枚举

```typescript
export enum Carry {
  CarryOne = 0,      // 一进位
  CarryTwo = 1,      // 两进位
  CarryThree = 2,    // 三进位
  CarryFour = 3,     // 四进位
  CarryRoundOff = 4, // 四舍五入
}

export const CarryLabels: Record<Carry, string> = {
  [Carry.CarryOne]: '逢1进位',
  [Carry.CarryTwo]: '逢2进位',
  [Carry.CarryThree]: '逢3进位',
  [Carry.CarryFour]: '逢4进位',
  [Carry.CarryRoundOff]: '四舍五入',
}
```

## 使用方法

### 1. 导入枚举

```typescript
import { DecimalPoint, DecimalPointLabels, Carry, CarryLabels } from '@/common/enum'
```

### 2. 在组件中使用

#### 下拉选择框

```vue
<el-select v-model="selectedDecimalPoint">
  <el-option
    v-for="(label, value) in DecimalPointLabels"
    :key="value"
    :label="label"
    :value="Number(value)"
  />
</el-select>
```

#### 单选按钮组

```vue
<el-radio-group v-model="selectedCarry">
  <el-radio
    v-for="(label, value) in CarryLabels"
    :key="value"
    :value="Number(value)"
  >
    {{ label }}
  </el-radio>
</el-radio-group>
```

### 3. 数据格式化

```typescript
// 格式化显示文本
const decimalPlacesText = computed(() => {
  const value = props.modelValue?.fabric_decimal_places
  if (value === undefined || value === null)
    return '-'
  return DecimalPointLabels[Number(value) as DecimalPoint] || '-'
})

const roundingTypeText = computed(() => {
  const value = props.modelValue?.rounding_type
  if (value === undefined || value === null)
    return '-'
  return CarryLabels[Number(value) as Carry] || '-'
})
```

## API接口字段映射

在与后端API交互时，需要注意字段名称的映射：

| 前端字段名 | API字段名 | 说明 |
|-----------|----------|------|
| `fabric_decimal_places` | `decimal_point` | 坯布位数 |
| `rounding_type` | `carry` | 进位方式 |
| `fabric_weight_min` | `fabric_min_weight` | 坯布重量最小值 |
| `fabric_weight_max` | `fabric_max_weight` | 坯布重量最大值 |

### 提交数据示例

```typescript
const submitData = {
  // 其他字段...
  decimal_point: weavingConfigData.value.fabric_decimal_places,
  carry: weavingConfigData.value.rounding_type,
  fabric_min_weight: Number(weavingConfigData.value.fabric_weight_min),
  fabric_max_weight: Number(weavingConfigData.value.fabric_weight_max),
}
```

### 接收数据示例

```typescript
// 从API获取数据后的映射
weavingConfigData.value.fabric_decimal_places = result.data.decimal_point
weavingConfigData.value.rounding_type = result.data.carry
weavingConfigData.value.fabric_weight_min = result.data.fabric_min_weight
weavingConfigData.value.fabric_weight_max = result.data.fabric_max_weight
```

## 数字输入组件改进

### 坯布重量限制输入框

我们将原来的 `el-input` 组件替换为 `el-input-number` 组件，提供了以下改进：

#### 功能特性

1. **数字验证**: 自动限制只能输入数字
2. **最小值限制**: 设置 `min="0"`，不允许输入负数
3. **精度控制**: 设置 `precision="2"`，支持两位小数
4. **单位显示**: 使用 `suffix` 插槽显示 "KG" 单位
5. **控制按钮**: 设置 `controls-position="right"`，在右侧显示增减按钮

#### 代码示例

```vue
<el-input-number
  :model-value="Number(modelValue?.fabric_weight_min) || undefined"
  placeholder="最小重量"
  :disabled="disabled"
  :min="0"
  :precision="2"
  controls-position="right"
  style="width: 130px !important"
  @update:model-value="updateField('fabric_weight_min', $event)"
>
  <template #suffix>
    KG
  </template>
</el-input-number>
```

#### 优势对比

| 特性 | 原 el-input | 新 el-input-number |
|------|-------------|-------------------|
| 数字验证 | 手动实现 | 内置支持 |
| 最小值限制 | 无 | 支持 min 属性 |
| 精度控制 | 无 | 支持 precision 属性 |
| 增减控制 | 无 | 内置增减按钮 |
| 单位显示 | 支持 | 支持 |
| 用户体验 | 一般 | 更好 |

## 注意事项

1. **类型安全**: 使用枚举可以确保类型安全，避免使用魔法数字
2. **国际化**: 标签文本已经中文化，便于用户理解
3. **扩展性**: 如需添加新的选项，只需在枚举中添加新值并更新对应的标签映射
4. **一致性**: 确保前端和后端使用相同的枚举值
5. **数字输入**: 使用 `el-input-number` 组件确保数据类型正确性和用户体验

## 相关文件

- `src/common/enum.ts` - 枚举定义
- `src/pages/contactUnitMange/customerMange/components/WeavingConfig/index.vue` - 织造配置组件
- `src/pages/contactUnitMange/customerMange/add.vue` - 客户新增页面
- `src/pages/contactUnitMange/customerMange/edit.vue` - 客户编辑页面
