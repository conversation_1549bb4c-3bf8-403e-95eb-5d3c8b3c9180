import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function getSaleProductOrderList() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/getSaleProductOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export function getSaleProductOrderListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/sale/saleProductOrder/getSaleProductOrderList',
    method: 'get',
    nameFile,
  })
}

// 新增数据
export function addSaleProductOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/addSaleProductOrder',
    method: 'post',
  })
}

// 更新数据
export function updateSaleProductOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/updateSaleProductOrder',
    method: 'put',
  })
}

// 详情
export function getSaleProductOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/getSaleProductOrder',
    method: 'get',
  })
}

// 审核
export function updateSaleProductOrderAuditStatusPass() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusPass',
    method: 'put',
  })
}

// 消审
export function updateSaleProductOrderAuditStatusWait() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusWait',
    method: 'put',
  })
}

// 作废
export function updateSaleProductOrderAuditStatusCancel() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusCancel',
    method: 'put',
  })
}

// 驳回
export function updateSaleProductOrderAuditStatusReject() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusReject',
    method: 'put',
  })
}

// 获取成品欠货单枚举接口
export function getShortageProductOrderDropdownList() {
  return useRequest({
    url: '/admin/v1/sale/shortageProductOrder/getShortageProductOrderDetailDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 获取 上次价 http://192.168.1.24:50001/hcscm/admin/v1/sale/saleProductOrder/getLastSalePrice?customer_id=1776291434889216&product_color_id=1752254262521984
export function getNextSalePrice() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/getLastSalePrice',
    method: 'get',
  })
}

// 获取 历史销售单  /hcscm/admin/v1/sale/saleProductOrder/getHistorySaleOrderList
export function getHistorySaleOrderList() {
  return useRequest({
    url: '/admin/v1/sale/saleProductOrder/getHistorySaleOrderList',
    method: 'get',
  })
}

// 智能下单
export function GetAISaleProductOrder() {
  return useRequest<Api.GetAISaleProductOrder.Request, Api.GetAISaleProductOrder.Response>({
    url: '/admin/v1/ai/order/saleProductOrder',
    method: 'get',
  })
}
