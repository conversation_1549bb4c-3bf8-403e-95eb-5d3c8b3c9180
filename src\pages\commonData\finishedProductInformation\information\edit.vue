<script lang="ts" setup name="FinishedProductInformationEdit">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { throttle } from 'lodash-es'
import { GetFinishProduct, UpdateFinishProduct } from '@/api/finishedProductInformation'
import { BusinessUnitIdEnum, DictionaryType, EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import { formatTwoDecimalsDiv, formatTwoDecimalsMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FibreModel from '@/components/FibreCount/modal/index.vue'
import FildCard from '@/components/FildCard.vue'
import SelectCascader from '@/components/SelectCascader/productType.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import useRouterList from '@/use/useRouterList'
import { SearchForSomeProductField } from '@/api/search'
import SelectDialog from '@/components/SelectDialog/index.vue'
import TextureMapWall from '@/components/TextureMapWall/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    finish_product_code: '',
    finish_product_name: '',
    finish_product_full_name: '',
    type_grey_fabric_id: '',
    type_grey_fabric_name: '',
    finish_product_ingredient: '',
    measurement_unit_name: '',
    measurement_unit_id: '',
    grey_fabric_code: '',
    grey_fabric_name: '',
    is_color_card: false,
    warehouse_id: '',
    warehouse_name: '',
    storage_area: '',
    dye_factory_order_follower_id: '',
    remark: '',
    finish_product_width: '',
    finish_product_gram_weight: '',
    length_to_weight_rate: '',
    standard_weight: '',
    paper_tube_weight: '',
    weight_error: '',
    finish_product_craft: '',
    dyeing_loss: '',
    touch_style: '',
    center: '',
    size: '',
    texture_url: [],
    ingredient_item: [] as any[],
    yarn_count: '',
    supplier_id: [],
    finish_product_width_unit_id: '',
    finish_product_gram_weight_unit_id: '',
  },
  formRules: {
    finish_product_name: [{ required: true, message: '请输入成品名称', trigger: 'blur' }],
    type_grey_fabric_id: [{ required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value)
        return callback(new Error('请选布种类型'))
      callback()
    } }],
    measurement_unit_id: [{ required: true, message: '请选择单位', trigger: 'blur' }],
    finish_product_ingredient: [{ require: true, message: '请输入成品成分', trigger: 'blur' }],
    // grey_fabric_code: [{ required: true, message: '请选择坯布编号', trigger: 'blur' }],
    // grey_fabric_name: [{ required: true, message: '请选择坯布名称', trigger: 'blur' }],
  },
})

const ruleFormRef = ref()
const fileList: any = defineModel()
const route = useRoute()
const { fetchData, data: dataInfo, success, msg } = GetFinishProduct()
onMounted(() => {
  getData()
})

async function getData() {
  await fetchData({ id: route.params.id })
  if (!success.value)
    return ElMessage.error(msg.value)

  state.form.finish_product_code = dataInfo.value.finish_product_code
  state.form.finish_product_name = dataInfo.value.finish_product_name
  state.form.finish_product_full_name = dataInfo.value.finish_product_full_name
  state.form.type_grey_fabric_id = dataInfo.value.type_grey_fabric_id || ''
  state.form.type_grey_fabric_name = dataInfo.value.type_grey_fabric_name
  state.form.finish_product_ingredient = dataInfo.value.finish_product_ingredient
  state.form.measurement_unit_name = dataInfo.value.measurement_unit_name
  state.form.measurement_unit_id = dataInfo.value.measurement_unit_id
  state.form.grey_fabric_code = dataInfo.value.grey_fabric_code
  state.form.grey_fabric_name = dataInfo.value.grey_fabric_name
  state.form.is_color_card = dataInfo.value.is_color_card
  state.form.warehouse_id = dataInfo.value.warehouse_id
  state.form.warehouse_name = dataInfo.value.warehouse_name
  state.form.storage_area = dataInfo.value.storage_area
  state.form.yarn_count = dataInfo.value.yarn_count
  state.form.supplier_id = dataInfo.value.supplier_id
  state.form.dye_factory_order_follower_id = dataInfo.value.dye_factory_order_follower_id
  state.form.remark = dataInfo.value.remark
  state.form.finish_product_width = dataInfo.value.finish_product_width
  state.form.finish_product_gram_weight = dataInfo.value.finish_product_gram_weight
  state.form.length_to_weight_rate = formatTwoDecimalsDiv(dataInfo.value.length_to_weight_rate)
  state.form.standard_weight = formatWeightDiv(dataInfo.value.standard_weight)
  state.form.paper_tube_weight = formatWeightDiv(dataInfo.value.paper_tube_weight)
  state.form.weight_error = formatWeightDiv(dataInfo.value.weight_error)
  state.form.finish_product_craft = dataInfo.value.finish_product_craft
  state.form.tape_specs = dataInfo.value.tape_specs
  state.form.paper_tube_specs = dataInfo.value.paper_tube_specs
  state.form.dyeing_loss = formatTwoDecimalsDiv(dataInfo.value.dyeing_loss)
  state.form.touch_style = dataInfo.value.touch_style
  state.form.grey_fabric_id = dataInfo.value.grey_fabric_id
  state.form.size = dataInfo.value.size
  state.form.center = dataInfo.value.center
  state.form.type_grey_fabric_name = dataInfo.value.type_grey_fabric_name
  state.form.type_grey_fabric_code = dataInfo.value.type_grey_fabric_code
  state.form.density = dataInfo.value.density
  // state.form.texture_url = dataInfo.value.texture_url
  fileList.value = dataInfo.value.texture_url
  state.form.cover_texture_url = dataInfo.value.cover_texture_url
  state.form.ingredient_item = dataInfo.value.ingredient_item?.map((item: any) => ({ ...item, count: formatTwoDecimalsDiv(item.count) }))
  state.form.finish_product_width_unit_id = dataInfo.value.finish_product_width_unit_id
  state.form.finish_product_gram_weight_unit_id = dataInfo.value.finish_product_gram_weight_unit_id
  state.form.bleach_id = dataInfo.value.bleach_id
  state.form.weaving_organization_id = dataInfo.value.weaving_organization_id
  state.form.shrinkage_warp = dataInfo.value.shrinkage_warp
}

const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
  grey_fabric_code: '',
  grey_fabric_name: '',
})

// watch(
//   () => dataInfo.value,
//   (val: any) => {}
// )

// 提交表格数据
async function handleSure() {
  ruleFormRef.value.validate((valid: any) => {
    if (valid)
      handSubmit()
  })
}

const { fetchData: postFetch, data: addData, success: postSuccess, msg: postMsg } = UpdateFinishProduct()

async function handSubmit() {
  if (!state.form.finish_product_ingredient)
    return ElMessage.error('请输入成品成分')

  const data = { ...state.form }
  data.supplier_id = data?.supplier_id.length ? data?.supplier_id.join(',') : ''
  data.length_to_weight_rate = formatTwoDecimalsMul(data.length_to_weight_rate)
  data.paper_tube_weight = formatWeightMul(data.paper_tube_weight)
  data.standard_weight = formatWeightMul(data.standard_weight)
  data.weight_error = formatWeightMul(data.weight_error)
  data.dyeing_loss = formatTwoDecimalsMul(data.dyeing_loss)
  data.finish_product_width_unit_id = Number(data.finish_product_width_unit_id)
  data.finish_product_gram_weight_unit_id = Number(data.finish_product_gram_weight_unit_id)
  data.grey_fabric_id = Number(data.grey_fabric_id)
  data.texture_url = fileList.value ? fileList.value?.join(',') : ''
  data.cover_texture_url = data?.cover_texture_url ? data?.cover_texture_url : state.form.texture_url?.[0]
  data.ingredient_item = data.ingredient_item?.map((item: any) => ({ ...item, count: formatTwoDecimalsMul(item.count) }))
  await postFetch(getFilterData({ ...data, id: Number.parseInt(route.params.id as string) }))
  if (postSuccess.value) {
    ElMessage.success('编辑成功')
    getData()
    routerList.push({
      name: 'FinishedProductInformationDetail',
      params: { id: addData.value.id },
    })
  }
  else {
    ElMessage.error(postMsg.value)
  }
}
function changeValue({ ids, row }: any) {
  state.form.type_grey_fabric_name = row?.name
  state.form.type_grey_fabric_code = row?.code
  state.form.type_grey_fabric_id = ids?.[ids.length - 1] || ''
}

const fibreRef = ref()
function compositionAdd() {
  fibreRef.value.state.showModal = true
  fibreRef.value.state.defaultList = state.form.ingredient_item
}

function handleSureData(val: any) {
  state.form.finish_product_ingredient = val?.composition
  state.form.ingredient_item = val?.list
  fibreRef.value.state.showModal = false
}

const { fetchData: searchCodes, data: searchedData } = SearchForSomeProductField()
interface filterData {
  id?: string
  finish_product_code?: any
}
const filterDatas = ref<filterData[]>([])
async function getSearchedCodes(val: string) {
  // 节流
  throttle(async () => {
    await searchCodes({
      finish_product_code: val,
    })
    filterDatas.value = searchedData.value?.list || []
  }, 500)()
}

function selectConfirm(e: any) {
  const value = e.target.value.replace(/#/g, '') // 移除#符号
  if (value) {
    // 检查是否包含非法字符
    if (/#/.test(value)) {
      ElMessage.error('编号中不能包含#符号')
      state.form.finish_product_code = ''
      return
    }
    state.form.finish_product_code = value
  }
}

// 添加输入时实时过滤
function handleCodeInput(visible: boolean) {
  if (!visible && state.form.finish_product_code)
    selectConfirm({ target: { value: state.form.finish_product_code } })
}

function greyFabricChange(val: any) {
  state.form.grey_fabric_name = val.name
  state.form.grey_fabric_code = val.code
  // 成品成分
  state.form.finish_product_ingredient = val.grey_fabric_composition
  // 密度
  state.form.density = val.density
  // 纱支
  state.form.yarn_count = val.yarn_count
  state.form.finish_product_width = val.finish_product_width
  state.form.finish_product_width_unit_id = val.finish_product_width_unit_id
  state.form.finish_product_gram_weight = val.finish_product_gram_weight
  state.form.finish_product_gram_weight_unit_id = val.finish_product_gram_weight_unit_id
  // 缩水率
  state.form.shrinkage_warp = val.shrinkage_warp
}
</script>

<template>
  <FildCard title="成品资料" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <div class="line" />
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号" :required="true">
          <template #content>
            <el-form-item prop="finish_product_code">
              <el-select
                v-model="state.form.finish_product_code"
                filterable
                :filter-method="getSearchedCodes"
                placeholder="成品编号"
                allow-create
                default-first-option
                @blur="selectConfirm"
                @visible-change="handleCodeInput"
              >
                <el-option v-for="item in filterDatas" :key="item?.id" :label="item?.finish_product_code" :value="item?.finish_product_code">
                  <span style="float: left">{{ item?.finish_product_code }}</span>
                  <span style="float: right; color: var(--el-text-color-secondary); font-size: 10px">已存在</span>
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称" :required="true">
          <template #content>
            <el-form-item prop="finish_product_name">
              <el-input v-model.trim="state.form.finish_product_name" style="width: 260px" clearable placeholder="成品名称" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品全称">
          <template #content>
            <el-form-item prop="finish_product_full_name">
              <el-input v-model.trim="state.form.finish_product_full_name" style="width: 260px" clearable placeholder="成品全称" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布种类型" :required="true">
          <template #content>
            <el-form-item prop="type_grey_fabric_id">
              <SelectCascader v-model="state.form.type_grey_fabric_id" style="width: 100%" @change-value="changeValue" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号">
          <template #content>
            <el-form-item prop="grey_fabric_code">
              <SelectDialog
                v-model="state.form.grey_fabric_id"
                :query="{ code: componentRemoteSearch.grey_fabric_code }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                :table-column="[
                  {
                    field: 'code',
                    title: '坯布编号',
                    minWidth: 100,
                  },
                ]"
                api="GetGreyFabricInfoListUseByOthersMenu"
                label-field="code"
                @on-input="val => (componentRemoteSearch.grey_fabric_code = val)"
                @change-value="greyFabricChange"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称">
          <template #content>
            <el-form-item prop="grey_fabric_name">
              <SelectDialog
                v-model="state.form.grey_fabric_id"
                api="GetGreyFabricInfoListUseByOthersMenu"
                :query="{ name: componentRemoteSearch.grey_fabric_name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                :table-column="[
                  {
                    field: 'name',
                    title: '坯布名称',
                  },
                ]"
                @change-value="greyFabricChange"
                @on-input="val => (componentRemoteSearch.grey_fabric_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品成分" :required="true">
          <template #content>
            <el-form-item prop="finish_product_ingredient">
              <div class="flex">
                <el-input v-model.trim="state.form.finish_product_ingredient" style="width: 260px" clearable placeholder="成品成分" class="input-with-select">
                  <template #append>
                    <el-button :icon="Plus" style="width: 70px" @click="compositionAdd" />
                  </template>
                </el-input>
              </div>
            </el-form-item>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="单位" :required="true">
          <template #content>
            <el-form-item prop="measurement_unit_id">
              <SelectComponents
                v-model="state.form.measurement_unit_id"
                style="width: 260px"
                api="getInfoBaseMeasurementUnitList"
                label-field="name"
                value-field="id"
                clearable
                @change-value="val => (state.form.measurement_unit_name = val.name)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="上架商城">
          <template #content>
            <el-form-item prop="is_color_card">
              <el-switch v-model="state.form.is_color_card" active-text="是" inactive-text="否" class="ml-2" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品仓库">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents
                v-model="state.form.warehouse_id"
                style="width: 260px"
                api="GetPhysicalWarehouseDropdownList"
                label-field="name"
                value-field="id"
                clearable
                :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                @change-value="val => (state.form.warehouse_name = val.name)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="存放区域">
          <template #content>
            <el-form-item prop="storage_area">
              <el-input v-model.trim="state.form.storage_area" style="width: 260px" clearable placeholder="存放区域" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂跟单">
          <template #content>
            <el-form-item prop="dye_factory_order_follower_id">
              <SelectComponents
                v-model="state.form.dye_factory_order_follower_id"
                api="Adminemployeelist"
                :query="{ duty: EmployeeType.follower }"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="手感风格">
          <template #content>
            <el-form-item prop="paper_tube_specs">
              <el-input v-model.trim="state.form.touch_style" style="width: 260px" clearable placeholder="手感风格" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染损">
          <template #content>
            <el-form-item prop="dyeing_loss">
              <vxe-input v-model.trim="state.form.dyeing_loss" min="0" max="100" style="width: 260px" type="float" clearable placeholder="染损">
                <template #suffix>
                  %
                </template>
              </vxe-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="经纬度">
          <template #content>
            <el-form-item prop="center">
              <el-input v-model.trim="state.form.center" clearable placeholder="经纬度" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="尺寸">
          <template #content>
            <el-form-item prop="size">
              <el-input v-model.trim="state.form.size" clearable placeholder="尺寸" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="密度">
          <template #content>
            <el-form-item prop="size">
              <el-input v-model.trim="state.form.density" clearable placeholder="密度" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="纱支">
          <template #content>
            <el-form-item prop="size">
              <el-input v-model.trim="state.form.yarn_count" clearable placeholder="纱支" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商" check-auth>
          <template #content>
            <el-form-item prop="size">
              <SelectComponents
                v-model="state.form.supplier_id"
                :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct }"
                api="BusinessUnitSupplierEnumAll"
                label-field="name"
                value-field="id"
                multiple
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="漂染性：">
          <template #content>
            <el-form-item prop="bleach">
              <SelectComponents
                v-model="state.form.bleach_id"
                quick-add-link="/systemTools/detail/10009"
                quick-add-premission="Dictionary_add"
                api="GetDictionaryDetailEnumListApi" :query="{ dictionary_id: DictionaryType.bleach }" label-field="name" value-field="id" clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织造组织：">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.form.weaving_organization_id"
                api="GetDictionaryDetailEnumListApi"
                :query="{ dictionary_id: DictionaryType.weavingOrganization }"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缩水率">
          <template #content>
            <el-form-item prop="shrinkage_warp">
              <el-input v-model.trim="state.form.shrinkage_warp" clearable placeholder="缩水率" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model.trim="state.form.remark" type="textarea" clearable placeholder="备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <div class="sale" />
      </div>

      <div class="sale">
        成品规格
      </div>
      <div class="line" />
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品幅宽">
          <template #content>
            <el-form-item prop="finish_product_width">
              <el-input v-model="state.form.finish_product_width" class="my_input" clearable placeholder="成品幅宽">
                <template #append>
                  <SelectComponents
                    v-model="state.form.finish_product_width_unit_id"
                    placeholder="单位"
                    style="width: 80px"
                    :query="{ dictionary_id: DictionaryType.width_unit }"
                    api="GetDictionaryDetailEnumListApi"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </template>
              </el-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品克重">
          <template #content>
            <el-form-item prop="finish_product_gram_weight">
              <el-input v-model.trim="state.form.finish_product_gram_weight" class="my_input" clearable placeholder="成品克重">
                <template #append>
                  <SelectComponents
                    v-model="state.form.finish_product_gram_weight_unit_id"
                    placeholder="单位"
                    style="width: 80px"
                    :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
                    api="GetDictionaryDetailEnumListApi"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </template>
              </el-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="数量转长度比率" width="330">
          <template #content>
            <el-form-item prop="length_to_weight_rate">
              <vxe-input v-model.trim="state.form.length_to_weight_rate" min="0" type="float" clearable placeholder="数量转长度比率">
                <template #suffix>
                  <span class="w-[50px] whitespace-nowrap">米/公斤</span>
                </template>
              </vxe-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="标准数量">
          <template #content>
            <el-form-item prop="standard_weight">
              <vxe-input v-model.trim="state.form.standard_weight" min="0" type="float" clearable placeholder="标准数量">
                <template #suffix>
                  公斤
                </template>
              </vxe-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="纸筒重量">
          <template #content>
            <el-form-item prop="paper_tube_weight">
              <vxe-input v-model.trim="state.form.paper_tube_weight" min="0" type="float" clearable placeholder="纸筒重量">
                <template #suffix>
                  公斤
                </template>
              </vxe-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="空差数量">
          <template #content>
            <el-form-item prop="weight_error">
              <vxe-input v-model.trim="state.form.weight_error" min="0" type="float" clearable placeholder="空差数量">
                <template #suffix>
                  公斤
                </template>
              </vxe-input>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品工艺">
          <template #content>
            <el-form-item prop="finish_product_craft">
              <el-input v-model.trim="state.form.finish_product_craft" clearable placeholder="成品工艺" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>

    <div class="sale">
      纹理图
    </div>
    <div class="line" />
    <TextureMapWall v-model:image-list="fileList" />
  </FildCard>
  <FibreModel ref="fibreRef" @handle-sure="handleSureData" />
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}
.sale {
  width: 100%;
  margin-top: 40px;
  font-weight: 600;
}
.my-search {
  width: 100%;
}
:deep(.my-search .vxe-input--suffix) {
  width: 100px;
  height: 32px;
  top: 1px;
  text-align: center;
  border-left: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  cursor: pointer;
}

::v-deep(.my-search .vxe-input--number-suffix){
  background-color: #f5f7fa;
  height: calc(100% - 2px);
}
:deep(.el-select__selected-item.el-select__input-wrapper){
  width: 80px;
}
::v-deep(.el-input-group__append) {
  padding: 0;
  font-size: 12px;
}
</style>
