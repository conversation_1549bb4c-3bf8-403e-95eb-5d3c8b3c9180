<script setup lang="ts" name="AdjustmentPriceIndex">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { price_list, price_listExport } from '@/api/adjustmentPrice'
import { BusinessUnitSupplierEnumlist } from '@/api/dyeingPrice'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate } from '@/common/format'
import { debounce, getFilterData, resetData } from '@/common/util'
import Accordion from '@/components/Accordion/index.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SideTree from '@/components/SideTree.vue'
import Table from '@/components/Table.vue'

const state = reactive<any>({
  filterData: {
    order_no: '',
    product_id: '',
    quote_order_no: '',
    project_name: '',
    project: '',
    dye_factory_id: '',
    remark: '',
    effect_time: '',
    deadline_time: '',
  },
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = price_list()

const tableConfig = ref({
  fieldApiKey: 'AdjustmentPriceIndex',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showSort: false,
  height: 'auto',
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 获取数据
const getData = debounce(() => {
  const query: any = {
    effect_start_time: state.filterData.effect_time && state.filterData.effect_time !== '' && state.filterData.effect_time.length ? formatDate(state.filterData.effect_time[0]) : '',
    effect_end_time: state.filterData.effect_time && state.filterData.effect_time !== '' && state.filterData.effect_time.length ? formatDate(state.filterData.effect_time[1]) : '',
    deadline_start_time: state.filterData.deadline_time && state.filterData.deadline_time !== '' && state.filterData.deadline_time.length ? formatDate(state.filterData.deadline_time[0]) : '',
    deadline_end_time: state.filterData.deadline_time && state.filterData.deadline_time !== '' && state.filterData.deadline_time.length ? formatDate(state.filterData.deadline_time[1]) : '',
    ...state.filterData,
    dye_factory_id: state.filterData.dye_factory_id.length ? state.filterData.dye_factory_id.join(',') : '',
  }
  delete query.effect_time
  // delete query.dye_factory_id
  delete query.deadline_time
  ApiCustomerList(getFilterData(query))
}, 400)

onMounted(() => {
  getData()
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '后整加工价目表'
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = price_listExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

const { fetchData: getEnum, data: enumData } = BusinessUnitSupplierEnumlist()

async function handleEdit() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请至少勾选一条记录')

  const firstId = state.multipleSelection[0].dye_factory_id

  const allIdsEqual = state.multipleSelection.every((item: any) => item.dye_factory_id === firstId)

  if (!allIdsEqual)
    return ElMessage.error('请选择同一染厂的相关数据')

  const firstQuote_unit = state.multipleSelection[0].quote_unit

  const allQuote_unitEqual = state.multipleSelection.every((item: any) => item.quote_unit === firstQuote_unit)

  if (!allQuote_unitEqual)
    return ElMessage.error('所选记录中染厂计价单位不一致，请分开调价')

  await getEnum({ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: state.multipleSelection[0].dyeing_factory_name })

  if (enumData.value) {
    state.multipleSelection.map((item: any) => {
      item.dnf_charging_method = enumData.value.list[0].dnf_charging_method
      return item
    })
  }

  for (let i = 0; i < state.multipleSelection.length; i++) {
    if (state.multipleSelection[i].dnf_charging_method !== state.multipleSelection[i].quote_unit)
      return ElMessage.error('该染厂计价单位已改变，请直接新建报价')
  }

  const idArr: any = []
  state.multipleSelection.forEach((item: any) => {
    idArr.push(item.item_id)
  })
  router.push({
    name: 'AdjustmentPriceEdit',
    query: {
      ids: idArr.join(','),
      info: JSON.stringify({
        sale_system_name: state.multipleSelection[0].sale_system_name,
        dyeing_factory_name: state.multipleSelection[0].dyeing_factory_name,
        quote_unit_name: state.multipleSelection[0].quote_unit_name,
        quote_unit: state.multipleSelection[0].quote_unit,
        dye_factory_id: state.multipleSelection[0].dye_factory_id,
      }),
    },
  })
}

function handleAdd() {
  router.push({
    name: 'AdjustmentAdd',
  })
}

const columnList = ref([
  {
    field: 'dyeing_factory_code',
    title: '染厂编号',
    minWidth: 120,
  },
  {
    field: 'dyeing_factory_name',
    title: '染厂名称',
    minWidth: 120,
  },

  {
    field: 'project_code',
    title: '项目编号',
    minWidth: 120,
  },
  {
    field: 'project_name',
    title: '项目名称',
    minWidth: 120,
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 120,
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 120,
  },
  {
    field: 'quote_unit_name',
    title: '染厂计价单位',
    minWidth: 120,
  },

  {
    field: 'unit_price',
    title: '单价',
    minWidth: 120,
    isUnitPrice: true,
  },
  {
    field: 'include_tax',
    title: '是否含税',
    minWidth: 120,
    soltName: 'include_tax',
  },
  {
    field: 'product_value',
    title: '计价项目',
    minWidth: 120,
  },

  {
    field: 'remark',
    title: '备注',
    minWidth: 120,
  },
  {
    field: 'order_no',
    title: '系统报价单号',
    width: '8%',
  },
  {
    field: 'quote_order_no',
    title: '染厂报价单号',
    minWidth: 120,
  },
  {
    field: 'quote_date',
    title: '染厂报价日期',
    minWidth: 120,
    is_date: true,
  },
  {
    field: 'effect_time',
    title: '生效时间',
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'deadline',
    title: '截止时间',
    minWidth: 140,
    isDate: true,
  },
  //   {
  //     field: '',
  //     title: '单据状态',
  //     fixed: 'right',
  //     soltName: 'audit_status',
  //     showOrder_status: true,
  //     minWidth: 120,
  //   },
])
</script>

<template>
  <div class="flex flex-col h-full">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="项目名称">
          <template #content>
            <el-input v-model="state.filterData.project_name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="计价项目">
          <template #content>
            <el-input v-model="state.filterData.project" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称">
          <template #content>
            <SelectComponents
              v-model="state.filterData.product_id"
              show-slot
              api="GetFinishProductDropdownList"
              solt-right-label-field="finish_product_name"
              solt-left-label-field="finish_product_code"
              label-field="finish_product_name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="系统报价单号">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂报价单号">
          <template #content>
            <el-input v-model="state.filterData.quote_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生效时间">
          <template #content>
            <SelectDate v-model="state.filterData.effect_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="截止时间">
          <template #content>
            <SelectDate v-model="state.filterData.deadline_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注">
          <template #content>
            <el-input v-model="state.filterData.remark" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-1 overflow-y-hidden">
      <Accordion :open-status="true">
        <SideTree
          v-model="state.filterData.dye_factory_id"
          show-select-all
          api="BusinessUnitSupplierEnumlist"
          select-api="BusinessUnitSupplierEnumlist"
          type="checkbox"
          check-strictly
          highlight-current
          default-expand-all
          :show-input="false"
          value-field="name"
          @select="() => (state.filterData.dye_factory_id = [])"
        />
      </Accordion>
      <FildCard title="" class="mt-[5px] !min-w-[auto] flex-1 overflow-hidden">
        <template #right-top>
          <el-button v-has="'AdjustmentPriceIndex_add'" type="primary" :icon="Plus" @click="handleAdd">
            新建
          </el-button>
          <el-button v-has="'AdjustmentPriceIndex_edit'" plain type="primary" @click="handleEdit">
            调价
          </el-button>
          <BottonExcel v-has="'AdjustmentPriceIndex_export'" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
        </template>
        <div class="h-[95%]">
          <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
            <template #include_tax="{ row }">
              {{ row?.include_tax ? '含税' : '不含税' }}
            </template>
          </Table>
        </div>
      </FildCard>
    </div>
  </div>
</template>
