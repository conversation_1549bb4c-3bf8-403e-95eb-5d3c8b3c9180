<script lang="ts" setup>
import { ref } from 'vue'
import BottomBar from '../SelectComponents/SelectBottomBar.vue'

export interface Props {
  options: []
  placeholder: string
  modelValue: number | string
  multiple: boolean
  size: string
  disabled: boolean
  allowCreate: boolean
  labelField: string
  valueField: string
  labelValue?: string
  visibleChangeClose: boolean
  clearable: boolean
  showSlot: boolean
  soltLeftLabelField: string
  soltRightLabelField: string
  quickAddLink?: string
  quickAddPremission?: string
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  placeholder: '请选择内容',
  modelValue: '',
  multiple: false,
  allowCreate: false,
  size: '',
  disabled: false,
  labelField: 'label',
  valueField: 'value',
  labelValue: '',
  soltLeftLabelField: 'code',
  soltRightLabelField: 'name',
  visibleChangeClose: false,
  clearable: true,
  showSlot: false,
  quickAddLink: '',
  quickAddPremission: '',
})

const emit = defineEmits(['update:modelValue', 'change', 'refresh'])

function handChange(val: any) {
  //

  emit('update:modelValue', val)
  emit('change', val)
}
// watch(
//   () => props.modelValue,
//   val => {
//

//     emit('update:modelValue', val)
//     emit('change', val)
//   }
// )

const selectRef = ref()
function visibleChange(val: boolean) {
  if (val && props.visibleChangeClose)
    selectRef.value.blur()
}

defineExpose({
  selectRef,
})
</script>

<template>
  <el-select
    ref="selectRef"
    popper-class="vxe-table--ignore-clear custom-select"
    filterable
    v-bind="$attrs"
    :allow-create="allowCreate"
    style="width: 100%"
    :multiple="props.multiple"
    :disabled="disabled"
    :clearable="clearable"
    :model-value="modelValue"
    :automatic-dropdown="true"
    default-first-option
    :placeholder="props.placeholder"
    :size="props.size"
    class="el-select"
    :class="`el-select-vxe-${props.size}`"
    @change="handChange"
    @visible-change="visibleChange"
  >
    <el-option v-for="(item, index) in props.options" :key="index" :label="item[props.labelField]" :value="item[props.valueField]">
      <div v-if="props.showSlot">
        <span style="float: left">{{ item[props.soltLeftLabelField] }}</span>
        <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">{{ item[props.soltRightLabelField] }}</span>
      </div>
    </el-option>
    <template #label="{ label, value }">
      <!-- 处理默认值不在list中时，则显示传入的labelValue -->
      <span v-if="label !== value">{{ label }}</span>
      <span v-else>{{ labelValue || label || '' }}</span>
    </template>
    <template #footer>
      <BottomBar :quick-add-link="quickAddLink" :quick-add-premission="quickAddPremission" :="$attrs" @refresh="() => emit('refresh')" />
    </template>
  </el-select>
</template>

<style lang="scss">
.el-select__tags {
  max-width: calc(100% - 40px) !important;
}

// 若是传入的size为small 则跟vxe的一样高度
.el-select-vxe-small .el-select__wrapper{
  height: var(--vxe-ui-input-height-mini);
}

.custom-select {
  .el-select-dropdown__footer:empty {
    display: none; // 隐藏空的footer;
  }
}
</style>
