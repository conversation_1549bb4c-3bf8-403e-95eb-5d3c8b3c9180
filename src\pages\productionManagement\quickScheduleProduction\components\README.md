# 快速排产组件说明

## ShutdownRemarkDialog 停机备注弹窗

### 功能描述
停机备注弹窗组件，用于设置和管理设备的停机信息，包括停机日期、停机状态和备注信息。

### 组件特性
- 基于 vxe-modal 实现的弹窗组件
- 支持表单验证
- 响应式设计
- TypeScript 类型安全

### Props
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | false | 弹窗显示状态 |
| title | string | '停机备注' | 弹窗标题 |

### Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value: boolean | 更新弹窗显示状态 |
| confirm | data: ShutdownRemarkData | 确认提交时触发 |

### 数据结构
```typescript
interface ShutdownRemarkData {
  shutdownDate: string // 停机日期 (YYYY-MM-DD)
  isShutdown: boolean  // 停机状态 (true: 已停机, false: 未停机)
  remark: string       // 停机备注 (1-200字符)
}
```

### 使用示例
```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="showDialog = true">
      停机备注
    </el-button>

    <!-- 停机备注弹窗 -->
    <ShutdownRemarkDialog
      v-model="showDialog"
      title="停机备注"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ShutdownRemarkDialog, { type ShutdownRemarkData } from './ShutdownRemarkDialog.vue'

const showDialog = ref(false)

function handleConfirm(data: ShutdownRemarkData) {
  console.log('停机备注数据:', data)
  // 处理停机备注数据
  // 例如：调用API保存数据
}
</script>
```

### 表单验证规则
- **停机日期**: 必填项
- **停机备注**: 必填项，长度限制 1-200 个字符

### 样式特点
- 弹窗宽度: 500px
- 表单标签宽度: 80px
- 备注输入框: 4行文本域，带字数统计
- 停机状态: 开关组件，显示"已停机"/"未停机"

### 注意事项
1. 弹窗关闭时会自动重置表单数据
2. 表单验证失败时不会关闭弹窗
3. 确认提交后会显示成功消息并关闭弹窗
4. 支持 ESC 键关闭弹窗

### 依赖组件
- Element Plus: el-form, el-form-item, el-date-picker, el-switch, el-input, el-button
- VXE Table: vxe-modal

### 文件位置
```
src/pages/productionManagement/quickScheduleProduction/components/
└── ShutdownRemarkDialog.vue
```

### 更新日志
- 2024-01-XX: 初始版本，实现基础停机备注功能
- 支持停机日期选择、状态切换、备注输入
- 完整的表单验证和错误处理
- TypeScript 类型安全支持
