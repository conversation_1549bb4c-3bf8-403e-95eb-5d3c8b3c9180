<script lang="ts" setup name="BulkSet">
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import { formatHashTag } from '@/common/format'

const emits = defineEmits(['submit', 'close'])
const bulkShow = defineModel<boolean>({ default: false })
const bulkSetting = ref()
function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit(parmas: any) {
  emits('submit', parmas)
}

const bulkList = reactive<any>([
  {
    field: 'customer_account_num',
    title: '款号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'product_level_id',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
  },
  {
    field: 'warehouse_id',
    title: '出货仓库',
    component: 'select',
    api: 'GetPhysicalWarehouseDropdownList',
  },
  {
    field: 'shortage_roll',
    title: '欠货匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'shortage_weight',
    title: '欠货数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'shortage_length',
    title: '欠货辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
</script>

<template>
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  />
</template>

<style></style>
