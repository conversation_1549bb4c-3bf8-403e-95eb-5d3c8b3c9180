<script setup lang="ts" name="FpOtherEntryOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import currency from 'currency.js'
import AccordingLibAdd from '../components/AccordingLibAdd.vue'
import FineSizeAdd from '../components/FineSizeRepertoryAdd.vue'
import { addFpmOtherInOrder } from '@/api/fpOtherEntryOrder'
import { BusinessUnitIdEnum, DictionaryType, EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import {
  formatDate,
  formatLengthMul,
  formatTwoDecimalsMul,
  formatUnitPriceMul,
  formatWeightMul,
} from '@/common/format'
import {
  deepClone,
  deleteToast,
  getDefaultSaleSystem,
  isMainUnit,
} from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { isFinishEnter } from '@/pages/finishManagement/finishPurchaseWarehouseEntry/utils'
import { SaleModeEnum } from '@/enum/orderEnum'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const routerList = useRouterList()
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const formRef = ref()
const state = reactive<any>({
  formInline: {
    sale_system_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    voucher_number: '',
    store_keeper_id: '',
    warehouse_in_time: new Date(),
    remark: '',
    sale_mode: SaleModeEnum.Bulk,
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    biz_unit_id: [{ required: true, message: '请选择发货单位', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库', trigger: 'change' }],
    warehouse_in_time: [{ required: true, message: '请选择进仓日期', trigger: 'change' }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
  defaultData: {
    customer_id: 0, // 选择的营销体系的默认客户
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})

let uuid = 0
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    fieldApiKey: 'fpOtherEntryOrderAdd',
    showCheckBox: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 进仓数量 采购匹数 采购总数量 进仓数量 空差 结算数量 采购辅助数量 进仓辅助数量 进仓金额
        if (['in_roll'].includes(column.field))
          return sumNum(data, 'in_roll', '')

        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '')

        if (['paper_tube_weight'].includes(column.field))
          return sumNum(data, 'paper_tube_weight', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['in_length'].includes(column.field))
          return sumNum(data, 'in_length', '')

        if (['quote_length'].includes(column.field))
          return sumNum(data, 'quote_length', '')

        if (['total_price'].includes(column.field))
          return sumNum(data, 'total_price', '￥')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      field: 'product_code',
      title: '成品编号',
      minWidth: 100,
      required: true,
    },
    {
      field: 'product_name',
      title: '成品名称',
      minWidth: 100,
      required: true,
    },
    {
      field: 'customer_id',
      soltName: 'customer_id',
      title: '所属客户',
      width: 205,
      required: true,
    },
    {
      field: 'product_color_code',
      soltName: 'product_color_code',
      title: '色号',
      width: 205,
      required: true,
    },
    {
      field: 'product_color_id',
      soltName: 'product_color_id',
      title: '颜色',
      width: 205,
      required: true,
    },
    {
      field: 'dye_factory_color_code',
      soltName: 'dye_factory_color_code',
      title: '染厂色号',
      minWidth: 100,
    },
    {
      field: 'dye_factory_dyelot_number',
      soltName: 'dye_factory_dyelot_number',
      title: '染厂缸号',
      minWidth: 100,
      required: true,
    },
    {
      field: 'product_width',
      soltName: 'product_width',
      title: '成品幅宽',
      minWidth: 180,
    },
    {
      field: 'product_gram_weight',
      soltName: 'product_gram_weight',
      title: '成品克重',
      minWidth: 180,
    },
    {
      field: 'product_level_id',
      soltName: 'product_level_id',
      title: '成品等级',
      minWidth: 100,
    },
    {
      field: 'product_remark',
      soltName: 'product_remark',
      title: '成品备注',
      minWidth: 100,
    },
    {
      field: 'product_craft',
      title: '成品工艺',
      minWidth: 100,
    },
    {
      field: 'product_ingredient',
      title: '成品成分',
      minWidth: 100,
    },
    {
      field: 'in_roll',
      soltName: 'in_roll',
      title: '进仓匹数',
      minWidth: 100,
      required: true,
    },
    {
      field: 'total_weight',
      title: '进仓数量',
      minWidth: 100,
      required: true,
    },
    {
      field: 'weight_error',
      title: '空差',
      minWidth: 100,
    },
    {
      field: 'paper_tube_weight',
      title: '纸筒',
      minWidth: 140,
    },
    {
      field: 'unit_name',
      title: '单位',
      minWidth: 70,
    },
    {
      field: 'auxiliary_unit_id',
      title: '结算单位',
      width: 100,
      soltName: 'auxiliary_unit_id',
    },
    {
      field: 'unit_price',
      soltName: 'unit_price',
      title: '基本单位单价',
      minWidth: 110,
    },
    {
      field: 'in_length',
      soltName: 'in_length',
      title: '进仓辅助数量',
      minWidth: 110,
    },
    {
      field: 'length_unit_price',
      soltName: 'length_unit_price',
      title: '辅助数量单价',
      minWidth: 110,
    },
    {
      field: 'other_price',
      soltName: 'other_price',
      title: '其他金额',
      minWidth: 100,
    },
    {
      field: 'total_price',
      soltName: 'total_price',
      title: '进仓金额',
      minWidth: 100,
    },
    {
      field: 'remark',
      soltName: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      field: 'xima',
      soltName: 'xima',
      title: '细码',
      width: 120,
    },
    {
      field: 'operate',
      soltName: 'operate',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ],
  // 确认添加带出数据
  handleSure: (list: any) => {
    // 自动带出字段
    list = list.map((item: any) => {
      // const customer = state.formInline.sale_system_id ? getDefaultCustomer(item?.customer_id) : {}
      const temp = {
        ...item,
        uuid: ++uuid,
        customer_id: state.defaultData.customer_id,
        customer_name: state.defaultData.customer_name,
        // ...customer,
        product_id: item.id,
        product_code: item.finish_product_code, // 成品编号
        product_name: item.finish_product_name, // 成品名称
        product_width: item.finish_product_width, // 成品幅宽
        product_gram_weight: item.finish_product_gram_weight, // 成品克重
        product_craft: item.finish_product_craft, //  成品工艺
        product_ingredient: item.finish_product_ingredient, // 成品成分
        unit_name: item.measurement_unit_name, // 单位
        unit_id: item.measurement_unit_id, // 单位
        product_remark: item.remark,
        product_weight_error: item.weight_error, // 成品资料空差
        product_paper_tube_weight: item.paper_tube_weight, // 成品资料纸筒质量
        remark: '',
        item_fc_data: [],
        in_length: 0,
        in_roll: 0,
        total_weight: 0,
        weight_error: 0,
        settle_weight: 0,
        // paper_tube_weight: item.paper_tube_weight,
        paper_tube_weight: 0,
        unit_price: 0,
        length_unit_price: 0,
        total_price: 0,
        other_price: 0,
        selected: false,
      }
      return conductUnitPrice(temp, true)
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  // 确认录入，需要统计部分字段
  handleSureFineSize: (list: any) => {
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 自动计算
    // 进仓数量 = 所有细码数量之和
    // 空差 = 所有细码空差之和
    // 进仓辅助数量 = 所有细码辅助数量之和
    // 结算数量 =  进仓数量-空差
    const total_weight = list.reduce((pre: any, val: any) => currency(pre).add(val.base_unit_weight || 0).value, 0)
    const weight_error = list.reduce((pre: any, val: any) => currency(pre).add(val.weight_error || 0).value, 0)
    const paper_tube_weight = list.reduce((pre: any, val: any) => currency(pre).add(val.paper_tube_weight || 0).value, 0)
    const settle_weight = total_weight - weight_error
    const in_length = list.reduce((pre: any, val: any) => currency(pre).add(val.length || 0).value, 0)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_weight = Number(total_weight)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].paper_tube_weight = Number(paper_tube_weight)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_length = Number(in_length)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_roll = Number(sumNum(list, 'roll', ''))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number(settle_weight)
    // 进仓金额 = (数量单价*结算数量)+(辅助数量单价*进仓辅助数量)+其他金额
    computedTotalPrice()
    // const {unit_price,length_unit_price,other_price} = finishProductionOptions.datalist[finishProductionOptions.rowIndex]
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_price = (Number(unit_price)*settle_weight)+(Number(length_unit_price)*in_length)+Number(other_price)
  },
  // 复制
  handleCopy: (row: any) => {
    const tempItem = deepClone({
      ...row,
      uuid: ++uuid,
    })
    tempItem.id = 0
    delete tempItem._X_ROW_KEY_TABLE
    finishProductionOptions.datalist.push(tempItem)
  },
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.uuid === uuid,
    )
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit: () => handEdit,
})
// 计算进仓金额
function computedTotalPrice() {
  // 进仓金额 = (数量单价*结算数量)+(辅助数量单价*进仓辅助数量)+其他金额
  finishProductionOptions.datalist.forEach((item: any) => {
    const {
      unit_price = 0,
      settle_weight = 0,
      in_length = 0,
      length_unit_price = 0,
      other_price = 0,
    } = item
    const total_price
      = Number(unit_price) * Number(settle_weight)
      + Number(in_length) * Number(length_unit_price)
      + Number(other_price)
    item.total_price = Number(
      (Number.isNaN(total_price) ? 0 : total_price).toFixed(2),
    )
  })
}
// 色号和颜色联动
function setFinishProductColor(item: any, row: any) {
  row.finishProductionColorId = item?.id
  row.product_color_id = item?.id
  row.product_color_name = item?.product_color_name
  row.product_color_code = item?.product_color_code
  row.product_width = item?.finish_product_width || row.product_width
  row.product_gram_weight = item?.finish_product_gram_weight || row.product_gram_weight
  row.remark = item?.remark
}

// 表格选中事件
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请选择数据')

  bulkShow.value = true
}
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_id',
    title: '所属客户',
    component: 'select',
    query: { sale_system_id: state.formInline.sale_system_id },
    api: 'GetCustomerEnumList',
  },
  // {
  //   field: 'finishProductionColorId',
  //   valueField: 'id',
  //   labelField: 'product_color_code',
  //   title: '色号',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  // },
  // {
  //   field: 'finishProductionColorId',
  //   valueField: 'id',
  //   labelField: 'product_color_name',
  //   title: '颜色',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  // },
  {
    field: 'dye_factory_color_code',
    title: '染厂色号',
    component: 'input',
  },
  {
    field: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'product_width',
    title: '成品幅宽',
    component: 'input',
  },
  {
    field: 'product_gram_weight',
    title: '成品克重(g)',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'product_level_id',
    field_name: 'product_level_id',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'in_roll',
    title: '进仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '进仓数量单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'in_length',
    title: '进仓辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'length_unit_price',
    title: '进仓辅助数量单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value, quickInputResult }: any, val: any) {
  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid,
  )
  finishProductionOptions.datalist.map((item: any, index: number) => {
    if (ids.includes(item.uuid)) {
      item[row.field] = value[row.field]

      if (row.quickInput && quickInputResult?.[index])
        item[row.field] = quickInputResult[index]

      if (row.field === 'finishProductionColorId') {
        item.product_color_name = val?.product_color_name
        item.product_color_code = val?.product_color_code
        item.product_color_id = val?.id
      }
      else if (row.field === 'product_level_id') {
        item.product_level_id = val?.id
        item.product_level_name = val?.name
      }
    }
  })
  if (row.field)
    ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 根据资料添加
function showLibDialog() {
  AccordingLibAddRef.value.state.showModal = true
}

// 默认生成出仓条数数据
function showFineSizeDialog(row: any, rowIndex: number) {
  // FineSizeAddRef.value.state.showModal = true
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.showDialog({
    ...row,
    finish_product_width_and_unit_name: `${row.product_width || ''}${row.finish_product_width_unit_name || ''}`,
    finish_product_gram_weight_and_unit_name: `${row.product_gram_weight || ''}${row.finish_product_gram_weight_unit_name || ''}`,
  }, state.formInline)
}

function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + val.roll, 0)
}

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg, loading: addLoading } = addFpmOtherInOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 成品信息必填项
      // 判断戏码匹数和还有辅助数量和
      // let ximaLengthFlag = true // xima必须和进仓匹数一致
      // let lengthFlag = true // 辅助数量和与分录行辅助数量不一致
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        const item = finishProductionOptions.datalist[i]
        if (!finishProductionOptions.datalist[i].customer_id)
          return ElMessage.error('所属客户为必填项')

        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号为必填项')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称为必填项')

        if (!finishProductionOptions.datalist[i].product_color_name)
          return ElMessage.error('颜色为必填项')

        if (!finishProductionOptions.datalist[i].product_color_code)
          return ElMessage.error('色号为必填项')

        if (!finishProductionOptions.datalist[i].dye_factory_dyelot_number)
          return ElMessage.error('染厂缸号为必填项')

        if (finishProductionOptions.datalist[i].in_roll === '')
          return ElMessage.error('进仓匹数为必填项')

        if (!Number(finishProductionOptions.datalist[i].total_weight))
          return ElMessage.error('进仓数量为必填项')

        if (!item.auxiliary_unit_id) {
          return ElMessage.error(
            `成品编号为${item.product_code}的数据,结算单位不能为空`,
          )
        }

        if (item.unit_price === '' && isMainUnitFormat(item)) {
          return ElMessage.error(
            `成品编号为${item.product_code}的数据,基本单位单价不能为空`,
          )
        }

        if (!item.in_length && !isMainUnitFormat(item)) {
          return ElMessage.error(
            `成品编号为${item.product_code}的数据,进仓辅助数量不能为空`,
          )
        }

        if (item.length_unit_price === '' && !isMainUnitFormat(item)) {
          return ElMessage.error(
            `成品编号为${item.product_code}的数据,辅助数量单价不能为空`,
          )
        }

        let roll = 0
        let length = 0
        finishProductionOptions.datalist[i].item_fc_data.forEach(
          (item: any) => {
            roll += Number(item.roll)
            length += Number(item.length)
          },
        )
        roll = Number(roll.toFixed(2))
        length = Number(length.toFixed(2))
        if (finishProductionOptions.datalist[i]?.item_fc_data?.length) {
          // const roll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          if (Number(finishProductionOptions.datalist[i].in_roll) !== roll)
            return ElMessage.error('进仓匹数与细码匹数总量不一致')

          // const length = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
          if (Number(finishProductionOptions.datalist[i].in_length) !== length)
            return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
        }
        else {
          return ElMessage.error('进仓匹数与细码匹数总量不一致')
        }
      }
      // if (!ximaLengthFlag) {
      //   return ElMessage.error('进仓匹数与细码匹数总量不一致')
      // }
      // if (!lengthFlag) {
      //   return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
      // }
      // 整理参数
      const query = {
        ...state.formInline,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        warehouse_in_time: formatDate(state.formInline.warehouse_in_time),
        item_data: [],
      }
      let total_paper_tube_weight = 0
      query.item_data = finishProductionOptions.datalist.map((item: any) => {
        const item_fc_data = item.item_fc_data.map((v: any) => {
          total_paper_tube_weight = currency(total_paper_tube_weight).add(formatWeightMul(v.paper_tube_weight),
          ).value
          return {
            ...v,
            roll: formatTwoDecimalsMul(v.roll),
            length: formatLengthMul(v.length),
            weight_error: formatWeightMul(v.weight_error),
            paper_tube_weight: formatWeightMul(v.paper_tube_weight),
            base_unit_weight: formatWeightMul(v.base_unit_weight),
          }
        })
        return {
          ...item,
          paper_tube_weight: total_paper_tube_weight,
          product_remark: item.product_remark,
          remark: item.remark,
          product_width: item.product_width,
          product_gram_weight: item.product_gram_weight,
          in_roll: formatTwoDecimalsMul(Number(item.in_roll)),
          quote_roll: formatTwoDecimalsMul(Number(item.quote_roll)),
          quote_total_weight: formatWeightMul(Number(item.quote_total_weight)),
          total_weight: formatWeightMul(Number(item.total_weight)),
          weight_error: formatWeightMul(Number(item.weight_error)),
          settle_weight: formatWeightMul(Number(item.settle_weight)),
          unit_price: formatUnitPriceMul(Number(item.unit_price)),
          quote_length: formatLengthMul(Number(item.quote_length)),
          in_length: formatLengthMul(Number(item.in_length)),
          length_unit_price: formatUnitPriceMul(Number(item.length_unit_price)),
          other_price: formatTwoDecimalsMul(Number(item.other_price)),
          total_price: formatTwoDecimalsMul(Number(item.total_price)),
          item_fc_data,
        }
      })
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        routerList.push({
          name: 'FpOtherEntryOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

function getSaleSystem(row: any) {
  state.formInline.sale_system_id = row.id
  state.formInline.warehouse_id = row?.default_physical_warehouse || '' // 默认的仓库
  state.defaultData.customer_id = row.default_customer_id
  state.defaultData.customer_name = row.default_customer_name
  clearData()
}

function clearData() {
  finishProductionOptions.datalist?.map((item: any) => {
    item.customer_id = state.defaultData.customer_id || ''
    item.customer_name = state.defaultData.customer_name || ''
  })
  bulkSetting.value.customer_id = state.defaultData.customer_id
}

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  state.formInline.sale_system_id = res?.default_sale_system_id
})

function addOrNextLineFocus(index: number, e: any, name: string) {
  if (e.$event.keyCode !== 13)
    return

  // 如果下一行有数据就获取焦点
  if (finishProductionOptions.datalist[index + 1]) {
    const box = document.getElementById(`${name}${index + 1}`)
    const input = box?.querySelector('input')
    input?.focus()
  }
  else {
    // 如果没有下一行就回到第一行
    const box = document.getElementById(`${name}0`)
    const input = box?.querySelector('input')
    input?.focus()
  }
}
// 处理字段不一致
function isMainUnitFormat(item: any) {
  return isMainUnit(item, 'unit_id')
}
/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.unit_id
    item.auxiliary_unit_name = item.unit_name
  }
  if (isMainUnitFormat(item))
    item.length_unit_price = 0 // 主单位-把辅助单价置0

  else
    item.unit_price = 0 // 辅助单位-把单价置0
  computedTotalPrice()
  return item
}
</script>

<template>
  <div class="list-page">
    <FildCard title="基础信息" :tool-bar="false">
      <template #right-top>
        <el-button type="primary" :loading="addLoading" :disabled="finishProductionOptions.datalist.length ? false : true" @click="submitAddAllData">
          提交
        </el-button>
      </template>
      <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem :required="true" label="营销体系:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.formInline.sale_system_id"
                  default-status
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  clearable
                  @select="getSaleSystem"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem :required="true" label="发货单位名称:">
            <template #content>
              <el-form-item prop="biz_unit_id">
                <SelectBusinessDialog
                  v-model="state.formInline.biz_unit_id"
                  :query="{
                    unit_type_id: `${BusinessUnitIdEnum.customer},${BusinessUnitIdEnum.finishedProduct}`,
                  }"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem :required="true" label="仓库名称:">
            <template #content>
              <el-form-item prop="warehouse_id">
                <SelectComponents
                  v-model="state.formInline.warehouse_id"
                  api="GetPhysicalWarehouseDropdownList"
                  :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                  label-field="name" value-field="id" clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem :required="true" label="进仓日期:">
            <template #content>
              <el-form-item prop="warehouse_in_time">
                <el-date-picker v-model="state.formInline.warehouse_in_time" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="仓管员:">
            <template #content>
              <el-form-item>
                <SelectComponents v-model="state.formInline.store_keeper_id" :query="{ duty: EmployeeType.warehouseManager }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="凭证号:">
            <template #content>
              <el-form-item prop="voucher_number">
                <el-input v-model="state.formInline.voucher_number" placeholder="凭证号" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="订单类型:" :copies="1">
            <template #content>
              <SelectSaleMode v-model="state.formInline.sale_mode" :show-customer-book="false" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <vxe-textarea v-model="state.formInline.remark" style="width: 100%" maxlength="500" show-word-count />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </FildCard>
    <FildCard title="成品信息" class="mt-[5px] table-card-full" :tool-bar="true">
      <template #right-top>
        <el-button type="primary" @click="handEdit">
          批量操作
        </el-button>
        <el-button type="primary" @click="showLibDialog">
          根据资料添加
        </el-button>
      </template>
      <div v-show="finishProductionOptions.datalist.length" class="flex-1">
        <Table ref="tablesRef" :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
          <!-- 所属客户 -->
          <template #customer_id="{ row }">
            <SelectDialog
              v-model="row.customer_id"
              :label-name="row.customer_name"
              api="GetCustomerEnumList"
              :query="{ sale_system_id: state.formInline.sale_system_id, name: componentRemoteSearch.customer_name }"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.customer_name = val)"
            />
          <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="row.customer_id" clearable /> -->
          </template>
          <!-- 色号 -->
          <template #product_color_code="{ row }">
            <SelectDialog
              v-model="row.finishProductionColorId"
              :query="{
                finish_product_id: row.product_id,
                product_color_code: row.color_code,
              }"
              :label-name="row.product_color_code"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  defaultData: {
                    id: row.finishProductionColorId,
                    product_color_code: row.product_color_code,
                    product_color_name: row.product_color_name,
                  },
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="val => (row.color_code = val)"
              @change-value="
                item => {
                  setFinishProductColor(item, row)
                }
              "
            />
          </template>
          <!-- 颜色 -->
          <template #product_color_id="{ row }">
            <SelectDialog
              v-model="row.finishProductionColorId"
              :label-name="row.product_color_name"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '颜色',
                  defaultData: {
                    id: row.finishProductionColorId,
                    product_color_code: row.product_color_code,
                    product_color_name: row.product_color_name,
                  },
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_name"
              :query="{
                finish_product_id: row.product_id,
                product_color_name: row.color_name,
              }"
              @on-input="val => (row.color_name = val)"
              @change-value="
                item => {
                  setFinishProductColor(item, row)
                }
              "
            />
          </template>
          <!-- 染厂色号 -->
          <template #dye_factory_color_code="{ row, rowIndex }">
            <vxe-input
              :id="`dye_factory_color_code${rowIndex}`"
              v-model="row.dye_factory_color_code"
              maxlength="200"
              clearable
              @keydown="addOrNextLineFocus(rowIndex, $event, 'dye_factory_color_code')"
            />
          </template>
          <!-- 染厂缸号 -->
          <template #dye_factory_dyelot_number="{ row, rowIndex }">
            <vxe-input
              :id="`dye_factory_dyelot_number${rowIndex}`"
              v-model="row.dye_factory_dyelot_number"
              maxlength="200"
              clearable
              @keydown="addOrNextLineFocus(rowIndex, $event, 'dye_factory_dyelot_number')"
            />
          </template>
          <!-- 成品幅宽 -->
          <template #product_width="{ row }">
            <!-- <vxe-input v-model="row.product_width"></vxe-input> -->
            <el-input v-model="row.product_width" clearable placeholder="成品幅宽">
              <template #append>
                <SelectComponents
                  v-model="row.finish_product_width_unit_id"
                  placeholder="单位"
                  style="width: 80px"
                  :query="{ dictionary_id: DictionaryType.width_unit }"
                  api="GetDictionaryDetailEnumListApi"
                  label-field="name"
                  value-field="id"
                  clearable
                  @change-value="(val:any) => row.finish_product_width_unit_name = val?.name || ''"
                />
              </template>
            </el-input>
          </template>
          <!-- 成品克重 -->
          <template #product_gram_weight="{ row }">
            <!-- <vxe-input v-model="row.product_gram_weight" type="text"></vxe-input> -->
            <el-input v-model="row.product_gram_weight" clearable placeholder="成品克重">
              <template #append>
                <SelectComponents
                  v-model="row.finish_product_gram_weight_unit_id"
                  placeholder="单位"
                  style="width: 80px"
                  :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
                  api="GetDictionaryDetailEnumListApi"
                  label-field="name"
                  value-field="id"
                  clearable
                  @change-value="(val:any) => row.finish_product_gram_weight_unit_name = val?.name || ''"
                />
              </template>
            </el-input>
          </template>
          <!-- 成品等级 -->
          <template #product_level_id="{ row }">
            <SelectComponents v-model="row.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
          </template>
          <!-- 成品备注 -->
          <template #product_remark="{ row }">
            <vxe-input v-model="row.product_remark" maxlength="200" />
          </template>
          <!-- 进仓匹数 -->
          <template #in_roll="{ row, rowIndex }">
            <vxe-input :id="`in_roll${rowIndex}`" v-model="row.in_roll" type="float" @keydown="addOrNextLineFocus(rowIndex, $event, 'in_roll')" />
          </template>
          <!-- 结算单位 -->
          <template #auxiliary_unit_id="{ row }">
            <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice(row)" />
          </template>
          <!-- 单价 -->
          <template #unit_price="{ row }">
            <vxe-input v-show="isMainUnitFormat(row)" v-model="row.unit_price" type="float" @change="computedTotalPrice" />
            <span v-show="!isMainUnitFormat(row)">{{ row.unit_price }}</span>
          </template>
          <!-- 进仓辅助数量 -->
          <template #in_length="{ row }">
            <vxe-input v-model="row.in_length" type="float" @change="computedTotalPrice" />
          </template>
          <!-- 辅助数量单价 -->
          <template #length_unit_price="{ row }">
            <vxe-input v-show="!isMainUnitFormat(row)" v-model="row.length_unit_price" type="float" @change="computedTotalPrice" />
            <span v-show="isMainUnitFormat(row)">{{ row.length_unit_price }}</span>
          </template>
          <!-- 其他金额 -->
          <template #other_price="{ row }">
            <vxe-input v-model="row.other_price" type="float" @change="computedTotalPrice" />
          </template>
          <!-- 进仓金额 -->
          <template #total_price="{ row }">
            ￥{{ row?.total_price }}
          </template>
          <!-- 备注 -->
          <template #remark="{ row }">
            <vxe-input v-model="row.remark" maxlength="200" type="text" />
          </template>
          <!-- 细码 -->
          <template #xima="{ row, rowIndex }">
            <!-- 进仓数量 -->
            <el-button v-if="row.in_roll !== ''" type="primary" text link @click="showFineSizeDialog(row, rowIndex)">
              录入
              <span v-if="isFinishEnter(row)" style="color: #ccc">(已录入)</span>
              <span v-else style="color: red">({{ row?.in_roll - getSFRoll(row) }}条未录)</span>
            </el-button>
            <span v-else style="color: #ccc">请先输入进仓匹数</span>
          </template>
          <!-- 操作 -->
          <template #operate="{ row }">
            <el-button type="primary" text link @click="finishProductionOptions.handleCopy(row)">
              复制
            </el-button>
            <el-button type="danger" text link @click="finishProductionOptions.handleRowDel(row)">
              删除
            </el-button>
          </template>
        </Table>
      </div>
      <div v-if="finishProductionOptions.datalist.length === 0" class="no_data">
        <el-icon :size="80">
          <MessageBox />
        </el-icon>
        <div class="text">
          请选择仓库
        </div>
      </div>
    </FildCard>
  </div>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #customer_id="{ row }">
      <SelectDialog
        v-model="bulkSetting[row.field]"
        api="GetCustomerEnumList"
        :query="{ sale_system_id: state.formInline.sale_system_id }"
        :column-list="[
          {
            title: '客户编号',
            minWidth: 100,
            required: true,
            colGroupHeader: true,
            childrenList: [
              {
                field: 'code',
                isEdit: true,
                title: '客户编号',
                minWidth: 100,
              },
            ],
          },
          {
            title: '客户名称',
            minWidth: 100,
            colGroupHeader: true,
            required: true,
            childrenList: [
              {
                isEdit: true,
                field: 'name',
                title: '客户名称',
                minWidth: 100,
              },
            ],
          },
        ]"
      />
    </template>
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingLibAdd ref="AccordingLibAddRef" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
