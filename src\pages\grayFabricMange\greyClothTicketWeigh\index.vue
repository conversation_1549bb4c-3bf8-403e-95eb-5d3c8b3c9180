<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete, DocumentAdd, InfoFilled, Monitor, QuestionFilled, Setting, User } from '@element-plus/icons-vue'
import { GetFineCodeDetail, WeighingFineCode } from '@/api/clothTicket'
import { useMeasureWeightStore } from '@/stores/measureWeight'
import { useMeasureWeightService } from '@/use/useMeasureWeightService'
import { EmployeeType } from '@/common/enum'
import SvgIcon from '@/components/Svglcon/index.vue'
import { formatHashTag, formatWeightDiv, formatWeightMul } from '@/common/format'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'

// 挂载快捷键监听

// 表单数据
const formData = reactive({
  // 生产信息录入
  barcode: '', // 条码编号
  machineNumber: '', // 机台号
  weaverId: null, // 织工ID
  weaverName: '', // 织工ID
  isShiftWorker: false, // 是否为交班织工
  shiftDuration: '', // 交班时长(rpm)
  shiftWeaverId: null, // 交班织工ID
  shiftWeaverName: '', // 交班织工姓名
  electronicWeight: 0, // 电子称重量(kg)
  actualWeight: 0, // 实际重量(kg)
  fineCodeId: null as number | null, // 细码ID
})

// 生产参数详情数据
const productionDetails = reactive({
  orderName: '', // 订单名称
  orderNumber: '', // 编号
  version: '', // 版号
  versionNumber: '', // 卷号
  batch: '', // 纱批
  machineType: '', // 织机类型
  machineColor: '', // 原料颜色
  needleSize: '', // 针寸数
  group: '', // 班次
  machine: '', // 机台
  isShiftWorker: '', // 是否交班布
  shiftTime: 0, // 称重
  shiftDuration: '', // 交班转速
  shiftWeaverName: '', // 交班织工
  actualWeight: 0, // 实重
  operator: '', // 织工
  monthlyOutput: '', // 本月产量
  yearlyOutput: '', // 本月产量
  productionNote: '', // 生产备注
  weightOfFabric: 0, // 布匹定重
})

// 加载状态
const loading = ref(false)

// 表单引用
const formRef = ref()

// 自动保存状态
const autoSave = ref(false)

// 电子秤相关
const measureWeightStore = useMeasureWeightStore()
// 弹窗控制
const showElectronicScaleModal = ref(false)
const showWeightLog = ref(false)

// 电子秤设置
const electronicScaleSettings = reactive({
  connection: '链接',
  stableValue: 15,
  isStable: false,
  autoSave: true,
  autoSaveSeconds: 5,
  noWeaverSelection: false,
  noInspectorSelection: false,
  weightReflection: false,
  dataHead: '',
  dataEnd: '',
  scanCodeReading: false,
  portAutoOpen: false,
})

// 连接状态
const isWeightConnected = computed(() => measureWeightStore.measureWeightState?.isConnected || false)
const WeightLog = computed(() => {
  return measureWeightStore.measureWeightState.Log
})
// 监听电子秤数据
watch(() => measureWeightStore.measureWeightState.currentFrameData, (newValue: number) => {
  formData.electronicWeight = newValue
  formData.actualWeight = newValue
})

const { fetchData, success, msg, data } = GetFineCodeDetail()
// 获取细码详情
async function getFineCodeDetail(barcode?: string, id?: number) {
  if (!barcode && !id) {
    ElMessage.error('请提供条码或细码ID')
    return
  }

  loading.value = true
  try {
    await fetchData({
      fabric_piece_code: barcode,
      id,
    })

    if (success.value) {
      // 更新表单数据
      formData.fineCodeId = data.value.id || null
      formData.machineNumber = data.value.machine_number || ''

      // 更新生产详情数据
      productionDetails.orderName = data.value.production_notify_order_no || ''
      productionDetails.orderNumber = data.value.fabric_piece_code || ''
      productionDetails.version = data.value.production_schedule_order_no || ''
      productionDetails.versionNumber = data.value.volume_number?.toString() || ''
      productionDetails.batch = data.value.yarn_batch || ''
      productionDetails.machineType = data.value.loom_model_name || ''
      productionDetails.machineColor = data.value.grey_fabric_color_name || ''
      productionDetails.needleSize = data.value.needle_size || ''
      productionDetails.machine = data.value.machine_number || ''
      productionDetails.actualWeight = formatWeightDiv(data.value.weighing_weight || 0)
      productionDetails.monthlyOutput = data.value.month_weighing_count ? `${data.value.month_weighing_count}条` : ''
      productionDetails.yearlyOutput = data.value.today_weighing_count ? `${data.value.today_weighing_count}条` : ''
      productionDetails.productionNote = data.value.produce_remark || ''
      productionDetails.weightOfFabric = data.value.weight_of_fabric || 0
      productionDetails.shiftTime = formatWeightDiv(data.value.electronic_scale_weight || 0)
      productionDetails.shiftDuration = data.value.shift_speed || 0
      productionDetails.shiftWeaverName = data.value.shift_weaver_name
      productionDetails.isShiftWorker = data.value.is_shift_weaver ? '是' : '否'
    }
    else {
      ElMessage.error(msg.value || '获取细码详情失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '获取细码详情失败')
  }
  finally {
    loading.value = false
  }
}

// 扫码输入处理
async function handleBarcodeInput() {
  if (formData.barcode)
    await getFineCodeDetail(formData.barcode)
}

const { fetchData: saveWeighingData, success: saveSuccess, msg: saveMsg } = WeighingFineCode()
// 称重保存
async function handleSave() {
  try {
    await formRef.value?.validate()

    if (!formData.fineCodeId) {
      ElMessage.error('请先扫码获取细码信息')
      return
    }

    if (!formData.weaverId) {
      ElMessage.error('请选择织工')
      return
    }

    loading.value = true

    const requestData: any = {
      id: formData.fineCodeId,
      actual_weight: formatWeightMul(formData.actualWeight),
      electronic_scale_weight: formatWeightMul(formData.electronicWeight),
      weaver_id: formData.weaverId,
      weaver_name: formData.weaverName,
      is_shift_weaver: formData.isShiftWorker,
    }

    // 如果是交班织工，添加交班相关信息
    if (formData.isShiftWorker) {
      if (formData.shiftDuration)
        requestData.shift_speed = Number(formData.shiftDuration)

      if (formData.shiftWeaverId)
        requestData.shift_weaver_id = formData.shiftWeaverId

      if (formData.shiftWeaverName)
        requestData.shift_weaver_name = formData.shiftWeaverName
    }

    await saveWeighingData(requestData)

    if (saveSuccess.value) {
      ElMessage.success('保存成功')
      handleSaveChar()
      // 重新获取细码详情以更新显示
      if (formData.fineCodeId)
        await getFineCodeDetail(undefined, formData.fineCodeId)
    }
    else {
      ElMessage.error(saveMsg.value || '保存失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '保存失败，请检查表单填写')
  }
  finally {
    loading.value = false
  }
}

// 清空表单
function handleSaveChar() {
  // 重置表单数据
  Object.assign(formData, {
    barcode: '',
    machineNumber: '',
    workerName: '',
    isShiftWorker: false,
    shiftDuration: '',
    shiftWeaverId: null,
    shiftWeaverName: '',
    electronicWeight: 0,
    actualWeight: 0,
    fineCodeId: null,
  })

  // 重置生产详情数据
  Object.assign(productionDetails, {
    orderName: '',
    orderNumber: '',
    version: '',
    versionNumber: '',
    batch: '',
    machineType: '',
    machineColor: '',
    needleSize: '',
    group: '',
    machine: '',
    isShiftWorker: '',
    shiftTime: '',
    shiftWorker: '',
    shiftWeaverName: '',
    actualWeight: '',
    operator: '',
    monthlyOutput: '',
    yearlyOutput: '',
    productionNote: '',
    weightOfFabric: '',
  })
}

// 自动保存切换
function handleAutoSaveToggle() {
  if (autoSave.value)
    ElMessage.info('已开启自动保存')
  else
    ElMessage.info('已关闭自动保存')
}

// 连接电子秤串口设备
function handleConnectToSerialPort() {
  if (!isWeightConnected.value) {
    const delimiterConfig = {
      dataHeader: electronicScaleSettings.dataHead
        ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
        : [],
      dataFooter: electronicScaleSettings.dataEnd
        ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
        : [13, 10], // 默认 \r\n
      useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
    }

    const stabilityConfig = {
      enabled: electronicScaleSettings.isStable,
      stableCount: Number(electronicScaleSettings.stableValue) || 3,
      precision: 2, // 默认精度到小数点后2位
    }

    const measureWeightService = useMeasureWeightService(delimiterConfig, stabilityConfig)
    try {
      measureWeightService.connectToSerialPort()
      measureWeightStore.setMeasureWeightState(measureWeightService)
    }
    catch (error) {
      console.error('串口连接错误:', error)
    }
  }
}

// 断开电子秤串口连接
function handleDisconnectToWeightSerialPort() {
  measureWeightStore.measureWeightState.clearLogMessages()
  measureWeightStore.clearLogList()
  measureWeightStore.measureWeightState.disconnectPort()
}

// 切换重量日志显示
function toggleWeightLog() {
  showWeightLog.value = !showWeightLog.value
}

// 打开设置电子秤弹窗
function openElectronicScaleModal() {
  showElectronicScaleModal.value = true
}

// 保存电子秤设置
function saveElectronicScaleSettings() {
  // 如果已经连接，更新现有连接的配置
  if (isWeightConnected.value && measureWeightStore.measureWeightState) {
    // 更新数据头尾配置
    const delimiterConfig = {
      dataHeader: electronicScaleSettings.dataHead
        ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
        : [],
      dataFooter: electronicScaleSettings.dataEnd
        ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
        : [13, 10], // 默认 \r\n
      useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
    }

    // 更新稳定性配置
    const stabilityConfig = {
      enabled: electronicScaleSettings.isStable,
      stableCount: Number(electronicScaleSettings.stableValue) || 3,
      precision: 2, // 默认精度到小数点后2位
    }

    // 更新现有连接的配置
    if (measureWeightStore.measureWeightState.updateDelimiterConfig)
      measureWeightStore.measureWeightState.updateDelimiterConfig(delimiterConfig)

    if (measureWeightStore.measureWeightState.updateStabilityConfig)
      measureWeightStore.measureWeightState.updateStabilityConfig(stabilityConfig)

    ElMessage.success('电子秤设置已更新并应用到当前连接')
  }

  showElectronicScaleModal.value = false
  ElMessage.success('电子秤设置保存成功')
}

// 监听F4快捷键
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'F4') {
    event.preventDefault()
    handleSave()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 表单验证规则
const rules = {
  barcode: [{ required: true, message: '请输入条码编号', trigger: 'blur' }],
  weaverId: [{ required: true, message: '请选择织工姓名', trigger: 'change' }],
  electronicWeight: [
    { required: true, message: '请输入电子称重量', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (Number(value) <= 0)
          callback(new Error('电子称重量必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
  actualWeight: [
    { required: true, message: '请输入实际重量', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (Number(value) <= 0)
          callback(new Error('实际重量必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
}
</script>

<template>
  <div class="production-entry-page">
    <div class="main-content">
      <!-- 左侧：生产信息录入 -->
      <div class="left-panel">
        <el-card>
          <h2 class="font-bold text-lg">
            生产信息录入
          </h2>
          <el-form
            ref="formRef"
            label-position="top"
            :model="formData"
            :rules="rules"
            size="large"
            class="production-form"
          >
            <!-- 条码编号 -->
            <el-form-item label="条码编号" prop="barcode">
              <div class="input-with-icon">
                <el-input
                  v-model="formData.barcode"
                  placeholder="扫码自动识别或手动输入"
                  clearable
                  @change="handleBarcodeInput"
                  @keyup.enter="handleBarcodeInput"
                >
                  <template #append>
                    <SvgIcon name="tiaoma2" size="20px" />
                  </template>
                </el-input>
              </div>
            </el-form-item>

            <!-- 机台号 -->
            <el-form-item label="机台号" prop="machineNumber">
              <div class="input-with-icon">
                <div class="flex items-center">
                  <span class="text-ls text-gray-500">
                    {{ formData.machineNumber || '自动录入机台号' }}
                  </span>
                  <!-- <el-icon class="input-icon">
                    <Setting />
                  </el-icon> -->
                </div>
                <!-- <el-input
                  v-model="formData.machineNumber"
                  size="default"
                  placeholder="自动带出机台号"
                  readonly
                >
                  <template #append>
                    <el-icon class="input-icon">
                      <Setting />
                    </el-icon>
                  </template>
                </el-input> -->
              </div>
            </el-form-item>

            <!-- 织工姓名 -->
            <el-form-item label="织工姓名" prop="weaverId">
              <div class="worker-input-group">
                <SelectMergeComponent
                  v-model="formData.weaverId"
                  :custom-label="(row:any) => `${formatHashTag(row.code, row.name)}`"
                  :query="{ duty: EmployeeType.follower }"
                  api-name="Adminemployeelist"
                  placeholder="请选择织工姓名"
                  remote
                  remote-key="code_or_name"
                  remote-show-suffix
                  label-field="name"
                  value-field="id"
                  clearable
                  @change="(item) => formData.weaverName = item.name"
                />
                <el-button class="ml-2" @click="() => formData.weaverId = null">
                  清空
                </el-button>
              </div>
            </el-form-item>
            <el-divider />

            <el-row class="w-full">
              <el-col :span="12">
                <!-- 是否为交班织工 -->
                <el-form-item label="是否为交班织工">
                  <el-radio-group
                    v-model="formData.isShiftWorker"
                  >
                    <el-radio :value="true">
                      是
                    </el-radio>
                    <el-radio :value="false">
                      否
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 是否为交班织工 -->
                <el-form-item label="交班织工">
                  <div class="flex">
                    <SelectMergeComponent
                      v-model="formData.shiftWeaverId"
                      :custom-label="(row:any) => `${formatHashTag(row.code, row.name)}`"
                      :query="{ duty: EmployeeType.follower }"
                      api-name="Adminemployeelist"
                      placeholder="请选择交班织工"
                      remote
                      remote-key="code_or_name"
                      remote-show-suffix
                      label-field="name"
                      value-field="id"
                      clearable
                      @change="(item) => formData.shiftWeaverName = item.name"
                    />
                    <el-button class="ml-2" @click="() => formData.weaverId = null">
                      清空
                    </el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 交班时长 -->
            <el-form-item label="交班转速(rpm)">
              <template #label>
                <div class="flex items-center">
                  <span>交班转速</span>
                </div>
              </template>
              <div class="input-with-icon">
                <el-input-number
                  v-model="formData.shiftDuration"
                  :precision="2"
                  :min="0"
                  placeholder="请输入交班转速"
                  class="shift-duration-input"
                >
                  <template #suffix>
                    <span>rpm</span>
                  </template>
                </el-input-number>
              </div>
            </el-form-item>
            <el-divider />

            <!-- 称重信息 -->
            <div class="weight-section">
              <h4 class="section-title">
                称重信息
              </h4>
              <el-row class="w-full">
                <el-col :span="12">
                  <el-form-item label="" prop="electronicWeight">
                    <template #label>
                      <span class="inline-flex items-center">
                        <span>电子称重量</span>
                      </span>
                    </template>
                    <div class="input-with-icon">
                      <el-input-number
                        v-model="formData.electronicWeight"
                        :precision="2"
                        :min="0"
                        placeholder="电子称重量"
                      >
                        <template #suffix>
                          <span>kg</span>
                        </template>
                      </el-input-number>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="" prop="actualWeight">
                    <template #label>
                      <span class="inline-flex items-center">
                        <span>实际重量</span>
                      </span>
                    </template>
                    <div class="actual-weight-group">
                      <div class="input-with-icon">
                        <el-input-number
                          v-model="formData.actualWeight"
                          :precision="2"
                          :min="0"
                          placeholder="实际重量（稳定后重量）"
                        >
                          <template #suffix>
                            <span>kg</span>
                          </template>
                        </el-input-number>
                      </div>
                      <span class="weight-note">坯布信息布要求定重：{{ formatWeightDiv(productionDetails.weightOfFabric) || '0' }}kg</span>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
          <div class="action-buttons">
            <div class="left-actions">
              <el-checkbox v-model="autoSave" size="large" @change="handleAutoSaveToggle">
                自动保存
              </el-checkbox>
              <el-button size="large" @click="openElectronicScaleModal">
                设置电子秤
              </el-button>
            </div>
            <div class="right-actions">
              <el-button size="large" :disabled="loading" @click="handleSaveChar">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
              <el-button size="large" type="primary" :loading="loading" @click="handleSave">
                <el-icon><DocumentAdd /></el-icon>
                保存数据(F4)
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 中间：基本信息 -->
      <div class="right-panel">
        <el-card>
          <h2 class="font-bold text-lg">
            基本信息
          </h2>
          <div class="production-details">
            <!-- 基本信息 -->
            <div class="detail-row">
              <div class="detail-item">
                <label>订单名称：</label>
                <span class="detail-value">{{ productionDetails.orderName }}</span>
              </div>
              <div class="detail-item">
                <label>条码：</label>
                <span class="detail-value">{{ productionDetails.orderNumber }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>版号：</label>
                <span class="detail-value">{{ productionDetails.version }}</span>
              </div>
              <div class="detail-item">
                <label>卷号：</label>
                <span class="detail-value">{{ productionDetails.versionNumber }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>纱批：</label>
                <span class="detail-value">{{ productionDetails.batch }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>织机类型：</label>
                <span class="detail-value">{{ productionDetails.machineType }}</span>
              </div>
              <div class="detail-item">
                <label>原料颜色：</label>
                <span class="detail-value">{{ productionDetails.machineColor }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>针寸数：</label>
                <span class="detail-value">{{ productionDetails.needleSize }}</span>
              </div>
              <div class="detail-item">
                <label>班次：</label>
                <span class="detail-value">{{ productionDetails.group }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>机台：</label>
                <span class="detail-value">{{ productionDetails.machine }}</span>
              </div>
              <div class="detail-item">
                <label>是否交班布：</label>
                <span class="detail-value highlight">{{ productionDetails.isShiftWorker }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>称重：</label>
                <span class="detail-value">{{ productionDetails.shiftTime }}kg</span>
              </div>
              <div class="detail-item">
                <label>交班转速：</label>
                <span class="detail-value highlight">{{ productionDetails.shiftDuration }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>实重：</label>
                <span class="detail-value">{{ productionDetails.actualWeight || '0' }}kg</span>
              </div>
              <div class="detail-item">
                <label>交班织工：</label>
                <span class="detail-value highlight">{{ productionDetails.shiftWeaverName }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>织工：</label>
                <span class="detail-value">{{ productionDetails.operator || '' }}</span>
              </div>
              <div class="detail-item flex">
                <div class="detail-item !p-0">
                  <label>本月产量：</label>
                  <span class="detail-value">{{ productionDetails.monthlyOutput }}</span>
                </div>
                <div class="detail-item !p-0">
                  <label>本月产量：</label>
                  <span class="detail-value">{{ productionDetails.yearlyOutput }}</span>
                </div>
              </div>
            </div>

            <!-- 生产备注 -->
            <div class="production-note">
              <label>生产备注：</label>
              <p class="note-content">
                {{ productionDetails.productionNote }}
              </p>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 设置电子秤弹窗 -->
    <vxe-modal
      v-model="showElectronicScaleModal"
      title="设置电子秤"
      width="800"
      show-footer
      resize
      show-close
    >
      <div class="electronic-scale-modal">
        <el-form size="large" :model="electronicScaleSettings" label-width="100px">
          <el-form-item label="电子秤:">
            <el-button @click="handleConnectToSerialPort">
              连接({{ isWeightConnected ? '已连接✅' : '未连接❌' }})
            </el-button>
            <el-button @click="handleDisconnectToWeightSerialPort">
              断开
            </el-button>
            <el-button class="ml-2" @click="toggleWeightLog">
              日志
            </el-button>
            <WeightLog v-if="showWeightLog" />
          </el-form-item>
          <el-form-item label="稳定值:">
            <template #label>
              稳定设置
              <el-tooltip
                class="box-item"
                effect="dark"
                content="电子台秤连续稳定的重量值"
                placement="top-start"
              >
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <div class="stable-value-row">
              <el-checkbox v-model="electronicScaleSettings.isStable">
                设置稳定值
              </el-checkbox>
              <el-input-number v-if="electronicScaleSettings.isStable" v-model="electronicScaleSettings.stableValue" precision="0" :min="1" style="width: 200px;margin-left: 10px">
                <template #suffix>
                  <span>次</span>
                </template>
              </el-input-number>
            </div>
          </el-form-item>

          <el-form-item label="保存设置">
            <el-checkbox v-model="electronicScaleSettings.autoSave">
              自动保存（多少秒自动保存）
            </el-checkbox>
            <el-input-number v-if="electronicScaleSettings.autoSave" v-model="electronicScaleSettings.autoSaveSeconds" :min="0" style="width: 200px;">
              <template #suffix>
                <span>秒</span>
              </template>
            </el-input-number>
          </el-form-item>
          <el-row>
            <el-col :span="8">
              <el-form-item>
                <el-checkbox v-model="electronicScaleSettings.noWeaverSelection" :label="true">
                  不需选择织工
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-checkbox v-model="electronicScaleSettings.noInspectorSelection" :label="true">
                  不需选择查布
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-checkbox v-model="electronicScaleSettings.weightReflection" :label="true">
                  重量反转
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <template #label>
              数据头
              <el-tooltip
                class="box-item"
                effect="dark"
                content="区分串口数据的开头，一般不需要填写"
                placement="top-start"
              >
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="electronicScaleSettings.dataHead" placeholder="数据头" style="width: 200px; margin-left: 10px" />
          </el-form-item>
          <el-form-item>
            <template #label>
              数据尾
              <el-tooltip
                class="box-item"
                effect="dark"
                content="区分串口数据的结尾，一般不需要填写"
                placement="top-start"
              >
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="electronicScaleSettings.dataEnd" placeholder="数据尾" style="width: 200px; margin-left: 10px" />
          </el-form-item>

          <el-row>
            <el-col :span="8">
              <el-form-item>
                <el-checkbox v-model="electronicScaleSettings.scanCodeReading" :label="true">
                  扫码后才读数
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <el-checkbox v-model="electronicScaleSettings.portAutoOpen" :label="true">
                  端口自动打开
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <el-button size="large" type="primary" @click="saveElectronicScaleSettings">
          保存
        </el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<style lang="scss" scoped>
.production-entry-page {

  .main-content {
    display: flex;
    gap: 20px;

    .left-panel {
      flex: 1;
      min-width: 500px;
    }

    .right-panel {
      flex: 1;
      min-width: 500px;
    }
  }

  // 生产信息录入表单样式
  .production-form {
    .input-with-icon {
      width: 100%;

      .input-icon {
        color: #909399;
        font-size: 16px;
        pointer-events: none;
      }

      .el-input {
        ::v-deep .el-input__inner {
          padding-right: 35px;
        }
      }

      .el-select {
        ::v-deep .el-input__inner {
          padding-right: 35px;
        }
      }

    }

    .required-label {
      position: relative;

      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .worker-input-group {
      display: flex;
    }

    .shift-duration-input {
      margin-bottom: 8px;
    }

    .input-note {
      font-size: 12px;
      color: #f56c6c;
      line-height: 1.2;
    }

    .weight-section {

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .actual-weight-group {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .weight-note {
          font-size: 12px;
          color: #909399;
          line-height: 1.2;
        }
      }
    }
  }

  // 生产参数详情样式
  .production-details {
    .detail-row {
      display: flex;
      margin-bottom: 10px;
      gap: 10px;

      .detail-item {
        flex: 1;
        min-height: 28px;
        background: white;
        padding: 10px;
        border-radius: 10px;
        label {
          display: block;
          font-weight: 500;
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
          min-width: 70px;
          font-size: 14px;
        }

        .detail-value {
          color: #303133;
          font-weight: 400;
          word-break: break-all;
          font-size: 14px;

          &.highlight {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }
    }

    .production-note {
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #409eff;

      label {
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
        display: block;
      }

      .note-content {
        margin: 0;
        line-height: 1.6;
        color: #606266;
        font-size: 14px;
      }
    }
  }

}
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  // max-width: 600px;

  .left-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .el-checkbox {
      font-weight: 500;
      color: #606266;
    }

    .button-separator {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-button {
      min-width: 100px;
      font-weight: 500;

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}
// 全局样式覆盖
::v-deep .el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;

    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }

}

::v-deep .el-radio-group {
  .el-radio {
    margin-right: 20px;

    .el-radio__label {
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .production-entry-page {
    .main-content {
      flex-direction: column;

      .left-panel,
      .right-panel {
        min-width: auto;
      }
    }

    .production-details {
      .detail-row {
        flex-direction: column;
        gap: 12px;

        .detail-item {
          label {
            min-width: auto;
          }
        }
      }
    }
  }
}
</style>
