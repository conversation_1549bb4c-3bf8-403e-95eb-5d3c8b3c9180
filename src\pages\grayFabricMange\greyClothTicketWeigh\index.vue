<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Camera, Delete, DocumentAdd, Monitor, QuestionFilled, Setting, User } from '@element-plus/icons-vue'
import { GetFineCodeDetail, WeighingFineCode } from '@/api/clothTicket'

// 挂载快捷键监听

// 表单数据
const formData = reactive({
  // 生产信息录入
  barcode: '', // 条码编号
  machineNumber: '', // 机台号
  workerName: '', // 织工姓名
  weaverId: null, // 织工ID
  isShiftWorker: false, // 是否为交班织工
  shiftDuration: '', // 交班时长(rpm)
  shiftWeaverId: null, // 交班织工ID
  shiftWeaverName: '', // 交班织工姓名
  electronicWeight: '', // 电子称重量(kg)
  actualWeight: '', // 实际重量(kg)
  fineCodeId: null, // 细码ID
})

// 生产参数详情数据
const productionDetails = reactive({
  orderName: '', // 订单名称
  orderNumber: '', // 编号
  version: '', // 版号
  versionNumber: '', // 卷号
  batch: '', // 纱批
  machineType: '', // 织机类型
  machineColor: '', // 原料颜色
  needleSize: '', // 针寸数
  group: '', // 班次
  machine: '', // 机台
  canShift: '', // 是否交班布
  shiftTime: '', // 称重
  shiftWorker: '', // 交班转速
  workerName: '', // 交班织工
  actualWeight: '', // 实重
  operator: '', // 织工
  monthlyOutput: '', // 本月产量
  yearlyOutput: '', // 本月产量
  productionNote: '', // 生产备注
  weightOfFabric: '', // 布匹定重
})

// 加载状态
const loading = ref(false)

// 表单引用
const formRef = ref()

// 自动保存状态
const autoSave = ref(false)

// 获取细码详情
async function getFineCodeDetail(barcode?: string, id?: number) {
  if (!barcode && !id) {
    ElMessage.error('请提供条码或细码ID')
    return
  }

  loading.value = true
  try {
    const api = GetFineCodeDetail()
    api.query.value = {
      fabric_piece_code: barcode,
      id,
    }
    await api.fetchData()

    if (api.success.value && api.data.value) {
      const data = api.data.value
      // 更新表单数据
      formData.fineCodeId = data.id || null
      formData.machineNumber = data.machine_number || ''

      // 更新生产详情数据
      productionDetails.orderName = data.production_notify_order_no || ''
      productionDetails.orderNumber = data.fabric_piece_code || ''
      productionDetails.version = data.production_schedule_order_no || ''
      productionDetails.versionNumber = data.volume_number?.toString() || ''
      productionDetails.batch = data.yarn_batch || ''
      productionDetails.machineType = data.loom_model_name || ''
      productionDetails.machineColor = data.grey_fabric_color_name || ''
      productionDetails.needleSize = data.needle_size || ''
      productionDetails.machine = data.machine_number || ''
      productionDetails.actualWeight = data.weighing_weight ? `${data.weighing_weight}kg` : ''
      productionDetails.monthlyOutput = data.month_weighing_count ? `${data.month_weighing_count}件` : ''
      productionDetails.yearlyOutput = data.today_weighing_count ? `${data.today_weighing_count}件` : ''
      productionDetails.productionNote = data.produce_remark || ''
      productionDetails.weightOfFabric = data.weight_of_fabric ? `${data.weight_of_fabric}kg` : ''

      ElMessage.success('细码详情获取成功')
    }
    else {
      ElMessage.error('获取细码详情失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '获取细码详情失败')
  }
  finally {
    loading.value = false
  }
}

// 扫码输入处理
async function handleBarcodeInput() {
  if (formData.barcode)
    await getFineCodeDetail(formData.barcode)
}

// 织工姓名选择处理
function handleWorkerChange(worker: any) {
  if (worker) {
    formData.workerName = worker.name || worker
    formData.weaverId = worker.id || null
  }
}

// 称重保存
async function handleSave() {
  try {
    await formRef.value?.validate()

    if (!formData.fineCodeId) {
      ElMessage.error('请先扫码获取细码信息')
      return
    }

    if (!formData.weaverId) {
      ElMessage.error('请选择织工')
      return
    }

    loading.value = true

    const requestData: any = {
      id: formData.fineCodeId,
      actual_weight: Number(formData.actualWeight),
      electronic_scale_weight: Number(formData.electronicWeight),
      weaver_id: formData.weaverId,
      weaver_name: formData.workerName,
      is_shift_weaver: formData.isShiftWorker,
    }

    // 如果是交班织工，添加交班相关信息
    if (formData.isShiftWorker) {
      if (formData.shiftDuration)
        requestData.shift_speed = Number(formData.shiftDuration)

      if (formData.shiftWeaverId)
        requestData.shift_weaver_id = formData.shiftWeaverId

      if (formData.shiftWeaverName)
        requestData.shift_weaver_name = formData.shiftWeaverName
    }

    const api = WeighingFineCode()
    api.query.value = requestData
    await api.fetchData()

    if (api.success.value) {
      ElMessage.success('称重数据保存成功')
      // 重新获取细码详情以更新显示
      if (formData.fineCodeId)
        await getFineCodeDetail(undefined, formData.fineCodeId)
    }
    else {
      ElMessage.error('保存失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '保存失败，请检查表单填写')
  }
  finally {
    loading.value = false
  }
}

// 清空表单
function handleSaveChar() {
  // 重置表单数据
  Object.assign(formData, {
    barcode: '',
    machineNumber: '',
    workerName: '',
    weaverId: null,
    isShiftWorker: false,
    shiftDuration: '',
    shiftWeaverId: null,
    shiftWeaverName: '',
    electronicWeight: '',
    actualWeight: '',
    fineCodeId: null,
  })

  // 重置生产详情数据
  Object.assign(productionDetails, {
    orderName: '',
    orderNumber: '',
    version: '',
    versionNumber: '',
    batch: '',
    machineType: '',
    machineColor: '',
    needleSize: '',
    group: '',
    machine: '',
    canShift: '',
    shiftTime: '',
    shiftWorker: '',
    workerName: '',
    actualWeight: '',
    operator: '',
    monthlyOutput: '',
    yearlyOutput: '',
    productionNote: '',
    weightOfFabric: '',
  })

  ElMessage.success('表单已清空')
}

// 自动保存切换
function handleAutoSaveToggle() {
  if (autoSave.value)
    ElMessage.info('已开启自动保存')
  else
    ElMessage.info('已关闭自动保存')
}

// 监听F4快捷键
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'F4') {
    event.preventDefault()
    handleSave()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 表单验证规则
const rules = {
  barcode: [{ required: true, message: '请输入条码编号', trigger: 'blur' }],
  workerName: [{ required: true, message: '请选择织工姓名', trigger: 'change' }],
  electronicWeight: [
    { required: true, message: '请输入电子称重量', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value <= 0)
          callback(new Error('电子称重量必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
  actualWeight: [
    { required: true, message: '请输入实际重量', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value <= 0)
          callback(new Error('实际重量必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
}
</script>

<template>
  <div class="production-entry-page">
    <div class="main-content">
      <!-- 左侧：生产信息录入 -->
      <div class="left-panel">
        <el-card>
          <h2 class="font-bold text-lg">
            生产信息录入
          </h2>
          <el-form
            ref="formRef"
            label-position="top"
            :model="formData"
            :rules="rules"
            class="production-form"
          >
            <!-- 条码编号 -->
            <el-form-item label="条码编号" prop="barcode">
              <div class="input-with-icon">
                <el-input
                  v-model="formData.barcode"
                  size="default"
                  placeholder="扫码自动识别或手动输入"
                  @blur="handleBarcodeInput"
                  @keyup.enter="handleBarcodeInput"
                >
                  <template #append>
                    <el-icon :size="18" class="input-icon">
                      <Camera />
                    </el-icon>
                  </template>
                </el-input>
              </div>
            </el-form-item>

            <!-- 机台号 -->
            <el-form-item label="机台号" prop="machineNumber">
              <div class="input-with-icon">
                <el-input
                  v-model="formData.machineNumber"
                  size="default"
                  placeholder="自动带出"
                  class="readonly-input"
                  readonly
                >
                  <template #append>
                    <el-icon class="input-icon">
                      <Setting />
                    </el-icon>
                  </template>
                </el-input>
              </div>
            </el-form-item>

            <!-- 织工姓名 -->
            <el-form-item label="织工姓名" prop="workerName">
              <div class="worker-input-group">
                <div class="input-with-icon">
                  <el-select
                    v-model="formData.workerName"
                    size="default"
                    placeholder="选择"
                    filterable
                    @change="handleWorkerChange"
                  >
                    <el-option label="王五" value="王五" />
                    <el-option label="李四" value="李四" />
                    <el-option label="张三" value="张三" />
                  </el-select>
                </div>
              </div>
            </el-form-item>
            <el-divider />
            <!-- 是否为交班织工 -->
            <el-form-item label="是否为交班织工">
              <el-radio-group
                v-model="formData.isShiftWorker"
                size="default"
              >
                <el-radio :value="true" size="default">
                  是
                </el-radio>
                <el-radio :value="false" size="default">
                  否
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 交班时长 -->
            <el-form-item label="交班转速(rpm)">
              <template #label>
                <div class="flex items-center">
                  <span>交班转速(rpm)</span>
                  <el-icon :size="18" class="input-icon">
                    <QuestionFilled />
                  </el-icon>
                </div>
              </template>
              <div class="input-with-icon">
                <el-input-number
                  v-model="formData.shiftDuration"
                  :precision="2"
                  :min="0"
                  size="default"

                  placeholder="请输入交班转速"
                  class="shift-duration-input"
                />
              </div>
            </el-form-item>
            <el-divider />

            <!-- 称重信息 -->
            <div class="weight-section">
              <h4 class="section-title">
                称重信息
              </h4>
              <el-row class="w-full">
                <el-col :span="12">
                  <el-form-item label="" prop="electronicWeight">
                    <template #label>
                      <span class="inline-flex items-center">
                        <span>电子称重量(kg)</span>
                        <el-icon :size="18" class="input-icon">
                          <Monitor />
                        </el-icon>
                      </span>
                    </template>
                    <div class="input-with-icon">
                      <el-input-number
                        v-model="formData.electronicWeight"
                        size="default"
                        :precision="2"
                        :min="0"
                        placeholder="电子称重量"
                      />
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="" prop="actualWeight">
                    <template #label>
                      <span class="inline-flex items-center">
                        <span>实际重量(kg)</span>
                        <el-icon :size="18" class="input-icon">
                          <QuestionFilled />
                        </el-icon>
                      </span>
                    </template>
                    <div class="actual-weight-group">
                      <div class="input-with-icon">
                        <el-input-number
                          v-model="formData.actualWeight"
                          size="default"
                          :precision="2"
                          :min="0"
                          placeholder="实际重量（按定后重量）"
                        />
                      </div>
                      <span class="weight-note">还布要求定重：{{ productionDetails.weightOfFabric || '25kg' }}</span>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
          <div class="action-buttons">
            <div class="left-actions">
              <el-checkbox v-model="autoSave" @change="handleAutoSaveToggle">
                自动保存
              </el-checkbox>
              <span class="button-separator">设置电子秤</span>
            </div>
            <div class="right-actions">
              <el-button :disabled="loading" @click="handleSaveChar">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
              <el-button type="primary" :loading="loading" @click="handleSave">
                <el-icon><DocumentAdd /></el-icon>
                保存数据(F4)
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 中间：基本信息 -->
      <div class="right-panel">
        <el-card>
          <h2 class="font-bold text-lg">
            基本信息
          </h2>
          <div class="production-details">
            <!-- 基本信息 -->
            <div class="detail-row">
              <div class="detail-item">
                <label>订单名称：</label>
                <span class="detail-value">{{ productionDetails.orderName }}</span>
              </div>
              <div class="detail-item">
                <label>条码：</label>
                <span class="detail-value">{{ productionDetails.orderNumber }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>版号：</label>
                <span class="detail-value">{{ productionDetails.version }}</span>
              </div>
              <div class="detail-item">
                <label>卷号：</label>
                <span class="detail-value">{{ productionDetails.versionNumber }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>纱批：</label>
                <span class="detail-value">{{ productionDetails.batch }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>织机类型：</label>
                <span class="detail-value">{{ productionDetails.machineType }}</span>
              </div>
              <div class="detail-item">
                <label>原料颜色：</label>
                <span class="detail-value">{{ productionDetails.machineColor }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>针寸数：</label>
                <span class="detail-value">{{ productionDetails.needleSize }}</span>
              </div>
              <div class="detail-item">
                <label>班次：</label>
                <span class="detail-value">{{ productionDetails.group }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>机台：</label>
                <span class="detail-value">{{ productionDetails.machine }}</span>
              </div>
              <div class="detail-item">
                <label>是否交班布：</label>
                <span class="detail-value highlight">{{ productionDetails.canShift }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>称重：</label>
                <span class="detail-value">{{ productionDetails.shiftTime }}</span>
              </div>
              <div class="detail-item">
                <label>交班转速：</label>
                <span class="detail-value highlight">{{ productionDetails.shiftWorker }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>实重：</label>
                <span class="detail-value">{{ productionDetails.actualWeight || '25kg' }}</span>
              </div>
              <div class="detail-item">
                <label>交班织工：</label>
                <span class="detail-value highlight">{{ productionDetails.workerName }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>织工：</label>
                <span class="detail-value">{{ productionDetails.operator || '李四' }}</span>
              </div>
              <div class="detail-item flex">
                <div class="detail-item !p-0">
                  <label>本月产量：</label>
                  <span class="detail-value">{{ productionDetails.monthlyOutput }}</span>
                </div>
                <div class="detail-item !p-0">
                  <label>本月产量：</label>
                  <span class="detail-value">{{ productionDetails.yearlyOutput }}</span>
                </div>
              </div>
            </div>

            <!-- 生产备注 -->
            <div class="production-note">
              <label>生产备注：</label>
              <p class="note-content">
                {{ productionDetails.productionNote }}
              </p>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.production-entry-page {

  .main-content {
    display: flex;
    gap: 20px;

    .left-panel {
      flex: 1;
      min-width: 500px;
    }

    .right-panel {
      flex: 1;
      min-width: 500px;
    }
  }

  // 生产信息录入表单样式
  .production-form {
    .input-with-icon {
      width: 100%;

      .input-icon {
        color: #909399;
        font-size: 16px;
        pointer-events: none;
      }

      .el-input {
        ::v-deep .el-input__inner {
          padding-right: 35px;
        }
      }

      .el-select {
        ::v-deep .el-input__inner {
          padding-right: 35px;
        }
      }

      .el-input-number {
        ::v-deep .el-input__inner {
          padding-right: 35px;
        }
      }
    }

    .required-label {
      position: relative;

      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .readonly-input {
      ::v-deep .el-input__inner {
        background-color: #f5f7fa;
        color: #909399;
      }
    }

    .worker-input-group {
      .input-tip {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        line-height: 1.2;
      }

      .error-tip {
        display: block;
        font-size: 12px;
        color: #f56c6c;
        margin-top: 4px;
        line-height: 1.2;
      }
    }

    .shift-duration-input {
      margin-bottom: 8px;
    }

    .input-note {
      font-size: 12px;
      color: #f56c6c;
      line-height: 1.2;
    }

    .weight-section {

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .weight-inputs {
        .actual-weight-group {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .weight-note {
            font-size: 12px;
            color: #909399;
            line-height: 1.2;
          }
        }
      }
    }
  }

  // 生产参数详情样式
  .production-details {
    .detail-row {
      display: flex;
      margin-bottom: 10px;
      gap: 10px;

      .detail-item {
        flex: 1;
        min-height: 28px;
        background: white;
        padding: 10px;
        border-radius: 10px;
        label {
          display: block;
          font-weight: 500;
          color: #606266;
          margin-right: 8px;
          white-space: nowrap;
          min-width: 70px;
          font-size: 14px;
        }

        .detail-value {
          color: #303133;
          font-weight: 400;
          word-break: break-all;
          font-size: 14px;

          &.highlight {
            color: #67c23a;
            font-weight: 600;
          }
        }
      }
    }

    .production-note {
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #409eff;

      label {
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
        display: block;
      }

      .note-content {
        margin: 0;
        line-height: 1.6;
        color: #606266;
        font-size: 14px;
      }
    }
  }

}
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  // max-width: 600px;

  .left-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .el-checkbox {
      font-weight: 500;
      color: #606266;
    }

    .button-separator {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-button {
      min-width: 100px;
      font-weight: 500;

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}
// 全局样式覆盖
::v-deep .el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 16px 20px;

    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }

}

::v-deep .el-radio-group {
  .el-radio {
    margin-right: 20px;

    .el-radio__label {
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .production-entry-page {
    .main-content {
      flex-direction: column;

      .left-panel,
      .right-panel {
        min-width: auto;
      }
    }

    .production-details {
      .detail-row {
        flex-direction: column;
        gap: 12px;

        .detail-item {
          label {
            min-width: auto;
          }
        }
      }
    }
  }
}
</style>
