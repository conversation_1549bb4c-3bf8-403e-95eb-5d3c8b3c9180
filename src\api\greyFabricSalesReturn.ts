import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmSaleReturnOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/getGfmSaleReturnOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmSaleReturnOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/getGfmSaleReturnOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmSaleReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/getGfmSaleReturnOrder',
    method: 'get',
  })
}

// 审核
export const updateGfmSaleDeliveryOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/updateGfmSaleReturnOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmSaleDeliveryOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/updateGfmSaleReturnOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmSaleReturnOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/updateGfmSaleReturnOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmSaleReturnOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/updateGfmSaleReturnOrderStatusReject',
    method: 'put',
  })
}

// 新增数据
export const addGfmSaleReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/addGfmSaleReturnOrder',
    method: 'post',
  })
}

// 从销售出货单添加
export const getGfmSaleDeliveryOrderItemListEnum = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/getGfmSaleDeliveryOrderItemListEnum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取细码列表
export const getGfmSaleDeliveryOrderItemFineCodeByItemID = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/getGfmSaleDeliveryOrderItemFineCodeByItemID',
    method: 'get',
  })
}

// 更新
export const updateGfmSaleReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleReturnOrder/updateGfmSaleReturnOrder',
    method: 'put',
  })
}
