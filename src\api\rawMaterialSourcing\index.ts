import type { ResponseList } from '../commonTs'
import type { SystemAddRawMaterialPurchaseOrderParams, SystemGetRawMaterialPurchaseOrderDetailResponse, SystemGetRawMaterialPurchaseOrderListItem } from './rules'
import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 原料采购订单列表
export function RawMaterialList() {
  return useRequest<any, ResponseList<SystemGetRawMaterialPurchaseOrderListItem>>({
    url: `/admin/v1/purchase_order/raw_material/list`,
    method: 'get',
    pageSize: 50,
    pagination: true,
  })
}

// 原料采购订单列表--导出
export function RawMaterialListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/purchase_order/raw_material/output',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 新增原料采购订单
export function PurchaseOrderRawMaterial() {
  return useRequest<SystemAddRawMaterialPurchaseOrderParams>({
    url: `/admin/v1/purchase_order/raw_material`,
    method: 'post',
  })
}
// 更新原料采购订单
export function PurchaseOrderRawMaterialEdit() {
  return useRequest({
    url: `/admin/v1/purchase_order/raw_material`,
    method: 'put',
  })
}

// 原料采购订单详情
export function PurchaseOrderRawMaterialDetail() {
  return useRequest<any, SystemGetRawMaterialPurchaseOrderDetailResponse>({
    url: `/admin/v1/purchase_order/raw_material/detail`,
    method: 'get',
  })
}

// 审核原料采购订单
export function PurchaseOrderRawMaterialPass() {
  return useRequest({
    url: `/admin/v1/purchase_order/raw_material/pass`,
    method: 'put',
  })
}

// 驳回原料采购订单
export function PurchaseOrderRawMaterialReject() {
  return useRequest({
    url: `/admin/v1/purchase_order/raw_material/reject`,
    method: 'put',
  })
}
// 更新BusinessClose字段
export function PurchaseOrderRawMaterialBusinessClose() {
  return useRequest({
    url: '/admin/v1/purchase_order/raw_material/business_close',
    method: 'put',
  })
}

// 作废原料采购订单
export function PurchaseOrderRawMaterialVoid() {
  return useRequest({
    url: `/admin/v1/purchase_order/raw_material/void`,
    method: 'put',
  })
}

// 消审原料采购订单
export function PurchaseOrderRawMaterialCancel() {
  return useRequest({
    url: `/admin/v1/purchase_order/raw_material/cancel`,
    method: 'put',
  })
}

// 获取未收货的原料项列表
export function NotReceiveItemList() {
  return useRequest({
    url: `/admin/v1/purchase_order/raw_material/item_list`,
    method: 'get',
    pageSize: 50,
    pagination: true,
  })
}
