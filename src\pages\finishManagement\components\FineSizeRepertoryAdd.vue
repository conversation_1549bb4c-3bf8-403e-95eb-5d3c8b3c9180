<script setup lang="ts">
import { computed, h, nextTick, reactive, ref, watch } from 'vue'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { VxeInput } from 'vxe-pc-ui'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import { arrayToString, deleteToast } from '@/common/util'
import { getMaxVolumeNumber } from '@/api/finishPurchaseWarehouseEntry'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'
import GridTable from '@/components/GridTable/index.vue'
import { MonthTransferOrderEnum } from '@/enum/orderEnum'
import { formatHashTag } from '@/common/format'
import SelectComponents from '@/components/SelectComponents/index.vue'

const emits = defineEmits(['handleSure'])

// 是否内部调拨单据类型
const isInternalAllot = ref<boolean | ''>('')

const {
  fetchData: fetchVNData,
  data: vNData,
  success,
  msg,
} = getMaxVolumeNumber()

const TablesRef = ref()
const form_options = computed(() => ([
  {
    text: '成品名称',
    key: 'product_code',
    formatter: (row: any) => {
      return formatHashTag(row.product_code, row.product_name)
    },
  },
  {
    text: '色号颜色',
    key: 'product_color_name',
    formatter: (row: any) => {
      return formatHashTag(row.product_color_code, row.product_color_name)
    },
  },
  ...(isInternalAllot.value
    ? []
    : [
        {
          text: '幅宽/克重',
          key: 'finish_product_width_and_unit_name',
          formatter: (row: any) => {
            return arrayToString([row.finish_product_width_and_unit_name, row.finish_product_gram_weight_and_unit_name], '*')
          },
        },
      ]
  ),
  {
    text: '面料单位',
    key: 'unit_name',
  },

]))
const list = ref<any[]>([])

const state = reactive<any>({
  baseDataForm: {},
  showModal: false,
  modalName: '细码录入',
  multipleSelection: [],
  in_roll: 0,
  warehouse_id: 0, // 仓库id
  billType: '', // 单据类型
  // 单位
  unit: {
    unit_name: '',
    unit_id: '',
  },
})
function handleSelectAllList(checked: boolean) {
  if (checked)
    state.multipleSelection = list.value
  else
    state.multipleSelection = []
}
const loading = ref(false)

function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      //   匹数 空差 单位 辅助数量 总数量
      if (['roll'].includes(column.field))
        return sumNum(data, 'roll', '')

      if (['base_unit_weight', 'paper_tube_weight'].includes(column.field))
        return sumNum(data, column.field, '')

      if (['weight_error'].includes(column.field))
        return sumNum(data, 'weight_error', '')

      if (['length'].includes(column.field))
        return sumNum(data, 'length', '')

      return null
    }),
  ]
  return footerData
}

watch(
  () => list.value,
  () => {
    nextTick(() => {
      TablesRef.value?.TableRef?.updateFooter()
    })
  },
  { deep: true },
)

let uuid = 0
let volume_number = 0
// 批量修改仓位
// const BulkEditWarehouse = () => {
//   return h(SelectComponents, {
//     query: {
//       physical_warehouse_id: state.warehouse_id,
//     },
//     api: 'GetPhysicalWarehouseBinListEnum',
//     modelValue: warehouse_bin_id.value,
//     'onUpdate:modelValue': value => (warehouse_bin_id.value = value),
//     onChangeValue: (val: any) => {
//       //   批量修改仓位
//       list.value.forEach((item: any) => {
//         item.warehouse_bin_id = val.id
//       })
//     },
//     labelField: 'name',
//     valueField: 'id',
//     clearable: true,
//   })
// }

const listItemSameWarehouse = ref<any>('') // 该数据后面的仓位一致
/**
 * 更新该下标之后的所有仓位
 * @param rowIndex 下标
 */
function dealWarehouseBin(rowIndex: number) {
  const curItem = listItemSameWarehouse.value
  if (curItem === '')
    return
  list.value.forEach((item: any, index: number) => {
    if (index > rowIndex) {
      item.warehouse_bin_id = curItem.warehouse_bin_id
      item.warehouse_bin_name = curItem.warehouse_bin_name
    }
  })
}

function handAdd(selected = false) {
  // 新增时校验匹数总量
  // const flag = regRollCount()
  // if (!flag) return
  const newRow = {
    uuid: ++uuid,
    roll: !state.in_roll ? 0 : 1, // 如果进仓匹数为0，匹数就为0
    warehouse_bin_id: listItemSameWarehouse.value?.warehouse_bin_id || '',
    warehouse_bin_name: listItemSameWarehouse.value?.warehouse_bin_name || '',
    volume_number: ++volume_number, // 根据库存自动递增
    base_unit_weight: '',
    weight_error: !list.value.length ? state.baseDataForm.product_weight_error : list.value[0]?.weight_error, // 若是第一行则带出成品资料的空差，否则是上一个的空差
    unit_name: state.unit.unit_name,
    unit_id: state.unit.unit_id,
    paper_tube_weight: !list.value.length ? state.baseDataForm.product_paper_tube_weight : list.value[0]?.paper_tube_weight,
    length: '',
    bar_code: '',
    shelf_no: '',
    remark: '',
    selected,
  }
  list.value.push(newRow)
  // if (selected) {
  //   nextTick(() => {

  // TablesRef.value?.TableRef?.loadData(list.value)
  //   })
  // }
}

const { addNewLineOnGridTable } = useTableEnterAutoFocus(
  TablesRef,
  list,
  handAdd,
)

const columnList: any = ref([])
function getColumnList() {
  const newCcolumnList: any = [
    {
      field: 'roll',
      title: '匹数',
      width: 100,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.roll
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            type: 'integer',
            id: `roll${rowIndex}`,
            onKeydown: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'roll'),
            min: '0',
            modelValue: row.roll,
            onInput: (val) => {
              row.roll = val.value
            },
          })
        },
      },
    },
    {
      field: 'volume_number',
      title: '卷号',
      sortable: true,
      width: 120,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.volume_number
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `volume_number${rowIndex}`,
            maxLength: 20,
            type: 'text',
            onKeydown: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'volume_number'),
            modelValue: row.volume_number,
            onInput: (val) => {
              row.volume_number = val.value
            },
          })
        },
      },
    },
    {
      field: 'warehouse_bin_id',
      title: '仓位',
      sortable: true,
      minWidth: 120,
      editRender: { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.warehouse_bin_name
        },
        edit: ({ row, rowIndex }) => {
          return h(SelectComponents, {
            query: { physical_warehouse_id: state.warehouse_id },
            api: 'GetPhysicalWarehouseBinListEnum',
            labelField: 'name',
            valueField: 'id',
            clearable: true,
            modelValue: row.warehouse_bin_id,
            onInput: (val) => {
              row.warehouse_bin_id = val.value
            },
            onChangeValue: async (val: any) => {
              row.warehouse_bin_name = val.name
              row.warehouse_bin_id = val.id
              // 第一行才做处理
              if (rowIndex === 0 && val.id) {
                const res = await deleteToast('下面的细码仓位是否一致？')
                if (res) {
                  listItemSameWarehouse.value = row
                  dealWarehouseBin(rowIndex)
                }
              }
            },
          })
        },
      },
    },
    {
      field: 'base_unit_weight',
      title: '数量',
      minWidth: 120,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      required: !isInternalAllot.value,
      slots: {
        default: ({ row }) => {
          return row.base_unit_weight
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `base_unit_weight${rowIndex}`,
            type: 'float',
            onKeydown: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'base_unit_weight'),
            modelValue: row.base_unit_weight,
            onInput: (val) => {
              row.base_unit_weight = val.value
            },
          })
        },
      },
    },
    {
      field: 'weight_error',
      title: '空差',
      width: 100,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.weight_error
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `weight_error${rowIndex}`,
            type: 'float',
            onKeydown: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'weight_error'),
            modelValue: row.weight_error,
            onInput: (val) => {
              // 所有空差都一样
              list.value.forEach((item: any) => {
                item.weight_error = val.value
              })
            },
          })
        },
      },
    },
    {
      field: 'paper_tube_weight',
      minWidth: 100,
      title: '纸筒',
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.paper_tube_weight
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `paper_tube_weight${rowIndex}`,
            type: 'float',
            onKeyup: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'paper_tube_weight'),
            modelValue: row.paper_tube_weight,
            onInput: (val) => {
              // 所纸筒都一样
              list.value.forEach((item: any) => {
                item.paper_tube_weight = val.value
              })
            },
          })
        },
      },
    },
    {
      field: 'length',
      title: '辅助数量',
      minWidth: 100,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.length
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `length${rowIndex}`,
            type: 'float',
            onKeyup: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'length'),
            modelValue: row.length,
            onInput: (val) => {
              row.length = val.value
            },
          })
        },
      },
    },
    ...(isInternalAllot.value
      ? [{
          field: 'finish_product_width_and_unit_name',
          title: '成品幅宽',
          width: 100,
        }, {
          field: 'finish_product_gram_weight_and_unit_name',
          title: '成品克重',
          width: 100,
        }]
      : []),
    {
      field: 'shelf_no',
      title: '货架号',
      width: 100,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.shelf_no
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `shelf_no${rowIndex}`,
            type: 'text',
            onKeyup: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'shelf_no'),
            modelValue: row.shelf_no,
            onInput: (val) => {
              row.shelf_no = val.value
            },
          })
        },
      },
    },
    {
      field: 'internal_remark',
      title: '对内备注',
      minWidth: 120,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.internal_remark
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `internal_remark${rowIndex}`,
            type: 'text',
            onKeyup: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'internal_remark'),
            modelValue: row.internal_remark,
            onInput: (val) => {
              row.internal_remark = val.value
            },
          })
        },
      },
    },
    {
      field: 'remark',
      title: '对外备注',
      minWidth: 120,
      editRender: isInternalAllot.value ? undefined : { autofocus: '.vxe-input--inner' },
      slots: {
        default: ({ row }) => {
          return row.remark
        },
        edit: ({ row, rowIndex }) => {
          return h(VxeInput, {
            id: `remark${rowIndex}`,
            type: 'text',
            onKeyup: $event =>
              addNewLineOnGridTable(rowIndex, $event, 'remark'),
            modelValue: row.remark,
            onInput: (val) => {
              row.remark = val.value
            },
          })
        },
      },
    },
    {
      field: 'bar_code',
      title: '条码', // 条码自动生成
      width: 100,
      slots: {
        default: ({ row }) => {
          return row.bar_code
        },
      },
    },

    // {
    //   field: 'unit_name',
    //   width: 80,
    //   title: '单位',
    // },

  ]
  columnList.value = newCcolumnList
  // TablesRef.value?.TableRef?.reloadColumn(newCcolumnList)
  getBulkList()
}

// 批量操作
const bulkSetting = ref<any>({})
let bulkList = reactive<any>([])
function getBulkList() {
  const newBulkList = reactive<any>([
    ...(isInternalAllot.value
      ? [{
          field: 'warehouse_bin_id',
          field_name: 'warehouse_bin_name',
          title: '仓位',
          component: 'select',
          api: 'GetPhysicalWarehouseBinListEnum',
          query: {
            physical_warehouse_id: 0,
          },
        }]
      : [
          {
            field: 'roll',
            title: '匹数',
            component: 'input',
            type: 'integer',
            quickInput: true,
          },
          {
            field: 'volume_number',
            title: '卷号',
            component: 'input',
            type: 'float',
            quickInput: true,
          },
          {
            field: 'warehouse_bin_id',
            field_name: 'warehouse_bin_name',
            title: '仓位',
            component: 'select',
            api: 'GetPhysicalWarehouseBinListEnum',
            query: {
              physical_warehouse_id: 0,
            },
          },
          {
            field: 'base_unit_weight',
            title: '数量',
            component: 'input',
            type: 'float',
            quickInput: true,
          },
          // {
          //   field: 'weight_error',
          //   title: '空差',
          //   component: 'input',
          //   type: 'float',
          //   quickInput: true,
          // },
          // {
          //   field: 'paper_tube_weight',
          //   title: '纸筒',
          //   component: 'input',
          //   type: 'float',
          //   quickInput: true,
          // },
          {
            field: 'length',
            title: '辅助数量',
            component: 'input',
            type: 'float',
            quickInput: true,
          },
          {
            field: 'shelf_no',
            title: '货架号',
            component: 'input',
            type: 'text',
            quickInput: true,
          },
          {
            field: 'internal_remark',
            title: '对内备注',
            component: 'input',
            type: 'text',
            quickInput: true,
          },
          {
            field: 'remark',
            title: '对外备注',
            component: 'input',
            type: 'text',
            quickInput: true,
          },
        ]),

  ])
  bulkList = newBulkList
}

const bulkShow = ref(false)
function handEdit() {
  if (state.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}

// watch(
//   () => state.warehouse_id,
//   () => {
//     bulkList[1].query.physical_warehouse_id = state.warehouse_id
//   },
//   { deep: true }
// )

async function bulkSubmit({ row, value, quickInputResult }: any) {
  if (
    row.field === 'roll'
    && list.value.length * value.roll > state.baseDataForm.in_roll
  )
    return ElMessage.error('匹数过大超过当前分录行的匹数')
  let quickInputIndex = 0
  list.value.map((item: any) => {
    if (!item?.selected)
      return
    if (row.quickInput && quickInputResult?.[quickInputIndex]) {
      item[row.field] = quickInputResult[quickInputIndex++]
      return item
    }
    item[row.field] = value[row.field]
    item[row.field_name] = value[row.field_name]
  })

  ElMessage.success('设置成功')
  // TablesRef.value?.TableRef?.updateFooter()
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 批量删除
async function handBathDel() {
  if (state.multipleSelection.length === 0)
    return ElMessage.error('请先勾选数据')

  const res = await deleteToast('确认删除已选中的数据嘛？')
  if (res) {
    list.value = list.value.filter(
      (item: any) =>
        !state.multipleSelection.some((val: any) => val.uuid === item.uuid),
    )
    // 清除设置的仓位
    if (state.multipleSelection.find((e: any) => e._X_ROW_KEY === listItemSameWarehouse.value?._X_ROW_KEY))
      listItemSameWarehouse.value = ''
    state.multipleSelection = []
    TablesRef.value?.TableRef?.loadData(list.value)
  }
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

async function showDialog(row: any, { warehouse_id, billType = '' }: any) {
  loading.value = true
  isInternalAllot.value = billType === MonthTransferOrderEnum.FpInteriorAllotWarehouseEntryOrder // 是否为内部调拨进仓单
  getColumnList()
  const {
    in_roll,
    unit_name,
    unit_id,
    measurement_unit_name,
    measurement_unit_id,
  } = row
  state.in_roll = Number(in_roll)
  let loopCount = state.in_roll // 循环次数
  state.baseDataForm = row
  state.warehouse_id = warehouse_id
  const warehouseBulkIndex = bulkList.findIndex(item => item.field === 'warehouse_bin_id')
  bulkList[warehouseBulkIndex].query.physical_warehouse_id = warehouse_id
  state.billType = billType
  state.unit = {
    unit_name: unit_name || measurement_unit_name,
    unit_id: unit_id || measurement_unit_id,
  }
  list.value = []
  await getFSMaxVolumeNumber(row)
  const item_fc_data = Array.isArray(row.item_fc_data)
    ? row.item_fc_data
    : row.item_data
  if (!item_fc_data?.length) {
    // 新增
    if (!loopCount)
      loopCount = 1

    for (let i = 0; i < loopCount; i++)

      handAdd(true)
  }
  else {
    // 数据回显
    list.value = item_fc_data.map((item: any) => {
      if (item.volume_number > volume_number)
        volume_number = item.volume_number

      return {
        ...item,
        uuid: ++uuid,
        warehouse_bin_id: item.warehouse_bin_id || '',
      }
    })
  }
  state.multipleSelection = list.value.map((item) => {
    nextTick(() => {
      TablesRef.value.TableRef.setCheckboxRow(item, true)
    })
    return item
  })
  state.showModal = true
  loading.value = false
}

async function handleSure() {
  if (!list.value.length)
    return ElMessage.error('请添加数据')

  // 失去焦点验证匹数
  const allRoll = list.value.reduce((pre: any, val: any) => {
    return pre + Number(val.roll)
  }, 0)

  let volume_numbers = []
  for (let i = 0; i < list.value.length; i++) {
    if (Number(list.value[i].roll === ''))
      return ElMessage.error('匹数为必填项')

    if (!Number(list.value[i].warehouse_bin_id))
      return ElMessage.error('仓位为必填项')

    if (!Number(list.value[i].base_unit_weight))
      return ElMessage.error('数量为必填项')

    if (
      Number(list.value[i].weight_error)
      > Number(list.value[i].base_unit_weight)
    )
      return ElMessage.error('空差数据不可大于数量')

    volume_numbers.push(Number(list.value[i].volume_number))
    // rollCount += Number(list.value[i].roll)
  }

  // if (!regFlag) return ElMessage.error('匹数、仓位和数量是必填项')
  // if (!regWeightFlag) return ElMessage.error('空差数据不可大于数量')
  // let volume_numbers = list.value.map((item: any) => item.volume_number)
  volume_numbers = [...new Set(volume_numbers)]
  if (volume_numbers.length !== list.value.length)
    return ElMessage.error('卷号必须唯一，请重新修改卷号')

  // const rollCount = list.value.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
  //

  // if (state.in_roll < rollCount) {
  //   return ElMessage.error('匹数总和超过当前分录行匹数')
  // }
  if (allRoll > Number(state.baseDataForm.in_roll)) {
    await ElMessageBox.confirm(
      '当前匹数总和已经超过当前分录行的匹数，是否修改',
      '匹数总和提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      },
    )
  }
  emits(
    'handleSure',
    list.value.map((item: any) => {
      return {
        ...item,
        paper_tube_weight: item.paper_tube_weight,
        bar_code: item.bar_code,
        length: item.length,
        base_unit_weight: Number(item.base_unit_weight),
        remark: item.remark,
        roll: Number(item.roll),
        selected: true,
        shelf_no: item.shelf_no,
        unit_name: item.unit_name,
        unit_id: item.unit_id,
        // uuid: item.uuid,
        volume_number: Number(item.volume_number),
        warehouse_bin_id: item.warehouse_bin_id,
        warehouse_bin_name: item.warehouse_bin_name,
        weight_error: item.weight_error,
        dye_factory_color_code: state.baseDataForm.dye_factory_color_code,
      }
    }),
  )
  state.showModal = false
}
function handCancel() {
  state.showModal = false
}

// 获取库存卷号
async function getFSMaxVolumeNumber(row: any) {
  await fetchVNData({
    dyelot_number: row.dye_factory_dyelot_number, // 'RC2023060501'
    product_color_id: row.product_color_id, // 1624484531228928
  })
  if (!success.value)
    return ElMessage.error(msg.value)

  volume_number = vNData.value.volume_number
}
defineExpose({
  state,
  showDialog,
  handAdd,
})

const columnList_config = computed(() => ({
  showSeq: true,
  // showSpanHeader: true,
  checkboxConfig: {
    checkField: 'selected',
  },
  filterConfig: {
    showIcon: false,
  },
  // fieldApiKey: 'FineSizeRepertoryAdd789844545456456',
  editRules: {
    roll: [
      { required: true, message: '盘点匹数为必填项' },
      {
        validator({ cellValue }) {
          // 模拟服务端校验
          return new Promise<void>((resolve, reject) => {
            if (!cellValue)
              reject(new Error('盘点匹数为必填项'))
            else
              resolve()
          })
        },
      },
    ],
    warehouse_bin_id: [
      { required: true, message: '仓位为必填项' },
      {
        validator({ cellValue }) {
          // 模拟服务端校验
          return new Promise<void>((resolve, reject) => {
            if (!cellValue)
              reject(new Error('仓位为必填项'))
            else
              resolve()
          })
        },
      },
    ],
    base_unit_weight: [
      { required: true, message: '数量为必填项' },
      {
        validator({ cellValue }) {
          // 模拟服务端校验
          return new Promise<void>((resolve, reject) => {
            if (!cellValue)
              reject(new Error('数量为必填项'))
            else
              resolve()
          })
        },
      },
    ],
    volume_number: [
      { required: true, message: '卷号为必填项' },
      {
        validator({ cellValue }) {
          // 模拟服务端校验
          return new Promise<void>((resolve, reject) => {
            if (!cellValue)
              reject(new Error('卷号不能为0'))
            else
              resolve()
          })
        },
      },
    ],
  },
  loading: loading.value,
  showCheckBox: true,
  scrollY: { enabled: true },
  footerMethod: FooterMethod,
  handAllSelect,
  handleSelectionChange,
}))
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    show-footer
    :title="state.modalName"
    width="80vw"
    height="80vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
  >
    <div class="list-page">
      <div
        v-if="form_options.length"
        class="descriptions_row vertical_min"
      >
        <DescriptionsFormItem
          v-for="(item, index) in form_options"
          :key="index"
          :copies="item.copies || 1"
          :label="`${item.text}`"
          vertical-block
        >
          <template #content>
            {{ item?.formatter ? item.formatter(state.baseDataForm) : state.baseDataForm[item.key] }}
          </template>
        </DescriptionsFormItem>
      </div>
      <FildCard
        title=""
        class="table-card-full"
        no-shadow
        :tool-bar="false"
      >
        <template #right-top>
          <ElButton v-if="!isInternalAllot" type="primary" @click="handAdd()">
            新增
          </ElButton>
          <ElButton v-if="!isInternalAllot" type="danger" @click="handBathDel">
            批量删除
          </ElButton>
          <ElButton type="primary" @click="handEdit">
            批量操作
          </ElButton>
        </template>
        <div class="flex-1 flex flex-col overflow-hidden h-full">
          <GridTable
            ref="TablesRef"
            :columns="columnList"
            :data="list"
            :config="columnList_config"
            show-pagition
            height="100%"
            @change-select-all-list-data="handleSelectAllList"
          />
        </div>
      </FildCard>
    </div>
    <template #footer>
      <ElButton type="primary" @click="handCancel">
        取消
      </ElButton>
      <ElButton type="primary" @click="handleSure">
        确认
      </ElButton>
    </template>
  </vxe-modal>
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  >
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 100px;
      text-align: right;
    }
  }
}
</style>
