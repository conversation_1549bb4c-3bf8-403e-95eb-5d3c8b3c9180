import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmStockAdjustOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/getGfmStockAdjustOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmStockAdjustOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/getGfmStockAdjustOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmStockAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/getGfmStockAdjustOrder',
    method: 'get',
  })
}

// 新增数据
export const addGfmStockAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/addGfmStockAdjustOrder',
    method: 'post',
  })
}

// 编辑数据
export const updateGfmStockAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/updateGfmStockAdjustOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmStockAdjustOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/updateGfmStockAdjustOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmStockAdjustOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/updateGfmStockAdjustOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmStockAdjustOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/updateGfmStockAdjustOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmStockAdjustOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmStockAdjustOrder/updateGfmStockAdjustOrderStatusReject',
    method: 'put',
  })
}
