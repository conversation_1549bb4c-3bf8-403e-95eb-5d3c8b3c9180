<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import { ArrowLeft, ArrowRight, Delete } from '@element-plus/icons-vue'
import { getStockProductDyelotNumberDetailList } from '@/api/fpPurchaseReturnDeliverGodown'
import { getStockProductDropdownList } from '@/api/productOutStock'
import { formatLengthDiv, formatPriceDiv, formatRollDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import { deepClone, deleteToast, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import FildCard from '@/components/FildCard.vue'
import type { TableColumn } from '@/components/Table/type'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

export interface Props {
  obj: any
}

const emits = defineEmits(['handleSure'])

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})

const filterData = ref({
  product_id: '',
  customer_id: '',
  product_color_id: '',
  warehouse_id: '',
  product_level_id: '',
  dyelot_number: '',
  available_only: false,
  radioValue: '1',
  with_price: true,
})

const state = reactive<any>({
  showModal: false,
  modalName: '根据库存添加',
  multipleSelection: [],
  list: [],
  sale_customer_id: '',
  cashList: [],
  info: {},
  rowIndex: -1,
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  success: success1,
  msg: msg1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getStockProductDyelotNumberDetailList()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  size: size2,
  success: success2,
  msg: msg2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = getStockProductDropdownList()

const tableConfig = reactive<any>({
  fieldApiKey: 'AddBasedOnInventory',
  showField: true,
  loading: filterData.value.radioValue === '1' ? loading1 : loading2,
  showPagition: true,
  showSlotNums: true,
  page: filterData.value.radioValue === '1' ? page1 : page2,
  size: filterData.value.radioValue === '1' ? size1 : size2,
  total: filterData.value.radioValue === '1' ? total1 : total2,
  showCheckBox: true,
  height: 'auto',
  showSort: false,
  filterStatus: false,
  checkboxConfig: {
    checkField: 'selected',
    checkMethod: ({ row }: any) => {
      return row.available_weight || row.available_roll
    },
  },
  handleSizeChange: (val: number) => (filterData.value.radioValue === '1' ? handleSizeChange1(val) : handleSizeChange2(val)),
  handleCurrentChange: (val: number) => (filterData.value.radioValue === '1' ? handleCurrentChange1(val) : handleCurrentChange2(val)),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod(val),
  // checkboxConfig: {
  //   selected: true,
  //   highlight: true,
  //   reserve: true,
  //   checkMethod: ({ row }: any) => {
  //     return row.available_roll !== 0
  //   },
  // },
})

const tableConfig_second = ref({
  fieldApiKey: 'AddBasedOnInventorySecond',
  showCheckBox: true,
  showSlotNums: true,
  showSort: false,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethodSc(val),
  handAllSelect: () => handAllSelect_twince(),
  handleSelectionChange: (val: any) => handleSelectionChange_twince(val),
  showOperate: true,
  height: 'auto',
  operateWidth: '100',
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['stock_roll'].includes(column.property))
        return `${formatRollDiv(sumNum(data, 'stock_roll'))}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight'))}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length'))}`

      if (['available_roll'].includes(column.property))
        return `${formatRollDiv(sumNum(data, 'available_roll'))}`

      return null
    }),
  ]
}

const returnPlan = ref({
  roll: 0,
  weight: 0,
})
function FooterMethodSc({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['stock_roll'].includes(column.property))
        return `${formatRollDiv(sumNum(data, 'stock_roll'))}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight'))}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length'))}`

      if (['available_roll'].includes(column.property))
        return `${formatRollDiv(sumNum(data, 'available_roll'))}`

      if (['use_roll'].includes(column.property)) {
        returnPlan.value.roll = Number(sumNum(data, 'use_roll'))
        return `${sumNum(data, 'use_roll')}`
      }
      if (['use_weight'].includes(column.property)) {
        returnPlan.value.weight = Number(sumNum(data, 'use_weight'))
        return `${sumNum(data, 'use_weight')}`
      }
      return null
    }),
  ]
}
function handChange(val: any) {
  filterData.value.radioValue = val.toString()
}

async function getData() {
  const query = {
    sale_customer_id: state?.sale_customer_id,
    ...filterData.value,
  }
  if (filterData.value.radioValue === '1') {
    await fetchData1(getFilterData(query))
    if (!success1.value)
      return ElMessage.error(msg1.value)
  }
  else {
    await fetchData2(getFilterData(query))
    if (!success2.value)
      return ElMessage.error(msg2.value)
  }
}

// 处理list数据
watch([
  () => data2.value.list,
  () => data1.value.list,
], () => {
  formatList()
}, {
  deep: true,
})
async function formatList() {
  if (filterData.value.radioValue === '1') {
    const dataList = deepClone(data1?.value?.list || [])
    state.list = dataList.map((item: any) => {
      item.choose_sign = 1
      item.use_weight = 0

      item.selected = state.multipleSelection?.some((citem: any) => citem.id === item.id && citem.choose_sign === item.choose_sign && citem.available_weight !== 0) ?? false
      return item
    })
    // TODO:缓存记住当前页的数据内容，以便取消的时候可以剔除
    state.cashList = dataList
  }
  else {
    const dataList = deepClone(data2?.value?.list || [])
    state.list = dataList?.map((item: any) => {
      item.use_weight = 0

      item.choose_sign = 2
      item.selected = state.multipleSelection?.some((citem: any) => citem.id === item.id && citem.choose_sign === item.choose_sign && citem.available_weight !== 0) ?? false
      return item
    })

    // TODO:缓存记住当前页的数据内容，以便取消的时候可以剔除
    state.cashList = dataList
  }
  tableConfig.total = filterData.value.radioValue === '1' ? total1 : total2
  tableConfig.page = filterData.value.radioValue === '1' ? page1 : page2
  tableConfig.size = filterData.value.radioValue === '1' ? size1 : size2
}

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
    else
      state.multipleSelection = []
  },
)

watch(
  () => filterData.value,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableRef = ref()
watch(
  () => state.multipleSelection,
  () => {
    // state.multipleSelection = state.multipleSelection.map((item: any) => {
    state.multipleSelection.map((item: any) => {
      item.selected = true
      return item
    })

    nextTick(() => {
      tableRef.value?.tableRef?.updateFooter()
    })
  },
  {
    deep: true,
  },
)

function handAllSelect({ records, checked }: any) {
  if (checked) {
    const resDult = checkSameIdAndChooseSign(state.multipleSelection, records)
    if (resDult)
      state.multipleSelection = margeArr(state.multipleSelection, records)
    else
      state.multipleSelection = [...records, ...state.multipleSelection]

    state.multipleSelection?.map((item: any) => {
      const arrRoll = sumTotal(state.multipleSelection, 'use_roll')

      const arrWeight = sumTotal(state.multipleSelection, 'use_weight')

      // available_roll 可用匹数 horsepower 计划匹数 arrRoll 已分配匹数
      item.use_roll = formatPriceDiv(item.available_roll) >= Number(state.info.roll) - arrRoll ? Number(state.info.roll) - arrRoll : formatPriceDiv(item.available_roll)
      item.use_weight = formatWeightDiv(item.available_weight) >= Number(state.info.weight) - arrWeight ? Number(state.info.weight) - arrWeight : formatWeightDiv(item.available_weight)
      return item
    })
  }
  else {
    state.multipleSelection = state.multipleSelection.filter((item2: any) => !state.cashList.some((item1: any) => item1.id === item2.id && item1.choose_sign === item2.choose_sign))
  }
}

function handleSelectionChange({ checked, row }: any) {
  if (checked) {
    state.multipleSelection.push(row)

    // // 已分配匹数
    // const resultRoll = averageAllocation({
    //   items: state.multipleSelection.map(item => formatPriceDiv(item.available_roll)),
    //   capacity: 10,
    // })
    // // 已分配数量
    // const resultWeight = averageAllocation({
    //   items: state.multipleSelection.map(item => formatPriceDiv(item.available_weight)),
    //   capacity: 10,
    // })
    state.multipleSelection?.map((item: any) => {
      if (!item.use_roll && !item.use_weight) {
        const arrRoll = sumTotal(state.multipleSelection, 'use_roll')

        const arrWeight = sumTotal(state.multipleSelection, 'use_weight')

        // available_roll 可用匹数
        item.use_roll = formatPriceDiv(item.available_roll) >= Number(state.info.roll) - arrRoll ? Number(state.info.roll) - arrRoll : formatPriceDiv(item.available_roll)

        item.use_weight = formatWeightDiv(item.available_weight) >= Number(state.info.weight) - arrWeight ? Number(state.info.weight) - arrWeight : formatWeightDiv(item.available_weight)
      }

      return item
    })
  }
  else {
    state.multipleSelection = removeObjectFromArray(row.id, row.choose_sign, state.multipleSelection)
  }
}

function handAllSelect_twince() {
  state.multipleSelection = []
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

function handleSelectionChange_twince({ rowIndex }: any) {
  state.multipleSelection.splice(rowIndex, 1)
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

// 合并清洗数据
function margeArr(array1: any, array2: any) {
  const mergedArray = array1.concat(array2)
  const uniqueArray = []
  const visitedIds = new Set()

  for (const item of mergedArray) {
    const key = `${item.id}-${item.choose_sign}`
    if (!visitedIds.has(key)) {
      visitedIds.add(key)
      uniqueArray.push(item)
    }
  }

  return uniqueArray
}

// 查找两个数组的id和choose_sign的值是否相同
function checkSameIdAndChooseSign(array1: any, array2: any) {
  for (let i = 0; i < array1.length; i++) {
    for (let j = 0; j < array2.length; j++) {
      if (array1[i].id === array2[j].id && array1[i].choose_sign === array2[j].choose_sign)
        return true
    }
  }
  return false
}

function removeObjectFromArray(id: number, choose_sign: number, array: any) {
  for (let i = 0; i < array.length; i++) {
    if (array[i].id === id && array[i].choose_sign === choose_sign) {
      array.splice(i, 1)
      break
    }
  }
  return array
}

function handDelete(index: number) {
  state.multipleSelection.splice(index, 1)
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择数据')
  }
  else {
    for (let i = 0; i < state.multipleSelection.length; i++) {
      if (state.multipleSelection[i].use_roll === '')
        return ElMessage.error('请输入匹数')

      if (state.multipleSelection[i].use_weight === '')
        return ElMessage.error('请输入数量')
    }
    const arrRoll = sumTotal(state.multipleSelection, 'use_roll')

    const arrWeight = sumTotal(state.multipleSelection, 'use_weight')

    if (arrRoll !== Number(state.info.roll) || arrWeight !== Number(state.info.weight)) {
      const res = await deleteToast('【计划匹数】或【计划数量】与实际不相等，确认提交？')
      if (res) {
        state.showModal = false
        state.multipleSelection.push(returnPlan.value)
        emits('handleSure', state.multipleSelection, state.rowIndex)
      }
      return
    }

    state.showModal = false
    state.multipleSelection.push(returnPlan.value)
    emits('handleSure', state.multipleSelection, state.rowIndex)
  }
}

// 重置
function handReset() {
  filterData.value = resetData(filterData.value)
  state.info.product_code = ''
  state.info.color_name = ''
  filterData.value.radioValue = '2'
}

defineExpose({
  state,
  filterData,
  handleSelectionChange,
  handAllSelect,
})

const columnList_new = ref([
  {
    field: 'product_code',
    minWidth: 130,
    title: '成品编号',
    fixed: 'left',
  },
  {
    field: 'product_name',
    minWidth: 130,
    title: '成品名称',
    fixed: 'left',
  },
  {
    field: 'customer_name',
    minWidth: 130,
    title: '所属客户',
  },
  {
    field: 'product_color_code',
    minWidth: 130,
    title: '色号',
  },
  {
    field: 'product_color_name',
    minWidth: 130,
    title: '颜色',
  },
  {
    field: 'warehouse_name',
    minWidth: 100,
    title: '所属仓库',
  },
  {
    field: 'dyelot_number',
    minWidth: 100,
    title: '染厂缸号',
  },
  {
    field: 'product_craft',
    minWidth: 90,
    title: '成品工艺',
    soltName: 'product_craft',
  },
  {
    field: 'product_level_name',
    minWidth: 100,
    title: '成品等级',
  },
  {
    field: 'product_ingredient',
    minWidth: 150,
    title: '成品成分',
    soltName: 'product_ingredient',
  },
  {
    field: 'product_remark',
    minWidth: 130,
    title: '成品备注',
  },
  // {
  //   field: 'unit_price',
  //   minWidth: 100,
  //   title: '数量单价',
  //   isUnitPrice: true,
  // },
  // {
  //   field: 'length_unit_price',
  //   minWidth: 100,
  //   title: '辅助数量单价',
  //   isUnitPrice: true,
  // },
  {
    field: 'stock_roll',
    minWidth: 100,
    title: '库存匹数',
    fixed: 'right',
    soltName: 'stock_roll',
  },
  {
    field: 'weight',
    minWidth: 100,
    title: '数量总计',
    fixed: 'right',
    isWeight: true,
  },
  {
    field: 'measurement_unit_name',
    minWidth: 60,
    title: '单位',
    fixed: 'right',
  },
  {
    field: 'length',
    minWidth: 100,
    title: '辅助数量总计',
    fixed: 'right',
    isLength: true,
  },
  {
    field: 'available_roll',
    soltName: 'available_roll',
    minWidth: 100,
    title: '可用匹数',
    fixed: 'right',
  },
  {
    field: 'available_weight',
    isWeight: true,
    minWidth: 100,
    title: '可用数量',
    fixed: 'right',
  },
  {
    field: 'use_roll',
    soltName: 'use_roll',
    minWidth: 100,
    title: '匹数',
    fixed: 'right',
    required: true,
  },
  {
    field: 'use_weight',
    soltName: 'use_weight',
    minWidth: 100,
    title: '数量',
    fixed: 'right',
    required: true,
  },
])

const columnList = ref<TableColumn[]>([
  {
    field: 'product_code',
    minWidth: 100,
    title: '成品编号',
    fixed: 'left',
  },
  {
    field: 'product_name',
    minWidth: 100,
    title: '成品名称',
    fixed: 'left',
  },
  {
    field: 'customer_name',
    minWidth: 100,
    title: '所属客户',
  },
  {
    field: 'product_color_code',
    minWidth: 70,
    title: '色号',
  },
  {
    field: 'product_color_name',
    minWidth: 70,
    title: '颜色',
  },
  {
    field: 'warehouse_name',
    minWidth: 100,
    title: '所属仓库',
  },
  {
    field: 'dyelot_number',
    minWidth: 100,
    title: '染厂缸号',
  },
  {
    field: 'product_craft',
    minWidth: 90,
    title: '成品工艺',
    soltName: 'product_craft',
  },
  {
    field: 'product_level_name',
    minWidth: 100,
    title: '成品等级',
  },
  {
    field: 'product_ingredient',
    minWidth: 150,
    title: '成品成分',
    soltName: 'product_ingredient',
  },
  {
    field: 'product_remark',
    minWidth: 100,
    title: '成品备注',
  },
  {
    field: 'remark',
    minWidth: 130,
    title: '库存备注',
  },
  // {
  //   field: 'unit_price',
  //   minWidth: 100,
  //   title: '数量单价',
  //   isUnitPrice: true,
  // },
  // {
  //   field: 'length_unit_price',
  //   minWidth: 100,
  //   title: '辅助数量单价',
  //   isUnitPrice: true,
  // },
  {
    field: 'stock_roll',
    minWidth: 100,
    title: '库存匹数',
    fixed: 'right',
    soltName: 'stock_roll',
  },
  {
    field: 'weight',
    minWidth: 100,
    title: '数量总计',
    fixed: 'right',
    isWeight: true,
  },
  {
    field: 'measurement_unit_name',
    minWidth: 70,
    title: '单位',
    fixed: 'right',
  },
  {
    field: 'length',
    minWidth: 90,
    title: '辅助数量总计',
    fixed: 'right',
    isLength: true,
  },
  {
    field: 'available_roll',
    soltName: 'available_roll',
    minWidth: 100,
    title: '可用匹数',
    fixed: 'right',
  },
  {
    field: 'available_weight',
    soltName: 'available_weight',
    minWidth: 100,
    title: '可用数量',
    fixed: 'right',
  },
])

const box = ref<any>(null)
function scrollLeft() {
  // 向左滚动的函数
  if (box.value !== null)
    box.value.scrollLeft = 0 // 设置滚动条的位置为 0
}
function scrollRight() {
  // 向右滚动的函数
  box.value.scrollLeft = box.value.scrollWidth - box.value.clientWidth // 设置滚动条的位置为盒子的最大宽度减去可视区域的宽度
}

function changePro(data: any) {
  state.info.product_name = data?.finish_product_name
  state.info.product_code = data?.finish_product_code
}

function changeColor(data: any) {
  state.info.color_name = data?.product_color_name
  state.info.color_code = data?.product_color_code
}
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    show-footer
    destroy-on-close
    :title="state.modalName"
    width="80vw"
    height="85vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    show-zoom
  >
    <div class="flex flex-col overflow-hidden h-full">
      <!--      <div class="flex_end mb-[20px]"> -->
      <!--      </div> -->
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="filterData.product_id"
              :label-name="state.info.product_code"
              field="finish_product_code"
              :query="{ finish_product_code: componentRemoteSearch.product_code }"
              @on-input="val => (componentRemoteSearch.product_code = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              v-model="filterData.product_id" -->
            <!--              :label-name="state.info.product_code" -->
            <!--              :query="{ finish_product_code: componentRemoteSearch.product_code }" -->
            <!--              label-field="finish_product_code" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '名称', -->
            <!--                  minWidth: 100, -->
            <!--                  isEdit: true, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      isEdit: true, -->
            <!--                      title: '名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '编号', -->
            <!--                  minWidth: 100, -->
            <!--                  isEdit: true, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      isEdit: true, -->
            <!--                      title: '编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->

            <!--              @on-input="val => (componentRemoteSearch.product_code = val)" -->
            <!--              @change-value="changePro" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="filterData.product_id"
              :label-name="state.info.product_name"
              field="finish_product_name"
              :query="{ finish_product_name: componentRemoteSearch.product_name }"
              @on-input="val => (componentRemoteSearch.product_name = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              v-model="filterData.product_id" -->
            <!--              :label-name="state.info.product_name" -->
            <!--              :query="{ finish_product_name: componentRemoteSearch.product_name }" -->
            <!--              label-field="finish_product_name" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '名称', -->
            <!--                  minWidth: 100, -->
            <!--                  isEdit: true, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      isEdit: true, -->
            <!--                      title: '名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '编号', -->
            <!--                  minWidth: 100, -->
            <!--                  isEdit: true, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      isEdit: true, -->
            <!--                      title: '编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="val => (componentRemoteSearch.product_name = val)" -->
            <!--              @change-value="changePro" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <SelectDialog
              v-model="filterData.customer_id"
              :query="{ name: componentRemoteSearch.customer_name }"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.customer_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色:">
          <template #content>
            <SelectDialog
              v-model="filterData.product_color_id"
              :label-name="state.info.color_name"
              :query="{ finish_product_id: filterData.product_id, product_color_name: componentRemoteSearch.color_name }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '颜色',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_name"
              value-field="id"
              @on-input="val => (componentRemoteSearch.color_name = val)"
              @change-value="changeColor"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号:">
          <template #content>
            <SelectDialog
              v-model="filterData.product_color_id"
              :label-name="state.info.color_code"
              :query="{ finish_product_id: filterData.product_id, product_color_code: componentRemoteSearch.color_code }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '色号',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              value-field="id"
              @on-input="val => (componentRemoteSearch.color_code = val)"
              @change-value="changeColor"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品等级:">
          <template #content>
            <SelectComponents v-model="filterData.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" />
          </template>
        </DescriptionsFormItem>
        <!-- <DescriptionsFormItem label="染厂缸号:">
          <template v-slot:content>
            <vxe-input v-model="filterData.dyelot_number"></vxe-input>
          </template>
        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="仅显示有可用库存:">
          <template #content>
            <el-checkbox v-model="filterData.available_only" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="库存选择方式:" copies="2">
          <template #content>
            <el-radio-group v-model="filterData.radioValue" class="ml-4" @change="(val:any) => handChange(val)">
              <el-radio label="1" size="large">
                按缸号库存添加
              </el-radio>
              <el-radio label="2" size="large">
                按汇总库存添加
              </el-radio>
            </el-radio-group>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex flex-1 flex-col overflow-hidden">
        <div ref="box" class="overflow-scroll flex flex-row scroll-smooth flex-1">
          <div class="w-[85%] flex flex-col flex-1">
            <FildCard class="m-1 mr-0 flex-1 flex flex-col" title="选择库存" :tool-bar="true">
              <template #right-top>
                <el-button type="primary" @click="scrollRight">
                  查看已选库存
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </template>
              <div class="flex-1">
                <Table :config="tableConfig" :table-list="state.list" :column-list="columnList">
                  <template #product_craft="{ row }">
                    {{ filterData.radioValue === '1' ? row.finish_product_craft : row.product_craft }}
                  </template>
                  <template #product_ingredient="{ row }">
                    {{ filterData.radioValue === '1' ? row.finish_product_ingredient : row.product_ingredient }}
                  </template>
                  <template #stock_roll="{ row }">
                    {{ formatPriceDiv(row?.roll || row?.stock_roll) }}
                  </template>
                  <template #available_roll="{ row }">
                    <span v-if="row.available_roll === 0" style="color: red">{{ formatPriceDiv(row?.available_roll) }}</span>
                    <span v-else>{{ formatPriceDiv(row?.available_roll) }}</span>
                  </template>
                  <template #available_weight="{ row }">
                    <span v-if="row.available_weight === 0" style="color: red">{{ formatWeightDiv(row?.available_weight) }}</span>
                    <span v-else>{{ formatWeightDiv(row?.available_weight) }}</span>
                  </template>
                </Table>
              </div>
            </FildCard>
          </div>
          <div class="min-w-[100%] flex flex-col flex-1">
            <FildCard class="m-1 h-full flex flex-col" title="已选" :tool-bar="true">
              <template #right-top>
                <div class="flex justify-between gap-4 mr-4">
                  <p>计划匹数:{{ state.info.roll }}</p>
                  <p>计划数量:{{ state.info.weight }}</p>
                </div>
                <el-button type="warning" :icon="ArrowLeft" @click="scrollLeft">
                  继续选择库存
                </el-button>
              </template>
              <div class="flex-1">
                <Table ref="tableRef" :config="tableConfig_second" :table-list="state?.multipleSelection" :column-list="columnList_new">
                  <template #product_craft="{ row }">
                    {{ filterData.radioValue === '1' ? row.finish_product_craft : row.product_craft }}
                  </template>
                  <template #product_ingredient="{ row }">
                    {{ filterData.radioValue === '1' ? row.finish_product_ingredient : row.product_ingredient }}
                  </template>
                  <template #stock_roll="{ row }">
                    {{ formatPriceDiv(row?.roll || row?.stock_roll) }}
                  </template>
                  <template #available_roll="{ row }">
                    <span v-if="row.available_roll === 0" style="color: red">{{ formatPriceDiv(row?.available_roll) }}</span>
                    <span v-else>{{ formatPriceDiv(row?.available_roll) }}</span>
                  </template>
                  <template #use_roll="{ row }">
                    <vxe-input v-model="row.use_roll" type="float" :min="0" :max="formatPriceDiv(row?.roll || row?.stock_roll)" />
                  </template>
                  <template #use_weight="{ row }">
                    <vxe-input v-model="row.use_weight" type="float" :min="0" :max="formatWeightDiv(row.weight)" />
                  </template>
                  <template #operate="{ rowIndex }">
                    <el-button text type="danger" @click="handDelete(rowIndex)">
                      删除
                    </el-button>
                  </template>
                </Table>
              </div>
            </FildCard>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex_end {
  display: flex;
  justify-content: flex-end;
}
</style>
