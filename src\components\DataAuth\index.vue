<script lang="ts" setup>
import { useToggle } from '@vueuse/core'
import { watch } from 'vue'
import { AuthList } from '@/common/authConvert'

const [allSelectState, toggleAllSelectState] = useToggle(false)
const [allSelectIndeterminateState, toggleAllSelectIndeterminateState] = useToggle(false)
const selectedList = defineModel([])

function SelectAllEvent(val: boolean) {
  selectedList.value = val ? AuthList.map(item => item.key) : []
  toggleAllSelectIndeterminateState(false)
}

function checkSelectedList(selectedArray: string[]) {
  selectedList.value = selectedArray.filter(key => AuthList.some(item => item.key === key))
}

watch(selectedList, () => {
  if (!selectedList.value)
    return
  authChangeEvent(selectedList.value)
})

function authChangeEvent(value: string[]) {
  if (selectedList.value === undefined)
    throw new Error('有病啊，不传选中的值绑条毛')

  const selectedCount = value.length

  if (selectedCount > AuthList.length)
    checkSelectedList(value)

  toggleAllSelectState(selectedCount === AuthList.length)
  toggleAllSelectIndeterminateState(selectedCount > 0 && selectedCount < AuthList.length)
}
</script>

<template>
  <div>
    <el-checkbox
      v-model="allSelectState"
      class="h-12"
      :indeterminate="allSelectIndeterminateState"
      @change="SelectAllEvent"
    >
      全选
    </el-checkbox>
    <el-checkbox-group
      v-model="selectedList"
      class="flex flex-col"
      @change="authChangeEvent"
    >
      <el-checkbox v-for=" auth in AuthList" :key="auth.key" :label="auth.authName" :value="auth.key" class="h-[26px]">
        {{ auth.authName }}
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
