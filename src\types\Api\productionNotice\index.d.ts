declare namespace Api.ProductionNoticeOrderDownList {
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * 生产完成状态(1完成 2未完成)
     */
    finish_status?: number
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布编号或者名称
     */
    grey_fabric_code_or_name?: string
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 是否只获取已审核单据
     */
    is_audit?: boolean
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * 生产通知单号
     */
    order_no?: string
    /**
     * page
     */
    page?: number
    /**
     * 生产计划单号
     */
    production_plan_order_no?: string
    /**
     * 排产完成状态(1完成 2未完成)
     */
    schedule_finish_status?: number
    /**
     * size
     */
    size?: number
    /**
     * 单据状态
     */
    status?: number
    /**
     * 织厂ID
     */
    weaving_mill_id?: number
    [property: string]: any
  }
  /**
   * produce.GetProductionNotifyOrderDropdownData
   */
  export interface Response {
  /**
   * 变更匹数
   */
    change_roll?: number
    /**
     * 变更数量
     */
    change_weight?: number
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 客户ID
     */
    customer_id?: number
    /**
     * 客户名称
     */
    customer_name?: string
    /**
     * 客户跟单用户ID
     */
    customer_order_follower_id?: number
    /**
     * 客户跟单用户名称
     */
    customer_order_follower_name?: string
    /**
     * 成品克重(2)
     */
    finish_product_gram_weight?: string
    /**
     * 成品克重及单位名称
     */
    finish_product_gram_weight_and_unit_name?: string
    /**
     * 成品克重单位id(字典)
     */
    finish_product_gram_weight_unit_id?: number
    /**
     * 成品克重单位名称
     */
    finish_product_gram_weight_unit_name?: string
    /**
     * 成品幅宽(2)
     */
    finish_product_width?: string
    /**
     * 成品幅宽及单位名称
     */
    finish_product_width_and_unit_name?: string
    /**
     * 成品幅宽单位id(字典)
     */
    finish_product_width_unit_id?: number
    /**
     * 成品幅宽单位名称
     */
    finish_product_width_unit_name?: string
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色名
     */
    grey_fabric_color_name?: string
    /**
     * 坯布克重
     */
    grey_fabric_gram_weight?: string
    /**
     * 坯布克重及单位名称
     */
    grey_fabric_gram_weight_and_unit_name?: string
    /**
     * 坯布克重单位id(字典)
     */
    grey_fabric_gram_weight_unit_id?: number
    /**
     * 坯布克重单位名称
     */
    grey_fabric_gram_weight_unit_name?: string
    /**
     * 坯布信息ID
     */
    grey_fabric_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 坯布信息备注
     */
    grey_fabric_remark?: string
    /**
     * 坯布幅宽
     */
    grey_fabric_width?: string
    /**
     * 坯布幅宽及单位名称
     */
    grey_fabric_width_and_unit_name?: string
    /**
     * 坯布幅宽单位id(字典)
     */
    grey_fabric_width_unit_id?: number
    /**
     * 坯布幅宽单位名称
     */
    grey_fabric_width_unit_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 通知日期
     */
    notify_date?: string
    /**
     * 生产通知单号
     */
    order_no?: string
    /**
     * 跟单QC员
     */
    order_qc_user_id?: number
    /**
     * 跟单QC员名称
     */
    order_qc_user_name?: string
    /**
     * 单据备注
     */
    order_remark?: string
    /**
     * 加工单价
     */
    process_price?: number
    /**
     * 已产条数
     */
    produced_roll?: number
    /**
     * 已产数量
     */
    produced_weight?: number
    /**
     * 未产匹数(原排产匹数+变更匹数-已产匹数)
     */
    producing_roll?: number
    /**
     * 未产数量(原排产数数量+变更数量-已产数量)
     */
    producing_weight?: number
    /**
     * 成品克重(1),优先使用
     */
    product_gram_weight?: string
    /**
     * 成品幅宽(1),优先使用
     */
    product_width?: string
    /**
     * 原料信息
     */
    production_notify_material_ratio?: ProduceGetProductionNotifyMaterialRatioData[]
    /**
     * 生产计划单ID
     */
    production_plan_order_id?: number
    /**
     * 生产计划单单号
     */
    production_plan_order_no?: string
    /**
     * 成品销售计划单子项信息id
     */
    sale_plan_order_item_id?: number
    /**
     * 成品销售计划单子项单号
     */
    sale_plan_order_item_no?: string
    /**
     * 营销体系ID
     */
    sale_system_id?: number
    /**
     * 营销体系名称
     */
    sale_system_name?: string
    /**
     * 销售员
     */
    sale_user_id?: number
    /**
     * 销售员名称
     */
    sale_user_name?: string
    /**
     * 已排产匹数
     */
    scheduled_roll?: number
    /**
     * 排产条数
     */
    scheduling_roll?: number
    /**
     * 排产数量
     */
    scheduling_weight?: number
    /**
     * 未排产匹数(排产匹数-已排产匹数)
     */
    un_schedule_roll?: number
    /**
     * 单位id
     */
    unit_id?: number
    /**
     * 单位名称
     */
    unit_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 织厂ID
     */
    weaving_mill_id?: number
    /**
     * 织厂名称
     */
    weaving_mill_name?: string
    /**
     * 织厂跟单用户ID
     */
    weaving_mill_order_follower_id?: number
    /**
     * 织厂跟单用户名称
     */
    weaving_mill_order_follower_name?: string
    /**
     * 织厂跟单电话
     */
    weaving_mill_order_follower_phone?: string
    /**
     * 布匹定重
     */
    weight_of_fabric?: number
    [property: string]: any
  }

  /**
   * produce.GetProductionNotifyMaterialRatioData
   */
  export interface ProduceGetProductionNotifyMaterialRatioData {
  /**
   * 变更发纱量
   */
    change_send_yarn_quantity?: number
    /**
     * 变更用纱量
     */
    change_use_yarn_quantity?: number
    /**
     * 原料颜色
     */
    color_scheme?: string
    /**
     * 最终发纱量
     */
    final_send_yarn_quantity?: number
    /**
     * 最终用纱量
     */
    final_use_yarn_quantity?: number
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色名称
     */
    grey_fabric_color_name?: string
    id?: number
    /**
     * 织厂出料
     */
    mill_private_yarn?: boolean
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 原料批号
     */
    raw_material_batch_number?: string
    /**
     * 原料品牌
     */
    raw_material_brand?: string
    /**
     * 原料编号
     */
    raw_material_code?: string
    /**
     * 原料ID
     */
    raw_material_id?: number
    /**
     * 原料名称
     */
    raw_material_name?: string
    /**
     * 原料库存ID
     */
    raw_stock_id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 发纱量
     */
    send_yarn_quantity?: number
    /**
     * 供方
     */
    supplier_id?: number
    /**
     * 供方名称
     */
    supplier_name?: string
    /**
     * 单位id
     */
    unit_id?: number
    /**
     * 单位名称
     */
    unit_name?: string
    /**
     * 用纱量
     */
    use_yarn_quantity?: number
    /**
     * 织造类别
     */
    weaving_category?: string
    /**
     * 用纱损耗
     */
    yarn_loss?: number
    /**
     * 用纱比例
     */
    yarn_ratio?: number
    [property: string]: any
  }
}
