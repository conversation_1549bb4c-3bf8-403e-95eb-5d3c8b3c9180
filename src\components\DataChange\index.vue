<template>
  <span v-if="props.val > 0" class="up_red">+{{ props.val }}</span>
  <span v-else-if="props.val == 0">{{ props.val }}</span>
  <span v-else class="lower_green">{{ props.val }}</span>
</template>
<script setup lang="ts">
interface Props {
  val: number
}
const props = withDefaults(defineProps<Props>(), {
  val: 0,
})
</script>
<style lang="scss" scoped>
.up_red {
  color: red;
}
.lower_green {
  color: green;
}
</style>
