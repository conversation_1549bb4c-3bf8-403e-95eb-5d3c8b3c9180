import '@/assets/iconfont/index.js'
import * as icons from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/es/locale/lang/zh-cn'
import { createApp } from 'vue'
import 'xe-utils'
import VxeTable from 'vxe-table'
import 'vxe-table/lib/style.css'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import './index.scss'
import './style.css'
import '@fortawesome/fontawesome-free/css/all.css'
import '@fortawesome/fontawesome-free/js/all.js'
import 'virtual:svg-icons-register'
import { hiPrintPlugin } from 'vue-plugin-hiprint'
import installPermissionPlus from './plugins/hasPermission'
import App from './App.vue'
import { INPUT_PLACEHOLDER, INPUT_SHOW_WHEEL } from './common/config/index'
import router from '@/router'
import pinia from '@/stores/index'
import btnAntiShake from '@/common/btnAntiShake'
import SvgIcon from '@/components/Svglcon/index.vue'
import './components/Table/customRender'
import '@/common/config/globalElInputConfig'
// 添加rem适配
function setupRem() {
  const docEl = document.documentElement
  const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize'
  const recalc = () => {
    const clientWidth = docEl.clientWidth
    if (!clientWidth)
      return
    // 基于1920设计稿，但设置最小字体大小
    // 当屏幕宽度小于1366px时，保持最小字体大小为12px
    const minFontSize = 12
    const calculatedSize = 16 * (clientWidth / 1920)
    const fontSize = Math.max(calculatedSize, minFontSize)
    docEl.style.fontSize = `${fontSize}px` // 基于1920设计稿
  }
  window.addEventListener(resizeEvt, recalc, false)
  document.addEventListener('DOMContentLoaded', recalc, false)
}
setupRem()

const app = createApp(App)
Object.keys(icons)?.forEach((key) => {
  Reflect.has(icons, key)
  && app.component(key, icons[key as keyof typeof icons])
})
VxeUI.setConfig({
  input: {
    size: 'mini',
    controls: INPUT_SHOW_WHEEL,
    placeholder: INPUT_PLACEHOLDER,
  },
})
app.use(pinia)
app.use(router)
app.use(btnAntiShake)
app.use(ElementPlus, { locale })
app.use(VxeUI)
app.use(VxeTable)
app.use(hiPrintPlugin, '$pluginName')
installPermissionPlus(app)

app.component('svg-icon', SvgIcon)

app.mount('#app')

// 屏蔽警告信息
app.config.warnHandler = () => null
hiPrintPlugin.disAutoConnect()
