import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

/**
 * 获取微信支付订单列表
 * @constructor
 */
export const GetPayRecordDataList = () => {
  return useRequest({
    url: '/admin/v1/payRecord/getPayRecordDataList',
    method: 'get',
    pagination: true,
  })
}

/**
 * 获取账套管理列表
 * @constructor
 */
export const GetTenantManagementList = () => {
  return useRequest({
    url: '/admin/v1/tenantManagement/getTenantManagementList',
    method: 'get',
    pagination: true,
  })
}

/**
 * 获取支付方式枚举
 * @constructor
 */
export const GetPayWayEnum = () => {
  return useRequest({
    url: '/admin/v1/payRecord/enum/getPayWayEnum',
    method: 'get',
  })
}

/**
 * 获取支付状态枚举
 * @constructor
 */
export const GetPayStatusEnum = () => {
  return useRequest({
    url: '/admin/v1/payRecord/enum/getPayStatusEnum',
    method: 'get',
  })
}

export const GetTenantManagementDetailInfo = () => {
  return useRequest({
    url: '/admin/v1/tenantManagement/getTenantManagementDetailInfo',
    method: 'get',
  })
}

/**
 * 作废购买记录
 */
export const updatePayRecordCancel = () => {
  return useRequest({
    url: '/admin/v1/payRecord/cancelPayRecord',
    method: 'put',
  })
}

/**
 * 创建支付记录
 */
export const ManualAddPayRecord = () => {
  return useRequest({
    url: '/admin/v1/payRecord/manualAddPayRecord',
    method: 'post',
  })
}

/**
 * 调货版线索
 */
export const GetWechatUserPuleList = () => {
  return useRequest({
    url: '/admin/v1/tenantManagement/getWechatUserPuleList',
    method: 'get',
  })
}

/**
 * 调货版线索
 */
export const ExportGetWechatUserPuleListExcel = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/tenantManagement/getWechatUserPuleList',
    method: 'get',
    nameFile,
  })
}
