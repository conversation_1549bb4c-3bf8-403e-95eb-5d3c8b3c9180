# 供应商管理页面接口对接说明

## 概述
本文档说明了供应商管理页面（add.vue 和 edit.vue）的接口对接情况，包括字段映射、数据转换和API调用。

## 接口类型定义
接口类型定义位于：`src/types/Api/supplier/index.d.ts`

主要接口：
- `Api.Supplier.Request` - 供应商请求数据结构
- `Api.Supplier.SystemQYWXCustomer` - 企微客户数据结构  
- `Api.Supplier.SystemQYWXGroupChat` - 企微群聊数据结构

## 数据转换函数

### 1. transformFormToApiRequest
将表单数据转换为API请求格式

**功能：**
- 将前端表单字段映射到API字段
- 处理数据类型转换（字符串转数字等）
- 处理空值和默认值
- 映射织造配置字段到正确的API字段名

**关键字段映射：**
```typescript
// 基础信息
name: formData.supplerName,
full_name: formData.supplerAllName,
code: formData.supplerNums,
unit_type_id: formData.supplerType,

// 织造配置字段映射
bf_prefix: formData.fabric_prefix,                    // 布飞前缀
wfi_process_name: formData.weighing_inspection_process, // 称重验布流程
weaving_storage_method_name: formData.weaving_storage_method, // 织造入库方式
bf_sequence_number_rule_name: formData.fabric_roll_number_rule, // 布飞卷号规则
```

### 2. transformApiResponseToForm
将API响应数据转换为表单格式

**功能：**
- 将API字段映射回前端表单字段
- 处理空值和默认值
- 反向映射织造配置字段

**关键字段映射：**
```typescript
// 基础信息
supplerName: apiData?.name,
supplerAllName: apiData?.full_name,
supplerNums: apiData?.code,
supplerType: apiData?.unit_type_id,

// 织造配置字段反向映射
fabric_prefix: apiData?.bf_prefix,
weighing_inspection_process: apiData?.wfi_process_name,
weaving_storage_method: apiData?.weaving_storage_method_name,
fabric_roll_number_rule: apiData?.bf_sequence_number_rule_name,
```

## 页面使用方式

### add.vue（新增页面）
```typescript
// 导入转换函数
import { transformFormToApiRequest } from './composables/useSupplierForm'

// 提交数据
async function handSubmit() {
  // 处理信用额度格式化
  const formDataWithFormattedCredit = {
    ...state.form,
    creditLimit: formatPriceMul(state.form.creditLimit || 0),
  }
  
  // 使用转换函数将表单数据转换为API格式
  const query = transformFormToApiRequest(formDataWithFormattedCredit, wechatContacts.value, boundQyGroups.value)
  
  await postFetch(getFilterData(query))
}
```

### edit.vue（编辑页面）
```typescript
// 导入转换函数
import { transformApiResponseToForm, transformFormToApiRequest } from './composables/useSupplierForm'

// 获取数据
async function getData() {
  await fetchData({ id: router.query.id })
  
  // 使用转换函数将API数据转换为表单格式
  const transformedData = transformApiResponseToForm(data.value)
  
  // 特殊处理信用额度格式化
  transformedData.creditLimit = formatPriceDiv(data.value?.credit_limit || 0)
  
  // 更新表单数据
  Object.assign(state.form, transformedData)
}

// 提交数据
async function handSubmit() {
  // 处理信用额度格式化
  const formDataWithFormattedCredit = {
    ...state.form,
    creditLimit: formatPriceMul(state.form.creditLimit || 0),
  }
  
  // 使用转换函数将表单数据转换为API格式
  const query = transformFormToApiRequest(formDataWithFormattedCredit, wechatContacts.value, boundQyGroups.value)
  
  // 添加编辑时需要的ID
  query.id = Number(router?.query.id)
  
  await postFetch(getFilterData(query))
}
```

## 特殊处理

### 1. 信用额度格式化
- 提交时：使用 `formatPriceMul` 将显示金额转换为分
- 获取时：使用 `formatPriceDiv` 将分转换为显示金额

### 2. 企微数据处理
- 企微客户和群聊数据单独处理，不在转换函数中处理
- 保持原有的数据结构和映射方式

### 3. 数据类型转换
- 数字字段自动转换：`Number(value) || undefined`
- 空值处理：使用 `undefined` 而不是 `null`（符合TypeScript类型定义）

## 优势

1. **类型安全**：使用TypeScript接口定义，确保数据结构正确
2. **代码复用**：转换逻辑集中管理，避免重复代码
3. **维护性**：字段映射集中在转换函数中，便于维护
4. **一致性**：确保add和edit页面使用相同的数据转换逻辑
5. **扩展性**：新增字段只需在转换函数中添加映射关系

## 注意事项

1. 新增字段时，需要同时更新两个转换函数
2. 织造配置字段使用API定义的正确字段名（带前缀和后缀）
3. 数字类型字段需要进行类型转换
4. 空值处理使用 `undefined` 而不是 `null`
