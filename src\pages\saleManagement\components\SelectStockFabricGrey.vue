<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import { ArrowLeft, ArrowRight, Delete } from '@element-plus/icons-vue'
import { formatPriceDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import { debounce, deepClone, deleteToast, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import FildCard from '@/components/FildCard.vue'
import type { TableColumn } from '@/components/Table/type'
import { getGfmWarehouseSumList } from '@/api/greyFabricPurchaseReturn'

export interface Props {
  obj: any
}

const emits = defineEmits(['handleSure'])

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
})

const filterData = ref({
  warehouse_id: '',
  grey_fabric_code: '',
  grey_fabric_id: '',
  grey_fabric_name: '',
  supplier_id: '',
  custom_id: '',
})

const state = reactive<any>({
  showModal: false,
  modalName: '根据库存添加',
  multipleSelection: [],
  list: [],
  sale_customer_id: '',
  warehouse_name: '',
  cashList: [],
  info: {},
  rowIndex: -1,
})

const { fetchData, data, total, loading, page, size, success, msg, handleSizeChange, handleCurrentChange } = getGfmWarehouseSumList()

const tableConfig = reactive<any>({
  fieldApiKey: 'AddBasedOnGreyInventory',
  showField: true,
  showPagition: true,
  showSlotNums: true,
  loading,
  page,
  size,
  total,
  showCheckBox: true,
  showSort: false,
  height: 'auto',
  filterStatus: false,
  checkboxConfig: {
    checkField: 'selected',
    checkMethod: ({ row }: any) => {
      return row.stock_roll || row.stock_weight
    },
  },
  handleSizeChange,
  handleCurrentChange,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod(val),
})

const tableConfig_second = ref({
  fieldApiKey: 'AddBasedOnGreyInventorySecond',
  showCheckBox: true,
  showSlotNums: true,
  height: 'auto',
  showSort: false,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethodSc(val),
  handAllSelect: () => handAllSelect_twince(),
  handleSelectionChange: (val: any) => handleSelectionChange_twince(val),
  showOperate: true,
  operateWidth: '100',
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['stock_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'stock_roll'))}`

      if (['stock_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'stock_weight'))}`

      return null
    }),
  ]
}

const returnPlan = ref({
  roll: 0,
  weight: 0,
})
function FooterMethodSc({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_roll'].includes(column.property)) {
        returnPlan.value.roll = Number(sumNum(data, 'use_roll'))
        return `${sumNum(data, 'use_roll')}`
      }
      if (['use_weight'].includes(column.property)) {
        returnPlan.value.weight = Number(sumNum(data, 'use_weight'))
        return `${sumNum(data, 'use_weight')}`
      }
      return null
    }),
  ]
}
const getData = debounce(async () => {
  const query = {
    sale_customer_id: state?.sale_customer_id,
    ...filterData.value,
  }
  await fetchData(getFilterData(query))
  if (!success.value)
    return ElMessage.error(msg.value)
}, 400)
// 处理list数据
watch([
  () => data.value.list,
], () => {
  formatList()
}, {
  deep: true,
})
async function formatList() {
  const dataList = deepClone(data?.value?.list || [])
  state.list = dataList?.map((item: any) => {
    item.use_weight = 0

    item.choose_sign = 2
    item.selected = state.multipleSelection?.some((citem: any) => citem.id === item.id && citem.choose_sign === item.choose_sign && citem.stock_weight !== 0) ?? false
    return item
  })

  // TODO:缓存记住当前页的数据内容，以便取消的时候可以剔除
  state.cashList = dataList
}

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
    else
      state.multipleSelection = []
  },
)

watch(
  () => filterData.value,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableRef = ref()
watch(
  () => state.multipleSelection,
  () => {
    // state.multipleSelection = state.multipleSelection.map((item: any) => {
    state.multipleSelection.map((item: any) => {
      item.selected = true
      return item
    })

    nextTick(() => {
      tableRef.value?.tableRef?.updateFooter()
    })
  },
  {
    deep: true,
  },
)

// 全选
function handAllSelect({ records, checked }: any) {
  if (checked) {
    const resDult = checkSameIdAndChooseSign(state.multipleSelection, records)
    if (resDult)
      state.multipleSelection = margeArr(state.multipleSelection, records)
    else
      state.multipleSelection = [...records, ...state.multipleSelection]

    state.multipleSelection?.map((item: any) => {
      const arrRoll = sumTotal(state.multipleSelection, 'use_roll')
      const arrWeight = sumTotal(state.multipleSelection, 'use_weight')

      // stock_roll 可用匹数，state.info.roll 计划匹数
      item.use_roll = formatPriceDiv(item.stock_roll) >= Number(state.info.roll) - arrRoll ? Number(state.info.roll) - arrRoll : formatPriceDiv(item.stock_roll)
      item.use_weight = formatWeightDiv(item.stock_weight) >= Number(state.info.weight) - arrWeight ? Number(state.info.weight) - arrWeight : formatWeightDiv(item.stock_weight)
      return item
    })
  }
  else {
    state.multipleSelection = state.multipleSelection.filter((item2: any) => !state.cashList.some((item1: any) => item1.id === item2.id && item1.choose_sign === item2.choose_sign))
  }
}

function handleSelectionChange({ checked, row }: any) {
  if (checked) {
    state.multipleSelection.push(row)
    state.multipleSelection?.map((item: any) => {
      if (!item.use_roll && !item.use_weight) {
        const arrRoll = sumTotal(state.multipleSelection, 'use_roll')
        const arrWeight = sumTotal(state.multipleSelection, 'use_weight')

        // available_roll 可用匹数
        item.use_roll = formatPriceDiv(item.stock_roll) >= Number(state.info.roll) - arrRoll ? Number(state.info.roll) - arrRoll : formatPriceDiv(item.stock_roll)
        item.use_weight = formatWeightDiv(item.stock_weight) >= Number(state.info.weight) - arrWeight ? Number(state.info.weight) - arrWeight : formatWeightDiv(item.stock_weight)
      }

      return item
    })
  }
  else {
    state.multipleSelection = removeObjectFromArray(row.id, row.choose_sign, state.multipleSelection)
  }
}

function handAllSelect_twince() {
  state.multipleSelection = []
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

function handleSelectionChange_twince({ rowIndex }: any) {
  state.multipleSelection.splice(rowIndex, 1)
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

// 合并清洗数据
function margeArr(array1: any, array2: any) {
  const mergedArray = array1.concat(array2)
  const uniqueArray = []
  const visitedIds = new Set()

  for (const item of mergedArray) {
    const key = `${item.id}-${item.choose_sign}`
    if (!visitedIds.has(key)) {
      visitedIds.add(key)
      uniqueArray.push(item)
    }
  }

  return uniqueArray
}

// 查找两个数组的id和choose_sign的值是否相同
function checkSameIdAndChooseSign(array1: any, array2: any) {
  for (let i = 0; i < array1.length; i++) {
    for (let j = 0; j < array2.length; j++) {
      if (array1[i].id === array2[j].id && array1[i].choose_sign === array2[j].choose_sign)
        return true
    }
  }
  return false
}

function removeObjectFromArray(id: number, choose_sign: number, array: any) {
  for (let i = 0; i < array.length; i++) {
    if (array[i].id === id && array[i].choose_sign === choose_sign) {
      array.splice(i, 1)
      break
    }
  }
  return array
}

function handDelete(index: number) {
  state.multipleSelection.splice(index, 1)
  state.list?.map((item: any) => {
    item.selected = state.multipleSelection?.some((citem: { id: any, choose_sign: number }) => citem.id === item.id && citem.choose_sign === item.choose_sign) ?? false
    return item
  })
}

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择数据')
  }
  else {
    for (let i = 0; i < state.multipleSelection.length; i++) {
      if (state.multipleSelection[i].use_roll === '')
        return ElMessage.error('请输入匹数')

      if (state.multipleSelection[i].use_weight === '')
        return ElMessage.error('请输入数量')
    }
    const arrRoll = sumTotal(state.multipleSelection, 'use_roll')
    const arrWeight = sumTotal(state.multipleSelection, 'use_weight')

    if (arrRoll !== Number(state.info.roll) || arrWeight !== Number(state.info.weight)) {
      const res = await deleteToast('【计划匹数】或【计划数量】与实际不相等，确认提交？')
      if (res) {
        state.showModal = false
        state.multipleSelection.push(returnPlan.value)
        emits('handleSure', state.multipleSelection, state.rowIndex)
      }
      return
    }

    state.showModal = false
    state.multipleSelection.push(returnPlan.value)
    emits('handleSure', state.multipleSelection, state.rowIndex)
  }
}

// 重置
function handReset() {
  filterData.value = resetData(filterData.value)
  state.info.product_code = ''
  state.info.color_name = ''
}

defineExpose({
  state,
  filterData,
  handleSelectionChange,
  handAllSelect,
})

const columnList_new = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方名称',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'source_remark',
    title: '坯布备注',
    minWidth: 100,
  },
  {
    field: 'use_roll',
    soltName: 'use_roll',
    minWidth: 100,
    title: '匹数',
    fixed: 'right',
    required: true,
  },
  {
    field: 'use_weight',
    soltName: 'use_weight',
    minWidth: 100,
    title: '数量',
    fixed: 'right',
    required: true,
  },
])

const columnList = ref<TableColumn[]>([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    width: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    width: 100,
  },
  {
    field: 'supplier_name',
    title: '供方名称',
    width: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    width: 100,
  },
  {
    field: 'machine_number',
    title: '机台号',
    width: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    width: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    width: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    width: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    width: 100,
  },
  {
    field: 'source_remark',
    title: '坯布备注',
    width: 100,
  },
  {
    field: 'stock_roll',
    title: '匹数',
    width: 100,
    isPrice: true,
  },
  {
    field: 'stock_weight',
    title: '数量',
    width: 100,
    isWeight: true,
  },
])

const box = ref<any>(null)
function scrollLeft() {
  // 向左滚动的函数
  if (box.value !== null)
    box.value.scrollLeft = 0 // 设置滚动条的位置为 0
}
function scrollRight() {
  // 向右滚动的函数
  box.value.scrollLeft = box.value.scrollWidth - box.value.clientWidth // 设置滚动条的位置为盒子的最大宽度减去可视区域的宽度
}
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    show-footer
    destroy-on-close
    :title="state.modalName"
    width="78vw"
    height="78vh"
    :mask="false"
    :esc-closable="true"
    resize
    show-zoom
    :z-index="11"
  >
    <div class="flex flex-col overflow-hidden h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="出货单位:">
          <template #content>
            {{ state?.warehouse_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="filterData.grey_fabric_code" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="filterData.grey_fabric_name" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供方名称:">
          <template #content>
            <SelectDialog
              v-model="filterData.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{ name: componentRemoteSearch.name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称')
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @change-input="val => (componentRemoteSearch.name = val)"
            />
            <!-- <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="filterData.supplier_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <SelectDialog
              v-model="filterData.custom_id"
              :query="{ sale_system_id: state.sale_system_id, name: componentRemoteSearch.customer_name }"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.customer_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex flex-1 flex-col overflow-hidden">
        <div ref="box" class="overflow-scroll flex flex-row gap-2 scroll-smooth flex-1">
          <div class="w-[85%] flex flex-col h-full">
            <FildCard class="m-1 mr-0 flex-1 flex flex-col" title="选择库存" :tool-bar="true">
              <template #right-top>
                <el-button type="primary" @click="scrollRight">
                  查看已选库存
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </template>
              <div class="flex-1">
                <Table :config="tableConfig" :table-list="state.list" :column-list="columnList" />
              </div>
            </FildCard>
          </div>
          <div class="min-w-[100%] flex flex-col h-full">
            <FildCard class="m-1 flex flex-col flex-1" title="已选" :tool-bar="true">
              <template #right-top>
                <div class="flex justify-between gap-4 mr-4">
                  <p>计划匹数:{{ state.info.roll }}</p>
                  <p>计划数量:{{ state.info.weight }}</p>
                </div>
                <el-button type="warning" :icon="ArrowLeft" @click="scrollLeft">
                  继续选择库存
                </el-button>
              </template>
              <div class="flex-1">
                <Table ref="tableRef" :config="tableConfig_second" :table-list="state?.multipleSelection" :column-list="columnList_new">
                  <template #use_roll="{ row }">
                    <vxe-input v-model="row.use_roll" type="float" :min="0" :max="formatPriceDiv(row?.stock_roll)" />
                  </template>
                  <template #use_weight="{ row }">
                    <vxe-input v-model="row.use_weight" type="float" :min="0" :max="formatWeightDiv(row.stock_weight)" />
                  </template>
                  <template #operate="{ rowIndex }">
                    <el-button text type="danger" @click="handDelete(rowIndex)">
                      删除
                    </el-button>
                  </template>
                </Table>
              </div>
            </FildCard>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex_end {
  display: flex;
  justify-content: flex-end;
}
</style>
