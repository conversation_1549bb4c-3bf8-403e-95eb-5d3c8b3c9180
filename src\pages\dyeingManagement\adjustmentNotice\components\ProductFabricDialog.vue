<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import Table from '@/components/Table.vue'
import { formatPriceDiv, sumNum } from '@/common/format'

const props = withDefaults(defineProps<{ modelValue: any, isDyeing: boolean }>(), {
  modelValue: [],
  isDyeing: true,
})

const emits = defineEmits(['handleSure', 'update:modelValue'])

const state = reactive<any>({
  tableList: [],
  rowIndex: -1,
  showModal: false,
  isDyeing: '',
})

const tableRef = ref()

const tableConfig = ref({
  showSlotNums: true,
  // showOperate: true,
  // operateWidth: '160',
  footerMethod: (val: any) => FooterMethod(val),
})

watch(
  () => props.modelValue,
  () => {
    if (props.modelValue.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['piece_count'].includes(column.property))
        return `${sumNum(data, 'piece_count')}`

      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')} KG`

      return null
    }),
  ]
}

function handDelete(index: number) {
  const arr = props.modelValue
  arr.splice(index, 1)
  emits('update:modelValue', arr)
}

// const handCancel = () => {
//   state.showModal = false
// }

// const handleSure = async () => {
//   emits('handleSure', state)
// }

const columnList = ref([
  {
    field: 'grey_fabric_name',
    title: '用坯名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方名称',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'source_code',
    title: '出坯单号',
    minWidth: 100,
  },
  {
    field: 'produce_order_no',
    title: '坯布生产收货单号',
    minWidth: 100,
  },
  {
    field: 'source_time',
    title: '收货日期',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'source_remark',
    title: '坯布备注',
    minWidth: 100,
  },
  {
    field: 'dye_factory_remark',
    title: '染厂备注',
    minWidth: 100,
  },
  {
    field: 'not_rtn_piece_count',
    title: '可用匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'not_rtn_weight',
    title: '可用数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'piece_count',
    title: '用坯匹数',
    minWidth: 100,
    soltName: 'roll',
    required: true,
  },
  {
    field: 'weight',
    title: '用坯数量',
    minWidth: 100,
    soltName: 'weight',
    required: true,
  },
  {
    field: '',
    title: '操作',
    minWidth: 100,
    soltName: 'operation',
    // fixed: 'right',
  },
])

const columnList_other = ref([
  {
    field: 'order_no',
    title: '成品加工发料出仓单号',
    minWidth: 100,
  },
  {
    field: 'date',
    title: '出仓日期',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '客户名称',
    minWidth: 100,
  },
  {
    field: 'fabric_code',
    title: '成品编号',
    minWidth: 100,
  },
  {
    field: 'fabric_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'dye_factory_color_code',
    title: '染厂色号',
    minWidth: 100,
  },
  {
    field: 'dyelot',
    title: '缸号',
    minWidth: 100,
  },
  {
    field: 'not_rtn_piece_count',
    title: '可用匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'not_rtn_weight',
    title: '可用数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'piece_count',
    title: '匹数',
    minWidth: 100,
    soltName: 'roll',
    required: true,
  },
  {
    field: 'weight',
    title: '总数量',
    minWidth: 100,
    soltName: 'weight',
    required: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
  {
    field: '',
    title: '操作',
    minWidth: 100,
    soltName: 'operation',
    // fixed: 'right',
  },
])

defineExpose({
  state,
})
</script>

<template>
  <!-- <vxe-modal v-model="state.showModal" :title="'坯布信息'" width="1200" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize> -->
  <Table ref="tableRef" :config="tableConfig" :table-list="props.modelValue" :column-list="props.isDyeing ? columnList : columnList_other">
    <template #roll="{ row }">
      <vxe-input v-model="row.piece_count" :min="0" type="float" :max="formatPriceDiv(row.not_rtn_piece_count)" clearable />
    </template>
    <template #weight="{ row }">
      <vxe-input v-model="row.weight" :min="0" type="float" clearable />
    </template>
    <template #operation="{ rowIndex }">
      <el-button text type="danger" @click="handDelete(rowIndex)">
        删除
      </el-button>
    </template>
  </Table>
  <!-- <div class="buttom-oper" style="margin-top: 20px">
      <el-button @click="handCancel">取消</el-button>
      <el-button type="primary" @click="handleSure">确认</el-button>
    </div> -->
  <!-- </vxe-modal> -->
</template>
