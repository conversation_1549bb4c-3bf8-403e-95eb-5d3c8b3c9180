import { useRequest } from '@/use/useRequest'

/**
 * 获取原料扣款出货单列表
 */
export const GetRawMaterialDmhList = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf/list`,
    method: 'get',
  })
}

/**
 * 获取原料扣款出货单详情
 */
export const GetRawMaterialDmhDetail = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf/detail`,
    method: 'get',
  })
}

// 获取用坯信息
export const GetRawMaterialDmhDetailNotice = () => {
  return useRequest({
    url: 'admin/v1/raw_material/raw_process_order/in/use_yarn_list/by_process_item_id',
    method: 'get',
  })
}

/**
 * 更新原料扣款出货单
 */
export const UpdateRawMaterialDmh = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf`,
    method: 'put',
  })
}

/**
 * 审核原料扣款出货单
 */
export const PassRawMaterialDmh = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf/pass`,
    method: 'put',
  })
}

/**
 * 消审原料扣款出货单
 */
export const CancelRawMaterialDmh = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf/cancel`,
    method: 'put',
  })
}

/**
 * 驳回原料扣款出货单
 */
export const RejectRawMaterialDmh = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf/reject`,
    method: 'put',
  })
}

/**
 * 作废原料扣款出货单
 */
export const DeleteRawMaterialDmh = () => {
  return useRequest({
    url: `/admin/v1/payable/raw_matl_dnf/void`,
    method: 'put',
  })
}
