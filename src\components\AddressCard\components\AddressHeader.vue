<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'

defineProps<{
  showAdd: boolean
}>()

const emit = defineEmits<{
  add: []
}>()
</script>

<template>
  <el-form-item class="form-title-weight" label="收货方式" prop="id">
    <el-link
      v-if="showAdd"
      class="add-link"
      type="primary"
      :underline="false"
      @click="emit('add')"
    >
      添加<el-icon><Plus /></el-icon>
    </el-link>
  </el-form-item>
</template>

<style scoped>
:deep(.form-title-weight) {
  .el-form-item__label {
    font-weight: 600;
    font-size: 14px;
    color: #303133;
  }
}

.add-link {
  margin-left: 8px;
}
</style>
