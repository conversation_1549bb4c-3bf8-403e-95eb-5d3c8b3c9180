<script setup lang="ts" name="RawMaterialPurchaseReceipt">
import { Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  PurchaseReceiveOrderCancel,
  PurchaseReceiveOrderDetail,
  PurchaseReceiveOrderList,
  PurchaseReceiveOrderListExport,
  PurchaseReceiveOrderPass,
} from '@/api/rawMaterIalPurchase'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import { debounce, getFilterData, orderStatusConfirmBox } from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const filterData = reactive({
  order_num: '',
  supplier_id: '',
  receipt_unit_id: '',
  purchase_time: '',
  voucher_num: '',
  status: '',
  purchase_start_date: '',
  purchase_end_date: '',
})

onMounted(() => {
  getData()
})
const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = PurchaseReceiveOrderList()
async function getData() {
  await fetchDataList(getQuery())
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}

// 列表请求参数
function getQuery() {
  const status = ((filterData.status as unknown as []) || []).join(',')
  return getFilterData({ ...filterData, status }, ['purchase_time'])
}

onActivated(getData)

const selectRow = ref()
const detailShow = ref()
const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = PurchaseReceiveOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: selectRow.value.id })
}

watch(
  () => filterData.purchase_time,
  (value: any) => {
    filterData.purchase_start_date = formatDate(value?.[0]) || ''
    filterData.purchase_end_date = formatDate(value?.[1]) || ''
  },
)

watch(
  () => filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

function showDetail(row: any) {
  selectRow.value = row
  detailShow.value = true
  getDataDetail()
}

function handDetail(row: any) {
  router.push({
    name: 'RawMaterialPurchaseReceiptDetail',
    params: { id: row?.id },
  })
}
async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({
    id,
    message: msg,
    api: PurchaseReceiveOrderPass,
  })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({
    id,
    message: msg,
    api: PurchaseReceiveOrderCancel,
  })
  getData()
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialPurchaseReceiptEdit',
    params: { id: row?.id },
  })
}

function handAdd() {
  router.push({
    name: 'RawMaterialPurchaseReceiptAdd',
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_num',
    title: '订单编号',
    soltName: 'order_num',
    width: '8%',
  },
  {
    sortable: true,
    field: 'voucher_num',
    title: '凭证号',
    width: 100,
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receipt_unit_name',
    title: '收货单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receipt_date',
    title: '收货时间',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'total_weight',
    title: '收货数量',
    isWeight: true,
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'warehouse_manager_name',
    title: '仓管员',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '订单金额',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    showOrder_status: true,
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const cColumnList = ref([
  {
    title: '原料信息',
    field: 'a',
    childrenList: [
      {
        sortable: true,
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'pur_order_num',
        title: '采购单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'brand',
        title: '原料品牌',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'craft',
        title: '原料工艺',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'color_scheme',
        title: '原料色系',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'batch_num',
        title: '原料批号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'production_order_num',
        title: '生产通知单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'cotton_origin',
        title: '棉花产地',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'yarn_origin',
        title: '棉纱产地',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'carton_num',
        title: '装箱单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'fapiao_num',
        title: '发票号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'weight_shortage_loss_rate',
        title: '欠重损耗率',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'spinning_type',
        title: '纺纱类型',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'production_date',
        title: '生产日期',
        is_date: true,
        formatTime: 'YYYY-MM-DD',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'level_name',
        title: '原料等级',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'raw_material_remark',
        title: '原料备注',
        minWidth: 100,
      },
    ],
  },
  {
    title: '整件',
    field: 'b',
    childrenList: [
      {
        sortable: true,
        field: 'whole_piece_count',
        title: '采购件数',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'whole_piece_weight',
        title: '件重',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '散件',
    field: 'c',
    childrenList: [
      {
        sortable: true,
        field: 'bulk_piece_count',
        title: '采购件数',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'bulk_weight',
        title: '采购数量',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '',
    field: 'd',
    childrenList: [
      {
        sortable: true,
        field: 'total_weight',
        title: '总采购数量',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'deduction_weight',
        title: '扣款数量',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '平均',
    field: 'e',
    childrenList: [
      {
        sortable: true,
        field: 'avg_actl_gross_weight',
        title: '实际毛重(kg)',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'avg_actl_tare',
        title: '实际皮重(kg)',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'avg_actl_net_weight',
        title: '实际净重(kg)',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'avg_actl_weight_shortage',
        title: '实际欠重(kg)',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '',
    field: 'f',
    childrenList: [
      {
        sortable: true,
        field: 'actl_total_net_weight',
        title: '实际总净重(kg)',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'actl_total_weight_shortage',
        title: '实际总欠重(kg)',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'weight_shortage_rate',
        title: '欠磅率(%)',
        minWidth: '5%',
        isUnderweightRate: true,
      },
    ],
  },
  {
    title: '金额信息',
    field: 'g',
    childrenList: [
      {
        sortable: true,
        field: 'other_price',
        title: '其他金额',
        minWidth: '5%',
        isPrice: true,
      },
      {
        sortable: true,
        field: 'unit_price',
        title: '单价',
        minWidth: '5%',
        isUnitPrice: true,
      },
      {
        sortable: true,
        field: 'total_price',
        title: '总金额',
        minWidth: '5%',
        isPrice: true,
      },
    ],
  },
  {
    title: '',
    field: 'h',
    childrenList: [
      {
        sortable: true,
        field: 'remark',
        title: '备注',
        minWidth: 150,
      },
    ],
  },
])

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '总计'

      if (['whole_piece_count', 'bulk_piece_count'].includes(column.property))
        return `${sumNum(data, column.property)}`

      if (
        [
          'whole_piece_weight',
          'bulk_weight',
          'total_weight',
          'deduction_weight',
          'avg_actl_gross_weight',
          'avg_actl_tare',
          'avg_actl_net_weight',
          'avg_actl_weight_shortage',
          'actl_total_net_weight',
          'actl_total_weight_shortage',
        ].includes(column.property)
      ) {
        return `${formatWeightDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      if (
        ['other_price', 'total_price', 'weight_shortage_rate'].includes(
          column.property,
        )
      ) {
        return `¥${formatPriceDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      return null
    }),
  ]
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!dataList?.value.list || dataList?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '原料采购收货单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = PurchaseReceiveOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getQuery(),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const tableConfig = ref({
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  show_footer: false,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '10%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  fieldApiKey: 'RawMaterialPurchaseReceipt_A',
})

const CTableConfig = ref({
  fieldApiKey: 'RawMaterialPurchaseReceipt_B',
  loadingDetail,
  height: '100%',
  operateWidth: '180',
  showSort: false,
  showSpanHeader: true,
  footerMethod,
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_num"
              placeholder="单据编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:">
          <template #content>
            <vxe-input
              v-model="filterData.voucher_num"
              style="width: 100%"
              placeholder="凭证号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{
                unit_type_id: `${BusinessUnitIdEnum.rawMaterial},${BusinessUnitIdEnum.dyeingMill}`,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货单位:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.receipt_unit_id"
              api="GetBusinessUnitListApi"
              :query="{
                unit_type_id: `${BusinessUnitIdEnum.knittingFactory},${BusinessUnitIdEnum.dyeingMill}`,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货时间:" width="330">
          <template #content>
            <SelectDate v-model="filterData.purchase_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.status"
              style="width: 200px"
              :multiple="true"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="`RawMaterialPurchaseReceiptAdd`"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="`RawMaterialPurchaseReceipt_export`"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_num="{ row }">
          <el-link type="primary" :underline="false" @click="showDetail(row)">
            {{
              row.order_num
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="`RawMaterialPurchaseReceiptDetail`"
              type="primary"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="`RawMaterialPurchaseReceiptEdit`"
              type="primary"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="`RawMaterialPurchaseReceipt_pass`"
              type="primary"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="`RawMaterialPurchaseReceipt_cancel`"
              type="primary"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="CTableConfig"
        :table-list="dataDetail.items"
        :column-list="cColumnList"
      />
    </FildCard>
  </div>
</template>
