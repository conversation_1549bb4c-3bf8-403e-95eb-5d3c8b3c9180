<script setup lang="ts" name="FpStockAdjustOrder">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onActivated, onMounted, reactive, ref, watch } from 'vue'
import FineSizeAdjustDetail from '../components/FineSizeAdjustDetail.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import {
  getProductAdjustOrder,
  getProductAdjustOrderList,
  updateProductAdjustOrderAuditStatusPass,
  updateProductAdjustOrderAuditStatusWait,
} from '@/api/fpStockAdjustOrder'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { usePageQuery } from '@/use/usePageQuery'

const { formatFilterObj, formatDateRange } = usePageQuery()

const mainOptionsTablesRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  biz_unit_id: '',
  warehouse_id: '',
  audit_status: [],
}))

const adjust_time = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateProductAdjustOrderAuditStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateProductAdjustOrderAuditStatusWait()
// 驳回
// const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = updateProductAdjustOrderAuditStatusReject()

const {
  fetchData: fetchDataList,
  data: mainDataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = getProductAdjustOrderList()
// 获取列表数据
async function getData() {
  const query = {
    ...filterData,
  }

  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (adjust_time?.value?.length) {
    query.start_adjust_time = formatDate(adjust_time.value[0])
    query.end_adjust_time = formatDate(adjust_time.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainDataList.value?.list)
    showFinishProductionDetail(mainDataList.value.list[0])
}

const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '8%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'adjust_time',
      title: '调整日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'warehouse_manager_name',
      title: '仓管员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_date',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  //   导出
  handleExport: async () => {
    // if (mainOptions.multipleSelection.length < 1) return ElMessage.warning('请勾选要导出的数据')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSheetOfProductionPlanList()
    // mainOptions.exportOptions.loadingExcel = true
    // exportExcel()
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //
    // ElMessage({
    //   type: 'success',
    //   message: '成功',
    // })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

watch(
  () => mainDataList.value,
  () => {
    mainOptions.mainList
          = mainDataList.value?.list?.map((item: any) => {
        const item_data
            = item?.item_data?.map((v: any) => {
              return {
                ...v,
                in_roll: formatTwoDecimalsDiv(item.in_roll), // 进仓匹数
                quote_roll: formatTwoDecimalsDiv(item.quote_roll), // 采购数量
                quote_weight: formatWeightDiv(item.quote_weight), // 采购基本单位数量
                total_weight: formatWeightDiv(item.total_weight), // 进仓数量
                weight_error: formatWeightDiv(item.weight_error), // 空差
                paper_tube_weight: formatWeightDiv(item.paper_tube_weight), // 纸筒总重
                settle_weight: formatWeightDiv(item.settle_weight), // 结算数量
                unit_price: formatUnitPriceDiv(item.unit_price), // 单价
                in_length: formatLengthDiv(item.in_length), // 进仓辅助数量
                quote_length: formatLengthDiv(item.quote_length), // 采购辅助数量
                length_unit_price: formatUnitPriceDiv(item.length_unit_price), // 辅助数量单价
                other_price: formatTwoDecimalsDiv(item.other_price), // 其他金额
                total_price: formatTwoDecimalsDiv(item.total_price), // 进仓金额
              }
            }) || []
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(item.total_roll), // 匹数总计
          total_weight: formatWeightDiv(item.total_weight), // 数量总计
          total_length: formatLengthDiv(item.total_length), // 辅助数量总计
          total_price: formatTwoDecimalsDiv(item.total_price), // 单据金额
          item_data,
        }
      }) || []
  },
  { deep: true },
)
// 表格选中事件
function handAllSelect({ records }: any) {
  mainOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  mainOptions.multipleSelection = records
}
function changeDate() {
  // adjust_time.value = [row.date_min, row.date_max]
  getData()
}

// 导出勾选的数据
// const exportExcel = () => {
//   mainOptionsTablesRef.value.tableRef.exportData({
//     filename: `成品采购进仓单列表${formatTime(new Date())}`,
//     // isFooter: true,
//     data: mainOptions.mainList,
//     columns: mainOptions.columnList.map((item: any) => {
//       return {
//         ...item,
//         field: item.field,
//       }
//     }),
//   })
// }

onMounted(() => {
  getData()
})
onActivated(getData)

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
// 表格操作列功能
// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: Number(row.id) })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: Number(row.id) })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 驳回
// const handReject = async (row: any) => {
//   const res = await deleteToast('确认驳回嘛？')
//   if (res) {
//     await rejectFetch({ audit_status: 1, id: row.id.toString() })
//     if (rejectSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(rejectMsg.value)
//     }
//   }
// }

function handDetail(row: any) {
  router.push({
    name: 'FpStockAdjustOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpStockAdjustOrderEdit',
    query: {
      id: row.id,
    },
  })
}
function handAdd() {
  router.push({
    name: 'FpStockAdjustOrderAdd',
  })
}

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        //   if (['total_weight'].includes(column.field)) {
        //     return sumNum(data, 'total_weight', '')
        //   }
        //   if (['paper_tube_weight'].includes(column.field)) {
        //     return sumNum(data, 'paper_tube_weight', '')
        //   }
        //   if (['settle_weight'].includes(column.field)) {
        //     return sumNum(data, 'settle_weight', '')
        //   }
        //   if (['in_length'].includes(column.field)) {
        //     return sumNum(data, 'in_length', '')
        //   }
        //   if (['quote_length'].includes(column.field)) {
        //     return sumNum(data, 'quote_length', '')
        //   }
        //   if (['total_price'].includes(column.field)) {
        //     return sumNum(data, 'total_price', '￥')
        //   }
        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '调整前',
      childrenList: [
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'roll',
          title: '库存匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'weight',
          title: '库存数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'measurement_unit_name',
          title: '单位',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'length',
          title: '库存辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '调整后',
      childrenList: [
        {
          sortable: true,
          field: 'adjust_customer_name',
          soltName: 'adjust_customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'adjust_product_code',
          soltName: 'adjust_product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'adjust_product_name',
          soltName: 'adjust_product_name',
          title: '成品名称',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'adjust_product_color_code',
          soltName: 'adjust_product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'adjust_product_color_name',
          soltName: 'adjust_product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'adjust_product_level_name',
          soltName: 'adjust_product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'adjust_product_remark',
          soltName: 'adjust_product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'adjust_dyelot_number',
          soltName: 'adjust_dyelot_number',
          title: '染厂缸号',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'adjust_roll',
          soltName: 'adjust_roll',
          title: '调整匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'adjust_weight',
          soltName: 'adjust_weight',
          title: '调整数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'adjust_length',
          soltName: 'adjust_length',
          title: '调整辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: '5%',
        },
      ],
    },
  ],
})
// 获取成品信息
function showFinishProductionDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

function adjustKeys(row: any, pre_key: string, val_key: string) {
  return row[pre_key] === row[val_key]
}

const { fetchData: DetailFetch, data: finishProData } = getProductAdjustOrder()
const currentFinishedProductAuditStatus = ref<any>(null) // 当前选中的成品数据
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  currentFinishedProductAuditStatus.value = finishProData.value // 设置当前选中的成品数据
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
        total_weight: formatWeightDiv(Number(item.total_weight)),
        weight_error: formatWeightDiv(Number(item.weight_error)),
        settle_weight: formatWeightDiv(Number(item.settle_weight)),
        paper_tube_weight: formatWeightDiv(Number(item.paper_tube_weight)),
        unit_price: formatUnitPriceDiv(Number(item.unit_price)),
        quote_length: formatLengthDiv(Number(item.quote_length)),
        in_length: formatLengthDiv(Number(item.in_length)),
        length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
        other_price: formatTwoDecimalsDiv(Number(item.other_price)),
        total_price: formatTwoDecimalsDiv(Number(item.total_price)),
        adjust_roll: formatTwoDecimalsDiv(item.adjust_roll), // 调整匹数
        adjust_weight: formatWeightDiv(item.adjust_weight), // 调整数量
        adjust_length: formatLengthDiv(item.adjust_length), // 调整辅助数量
        roll: formatTwoDecimalsDiv(item.roll), // 库存匹数
        length: formatLengthDiv(item.length), // 库存匹数
        weight: formatWeightDiv(item.weight), // 调整数量
      }
    },
  )
}
const FineSizeAdjustDetailRef = ref()
function showDialog(row: any) {
  FineSizeAdjustDetailRef.value.showDialog(row)
}
const allPrintData = computed(() => {
  if (currentFinishedProductAuditStatus.value) {
    //
    const printList: any[] = []
    currentFinishedProductAuditStatus.value.item_data.forEach((item: any) => {
      //
      item.item_data.forEach((it: any) => {
        //
        printList.push({
          fatherKey: item.id,
          product_name: item?.product_name, // 名称
          finish_product_craft: it?.finish_product_craft,
          density: it?.density,
          product_kind_name: it?.product_kind_name,
          bleach_name: it?.bleach_name,
          finish_product_width: it?.finish_product_width_and_unit_name,
          finish_product_gram_weight: it?.finish_product_gram_weight_and_unit_name,
          weaving_organization_name: it?.weaving_organization_name,
          product_code: item?.product_code,
          yarn_count: it?.yarn_count,
          finish_product_ingredient: item?.product_ingredient,
          measurement_unit_name: it?.measurement_unit_name,
          dyelot_number: item?.adjust_dyelot_number,
          weight: it.adjust_weight || 0,
          product_color_code: item?.adjust_product_color_code,
          product_color_name: item?.adjust_product_color_name,
          qr_code: it?.qr_code, // 二维码
          bar_code: it?.bar_code, // 条形码
          volume_number: it?.adjust_volume_number, // 匹号
          print_date: it?.print_date, // 打印时间
        })
      })
    })
    return printList
  }

  else {
    return []
  }
})
// 需要打印到的数据
function filterPrintList(id: number | undefined) {
  const printList: any[] = allPrintData.value.filter((item: any) => {
    if (item.fatherKey === id)
      return item
  })
  return printList
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input
              v-model="filterData.order_no"
              style="width: 100%"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.sale_system_id"
              api="GetSaleSystemDropdownListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 100%"
              api="GetPhysicalWarehouseDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="调整日期:" width="310">
          <template #content>
            <SelectDate
              v-model="adjust_time"
              style="width: 100%"
              @change-date="changeDate"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              multiple
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          v-has="'FpStockAdjustOrder_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        ref="mainOptionsTablesRef"
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showFinishProductionDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpStockAdjustOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpStockAdjustOrder_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpStockAdjustOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpStockAdjustOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      :tool-bar="false"
      title=""
      class="table-card-bottom"
    >
      <template #right-top>
        <PrintPopoverBtn
          v-if="finishProductionOptions.datalist.length && currentFinishedProductAuditStatus?.audit_status === 2"
          :print-type="PrintType.PrintTemplateTypeStock"
          :data-type="PrintDataType.Product"
          :list="allPrintData"
        />
      </template>
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #adjust_customer_name="{ row }">
          <span
            v-if="!adjustKeys(row, 'customer_name', 'adjust_customer_name')"
            class="adjust_key"
          >{{ row.adjust_customer_name }}</span>
          <span v-else>{{ row.adjust_customer_name }}</span>
        </template>
        <!-- S 成品信息 -->
        <template #adjust_product_code="{ row }">
          <span :class="{ adjust_key: !adjustKeys(row, 'product_id', 'adjust_product_id') }">{{ row.adjust_product_code }}</span>
        </template>
        <template #adjust_product_name="{ row }">
          <span :class="{ adjust_key: !adjustKeys(row, 'product_id', 'adjust_product_id') }">{{ row.adjust_product_name }}</span>
        </template>
        <!-- E 成品信息 -->
        <template #adjust_product_color_code="{ row }">
          <span
            :class="{ adjust_key: !adjustKeys(row, 'product_color_id', 'adjust_product_color_id') }"
          >{{ row.adjust_product_color_code }}</span>
        </template>
        <template #adjust_product_color_name="{ row }">
          <span
            :class="{ adjust_key: !adjustKeys(row, 'product_color_id', 'adjust_product_color_id') }"
          >{{ row.adjust_product_color_name }}</span>
        </template>
        <template #adjust_product_level_name="{ row }">
          <span
            v-if="
              !adjustKeys(row, 'product_level_name', 'adjust_product_level_name')
            "
            class="adjust_key"
          >{{ row.adjust_product_level_name }}</span>
          <span v-else>{{ row.adjust_product_level_name }}</span>
        </template>
        <template #adjust_product_remark="{ row }">
          <span
            v-if="!adjustKeys(row, 'product_remark', 'adjust_product_remark')"
            class="adjust_key"
          >{{ row.adjust_product_remark }}</span>
          <span v-else>{{ row.adjust_product_remark }}</span>
        </template>
        <template #adjust_dyelot_number="{ row }">
          <span
            v-if="!adjustKeys(row, 'dyelot_number', 'adjust_dyelot_number')"
            class="adjust_key"
          >{{ row.adjust_dyelot_number }}</span>
          <span v-else>{{ row.adjust_dyelot_number }}</span>
        </template>
        <template #adjust_roll="{ row }">
          <span
            v-if="!adjustKeys(row, 'roll', 'adjust_roll')"
            class="adjust_key"
          >{{ row.adjust_roll }}</span>
          <span v-else>{{ row.adjust_roll }}</span>
        </template>
        <template #adjust_weight="{ row }">
          <span
            v-if="!adjustKeys(row, 'weight', 'adjust_weight')"
            class="adjust_key"
          >{{ row.adjust_weight }}</span>
          <span v-else>{{ row.adjust_weight }}</span>
        </template>
        <template #adjust_length="{ row }">
          <span
            v-if="!adjustKeys(row, 'length', 'adjust_length')"
            class="adjust_key"
          >{{ row.adjust_length }}</span>
          <span v-else>{{ row.adjust_length }}</span>
        </template>

        <template #xima="{ row }">
          <el-link @click="showDialog(row)">
            查看
          </el-link>
          <PrintPopoverBtn
            v-if="finishProductionOptions.datalist.length && currentFinishedProductAuditStatus?.audit_status === 2"
            :print-btn-text-style="{ fontSize: '12px', color: '#0e7eff' }"
            print-btn-type="text"
            :print-type="PrintType.PrintTemplateTypeStock"
            :data-type="PrintDataType.Product"
            :list="filterPrintList(row.id)"
          />
        </template>
      </Table>
    </FildCard>
  </div>
  <FineSizeAdjustDetail ref="FineSizeAdjustDetailRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}

.adjust_key {
  color: red;
}
</style>
