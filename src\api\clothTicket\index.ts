import type { ResponseList } from '@/api/commonTs/index'
import { useRequest } from '@/use/useRequest'

// 获取生产排产单细码详情
export function GetFineCodeDetail() {
  return useRequest<Api.GetFineCodeDetail.Request, Api.GetFineCodeDetail.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/getFineCodeDetail',
    method: 'get',
  })
}
// 生产排产单细码称重
export function WeighingFineCode() {
  return useRequest<Api.WeighingFineCode.Request, Api.WeighingFineCode.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/weighingFineCode',
    method: 'put',
  })
}
