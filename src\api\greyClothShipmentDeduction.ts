import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmDeductionDeliveryOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/getGfmDeductionDeliveryOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmDeductionDeliveryOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/getGfmDeductionDeliveryOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmDeductionDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/getGfmDeductionDeliveryOrder',
    method: 'get',
  })
}

// 新建数据
export const addGfmDeductionDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/addGfmDeductionDeliveryOrder',
    method: 'post',
  })
}

// 编辑
export const updateGfmDeductionDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/updateGfmDeductionDeliveryOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmDeductionDeliveryOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/updateGfmDeductionDeliveryOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmDeductionDeliveryOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/updateGfmDeductionDeliveryOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmDeductionDeliveryOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/updateGfmDeductionDeliveryOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmDeductionDeliveryOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmDeductionDeliveryOrder/updateGfmDeductionDeliveryOrderStatusReject',
    method: 'put',
  })
}
