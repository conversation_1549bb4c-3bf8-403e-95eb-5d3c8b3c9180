<script lang="ts" setup name="ProductSaleEdit">
import { ElMessage } from 'element-plus'
import { computed, nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { VxeGridPropTypes } from 'vxe-table'
import { TrendCharts } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import currency from 'currency.js'
import AddOutStockDialog from '../components/AddOutStockDialog.vue'
import FinishStockDialog from '../components/FinishStockDialog.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import { GetAISaleProductOrder, getHistorySaleOrderList, getNextSalePrice, getSaleProductOrder, updateSaleProductOrder } from '@/api/productSale'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { EmployeeType } from '@/common/enum'
import {
  formatDate,
  formatHashTag,
  formatLengthDiv,
  formatLengthMul,
  formatPriceDiv,
  formatPriceMul,
  formatRollDiv,
  formatTwoDecimalsMul,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { deepClone, getCurrentDate, getFilterData, isMainUnit, strIsEmpty } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import GridTable from '@/components/GridTable/index.vue'
import useRouterList from '@/use/useRouterList'
import { columnList } from '@/pages/saleManagement/productSale/column'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import { BlocksModal } from '@/components/BuildingBlocks/index'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import { formValidatePass } from '@/common/rule'
import { vueEffect } from '@/use/vueEffect'
import { processDataOut } from '@/common/handBinary'
import AddressCard from '@/components/AddressCard/index.vue'
import { isBulk, isCustomerBook } from '@/components/SelectSaleMode/common'
import AITextarea from '@/components/AITextarea/index.vue'
import { OutTypeEnum } from '@/enum'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'
import SelectProductColorDialog from '@/components/SelectProductColorDialog/index.vue'

const routerList = useRouterList()
const addressCardRef = ref()

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^(?:(?:\+|00)86)?1\d{10}$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}

const state = reactive<any>({
  form: {
    sale_system_id: undefined, // 营销体系
    voucher_number: '', // 凭证单号
    shipment_type: '', // 出货类型
    warehousing: '', // 调入仓库
    customer_code: '', // 客户编号
    customer_name: '', // 客户名称
    sale_user_id: '', // 销售员
    sale_follow_id: '', // 销售跟单
    date: '', // 订单日期
    seetlements_type: '', // 结算类型
    processing_plant: '', // 加工厂名称
    user: '', // 联系人
    userPhone: '', // 联系电话
    logistics_company: '', // 物流公司
    logistics_region: '', // 物流区域
    receiver_address: '', // 收货地址
    tax_included: '', // 含税项目
    tax_rate: '', // 税率
    is_with_tax_rate: false, // 是否含税
    postage_item: '', // 邮费项目
    print_tag: '', // 出货标签
    order_type: '', // 订单类型
    sale_mode: '', // 订单类型
    internal_remark: '', // 内部备注
    shipping_remark: '', // 出货备注
    customer_id: '', // 客户id
    sale_group_id: '', // 销售群体id
    sale_group_name: '', // 销售群体名称
    canChangePickUpGoodsInOrder: true, // 是否可以修改齐单提货
    pick_up_goods_in_order: false, // 齐单提货
    same_color_same_dyelot: false, // 同色同缸
  },
  formRules: {
    sale_system_id: [{ required: true, validator: formValidatePass.sale_system_id(), message: '请选择营销体系', trigger: 'blur' }],
    shipment_type: [{ required: true, message: '请选择出货类型', trigger: 'blur' }],
    sale_user_id: [{ required: true, message: '请选择销售员', trigger: 'blur' }],
    warehousing: [{ trigger: 'blur', validator: checkWarehousing }],
    customer_code: [{ required: true, message: '请选择客户编号', trigger: 'blur' }],
    userPhone: [{ trigger: 'blur', validator: checkPhone }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
  tableList: [],
  multipleSelection: [],
})

function checkWarehousing(rule: any, value: any, callback: any) {
  if (state.form.shipment_type !== 1 && state.form.warehousing === '')
    callback(new Error('请选择调入仓库'))
  else
    callback()
}
const tableConfig = ref({
  fieldApiKey: 'productSaleEdit',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
})

const { fetchData: getFetch, data: fabricList, success, msg } = getSaleProductOrder()

const route = useRoute()
// onActivated(getInfomation)
onMounted(() => {
  getInfomation()
})
const customerRef = ref()
// 获取坯布信息
async function getInfomation() {
  await getFetch({ id: route.query.id })
  if (!success.value)
    return ElMessage.error(msg.value)

  customerRef.value.customerRef.inputLabel = fabricList.value.customer_code
  state.form.sale_group_id = fabricList.value.sale_group_id
  state.form.sale_group_name = fabricList.value.sale_group_name
  state.form.sale_system_id = fabricList.value.sale_system_id ? fabricList.value.sale_system_id : undefined
  state.form.voucher_number = fabricList.value.voucher_number
  state.form.shipment_type = fabricList.value.send_product_type
  state.form.warehousing = fabricList.value.warehouse_id
  state.form.customer_code = fabricList.value.customer_code
  state.form.customer_name = fabricList.value.customer_name
  state.form.customer_id = fabricList.value.customer_id ? fabricList.value.customer_id : undefined
  state.form.sale_user_id = fabricList.value.sale_user_id ? fabricList.value.sale_user_id : undefined
  state.form.sale_follow_id = fabricList.value.sale_follower_id ? fabricList.value.sale_follower_id : undefined
  if (fabricList.value.order_time === '')
    state.form.date = getCurrentDate()
  else
    state.form.date = fabricList.value.order_time

  state.form.seetlements_type = fabricList.value.settle_type ? fabricList.value.settle_type : undefined
  state.form.processing_plant = fabricList.value.process_factory_id
  state.form.user = fabricList.value.contacts
  state.form.userPhone = fabricList.value.contact_phone
  state.form.logistics_company = fabricList.value.logistics_company_id
  state.form.logistics_region = fabricList.value.logistics_area
  state.form.receiver_address = fabricList.value.receipt_address
  state.form.tax_included = fabricList.value.info_sale_taxable_item_id ? fabricList.value.info_sale_taxable_item_id : undefined
  state.form.tax_rate = fabricList.value.sale_tax_rate ? formatPriceDiv(fabricList.value.sale_tax_rate) : undefined
  state.form.is_with_tax_rate = fabricList.value.is_with_tax_rate ? fabricList.value.is_with_tax_rate : false
  state.form.postage_item = fabricList.value.postage_items ? fabricList.value.postage_items : undefined
  state.form.print_tag = fabricList.value.print_tag
  state.form.shipping_label = fabricList.value.print_tag
  state.form.sale_mode = fabricList.value.sale_mode
  state.form.internal_remark = fabricList.value.internal_remark
  state.form.shipping_remark = fabricList.value.send_product_remark
  state.form.pick_up_goods_in_order = fabricList.value.pick_up_goods_in_order || false
  state.form.same_color_same_dyelot = fabricList.value.same_color_same_dyelot || false
  state.form.canChangePickUpGoodsInOrder = fabricList.value.send_product_type === OutTypeEnum.SaleTransfer
  state.tableList = fabricList.value?.item_data

  for (let i = 0; i < state.tableList?.length; i++) {
    const item = state.tableList[i]
    const temp = Object.assign(state.tableList[i], {
      roll: Number(formatPriceDiv(item.roll)) || 0,
      weight: Number(formatWeightDiv(item.weight)) || 0,
      adjust_weight_error: Number(formatWeightDiv(item.adjust_weight_error)) || 0,
      offset_weight_error: Number(formatWeightDiv(item.offset_weight_error)) || 0,
      settle_weight_error: Number(formatWeightDiv(item.settle_weight_error)) || 0,
      sale_price: Number(formatUnitPriceDiv(item.sale_price)) || 0,
      length_cut_sale_price: Number(formatUnitPriceDiv(item.length_cut_sale_price)) || 0,
      length: Number(formatLengthDiv(item.length)) || 0,
      other_price: Number(formatPriceDiv(item.other_price)) || 0,
      book_roll: Number(formatPriceDiv(item.book_roll)) || 0,
      purchase_roll: Number(formatPriceDiv(item.purchase_roll)) || 0,
      purchase_weight: Number(formatWeightDiv(item.purchase_weight)) || 0,
      purchase_length: Number(formatLengthDiv(item.purchase_length)) || 0,
      shortage_roll: Number(formatPriceDiv(item.shortage_roll)) || 0,
      shortage_weight: Number(formatWeightDiv(item.shortage_weight)) || 0,
      shortage_length: Number(formatLengthDiv(item.shortage_length)) || 0,
      stock_weight: item.available_weight,
      quantity_price: '-', // 数量结算上次价
      length_price: '-', // 辅助数量结算上次价
      last_settlement: '', // 上次结算方式
    })
    state.tableList[i] = conductUnitPrice(temp, true)
  }
}

// watch(
//   () => fabricList.value,
//   () => {
//     //

//   },
// )

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['book_roll'].includes(column.property))
        return `${sumNum(data, 'book_roll')}`

      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')}`

      if (['length'].includes(column.property))
        return `${sumNum(data, 'length')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['purchase_roll'].includes(column.property))
        return `${sumNum(data, 'purchase_roll')}`

      if (['purchase_weight'].includes(column.property))
        return `${sumNum(data, 'purchase_weight')}`

      if (['purchase_length'].includes(column.property))
        return `${sumNum(data, 'purchase_length')}`

      if (['shortage_roll'].includes(column.property))
        return `${sumNum(data, 'shortage_roll')}`

      if (['shortage_weight'].includes(column.property))
        return `${sumNum(data, 'shortage_weight')}`

      if (['shortage_length'].includes(column.property))
        return `${sumNum(data, 'shortage_length')}`

      return null
    }),
  ]
}

const ruleFormRef = ref()

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

// 处理空差:结算空差=优惠空差+调整空差
function handleWeightError(row: any) {
  row.settle_weight_error = currency(row.offset_weight_error).add(row.adjust_weight_error).value
  return row
}

const FinishStockDialogRef = ref()

function handStockAdd() {
  FinishStockDialogRef.value.state.showModal = true
  FinishStockDialogRef.value.state.filterData.sale_customer_id = state.form?.customer_id
  FinishStockDialogRef.value.state.filterData.sale_system_id = state.form.sale_system_id || ''
  FinishStockDialogRef.value.state.filterData.customer_id = isCustomerBook(state.form.sale_mode) ? state.form.customer_id : 0
  FinishStockDialogRef.value.state.customer_name = isCustomerBook(state.form.sale_mode) ? state.form.customer_name : 0
}

const AddOutStockDialogRef = ref()

function handAdd() {
  AddOutStockDialogRef.value.state.showModal = true
  AddOutStockDialogRef.value.state.sale_system_id = state.form.sale_system_id
  AddOutStockDialogRef.value.state.sale_customer_id = state.form.customer_id
}
function handleAddressChange(address: any) { // 当选框的地址发送变化的时候把外部的出货标签重写
  state.form.print_tag = address.print_tag
}

// 选择发货类型的时候
function handleShipmentChange(shipment: any) {
  state.form.canChangePickUpGoodsInOrder = true
  // 销调-选择齐单提货
  if (shipment.out_order_type === OutTypeEnum.SaleTransfer) {
    state.form.pick_up_goods_in_order = true
  }
  else if (shipment.out_order_type === OutTypeEnum.Shipment) {
    // 出货类型-不能选择
    state.form.canChangePickUpGoodsInOrder = false
    state.form.pick_up_goods_in_order = false
  }
}

// 点击齐单提货的时候
function handlePickUpGoodsInOrderClick() {
  if (!state.form.canChangePickUpGoodsInOrder)
    return ElMessage.error('非销调类型无法齐单提货')
}

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = updateSaleProductOrder()

async function handleSure() { // 提交编辑
  const { addressData, deliveryData } = await addressCardRef.value?.getFormData()
  //

  //

  // return
  if (!deliveryData.id)
    return ElMessage.error('发货类型是必选项')
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条成品信息')
  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    const {
      roll,
      length,
      sale_price,
      length_cut_sale_price,
      weight,
      adjust_weight_error,
      other_price,
      settle_weight_error,
      book_roll,
      purchase_roll,
      purchase_weight,
      purchase_length,
      shortage_roll,
      shortage_weight,
      shortage_length,
      offset_weight_error,
    } = list[i]

    if (!item.auxiliary_unit_id)
      return ElMessage.error(`成品编号为${item.product_code}的数据,结算单位不能为空`)

    if (strIsEmpty(item.roll))
      return ElMessage.error(`成品编号为${item.product_code}的数据,数量单价的匹数不能为空`)

    if (!Number(item.weight) && isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,数量单价的数量不能为空且不能为0`)

    if (item.sale_price === '' && isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,数量单价的销售单价不能为空`)

    if (!Number(item.length) && !isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量不能为空且不能为0`)

    if (item.length_cut_sale_price === '' && !isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量的销售单价不能为空`)

    if (strIsEmpty(item.book_roll))
      return ElMessage.error(`成品编号为${item.product_code}的数据,预约匹数不能为空`)

    if (roll < 0 || length < 0 || weight < 0 || book_roll < 0 || purchase_roll < 0 || purchase_weight < 0 || purchase_length < 0 || shortage_roll < 0 || shortage_weight < 0 || shortage_length < 0)
      return ElMessage.error('所填写数值不可为负数')

    list[i] = {
      ...list[i],
      roll: formatPriceMul(roll),
      weight: formatWeightMul(weight),
      adjust_weight_error: formatWeightMul(adjust_weight_error),
      length: formatLengthMul(length),
      other_price: formatPriceMul(other_price),
      book_roll: formatPriceMul(book_roll),
      purchase_roll: formatPriceMul(purchase_roll),
      purchase_length: formatLengthMul(purchase_length),
      purchase_weight: formatWeightMul(purchase_weight),
      sale_price: formatUnitPriceMul(sale_price),
      length_cut_sale_price: formatUnitPriceMul(length_cut_sale_price),
      shortage_weight: formatWeightMul(shortage_weight),
      shortage_roll: formatPriceMul(shortage_roll),
      shortage_length: formatLengthMul(shortage_length),
      settle_weight_error: formatWeightMul(settle_weight_error),
      offset_weight_error: formatWeightMul(offset_weight_error),
    }
  }
  const query = {
    sale_group_id: state.form.sale_group_id || 0, // 销售群体 id
    sale_system_id: state.form.sale_system_id || 0, // 营销体系
    voucher_number: state.form.voucher_number, // 凭证单号
    order_time: formatDate(state.form.date), // 订单日期
    customer_id: state.form.customer_id, // 客户id
    sale_user_id: state.form.sale_user_id || 0, // 销售员
    sale_follower_id: state.form.sale_follow_id || 0, // 销售跟单
    settle_type: state.form.seetlements_type || 0, // 结算类型
    postage_items: state.form.postage_item || 0, // 邮费项目
    is_with_tax_rate: state.form.is_with_tax_rate, // 是否含税
    sale_tax_rate: formatPriceMul(state.form.tax_rate), // 税率
    info_sale_taxable_item_id: state.form.tax_included || 0, // 含税项目
    internal_remark: state.form.internal_remark, // 内部备注
    send_product_remark: state.form.shipping_remark, // 出货备注
    // 以下是地址卡的发货类型数据 saleInfos
    code: deliveryData.code, // 发货类型编号
    sale_shipment_name: deliveryData.name, // 发货类型名称
    send_product_type: deliveryData.out_order_type, // 出仓类型
    warehouse_id: deliveryData.ware_house_in_id, // 调至仓库id
    order_type: 0, // 订单类型
    // 以下是地址卡的收货方式数据 addressInfos
    receipt_address: addressData.location.join(' '), // 收货地址(省市区)
    receipt_address_detail: addressData.address, // 收货地址(详情地址)
    contacts: addressData.contact_name, // 联系人
    contact_phone: addressData.phone, // 联系电话
    logistics_area: addressData.logistics_area, // 物流区域
    is_default: addressData.is_default, // 是否默认地址
    logistics_company: addressData.logistics_company, // 物流公司名称
    process_factory: addressData.name, // 加工厂昵称
    print_tag: state.form.print_tag || addressData.print_tag, // 出货标签
    sale_mode: state.form.sale_mode || 0,
    pick_up_goods_in_order: state.form.pick_up_goods_in_order, // 齐单提货
    same_color_same_dyelot: state.form.same_color_same_dyelot, // 同色同缸
    item_data: list, // 成品信息列表
    id: Number(route.query.id), // 订单id
    sale_mode: state.form.sale_mode || 0,
  }
  //

  // return
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        // router.push({ name: 'ProductSale' })
        routerList.push({ name: 'ProductSaleDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handSureStock(list: any) {
  list.forEach((item: any) => {
    let temp = {
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      product_id: item.product_id,
      customer_account_num: '',
      product_code: item.product_code,
      product_name: item.product_name,
      product_color_code: item.product_color_code,
      product_color_name: item.product_color_name,
      product_color_id: item.product_color_id,
      product_color_kind_name: item.product_color_kind_name,
      product_color_kind_id: item.product_color_kind_id,
      dyelot_number: item.dyelot_number,
      product_level_name: item.product_level_name,
      product_level_id: item.product_level_id,
      product_remark: item.product_remark,
      measurement_unit_name: item.measurement_unit_name,
      measurement_unit_id: item.measurement_unit_id,
      stock_roll: item.available_roll,
      stock_weight: item.available_weight,
      warehouse_name: item.warehouse_name,
      warehouse_id: item.warehouse_id,
      book_roll: '',
      purchase_roll: '',
      purchase_weight: '',
      purchase_length: '',
      shortage_roll: '',
      shortage_weight: '',
      shortage_length: '',
      roll: '',
      length: '',
      weight: '',
      stock_remark: item.remark,
      standard_sale_price: item.standard_sale_price,
      sale_level_name: item.sale_level_name,
      sale_level_id: item.sale_level_id,
      sale_price: formatUnitPriceDiv(item.sale_price),
      offset_sale_price: item.offset_sale_price,
      weight_error: item.weight_error,
      offset_weight_error: formatWeightDiv(item.offset_weight_error),
      length_cut_sale_price: formatUnitPriceDiv(item.length_cut_sale_price),
      weight_cut_sale_price: formatUnitPriceDiv(item.weight_cut_sale_price),
      offset_length_cut_sale_price: item.offset_length_cut_sale_price,
      settle_weight_error: '',
      stock_product_id: item.stock_product_id,
      standard_length_cut_sale_price: item.standard_length_cut_sale_price,
      standard_weight_cut_sale_price: item.standard_weight_cut_sale_price,
      is_display_price: item.is_display_price,
      adjust_weight_error: '',
      product_kind_id: item?.product_kind_id,
      quantity_price: '-', // 数量结算上次价
      length_price: '-', // 辅助数量结算上次价
      last_settlement: '', // 上次结算方式
      standard_weight: formatWeightDiv(item.standard_weight),
    }
    temp = getUnitPrice(temp)
    temp = handleWeightError(temp)
    state.tableList.push(conductUnitPrice(temp, true))
  })
}

function handSureOutStock(list: any) {
  list.forEach((item: any) => {
    let temp = {
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      product_id: item.product_id,
      customer_account_num: '',
      product_code: item.product_code,
      product_name: item.product_name,
      product_color_code: item.product_color_code,
      product_color_name: item.product_color_name,
      product_color_id: item.product_color_id,
      product_color_kind_name: item.product_color_kind_name,
      product_color_kind_id: item.product_color_kind_id,
      dyelot_number: item.dyelot_number,
      product_level_name: item.product_level_name,
      product_level_id: item.product_level_id,
      product_remark: item.product_remark,
      measurement_unit_name: item.measurement_unit_name,
      measurement_unit_id: item.measurement_unit_id,
      stock_roll: item.roll,
      stock_weight: item.weight,
      warehouse_name: item.warehouse_name,
      warehouse_id: item.warehouse_id,
      book_roll: '',
      purchase_roll: '',
      purchase_weight: '',
      purchase_length: '',
      roll: formatPriceDiv(item.shortage_roll),
      weight: formatWeightDiv(item.shortage_weight),
      length: formatLengthDiv(item.shortage_length),
      shortage_roll: '',
      shortage_weight: '',
      shortage_length: '',
      remark: '',
      standard_sale_price: item.standard_sale_price,
      sale_level: item.sale_level_name,
      sale_level_id: item.sale_level_id,
      sale_price: formatUnitPriceDiv(item.sale_price),
      offset_sale_price: item.offset_sale_price,
      weight_error: item.weight_error,
      offset_weight_error: formatWeightDiv(item.offset_weight_error),
      length_cut_sale_price: formatUnitPriceDiv(item.length_cut_sale_price),
      weight_cut_sale_price: formatUnitPriceDiv(item.weight_cut_sale_price),
      offset_length_cut_sale_price: item.offset_length_cut_sale_price,
      settle_weight_error: '',
      stock_product_id: item.stock_product_id,
      standard_length_cut_sale_price: item.standard_length_cut_sale_price,
      standard_weight_cut_sale_price: item.standard_weight_cut_sale_price,
      is_display_price: item.is_display_price,
      adjust_weight_error: '',
      product_kind_id: item?.product_kind_id,
      quantity_price: '-', // 数量结算上次价
      length_price: '-', // 辅助数量结算上次价
      last_settlement: '', // 上次结算方式
      standard_weight: formatWeightDiv(item.standard_weight),
    }
    temp = getUnitPrice(temp)
    temp = handleWeightError(temp)
    state.tableList.push(conductUnitPrice(temp, true))
  })
  AddOutStockDialogRef.value.state.showModal = false
}

/**
 * 处理销售单价取值
 * @param item
 * 后端返回的单价都是已经处理好的，前端只需要取值即可
 * sale_price 大货最终单价 - 主辅都用这个
 * weight_cut_sale_price  剪板最终单价-主单位
 * length_cut_sale_price  剪板最终单价-辅助单位
 */
function getUnitPrice(item: any) {
  if (isBulk(state.form.sale_mode)) {
    // 大货
    // item.sale_price = item.sale_price // 大货主单位单价
    item.length_cut_sale_price = item.sale_price // 大货辅助单位单价
  }
  else {
    // 剪板
    item.sale_price = item.weight_cut_sale_price // 剪板主单位单价
    // item.length_cut_sale_price = item.length_cut_sale_price // 剪板辅助数量单价
  }
  return item
}

const bulkSetting = ref<any>({})

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value, quickInputResult }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.forEach((item: any, index: number) => {
    if (item?.selected) {
      if (row.quickInput && quickInputResult?.[index])
        item[row.field] = quickInputResult[index]
      else
        item[row.field] = value[row.field]

      if (row.field === 'roll')
        item = changeRoll(item)
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'customer_account_num',
    title: '款号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'weight',
    title: '数量单价-数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'sale_price',
    title: '数量单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'adjust_weight_error',
    title: '调整空差',
    component: 'input',
    type: 'float',
  },
  {
    field: 'length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'length_cut_sale_price',
    title: '辅助数量单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'book_roll',
    title: '预约匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'purchase_roll',
    title: '采购匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'purchase_weight',
    title: '采购数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'purchase_length',
    title: '采购辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'shortage_roll',
    title: '欠货匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'shortage_weight',
    title: '欠货数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'shortage_length',
    title: '欠货辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function selectCustomerValueChange(val: any) {
  state.form.customer_name = val.name
  state.form.customer_code = val.code
  state.form.customer_id = val.id
  state.form.sale_group_id = val.sale_group_id || ''
  state.form.sale_group_name = val.sale_group_name || ''
  state.form.sale_user_id = val.seller_id || ''
  state.form.sale_follow_id = val.order_follower_id || ''
  state.form.seetlements_type = val?.settle_type || ''
  state.form.receiver_address = val.address || ''
  state.form.userPhone = val.phone || ''
  state.form.sale_system_id = val?.select_sale_system_id || ''
}

// 输入订单数量-匹数=预约匹数+欠货匹数
function changeRoll(row: any) {
  // 预约信息-匹数
  // 若订单匹数 ≤ 可用匹数，则默认等于订单匹数
  // 若订单匹数 > 可用匹数，则默认等于可用匹数
  const stock_roll = formatPriceDiv(row.stock_roll)
  const input_roll = Number(row.roll) // 输入的订单匹数
  row.book_roll = Math.min(input_roll, stock_roll)
  // 若订单匹数>可用匹数则欠货匹数=订单匹数-可用匹数
  row.shortage_roll = ''
  if (input_roll > stock_roll)
    row.shortage_roll = currency(input_roll).subtract(stock_roll).value
  if (row.standard_weight)
    row.weight = currency(row.roll).multiply(row.standard_weight) // 数量=匹数*标准报价
  return row
}
const productColorId = computed<string>(() => state.tableList?.map((product: { product_color_id: any }) => product.product_color_id).join(','))
// 每当 tableList 发生变化时
watch(() => [state.form.customer_id, state.form.sale_system_id, productColorId.value], async () => {
  // 如果客户id为空 而且 成品信息列表 数组不为空 那么就执行清空操作
  if (!state.form.customer_id && state.tableList.length) {
    for (let i = 0; i < state.tableList.length; i++) {
      // const element = array[i]
      state.tableList[i].quantity_price = ''
      state.tableList[i].length_price = ''
      state.tableList[i].last_settlement = ''
    }
  }
  const customer_id = state.form.customer_id
  const sale_system_id = state.form.sale_system_id
  const query = { customer_id, sale_system_id }
  if (query.customer_id && productColorId.value)
    await getNP(query)
})

const { fetchData: getNextPrice } = getNextSalePrice() // 获取上次价 函数

// 获取上次价格
async function getNP(query: { customer_id: string, sale_system_id: string }) {
  const result: any = await getNextPrice({
    ...query,
    product_color_id: productColorId.value,
  })
  //

  // id: 1756388631765504 颜色id
  // price: 0 上次价
  // type: "L" L:长度结算 W:数量结算
  if (result.success) {
    state.tableList.forEach((item: any) => {
      const ursIndex = result.data.findIndex((product: any) => product.id === item.product_color_id)
      if (ursIndex !== -1) {
        if (result.data[ursIndex].type === 'W') {
          item.quantity_price = formatPriceDiv(result.data[ursIndex].price)
          item.length_price = ''
          item.last_settlement = 'W'
          // item.sale_price = result.data[ursIndex].default_latest_price ? formatPriceDiv(result.data[ursIndex].price) : undefined
        }
        else if (result.data[ursIndex].type === 'L') {
          item.quantity_price = ''
          item.length_price = formatPriceDiv(result.data[ursIndex].price)
          item.last_settlement = 'L'
          // item.length_cut_sale_price = result.data[ursIndex].default_latest_price ? formatPriceDiv(result.data[ursIndex].price) : undefined
        }
        else {
          item.quantity_price = ''
          item.length_price = ''
          item.last_settlement = ''
          // item.sale_price = undefined
          // item.length_cut_sale_price = undefined
        }
      }
    })
  }
}

// 成品编号和成品名称弹出表格
const finishedInfos = [
// 成品编号和成品名称
  {
    field: 'finish_product_name',
    colGroupHeader: true,
    title: '成品名称',
    minWidth: 100,
    childrenList: [
      {
        field: 'finish_product_name',
        title: '成品名称',
        minWidth: 100,
      },
    ],
  },
  {
    field: 'finish_product_code',
    colGroupHeader: true,
    title: '成品编号',
    minWidth: 100,
    childrenList: [
      {
        field: 'finish_product_code',
        title: '成品编号',
        minWidth: 100,
      },
    ],
  },
  // 颜色和色号
  {
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
    isEdit: true,
    colGroupHeader: true,
    childrenList: [
      {
        field: 'product_color_code',
        isEdit: true,
        title: '色号',
        minWidth: 100,
      },
    ],
  },
  {
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
    isEdit: true,
    colGroupHeader: true,
    childrenList: [
      {
        field: 'product_color_name',
        isEdit: true,
        title: '颜色',
        minWidth: 100,
      },
    ],
  },
]
// 临时输入框的信息
const tcInfos: any = [
  {
    field: 'finish_product_code',
    title: '成品编号',
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
  },
  {
    field: 'product_color_code',
    title: '色号',
  },
  {
    field: 'product_color_name',
    title: '颜色',
  },
]
// 临时信息
const componentRemoteSearch = ref({
  finish_product_code: '',
  finish_product_name: '',
  color_name: '',
  color_code: '',
})

// 历史价格弹窗的表单内容
const historicalPriceQuery = ref({
  arrange_time: null, // 质检日期
  sale_system_id: '', // 营销体系ID
  product_id: '', // 成品ID
  product_color_id: '', // 颜色ID
})
const priceFluctuation = ref(false) // 是否显示价格浮动弹窗
// 展开价格浮动弹窗
async function handleShowFluctuation(_row: any) {
  historicalPriceQuery.value.product_id = _row.product_id // 成品id
  historicalPriceQuery.value.product_color_id = _row.product_color_id // 颜色id
  historicalPriceQuery.value.sale_system_id = state.form.sale_system_id // 营销体系id

  componentRemoteSearch.value.finish_product_code = _row?.product_code || '' // 给临时变量绑定成品编号
  componentRemoteSearch.value.finish_product_name = _row?.product_name || '' // 给临时变量绑定成品名称

  componentRemoteSearch.value.color_code = _row?.product_color_code || '' // 给临时变量绑定色号
  componentRemoteSearch.value.color_name = _row?.product_color_name || '' // 给临时变量绑定色号名称
  // 将关键信息带进去
  // await getHD()
  priceFluctuation.value = true // 开启弹窗
}
// 选中成品编号或者成品名称
function changeProductSelect(val: any) {
  historicalPriceQuery.value.product_id = val?.id || '' // 请求参数添加成品id
  historicalPriceQuery.value.product_color_id = '' // 请求参数添加颜色id

  componentRemoteSearch.value.finish_product_code = val?.finish_product_code || ''
  componentRemoteSearch.value.finish_product_name = val?.finish_product_name || ''

  componentRemoteSearch.value.color_code = ''
  componentRemoteSearch.value.color_name = ''
}
// 当颜色发生变化
function changeColor(val: any) {
  historicalPriceQuery.value.product_color_id = val?.id || ''
  componentRemoteSearch.value.color_code = val?.product_color_code || ''
  componentRemoteSearch.value.color_name = val?.product_color_name || ''
}
const TableRef = ref<InstanceType<typeof GridTable> | null>() // 弹出层表格的实例
// 弹出层的表格渲染配置
const gridColumnList = ref<VxeGridPropTypes.Columns | any>([
  { field: 'order_no', title: '销售单号', minWidth: 100 },
  { field: 'order_date', title: '订单日期', minWidth: 100, slots: {
    default: ({ row }: any) => {
      return formatDate(row.order_date)
    },
  } },
  { field: 'sale_system_name', title: '营销体系', minWidth: 100 },
  { field: 'customer_name', title: '客户名称', minWidth: 100 },
  { field: 'product_code', title: '成品编号', minWidth: 100 },
  { field: 'product_name', title: '成品名称', minWidth: 100 },
  { field: 'product_color_code', title: '色号', minWidth: 100 },
  { field: 'product_color_name', title: '颜色', minWidth: 100 },
  { field: 'measurement_unit_name', title: '数量单位', minWidth: 100 },
  { field: 'roll', title: '匹数', minWidth: 100 },
  { field: 'weight', title: '数量', minWidth: 100 },
  { field: 'standard_price', title: '标准价', minWidth: 100 },
  { field: 'unit_price', title: '销售单价', minWidth: 100 },
  { field: 'length', title: '辅助数量', minWidth: 100 },
  { field: 'length_unit_price', title: '辅助数量销售单价', minWidth: 100 },
  { field: 'standard_weight_error', title: '标准空差', minWidth: 100 },
  { field: 'settle_weight_error', title: '结算空差', minWidth: 100 },
])

const hpage = ref(1)
const hsize = ref(50)
const { fetchData: gethistoryData, data: historyData, total } = getHistorySaleOrderList() // 历史价格

const hloadding = ref(true) // 表格加载
const hData = ref([]) // 历史价格数据
async function getHD() {
  hloadding.value = true
  const query = {
    sale_system_id: historicalPriceQuery.value.sale_system_id, // 营销体系ID
    product_id: historicalPriceQuery.value.product_id, // 成品ID
    product_color_id: historicalPriceQuery.value.product_color_id, // 颜色ID
    order_date_begin: historicalPriceQuery.value.arrange_time ? dayjs(historicalPriceQuery.value.arrange_time[0]).format('YYYY-MM-DD') : '', // 质检日期 开始
    order_date_end: historicalPriceQuery.value.arrange_time ? dayjs(historicalPriceQuery.value.arrange_time[1]).format('YYYY-MM-DD') : '', // 质检日期 结束
    page: hpage.value,
    size: hsize.value,
    customer_id: state.form.customer_id,
  }
  const result = await gethistoryData(getFilterData(query))
  if (result.success && result.total) {
    const afterList: any = processDataOut(historyData.value)
    hData.value = afterList.list.map((item: any) => {
      return {
        ...item,
        unit_price: formatTwoDecimalsMul(Number(item.unit_price)),
      }
    })
    await nextTick(() => {
      TableRef.value?.TableRef?.loadData(afterList.list)
    })
  }

  else {
    hData.value = []
  }

  hloadding.value = false
}
const elPaginationConfig = computed(() => ({
  defaultPageSize: 50,
  page: hpage.value,
  pageSizes: [50, 100, 500, 1000],
  size: hsize.value,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: total.value,
  loading: hloadding.value,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))

function handleSizeChange(val: number) {
  hpage.value = 1
  hsize.value = val
}
function handleCurrentChange(val: number) {
  hpage.value = val
}
// 当请求参数发生变化的时候，执行这段请求
vueEffect(async () => {
  if (priceFluctuation.value)
    await getHD()
}, [historicalPriceQuery, hpage, hsize, priceFluctuation], 300, false, 'throttle')

function onBMclose() {
  hpage.value = 1
  hsize.value = 50
  // hData.value = []
}

/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 * @param e 选择单位返回的
 */
function conductUnitPrice(item: any, isInit = false, e: any = null) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.measurement_unit_id
    item.auxiliary_unit_name = item.measurement_unit_name
  }
  // 保留上次设置的价格
  if (isInit) {
    item.pre_length_cut_sale_price = item.length_cut_sale_price
    item.pre_sale_price = item.sale_price
  }
  if (isMainUnit(item)) {
    item.sale_price = item.pre_sale_price
    if (Number(item.length_cut_sale_price) !== 0) {
      item.pre_length_cut_sale_price = item.length_cut_sale_price
      item.length_cut_sale_price = 0 // 主单位-把辅助单价置0
    }
  }
  else {
    item.length_cut_sale_price = item.pre_length_cut_sale_price
    if (Number(item.sale_price) !== 0) {
      item.pre_sale_price = item.sale_price
      item.sale_price = 0 // 辅助单位-把单价置0
    }
  }
  if (e)
    item.auxiliary_unit_name = e.name

  // 数量-标准报价/销售报价
  item.standard_main_sale_price = isBulk(state.form.sale_mode) ? item.standard_sale_price : item.standard_weight_cut_sale_price

  // 辅助数量-标准报价/销售报价
  item.standard_length_sale_price = isBulk(state.form.sale_mode) ? item.standard_sale_price : item.standard_length_cut_sale_price
  return item
}
// 计算属性处理地址数据
const addressDataProps = computed(() => {
  if (!fabricList.value && !fabricList.value.id)
    return null
  //
  return {
    id: 0,
    location: fabricList.value.receipt_address ? fabricList.value.receipt_address.split(',') : [], // 省市区地址
    address: fabricList.value.receipt_address_detail, // 详细地址
    biz_uint_id: fabricList.value.customer_id, // 客户id
    name: fabricList.value.process_factory, // 加工厂名称
    is_default: fabricList.value.is_default, // 是否默认地址 (没有默认选择)
    logistics_company: fabricList.value.logistics_company, // 物流公司
    logistics_area: fabricList.value.logistics_area, // 物流区域
    contact_name: fabricList.value.contacts, // 联系人名称
    phone: fabricList.value.contact_phone, // 手机号
    print_tag: fabricList.value.print_tag, // 打印打印标签
  }
})

const textarea = ref('')
const { fetchData: getAISaleProductOrder, data: AISaleProductOrder, success: AISaleProductOrderSuccess, msg: AISaleProductOrderMsg, loading } = GetAISaleProductOrder()
async function handleSubmit(content: string) {
  await getAISaleProductOrder({
    content,
  })
  if (!AISaleProductOrderSuccess.value)
    return ElMessage.error(AISaleProductOrderMsg.value)
  textarea.value = ''
  state.form = {
    sale_system_id: AISaleProductOrder.value?.sale_system_id || state.form.sale_system_id, // 营销体系
    voucher_number: state.form.voucher_number, // 凭证单号
    date: state.form.date, // 订单日期
    customer_id: AISaleProductOrder.value?.customer_id || state.form.customer_id, // 客户 id
    customer_code: AISaleProductOrder.value?.customer_code || state.form.customer_code, // 客户编号
    customer_name: AISaleProductOrder.value?.customer_name || state.form.customer_name, // 客户名称
    sale_group_name: AISaleProductOrder.value?.sale_group_name || state.form.sale_group_name, // 销售群体
    sale_group_id: AISaleProductOrder.value?.sale_group_id || state.form.sale_group_id, // 销售群体 id
    sale_user_id: AISaleProductOrder.value?.sale_user_id || state.form.sale_user_id, // 销售员
    sale_follower_id: AISaleProductOrder.value?.sale_follower_id || state.form.sale_follower_id, // 销售跟单
    settle_type: AISaleProductOrder.value?.settle_type || state.form.settle_type, // 结算类型
    postage_items: state.form.postage_items, // 邮费项目
    is_with_tax_rate: state.form.is_with_tax_rate, // 是否含税
    tax_rate: state.form.tax_rate, // 税率
    info_sale_taxable_item_id: state.form.info_sale_taxable_item_id, // 含税项目
    postage_item: state.form.postage_item, // 邮费项目
    shipping_label: state.form.shipping_label, // 出货标签
    sale_mode: AISaleProductOrder.value?.sale_mode || state.form.sale_mode, // 订单类型
    internal_remark: state.form.internal_remark, // 内部备注
    shipping_remark: state.form.shipping_remark, // 出货备注
  }
  fabricList.value.sale_shipment_type_code = AISaleProductOrder.value?.sale_shipment_code
  if (!AISaleProductOrder.value?.list)
    ElMessage.error('没有识别到订单信息')

  const list = AISaleProductOrder.value?.list || []
  list?.forEach((item: Api.GetAISaleProductOrder.AiAiSaleProductOrderDetail) => {
    const temp = {
      ...item,
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      product_id: item.product_id,
      customer_account_num: item.customer_account_num,
      product_code: item.product_code,
      product_name: item.product_name,
      product_color_code: item.product_color_code,
      product_color_name: item.product_color_name,
      product_color_id: item.product_color_id,
      product_color_kind_name: item.product_color_kind_name,
      product_color_kind_id: item.product_color_kind_id,
      dyelot_number: item.dyelot_number,
      product_level_name: item.product_level_name,
      product_level_id: item.product_level_id || 0,
      product_remark: item.product_remark,
      measurement_unit_name: item.measurement_unit_name,
      measurement_unit_id: item.measurement_unit_id,
      stock_roll: item.stock_roll,
      stock_weight: item.stock_weight,
      warehouse_name: item.warehouse_name,
      warehouse_id: item.warehouse_id,
      book_roll: formatRollDiv(item.book_roll),
      purchase_roll: item.purchase_roll,
      purchase_weight: item.purchase_weight,
      purchase_length: item.purchase_length,
      shortage_roll: formatRollDiv(item.shortage_roll),
      shortage_weight: item.shortage_weight,
      shortage_length: item.shortage_length,
      roll: formatRollDiv(item.roll),
      length: formatLengthDiv(item.length),
      weight: formatWeightDiv(item.weight),
      stock_remark: item.remark,
      standard_sale_price: item.standard_sale_price,
      sale_level_name: item.sale_level_name,
      sale_level_id: item.sale_level_id || 0,
      sale_price: formatUnitPriceDiv(item.sale_price),
      offset_sale_price: item.offset_sale_price,
      weight_error: item.weight_error,
      offset_weight_error: item.offset_weight_error,
      length_cut_sale_price: formatUnitPriceDiv(item.length_cut_sale_price),
      offset_length_cut_sale_price: item.offset_length_cut_sale_price,
      settle_weight_error: item.settle_weight_error,
      stock_product_id: item.stock_product_id,
      standard_length_cut_sale_price: item.standard_length_cut_sale_price,
      is_display_price: item.is_display_price,
      adjust_weight_error: item.adjust_weight_error,
      product_kind_id: item?.product_kind_id || 0,
      quantity_price: '-', // 数量结算上次价
      length_price: '-', // 辅助数量结算上次价
      last_settlement: '', // 上次结算方式
    }
    state.tableList.push(conductUnitPrice(temp, true))
  })
  state.form.internal_remark += `${AISaleProductOrder.value?.internal_remark}|`
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #title>
      <div class="flex items-center">
        <div class="mr-2">
          基础信息
        </div>
        <AITextarea v-model="textarea" v-has="'AIOrderButton'" :loading="loading" placeholder="输入或黏贴下单内容" :max-length="500" @submit="handleSubmit" />
      </div>
    </template>
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户编号:" required>
          <template #content>
            <el-form-item prop="customer_code">
              <SelectCustomerDialog ref="customerRef" v-model="state.form.customer_id" :default-value="{ id: state.form.customer_id, code: state.form.customer_code, name: state.form.customer_name }" field="code" show-choice-system @change-value="selectCustomerValueChange" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:" required>
          <template #content>
            <SelectCustomerDialog v-model="state.form.customer_id" field="name" :default-value="{ id: state.form.customer_id, name: state.form.customer_name, code: state.form.customer_code }" show-choice-system @change-value="selectCustomerValueChange" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:" required>
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.form.sale_system_id"
                api="AdminsaleSystemgetSaleSystemDropdownList"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:" required>
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents v-model="state.form.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期:" required>
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="订单日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售群体:">
          <template #content>
            <el-input v-model="state.form.sale_group_name" disabled />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单:">
          <template #content>
            <el-form-item prop="sale_follow_id">
              <SelectComponents v-model="state.form.sale_follow_id" :query="{ duty: EmployeeType.follower }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算类型:">
          <template #content>
            <SelectComponents
              v-model="state.form.seetlements_type"
              api="GetSettleTypeReverseIntMap"
              label-field="name" value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="邮费项目:">
          <template #content>
            <el-form-item prop="postage_item">
              <SelectComponents v-model="state.form.postage_item" api="GetPostageItemsReverseIntMap" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <div style="display: flex; align-items: center;">
          <div style="min-width: 96px;text-align: right;margin-right: 15px;">
            <el-checkbox v-model="state.form.is_with_tax_rate" label="是否含税" />
          </div>
          <vxe-input
            v-model="state.form.tax_rate" placeholder="税率" :min="0" type="float" clearable :disabled="!state.form.is_with_tax_rate" style="width: 170px;"
            :controls="false"
          >
            <template #suffix>
              %
            </template>
          </vxe-input>
        </div>
        <DescriptionsFormItem label="含税项目:">
          <template #content>
            <el-form-item prop="tax_included">
              <SelectComponents v-model="state.form.tax_included" api="GetInfoSaleTaxableItemEnumList" label-field="name" value-field="id" :disabled="!state.form.is_with_tax_rate" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货备注:" copies="2">
          <template #content>
            <el-input v-model="state.form.shipping_remark" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货标签:">
          <template #content>
            <el-form-item prop="print_tag">
              <el-input v-model="state.form.print_tag" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="内部备注:" copies="2">
          <template #content>
            <el-input v-model="state.form.internal_remark" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单要求:">
          <template #content>
            <div class="flex">
              <el-checkbox v-model="state.form.pick_up_goods_in_order" label="齐单提货" :disabled="!state.form.canChangePickUpGoodsInOrder" @click="handlePickUpGoodsInOrderClick" />
              <el-checkbox v-model="state.form.same_color_same_dyelot" label="同色同缸" />
            </div>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:" copies="2">
          <template #content>
            <SelectSaleMode v-model="state.form.sale_mode" />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
    <div class="m-[10px]">
      <AddressCard
        v-if="fabricList.id"
        ref="addressCardRef"
        type="Edit"
        :sale-shipment-type-code="fabricList.sale_shipment_type_code"
        :sale-system-ids="state.form.sale_system_id"
        :customer-ids="state.form.customer_id"
        :customer-name="state.form.customer_name"
        :address-data="addressDataProps"
        @address-changes="handleAddressChange"
        @shipment-changes="handleShipmentChange"
      />
    </div>
  </FildCard>
  <FildCard :title="`成品信息(共${state.tableList.length}条)`" :tool-bar="true" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" :disabled="state.multipleSelection?.length <= 0" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" :disabled="state.form.sale_system_id === ''" @click="handStockAdd">
        从库存中添加
      </el-button>
      <el-button type="primary" :disabled="state.form.customer_code === ''" @click="handAdd">
        从成品欠货单中添加
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #product_code="{ row }">
        {{ formatHashTag(row.product_code, row.product_name) }}
      </template>
      <template #product_color_code="{ row }">
        {{ formatHashTag(row.product_color_code, row.product_color_name) }}
      </template>
      <template #customer_account_num="{ row }">
        <vxe-input v-model="row.customer_account_num" clearable />
      </template>
      <template #roll="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" :controls="false" clearable @change="changeRoll(row)" />
      </template>
      <template #weight="{ row }">
        <vxe-input v-model="row.weight" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #length="{ row }">
        <vxe-input v-model="row.length" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #standard_sale_price="{ row }">
        {{ formatUnitPriceDiv(row.standard_sale_price) }}
        {{ row.measurement_unit_name }}/元
      </template>
      <template #standard_length_cut_sale_price="{ row }">
        {{ formatUnitPriceDiv(row.standard_length_cut_sale_price) }}
        <span v-if="!isMainUnit(row)">{{ row.auxiliary_unit_name }}/元</span>
      </template>
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" type="float" :controls="false" clearable />
      </template>
      <template #book_roll="{ row }">
        <vxe-input v-model="row.book_roll" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #purchase_roll="{ row }">
        <vxe-input v-model="row.purchase_roll" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #purchase_weight="{ row }">
        <vxe-input v-model="row.purchase_weight" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #purchase_length="{ row }">
        <vxe-input v-model="row.purchase_length" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #shortage_roll="{ row }">
        <vxe-input v-model="row.shortage_roll" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #adjust_weight_error="{ row }">
        <vxe-input v-model="row.adjust_weight_error" type="float" :controls="false" clearable @blur="handleWeightError(row)" />
      </template>
      <template #shortage_weight="{ row }">
        <vxe-input v-model="row.shortage_weight" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #shortage_length="{ row }">
        <vxe-input v-model="row.shortage_length" :min="0" type="float" :controls="false" clearable />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" clearable />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text link type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
      <!-- 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        <SelectComponents v-model="row.auxiliary_unit_id" :label-value="row.auxiliary_unit_name" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="(e) => conductUnitPrice(row, false, e)" />
      </template>
      <!-- 数量-销售单价 -->
      <template #sale_price="{ row }">
        <vxe-input v-show="isMainUnit(row)" v-model="row.sale_price" type="float" clearable />
        <span v-show="!isMainUnit(row)">{{ row.sale_price }}</span>
      </template>
      <!--      数量 上次价 -->
      <template #quantity_price="{ row }">
        <div class="flex items-center">
          <span>{{ row.quantity_price !== '' ? row.quantity_price : '-' }}</span>
          <el-icon
            v-if="state.form.customer_id"
            v-has="'ViewHistoricalPrices'"
            :size="22"
            color="#0078fa"
            style="width: 100%;display: flex;justify-content: center;align-items: center;cursor: pointer;"
            @click="handleShowFluctuation(row)"
          >
            <TrendCharts />
          </el-icon>
        </div>
      </template>
      <!--      辅助数量 上次价 -->
      <template #length_price="{ row }">
        <div class="flex items-center">
          <span>{{ row.length_price !== '' ? row.length_price : '-' }}</span>
          <el-icon
            v-if="state.form.customer_id"
            v-has="'ViewHistoricalPrices'"
            :size="22"
            color="#0078fa"
            style="width: 100%;display: flex;justify-content: center;align-items: center;cursor: pointer;"
            @click="handleShowFluctuation(row)"
          >
            <TrendCharts />
          </el-icon>
        </div>
      </template>
      <!-- 辅助数量-销售单价 -->
      <template #length_cut_sale_price="{ row }">
        <vxe-input v-show="!isMainUnit(row)" v-model="row.length_cut_sale_price" type="float" clearable :controls="false" />
        <span v-show="isMainUnit(row)">{{ row.length_cut_sale_price }}</span>
      </template>
    </Table>
  </FildCard>
  <FinishStockDialog ref="FinishStockDialogRef" @handle-sure="handSureStock" />
  <AddOutStockDialog ref="AddOutStockDialogRef" @handle-sure="handSureOutStock" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
  <!-- 价格浮动弹出 -->
  <BlocksModal v-model:model-value="priceFluctuation" title="历史价格" width="1200px" height="700px" @close="onBMclose">
    <template #content>
      <!-- 表单 -->
      <div style="display: flex;flex-wrap: wrap; gap: 15px;margin-bottom: 15px;">
        <!-- 订单日期 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>订单日期:</div>
          <div style="width: 250px;">
            <SelectDate v-model="historicalPriceQuery.arrange_time as any" />
          </div>
        </div>
        <!-- 营销体系 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>营销体系:</div>
          <div style="width: 210px;">
            <SelectComponents
              v-model="historicalPriceQuery.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </div>
        </div>
        <!-- 成品编号 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>成品编号:</div>
          <div style="width: 180px;">
            <SelectProductDialog
              v-model="historicalPriceQuery.product_id"
              field="finish_product_code"
              :default-value="{
                id: historicalPriceQuery.product_id,
                finish_product_name: componentRemoteSearch.finish_product_name,
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @change-value="changeProductSelect"
            />
          </div>
        </div>
        <!-- 成品名称 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>成品名称:</div>
          <div style="width: 180px;">
            <SelectProductDialog
              v-model="historicalPriceQuery.product_id"
              :default-value="{
                id: historicalPriceQuery.product_id,
                finish_product_name: componentRemoteSearch.finish_product_name,
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @change-value="changeProductSelect"
            />
          </div>
        </div>
        <!-- 色号 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>色号:</div>
          <div style="width: 180px;">
            <SelectProductColorDialog
              v-model="historicalPriceQuery.product_color_id"
              field="product_color_code"
              :query="{
                finish_product_id: historicalPriceQuery.product_id,
              }"
              :disabled="!historicalPriceQuery.product_id"
              :default-value="{
                id: historicalPriceQuery.product_color_id,
                product_color_name: componentRemoteSearch.color_name,
                product_color_code: componentRemoteSearch.color_code,
              }"
              @change-value="changeColor"
            />
          </div>
        </div>
        <!-- 颜色 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>颜色:</div>
          <div style="width: 180px;">
            <SelectProductColorDialog
              v-model="historicalPriceQuery.product_color_id"
              :query="{
                finish_product_id: historicalPriceQuery.product_id,
              }"
              :disabled="!historicalPriceQuery.product_id"
              :default-value="{
                id: historicalPriceQuery.product_color_id,
                product_color_name: componentRemoteSearch.color_name,
                product_color_code: componentRemoteSearch.color_code,
              }"
              @change-value="changeColor"
            />
          </div>
        </div>
      </div>
      <GridTable
        ref="TableRef"
        :columns="gridColumnList"
        :el-pagination-config="elPaginationConfig"
        :config="{ loading: hloadding }"
        :data="hData"
        show-pagition
        height="420"
      />
    </template>
  </BlocksModal>
</template>
