<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 28</title>
    <defs>
        <linearGradient x1="30.1048764%" y1="26.3671875%" x2="69.1623717%" y2="67.0079618%" id="linearGradient-1">
            <stop stop-color="#558DE7" offset="0%"></stop>
            <stop stop-color="#0968F4" offset="100%"></stop>
        </linearGradient>
        <path d="M38.6086957,13 C39.3770918,13 40,13.6229082 40,14.3913043 L40,33.6086957 C40,34.3770918 39.3770918,35 38.6086957,35 L9.39130435,35 C8.62290817,35 8,34.3770918 8,33.6086957 L8,14.3913043 C8,13.6229082 8.62290817,13 9.39130435,13 L38.6086957,13 Z M22.2413913,17.8125 L18.4347826,17.8125 L18.9245217,18.3845 L21.9074783,24.0825 L18.8132174,24.0825 L18.5238261,26.1725 L21.8852174,26.1725 L21.3064348,30.2645 L24.690087,30.2645 L25.2688696,26.1725 L28.6525217,26.1725 L28.941913,24.0825 L25.8476522,24.0825 L30.9008696,17.8125 L27.2703919,17.8125 L24.178087,22.0585 L22.2413913,17.8125 Z" id="path-2"></path>
        <filter x="-17.2%" y="-15.9%" width="134.4%" height="150.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.211764706   0 0 0 0 0.494117647   0 0 0 0 0.925490196  0 0 0 0.222321624 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="9.04016821%" y1="31.4514505%" x2="76.4379839%" y2="62.8520674%" id="linearGradient-4">
            <stop stop-color="#F5FBFE" stop-opacity="0.755896935" offset="0%"></stop>
            <stop stop-color="#1C72F1" stop-opacity="0.342189549" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-1346.000000, -503.000000)" id="编组-28">
            <g transform="translate(1346.000000, 503.000000)">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="48" height="48"></rect>
                <g id="形状结合" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-4)" stroke-width="0.5" x="27.75" y="26.75" width="17.5" height="11.5" rx="2"></rect>
                <polygon id="矩形" fill="#FFFFFF" opacity="0.996930804" points="40 34 43 34 42 36 39 36"></polygon>
            </g>
        </g>
    </g>
</svg>