import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取营销体系下拉列表
export function GetSaleSystemDropdownListApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/getSaleSystemDropdownList',
    method: 'get',
  })
}

// 获取营销体系下拉列表无权限
export function GetSaleSystemDropdownListV2() {
  return useRequest({
    url: '/admin/v1/saleSystem/getSaleSystemDropdownListV2',
    method: 'get',
  })
}

// 添加营销体系
export function AddSaleSystemApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/addSaleSystem',
    method: 'post',
  })
}

// 导出数据
export function getSaleSystemDropdownListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/saleSystem/getSaleSystemDropdownList',
    method: 'get',
    nameFile,
  })
}

// 删除营销体系
export function DeleteSaleSystemApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/deleteSaleSystem',
    method: 'delete',
  })
}

// 查看营销体系
export function GetSaleSystemApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/getSaleSystem',
    method: 'get',
  })
}

// 营销体系列表
export function GetSaleSystemListApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/getSaleSystemList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 更新营销体系列表
export function UpdateSaleSystemApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/updateSaleSystem',
    method: 'put',
  })
}

// 获取小数位枚举列表
export function GetRadixPointSignApi() {
  return useRequest({
    url: '/admin/v1/enum/radixPointSign',
    method: 'get',
    // cancelRequest: true, // 开启取消请求
    // cache: true, // 开启请求缓存
    // setExpireTime: 30000, // 缓存时间
  })
}

// 获取默认结算类型枚举列表
export function GetSettleTypeApi() {
  return useRequest({
    url: '/admin/v1/enum/settleType',
    method: 'get',
  })
}

// 获取默认结算周期枚举列表
export function GetSettleCycleApi() {
  return useRequest({
    url: '/admin/v1/enum/settleCycle',
    method: 'get',
  })
}

// 获取信用等级列表
export function CreditLevel() {
  return useRequest({
    url: '/admin/v1/enum/creditLevel',
    method: 'get',
  })
}

// 更新状态
export function UpdateSaleSystemStatusApi() {
  return useRequest({
    url: '/admin/v1/saleSystem/updateSaleSystemStatus',
    method: 'put',
  })
}

// 状态枚举
export function StatusListApi() {
  return useRequest({
    url: '/admin/v1/enum/status',
    method: 'get',
  })
}

// 获取用户下拉列表
export function AdmingetUserDropdownList() {
  return useRequest({
    // url: '/admin/v1/user/getUserDropdownList',
    url: '/admin/v1/employee/duty/list',
    method: 'get',
  })
}

// 员工下拉列表
export function Adminemployeelist() {
  return useRequest({
    url: '/admin/v1/employee/list_enum',
    method: 'get',
  })
}

// 销售区域下拉列表
export function Adminbusiness_unitsale_arealist() {
  return useRequest({
    url: '/admin/v1/business_unit/sale_area/list',
    method: 'get',
  })
}

// 销售群体下拉列表
export function Adminbusiness_unitsale_grouplist() {
  return useRequest({
    url: '/admin/v1/business_unit/sale_group/list',
    method: 'get',
  })
}

// 信用等级下拉列表
export function Adminbusiness_unitcredit_levellist() {
  return useRequest({
    url: '/admin/v1/business_unit/credit_level/list',
    method: 'get',
  })
}

// 所属营销体系
export function AdminsaleSystemgetSaleSystemDropdownList() {
  return useRequest({
    url: '/admin/v1/saleSystem/getSaleSystemDropdownList',
    method: 'get',
  })
}

// 默认结算类型
export function AdminenumsettleType() {
  return useRequest({
    url: '/admin/v1/enum/settleType',
    method: 'get',
  })
}

// 销售员下拉列表
export function AdminusergetSaleUserDropdownList() {
  return useRequest({
    url: '/admin/v1/user/getSaleUserDropdownList',
    method: 'get',
  })
}

// 供应商类型下拉枚举列表
export function AdminuenumsupplierType() {
  return useRequest({
    url: '/admin/v1/basic_data/typeIntercourseUnits/getTypeIntercourseUnitsListEnum',
    method: 'get',
  })
}

// 仓库类型下拉枚举
export function AdminuenumwarehouseType() {
  return useRequest({
    url: '/admin/v1/basic_data/typeWarehouse/getTypeWarehouseEnumList',
    method: 'get',
  })
}

// 计价项目枚举
export function Quoterojectenum() {
  return useRequest({
    url: '/admin/v1/dnf_quote/quote_project_enum',
    method: 'get',
  })
}

// 审核、修改人、创建人下拉
export function GetUserDropdownList() {
  return useRequest({
    url: '/admin/v1/user/getUserDropdownList',
    method: 'get',
  })
}

// 收支类型枚举
export function RecExpType() {
  return useRequest({
    url: '/admin/v1/basic_data/enum/recExpType',
    method: 'get',
  })
}
