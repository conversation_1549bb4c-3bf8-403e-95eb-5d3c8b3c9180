<script setup lang="ts" name="OtherPay">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { Delete, Plus } from '@element-plus/icons-vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import { othercancel, otherlist, otherpass } from '@/api/otherPay'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import { formatDate } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import { processDataOut } from '@/common/handBinary'

const state = reactive<any>({
  filterData: {
    order_no: '',
    src_order_no: '',
    sale_system_id: '',
    voucher_num: '',
    supplier_id: '',
    handler_id: '',
    auditor_id: '',
    updater_id: '',
    status: '',
    create_time: '',
    audit_time: '',
    edit_time: '',
    devierDate: '',
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  success,
  msg,
  handleSizeChange,
  handleCurrentChange,
} = otherlist()

const tableConfig = ref<any>({
  loading,
  showPagition: true,
  showSlotNums: true,
  fieldApiKey: fieldApiKeyList.OtherPayList,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.status.length)
    obj.status = obj.status.join(',')

  const query: any = {
    pay_start_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[0])
        : '',
    pay_end_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[1])
        : '',
    create_start_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    create_end_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    update_start_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[0])
        : '',
    update_end_time:
      state.filterData.edit_time
      && state.filterData.edit_time !== ''
      && state.filterData.edit_time.length
        ? formatDate(state.filterData.edit_time[1])
        : '',
    audit_start_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    audit_end_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
    status: obj.status,
  }
  delete query.audit_time
  delete query.create_time
  delete query.edit_time
  delete query.devierDate
  await ApiCustomerList(getFilterData(query))
  if (!success.value)
    return ElMessage.error(msg.value)

  data.value.list = processDataOut(data.value.list)
}, 400)
onActivated(getData)
onMounted(() => {
  getData()
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = otherpass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: Number(row.id) })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}

// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = othercancel()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: Number(row.id) })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

const router = useRouter()

function handDetail(row: any) {
  router.push({
    name: 'OtherPayDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'OtherPayEdit',
    query: { id: row.id },
  })
}

function handleAdd() {
  router.push({
    name: 'OtherPayAdd',
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    fixed: 'left',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供方名称',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'handler_name',
    title: '经手人',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '应付金额',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'paid_price',
    title: '实付金额',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'unpaid_price',
    title: '未付金额',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weight',
    title: '数量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 100,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    fixed: 'right',
    soltName: 'status',
    showOrder_status: true,
    width: '5%',
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.filterData.voucher_num" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供方名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{ name: componentRemoteSearch.name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称');
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          <!-- <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="应付日期:">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="经手人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.handler_id"
              api="GetEmployeeListEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <!--        <DescriptionsFormItem label="审核人:"> -->
        <!--          <template #content> -->
        <!--            <SelectComponents -->
        <!--              v-model="state.filterData.auditor_id" -->
        <!--              api="GetUserDropdownList" -->
        <!--              label-field="name" -->
        <!--              value-field="id" -->
        <!--              clearable -->
        <!--            /> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <!--        <DescriptionsFormItem label="审核时间:" width="330"> -->
        <!--          <template #content> -->
        <!--            <SelectDate v-model="state.filterData.audit_time" /> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <!--        <DescriptionsFormItem label="最后修改人:"> -->
        <!--          <template #content> -->
        <!--            <SelectComponents -->
        <!--              v-model="state.filterData.updater_id" -->
        <!--              api="GetUserDropdownList" -->
        <!--              label-field="name" -->
        <!--              value-field="id" -->
        <!--              clearable -->
        <!--            /> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <!--        <DescriptionsFormItem label="最后修改时间:" width="330"> -->
        <!--          <template #content> -->
        <!--            <SelectDate v-model="state.filterData.edit_time" /> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" tool-bar>
      <template #right-top>
        <el-button
          v-has="'OtherPayAdd'"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'OtherPayDetail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="'OtherPayEdit'"
              type="primary" :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="'OtherPayPass'"
              type="primary" :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="'OtherPayWait'"
              type="primary" :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>
