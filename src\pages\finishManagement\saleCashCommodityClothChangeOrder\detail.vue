<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import FineSizeAdd from '../components/FineSizeSaleCashCommodityClothAdd.vue'
import {
  getFpmChangeArrangeOrder,
  updateFpmChangeArrangeOrderStatusCancel,
  updateFpmChangeArrangeOrderStatusPass,
  updateFpmChangeArrangeOrderStatusReject,
  updateFpmChangeArrangeOrderStatusWait,
} from '@/api/saleCashCommodityClothChangeOrder'
import { formatDate, sumNum } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { processDataOut } from '@/common/handBinary'

const form_options = [
  {
    text: '出货类型',
    key: 'after_out_order_type_name',
  },
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '配布日期',
    key: 'arrange_time',
  },
  {
    text: '加工厂名称',
    key: 'process_factory_name',
  },
  {
    text: '收货标签',
    key: 'receive_tag',
  },
  {
    text: '原往来单位名称',
    key: 'before_biz_unit_name',
  },
  {
    text: '变更往来单位名称',
    key: 'after_biz_unit_name',
  },
  {
    text: '收货地址',
    key: 'receive_addr',
  },
  {
    text: '收货电话',
    key: 'receive_phone',
  },
  {
    text: '原仓库名称',
    key: 'before_arrange_to_warehouse_name',
  },
  {
    text: '变更仓库名称',
    key: 'after_arrange_to_warehouse_name',
  },
  {
    text: '销售员',
    key: 'sale_user_name',
  },
  {
    text: '销售跟单员',
    key: 'sale_follower_name',
  },
  {
    text: '司机名称',
    key: 'driver_name',
  },
  {
    text: '物流公司',
    key: 'logistics_company_name',
  },
  {
    text: '内部备注',
    key: 'internal_remark',
  },
  {
    text: '销售备注',
    key: 'sale_remark',
  },
]
const route = useRoute()

const state = reactive<any>({
  baseData: {
    order_no: '',
    audit_status_name: '',
    audit_status: 1,
    arrange_order_no: '',
  },
})
const { fetchData, data: detailData } = getFpmChangeArrangeOrder()
onMounted(() => {
  getData()
})

const setColorObjData: any = {}
// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'SaleCashCommodityClothChangeOrderDetail',
    showSlotNums: false,
    showSpanHeader: true,
    filterStatus: false,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    footerCellClassName: (row: any) => finishProductionOptions.footerCellClassName(row),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['arrange_weight'].includes(column.field))
          return sumNum(data, 'arrange_weight', '', 'float')

        if (['arrange_length'].includes(column.field))
          return sumNum(data, 'arrange_length', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['change_roll'].includes(column.field))
          return sumNum(data, 'change_roll', '', 'float')

        if (['change_roll'].includes(column.field))
          return sumNum(data, 'change_roll', '', 'float')

        if (['change_weight'].includes(column.field))
          return sumNum(data, 'change_weight', '', 'float')

        if (['change_length'].includes(column.field))
          return sumNum(data, 'change_length', '', 'float')

        if (['result_roll'].includes(column.field)) {
          let result = 0
          let sub = 0
          data.forEach((item: any) => {
            result += item.result_roll
            sub += Number(item.change_roll)
          })
          setColorObjData.change_roll = sub
          if (sub === 0)
            return result

          return `${result}(${sub})`
        }
        if (['result_length'].includes(column.field)) {
          let result = 0
          let sub = 0
          data.forEach((item: any) => {
            result += item.result_length
            sub += Number(item.change_length)
          })
          setColorObjData.change_length = sub
          if (sub === 0)
            return result

          return `${result}(${sub})`
        }
        if (['result_weight'].includes(column.field)) {
          let result = 0
          let sub = 0
          data.forEach((item: any) => {
            result += item.result_weight
            sub += Number(item.change_weight)
          })
          setColorObjData.change_weight = sub
          if (sub === 0)
            return result

          return `${result}(${sub})`
        }
        return null
      }),
    ]
  },
  footerCellClassName: (row: any) => {
    if (row.columnIndex === 0 && row.$rowIndex === 0 && row.column.title === '变更匹数') {
      if (setColorObjData.change_roll < 0)
        return 'col-green'
      else if (setColorObjData.change_roll > 0)
        return 'col-red'

      return ''
    }
    if (row.columnIndex === 0 && row.$rowIndex === 0 && row.column.title === '最终匹数') {
      if (setColorObjData.change_roll < 0)
        return 'col-green'
      else if (setColorObjData.change_roll > 0)
        return 'col-red'

      return ''
    }
    if (row.columnIndex === 1 && row.$rowIndex === 0 && row.column.title === '变更数量') {
      if (setColorObjData.change_weight < 0)
        return 'col-green'
      else if (setColorObjData.change_weight > 0)
        return 'col-red'

      return ''
    }
    if (row.columnIndex === 1 && row.$rowIndex === 0 && row.column.title === '最终数量') {
      if (setColorObjData.change_weight < 0)
        return 'col-green'
      else if (setColorObjData.change_weight > 0)
        return 'col-red'

      return ''
    }
    if (row.columnIndex === 2 && row.$rowIndex === 0 && row.column.title === '变更辅助数量') {
      if (setColorObjData.change_length < 0)
        return 'col-green'
      else if (setColorObjData.change_length > 0)
        return 'col-red'

      return ''
    }
    if (row.columnIndex === 2 && row.$rowIndex === 0 && row.column.title === '最终辅助数量') {
      if (setColorObjData.change_length < 0)
        return 'col-green'
      else if (setColorObjData.change_length > 0)
        return 'col-red'

      return ''
    }
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'arrange_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'C',
      title: '出仓数量信息',
      childrenList: [
        {
          field: 'arrange_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'D',
      title: '出仓辅助数量信息',
      childrenList: [
        {
          field: 'arrange_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'E',
      title: '单据备注信息',
      childrenList: [
        {
          field: 'arrange_item_remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'F',
      title: '变更信息',
      fixed: 'right',
      childrenList: [
        {
          field: 'change_roll',
          soltName: 'change_roll',
          title: '变更匹数',
          minWidth: 100,
        },
        {
          field: 'change_weight',
          soltName: 'change_weight',
          title: '变更数量',
          minWidth: 100,
        },
        {
          field: 'change_length',
          soltName: 'change_length',
          title: '变更辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'G',
      title: '最终数量',
      fixed: 'right',
      childrenList: [
        {
          field: 'result_roll',
          soltName: 'result_roll',
          title: '最终匹数',
          minWidth: 100,
        },
        {
          field: 'result_weight',
          soltName: 'result_weight',
          title: '最终数量',
          minWidth: 100,
        },
        {
          field: 'result_length',
          soltName: 'result_length',
          title: '最终辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'H',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'I',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          minWidth: 100,
        },
      ],
    },
  ],
})

async function getData() {
  await fetchData({ id: route.query.id })
  state.baseData = {
    ...detailData.value,
    arrange_time: formatDate(detailData.value.arrange_time),
  }
  finishProductionOptions.datalist = detailData.value?.item_data?.map((item: any) => {
    return processDataOut(item)
  })
}

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateFpmChangeArrangeOrderStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateFpmChangeArrangeOrderStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateFpmChangeArrangeOrderStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateFpmChangeArrangeOrderStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  const fn = (_data: any) => {
    // if (audit_status === 2) {
    //   // 审核通过自动生产进仓单
    //   router.push({
    //     name: 'FpInteriorAllotWarehouseEntryOrderEdit',
    //     query: {
    //       id: data.create_id,
    //     },
    //   })
    // }
  }
  await orderStatusConfirmBox({ id, audit_status, message, api, fn })

  getData()
}
const FineSizeAddRef = ref()
function showDialog(row: any) {
  FineSizeAddRef.value.state.modalName = '细码查看'
  FineSizeAddRef.value.state.isEdit = false
  FineSizeAddRef.value.showDialog(row)
}
</script>

<template>
  <StatusColumn
    :order_no="state.baseData.order_no"
    :order_id="state.baseData.id"
    :cloth_order_no="state.baseData.arrange_order_no"
    :status="state.baseData.audit_status"
    :status_name="state.baseData.audit_status_name"
    permission_print_key=""
    permission_wait_key="SaleCashCommodityClothChangeOrder_wait"
    permission_reject_key="SaleCashCommodityClothChangeOrder_reject"
    permission_pass_key="SaleCashCommodityClothChangeOrder_pass"
    permission_cancel_key="SaleCashCommodityClothChangeOrder_cancel"
    permission_edit_key="SaleCashCommodityClothChangeOrder_edit"
    edit_router_name="SaleCashCommodityClothChangeOrderEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :copies="item.copies || 1" :label="`${item.text}:`">
        <template #content>
          {{ state.baseData[item.key] }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="变更信息" class="mt-[5px]">
    <Table :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
      <!-- 变更匹数 -->
      <template #change_roll="{ row }">
        <span v-if="row.change_roll !== 0" class="green">
          {{ row.change_roll }}
        </span>
        <span v-else>{{ row.change_roll }}</span>
      </template>
      <!-- 变更数量 -->
      <template #change_weight="{ row }">
        <span v-if="row.change_weight !== 0" class="green">
          {{ row.change_weight }}
        </span>
        <span v-else>{{ row.change_weight }}</span>
      </template>
      <!-- 变更辅助数量 -->
      <template #change_length="{ row }">
        <span v-if="row.change_length !== 0" class="green">
          {{ row.change_length }}
        </span>
        <span v-else>{{ row.change_length }}</span>
      </template>
      <!-- 最终匹数 -->
      <template #result_roll="{ row }">
        <span v-if="row.change_roll !== 0" class="green">{{ row.result_roll }}({{ row.change_roll }})</span>
        <span v-else>{{ row.result_roll }}</span>
      </template>
      <!-- 最终数量 -->
      <template #result_weight="{ row }">
        <span v-if="row.change_weight !== 0" class="green">{{ row.result_weight }}({{ row.change_weight }})</span>
        <span v-else>{{ row.result_weight }}</span>
      </template>
      <!-- 最终辅助数量 -->
      <template #result_length="{ row }">
        <span v-if="row.change_length !== 0" class="green">{{ row.result_length }}({{ row.change_length }})</span>
        <span v-else>{{ row.result_length }}</span>
      </template>
      <template #xima="{ row }">
        <el-link @click="showDialog(row)">
          查看
        </el-link>
      </template>
    </Table>
  </FildCard>
  <FineSizeAdd ref="FineSizeAddRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
    }
  }
}

.el-link {
  color: #0e7eff;
}

::v-deep(.green) {
  color: green;
}

::v-deep(.col-red) {
  color: red;
}

::v-deep(.col-green) {
  color: green;
}
</style>
