<script lang="ts" setup name="ProductSalePlanAdd">
import { Decimal } from 'decimal.js'
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { omit } from 'lodash-es'
import AddInformatinDialog from '../components/AddInformatinDialog.vue'
import AddOutStockDialog from '../components/AddOutStockDialog.vue'
import ProductColorDialog from '../components/productColorDialog.vue'
import { colorSelectConfig, orderDate, productSelectConfig } from './common'
import { addSaleProductPlanOrder } from '@/api/productSalePlan'
import { DictionaryType, EmployeeType } from '@/common/enum'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import {
  formatDate,
  formatHashTag,
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import {
  deepClone,
  getFilterData,
} from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'
import SelectProductColorDialog from '@/components/SelectProductColorDialog/index.vue'
import SelectSettleTypeDialog from '@/components/SelectSettleTypeDialog/index.vue'
import AddressCard from '@/components/AddressCard/index.vue'

const routerList = useRouterList()
const addressCardRef = ref() // 获取地址卡引用

// function checkPhone(rule: any, value: any, callback: any) {
//   const reg = /^(?:(?:\+|00)86)?1\d{10}$/
//   if (reg.test(value))
//     callback()
//   else
//     callback(new Error('请输入正确的手机号码'))
// }

const state = reactive<any>({
  form: {
    sale_system_id: '', // 营销体系
    voucher_number: '', // 合同编号
    customer_code: '', // 客户编号
    customer_name: '', // 客户名称
    sale_user_id: null, // 销售员
    date: '', // 订单日期
    // receipt_address: '', // 交货地址
    delivery_date: '', // 交货日期
    bargain_money: '', // 定金
    // customer_phone: '', // 客户电话
    remark: '',
    sale_tax_rate: '',
    is_with_tax_rate: true,
    settle_method_id: '', // 结算方式
    // delivery_type_id: '', // 发货方式
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    date: [{ required: true, message: '请选择订单日期', trigger: 'blur' }],
    delivery_date: [{ required: true, message: '请选择交货日期', trigger: 'blur' }],
    sale_user_id: [{ required: true, message: '请选择销售员', trigger: 'blur' }],
    customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
    // customer_phone: [{ trigger: 'blur', validator: checkPhone }],
  },
  fileList: [],
  fabricList: [],
  multipleSelection: [],
})

const tableRef = ref()
const tableRefOne = ref()
const TableList = ref<any>([])
const tableConfig = ref({
  fieldApiKey: 'ProductSalePlanAdd',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '150',
  filterStatus: false,
  maxHeight: '500px',
  // rowKey: 'product_id',
  scrollY: {
    enabled: false,
  },
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const tableConfig_fabric = ref({
  fieldApiKey: 'ProductSalePlanAdd_b',
  showSlotNums: true,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethodOne(val),
})

onMounted(() => {
  state.form.date = orderDate.date
  state.form.delivery_date = orderDate.delivery_date
  handAddTableList()
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')} kg`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      return null
    }),
  ]
}

function FooterMethodOne({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')}`

      return null
    }),
  ]
}

const ruleFormRef = ref()

// const router = useRouter()

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handDelete(index: number) {
  TableList.value.splice(index, 1)
  const filterArr = state.fabricList.filter((item: any) => {
    return TableList.value.some(
      (it: any) => it.grey_fabric_id === item.grey_fabric_id,
    )
  })
  state.fabricList = filterArr
  setData()
}

// 新增一条数据
function handAddTableList() {
  addTableRowFun({
    product_id: '',
    grey_fabric_id: 0,
    product_color_id: '',
    roll: '',
    weight: '',
    weight_error: '',
    unit_price: '',
    other_price: '',
    total_price: '',
    canChangeProduct: true, // 是否可以修改产品
  }, false)
}

/**
 * 新增表格数据：如果第一条为空数据则替换第一条
 * @param item 新增的列数据
 * @param isDealFirst 是否处理第一条空数据
 */
function addTableRowFun(item: any, isDealFirst = true) {
  if (TableList.value.length && !TableList.value?.[0]?.product_id && isDealFirst) {
    TableList.value[0] = item
    tableRef.value.tableRef?.loadData(TableList.value)
  }
  else {
    TableList.value.push(item)
  }
}

function handCopy(index: number) {
  const copyData = deepClone(TableList.value[index])
  copyData.id = 0
  delete copyData._X_ROW_KEY_TABLE
  addTableRowFun(copyData)
  //

  // // 手动同步颜色信息
  // if (colorInfo.product_color_id) {
  //   handChangeColorNo(colorInfo, TableList.value[index + 1])
  // }
  setData()
}

watch(
  () => TableList.value,
  () => {
    if (TableList.value?.length > 0) {
      // const sale_tax_rate = checkbox.value ? formatPriceDiv(state.form.sale_tax_rate) : 0
      TableList.value.map((item: any) => {
        // const timesRoll = new Decimal(Number(item.roll))
        // const times_weigt_error = timesRoll.times(Number(item.weight_error))

        // const plusValue = new Decimal(Number(item.weight))

        // const leftVal = plusValue.minus(times_weigt_error)
        //
        // const centerVal = leftVal.times(Number(item.unit_price))
        //
        // // const tax_rite = new Decimal(1 + sale_tax_rate)
        // const tax_rite = new Decimal(1)
        //
        // const leftRes = centerVal.times(tax_rite)
        //
        // const res = leftRes.plus(Number(item.other_price))

        // 计算 (数量 - 匹数 × 空差)
        const leftVal = new Decimal(Number(item.weight)).minus(
          new Decimal(Number(item.roll)).times(Number(item.weight_error)),
        )

        // 计算 (数量 - 匹数 × 空差) * 单价
        const centerVal = leftVal.times(Number(item.unit_price))

        // 计算 (数量 - 匹数 × 空差) * 单价 + 其他金额
        const res = centerVal.plus(Number(item.other_price))

        item.total_price = res.toFixed(2)

        return item
      })
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
        tableRefOne.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const AddInformatinDialogRef = ref()

function handStockAdd() {
  AddInformatinDialogRef.value.state.showModal = true
}

// 选择成品等级
function handLevel(val: any, rowIndex: number) {
  TableList.value.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.product_level_name = val.name
      item.product_level_id = val.id
    }
    return item
  })
}

// 从资料中添加
async function handAddProduct(list: any) {
  list.forEach((item: any) => {
    addTableRowFun({
      ...item,
      product_id: item.id,
      product_code: item.finish_product_code,
      product_name: item.finish_product_name,
      customer_account_num: '',
      grey_fabric_name: item.grey_fabric_name,
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_id: item.grey_fabric_id,
      measurement_unit_name: item.measurement_unit_name,
      measurement_unit_id: item.measurement_unit_id,
      roll: '',
      weight: '',
      remark: '',
      finish_product_width: item.finish_product_width,
      finish_product_gram_weight: item.finish_product_gram_weight,
      paper_tube_weight: formatWeightDiv(item.paper_tube_weight),
      weight_error: formatWeightDiv(item.weight_error),
      product_level_id: item?.finish_product_level_id || '',
      other_price: '',
      lower_limit: '',
      upper_limit: '',
      unit_price: '',
      total_price: 0,
      canChangeProduct: true,
    })
  })
  state.multipleSelection = [...state.multipleSelection, ...list]
  AddInformatinDialogRef.value.state.showModal = false
  // TODO:将坯布信息汇总
  setData()
}
const AddOutStockDialogRef = ref()

function handAdd() {
  AddOutStockDialogRef.value.state.showModal = true
  AddOutStockDialogRef.value.state.sale_system_id = state.form.sale_system_id
  //   AddOutStockDialogRef.value.state.customer_id = state.form.customer_id
}

// 从成品欠货单新增
function handSureOutStock(list: any) {
  list.forEach((item: any) => {
    addTableRowFun({
      ...item,
      product_id: item.product_id,
      measurement_unit_name: item.measurement_unit_name,
      measurement_unit_id: item.measurement_unit_id,
      product_code: item.product_code,
      product_name: item.product_name,
      grey_fabric_name: item.grey_fabric_name,
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_id: item.grey_fabric_id,
      roll: formatPriceDiv(item.shortage_roll),
      weight: formatWeightDiv(item.shortage_weight),
      remark: '',
      finish_product_width: item?.finish_product_width,
      finish_product_gram_weight: item?.finish_product_gram_weight,
      customer_account_num: item.customer_account_num,
      product_color_name: item.product_color_name,
      product_color_kind_name: item.product_color_kind_name,
      product_color_kind_id: item.product_color_kind_id,
      product_color_id: item.product_color_id,
      paper_tube_weight: '',
      weight_error: 0,
      product_level_id: item?.product_level_id || '',
      other_price: '',
      lower_limit: '',
      upper_limit: '',
      unit_price: '',
      total_price: 0,
    })
  })
  AddOutStockDialogRef.value.state.showModal = false
  // TODO:将坯布信息汇总
  setData()
}

// 数据去重
function uniqueArr(arr: any) {
  const map = new Map()
  return arr.filter((item: any) => {
    if (map.has(item.grey_fabric_id)) {
      return false
    }
    else {
      map.set(item.grey_fabric_id, true)
      return true
    }
  })
}

// 合并数据源 ZS03D255-01
function setData() {
  const arr = uniqueArr(TableList.value)

  // TODO:每次添加都需要初始化这个数据
  state.fabricList = []

  arr.forEach((item: any) => {
    state.fabricList.push({
      grey_fabric_id: item.grey_fabric_id,
      grey_fabric_name: item.grey_fabric_name,
      grey_fabric_code: item.grey_fabric_code,
      roll: 0,
      weight: 0,
    })
  })

  const newFabricList = state.fabricList.map((item: any) => {
    const sumRoll = TableList.value.reduce((acc: any, it: any) => {
      if (it.grey_fabric_id === item.grey_fabric_id)
        return acc + Number(it.roll)

      return acc
    }, 0)

    const sumWeight = TableList.value.reduce((acc: any, it: any) => {
      if (it.grey_fabric_id === item.grey_fabric_id)
        return acc + Number(it.weight)

      return acc
    }, 0)

    return {
      ...item,
      roll: sumRoll,
      weight: sumWeight,
    }
  })
  state.fabricList = newFabricList
}

// 修改也汇总当前坯布ID值一样的
function handChangeRoll() {
  setData()
}

function handChangeWeight() {
  setData()
}

const {
  fetchData: addPost,
  data: addData,
  success: addSuccess,
  msg: addMsg,
} = addSaleProductPlanOrder()

async function handleSure() {
  const { addressData, deliveryData } = await addressCardRef.value?.getFormData()
  if (!deliveryData.id)
    return ElMessage.error('发货类型是必选项')
  if (!TableList.value.length)
    return ElMessage.error('至少添加一条成品信息')

  const list = deepClone(TableList.value)

  for (let i = 0; i < list.length; i++) {
    if (list[i].product_color_id === '' || !list[i].product_color_id)
      return ElMessage.error('请选择色号')

    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (list[i].weight === '')
      return ElMessage.error('数量不可为空')

    if (list[i].unit_price === '')
      return ElMessage.error('单价不可为空')

    list[i].weight_error !== ''
    && (list[i].weight_error = formatWeightMul(list[i].weight_error))

    list[i].roll !== '' && (list[i].roll = formatPriceMul(list[i].roll))
    list[i].weight !== '' && (list[i].weight = formatWeightMul(list[i].weight))
    list[i].paper_tube_weight !== ''
    && (list[i].paper_tube_weight = formatWeightMul(list[i].paper_tube_weight))
    list[i].other_price !== ''
    && (list[i].other_price = formatPriceMul(list[i].other_price))
    list[i].upper_limit !== ''
    && (list[i].upper_limit = formatPriceMul(list[i].upper_limit))
    list[i].lower_limit !== ''
    && (list[i].lower_limit = formatPriceMul(list[i].lower_limit))
    list[i].unit_price !== ''
    && (list[i].unit_price = formatUnitPriceMul(list[i].unit_price))
    list[i].total_price = formatPriceMul(list[i].total_price)
    list[i].product_color_kind_id = list[i].type_finished_product_kind_id
    list[i].product_kind_id = list[i].type_grey_fabric_id
    list[i] = getFilterData(list[i])
  }

  const fabricArr = deepClone(state.fabricList)

  for (let i = 0; i < fabricArr.length; i++) {
    if (fabricArr[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (fabricArr[i].weight === '')
      return ElMessage.error('数量不可为空')

    fabricArr[i].roll = formatPriceMul(fabricArr[i].roll)
    fabricArr[i].weight = formatWeightMul(fabricArr[i].weight)
  }

  const query = {
    customer_id: state.form.customer_id,
    // customer_phone: state.form.customer_phone,
    deposit: state.form.bargain_money,
    order_time: formatDate(state.form.date),
    // receipt_address: state.form.receipt_address,
    receipt_time: formatDate(state.form.delivery_date),
    sale_user_id: state.form.sale_user_id,
    sale_system_id: state.form.sale_system_id,
    voucher_number: state.form.voucher_number,
    internal_remark: state.form.remark,
    item_data: list,
    gf_data: fabricArr,
    sale_tax_rate: formatPriceMul(state.form.sale_tax_rate),
    is_with_tax_rate: state.form.is_with_tax_rate,
    plan_type: 1,
    settle_method_id: Number(state.form.settle_method_id) || 0,
    // delivery_type_id: Number(state.form.delivery_type_id) || 0,
    texture_url: state.fileList ? state.fileList.join(',') : '',
    // 发货类型
    sale_shipment_id: deliveryData.id, // 发货类型id
    code: deliveryData.code, // 发货类型编号
    sale_shipment_name: deliveryData.name, // 发货类型名称
    send_product_type: deliveryData.out_order_type, // 出仓类型
    warehouse_id: deliveryData.ware_house_in_id, // 调至仓库id
    // 以下是地址卡的收货方式数据 addressInfos
    location: addressData.location.join(' '), // 收货地址(省市区)
    receipt_address: addressData.address, // 收货地址(详情地址)
    contact_name: addressData.contact_name, // 联系人
    customer_phone: addressData.phone, // 联系电话
    logistics_area: addressData.logistics_area, // 物流区域
    is_default: addressData.is_default, // 是否默认地址
    logistics_company: addressData.logistics_company, // 物流公司名称
    process_factory: addressData.name, // 加工厂昵称
    print_tag: state.form.print_tag || addressData.print_tag, // 出货标签
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({
          name: 'ProductSalePlanDetail',
          query: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const productColorDialogRef = ref()
// 选择客户
async function customerChange(val: any) {
  state.form.customer_name = val?.name
  state.form.customer_code = val?.code
  // state.form.customer_phone = val.phone
  state.form.sale_user_id = !val.seller_id ? null : val.seller_id
  // state.form.receipt_address = val.address
  state.form.sale_system_id = ''

  state.form.sale_system_id = val?.select_sale_system_id || ''
}
// 选择成品
function changeProductSelect(val: any, row: any) {
  // 带出相应的信息
  Object.assign(row, {
    ...omit(val, ['id', '_X_ROW_KEY_TABLE']),
    product_code: val.finish_product_code || '',
    product_name: val.finish_product_name || '',
    paper_tube_weight: formatWeightDiv(val.paper_tube_weight) || '',
    weight_error: formatWeightDiv(val.weight_error) || '',
    product_level_id: val?.finish_product_level_id || '',
    customer_account_num: '',
    product_color_id: '',
    product_color_code: '',
    product_color_name: '',
    roll: '',
    weight: '',
    remark: '',
    other_price: '',
    lower_limit: '',
    upper_limit: '',
    unit_price: '',
    total_price: 0,
    measurement_unit_id: val?.measurement_unit_id || '',
    measurement_unit_name: val?.measurement_unit_name || '',
    grey_fabric_id: val?.grey_fabric_id || '',
    grey_fabric_code: val?.grey_fabric_code || '',
    grey_fabric_name: val?.grey_fabric_name || '',
    type_finished_product_kind_name: '',
    finish_product_width: val.finish_product_width,
    finish_product_width_unit_id: val.finish_product_width_unit_id || '',
    finish_product_gram_weight: val.finish_product_gram_weight,
    finish_product_gram_weight_unit_id: val.finish_product_gram_weight_unit_id || '',
  })
  setData()
}
// function focus(row: any, rowIndex: number) {
//   productColorDialogRef.value.state.showModal = true
//   productColorDialogRef.value.state.productId = row.product_id
//
//   productColorDialogRef.value.state.rowIndex = rowIndex
// }

function handSelectColor(list: any, rowIndex: number) {
  TableList.value.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.product_color_id = list[0]?.id
      item.product_color_name = list[0]?.product_color_name
      item.product_color_kind_name = list[0]?.type_finished_product_kind_name
      item.product_color_kind_id = list[0]?.type_finished_product_kind_id
    }
    return item
  })
  productColorDialogRef.value.state.showModal = false
}

function handChangeColorNo(val: any, row: any) {
  if (val == null) {
    row.product_color_id = ''
    row.product_color_name = ''
    row.product_color_kind_name = ''
    row.product_color_kind_id = ''

    row.grey_fabric_code = ''
    row.grey_fabric_id = ''
    row.grey_fabric_name = ''
    row.type_finished_product_kind_name = ''
    row.type_finished_product_kind_id = ''
  }
  else {
    // row.product_color_id = val.product_color_id
    row.product_color_name = val.product_color_name
    row.product_color_code = val.product_color_code
    row.type_finished_product_kind_name = val.type_finished_product_kind_name
    row.type_finished_product_kind_id = val.type_finished_product_kind_id

    row.grey_fabric_code = val.grey_fabric_code
    row.grey_fabric_id = val.grey_fabric_id
    row.grey_fabric_name = val.grey_fabric_name
  }
}

const bulkSetting = ref<any>({})

const bulkShow = ref(false)

function bulkHand() {
  const checkList = TableList.value.filter((item: any) => item.selected)
  if (checkList?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value, quickInputResult }: any) {
  const checkList = TableList.value.filter((item: any) => item.selected)
  if (checkList?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  TableList.value?.map((item: any, index: any) => {
    if (item?.selected) {
      if (row.quickInput && quickInputResult?.[index]) {
        item[row.field] = quickInputResult[index]
        return item
      }
      item[row.field] = value[row.field]
      return item
    }
  })

  if (row.field === 'roll' || row.field === 'weight')
    setData()

  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'customer_account_num',
    title: '款号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    component: 'input',
    type: 'text',
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    component: 'input',
    type: 'text',
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒',
    component: 'input',
    type: 'float',
  },
  {
    field: 'weight_error',
    title: '空差',
    component: 'input',
    type: 'float',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'weight',
    title: '数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'upper_limit',
    title: '上限',
    component: 'input',
    type: 'float',
  },
  {
    field: 'lower_limit',
    title: '下限',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

const columnList = ref([
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 200,
    soltName: 'product_code',
    required: true,
    fixed: 'left',
  },
  // {
  //   field: 'product_name',
  //   title: '成品名称',
  //   minWidth: 120,
  //   soltName: 'product_name',
  //   fixed: 'left',
  // },
  {
    field: 'product_color_id',
    title: '色号颜色',
    minWidth: 200,
    soltName: 'product_color_id',
    required: true,
    fixed: 'left',
  },
  // {
  //   field: 'product_color_name',
  //   title: '颜色',
  //   minWidth: 120,
  //   soltName: 'product_color_name',
  //   required: true,
  //   fixed: 'left',
  // },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    soltName: 'roll',
    required: true,
    fixed: 'left',
  },
  {
    field: 'unit_price',
    title: '单价',
    minWidth: 100,
    soltName: 'unit_price',
    required: true,
    fixed: 'left',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    soltName: 'weight',
    required: true,
    fixed: 'left',
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 75,
  },
  {
    field: 'upper_limit',
    title: '上限',
    minWidth: 100,
    soltName: 'upper_limit',
  },
  {
    field: 'lower_limit',
    title: '下限',
    minWidth: 100,
    soltName: 'lower_limit',
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'product_level_id',
    title: '成品等级',
    minWidth: 140,
    soltName: 'product_level_id',
  },
  {
    field: 'type_finished_product_kind_name',
    title: '颜色类别',
    minWidth: 100,
  },
  {
    field: 'customer_account_num',
    title: '款号',
    minWidth: 100,
    soltName: 'customer_account_num',
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 180,
    soltName: 'finish_product_width',
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 180,
    soltName: 'finish_product_gram_weight',
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒',
    minWidth: 100,
    soltName: 'paper_tube_weight',
  },
  {
    field: 'weight_error',
    title: '空差',
    minWidth: 100,
    soltName: 'weight_error',
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    soltName: 'other_price',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 70,
    fixed: 'right',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
    fixed: 'right',
  },
])

const columnList_fabric = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 150,
  },
  {
    field: 'roll',
    title: '用坯匹数',
    minWidth: 150,
    soltName: 'roll',
  },
  {
    field: 'weight',
    title: '用坯数量',
    minWidth: 150,
    soltName: 'weight',
  },
])

const { 聚焦下一行 } = useTableEnterAutoFocus(
  tableRef,
  TableList,
)
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <!-- <el-descriptions :column="4" border size="small"> -->
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户名称:" required>
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                is-merge
                field="name"
                :default-value="{
                  id: state.form.customer_id,
                  name: state.form.customer_name,
                  code: state.form.customer_code,
                }"
                show-choice-system
                @change-value="customerChange"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:" required>
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.form.sale_system_id"
                api="AdminsaleSystemgetSaleSystemDropdownList"
                label-field="name"
                value-field="id"
                clearable
                placeholder="请选择"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单日期:" required>
          <template #content>
            <el-form-item prop="date">
              <el-date-picker
                v-model="state.form.date"
                type="date"
                placeholder="订单日期"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="交货日期:" required>
          <template #content>
            <el-form-item prop="delivery_date">
              <el-date-picker
                v-model="state.form.delivery_date"
                type="date"
                placeholder="交货日期"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:" required>
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents
                v-model="state.form.sale_user_id"
                :query="{ duty: EmployeeType.salesman }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="合同编号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" clearable placeholder="请输入" />
          </template>
        </DescriptionsFormItem>

        <!-- <DescriptionsFormItem label="客户电话:">
          <template #content>
            <el-input
              v-model="state.form.customer_phone"
              type="text"
            />
          </template>
        </DescriptionsFormItem> -->

        <DescriptionsFormItem label="合同定金:">
          <template #content>
            <el-input v-model="state.form.bargain_money" clearable placeholder="请输入" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="结算方式:">
          <template #content>
            <SelectSettleTypeDialog
              v-model="state.form.settle_method_id"
              field="name"
            />
            <!--            <SelectComponents -->
            <!--              v-model="state.form.settle_method_id" -->
            <!--              style="width: 100%" -->
            <!--              api="GetInfoSaleSettlementMethodEnumList" -->
            <!--              label-field="name" -->
            <!--              value-field="id" -->
            <!--              clearable -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>

        <!-- <DescriptionsFormItem label="发货方式">
          <template #content>
            <SelectComponents
              v-model="state.form.delivery_type_id"
              style="width: 100%"
              api="GetEmployeeDutyListApi"
              :query="{ dictionary_id: DictionaryType.deliveryType }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem> -->
        <div style="display: flex; align-items: center">
          <div style="min-width: 96px; text-align: right; margin-right: 15px">
            <el-checkbox
              v-model="state.form.is_with_tax_rate"
              label="是否含税"
            />
          </div>
          <vxe-input
            v-model="state.form.sale_tax_rate"
            placeholder="税率"
            :min="0"
            type="float"
            :controls="false"
            clearable
            :disabled="!state.form.is_with_tax_rate"
            style="width: 170px"
          >
            <template #suffix>
              %
            </template>
          </vxe-input>
        </div>

        <!-- <DescriptionsFormItem label="交货地址:">
          <template #content>
            <el-input v-model="state.form.receipt_address" clearable />
          </template>
        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="单据备注:">
          <template #content>
            <el-input
              v-model="state.form.remark"
              clearable
              :maxlength="200"
              placeholder="请输入"
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
    <!-- 地址卡片 -->
    <div class="m-[10px]">
      <AddressCard
        ref="addressCardRef"
        type="Add"
        :sale-system-ids="state.form.sale_system_id"
        :customer-ids="state.form.customer_id"
        :customer-name="state.form.customer_name"
      />
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <template #right-top>
      <el-button @click="bulkHand">
        批量操作
      </el-button>
      <el-button
        type="primary"
        :disabled="state.form.sale_system_id === ''"
        @click="handStockAdd"
      >
        从资料中添加
      </el-button>
      <el-button
        type="primary"
        :disabled="state.form.sale_system_id === ''"
        @click="handAdd"
      >
        从成品欠货单中添加
      </el-button>
    </template>
    <Table
      ref="tableRef"
      :config="tableConfig"
      :table-list="TableList"
      :column-list="columnList"
    >
      <template #customer_account_num="{ row, rowIndex }">
        <vxe-input :id="`customer_account_num-${rowIndex}`" v-model="row.customer_account_num" clearable @keydown="聚焦下一行(rowIndex, $event, 'customer_account_num')" />
      </template>
      <template #product_code="{ row }">
        <SelectMergeComponent
          v-if="row.canChangeProduct"
          v-model="row.product_id"
          :custom-label="(e:any) => `${formatHashTag(e.finish_product_code, e.finish_product_name)}`"
          :default-value="formatHashTag(row.product_code, row.product_name)"
          :multiple="false"
          api-name="GetFinishProductDropdownList"
          remote
          remote-key="finish_product_code_or_name"
          remote-show-suffix
          placeholder="成品编号、成品名称"
          value-field="id"
          @change="(val) => changeProductSelect(val, row)"
        />
        <span v-else>{{ row.product_code }}</span>
      </template>

      <template #product_color_id="{ row }">
        <SelectMergeComponent
          v-model="row.product_color_id"
          :custom-label="(e:any) => `${formatHashTag(e.product_color_code, e.product_color_name)}`"
          :multiple="false"
          :disabled="!row.product_id"
          :query="{
            finish_product_id: row.product_id,
          }"
          api-name="GetFinishProductColorDropdownList"
          remote
          remote-key="product_color_code_or_name"
          remote-show-suffix
          placeholder="成品颜色、色号"
          value-field="id"
          :default-value="formatHashTag(row.product_color_code, row.product_color_name)"
          @change="(val) => handChangeColorNo(val, row)"
        />
      </template>
      <!-- <template #product_code="{ row }">
        <SelectDialog
          v-if="row.canChangeProduct"
          v-model="row.product_id"
          label-field="finish_product_code"
          :label-name="row.product_code"
          :query="{
            finish_product_code: row.product_code1,
          }"
          api="GetFinishProductDropdownList"
          :column-list="productSelectConfig.columnList"
          :table-column="[
            {
              field: 'finish_product_code',
              title: '成品编号',
            },
          ]"
          @on-input="val => row.product_code1 = val"
          @change-value="(val) => changeProductSelect(val, row)"
        />
        <span v-else>{{ row.product_code }}</span>
      </template>

      <template #product_name="{ row }">
        <SelectDialog
          v-if="row.canChangeProduct"
          v-model="row.product_id"
          label-field="finish_product_name"
          :label-name="row.product_name"
          :query="{
            finish_product_name: row.product_name1,
          }"
          api="GetFinishProductDropdownList"
          :column-list="productSelectConfig.columnList"
          :table-column="[
            {
              field: 'finish_product_name',
              title: '成品名称',
            },
          ]"
          @on-input="val => row.product_name1 = val"
          @change-value="(val) => changeProductSelect(val, row)"
        />
        <span v-else>{{ row.product_name }}</span>
      </template> -->
      <!-- <template #product_color_id="{ row, rowIndex }">
        <SelectDialog
          :key="row?.product_id"
          v-model="row.product_color_id"
          :column-list="colorSelectConfig.columnList"
          :table-column="[
            {
              field: 'product_color_code',
              title: '色号',
            },
          ]"
          :query="{
            finish_product_id: row?.product_id,
            product_color_code: row.product_color_code1,
          }"
          :disabled="!row.product_id"
          api="GetFinishProductColorDropdownList"
          label-field="product_color_code"
          :label-name="row.product_color_code"
          @on-input="(val) => (row.product_color_code1 = val)"
          @change-value="(val) => handChangeColorNo(val, row, rowIndex)"
        />
      </template>
      <template #product_color_name="{ row, rowIndex }">
        <SelectDialog
          :key="row?.product_id"
          v-model="row.product_color_id"
          :column-list="colorSelectConfig.columnList"
          :table-column="[
            {
              field: 'product_color_name',
              title: '颜色',
            },
          ]"
          :query="{
            finish_product_id: row?.product_id,
            product_color_name: row.product_color_name1,
          }"
          :disabled="!row.product_id"
          api="GetFinishProductColorDropdownList"
          label-field="product_color_name"
          :label-name="row.product_color_name"
          @on-input="(val) => (row.product_color_name1 = val)"
          @change-value="(val) => handChangeColorNo(val, row, rowIndex)"
        />
      </template> -->
      <template #product_level_id="{ row, rowIndex }">
        <SelectComponents
          v-model="row.product_level_id"
          placeholder="成品等级"
          api="GetInfoBaseFinishedProductLevelEnumList"
          label-field="name"
          value-field="id"
          clearable
          size="small"
          @change-value="(val) => handLevel(val, rowIndex)"
        />
      </template>
      <template #paper_tube_weight="{ row, rowIndex }">
        <vxe-input
          :id="`paper_tube_weight-${rowIndex}`"
          v-model="row.paper_tube_weight"
          :min="0"
          type="float"
          clearable @keydown="聚焦下一行(rowIndex, $event, 'paper_tube_weight')"
        >
          <template #suffix>
            Kg
          </template>
        </vxe-input>
      </template>
      <template #weight_error="{ row, rowIndex }">
        <vxe-input :id="`weight_error-${rowIndex}`" v-model="row.weight_error" :min="0" type="float" clearable @keydown="聚焦下一行(rowIndex, $event, 'weight_error')">
          <template #suffix>
            Kg
          </template>
        </vxe-input>
      </template>
      <template #roll="{ row, rowIndex }">
        <vxe-input
          :id="`roll-${rowIndex}`"
          v-model="row.roll"
          :min="0"
          type="float"
          clearable
          @blur="handChangeRoll(row)" @keydown="聚焦下一行(rowIndex, $event, 'roll')"
        />
      </template>
      <template #finish_product_width="{ row }">
        <!-- <vxe-input v-model="row.finish_product_width" clearable></vxe-input>
         -->
        <el-input
          v-model="row.finish_product_width"
          clearable
          placeholder="成品幅宽"
          class="input-with-select"
          size="small"
        >
          <template #append>
            <SelectComponents
              v-model="row.finish_product_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
              size="small"
            />
          </template>
        </el-input>
      </template>
      <template #finish_product_gram_weight="{ row }">
        <!-- <vxe-input v-model="row.finish_product_gram_weight" clearable></vxe-input> -->
        <el-input
          v-model="row.finish_product_gram_weight"
          clearable
          placeholder="成品克重"
          class="input-with-select"
          size="small"
        >
          <template #append>
            <SelectComponents
              v-model="row.finish_product_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
              size="small"
            />
          </template>
        </el-input>
      </template>
      <template #weight="{ row, rowIndex }">
        <vxe-input
          :id="`weight-${rowIndex}`"
          v-model="row.weight"
          :min="0"
          type="float"
          clearable
          @blur="handChangeWeight(row)" @keydown="聚焦下一行(rowIndex, $event, 'weight')"
        />
      </template>
      <template #remark="{ row, rowIndex }">
        <vxe-input :id="`remark-${rowIndex}`" v-model="row.remark" clearable @keydown="聚焦下一行(rowIndex, $event, 'remark')" />
      </template>
      <template #upper_limit="{ row, rowIndex }">
        <vxe-input
          :id="`upper_limit-${rowIndex}`"
          v-model="row.upper_limit"
          :min="0"
          type="float"
          clearable @keydown="聚焦下一行(rowIndex, $event, 'upper_limit')"
        />
      </template>
      <template #lower_limit="{ row, rowIndex }">
        <vxe-input
          :id="`lower_limit-${rowIndex}`"
          v-model="row.lower_limit"
          :min="0"
          type="float"
          clearable @keydown="聚焦下一行(rowIndex, $event, 'lower_limit')"
        />
      </template>
      <template #unit_price="{ row, rowIndex }">
        <vxe-input
          :id="`unit_price-${rowIndex}`"
          v-model="row.unit_price"
          :min="0"
          type="float"
          clearable @keydown="聚焦下一行(rowIndex, $event, 'unit_price')"
        />
      </template>
      <template #other_price="{ row, rowIndex }">
        <vxe-input :id="`other_price-${rowIndex}`" v-model="row.other_price" type="float" clearable @keydown="聚焦下一行(rowIndex, $event, 'other_price')" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text link type="primary" @click="handAddTableList()">
          新增
        </el-button>
        <el-button text link type="primary" @click="handCopy(rowIndex)">
          复制
        </el-button>
        <el-button text link type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <Table
      ref="tableRefOne"
      :config="tableConfig_fabric"
      :table-list="state.fabricList"
      :column-list="columnList_fabric"
    >
      <template #roll="{ row }">
        <vxe-input
          v-model="row.roll"
          :min="0"
          type="float"
          clearable
        />
      </template>
      <template #weight="{ row }">
        <vxe-input
          v-model="row.weight"
          :min="0"
          type="float"
          clearable
        />
      </template>
    </Table>
  </FildCard>
  <FildCard title="凭证信息" class="mt-[5px]" :tool-bar="false">
    <TextureMapWall v-model:image-list="state.fileList" text="" />
  </FildCard>
  <AddInformatinDialog
    ref="AddInformatinDialogRef"
    @handle-sure="handAddProduct"
  />
  <AddOutStockDialog
    ref="AddOutStockDialogRef"
    @handle-sure="handSureOutStock"
  />
  <ProductColorDialog
    ref="productColorDialogRef"
    @handle-sure="handSelectColor"
  />
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  />
</template>

<style></style>
