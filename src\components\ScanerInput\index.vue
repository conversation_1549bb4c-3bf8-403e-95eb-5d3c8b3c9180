<script setup lang="ts">
import { Html5Qrcode } from 'html5-qrcode'
import { onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useDebounceFn } from '@vueuse/core'
import type { VxeInputInstance } from 'vxe-pc-ui'
import { getDetailByCond } from '@/api/fpQualityCheck'

const emits = defineEmits(['handleSure'])

const cameraId = ref('')
const devicesInfo = ref('')
const html5QrCode = ref(null)
const scannerResult = defineModel({
  default: '',
  required: true,
})
let isInput = false

function getCameras() {
  isInput = false
  Html5Qrcode.getCameras()
    .then((devices) => {
      ElMessage.info('摄像头信息')
      if (devices && devices.length) {
        // 如果有2个摄像头，1为前置的
        if (devices.length > 1)
          cameraId.value = devices[1].id
        else
          cameraId.value = devices[0].id

        devicesInfo.value = devices
        // start开始扫描
        start()
      }
    })
    .catch((err) => {
      // handle err
      ElMessage.info(`获取设备信息失败${err}`)
    })
}
function start() {
  if (!html5QrCode.value)
    html5QrCode.value = new Html5Qrcode('reader')

  html5QrCode.value.start(
    cameraId.value,
    {
      fps: 10, // 设置每秒多少帧
      qrbox: { width: 300, height: 200 }, // 设置取景范围，默认设备是横屏的，所以宽度大于高度
    },
    (decodedText, _decodedResult) => {
      scannerResult.value = decodedText
      if (decodedText)
        stop()
    },
    (_errorMessage) => {
      // ElMessage.info('暂无扫描结果')
    },
  )
    .catch((err) => {
      console.error(`Unable to start scanning, error: ${err}`)
    })
}

function stop() {
  html5QrCode.value
    .stop()
    .then((ignore) => {
      // QR Code scanning is stopped.
      ElMessage.info(`QR Code scanning stopped.${ignore}`)
    })
    .catch((err) => {
      // Stop failed, handle it.
      ElMessage.info(`Unable to stop scanning.${err}`)
    })
}

const { fetchData, data } = getDetailByCond()
async function getData() {
  if (!scannerResult.value)
    return
  const scanerCode = scannerResult.value.replace('\r', '')
  const query = {}
  const splitCodeExp = /(\^|……)/g
  const splitCodeInChinese = /(……)/g

  if (scanerCode.split(splitCodeExp).length > 1)
    query.qr_code = scanerCode.split(splitCodeInChinese).length > 1 ? scanerCode.replaceAll(splitCodeInChinese, '^') : scanerCode

  else query.bar_code = scanerCode
  //   scannerCode
  await fetchData(query)
  emits('handleSure', [data.value])
}
const inputRef = ref<VxeInputInstance | null>(null)

onMounted(() => {
  inputRef.value?.focus()
})

const getDataWithDebounce = useDebounceFn(getData, 1000)
function handleInput() {
  isInput = true
  getDataWithDebounce()
}

watch(
  () => scannerResult.value,
  () => {
    if (!isInput)
      getData()
  },
)
</script>

<template>
  <div class="mr-4">
    <el-input ref="inputRef" v-model="scannerResult" size="large" class="my-search w-80" placeholder="请扫描" @input="handleInput">
      <template #suffix>
        <p class="w-full" @click="getCameras">
          <i class="vxe-icon-zoom-out" />
        </p>
      </template>
    </el-input>
    <div class="container">
      <div id="reader" />
    </div>
  </div>
</template>

<style scoped>
.my-search.vxe-input:deep .vxe-input--suffix {
  width: 50px;
  //height: 32px;
  top: 1px;
  text-align: center;
  border-left: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  cursor: pointer;
}
.my-search.vxe-input:deep .vxe-input--suffix-icon {
  width: 100%;
  padding-right: 0;
}
</style>
