import { useRequest } from '@/use/useRequest'

export interface AddressTypes {
  id: number
  address: string // 详细地址
  biz_uint_id: number // 客户id
  contact_name: string // 客户名称
  is_default: boolean // 是否默认地址
  location: string[] // 省市区地址
  logistics_company: string // 物流公司
  logistics_area: string // 物流区域
  name: string // 加工厂名称
  phone: string // 手机号
  print_tag: string // 打印打印标签
}

// 获取省市区 GET /hcscm/admin/v1/district/list
export function GetDistrictList() {
  return useRequest({
    url: '/admin/v1/district/list',
    method: 'get',
  })
}

// 获取所有省市区
export function GetDistrictEnumList() {
  return useRequest({
    url: '/admin/v1/district/enum_list',
    method: 'get',
  })
}

// 获取客户地址列表 /hcscm/admin/v1/factory_logistics/list
export function GetBizUnitFactoryLogisticsListByUintList() {
  return useRequest({
    url: '/admin/v1/factory_logistics/list',
    method: 'get',
  })
}

// 新增客户地址 /admin/v1/factory_logistics/addBizUnitFactoryLogistics
export function AddBizUnitFactoryLogistics() {
  return useRequest({
    url: '/admin/v1/factory_logistics/addBizUnitFactoryLogistics',
    method: 'post',
  })
}

// 更新客户地址 /admin/v1/factory_logistics/updateBizUnitFactoryLogistics
export function UpdateBizUnitFactoryLogistics() {
  return useRequest({
    url: '/admin/v1/factory_logistics/updateBizUnitFactoryLogistics',
    method: 'put',
  })
}

// 获取客户地址详情 /admin/v1/factory_logistics/getBizUnitFactoryLogisticsListByUintID
export function GetBizUnitFactoryLogisticsListByUintID() {
  return useRequest({
    url: '/admin/v1/factory_logistics/getBizUnitFactoryLogisticsListByUintID',
    method: 'get',
  })
}
