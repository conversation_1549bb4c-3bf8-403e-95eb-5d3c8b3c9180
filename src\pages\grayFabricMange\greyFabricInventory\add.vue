<script lang="ts" setup name="GreyFabricInventoryAdd">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import AddAdjustXimaDialog from '../components/AddAdjustXimaDialog.vue'
import AddInventoryDialog from '../components/AddInventoryDialog.vue'
import { addGfmStockAdjustOrder } from '@/api/greyFabricInventory'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { deepClone, deleteToast, getCurrentDate, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 调整单位
    date: '', // 退货日期
    remark: '',
    dye_unit_use_order_no: '', // 染厂用坯单号
    warehouse_keeper: '', // 仓管员
    store_keeper_name: '',
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择调整单位', trigger: 'blur' }],
    date: [{ required: true, message: '请选择调整日期', trigger: 'blur' }],
  },
  tableList: [],
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

onMounted(() => {
  state.form.date = getCurrentDate()
  const resDes = getDefaultSaleSystem()
  state.form.marketingSystem = resDes?.default_sale_system_id
})

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '180',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')} kg`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      return null
    }),
  ]
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const columnList = ref([
  {
    title: '调整前',
    childrenList: [
      {
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 150,
      },
      {
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 150,
      },
      {
        field: 'supplier_name',
        title: '供方名称',
        minWidth: 150,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 150,
      },
      {
        field: 'yarn_batch',
        title: '纱批',
        minWidth: 100,
      },
      {
        field: 'machine_number',
        title: '机台号',
        minWidth: 100,
      },
      {
        field: 'grey_fabric_width',
        soltName: 'grey_fabric_width',
        title: '坯布幅宽',
        minWidth: 100,
      },
      {
        field: 'grey_fabric_gram_weight',
        soltName: 'grey_fabric_gram_weight',
        title: '坯布克重',
        minWidth: 100,
        // isWeight: true,
      },
      {
        field: 'needle_size',
        title: '针寸数',
        minWidth: 100,
      },
      {
        field: 'component',
        title: '成分',
        minWidth: 100,
      },
      {
        field: 'gray_fabric_color_name',
        title: '织坯颜色',
        minWidth: 100,
      },
      {
        field: 'grey_fabric_level_name',
        title: '坯布等级',
        minWidth: 100,
      },
      {
        field: 'raw_material_batch_num',
        title: '原料批号',
        minWidth: 100,
      },
      //   {
      //     field: 'pound_weight',
      //     title: '磅重',
      //     minWidth: 100,
      //     isWeight: true,
      //   },

      {
        field: 'single_price',
        title: '单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'grey_fabric_remark',
        title: '坯布备注',
        minWidth: 100,
      },
    ],
  },

  {
    title: '调整后',
    childrenList: [
      {
        field: 'after_customer_id',
        title: '所属客户',
        minWidth: 100,
        soltName: 'custom_name',
        required: true,
      },
      {
        field: 'after_machine_number',
        title: '机台号',
        minWidth: 100,
        soltName: 'machine_number',
      },
      {
        field: 'after_yarn_batch',
        title: '纱批',
        minWidth: 100,
        soltName: 'yarn_batch',
      },
      {
        field: 'after_gray_fabric_color_id',
        title: '织坯颜色',
        minWidth: 100,
        soltName: 'gray_fabric_color_name',
      },
      {
        field: 'after_grey_fabric_level_id',
        title: '坯布等级',
        minWidth: 100,
        soltName: 'grey_fabric_level_name',
      },
      {
        field: 'after_raw_material_batch_num',
        title: '原料批号',
        minWidth: 100,
        soltName: 'after_raw_material_batch_num',
      },
      {
        field: 'after_grey_fabric_remark',
        title: '坯布备注',
        minWidth: 100,
        soltName: 'after_grey_fabric_remark',
      },
      {
        field: 'after_single_price',
        title: '单价',
        minWidth: 100,
        soltName: 'after_single_price',
      },
    ],
  },

  {
    title: '涉及匹数',
    childrenList: [
      {
        field: 'stock_roll',
        title: '库存匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'roll',
        title: '匹数',
        minWidth: 100,
        soltName: 'roll',
      },
      {
        field: 'total_weight',
        title: '总数量',
        minWidth: 100,
        soltName: 'total_weight',
      },
    ],
  },
])

async function handDelete(index: number) {
  const res = await deleteToast('确认删除嘛？')
  if (res)
    state.tableList.splice(index, 1)
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const AddInventoryDialogRef = ref()

function handAdd() {
  AddInventoryDialogRef.value.state.showModal = true
  AddInventoryDialogRef.value.state.filterData.warehouse_id = state.form.consignee
  AddInventoryDialogRef.value.state.sale_system_id = state.form.marketingSystem
}

function handleSureFabric(list: any) {
  list.forEach((item: any) => {
    state.tableList.push({
      ...item,
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      yarn_batch: item.yarn_batch,
      supplier_id: item.supplier_id,
      supplier_name: item.supplier_name,
      gray_fabric_color_id: item.gray_fabric_color_id,
      gray_fabric_color_name: item.gray_fabric_color_name,
      grey_fabric_level_id: item.grey_fabric_level_id,
      grey_fabric_level_name: item.grey_fabric_level_name,
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      needle_size: item.needle_size,
      machine_number: item.machine_number,
      component: item.component,
      single_price: item.single_price,
      stock_roll: item.stock_roll,
      total_weight: 0,
      roll: '',
      remark: '',
      item_fc_data: [],
      is_stock_source: true,
      grey_fabric_remark: item.source_remark,
      //   raw_material_batch_brand: item.raw_material_batch_brand,
      raw_material_batch_num: item.raw_material_batch_num,
      //   raw_material_yarn_name: item.raw_material_yarn_name,
      //   pound_weight: item.pound_weight,

      // 初始化读取调整前的数据
      after_customer_id: item.customer_id,
      after_customer_name: item.customer_name,
      after_machine_number: item.machine_number,
      after_yarn_batch: item.yarn_batch,
      after_gray_fabric_color_id: item.gray_fabric_color_id,
      after_grey_fabric_level_id: item.grey_fabric_level_id || '',
      after_raw_material_batch_num: item.raw_material_batch_num,
      after_grey_fabric_remark: item.source_remark,
      after_single_price: formatUnitPriceDiv(item.single_price),
      grey_fabric_id: item?.grey_fabric_id || 0,
      warehouse_sum_id: item?.id || 0,
    })
  })
  AddInventoryDialogRef.value.state.showModal = false
}

const AddAdjustXimaDialogRef = ref()

// 细码调整
function handWrite(row: any, rowIndex: number) {
  if (row.roll === '')
    return ElMessage.error('请先输入匹数')

  const info = {
    supplier_name: row.supplier_name,
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    gray_fabric_color_name: row.gray_fabric_color_name,
    grey_fabric_level_name: row.grey_fabric_level_name,
    yarn_batch: row.yarn_batch,
    machine_number: row.machine_number,
    customer_name: row.customer_name,
  }
  const filterData = {
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    customer_id: row.customer_id,
    customer_name: row.customer_name,
    yarn_batch: row.yarn_batch,
    supplier_id: row.supplier_id,
    gray_fabric_color_id: row.gray_fabric_color_id,
    grey_fabric_level_id: row.grey_fabric_level_id,
    raw_material_batch_brand: row.raw_material_batch_brand,
    raw_material_batch_num: row.raw_material_batch_num,
    raw_material_yarn_name: row.raw_material_yarn_name,
    source_remark: row.grey_fabric_remark,
    warehouse_id: state.form.consignee,
    warehouse_sum_id: row?.warehouse_sum_id || 0,
  }
  AddAdjustXimaDialogRef.value.state.filterData = filterData
  AddAdjustXimaDialogRef.value.state.info = info
  AddAdjustXimaDialogRef.value.state.canEnter = row.roll
  AddAdjustXimaDialogRef.value.state.showModal = true
  AddAdjustXimaDialogRef.value.state.rowIndex = rowIndex
}

function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.ximaList
      item.total_weight = sumNum(val.ximaList, 'weight')
      return item
    }
  })
  AddAdjustXimaDialogRef.value.state.showModal = false
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addGfmStockAdjustOrder()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)
  for (let i = 0; i < list.length; i++) {
    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (list[i].after_customer_id === '')
      return ElMessage.error('请选择所属客户')

    if (list[i].roll > formatPriceDiv(list[i].stock_roll))
      return ElMessage.error('调整匹数不能大于库存匹数')

    if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0)
      return ElMessage.error(`第${i + 1}行的细码必须录完`)

    list[i].roll = Number(formatPriceMul(list[i]?.roll))
    list[i].after_single_price = Number(formatUnitPriceMul(list[i]?.after_single_price))

    if (list[i]?.after_gray_fabric_color_id === '')
      list[i].after_gray_fabric_color_id = 0

    if (list[i].after_grey_fabric_level_id === '')
      list[i].after_grey_fabric_level_id = 0

    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].item_fc_data[q].weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].item_fc_data[q].roll === '')
        return ElMessage.error('细码匹数不可为空')

      list[i].item_fc_data[q].roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
      list[i].item_fc_data[q].weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
      list[i].item_fc_data[q].grey_fabric_stock_id = Number(list[i].item_fc_data[q].id)
      list[i].item_fc_data[q].grey_fabric_gram_weight = list[i].item_fc_data[q].grey_fabric_gram_weight.toString()
    }
  }

  const query = {
    item_data: list,
    remark: state.form.remark,
    dye_unit_use_order_no: state.form.dye_unit_use_order_no,
    adjust_unit_id: state.form.consignee,
    store_keeper_id: state.form.warehouse_keeper || 0,
    adjust_time: formatDate(state.form.date),
    sale_system_id: state.form.marketingSystem,
    store_keeper_name: state.form.store_keeper_name,
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyFabricInventoryDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

const bulkSetting = ref<any>({})

const bulkList = reactive<any>([
  {
    field: 'after_customer_id',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    labelField: 'name',
    valueField: 'id',
  },
  //   {
  //     field: 'grey_fabric_name',
  //     title: '坯布名称',
  //     component: 'select',
  //     api: 'GetGreyFabricInfoListUseByOthers',
  //     labelField: 'name',
  //     valueField: 'name',
  //   },
  //   {
  //     field: 'supplier_id',
  //     title: '供方名称',
  //     component: 'select',
  //     api: 'business_unitsupplierlist',
  //   },
  //   {
  //     field: 'after_customer_id',
  //     title: '所属客户',
  //     component: 'select',
  //     api: 'business_unitsupplierlist',
  //   },
  //   {
  //     field: 'raw_material_yarn_name',
  //     title: '原料纱名',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'after_raw_material_batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  //   {
  //     field: 'raw_material_batch_brand',
  //     title: '原料品牌',
  //     component: 'input',
  //     type: 'text',
  //   },
  {
    field: 'after_gray_fabric_color_id',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  {
    field: 'after_grey_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'getInfoBaseGreyFabricLevelList',
  },
  {
    field: 'after_machine_number',
    title: '机台号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'after_yarn_batch',
    title: '纱批',
    component: 'input',
    type: 'text',
  },
  {
    field: 'after_grey_fabric_remark',
    title: '坯布备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'roll',
    title: '调整匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'after_single_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
])

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="marketingSystem">
              <SelectComponents v-model="state.form.marketingSystem" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="调整单位:">
          <template #content>
            <el-form-item prop="consignee">
              <SelectDialog
                v-model="state.form.consignee"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.unit_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.unit_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item prop="warehouse_keeper">
              <SelectComponents
                v-model="state.form.warehouse_keeper"
                :query="{ duty: EmployeeType.warehouseManager }"
                api="Adminemployeelist"
                label-field="name"
                value-field="id"
                clearable
                @change-value="val => (state.form.store_keeper_name = val.name)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="调整日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="调整日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂用坯单号:">
          <template #content>
            <el-form-item prop="dye_unit_use_order_no">
              <el-input v-model="state.form.dye_unit_use_order_no" placeholder="染厂用坯单号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" :disabled="state.form.marketingSystem === '' || state.form.consignee === ''" @click="handAdd">
        从库存中添加
      </el-button>
    </template>
  </FildCard>
  <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
    <template #custom_name="{ row }">
      <SelectDialog
        v-model="row.after_customer_id"
        api="GetCustomerEnumList"
        :label-name="row.after_customer_name"
        :query="{ sale_system_id: state.form.marketingSystem, name: componentRemoteSearch.customer_name }"
        :column-list="[
          {
            title: '客户编号',
            minWidth: 100,
            required: true,
            colGroupHeader: true,
            childrenList: [
              {
                field: 'code',
                isEdit: true,
                title: '客户编号',
                minWidth: 100,
              },
            ],
          },
          {
            title: '客户名称',
            minWidth: 100,
            colGroupHeader: true,
            required: true,
            childrenList: [
              {
                isEdit: true,
                field: 'name',
                title: '客户名称',
                minWidth: 100,
              },
            ],
          },
          {
            title: '电话',
            colGroupHeader: true,
            minWidth: 100,
            childrenList: [
              {
                field: 'phone',
                isEdit: true,
                title: '电话',
                minWidth: 100,
              },
            ],
          },
          {
            title: '销售员',
            minWidth: 100,
            colGroupHeader: true,
            childrenList: [
              {
                field: 'seller_name',
                title: '销售员',
                soltName: 'seller_name',
                isEdit: true,
                minWidth: 100,
              },
            ],
          },
        ]"
        @change-input="val => (componentRemoteSearch.customer_name = val)"
        @change-value="val => (row.after_customer_name = val.name)"
      />
    </template>
    <template #machine_number="{ row }">
      <vxe-input v-model="row.after_machine_number" />
    </template>
    <template #yarn_batch="{ row }">
      <vxe-input v-model="row.after_yarn_batch" />
    </template>
    <template #gray_fabric_color_name="{ row }">
      <SelectComponents v-model="row.after_gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
    </template>
    <template #grey_fabric_level_name="{ row }">
      <SelectComponents v-model="row.after_grey_fabric_level_id" api="getInfoBaseGreyFabricLevelList" label-field="name" value-field="id" clearable />
    </template>
    <!-- <template #gray_fabric_remark="{ row }">
      <vxe-input v-model="row.after_grey_fabric_remark"></vxe-input>
    </template> -->
    <template #after_raw_material_batch_num="{ row }">
      <vxe-input v-model="row.after_raw_material_batch_num" />
    </template>
    <template #after_grey_fabric_remark="{ row }">
      <vxe-input v-model="row.after_grey_fabric_remark" />
    </template>
    <template #after_single_price="{ row }">
      <vxe-input v-model="row.after_single_price" type="float" />
    </template>
    <template #roll="{ row }">
      <vxe-input v-model="row.roll" :min="0" type="float" />
    </template>
    <template #total_weight="{ row }">
      {{ row.total_weight }} {{ row.unit_name }}
    </template>
    <template #operate="{ row, rowIndex }">
      <el-button type="primary" text link @click="handWrite(row, rowIndex)">
        细码调整
      </el-button>
      <el-button text type="danger" @click="handDelete(rowIndex)">
        删除
      </el-button>
    </template>
    <template #grey_fabric_width="{ row }">
      {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
    </template>
    <template #grey_fabric_gram_weight="{ row }">
      {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
    </template>
  </Table>
  <AddInventoryDialog ref="AddInventoryDialogRef" @handle-sure="handleSureFabric" />
  <AddAdjustXimaDialog ref="AddAdjustXimaDialogRef" @handle-sure="handleSureXima" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
