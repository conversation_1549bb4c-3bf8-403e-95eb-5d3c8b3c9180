<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import {
  AddGfmCostPrice,
  EditGfmCostPrice,
  GetGfmWarehouseSummaryList,
  GetGfmWarehouseSummaryListExport,
} from '@/api/dyeingFactoryTable'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceDiv, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { debounce, getFilterData, getRecentDay_Date } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import { useListExport } from '@/hooks/useListExport'
import EditGrossCost from '@/pages/dyeingManagement/rawStockTable/components/NoDyeingFabirc/EditGrossCost.vue'
import RemarkrRecord from '@/pages/dyeingManagement/dyeingFactoryTable/components/RemarkrRecord.vue'

const { handleExport, loadingExcel } = useListExport()

const state = reactive<any>({
  multipleSelection: [],
  filterData: {
    grey_fabric_code: '',
    grey_fabric_name: '',
    machine_number: '',
    warehouse_id: '',
    supplier_id: '',
    gray_fabric_color_id: '',
    date: [],
    source_code: '',
    yarn_batch: '',
    source_num: '',
  },
})

const { loading, fetchData: ApiCustomerList, data, total, handleSizeChange, handleCurrentChange } = GetGfmWarehouseSummaryList()

const tableConfig = ref({
  fieldApiKey: 'DyeingFactoryTable_D',
  showSlotNums: true,
  height: '100%',
  showCheckBox: true,
  total,
  showPagition: true,
  footerMethod: (val: any) => FooterMethod(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
  loading,
  handleSizeChange,
  handleCurrentChange,
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}
const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
  warehouse_name: '',
})

onMounted(() => {
  state.filterData.date = getRecentDay_Date(1)
})
onMounted(() => {
  state.filterData.date = getRecentDay_Date(1)
})

// 获取请求参数
function getQuery() {
  const query = {
    begin_time: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[0]) : '',
    end_time: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[1]) : '',
    ...state.filterData,
  }
  return getFilterData(query)
}
// 获取数据
const getData = debounce(() => {
  ApiCustomerList(getQuery())
}, 400)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const shortcuts: any = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['pre_settle_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'pre_settle_roll') as any)}`

      if (['pre_settle_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'pre_settle_weight') as any)}`

      if (['in_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'in_roll') as any)}`

      if (['in_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'in_weight') as any)}`

      if (['return_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'return_roll') as any)}`

      if (['return_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'return_weight') as any)}`

      if (['dye_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'dye_roll') as any)}`

      if (['dye_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'dye_weight') as any)}`

      if (['allo_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'allo_roll') as any)}`

      if (['allo_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'allo_weight') as any)}`

      if (['deduction_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'deduction_roll') as any)}`

      if (['deduction_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'deduction_weight') as any)}`

      if (['check_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'check_roll') as any)}`

      if (['check_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'check_weight') as any)}`

      if (['adjust_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'adjust_roll') as any)}`

      if (['adjust_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'adjust_weight') as any)}`

      if (['stock_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'stock_roll') as any)}`

      if (['stock_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'stock_weight') as any)}`

      if (['finishing_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'finishing_roll') as any)}`

      if (['finishing_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'finishing_weight') as any)}`

      if (['back_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'back_roll') as any)}`

      if (['back_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'back_weight') as any)}`

      return null
    }),
  ]
}
const table_coloumn = ref([
  {
    title: '',
    field: 'base',
    childrenList: [
      {
        sortable: true,
        field: 'warehouse_name',
        title: '染厂名称',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供方名称',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'source_code',
        title: '出坯单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'source_time',
        title: '出坯日期',
        minWidth: 100,
        isDate: true,
        formatTime: 'YYYY-MM-DD',
      },
      {
        sortable: true,
        field: 'dye_unit_use_order_no',
        title: '染厂用坯单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'produce_order_no',
        title: '生产通知单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'yarn_batch',
        title: '纱批',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'raw_material_batch_brand',
        title: '原料品牌',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'raw_material_yarn_name',
        title: '原料纱名',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'raw_material_batch_num',
        title: '原料批号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_level_name',
        title: '坯布等级',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'machine_number',
        title: '机台号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'gray_fabric_color_name',
        title: '织坯颜色',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'source_remark',
        title: '坯布备注',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'warehouse_remark',
        title: '染厂备注',
        minWidth: 100,
      },
    ],
  },
  {
    title: '上期结存',
    field: 'previous_period_balance',
    childrenList: [
      {
        sortable: true,
        field: 'pre_settle_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'pre_settle_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '本期进仓',
    field: 'current_stock_entry',
    childrenList: [
      {
        sortable: true,
        field: 'in_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'in_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '本期出仓',
    field: 'current_blank_rejection',
    childrenList: [
      {
        sortable: true,
        field: 'return_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'return_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '本期后整',
    field: 'current_round',
    childrenList: [
      {
        sortable: true,
        field: 'finishing_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'finishing_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '本期返布',
    field: 'current_return',
    childrenList: [
      {
        sortable: true,
        field: 'back_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'back_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  // {
  //   title: '本期调拨',
  //   field: 'current_allocation',
  //   childrenList: [
  //     {
  //       sortable: true,
  //       field: 'allo_roll',
  //       title: '匹数',
  //       minWidth: 100,
  //       isPrice: true,
  //     },
  //     {
  //       sortable: true,
  //       field: 'allo_weight',
  //       title: '数量',
  //       minWidth: 100,
  //       isWeight: true,
  //     },
  //   ],
  // },
  {
    title: '本期扣款',
    field: 'current_deduction',
    childrenList: [
      {
        sortable: true,
        field: 'deduction_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'deduction_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '本期盘点',
    field: 'current_inventory',
    childrenList: [
      {
        sortable: true,
        field: 'check_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'check_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '本期结存',
    field: 'current_balance',
    childrenList: [
      {
        sortable: true,
        field: 'stock_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'stock_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'buoyant_weight_price',
        fixed: 'right',
        title: '毛重单价',
        minWidth: 100,
        soltName: 'buoyant_weight_price',
      },
      {
        sortable: true,
        field: 'stock_cost',
        title: '库存成本',
        fixed: 'right',
        minWidth: 100,
        isPrice: true,
      },
    ],
  },
])
const EditGrossCostRef = ref()
const showEditGross = ref(false)
const currentRow = ref<any>(null)
const { fetchData, success, msg } = EditGfmCostPrice()
const { fetchData: addGfmCostPrice, success: addSuccess, msg: addMsg } = AddGfmCostPrice()
async function handleEditGrossSuccess(val: any) {
  if (!currentRow.value.gfm_cost_price_id) {
    await addGfmCostPrice({
      buoyant_weight_price: formatWeightMul(val),
      buoyant_weight: currentRow.value.stock_weight,
      warehouse_sum_id: currentRow.value.id,
      // cost_calculate_date: formatDate(Date.now()),
      // grey_fabric_id: currentRow.value.grey_fabric_id,
      // grey_fabric_color_id: currentRow.value.gray_fabric_color_id,
      // src_order_id: currentRow.value.source_id,
    })
    if (!addSuccess.value)
      return ElMessage.error(addMsg.value)
    getData()

    return
  }
  await fetchData({
    id: currentRow.value.gfm_cost_price_id,
    buoyant_weight_price: formatWeightMul(val),
  })
  if (!success.value)
    return ElMessage.error(msg.value)
  getData()
}
function handleEditGross(row: any) {
  EditGrossCostRef.value.form.number = formatWeightDiv(row.buoyant_weight_price)
  showEditGross.value = true
  currentRow.value = row
}
const RemarkrRecordRef = ref()
function handEditDialog() {
  if (state.multipleSelection.length > 1)
    return ElMessage.error('只能勾选一条数据')

  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')

  RemarkrRecordRef.value.state.showModal = true
  RemarkrRecordRef.value.state.warehouse_sum_ids = state.multipleSelection.map(item => item.id).join(',')
  RemarkrRecordRef.value.state.src_ids = state.multipleSelection.map(item => item.gfm_cost_price_id).join(',')
}
</script>

<template>
  <FildCard title="" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="日期范围:" copies="2">
        <template #content>
          <el-date-picker
            v-model="state.filterData.date"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="染厂名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.warehouse_id"
            api="BusinessUnitSupplierEnumlist"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.warehouse_name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '染厂编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '染厂编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.warehouse_name = val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供方名称:">
        <template #content>
          <SelectDialog
            v-model="state.filterData.supplier_id"
            :query="{ name: componentRemoteSearch.name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '供应商编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '供应商编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.name = val)"
          />
          <!-- <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="纱批:">
        <template #content>
          <el-input v-model="state.filterData.yarn_batch" clearable placeholder="纱批" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据类型:">
        <template #content>
          <SelectComponents v-model="state.filterData.source_num" api="GetGfmWarehouseTypeEnum" label-field="name" value-field="id" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出坯单号:">
        <template #content>
          <el-input v-model="state.filterData.source_code" clearable placeholder="出坯单号" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布编号:">
        <template #content>
          <el-input v-model="state.filterData.grey_fabric_code" clearable placeholder="坯布编号" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="坯布名称:">
        <template #content>
          <el-input v-model="state.filterData.grey_fabric_name" clearable placeholder="坯布名称" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="织坯颜色:">
        <template #content>
          <SelectComponents v-model="state.filterData.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="机台号:">
        <template #content>
          <el-input v-model="state.filterData.machine_number" clearable placeholder="机台号" />
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="" class="flex-1 flex flex-col overflow-y-hidden">
    <template #right-top>
      <el-button plain type="primary" :disabled="!state.multipleSelection.length" @click="handEditDialog">
        查看修改记录
      </el-button>
      <BottonExcel
        v-has="`DyeingFactoryTableFabricStockTotalExport`"
        :loading="loadingExcel"
        title="导出文件"
        @on-click-excel="handleExport({
          tableList: data?.list,
          apiRequest: GetGfmWarehouseSummaryListExport,
          query: getQuery(),
          tableName: '坯布总库存',
        })"
      />
    </template>
    <Table :config="tableConfig" :table-list="data?.list" :column-list="table_coloumn">
      <template #buoyant_weight_price="{ row }">
        <span class="flex cursor-pointer items-center" @click="handleEditGross(row)">
          ￥{{ formatWeightDiv(row.buoyant_weight_price) || 0 }}
          <el-link :icon="Edit" :underline="false" />
        </span>
      </template>
    </Table>
  </FildCard>
  <RemarkrRecord ref="RemarkrRecordRef" />
  <EditGrossCost
    ref="EditGrossCostRef"
    v-model="showEditGross"
    :row-data="currentRow"
    @success="handleEditGrossSuccess"
  />
</template>

<style></style>
