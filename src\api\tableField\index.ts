import type { ResponseList } from '../commonTs'
import type { SystemGetListHabitsManagementData, SystemUpdateListHabitsParam } from './rules'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function GetListHabitsList() {
  return useRequest<any, ResponseList<SystemGetListHabitsManagementData>>({
    url: '/admin/v1/system/listHabits/getManagement',
    method: 'get',
  })
}
// 保存
export function UpdateListHabits() {
  return useRequest<any, ResponseList<SystemUpdateListHabitsParam>>({
    url: '/admin/v1/system/listHabits/updateListHabits',
    method: 'put',
  })
}

// 全局保存
export function UpdateAllListHabits() {
  return useRequest<any, ResponseList<SystemUpdateListHabitsParam>>({
    url: '/admin/v1/system/listHabits/updateAllListHabits',
    method: 'put',
  })
}

// 获取列表习惯表列表
export function GetListHabits() {
  return useRequest<any, ResponseList<SystemGetListHabitsManagementData>>({
    url: '/admin/v1/system/listHabits/getListHabitsList',
    method: 'get',
  })
}

// 新增快捷入口
export function AddEnterListHabits() {
  return useRequest<any, ResponseList<SystemUpdateListHabitsParam>>({
    url: '/admin/v1/system/listHabits/addEnterListHabits',
    method: 'post',
  })
}

// 更新快捷入口
export function UpdateEnterListHabits() {
  return useRequest<any, ResponseList<SystemUpdateListHabitsParam>>({
    url: '/admin/v1/system/listHabits/updateEnterListHabits',
    method: 'put',
  })
}
