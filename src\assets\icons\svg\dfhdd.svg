<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 34</title>
    <defs>
        <linearGradient x1="30.1048764%" y1="0%" x2="69.1623717%" y2="85.9837869%" id="linearGradient-1">
            <stop stop-color="#558DE7" offset="0%"></stop>
            <stop stop-color="#0968F4" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="10" y="9" width="29" height="29" rx="2"></rect>
        <filter x="-24.1%" y="-17.2%" width="148.3%" height="148.3%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.211764706   0 0 0 0 0.494117647   0 0 0 0 0.925490196  0 0 0 0.222321624 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="9.04016821%" y1="31.4514505%" x2="76.4379839%" y2="62.8520674%" id="linearGradient-4">
            <stop stop-color="#F5FBFE" stop-opacity="0.755896935" offset="0%"></stop>
            <stop stop-color="#1C72F1" stop-opacity="0.342189549" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-910.000000, -503.000000)" id="编组-34">
            <g transform="translate(910.000000, 503.000000)">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="48" height="48"></rect>
                <g id="矩形" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
                <g id="编组-31" transform="translate(15.000000, 17.000000)" fill="#F5F6FA">
                    <rect id="矩形" x="0" y="5" width="17" height="3" rx="1.5"></rect>
                    <path d="M10.5083651,0.380235701 C11.1428921,-0.163061655 12.1247513,-0.118863275 12.7014091,0.478955546 L17.5964165,5.55359069 C18.1730742,6.15140951 18.1261619,7.07646694 17.4916349,7.6197643 C16.8571079,8.16306166 15.8752487,8.11886327 15.2985909,7.52104445 L10.4035835,2.44640931 C9.82692578,1.84859049 9.8738381,0.923533057 10.5083651,0.380235701 Z" id="路径-9" fill-rule="nonzero"></path>
                    <path d="M10.5083651,5.3802357 C11.1428921,4.83693834 12.1247513,4.88113673 12.7014091,5.47895555 L17.5964165,10.5535907 C18.1730742,11.1514095 18.1261619,12.0764669 17.4916349,12.6197643 C16.8571079,13.1630617 15.8752487,13.1188633 15.2985909,12.5210445 L10.4035835,7.44640931 C9.82692578,6.84859049 9.8738381,5.92353306 10.5083651,5.3802357 Z" id="路径-9备份" fill-rule="nonzero" transform="translate(14.000000, 9.000000) scale(1, -1) translate(-14.000000, -9.000000) "></path>
                </g>
                <rect id="矩形" stroke="url(#linearGradient-4)" stroke-width="0.5" x="26.75" y="29.75" width="17.5" height="11.5" rx="2"></rect>
                <polygon id="矩形" fill="#F5FBFE" opacity="0.5" points="39 37 42 37 41 39 38 39"></polygon>
            </g>
        </g>
    </g>
</svg>