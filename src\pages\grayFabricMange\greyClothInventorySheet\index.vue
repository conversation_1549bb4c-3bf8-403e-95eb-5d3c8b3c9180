<script setup lang="ts" name="GreyClothInventorySheet">
import { Delete, Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import AddInventoryXimaDialog from '../components/AddInventoryXimaDialog.vue'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
// import router from '@/router'
import SelectComponents from '@/components/SelectComponents/index.vue'
import {
  getGfmStockCheckOrder,
  getGfmStockCheckOrderList,
  getGfmStockCheckOrderListExport,
  updateGfmStockCheckOrderStatusPass,
  updateGfmStockCheckOrderStatusWait,
} from '@/api/greyClothInventorySheet'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  getTableCellTextClass,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import {
  formatDate,
  formatNumberPrefix,
  formatPriceDiv,
  formatRollDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import SelectDate from '@/components/SelectDate/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    order_no: '',
    check_unit_id: '',
    date: '',
    audit_status: [],
    order_type: null,
  },
  multipleSelection: [],
  information: false,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getGfmStockCheckOrderList()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.audit_status.length)
    obj.audit_status = obj.audit_status.join(',')

  const query: any = {
    begin_check_time:
      state.filterData.date
      && state.filterData.date !== ''
      && state.filterData.date.length
        ? formatDate(state.filterData.date[0])
        : '',
    end_check_time:
      state.filterData.date
      && state.filterData.date !== ''
      && state.filterData.date.length
        ? formatDate(state.filterData.date[1])
        : '',
    ...state.filterData,
    audit_status: obj.audit_status,
  }
  delete query.date
  await ApiCustomerList(getFilterData(query))
  if (data.value?.list)
    getInfomation(data.value.list[0])
}, 400)

onMounted(() => {
  getData()
})

onActivated(getData)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  height: '100%',
  fieldApiKey: 'greyClothInventorySheetIndex',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
})

const columnList_fabic_config = ref({
  fieldApiKey: 'greyClothInventorySheetFabic',
  footerMethod: (val: any) => FooterMethod(val),
  height: '100%',
  showSpanHeader: true,
  showSlotNums: true,
  footerCellClassName: ({ column, $columnIndex, row }: any) => {
    if (['result_roll', 'result_weight'].includes(column.field)) {
      const footVal = row[$columnIndex]
      return getTableCellTextClass(footVal)
    }
  },
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['before_roll', 'actually_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'actually_roll') as any)}`

      if (['before_weight', 'actually_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_weight') as any)}`

      if (['result_roll'].includes(column.property))
        return formatNumberPrefix(formatRollDiv(sumNum(data, column.property) as any))

      if (['result_weight'].includes(column.property))
        return formatNumberPrefix(formatWeightDiv(sumNum(data, column.property) as any))

      return null
    }),
  ]
}

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    title: '基础信息',
    field: 'base',
    childrenList: [
      {
        sortable: true,
        field: 'order_no',
        title: '单据编号',
        width: '8%',
        fixed: 'left',
        soltName: 'link',
      },
      {
        sortable: true,
        field: 'sale_system_name',
        title: '营销体系',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'check_unit_name',
        title: '盘点单位',
        minWidth: 100,
      },
    ],
  },

  {
    title: '盘前',
    field: 'before',
    childrenList: [
      {
        sortable: true,
        field: 'total_before_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'total_before_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '实盘',
    field: 'actually',
    childrenList: [
      {
        sortable: true,
        field: 'total_actually_roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'total_actually_weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
    ],
  },
  {
    title: '盘盈/亏',
    field: 'result',
    childrenList: [
      {
        sortable: true,
        field: 'inventoryRoll',
        title: '匹数',
        minWidth: 100,
        soltName: 'roll',
      },
      {
        sortable: true,
        field: 'inventoryWeight',
        title: '数量',
        minWidth: 100,
        soltName: 'weight',
      },
    ],
  },
  {
    title: '其他信息',
    field: 'other',
    childrenList: [
      {
        sortable: true,
        field: 'check_time',
        title: '盘点日期',
        minWidth: 150,
        is_date: true,
      },
      {
        field: 'create_time',
        title: '创建时间',
        sortable: true,
        minWidth: 150,
        isDate: true,
      },
      {
        field: 'audit_time',
        title: '审核时间',
        sortable: true,
        minWidth: 150,
        isDate: true,
      },
      {
        field: 'update_time',
        title: '最后修改时间',
        sortable: true,
        isDate: true,
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'audit_status',
        title: '单据状态',
        fixed: 'right',
        soltName: 'audit_status',
        showOrder_status: true,
        width: '5%',
      },
    ],
  },
])

const columnList_fabic = ref([
  {
    title: '基础信息',
    field: 'base',
    childrenList: [
      {
        sortable: true,
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供方名称',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'yarn_batch',
        title: '纱批',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'gray_fabric_color_name',
        title: '织坯颜色',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'machine_number',
        title: '机台号',
        minWidth: '5%',
      },
      {
        field: 'grey_fabric_level_name',
        title: '坯布等级',
        minWidth: '5%',
      },
      {
        field: 'grey_fabric_remark',
        title: '坯布备注',
        minWidth: 100,
      },
    ],
  },
  {
    title: '盘前',
    field: 'before',
    childrenList: [
      {
        sortable: true,
        field: 'before_roll',
        title: '匹数',
        minWidth: '5%',
        isPrice: true,
      },
      {
        sortable: true,
        field: 'before_weight',
        title: '数量',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '实盘',
    field: 'actually',
    childrenList: [
      {
        sortable: true,
        field: 'actually_roll',
        title: '匹数',
        minWidth: '5%',
        isPrice: true,
      },
      {
        sortable: true,
        field: 'actually_weight',
        title: '数量',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
  {
    title: '盘盈/亏',
    field: 'result',
    childrenList: [
      {
        sortable: true,
        field: 'result_roll',
        title: '匹数',
        minWidth: '5%',
        soltName: 'result_roll',
      },
      {
        sortable: true,
        field: 'result_weight',
        title: '数量',
        minWidth: '5%',
        soltName: 'result_weight',
      },
    ],
  },
  {
    field: 'code',
    title: '其他信息',
    minWidth: 100,
    childrenList: [
      {
        field: 'xima',
        title: '细码',
        minWidth: 100,
        soltName: 'xima',
      },
      {
        sortable: true,
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
    ],
  },
])

// const handShowSort = () => {
//   tableConfig.value.showSort = true
// }

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '坯布盘点单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = getGfmStockCheckOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'GreyClothInventorySheetAdd',
  })
}

const { fetchData: getFetchDetail, data: fabricList } = getGfmStockCheckOrder()

// 获取坯布信息
async function getInfomation(row: any) {
  await getFetchDetail({ id: row.id })

  state.information = true
}

function handDetail(row: any) {
  router.push({
    name: 'GreyClothInventorySheetDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'GreyClothInventorySheetEdit',
    query: { id: row.id },
  })
}

const AddXimaDialogRef = ref()
const AddInventoryXimaDialogRef = ref()

function handSeeXima(row: any, rowIndex: number) {
  const listData = deepClone(row?.item_fc_data || [])
  listData?.map((item: any) => {
    item.roll = Number(formatPriceDiv(item?.actually_roll))
    item.actually_roll = Number(formatRollDiv(item?.actually_roll))
    item.weight = Number(formatWeightDiv(item?.actually_weight))
    item.actually_weight = Number(formatWeightDiv(item?.actually_weight))
    return item
  })
  if (row.is_stock_source) {
    const filterData = {
      grey_fabric_code: row.grey_fabric_code,
      grey_fabric_name: row.grey_fabric_name,
      customer_id: row.customer_id,
      customer_name: row.customer_name,
      yarn_batch: row.yarn_batch,
      supplier_id: row.supplier_id,
      gray_fabric_color_id: row.gray_fabric_color_id,
      grey_fabric_level_id: row.grey_fabric_level_id,
      raw_material_batch_brand: row.raw_material_batch_brand,
      raw_material_batch_num: row.raw_material_batch_num,
      raw_material_yarn_name: row.raw_material_yarn_name,
      grey_fabric_remark: row.grey_fabric_remark,
      warehouse_id: row.warehouse_id,
      warehouse_sum_id: row?.warehouse_sum_id || 0,
    }
    const info = {
      supplier_name: row.supplier_name,
      grey_fabric_code: row.grey_fabric_code,
      grey_fabric_name: row.grey_fabric_name,
      gray_fabric_color_name: row.gray_fabric_color_name,
      grey_fabric_level_name: row.grey_fabric_level_name,
      yarn_batch: row.yarn_batch,
      machine_number: row.machine_number,
      customer_name: row.customer_name,
    }
    AddInventoryXimaDialogRef.value.state.filterData = filterData
    AddInventoryXimaDialogRef.value.state.info = info
    AddInventoryXimaDialogRef.value.state.canEnter = row.roll
    AddInventoryXimaDialogRef.value.state.showModal = true
    AddInventoryXimaDialogRef.value.state.rowIndex = rowIndex
    AddInventoryXimaDialogRef.value.state.ximaList = listData || []
  }
  else {
    AddXimaDialogRef.value.state.showModal = true
    AddXimaDialogRef.value.state.tableData = listData || []
    AddXimaDialogRef.value.state.canEnter = formatPriceDiv(row.actually_roll)
    AddXimaDialogRef.value.state.horsepower = formatPriceDiv(row.actually_roll)
    AddXimaDialogRef.value.state.code = row.grey_fabric_code
    AddXimaDialogRef.value.state.name = row.grey_fabric_name
    AddXimaDialogRef.value.state.isDisabled = true
  }
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateGfmStockCheckOrderStatusPass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateGfmStockCheckOrderStatusWait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="盘点单位:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.check_unit_id"
              :query="{ name: componentRemoteSearch.unit_name }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="盘点日期:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              multiple
              api="getAuditStatusEnums"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.order_type"
              api="GetGfmCheckOrderTypeEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button
          v-has="'GreyClothInventorySheet_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="'GreyClothInventorySheet_export'"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="getInfomation(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #roll="{ row }">
          <div
            v-if="formatPriceDiv(row.total_result_roll) > 0"
            class="text-[#3fc191]"
          >
            {{ formatPriceDiv(row.total_result_roll) }}
          </div>
          <div
            v-else-if="formatPriceDiv(row.total_result_roll) < 0"
            class="text-[#ed90bd]"
          >
            {{ formatPriceDiv(row.total_result_roll) }}
          </div>
          <div v-else-if="row.total_result_roll === 0">
            {{ row.total_result_roll }}
          </div>
        </template>
        <template #weight="{ row }">
          <div
            v-if="formatWeightDiv(row.total_result_weight) > 0"
            class="text-[#3fc191]"
          >
            {{ formatWeightDiv(row.total_result_weight) }}
          </div>
          <div
            v-else-if="formatWeightDiv(row.total_result_weight) < 0"
            class="text-[#ed90bd]"
          >
            {{ formatWeightDiv(row.total_result_weight) }}
          </div>
          <div v-else-if="row.total_result_weight === 0">
            {{ row.total_result_weight }}
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'GreyClothInventorySheet_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'GreyClothInventorySheet_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'GreyClothInventorySheet_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'GreyClothInventorySheet_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="columnList_fabic_config"
        :table-list="fabricList?.item_data"
        :column-list="columnList_fabic"
      >
        <template #result_roll="{ row }">
          <span :class="getTableCellTextClass(formatRollDiv(row.result_roll))">
            {{ formatNumberPrefix(formatRollDiv(row.result_roll)) }}
          </span>
        </template>
        <template #result_weight="{ row }">
          <span :class="getTableCellTextClass(formatWeightDiv(row.result_weight))">
            {{ formatNumberPrefix(formatWeightDiv(row.result_weight)) }}
          </span>
        </template>
        <template #xima="{ row, rowIndex }">
          <el-button type="primary" text link @click="handSeeXima(row, rowIndex)">
            查看
          </el-button>
        </template>
      </Table>
    </FildCard>
  </div>
  <AddInventoryXimaDialog ref="AddInventoryXimaDialogRef" modal-name="细码详情" :is-edit="false" />
  <AddXimaDialog ref="AddXimaDialogRef" modal-name="细码详情" />
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
