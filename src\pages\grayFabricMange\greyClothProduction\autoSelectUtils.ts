import currency from 'currency.js'
import { formatWeightDiv } from '@/common/format'

/**
 * 自动选择原料并分摊用纱量
 * @param options 配置选项
 * @returns 选择后的列表
 */
export async function autoSelectYarn(options: {
  // 原料列表
  originList: any[]
  // 需要分摊的总量
  totalWeight: number
  // 父级ID
  parentId?: number | string
  // 灰布ID
  grayId?: number | string
  // 其他需要传递的数据
  extraData?: Record<string, any>
}) {
  const { originList, totalWeight, parentId, grayId, extraData = {} } = options

  // 清空当前已选择的数据
  const selectList: any[] = []

  // 按照total_weight从大到小排序，优先选择库存量大的原料
  // const sortedList = [...originList].sort((a, b) => {
  //   return formatWeightDiv(b.total_weight) - formatWeightDiv(a.total_weight)
  // })

  // 需要分摊的总量
  let remainingWeight = totalWeight

  // 遍历排序后的列表，逐个选择直到达到需要的用纱量
  for (let i = 0; i < originList.length; i++) {
    const item = originList[i]

    // 如果已经达到需要的用纱量，则停止选择
    if (remainingWeight <= 0)
      break

    // 计算当前项可以分配的最大用纱量
    const total_weight = formatWeightDiv(item.total_weight)

    // 计算实际分配的用纱量（不超过剩余需要量和当前项的库存量）
    const useYarnQuantity = remainingWeight > total_weight ? total_weight : remainingWeight

    // 将当前项添加到已选列表
    selectList.push({
      id: item.id,
      ...item,
      rml_stock_id: item.id,
      parent_id: parentId,
      grayId,
      use_yarn_quantity: useYarnQuantity,
      total_weight,
      max_use_yarn_quantity: useYarnQuantity,
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      unit_name: item.unit_name,
      supplier_name: item.supplier_name,
      customer_name: item.customer_name,
      brand: item.brand,
      batch_num: item.batch_num,
      color_scheme: item.color_scheme,
      level_name: item.level_name,
      raw_remark: item.remark,
      production_date: item.production_date,
      cotton_origin: item.cotton_origin,
      remark: item.remark,
      spinning_type: item.spinning_type,
      carton_num: item.carton_num,
      fapiao_num: item.fapiao_num,
      ...extraData,
    })

    // 更新剩余需要分摊的用纱量
    remainingWeight = currency(remainingWeight).subtract(useYarnQuantity).value
  }

  return selectList
}
