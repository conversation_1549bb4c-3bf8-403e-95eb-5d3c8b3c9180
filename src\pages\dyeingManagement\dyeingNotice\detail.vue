<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { nextTick, onMounted, reactive, ref } from 'vue'
import OrderMore from './components/OrderMore.vue'
import { handleCraftRequirement } from './share'
import {
  dyeing_and_finishingCancel,
  dyeing_and_finishingdetail,
  dyeing_and_finishingpass,
  dyeing_and_finishingreject,
  dyeing_and_finishingvoid,
} from '@/api/dyeingNotice'
import FildCard from '@/components/FildCard.vue'
import { deepClone, orderStatusConfirmBox } from '@/common/util'
import StatusColumn from '@/components/StatusColumn/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'

const rourte = useRoute()

const state = reactive<any>({
  moreItems: [],
})

const { fetchData, data } = dyeing_and_finishingdetail()

const OrderMoreRef = ref()

onMounted(async () => {
  await fetchData({ id: rourte.query.id })
  await nextTick(() => {
    if (data.value) {
      const obj = deepClone(handleCraftRequirement(data.value))
      obj.items.map((item: any) => {
        // item.change_piece_count = formatPriceDiv(item.change_piece_count)
        // item.change_weight = formatWeightDiv(item.change_weight)

        item.use_fabric = item.use_fabric?.map((it) => {
          // 坯布染整
          if (item.final_src_type === 1) {
            return {
              ...it, // 展开整个it对象
              ...it.gf_stock_info,
              all_weight: it.weight,
              produce_order_no: it.gf_stock_info?.product_code,
              stock_roll: it.gf_stock_info?.num,
            }
          }
          // 成品加工/回修
          else {
            return {
              ...it, // 展开整个it对象
              ...it.f_out_info,
              all_weight: it?.weight,
              total_roll: it.f_out_info?.piece_count,
              fabric_remark: it.f_out_info?.remark,
            }
          }
        })
        return item
      })
      state.moreItems = obj.items

      OrderMoreRef.value.state.detail = obj
      setTimeout(() => {
        OrderMoreRef.value.toggleExpandChangeEvent({ expanded: true, row: state.moreItems[0], rowIndex: 0 })
      }, 60)
    }
  })
})

async function updateStatus(audit_status: number) {
  const id: any = Number(rourte.query.id)
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: dyeing_and_finishingvoid,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: dyeing_and_finishingreject,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: dyeing_and_finishingpass,
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: {
        desc: '点击消审后订单将变为待审核状态',
        title: '是否消审该订单？',
      },
      api: dyeing_and_finishingCancel,
    })
  }
  fetchData({ id })
}

const router = useRouter()

function onModification() {
  if (data.value.dnf_type === 1) {
    router.push({
      name: 'DyeingChangeAdd',
      query: { id: Number(rourte.query.id) },
    })
  }
  else {
    router.push({
      name: 'ProcessChangeAdd',
      query: { id: Number(rourte.query.id) },
    })
  }
}
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <StatusColumn
      :order_no="data.order_no"
      :order_id="Number(rourte.query.id)"
      :status="data.status"
      :status_name="data.status_name"
      permission_wait_key="DyeingNoticeCancel"
      permission_reject_key="DyeingNotice_reject"
      permission_pass_key="DyeingNoticeAudit"
      permission_cancel_key="DyeingNotice_void"
      permission_change_key="DyeingChangeAdd"
      permission_edit_key="DyeingNoticeEdit_edit"
      edit_router_name="DyeingNoticeEdit"
      show-change-btn
      @eliminate="updateStatus"
      @reject="updateStatus"
      @cancel="updateStatus"
      @audit="updateStatus"
      @on-modification="onModification"
    >
      <template #print>
        <PrintPopoverBtn
          :id="rourte.query.id"
          :print-type="PrintType.PrintTemplateTypeDNFNotify"
          :data-type="PrintDataType.Product"
          api="dyeing_and_finishingdetail"
        />
      <!--      <PrintBtn -->
      <!--        type="dyeingNoticeDetail" -->
      <!--        api="dyeing_and_finishingdetail" -->
      <!--        :tid="1717812886130944" -->
      <!--        :id="rourte.query.id" -->
      <!--      /> -->
      </template>
      <template #custom>
        <div
          class="mr-[90px] ml-[40px] justify-center flex flex-col items-center"
        >
          <div class="mb-[10px] font-medium">
            染整类型
          </div>
          <div class="text-[14px]">
            {{ data.dnf_type_name }}
          </div>
        </div>
      </template>
    </StatusColumn>
    <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            {{ data?.sale_system_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂名称:">
          <template #content>
            {{ data?.dye_factory_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂跟单:">
          <template #content>
            {{ data?.order_follower_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跟单电话:">
          <template #content>
            {{ data?.order_follower_phone }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整日期:">
          <template #content>
            {{ data?.dnf_date }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算方式:">
          <template #content>
            {{ data?.settle_type_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="回货地址:">
          <template #content>
            {{ data?.return_address }}
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <OrderMore
      ref="OrderMoreRef"
      anchor
      :table-list="state.moreItems"
      :is-dyeing="data.dnf_type === 1"
    />
  </div>
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
