import { h } from 'vue'
import { <PERSON><PERSON>utton, ElSwitch } from 'element-plus'
import { ActiveStatusEnum } from '@/enum'
import { formatDate, formatTime } from '@/common/format'

export function createColumnList(params: any) {
  return [
    {
      sortable: true,
      field: 'id',
      title: '账户ID',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'company_name',
      title: '账套名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'contacts',
      title: '联系人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'phone',
      title: '联系电话',
    },
    {
      sortable: true,
      field: 'electronic_color_card_dead_line',
      title: '电子色卡有效期',
      width: 150,
      slots: {
        default: ({ row }: any) => {
          return formatDate(row.electronic_color_card_dead_line)
        },
      },
    },
    {
      sortable: true,
      field: 'deadline',
      title: '有效期',
      width: 150,
      slots: {
        default: ({ row }: any) => {
          return formatDate(row.deadline)
        },
      },
    },
    {
      field: 'electronic_color_card_status',
      title: '状态',
      width: 100,
      slots: {
        default: ({ row }: any) => {
          return h(ElSwitch, {
            activeText: '启用',
            inactiveText: '禁用',
            inlinePrompt: true,
            modelValue: row?.electronic_color_card_status === ActiveStatusEnum.Enable,
            onChange: async (val) => {
              params.changeStatus(row, val)
            },
          })
        },

      },
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '最后修改人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '最后修改时间',
      width: 150,
      slots: {
        default: ({ row }: any) => {
          return formatTime(row.update_time)
        },
      },
    },
    {
      field: 'operation',
      title: '操作',
      width: 150,
      fixed: 'right',
      slots: {
        default: ({ row }: { row: any }) => {
          return [
            h(
              ElButton,
              {
                type: 'primary',
                size: 'small',
                link: true,
                onClick: () => {
                  params.reCharge(row)
                },
              },
              '充值',
            ),
            h(
              ElButton,
              {
                type: 'primary',
                size: 'small',
                link: true,
                onClick: () => {
                  params.reChargeRecord(row)
                },
              },
              '充值记录',
            ),
            // h(
            //   ElButton,
            //   {
            //     type: 'primary',
            //     size: 'small',
            //     link: true,
            //     onClick: () => {
            //       params.consumeRecord(row)
            //     },
            //   },
            //   '消耗记录',
            // ),
          ]
        },
      },
    },
  ]
}
