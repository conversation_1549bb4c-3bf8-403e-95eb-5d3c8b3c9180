<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useMeasureWeightService } from './index'

// 测试配置
const testConfig = reactive({
  dataHead: '',
  dataEnd: '',
  useCustomDelimiter: false,
})

// 创建称重服务实例
const weightService = ref<any>(null)

// 连接状态
const isConnected = ref(false)
const logMessages = ref<string[]>([])

// 初始化服务
function initService() {
  const delimiterConfig = {
    dataHeader: testConfig.dataHead
      ? Array.from(testConfig.dataHead).map(char => char.charCodeAt(0))
      : [],
    dataFooter: testConfig.dataEnd
      ? Array.from(testConfig.dataEnd).map(char => char.charCodeAt(0))
      : [13, 10],
    useCustomDelimiter: testConfig.useCustomDelimiter,
  }

  weightService.value = useMeasureWeightService(delimiterConfig)
  isConnected.value = weightService.value.isConnected.value
  logMessages.value = weightService.value.logMessages.value
}

// 连接设备
async function connect() {
  if (!weightService.value)
    initService()

  try {
    await weightService.value.connectToSerialPort()
    isConnected.value = weightService.value.isConnected.value
  }
  catch (error) {
    console.error('连接失败:', error)
  }
}

// 断开连接
async function disconnect() {
  if (weightService.value) {
    await weightService.value.disconnectPort()
    isConnected.value = weightService.value.isConnected.value
  }
}

// 更新配置
function updateConfig() {
  if (weightService.value) {
    const delimiterConfig = {
      dataHeader: testConfig.dataHead
        ? Array.from(testConfig.dataHead).map(char => char.charCodeAt(0))
        : [],
      dataFooter: testConfig.dataEnd
        ? Array.from(testConfig.dataEnd).map(char => char.charCodeAt(0))
        : [13, 10],
      useCustomDelimiter: testConfig.useCustomDelimiter,
    }

    weightService.value.updateDelimiterConfig(delimiterConfig)
  }
}

// 预设配置
function applyPreset(preset: string) {
  switch (preset) {
    case 'default':
      testConfig.dataHead = ''
      testConfig.dataEnd = ''
      testConfig.useCustomDelimiter = false
      break
    case 'stx-etx':
      testConfig.dataHead = String.fromCharCode(0x02) // STX
      testConfig.dataEnd = String.fromCharCode(0x03) // ETX
      testConfig.useCustomDelimiter = true
      break
    case 'custom-string':
      testConfig.dataHead = 'START'
      testConfig.dataEnd = 'END'
      testConfig.useCustomDelimiter = true
      break
    case 'hex-delim':
      testConfig.dataHead = String.fromCharCode(0xAA, 0xBB)
      testConfig.dataEnd = String.fromCharCode(0xCC, 0xDD)
      testConfig.useCustomDelimiter = true
      break
  }
  updateConfig()
}

// 测试数据解析
function testParse() {
  if (weightService.value)
    weightService.value.testParseData()
}

// 清空日志
function clearLog() {
  if (weightService.value)
    weightService.value.clearLogMessages()
}
</script>

<template>
  <div class="test-container">
    <h2>useMeasureWeightService 数据头尾测试</h2>

    <!-- 配置区域 -->
    <el-card title="配置设置">
      <el-form :model="testConfig" label-width="120px">
        <el-form-item label="数据头:">
          <el-input v-model="testConfig.dataHead" placeholder="输入数据头字符串" />
        </el-form-item>

        <el-form-item label="数据尾:">
          <el-input v-model="testConfig.dataEnd" placeholder="输入数据尾字符串" />
        </el-form-item>

        <el-form-item label="启用自定义:">
          <el-switch v-model="testConfig.useCustomDelimiter" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="updateConfig">
            更新配置
          </el-button>
          <el-button @click="initService">
            重新初始化
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预设配置 -->
    <el-card title="预设配置" class="mt-4">
      <el-space>
        <el-button @click="applyPreset('default')">
          默认 (\r\n)
        </el-button>
        <el-button @click="applyPreset('stx-etx')">
          STX/ETX
        </el-button>
        <el-button @click="applyPreset('custom-string')">
          自定义字符串
        </el-button>
        <el-button @click="applyPreset('hex-delim')">
          十六进制分割符
        </el-button>
      </el-space>
    </el-card>

    <!-- 连接控制 -->
    <el-card title="连接控制" class="mt-4">
      <el-space>
        <el-button
          type="primary"
          :disabled="isConnected"
          @click="connect"
        >
          连接设备
        </el-button>
        <el-button
          type="danger"
          :disabled="!isConnected"
          @click="disconnect"
        >
          断开连接
        </el-button>
        <el-button @click="testParse">
          测试解析
        </el-button>
        <el-button @click="clearLog">
          清空日志
        </el-button>
      </el-space>

      <div class="status-info mt-2">
        <el-tag :type="isConnected ? 'success' : 'danger'">
          {{ isConnected ? '已连接' : '未连接' }}
        </el-tag>
      </div>
    </el-card>

    <!-- 当前配置显示 -->
    <el-card title="当前配置" class="mt-4">
      <pre>{{ JSON.stringify(testConfig, null, 2) }}</pre>
    </el-card>

    <!-- 日志显示 -->
    <el-card title="日志信息" class="mt-4">
      <div class="log-container">
        <div v-for="(msg, index) in logMessages" :key="index" class="log-item">
          {{ msg }}
        </div>
        <div v-if="logMessages.length === 0" class="no-logs">
          暂无日志信息
        </div>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.no-logs {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
