import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取汇总库存列表
export function GetStockProductList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出汇总库存列表
export function ExportStockProductList(nameFile) {
  return useDownLoad({
    url: '/admin/v1/product/stockProduct/getStockProductList',
    method: 'get',
    nameFile,
  })
}

// 获取缸号库存列表
export function GetStockProductDyelotNumberList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDyelotNumberList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出缸号库存列表
export function ExportStockProductDyelotNumberList(nameFile) {
  return useDownLoad({
    url: '/admin/v1/product/stockProduct/getStockProductDyelotNumberList',
    method: 'get',
    nameFile,
  })
}

// 获取细码库存列表
export function GetStockProductDetailList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出细码库存列表
export function ExportStockProductDetailList(nameFile) {
  return useDownLoad({
    url: '/admin/v1/product/stockProduct/getStockProductDetailList',
    method: 'get',
    nameFile,
  })
}

// 获取显示方式枚举下拉
export function GetStockShowType() {
  return useRequest({
    url: '/admin/v1/product/enum/getStockShowType',
    method: 'get',
  })
}

// 出入库记录（细码）
export function GetDetailStockProductOutAndInDetailList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getDetailStockProductOutAndInDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 出入库记录（缸号）
export function GetDyeNumberAndColorStockProductOutAndInDetailList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getDyeNumberAndColorStockProductOutAndInDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 出入库记录（库存）
export function GetSumStockProductOutAndInDetailList() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getSumStockProductOutAndInDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 库存状态(下拉)
export function GetStockStatusEnum() {
  return useRequest({
    url: '/admin/v1/product/enum/getStockStatusEnum ',
    method: 'get',
  })
}

// 通过stock_id查询成品详细信息--占用库存
export function GetStockProductDetailInfo() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDetailInfo',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
