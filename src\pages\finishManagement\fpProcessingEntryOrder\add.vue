<script setup lang="ts" name="FpProcessingEntryOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import type { VxeInputDefines } from 'vxe-pc-ui'
import currency from 'currency.js'
import { groupBy } from 'lodash-es'
import AccordingdyePlanAdd from '../components/AccordingdyePlanAdd.vue'
import FineSizeAdd from '../components/FineSizeRepertoryAdd.vue'
import RollInfo from './components/rollInfo.vue'
import { autoEnterData } from './common'
import { addFpmProcessInOrder, getDyeingAndFinishing } from '@/api/fpProcessingEntryOrder'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { formatDate, formatLengthMul, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deleteToast, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { AddColumn } from '@/pages/finishManagement/fpProcessingEntryOrder/column/addColumn'
import { isFinishEnter } from '@/pages/finishManagement/finishPurchaseWarehouseEntry/utils'
import { formValidatePass } from '@/common/rule'
import { processDataOut } from '@/common/handBinary'

defineOptions({
  name: 'FpProcessingEntryOrderAdd',
})
const routerList = useRouterList()
const AccordingdyePlanAddRef = ref()
const FineSizeAddRef = ref()
const formRef = ref()

const distributableGroup = ref<any[]>([])

const form: any = ref({
  sale_system_id: '',
  process_unit_id: '',
  warehouse_id: '',
  store_keeper_id: '',
  voucher_number: '',
  remark: '',
})
form.value.warehouse_in_time = new Date()

const state = reactive<any>({
  item_bum_data: [],
  rowIndex: 0,
  rowQuery: {},
  // 坯布染整
  rowDetail1: false,
  // 成品加工 成品回修
  rowDetail2: false,
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', validator: formValidatePass.sale_system_id(), trigger: 'change' }],
    process_unit_id: [{ required: true, message: '请选择加工单位', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库', trigger: 'change' }],
    warehouse_in_time: [{ required: true, message: '请选择进仓日期', trigger: 'change' }],
  },
  firstDetail: false,
  secondDetail: false,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

const currentRow = ref<any>({})

const { fetchData: fetchDataList, data: dataList } = getDyeingAndFinishing()
const rollInfoRef = ref()

let uuid = 0
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    // showSpanHeader: true,
    showCheckBox: true,
    // fieldApiKey: 'fpProcessingEntryOrderAdd', // columnList的 childrenList 加了之后有问题 vxe官方问题
    // showOperate: true,
    showSlotNums: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    fieldApiKey: 'FpProcessingEntryOrderEdit',
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['use_gf_weight'].includes(column.field))
          return sumNum(data, 'use_gf_weight')

        if (['in_weight'].includes(column.field))
          return sumNum(data, 'in_weight')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight')

        if (['dye_delivery_order_weight'].includes(column.field))
          return sumNum(data, 'dye_delivery_order_weight')

        if (['use_gf_roll', 'wait_deducted_roll', 'able_use_gf_roll', 'able_use_gf_weight'].includes(column.field))
          return sumNum(data, column.field)

        return null
      }),
    ]
  },
  datalist: [],
  columnList: AddColumn,
  // 确认添加带出数据
  handleSure: async (list: any) => {
    const group = groupBy(list, 'order_id')

    // 可以使用日期的判断方式去自动填入用坯匹数和用坯数量
    // const canUseDateEnter = ref(false)
    const entries = Object.entries(group)
    let resultList: any[] = []
    for await (const [key, values] of entries) {
      await fetchDataList({
        id: key, // order_id
      })
      const tempList = values.map((value) => {
        addDistributableGroupItem({
          item_id: value.item_id,
          not_rtn_piece_count: value.not_rtn_piece_count,
          not_rtn_weight: value.not_rtn_weight,
        })

        const target = dataList.value.items?.find((item: any) => item.id === value.item_id)

        if (!target) {
          // ElMessage.info('新增时未找到该成品')
          return
        }
        // const temp = target?.use_fabric
        let item_bum_data = target.use_fabric.map((item: any) => {
          // if (index > 0 && temp) {
          //   // 如果上一条数据的出坯日期和当前数据的出坯日期不一致，可以使用日期的判断方式去自动填入用坯匹数和用坯数量
          //   if (item.gf_stock_info?.source_time && temp[index - 1].gf_stock_info.source_time && item.gf_stock_info.source_time !== temp[index - 1].gf_stock_info.source_time)
          //     canUseDateEnter.value = true
          // }
          return {
            ...item,
            back_use_manage_id: item.id,
            use_roll: 0,
            use_weight: 0,
            uniqueId: `${value.item_id}_${item.id}`,
          }
        })
        item_bum_data = processDataOut([...item_bum_data])
        // const able_use_gf_roll = value.dnf_type === DyeingTypeEnum.GrayFabricDyeing ? value.not_rtn_gf_piece_count : value.not_rtn_piece_count // 可分配匹数
        // const able_use_gf_weight = value.dnf_type === DyeingTypeEnum.GrayFabricDyeing ? value.not_rtn_gf_weight : value.not_rtn_weight
        const able_use_gf_roll = value.total_not_rtn_use_roll // 可分配匹数
        const able_use_gf_weight = value.total_not_rtn_use_weight
        value = {
          ...value,
          use_fabric: target?.use_fabric,
          item_id: value.item_id,
          in_roll: value.in_roll || 0,
          uuid: ++uuid,
          isAllocated: false,
          product_code: value.code, // 成品编号
          product_name: value.name,
          product_id: value.product_id,
          customer_id: value.customer_id, // 所属客户
          customer_name: value.customer_name, // 所属客户
          quote_order_item_id: value.item_id, // 进度id
          quote_order_no: value.order_no,
          quote_order_id: value.order_id,
          quote_order_type: value.order_type,
          quote_order_type_name: value.order_type_name,
          dnf_type_name: value.dnf_type_name, // 源头单类型
          dnf_type: value.dnf_type, // 源头单类型
          product_level_id: value.level_id,
          product_color_code: value.color_no,
          // product_color_id: value.color_id, // 颜色id
          product_color_id: value?.product_color_id || 0, // 后端更改
          // finishProductionColorId: value.color_id,
          finishProductionColorId: value?.product_color_id || 0, // 后端更改
          product_color_name: value.color_name,
          dye_factory_color_code: value.df_color_no,
          dye_factory_dyelot_number: value.dyelot,
          product_width: value.width,
          product_gram_weight: value.gram_weight,
          product_craft: value.craft, // 成品工艺
          product_ingredient: value.ingredient, // 成品成分
          customer_account_num: value.style_no, // 款号
          contract_number: value.sale_plan_order_no, // 合同号
          unit_name: value.unit, // 单位
          unit_id: value.unit_id, // 单位id 后续补充
          wait_deducted_roll: value.not_rtn_piece_count, // 未扣匹数
          process_roll: value.not_rtn_piece_count + value.rtn_piece_count, // 加工匹数 = 未返匹数 + 已返匹数
          use_gf_roll: able_use_gf_roll,
          use_gf_weight: able_use_gf_weight,
          item_fc_data: [],
          in_weight: 0,
          weight_error: 0,
          settle_weight: 0,
          item_bum_data,
          able_use_gf_roll, // 可分配匹数
          able_use_gf_weight,
          selected: false,
          // dnf_type: 1, // 1坯布染整 2成品加工 3成品回修
        }
        // currentRow.value = value
        // // eslint-disable-next-line ts/no-use-before-define
        // autoEnterInstance.list.value = item_bum_data
        // // eslint-disable-next-line ts/no-use-before-define
        // autoEnterInstance.targetGroupItem.value = distributableGroup.value.find(item => item.item_id === value.quote_order_item_id)
        // // eslint-disable-next-line ts/no-use-before-define
        // autoEnterInstance.exec(() => {}, {
        //   canUseDateEnter: canUseDateEnter.value,
        // })

        return value
      })

      resultList = [...resultList, ...tempList]
    }
    resultList = autoEnterData(resultList) // 执行自动录入
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...resultList]
  },
  // 确认录入，需要统计部分字段
  handleSureFineSize: (list: any) => {
    //

    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 自动计算
    // 进仓数量 = 所有细码数量之和
    // 空差 = 所有细码空差之和
    // 结算数量 =  进仓数量-空差
    let in_weight = 0
    let in_length = 0
    let weight_error = 0
    list.forEach((item: any) => {
      in_length += Number(item.length)
      in_weight += Number(item.base_unit_weight)
      weight_error += Number(item.weight_error)
    })
    // const in_weight = list.reduce((pre: any, val: any) => pre + val.base_unit_weight, 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + val.weight_error, 0)
    const settle_weight = in_weight - weight_error
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_length = Number(in_length.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_weight = Number(in_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].in_roll = Number(sumNum(list, 'roll'))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number(settle_weight.toFixed(2))
    computedGeryInfo(finishProductionOptions.datalist[finishProductionOptions.rowIndex])
  },
  //   删除
  handleRowDel: async (row: any, rowIndex: number) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    finishProductionOptions.datalist.splice(rowIndex, 1)
    deleteDistributableGroup(row)
    rollInfoRef.value.tableList = rollInfoRef.value.tableList.filter(item => item.parentId !== row.quote_order_item_id)
    // if (row.item_id === currentRow.value.quote_order_item_id) {
    //   state.rowDetail1 = false
    //   state.rowDetail2 = false
    // }
  },
  //   批量操作
  handEdit: () => handEdit,
})

// 重新编写了自动录入逻辑-以前的先不用
// const autoEnterInstance = useAutoEnter(finishProductionOptions.datalist, currentRow, distributableGroup)
const autoEnterInstance = null

function deleteDistributableGroup(item: any) {
  const index = distributableGroup.value.findIndex((groupItem: any) => groupItem?.quote_order_id === item.quote_order_id && groupItem?.product_color_id === item.product_color_id)

  if (index > -1) {
    const { totalDistributablePieceCount, totalDistributableWeight } = distributableGroup.value[index]
    if (item.use_gf_roll < totalDistributablePieceCount)
      distributableGroup.value[index].totalDistributablePieceCount = currency(distributableGroup.value[index].totalDistributablePieceCount).subtract(item.use_gf_roll).value
    else
      distributableGroup.value[index].totalDistributablePieceCount = 0

    if (item.use_gf_weight < totalDistributableWeight)
      distributableGroup.value[index].totalDistributableWeight = currency(distributableGroup.value[index].totalDistributableWeight).subtract(item.use_gf_weight).value
    else
      distributableGroup.value[index].totalDistributableWeight = 0

    if (
      (distributableGroup.value[index].totalDistributableWeight === 0 && distributableGroup.value[index].totalDistributablePieceCount === 0)
      || !finishProductionOptions.datalist.some((listItem: any) =>
        distributableGroup.value.some((groupItem: any) => groupItem.quote_order_id === listItem.quote_order_id && groupItem.product_color_id === listItem.product_color_id),
      )
    )
      distributableGroup.value.splice(index, 1)
  }
}
// 表格选中事件
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请选择数据')

  bulkShow.value = true
}
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'product_level_id',
    field_name: 'product_level_id',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'in_roll',
    title: '进仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'customer_account_num',
    title: '款号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'contract_number',
    title: '合同号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'dye_delivery_order_no',
    title: '染厂送货单号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'use_gf_roll',
    title: '用坯匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'use_gf_weight',
    title: '用坯数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'dye_delivery_order_weight',
    title: '染厂单据数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'in_length',
    title: '进仓辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any, val: any) {
  //

  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid)
  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid)) {
      item[row.field] = value[row.field]
      if (row.field === 'finishProductionColorId') {
        item.product_color_name = val?.product_color_name
        item.product_color_code = val?.product_color_code
        item.product_color_id = val?.id
      }
    }
  })
  if (row.field)
    ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 根据资料添加
function showLibDialog() {
  AccordingdyePlanAddRef.value.state.showModal = true
  AccordingdyePlanAddRef.value.state.sureMultipleSelection = finishProductionOptions.datalist
}

// 默认生成出仓条数数据
function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.showDialog(row, form.value)
}

function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
}

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = addFpmProcessInOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 成品信息必填项
      // 判断戏码匹数和还有辅助数量和
      // let ximaLengthFlag = true // xima必须和进仓匹数一致
      // let lengthFlag = true // 辅助数量和与分录行辅助数量不一致
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        const item = finishProductionOptions.datalist[i]
        if (!item.product_code)
          return ElMessage.error('成品编号为必填项')

        if (!item.product_name)
          return ElMessage.error('成品名称为必填项')

        if (!item.product_color_id)
          return ElMessage.error('颜色、色号为必填项')

        // if (!finishProductionOptions.datalist[i].product_color_code) {
        //   return ElMessage.error('色号为必填项')
        // }
        if (!item.dye_factory_dyelot_number)
          return ElMessage.error(`[${item.product_code}]-染厂缸号为必填项`)

        if (item.in_roll === '')
          return ElMessage.error(`[${item.product_code}]-进仓匹数为必填项`)

        if (!Number(item.in_weight))
          return ElMessage.error(`[${item.product_code}]-进仓数量为必填项`)

        if (item.use_gf_roll === '')
          return ElMessage.error(`[${item.product_code}]-用坯匹数为必填项`)

        if (item.use_gf_roll > item.able_use_gf_roll)
          return ElMessage.error(`[${item.product_code}]-用坯匹数不能大于可分配匹数${item.able_use_gf_roll}匹`)

        if (item.use_gf_weight === '')
          return ElMessage.error(`[${item.product_code}]-用坯数量为必填项`)

        if (item.use_gf_weight > item.able_use_gf_weight)
          return ElMessage.error(`[${item.product_code}]-用坯数量不能大于可分配数量${item.able_use_gf_weight}kg`)

        if (finishProductionOptions.datalist[i]?.item_fc_data?.length) {
          let roll = 0
          let length = 0
          for (let ind = 0; ind < finishProductionOptions.datalist[i].item_fc_data.length; ind++) {
            roll += Number(finishProductionOptions.datalist[i].item_fc_data[ind].roll)
            length += Number(finishProductionOptions.datalist[i].item_fc_data[ind].length)
          }
          roll = Number(roll.toFixed(2))
          length = Number(length.toFixed(2))
          // const roll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          if (Number(finishProductionOptions.datalist[i].in_roll) !== roll)
            return ElMessage.error('进仓匹数与细码匹数总量不一致')

          // const length = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
          if (Number(finishProductionOptions.datalist[i].in_length) && Number(finishProductionOptions.datalist[i].in_length) !== length)
            return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
        }
        else {
          return ElMessage.error('进仓匹数与细码匹数总量不一致')
        }
      }
      // if (!ximaLengthFlag) {
      //   return ElMessage.error('进仓匹数与细码匹数总量不一致')
      // }
      // if (!lengthFlag) {
      //   return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
      // }
      // 整理参数
      //

      const query = {
        ...form.value,
        store_keeper_id: form.value.store_keeper_id || 0,
        warehouse_in_time: formatDate(form.value.warehouse_in_time),
        item_data: finishProductionOptions.datalist.map((item: any) => {
          const item_fc_data = item.item_fc_data.map((v: any) => {
            return {
              ...v,
              roll: formatTwoDecimalsMul(v.roll),
              length: formatLengthMul(v.length),
              weight_error: formatWeightMul(v.weight_error),
              paper_tube_weight: formatWeightMul(v.paper_tube_weight),
              base_unit_weight: formatWeightMul(v.base_unit_weight),
            }
          })
          const item_bum_data = item.item_bum_data.map((v: any) => {
            return {
              ...v,
              use_roll: v.use_roll === '' ? v.use_roll : formatTwoDecimalsMul(Number(v.use_roll)),
              use_weight: v.use_weight === '' ? v.use_weight : formatWeightMul(Number(v.use_weight)),
            }
          })
          return {
            ...item,
            dyeing_situ_id: item.id,
            in_roll: formatTwoDecimalsMul(Number(item.in_roll)),
            quote_roll: formatTwoDecimalsMul(Number(item.quote_roll)),
            in_weight: formatWeightMul(Number(item.in_weight)),
            weight_error: formatWeightMul(Number(item.weight_error)),
            settle_weight: formatWeightMul(Number(item.settle_weight)),
            quote_length: formatLengthMul(Number(item.quote_length)),
            in_length: formatLengthMul(Number(item.in_length)),
            use_gf_roll: formatTwoDecimalsMul(Number(item.use_gf_roll)),
            use_gf_weight: formatWeightMul(Number(item.use_gf_weight)),
            dye_delivery_order_weight: formatWeightMul(Number(item.dye_delivery_order_weight)),
            item_fc_data,
            item_bum_data,
          }
        }),
      }
      // 如果返布用坯情况，填写了匹数或数量，判断总和是否等于单元行的用坯匹数或用坯数量

      for (let i = 0; i < query.item_data.length; i++) {
        let use_gf_roll = 0
        let use_gf_weight = 0

        for (let index = 0; index < query.item_data[i].item_bum_data.length; index++) {
          if (query.item_data[i].item_bum_data[index].use_roll === '')
            return ElMessage.error('成品详情内匹数为必填项')

          if (query.item_data[i].item_bum_data[index].use_weight === '')
            return ElMessage.error('成品详情内数量为必填项')

          use_gf_roll += query.item_data[i].item_bum_data[index].use_roll
          use_gf_weight += query.item_data[i].item_bum_data[index].use_weight
        }

        if (use_gf_roll > 0 && query.item_data[i].use_gf_roll !== use_gf_roll)
          return ElMessage.error('返布用坯情况下填写匹数之和需要与单元行的用坯匹数相等')

        if (use_gf_weight > 0 && query.item_data[i].use_gf_weight !== use_gf_weight)
          return ElMessage.error('返布用坯情况下填写数量之和需要与单元行的用坯数量相等')
      }
      // let useRollFlag = true
      // let useWeightFlag = true
      // query.item_data.forEach((item: any) => {
      //   const use_gf_roll = item.item_bum_data.reduce((pre: number, val: any) => pre + val.use_roll, 0)
      //   const use_gf_weight = item.item_bum_data.reduce((pre: number, val: any) => pre + val.use_weight, 0)
      //   if (use_gf_roll > 0 && item.use_gf_roll !== use_gf_roll) {
      //     useRollFlag = false
      //   }
      //   if (use_gf_weight > 0 && item.use_gf_weight !== use_gf_weight) {
      //     useWeightFlag = false
      //   }
      // })
      // if (!useRollFlag) {
      //   return ElMessage.error('返布用坯情况下填写匹数之和需要与单元行的用坯匹数相等')
      // }
      // if (!useWeightFlag) {
      //   return ElMessage.error('返布用坯情况下填写数量之和需要与单元行的用坯数量相等')
      // }
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        routerList.push({
          name: 'FpProcessingEntryOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    //

    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

function showDetail(row: any) {
  routerList.push({
    name: 'DyeingNoticeDetail',
    query: { id: row.quote_order_id },
    close_self: false,
  })
  // currentRow.value = row
  // const raw = deepClone(row)
  // finishProductionOptions.rowIndex = finishProductionOptions.datalist.findIndex((item: any) => item.uuid === row.uuid)
  // state.rowQuery = {
  //   id: row.quote_order_id,
  //   in_roll: Number(row.in_roll) || 0,
  //   in_weight: Number(row.in_weight) || 0,
  //   item_id: row.quote_order_item_id, // 进度id
  // }
  // state.item_bum_data
  //   = row.item_bum_data?.map((item: any) => {
  //     item.uniqueId = `${row.item_id}_${item.back_use_manage_id}` //  唯一值
  //     return item
  //   }) || []
  // 染整类型：1坯布染整 2成品加工 3成品回修
  // if (row.dnf_type === 1) {
  //   state.rowDetail1 = true
  //   state.rowDetail2 = false
  // }
  // else {
  //   state.rowDetail1 = false
  //   state.rowDetail2 = true
  // }
}

// function handleFocus(_: VxeInputDefines.BlurParams, row: any) {
//   showDetail(row)
// }
// 成品信息-用坯匹数/数量输入失焦
function handleBlur(event: VxeInputDefines.BlurParams, row: any, type: any) {
  if (type === 'use_gf_roll') {
    if (Number(event.value) > row.able_use_gf_roll) {
      row.use_gf_roll = row.able_use_gf_roll
      ElMessage.error(`用坯匹数不能大于可分配匹数${row.able_use_gf_roll}匹`)
    }
  }
  else if (type === 'use_gf_weight') {
    if (Number(event.value) > row.able_use_gf_weight) {
      row.use_gf_weight = row.able_use_gf_weight
      ElMessage.error(`用坯数量不能大于可分配数量${row.able_use_gf_weight}kg`)
    }
  }
  row = autoEnterData([row])[0] // 重新自动录入当前成品-坯布

  rollInfoRef.value?.emitUpdateData([row])
  rollInfoRef.value?.updateFooter()
}

// 把可分配的染整 进行分组
function addDistributableGroupItem(item: any) {
  const index = distributableGroup.value.findIndex(groupItem => groupItem.item_id === item.item_id)
  if (index > -1) {
    distributableGroup.value[index].totalDistributablePieceCount = currency(distributableGroup.value[index].totalDistributablePieceCount).add(formatTwoDecimalsDiv(item.not_rtn_piece_count))
    distributableGroup.value[index].totalDistributableWeight = currency(distributableGroup.value[index].totalDistributableWeight).add(formatWeightDiv(item.not_rtn_weight))
    distributableGroup.value[index].prevTotalDistributablePieceCount = currency(distributableGroup.value[index].prevTotalDistributablePieceCount).add(formatTwoDecimalsDiv(item.not_rtn_piece_count))
    distributableGroup.value[index].prevTotalDistributableWeight = currency(distributableGroup.value[index].prevTotalDistributableWeight).add(formatWeightDiv(item.not_rtn_weight))
  }
  else {
    if (distributableGroup.value.length === 0 || distributableGroup.value.some(groupItem => groupItem.item_id !== item.item_id)) {
      distributableGroup.value.push({
        item_id: item.item_id,
        totalDistributablePieceCount: item.not_rtn_piece_count,
        prevTotalDistributablePieceCount: item.not_rtn_piece_count, // 上一个可分配匹数
        totalDistributableWeight: item.not_rtn_weight,
        prevTotalDistributableWeight: item.not_rtn_weight, // 上一个可分配数量
      })
    }
  }
}

// 同步对应的用坯情况
function computedGeryInfo(row: any) {
  row.item_bum_data.forEach((item: any) => {
    item.in_roll = Number(row.in_roll)
    item.in_weight = Number(row.in_weight)
  })
  rollInfoRef.value.emitUpdateData([row])
}
// 同步数据
function setItemBumData(item_bum_data: any, rowIndex: number) {
  finishProductionOptions.datalist[rowIndex].item_bum_data = item_bum_data

  // 同步汇总成品的的匹数/数量
  const use_gf_roll = item_bum_data.reduce((pre: number, val: any) => currency(pre).add(val.use_roll).value, 0)
  const use_gf_weight = item_bum_data.reduce((pre: number, val: any) => currency(pre).add(val.use_weight).value, 0)

  finishProductionOptions.datalist[rowIndex].use_gf_roll = use_gf_roll
  finishProductionOptions.datalist[rowIndex].use_gf_weight = use_gf_weight
}

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  form.value.sale_system_id = res?.default_sale_system_id
})

function changeSaleSystemId(item: any) {
  form.value.warehouse_id = item?.default_physical_warehouse || ''
}

// 切换加工单位
function changeProcessUnit() {
  finishProductionOptions.datalist = []
  finishProductionOptions.multipleSelection = []
  currentRow.value = {}
  state.rowDetail1 = false
  state.rowDetail2 = false
}
function handleInputDyelotNumber(row: any) {
  rollInfoRef.value.emitUpdateData([row])
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="!finishProductionOptions.datalist.length" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="form.sale_system_id" default-status api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable @change-value="changeSaleSystemId" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="加工单位名称:">
          <template #content>
            <el-form-item prop="process_unit_id">
              <SelectDialog
                v-model="form.process_unit_id"
                :query="{
                  unit_type_id: `${BusinessUnitIdEnum.dyeFactory}`,
                  name: componentRemoteSearch.name,
                }"
                api="business_unitlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.name = val)"
                @change-value="changeProcessUnit"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents v-model="form.warehouse_id" api="GetPhysicalWarehouseDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="进仓日期:">
          <template #content>
            <el-form-item prop="warehouse_in_time">
              <el-date-picker v-model="form.warehouse_in_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="form.store_keeper_id"
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:" copies="1">
          <template #content>
            <el-form-item prop="voucher_number">
              <el-input v-model="form.voucher_number" placeholder="凭证号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="form.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" :disabled="finishProductionOptions.multipleSelection.length <= 0" @click="handEdit">
        批量操作
      </el-button>
      <el-button :disabled="!form.process_unit_id" type="primary" @click="showLibDialog">
        根据染整进度添加
      </el-button>
    </template>
    <div v-show="finishProductionOptions.datalist.length">
      <Table ref="tablesRef" :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
        <!-- 染整单号 -->
        <template #quote_order_no="{ row }">
          <div class="flex items-center" @click="showDetail(row)">
            <span class="link_text">{{ row.quote_order_no }}</span>
            <!--            <el-icon :size="20" color="#409eff"> -->
            <!--              <Edit /> -->
            <!--            </el-icon> -->
          </div>
        </template>
        <!-- 单据类型 -->
        <template #quote_order_type_name="{ row }">
          <span>{{ row.quote_order_type_name }}({{ row.dnf_type_name }})</span>
        </template>
        <!-- 所属客户 -->
        <template #customer_name="{ row }">
          {{ row.customer_name }}
        </template>
        <!-- 色号 -->
        <template #product_color_code="{ row }">
          {{ row.product_color_code }}
        </template>
        <!-- 颜色 -->
        <template #product_color_id="{ row }">
          {{ row.product_color_name }}
        </template>
        <!-- 染厂色号 -->
        <template #dye_factory_color_code="{ row }">
          <vxe-input v-model="row.dye_factory_color_code" />
        </template>
        <!-- 染厂缸号 -->
        <template #dye_factory_dyelot_number="{ row }">
          <vxe-input v-model="row.dye_factory_dyelot_number" @input="handleInputDyelotNumber(row)" />
          <!-- {{ row.dye_factory_dyelot_number }} -->
        </template>
        <!-- 成品幅宽 -->
        <template #product_width="{ row }">
          {{ row.product_width }} {{ row.finish_product_width_unit_name }}
        </template>
        <!-- 成品克重 -->
        <template #product_gram_weight="{ row }">
          {{ row.product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
        </template>
        <!-- 成品等级 -->
        <template #product_level_id="{ row }">
          <SelectComponents v-model="row.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
        </template>
        <!-- 成品备注 -->
        <template #product_remark="{ row }">
          <vxe-input v-model="row.product_remark" maxlength="200" />
        </template>
        <!-- 进仓匹数 -->
        <template #in_roll="{ row }">
          <!--           :max="row.wait_deducted_roll" -->
          <vxe-input v-model="row.in_roll" type="float" @change="computedGeryInfo(row)" />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" maxlength="200" type="text" />
        </template>
        <!-- 凭证单号 -->
        <template #voucher_number="{ row }">
          <vxe-input v-model="row.voucher_number" maxlength="200" type="text" />
        </template>
        <!-- 款号 -->
        <template #customer_account_num="{ row }">
          <vxe-input v-model="row.customer_account_num" type="text" />
        </template>
        <!-- 合同号 -->
        <template #contract_number="{ row }">
          <vxe-input v-model="row.contract_number" type="text" />
        </template>
        <!-- 染厂送货单号 -->
        <template #dye_delivery_order_no="{ row }">
          <vxe-input v-model="row.dye_delivery_order_no" maxlength="50" clearable type="text" />
        </template>
        <!-- 用坯匹数 -->
        <template #use_gf_roll="{ row }">
          <!-- :max="row.able_use_gf_roll" -->
          <vxe-input v-model="row.use_gf_roll" type="float" @blur="handleBlur($event, row, 'use_gf_roll')" />
        </template>
        <!-- 用坯数量 -->
        <template #use_gf_weight="{ row }">
          <!-- :max="row.able_use_gf_weight" -->
          <vxe-input v-model="row.use_gf_weight" type="float" @blur="handleBlur($event, row, 'use_gf_weight')" />
        </template>
        <!-- 染厂单据数量 -->
        <template #dye_delivery_order_weight="{ row }">
          <vxe-input v-model="row.dye_delivery_order_weight" type="float" />
        </template>
        <!-- 进仓辅助数量 -->
        <template #in_length="{ row }">
          <vxe-input v-model="row.in_length" type="float" />
        </template>
        <!-- 细码 -->
        <template #xima="{ row, rowIndex }">
          <!-- 进仓数量 -->
          <el-button v-if="row.in_roll !== ''" type="text" @click="showFineSizeDialog(row, rowIndex)">
            录入
            <span v-if="isFinishEnter(row)" style="color: #ccc">(已录入)</span>
            <span v-else style="color: red">({{ row.in_roll - getSFRoll(row) }}条未录)</span>
          </el-button>
          <span v-else style="color: #ccc">请先输入进仓匹数</span>
        </template>
        <!-- 操作 -->
        <template #operate="{ row, rowIndex }">
          <el-button type="text" @click="finishProductionOptions.handleRowDel(row, rowIndex)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <div v-if="finishProductionOptions.datalist.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
    <!--    坯布染整 -->
    <!--    <RollInfo -->
    <!--      v-if="state.rowDetail1" -->
    <!--      ref="rollInfoRef" -->
    <!--      v-model:group="distributableGroup" -->
    <!--      class="mt-[20px]" -->
    <!--      :auto-enter-instance="autoEnterInstance" -->
    <!--      :row="currentRow" -->
    <!--      :item_bum_data="state.item_bum_data" -->
    <!--      :row-query="state.rowQuery" -->
    <!--      @set-item-bum-data="setItemBumData" -->
    <!--    /> -->
    <!--   成品加工 3成品回修 -->
    <!--    <RollInfo1 -->
    <!--      v-if="state.rowDetail2" -->
    <!--      ref="rollInfoRef1" -->
    <!--      v-model:group="distributableGroup" -->
    <!--      class="mt-[20px]" -->
    <!--      :auto-enter-instance="autoEnterInstance" -->
    <!--      :row="currentRow" -->
    <!--      :item_bum_data="state.item_bum_data" -->
    <!--      :row-query="state.rowQuery" -->
    <!--      @set-item-bum-data="setItemBumData" -->
    <!--    /> -->
  </FildCard>
  <RollInfo
    v-if="finishProductionOptions.datalist.length !== 0"
    ref="rollInfoRef"
    v-model:group="distributableGroup"
    :parent-list="finishProductionOptions.datalist"
    type="add"
    :auto-enter-instance="autoEnterInstance"
    :row="currentRow"
    :item_bum_data="state.item_bum_data"
    :row-query="state.rowQuery"
    @set-item-bum-data="setItemBumData"
  />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingdyePlanAdd
    ref="AccordingdyePlanAddRef"
    :multiple-selection="finishProductionOptions.datalist"
    :dye_factory_id="form.process_unit_id"
    @handle-sure="finishProductionOptions.handleSure"
  />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

::v-deep(.link_text) {
  color: #409eff;
  cursor: pointer;
}
</style>
