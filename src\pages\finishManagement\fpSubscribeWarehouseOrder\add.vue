<script setup lang="ts" name="FpSubscribeWarehouseOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import AccordingSubscribeStockAdd from '../components/AccordingSubscribeStockAdd.vue'
import { addFpmOutReservationOrder } from '@/api/fpSubscribeWarehouseOrder'
import { BusinessUnitIdEnum, EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import {
  formatDate,
  formatLengthMul,
  formatTwoDecimalsMul,
  formatWeightMul,
} from '@/common/format'
import {
  deleteToast,
  getDefaultSaleSystem,
  getFilterData,
} from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'
import { WarehouseGoodOutEnum } from '@/enum/productEnum'

const routerList = useRouterList()
const route = useRoute()
let uuid = 0
const AccordingSubscribeStockAddRef = ref()
const state = reactive<any>({
  formInline: {
    reservation_type_name: '',
    reservation_type_id: '',
    out_warehouse_id: '',
    sale_system_id: '',
    in_warehouse_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    reservation_time: new Date(),
    store_keeper_id: '',
    remark: '',
  },
  formRules: {
    sale_system_id: [
      { required: true, message: '请选择营销体系', trigger: 'change' },
    ],
    out_warehouse_id: [
      { required: true, message: '请选择调出仓库名称', trigger: 'change' },
    ],
    in_warehouse_id: [
      { required: true, message: '请选择调入仓库名称', trigger: 'change' },
    ],
    biz_unit_id: [
      { required: true, message: '请选择单位名称', trigger: 'change' },
    ],
    warehouse_id: [
      { required: true, message: '请选择仓库名称', trigger: 'change' },
    ],
    reservation_time: [
      { required: true, message: '请选择预约日期', trigger: 'change' },
    ],
  },
})
const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    fieldApiKey: fieldApiKeyList.FpSubscribeWarehouseOrderAdd,
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    filterStatus: false,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['reservation_weight'].includes(column.field))
          return sumNum(data, 'reservation_weight', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'F',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
          required: true,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
          required: true,
        },
      ],
    },
    {
      title: '成品信息',
      field: 'E',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'reservation_roll',
          soltName: 'reservation_roll',
          title: '预约匹数',
          minWidth: 100,
        },
      ],
    },
    {
      title: '库存信息',
      field: 'D',
      childrenList: [
        {
          field: 'available_roll',
          title: '可用匹数',
          minWidth: 100,
        },
        {
          field: 'available_weight',
          title: '可用数量',
          minWidth: 100,
        },
        {
          field: 'sum_stock_length',
          title: '库存辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '出仓数量信息',
      field: 'C',
      childrenList: [
        {
          field: 'reservation_weight',
          soltName: 'reservation_weight',
          title: '总数量',
          minWidth: 100,
        },

        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      title: '出仓辅助数量信息',
      field: 'B',
      childrenList: [
        {
          field: 'reservation_length',
          soltName: 'reservation_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      title: '单据备注信息',
      field: 'A',
      childrenList: [
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '操作',
      field: 'operate',
      fixed: 'right',
      childrenList: [
        {
          field: 'operate',
          soltName: 'operate',
          title: '操作',
          width: 100,
        },
      ],
    },
  ],
  handleSure: (list: any) => {
    list = list.map((item: any) => {
      return {
        // ...item,
        uuid: ++uuid,
        sum_stock_id: item.stock_product_id ? item.stock_product_id : item.id, // 后端要求改
        product_code: item.product_code,
        product_name: item.product_name,
        customer_name: item.customer_name,
        customer_id: item.customer_id,
        available_roll: item.available_roll,
        available_weight: item.available_weight,
        product_color_code: item.product_color_code,
        product_color_id: item.product_color_id,
        product_color_name: item.product_color_name,
        product_level_name: item.product_level_name,
        product_level_id: item.product_level_id,
        product_remark: item.product_remark,
        product_craft: item.finish_product_craft || item.product_craft,
        sum_stock_roll: item.stock_roll,
        sum_stock_weight: Number(item.weight),
        sum_stock_length: item.length,
        unit_name: item.measurement_unit_name,
        unit_id: item.measurement_unit_id,
        id: item.id,
        product_ingredient: item.finish_product_ingredient || item.product_ingredient,
        dye_factory_dyelot_number: item.dyelot_number,
        remark: '',
        product_id: item.product_id,
      }
    })
    finishProductionOptions.datalist = [
      ...finishProductionOptions.datalist || [],
      ...list,
    ]
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex(
      (item: any) => item.id === id,
    )
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit,
})

const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}

function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'reservation_roll',
    title: '预约匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'reservation_weight',
    title: '总数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'reservation_length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  const ids = finishProductionOptions.multipleSelection.map(
    (item: any) => item.uuid,
  )
  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid))
      item[row.field] = value[row.field]
  })
  ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}

const stock_warehouse_id = ref(0) // 查看库存传递的仓库id
function showLibDialog() {
  stock_warehouse_id.value = [WarehouseGoodOutEnum.InternalAllocate].includes(state.formInline.reservation_type_id) ? state.formInline.out_warehouse_id : state.formInline.warehouse_id

  AccordingSubscribeStockAddRef.value.state.showModal = true
}

onMounted(() => {
  state.formInline.reservation_type_name = route.query.reservation_type_name
  state.formInline.reservation_type_id = Number(route.query.reservation_type_id)
})

// 新增提交
const {
  fetchData: addFetch,
  data: successData,
  success: addSuccess,
  msg: addMsg,
} = addFpmOutReservationOrder()
const formRef = ref()
// 提交所有数据
function submitAddAllData() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!finishProductionOptions.datalist?.length)
        return ElMessage.error('请先添加成品信息')
      // let maxRollFlag = true
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号不能为空')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称不能为空')

        if (
          Number(finishProductionOptions.datalist[i].reservation_roll)
          > Number(finishProductionOptions.datalist[i].available_roll)
        )
          return ElMessage.error('预约匹数不可大于库存匹数')

        if (
          Number(finishProductionOptions.datalist[i].reservation_roll) === 0
          && Number(finishProductionOptions.datalist[i].reservation_length)
          === 0
          && Number(finishProductionOptions.datalist[i].reservation_weight) === 0
        )
          return ElMessage.error('至少填写预约匹数或出仓数量或辅助数量')
      }
      // if (!maxRollFlag) {
      //   return ElMessage.error('预约匹数不可大于库存匹数')
      // }
      const query = {
        ...state.formInline,
        reservation_time: formatDate(state.formInline.reservation_time),
        out_order_type: Number(state.formInline.reservation_type_id),
      }
      // 调出调入仓库不可相同
      if (
        query.in_warehouse_id
        && query.out_warehouse_id
        && query.in_warehouse_id === query.out_warehouse_id
      )
        return ElMessage.error('调出仓库和调入仓库不可相同')

      query.item_data = finishProductionOptions.datalist.map((item: any) => {
        return {
          ...item,
          available_roll: formatTwoDecimalsMul(item.available_roll),
          available_weight: formatWeightMul(item.available_weight),
          sum_stock_weight: formatWeightMul(item.sum_stock_weight),
          sum_stock_roll: formatTwoDecimalsMul(item.sum_stock_roll),
          reservation_roll: formatTwoDecimalsMul(item.reservation_roll),
          reservation_weight: formatWeightMul(item.reservation_weight),
          reservation_length: formatLengthMul(item.reservation_length),
        }
      })

      await addFetch({
        ...getFilterData(query),
      })
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        routerList.push({
          name: 'FpSubscribeWarehouseOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
        // 跳转到列表页
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

function clearCustomer(item: any) {
  state.formInline.in_warehouse_id = item?.default_physical_warehouse || ''
}
onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  state.formInline.sale_system_id = res?.default_sale_system_id
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button
        type="primary"
        :disabled="!finishProductionOptions.datalist.length"
        @click="submitAddAllData"
      >
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
        <DescriptionsFormItem label="预约类型:">
          <template #content>
            {{ state.formInline.reservation_type_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                default-status
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="clearCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem
          v-if="[WarehouseGoodOutEnum.InternalAllocate].includes(state.formInline.reservation_type_id)"
          label="调出仓库名称:"
          :required="true"
        >
          <template #content>
            <el-form-item prop="out_warehouse_id">
              <SelectComponents
                key="one"
                v-model="state.formInline.out_warehouse_id"
                api="GetPhysicalWarehouseDropdownList"
                :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                :disabled="!!state.formInline.out_warehouse_id && finishProductionOptions?.datalist?.length"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem
          v-if="[WarehouseGoodOutEnum.InternalAllocate].includes(state.formInline.reservation_type_id)"
          label="调入仓库名称:"
          :required="true"
        >
          <template #content>
            <el-form-item prop="in_warehouse_id">
              <SelectComponents
                key="two"
                v-model="state.formInline.in_warehouse_id"
                api="GetDropdownListWithoutDS"
                :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem
          v-if="[WarehouseGoodOutEnum.Other].includes(state.formInline.reservation_type_id)"
          label="收货单位名称:"
          :required="true"
        >
          <template #content>
            <el-form-item prop="biz_unit_id">
              <SelectBusinessDialog
                v-model="state.formInline.biz_unit_id"
                :query="{
                  unit_type_id: `${BusinessUnitIdEnum.customer},${BusinessUnitIdEnum.finishedProduct}`,
                }"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem
          v-if="
            [WarehouseGoodOutEnum.Processing, WarehouseGoodOutEnum.Other, WarehouseGoodOutEnum.Deduct, WarehouseGoodOutEnum.Repair].includes(state.formInline.reservation_type_id)
          "
          label="仓库名称:"
          :required="true"
        >
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents
                key="three"
                v-model="state.formInline.warehouse_id"
                :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
                :disabled="!!state.formInline.warehouse_id && finishProductionOptions?.datalist?.length"
                api="GetPhysicalWarehouseDropdownList"
                warehouse_type_id="finishProduction"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem
          v-if="[WarehouseGoodOutEnum.Processing, WarehouseGoodOutEnum.Repair].includes(state.formInline.reservation_type_id)"
          label="加工单位名称:"
          :required="true"
        >
          <template #content>
            <el-form-item prop="biz_unit_id">
              <SelectBusinessDialog
                v-model="state.formInline.biz_unit_id"
                api="BusinessUnitSupplierEnumlist"
                :query="{
                  unit_type_id: BusinessUnitIdEnum.dyeFactory,
                }"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem
          v-if="[WarehouseGoodOutEnum.Deduct].includes(state.formInline.reservation_type_id)"
          label="退货单位名称:"
          :required="true"
        >
          <template #content>
            <el-form-item prop="biz_unit_id">
              <SelectBusinessDialog
                v-model="state.formInline.biz_unit_id"
                api="BusinessUnitSupplierEnumlist"
                :query="{
                  unit_type_id: `${BusinessUnitIdEnum.dyeFactory},${BusinessUnitIdEnum.finishedProduct}`,
                }"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="预约日期:" :required="true">
          <template #content>
            <el-form-item prop="reservation_time">
              <el-date-picker
                v-model="state.formInline.reservation_time"
                type="date"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.store_keeper_id"
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea
                v-model="state.formInline.remark"
                style="width: 100%"
                maxlength="500"
                show-word-count
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="预约成品信息" class="mt-[10px]" :tool-bar="true">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button type="primary" :disabled="!state.formInline.out_warehouse_id && !state.formInline.warehouse_id" @click="showLibDialog">
        根据库存添加
      </el-button>
    </template>
    <Table
      ref="tablesRef"
      :config="finishProductionOptions.tableConfig"
      :table-list="finishProductionOptions.datalist"
      :column-list="finishProductionOptions.columnList"
    >
      <!-- 预约匹数 -->
      <template #reservation_roll="{ row }">
        <vxe-input
          v-model="row.reservation_roll"
          :max="row.available_roll"
          type="float"
        />
      </template>
      <!-- 出仓辅助数量 -->
      <template #reservation_length="{ row }">
        <vxe-input v-model="row.reservation_length" type="float" />
      </template>
      <!-- 总数量 -->
      <template #reservation_weight="{ row }">
        <vxe-input
          v-model="row.reservation_weight"
          :max="row.available_weight"
          type="float"
        />
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" type="text" />
      </template>
      <!-- 操作 -->
      <template #operate="{ row }">
        <el-button
          type="text"
          @click="finishProductionOptions.handleRowDel(row)"
        >
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  >
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingSubscribeStockAdd
    ref="AccordingSubscribeStockAddRef"
    :warehouse_id="stock_warehouse_id"
    @handle-sure="finishProductionOptions.handleSure"
  />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
