import {
  AddBizUnitFactoryLogistics,
  GetBizUnitFactoryLogisticsListByUintList,
  UpdateBizUnitFactoryLogistics,
} from '@/api/addressCard'

export function useAddressAPI() {
  const { fetchData: getAddress } = GetBizUnitFactoryLogisticsListByUintList()
  const { fetchData: addAddress } = AddBizUnitFactoryLogistics()
  const { fetchData: updateAddress } = UpdateBizUnitFactoryLogistics()

  async function fetchAddressList(customerId: number) {
    try {
      const result = await getAddress({ id: customerId })
      if (!result?.success)
        return { list: [], defaultAddress: null }
      const list = (result.data?.list || []).map((item: any) => ({
        ...item,
        location: typeof item.location === 'string' ? item.location.split(',') : item.location,
      }))

      const defaultAddress = list.find((item: any) => item.is_default)

      return {
        list,
        defaultAddress,
      }
    }
    catch (error) {
      console.error('获取地址列表失败:', error)
      return { list: [], defaultAddress: null }
    }
  }

  return {
    fetchAddressList,
    addAddress,
    updateAddress,
  }
}
