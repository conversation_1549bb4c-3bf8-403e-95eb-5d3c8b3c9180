type ModeType = 'development' | 'pre' | 'production' | 'test'

const modeList: Array<{ mode: ModeType, url: string }> = [
  { mode: 'development', url: 'https://test.zzfzyc.com/xima-ocr/' },
  { mode: 'pre', url: 'https://pre.zzfzyc.com/xima-ocr/' },
  { mode: 'production', url: 'https://www.zzfzyc.com/xima-ocr/' },
  { mode: 'test', url: 'https://test.zzfzyc.com/xima-ocr/' },
]
function GET_OCR_URL(sourceMode: ModeType) {
  const obj = modeList.find(item => item.mode === sourceMode)
  return obj?.url
}
// CDN
// 生成密钥
export const GET_UPLOAD_SIGN = `/upyun/getsign` // 请求签名 url
export const UPLOAD_CDN_URL = `//v0.api.upyun.com`
export const OCR_URL = GET_OCR_URL(import.meta.env.VITE_APP_IMAGE_MODE as ModeType)

const imgModeList = [
  { mode: 'development', url: 'https://testhcscm.cdn.zzfzyc.com' },
  { mode: 'pre', url: 'https://testhcscm.cdn.zzfzyc.com' },
  { mode: 'production', url: 'https://hcscm.cdn.zzfzyc.com' },
  { mode: 'test', url: 'https://testhcscm.cdn.zzfzyc.com' },
]
export function GET_IMG_CND_Prefix(sourceMode: ModeType) {
  const obj = imgModeList.find(item => item.mode === sourceMode)
  return obj?.url
}
// 前缀

const payUrlList = [
  { mode: 'development', url: 'https://test.zzfzyc.com/cashier?key=' },
  { mode: 'pre', url: 'https://pre.zzfzyc.com/cashier?key=' },
  { mode: 'production', url: 'https://www.zzfzyc.com/cashier?key=' },
  { mode: 'test', url: 'https://test.zzfzyc.com/cashier?key=' },
]

export const IMG_CND_Prefix = GET_IMG_CND_Prefix(import.meta.env.VITE_APP_IMAGE_MODE as ModeType)

function GET_PAY_URL(sourceMode: ModeType) {
  const obj = payUrlList.find(item => item.mode === sourceMode)
  return obj?.url
}

export const PAY_URL = GET_PAY_URL(import.meta.env.VITE_APP_IMAGE_MODE as ModeType)

// 上传图片视频
export const CDN_UPLOAD_IMG = `${UPLOAD_CDN_URL || ''}`
// // 手机号正则
// export const PHONE_REGEXP = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s]?[0-9]{4,6}$/
// // 正则验证
// export const verification = ({ scenes, val }: { scenes: 'phone'; val: string }) => {
//   const list = {
//     phone: /^[+]?[(]?[0-9]{3}[)]?[-s.]?[0-9]{3}[-s]?[0-9]{4,6}$/, // 手机号码
//   }
//   if (scenes && val) {
//     return list[scenes].test(val)
//   }
//   return false
// }

export const UNIT_PRICE_DIGIT = 2 // 单价输入框小数位
export const PRICE_DIGIT = 2 // 价格输入框小数位
export const WEIGHT_DIGIT = 2 // 重量输入框小数位

export function getBaseUrl() {
  if (isDevRun()) {
    const requestConfig = JSON.parse(localStorage.getItem('requestUrl') || '{}')
    return requestConfig?.url || import.meta.env.VITE_APP_BASE_API
  }
  return import.meta.env.VITE_APP_BASE_API
}

// 判断是否为本地开发run dev
export function isDevRun() {
  if (!import.meta.env.DEV)
    return false
  // 这里因为.env.development中设置为dev，所以新增判断ip是否为本地ip
  // 判断是否为本地ip,http且192.168开头且有端口
  const ip = window.location.hostname
  if ((ip === 'localhost' || ip === '127.0.0.1' || ip.startsWith('192.168')) && window.location.protocol === 'http:' && window.location.port)
    return true
  return false
}
