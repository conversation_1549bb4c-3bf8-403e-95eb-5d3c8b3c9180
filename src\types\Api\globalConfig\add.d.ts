declare namespace Api.GlobalConfigAdd {
  /**
   * system.AddAndUpdateResponse
   */
  export interface Response {
    /**
     * Id
     */
    id?: number
    [property: string]: any
  }
  /**
   * system.AddGlobalConfigParam
   */
  export interface Request {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 描述
     */
    description?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 配置名称
     */
    key?: string
    /**
     * 配置值
     */
    options?: string
    /**
     * 配置值预设值,逗号分割
     */
    options_presets?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 配置类型 1文本 2单选 3多选
     */
    type?: number
    /**
     * 配置类型名称
     */
    type_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
