t
<script setup lang="ts" name="SalesPriceAdjustmentOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { isEmpty } from 'lodash-es'
import { GetSaleLevelDropdownList } from '@/api/salePriceManagement'
import { AddSalePriceAdjustOrder } from '@/api/salesPriceAdjustmentOrder'
import { formatTime, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectCascader from '@/components/SelectCascader/productType.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import useRouterList from '@/use/useRouterList'
import SelectComponents from '@/components/SelectComponents/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  tableData: [],
})

const formRules = ref({
  effective_time: [{ required: true, message: '生效时间不能为空', trigger: 'change' }],
})

const formData = ref({
  effective_time: new Date(),
  deadline_time: '',
  remark: '',
  item_data: [],
  sale_system_id: '',
  dye_factory_id: '',
})

onMounted(async () => {
  await getLevelList()
  handSureDyeProduct()
})

const levelPriceColumn = ref<any[]>([])
const { fetchData: fetchDataList, data: dataLevel } = GetSaleLevelDropdownList()
async function getLevelList() {
  await fetchDataList()
  levelPriceColumn.value = dataLevel.value?.list || []
}

// const tableConfig = ref({
//   showSlotNums: true,
//   showOperate: true,
//   operateWidth: '80',
//   height: 400,
//   footerMethod: (val: any) => FooterMethod(val),
// })
const { fetchData: fetchDataAdd, data: addData, success: successAdd, msg: msgAdd } = AddSalePriceAdjustOrder()
const ruleFormRef = ref()
function handleSure() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid && checkData()) {
      const data = state.tableData?.map((item: any) => {
        const level_item = item.level_item?.map((citem: any) => {
          return {
            product_color_kind_id: item.product_color_kind_id || 0,
            product_id: item.product_id || 0,
            sale_level_id: citem.sale_level_id || 0,
            weight_error: formatWeightMul(citem.weight_error || 0),
            level_price_adjust: formatUnitPriceMul(citem.level_price_adjust || 0),
          }
        })
        return {
          level_item,
          adjust_bulk_sale_limit_price: formatUnitPriceMul(item.adjust_bulk_sale_limit_price || 0),
          adjust_bulk_sale_price: formatUnitPriceMul(item.adjust_bulk_sale_price || 0),
          product_color_kind_id: item.product_color_kind_id || 0,
          product_id: item.product_id || 0,
          remark: item.remark || '',
          target_length_cut_sale_price: formatUnitPriceMul(item.target_length_cut_sale_price || 0),
          target_weight_cut_sale_price: formatUnitPriceMul(item.target_weight_cut_sale_price || 0),
          weight_error: formatWeightMul(item.weight_error) || 0,
          product_kind_id: item.product_kind_id || 0,
        }
      })

      formData.value.effective_time = formData.value.effective_time ? formatTime(formData.value.effective_time) : ''
      formData.value.deadline_time = formData.value.deadline_time ? formatTime(formData.value.deadline_time) : ''

      await fetchDataAdd(getFilterData({ ...formData.value, item_data: data }))
      if (successAdd.value) {
        ElMessage.success('添加成功')
        routerList.push({
          name: 'SalesPriceAdjustmentOrderDetail',
          params: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

function formatColorName(list: any) {
  return list
    ?.map((item: any) => {
      return item.product_color_code + item.product_color_name
    })
    .join(';')
}

// 选择成品
function selectChange(val: any, row: any) {
  if (!isEmpty(val)) {
    row.product_kind_id = val?.type_grey_fabric_id ? val.type_grey_fabric_id : ''
    row.product_kind_name = val?.type_grey_fabric_name ? val.type_grey_fabric_name : ''
  }
  row.product_code = val.finish_product_code
  row.product_name = val.finish_product_name
  row.product_paper_tube_weight = val?.paper_tube_weight ? formatWeightDiv(val.paper_tube_weight) : ''
  row.product_weight_error = val?.weight_error ? formatWeightDiv(val.weight_error) : ''
  row.product_color_list = []
  row.product_color_kind_id = ''
  row.measurement_unit_name = val.measurement_unit_name
  row.measurement_unit_id = val.measurement_unit_id
}

const selectDialogRef = ref()
// 选择布种类别
function changeValue(val: any, row: any) {
  row.product_kind_id = val.ids && val.ids.length > 0 ? val.ids[val.ids.length - 1] : ''
  row.product_kind_name = val?.row?.name || ''
  // 清空成品信息
  row.product_id = ''
  row.product_code = ''
  row.product_name = ''

  row.product_paper_tube_weight = ''
  row.product_weight_error = ''
  row.product_color_list = []
  row.product_color_kind_id = ''
}

function selectChangeColorKind(val: any, row: any) {
  row.product_color_list = val?.product_color_list
  row.product_color_kind_name = val.name
}

function checkData() {
  let msg = ''
  if (!state.tableData || state.tableData.length <= 0)
    msg = '请添加调价信息'
  state.tableData?.some((item: any) => {
    if (!item.product_id)
      return (msg = '成品名称不能为空')

    if (!item.product_kind_id)
      return (msg = '布种类型不能为空')

    if (!item.product_color_kind_id)
      return (msg = '颜色类别不能为空')
  })
  if (Date.parse(formData.value.effective_time) >= Date.parse(formData.value.deadline_time))
    msg = '截止时间不能小于生效时间'

  if (Date.parse(new Date()) > Date.parse(formData.value.deadline_time))
    msg = '截止时间不能小于当前时间'

  if (msg) {
    ElMessage.error(msg)
    return false
  }
  else {
    return true
  }
}

function onDelete(rowIndex: number) {
  state.tableData?.splice(rowIndex, 1)
}

function handSureDyeProduct() {
  const level_item = levelPriceColumn.value?.map((item) => {
    return {
      level_price_adjust: '',
      product_color_kind_id: '',
      product_id: '',
      sale_level_id: item.id,
      weight_error: '',
    }
  })
  state.tableData.push({
    adjust_bulk_sale_limit_price: '',
    adjust_bulk_sale_price: '',
    level_item,
    product_color_kind_id: '',
    product_id: '',
    product_kind_id: '',
    remark: '',
    target_length_cut_sale_price: '',
    target_weight_cut_sale_price: '',
    weight_error: '',
  })
}
</script>

<template>
  <FildCard :tool-bar="false">
    <div class="flex justify-between">
      <el-form ref="ruleFormRef" :model="formData" :rules="formRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="生效时间:" required>
            <template #content>
              <el-form-item prop="effective_time">
                <SelectDate v-model="formData.effective_time" type="datetime" clearable style="width: 200px" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="截止时间:">
            <template #content>
              <el-form-item prop="deadline_time">
                <SelectDate v-model="formData.deadline_time" type="datetime" clearable style="width: 200px" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="deadline_time">
                <el-input v-model="formData.remark" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </div>
  </FildCard>
  <FildCard title="" :tool-bar="false" class="mt-[10px]">
    <template #right-top>
      <el-button type="primary" @click="handSureDyeProduct">
        新增
      </el-button>
    </template>
    <!--    <Table ref="tableRefs" :config="tableConfig" :tableList="state.tableData" :column-list="columnList"> -->
    <!--      <template #use_price="{ row }"> -->
    <!--        <vxe-input min="0" type="float" v-model="row.use_price"></vxe-input> -->
    <!--      </template> -->
    <!--      <template #operate="{ rowIndex }"> -->
    <!--        <el-button type="danger" link @click="onDelete(rowIndex)">删除</el-button> -->
    <!--      </template> -->
    <!--    </Table> -->
    <vxe-table show-overflow :edit-config="{ trigger: 'click', mode: 'row', autoClear: false }" :column-config="{ resizable: true }" border height="500" :data="state.tableData" size="mini">
      <vxe-colgroup title="成品颜色类别">
        <vxe-column field="product_kind_name" title="布种类别" width="100" fixed="left" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.product_kind_name }}
          </template>
          <template #edit="{ row }">
            <SelectCascader v-model="row.product_kind_id" @change-value="val => changeValue(val, row)" />
          </template>
        </vxe-column>
        <vxe-column field="product_code" title="成品编号" width="140" fixed="left" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.product_code }}
          </template>
          <template #edit="{ row }">
            <SelectDialog
              ref="selectDialogRef"
              v-model="row.product_id"
              class="custom-select"
              label-field="finish_product_code"
              :label-name="row.product_code"
              api="GetFinishProductDropdownList"
              :query="{ type_grey_fabric_id: row.product_kind_id, finish_product_code: row.product_code }"
              :column-list="[
                {
                  field: 'finish_product_name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'finish_product_name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'finish_product_code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'finish_product_code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'finish_product_code',
                  title: '成品编号',
                },
              ]"
              @change-value="val => selectChange(val, row)"
              @on-input="val => (row.product_code = val)"
            />
          </template>
        </vxe-column>
        <vxe-column field="product_name" title="成品名称" width="140" fixed="left" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.product_name }}
          </template>
          <template #edit="{ row }">
            <SelectDialog
              ref="selectDialogRef"
              v-model="row.product_id"
              class="custom-select"
              label-field="finish_product_name"
              :label-name="row.product_name"
              api="GetFinishProductDropdownList"
              :query="{ type_grey_fabric_id: row.product_kind_id, finish_product_name: row.product_name }"
              :column-list="[
                {
                  field: 'finish_product_name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'finish_product_name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'finish_product_code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'finish_product_code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'finish_product_name',
                  title: '成品名称',
                },
              ]"
              @change-value="val => selectChange(val, row)"
              @on-input="val => (row.product_name = val)"
            />
          </template>
        </vxe-column>
        <vxe-column field="product_paper_tube_weight" title="成品纸筒重量" width="100" fixed="left">
          <template #default="{ row }">
            {{ row.product_paper_tube_weight }}
          </template>
        </vxe-column>
        <vxe-column field="product_weight_error" title="成品空差数量" width="100" fixed="left">
          <template #default="{ row }">
            {{ row.product_weight_error }}
          </template>
        </vxe-column>
        <vxe-column field="product_color_kind_name" title="颜色类别" width="140" fixed="left" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.product_color_kind_name }}
          </template>
          <template #edit="{ row }">
            <SelectComponents v-model="row.product_color_kind_id" api="GetProductColorKindByProductId" :query="{ product_id: row.product_id }" label-field="name" value-field="id" clearable size="small" @change-value="val => selectChangeColorKind(val, row)" />
          </template>
        </vxe-column>
        <vxe-column field="product_color_list" title="色号颜色" width="80" fixed="left">
          <template #default="{ row }">
            {{ formatColorName(row.product_color_list) }}
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup title="单位">
        <vxe-column field="measurement_unit_name" title="主单位" width="60" />
      </vxe-colgroup>
      <vxe-colgroup title="标准报价">
        <vxe-column field="target_length_cut_sale_price" title="剪版价-非主单位" width="130" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.target_length_cut_sale_price }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.target_length_cut_sale_price" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="target_weight_cut_sale_price" title="剪版价-主单位" width="120" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.target_weight_cut_sale_price }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.target_weight_cut_sale_price" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="weight_error" title="空差减重" width="100" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.weight_error }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.weight_error" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="adjust_bulk_sale_price" title="标准售价" width="100" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.adjust_bulk_sale_price }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.adjust_bulk_sale_price" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="adjust_bulk_sale_limit_price" title="最低售价" width="100" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.adjust_bulk_sale_limit_price }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.adjust_bulk_sale_limit_price" type="float" digits="2" />
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup v-for="(item, index) in levelPriceColumn" :key="index" :title="item.name">
        <vxe-column field="weight_error_level" title="空差减重" width="100" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.level_item[index].weight_error }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.level_item[index].weight_error" type="float" digits="2" />
          </template>
        </vxe-column>
        <vxe-column field="level_price_adjust" title="售价" width="80" :edit-render="{ }">
          <template #default="{ row }">
            {{ row.level_item[index].level_price_adjust }}
          </template>
          <template #edit="{ row }">
            <vxe-input v-model="row.level_item[index].level_price_adjust" type="float" digits="2" />
          </template>
        </vxe-column>
      </vxe-colgroup>
      <vxe-colgroup>
        <vxe-column field="level_price_adjust" title="操作" width="137px" fixed="right">
          <template #default="{ rowIndex }">
            <el-button type="danger" link @click="onDelete(rowIndex)">
              删除
            </el-button>
          </template>
        </vxe-column>
      </vxe-colgroup>
    </vxe-table>
  </FildCard>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}

::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
::v-deep(.custom-select .vxe-input) {
  width: 100%;
}
</style>
