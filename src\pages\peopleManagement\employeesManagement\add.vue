<script setup lang="ts" name="EmployeesManagementAdd">
import { ElMessage } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import SelectQywxEmployee from './components/SelectQywxEmployee.vue'
import { GetEmployeeDetailApi, PostEmployeeAddApi, PutEmployeeAddApi } from '@/api/employees'
import { DictionaryType } from '@/common/enum'
import { PHONE_REGEXP } from '@/common/rule'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectCascader from '@/components/SelectCascader/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
const data = ref<any>({
  name: '',
  department_id: '',
  duty: '',
  sale_system_list: '',
  phone: '',
  email: '',
  is_blacklist: false,
  remark: '',
  address: '',
  birthday: '',
  identity_number: '',
  nation: '',
  marital_status: '',
  education_level: '',
  graduate_school: '',
  graduate_date: '',
  contract_date: '',
  resign_date: '',
  timecard_number: '',
  contract_end_date: '',
  contract_start_date: '',
})

const route = useRoute()

onMounted(() => {
  route.params.id && getData()
})

const selectedEmployees = ref<Api.GetQywxUsers.QywxGetQYWXUsersResponseItem[]>([])
const { fetchData, data: dataList } = GetEmployeeDetailApi()
async function getData() {
  await fetchData({ id: route.params.id })
  data.value = dataList.value
  if (dataList.value.qywx_user_id)
    selectedEmployees.value = [{ user_id: dataList.value.qywx_user_id, name: dataList.value.qywx_user_name }]
}

const rules = ref({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  department_id: [{ required: true, message: '请选择部门', trigger: 'blur' }],
  duty: [{ required: true, message: '职责', trigger: 'blur' }],
  phone: [
    {
      required: true,
      message: '请输入联系电话',
      trigger: 'blur',
    },
    {
      pattern: PHONE_REGEXP,
      message: '手机号格式不对',
      trigger: 'blur',
    },
  ],
  email: [{ type: 'email', message: '邮箱格式不正确' }],
  remark: [{ max: 100, message: '备注最多只能输入100字符' }],
  address: [{ max: 50, message: '最多只能输入15字符' }],
  identity_number: [{ max: 50, message: '最多只能输入15字符' }],
  nation: [{ max: 50, message: '最多只能输入15字符' }],
  timecard_number: [{ max: 50, message: '最多只能输入15字符' }],
  graduate_school: [{ max: 50, message: '最多只能输入15字符' }],
})
const formRef = ref()
const { fetchData: fetchDataEdit, data: editData, success: successEdit, msg: msgEdit } = PutEmployeeAddApi()
const { fetchData: fetchDataAdd, data: addData1, success: successAdd, msg: msgAdd } = PostEmployeeAddApi()
async function addData() {
  formRef.value!.validate(async (valid: any) => {
    if (valid) {
      if (route.params.id) {
        await fetchDataEdit(getFilterData({
          id: Number.parseInt(route.params.id as string),
          ...data.value,
          qywx_user_id: selectedEmployees.value?.[0]?.user_id || '',
          qywx_user_name: selectedEmployees.value?.[0]?.name || '',
        }))
        if (successEdit.value) {
          ElMessage({
            type: 'success',
            message: '编辑成功',
            showClose: true,
          })
          routerList.push({
            name: 'EmployeesManagementDetail',
            params: { id: editData.value.id },
          })
        }
        else {
          ElMessage({
            type: 'error',
            message: msgEdit.value,
            showClose: true,
          })
        }
      }
      else {
        await fetchDataAdd(getFilterData(data.value))
        if (successAdd.value) {
          ElMessage({
            type: 'success',
            message: '添加成功',
            showClose: true,
          })
          routerList.push({
            name: 'EmployeesManagementDetail',
            params: { id: addData1.value.id },
          })
        }
        else {
          ElMessage({
            type: 'error',
            message: msgAdd.value,
            showClose: true,
          })
        }
      }
    }
  })
}

watch(
  () => data.value.settle_type,
  (value) => {
    if (Number.parseInt(value) === 4)
      data.value.settle_cycle = 0
  },
)

watch(
  () => data.value.contract_date,
  (value) => {
    if (value && value?.length > 1) {
      data.value.contract_start_date = value[0]
      data.value.contract_end_date = value[1]
    }
  },
)

function getDepartment({ ids, row }: any) {
  if (ids && ids.length) {
    data.value.department_id = ids[ids.length - 1]
    data.value.sale_system_list = [row?.sale_system_id]
  }
}
function handleDeleteEmployee() {
  selectedEmployees.value = []
}
</script>

<template>
  <FildCard :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="addData">
        提交
      </el-button>
    </template>
    <div class="detail-container">
      <el-form ref="formRef" :model="data" :rules="rules" status-icon>
        <h2 style="font-weight: 700" class="mb-[20px]">
          基础信息
        </h2>
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="员工名称:">
            <template #content>
              <el-form-item prop="name">
                <el-input v-model="data.name" clearable placeholder="请输入员工名称" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="部门:">
            <template #content>
              <el-form-item prop="department_id">
                <!-- <SelectComponents api="GetDepartmentApi" label-field="name" value-field="id" v-model="data.department_id" clearable /> -->
                <SelectCascader v-model="data.department_id" style="width: 100%" @change-value="getDepartment" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="所属营销体系:">
            <template #content>
              <el-form-item prop="sale_system_list">
                <SelectComponents v-model="data.sale_system_list" api="GetSaleSystemDropdownListV2" multiple label-field="name" value-field="id" clearable />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="联系电话:">
            <template #content>
              <el-form-item prop="phone">
                <el-input v-model="data.phone" clearable placeholder="请输入联系电话" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="邮箱:">
            <template #content>
              <el-form-item prop="email">
                <el-input v-model="data.email" clearable placeholder="请输入邮箱" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="是否为黑名单:">
            <template #content>
              <el-form-item prop="is_blacklist">
                <el-switch v-model="data.is_blacklist" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="职责:">
            <template #content>
              <el-form-item prop="duty">
                <SelectComponents
                  v-model="data.duty"
                  style="width: 100%"
                  api="GetEmployeeDutyListApi"
                  :query="{ dictionary_id: DictionaryType.duty }"
                  multiple
                  label-field="name"
                  value-field="id"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <el-input v-model="data.remark" type="textarea" clearable placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="企微员工:" copies="2">
            <template #content>
              <SelectQywxEmployee v-model="selectedEmployees" @on-delete="handleDeleteEmployee" />
            </template>
          </DescriptionsFormItem>
        </div>
        <h2 style="font-weight: 700" class="mb-[20px]">
          扩展信息
        </h2>
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="地址:" copies="2">
            <template #content>
              <el-form-item prop="address">
                <el-input v-model="data.address" clearable placeholder="请输入地址" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="出生日期:">
            <template #content>
              <el-form-item prop="sale_radix_point_sign">
                <SelectDate v-model="data.birthday" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="身份证号:" copies="2">
            <template #content>
              <el-form-item prop="identity_number">
                <el-input v-model="data.identity_number" clearable placeholder="请输入身份证号" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="民族:">
            <template #content>
              <el-form-item prop="sale_radix_point_sign">
                <el-input v-model="data.nation" clearable placeholder="请输入民族" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="婚姻状况:">
            <template #content>
              <el-form-item prop="marital_status">
                <SelectComponents v-model="data.marital_status" api="GetMaritalStatusListApi" placeholder="请选择婚姻状况" label-field="name" value-field="id" clearable />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="教育状况:">
            <template #content>
              <el-form-item prop="education_level">
                <SelectComponents v-model="data.education_level" api="GetEducationLevelListApi" placeholder="请输入教育状况" label-field="name" value-field="id" clearable />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="毕业院校:">
            <template #content>
              <el-form-item prop="graduate_school">
                <el-input v-model="data.graduate_school" clearable placeholder="请输入毕业院校" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="毕业日期:">
            <template #content>
              <el-form-item prop="graduate_date">
                <SelectDate v-model="data.graduate_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="合同时间:" width="330">
            <template #content>
              <el-form-item prop="contract_date">
                <SelectDate v-model="data.contract_date" type="daterange" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="离职日期:">
            <template #content>
              <el-form-item prop="resign_date">
                <SelectDate v-model="data.resign_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="考勤卡号:">
            <template #content>
              <el-form-item prop="timecard_number">
                <el-input v-model="data.timecard_number" clearable placeholder="请输入考勤卡号" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </div>
  </FildCard>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}
::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
</style>
