<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import { GetGreyFabricInfoListUseByOthers } from '@/api/greyFabricPurchase'
import { getGfmSaleDeliveryOrderItemListEnum } from '@/api/greyFabricSalesReturn'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'

export interface Props {
  api: number
}

const props = withDefaults(defineProps<Props>(), {
  api: -1,
})

const emits = defineEmits(['handleSure'])

const state = reactive({
  filterData: {
    code: '',
    name: '',
    order_code: '',
    grey_fabric_width: '',
    grey_fabric_gram_weight: '',
  },
  showModal: false,
  modalName: '添加坯布',
  multipleSelection: [],
  apiString: '',
  customer_id: '',
  // rowIndex: -1,
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = GetGreyFabricInfoListUseByOthers()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  size: size2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = getGfmSaleDeliveryOrderItemListEnum()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = reactive<any>({
  loading: props.api === 1 ? loading1 : loading2,
  showPagition: true,
  showSlotNums: true,
  page: props.api === 1 ? page1 : page2,
  size: props.api === 1 ? size1 : size2,
  total: props.api === 1 ? total1 : total2,
  showCheckBox: true,
  showSort: false,
  height: '100%',
  handleSizeChange: (val: number) => (props.api === 1 ? handleSizeChange1(val) : handleSizeChange2(val)),
  handleCurrentChange: (val: number) => (props.api === 1 ? handleCurrentChange1(val) : handleCurrentChange2(val)),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const tableRef = ref()

function getData() {
  const query = {
    ...state.filterData,
    customer_id: state.customer_id,
  }
  props.api === 1 ? fetchData1(getFilterData(state.filterData)) : fetchData2(getFilterData(query))
  tableConfig.total = props.api === 1 ? total1 : total2
  tableConfig.page = props.api === 1 ? page1 : page2
  tableConfig.size = props.api === 1 ? size1 : size2
  nextTick(() => {
    tableRef.value.tableRef.refreshColumn()
  })
}

// function handReset() {
//   state.filterData = resetData(state.filterData)
// }

const columnList_first = ref([
  // {
  //   field: 'document_code',
  //   title: '坯布销售出货单号',
  // },
  {
    field: 'code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'full_name',
    title: '坯布全称',
    minWidth: 100,
  },
  {
    field: 'type_grey_fabric_name',
    title: '布种类型',
    minWidth: 100,
  },
  {
    field: 'type_grey_fabric_order_name',
    title: '坯布订单类型',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_composition',
    title: '坯布成分',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    minWidth: 100,
    title: '针寸数',
  },
  {
    field: '',
    minWidth: 100,
    title: '总针数',
  },
  {
    field: 'loom_model_name',
    minWidth: 100,
    title: '织机机型',
  },
  {
    field: 'gray_fabric_color_name',
    minWidth: 100,
    title: '织坯颜色',
  },
  {
    field: 'weave_spec_str',
    minWidth: 100,
    title: '织造规格',
  },
  {
    field: 'weaving_loss',
    minWidth: 100,
    title: '织造损耗',
    isPrice: true,
  },
  {
    field: 'weight_of_fabric',
    minWidth: 100,
    title: '布匹定重',
    isWeight: true,
  },
  {
    field: 'yarn_length',
    minWidth: 100,
    title: '纱长',
  },
  // {
  //   field: 'number',
  //   title: '匹数',
  //   isPrice: true,
  //   minWidth: 100,
  // },
  // {
  //   field: 'remark',
  //   title: '备注',
  //   minWidth: 100,
  // },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 150,
  },
])

const columnList = ref([
  {
    field: 'document_code',
    title: '坯布销售出货单号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '匹数',
    isPrice: true,
    minWidth: 100,
  },
  // {
  //   field: '',
  //   title: '备注',
  // },
  {
    field: 'create_time',
    title: '创建时间',
    minWidth: 150,
    isDate: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请选择一条数据')
  else
    emits('handleSure', state.multipleSelection)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1300" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div class="descriptions_row mb-2 flex-shrink-0" :style="{ '--minLabelWidth': '120px' }">
        <DescriptionsFormItem v-if="props.api !== 1" label="坯布销售出货单号:">
          <template #content>
            <el-input v-model="state.filterData.order_code" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template #content>
            <el-input v-model="state.filterData.code" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template #content>
            <el-input v-model="state.filterData.name" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="props.api === 1" label="坯布幅宽:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_width" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="props.api === 1" label="坯布克重:">
          <template #content>
            <el-input v-model="state.filterData.grey_fabric_gram_weight" />
          </template>
        </DescriptionsFormItem>
        <!--        <DescriptionsFormItem label=""> -->
        <!--          <template #content> -->
        <!--            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info"> -->
        <!--              清除条件 -->
        <!--            </el-button> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
      </div>
      <div class="flex-1 overflow-hidden">
        <Table ref="tableRef" :config="tableConfig" :table-list="props.api === 1 ? data1?.list : data2?.list" :column-list="props.api === 1 ? columnList_first : columnList">
          <template #grey_fabric_width="{ row }">
            {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
          </template>
          <template #grey_fabric_gram_weight="{ row }">
            {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
          </template>
          <template #finish_product_width="{ row }">
            {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
          </template>
          <template #finish_product_gram_weight="{ row }">
            {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
