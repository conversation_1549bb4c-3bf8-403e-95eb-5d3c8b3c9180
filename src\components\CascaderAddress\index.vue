<script lang="ts" setup>
import { computed, ref } from 'vue'
import { GetDistrictList } from '@/api/addressCard'

// 获取省市区
const props = defineProps({ // 显示的值
  lableName: { // 绑定的值的key值
    type: String,
    default: 'name',
  },
  valueName: { // 绑定的值的keyValue
    type: String,
    default: 'name',
  },
  querys: {
    type: Object,
    default: () => ({}),
  },
  placeholder: {
    type: String,
    default: '请选择省市区',
  },
  style: {
    type: Object,
    default: () => ({ width: '100%' }),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
    default: '',
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  // CascaderProps
  cascaderProps: {
    type: Object,
    default: () => ({}),
  },
})
const emits = defineEmits(['changeValue'])
const { fetchData: getProvince } = GetDistrictList()
const keyValue = defineModel('keyValue')

const cascaderComp = computed(() => { // 省市区懒加载
  const pops = {
    value: props.valueName,
    label: props.valueName,
    lazy: true, // 开启懒加载
    async lazyLoad(node: any, resolve: any) {
      const result = await getProvince({ parent_id: node.data.id, ...props.querys })
      if (result.success) {
        const nodes = result?.data?.list || []
        // 判断当前是否为叶子节点
        nodes.forEach((item: any) => {
          if (item.name === '台湾省')
            item.leaf = true
          else item.leaf = item.level > 3
        })
        resolve(nodes)
      }
      else {
        resolve([])
      }
    },
    ...props.cascaderProps,
  }
  return pops
})
function handleChange(val: any) {
  emits('changeValue', val)
  // 若果是任意选择一层级模式，则关闭下拉框
  if (props.cascaderProps.checkStrictly)
    close()
}
const cascader = ref()
function close() {
  cascader.value.togglePopperVisible()
}
defineExpose({
  close,
  cascaderRef: cascader,
})
</script>

<template>
  <el-cascader
    ref="cascader"
    v-model="keyValue"
    :placeholder="props.placeholder"
    :clearable="props.clearable"
    :style="props.style"
    :props="cascaderComp"
    :disabled="props.disabled"
    :class="props.className"
    @change="handleChange"
  />
</template>

<style scoped></style>
