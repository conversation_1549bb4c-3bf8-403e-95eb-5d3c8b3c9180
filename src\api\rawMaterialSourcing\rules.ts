// 新增原料采购订单-请求参数
export interface SystemRawMaterialPurchaseOrderItemLogistics {
  /** 件数 */
  piece_count?: number
  /** 收货地址 */
  receipt_address?: string
  /** 收货人 */
  receipt_person?: string
  /** 收货电话 */
  receipt_phone?: string
  /** 收货单位id */
  receipt_unit_id?: number
  /** 收货单位名称 */
  receipt_unit_name?: string
  /** 总数量 */
  weight?: number
}

export interface SystemAddRawMaterialPurchaseOrderItem {
  /** 坯布编号 */
  blank_fabric_code?: string
  /** 坯布名称 */
  blank_fabric_name?: string
  /** 品牌 */
  brand?: string
  /** 散装件数 */
  bulk_piece_count?: number
  /** 散装件重（kg） */
  bulk_piece_weight?: number
  /** 散装总重（kg） */
  bulk_weight?: number
  /** 色系 */
  color_scheme?: string
  /** 工艺 */
  craft?: string
  /** 客户id */
  customer_id?: number
  /** 客户名称 */
  customer_name?: string
  /** 等级ID */
  level_id?: number
  /** 物流信息 */
  logistics?: SystemRawMaterialPurchaseOrderItemLogistics[]
  /** 包装价格 */
  package_price?: number
  /** 生产日期 */
  production_date?: string
  /** 生产通知单号 */
  production_order_num?: string
  /** 原料编号 */
  raw_material_code?: string
  /** 原料id */
  raw_material_id?: number
  /** 原料名称 */
  raw_material_name?: string
  /** 备注 */
  remark?: string
  /** 纺纱类型 */
  spinning_type?: string
  /** 含税 */
  tax_included?: number
  /** 单价（kg/元） */
  unit_price?: number
  /** 整件件数 */
  whole_piece_count?: number
  /** 件重（kg） */
  whole_piece_weight?: number
}

export interface SystemAddRawMaterialPurchaseOrderParams {
  /** 发票抬头 */
  fapiao_title?: string
  items?: SystemAddRawMaterialPurchaseOrderItem[]
  /** 采购日期 */
  purchase_date?: string
  /** 收货日期 */
  receipt_date?: string
  /** 收货单位ID */
  receipt_unit_id?: number
  /** 收货单位名称 */
  receipt_unit_name?: string
  /** 备注 */
  remark?: string
  /** 营销体系ID */
  sale_system_id?: number
  /** 营销体系名称 */
  sale_system_name?: string
  /** 供应商ID */
  supplier_id?: number
  /** 供应商名称 */
  supplier_name?: string
}

// 原料采购订单列表-返回参数
export interface SystemGetRawMaterialPurchaseOrderListItem {
  /** 审核时间 */
  audit_time?: string
  /** 审核人ID （关联user.id） */
  auditor_id?: number
  /** 审核人名称 */
  auditor_name?: string
  /** 创建时间 */
  create_time?: string
  /** 创建人 */
  creator_name?: string
  /** 创建人 */
  creator_id?: number
  /** 记录ID */
  id?: number
  /** 订单号 */
  order_num?: string
  /** 采购日期 */
  purchase_date?: string
  /** 收货日期 */
  receipt_date?: string
  /** 收货单位ID */
  receipt_unit_id?: number
  /** 收货单位名称 */
  receipt_unit_name?: string
  /** 营销体系ID */
  sale_system_id?: number
  /** 营销体系名称 */
  sale_system_name?: string
  /** 状态 1待审核 2已审核 3已驳回 4已作废 */
  status?: number
  /** 状态名称 */
  status_name?: string
  /** 供应商ID */
  supplier_id?: number
  /** 供应商名称 */
  supplier_name?: string
  /** 总价格 */
  total_price?: number
  /** 修改时间 */
  update_time?: string
  /** 修改人 */
  update_user_name?: string
  /** 修改人 */
  updater_id?: number
}

// 原料采购订单详情-返回数据
export interface SystemRawMaterialPurchaseOrderDetailItem {
  /** 坯布编号 */
  blank_fabric_code?: string
  /** 坯布名称 */
  blank_fabric_name?: string
  /** 品牌 */
  brand?: string
  /** 散装件数 */
  bulk_piece_count?: number
  /** 散装件重（kg） */
  bulk_piece_weight?: number
  /** 散装总重（kg） */
  bulk_weight?: number
  /** 色系 */
  color_scheme?: string
  /** 工艺 */
  craft?: string
  /** 客户id */
  customer_id?: number
  /** 客户名称 */
  customer_name?: string
  /** 等级ID */
  level_id?: number
  /** 等级名称 */
  level_name?: string
  /** 物流信息 */
  logistics?: SystemRawMaterialPurchaseOrderItemLogistics[]
  /** 包装价格 */
  package_price?: number
  /** 生产日期 */
  production_date?: string
  /** 生产通知单号 */
  production_order_num?: string
  /** 原料名称 */
  raw_material_code?: string
  /** 原料id */
  raw_material_id?: number
  /** 原料名称 */
  raw_material_name?: string
  /** 备注 */
  remark?: string
  /** 纺纱类型 */
  spinning_type?: string
  /** 含税 */
  tax_included?: number
  /** 金额 */
  total_price?: number
  /** 总重（kg） */
  total_weight?: number
  /** 单价（kg/元） */
  unit_price?: number
  /** 整件件数 */
  whole_piece_count?: number
  /** 件重（kg） */
  whole_piece_weight?: number
  /** 件总重（kg） */
  whole_weight?: number
}

export interface SystemGetRawMaterialPurchaseOrderDetailResponse {
  /** 发票抬头 */
  fapiao_title?: string
  items?: SystemRawMaterialPurchaseOrderDetailItem[]
  /** 单号 */
  order_num?: string
  /** 打印类型(打印用) */
  print_type?: any
  /** 采购日期 */
  purchase_date?: string
  /** 收货日期 */
  receipt_date?: string
  /** 收货单位ID */
  receipt_unit_id?: number
  /** 收货单位名称 */
  receipt_unit_name?: string
  /** 备注 */
  remark?: string
  /** 营销体系ID */
  sale_system_id?: number
  /** 营销体系名称 */
  sale_system_name?: string
  /** 状态 1待审核 2已审核 3已驳回 4已作废 */
  status?: number
  /** 状态 1待审核 2已审核 3已驳回 4已作废 */
  status_name?: string
  /** 供应商ID */
  supplier_id?: number
  /** 供应商名称 */
  supplier_name?: string
  /** 税率 */
  tax_rate: number
  /** 是否含税 */
  is_with_tax_rate?: boolean
}
