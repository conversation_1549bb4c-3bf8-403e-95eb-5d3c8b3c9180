<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Minus, Plus } from '@element-plus/icons-vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import {
  createGlobalConfig,
  getGlobalConfigList,
  updateGlobalConfig,
} from '@/api/globalConfig'

// 页面状态
const dialogVisible = ref(false)
const dialogTitle = ref('新增配置')
const isEdit = ref(false)

// 选项项接口
interface OptionItem {
  label: string
  value: string
}

// 表单数据
const formData = reactive({
  id: undefined as number | undefined,
  key: '',
  description: '',
  options: '',
  options_presets: '',
  remark: '',
  type: 1,
})

// 动态选项列表（用于单选和多选）
const optionItems = ref<OptionItem[]>([{ label: '', value: '' }])

// 搜索表单
const searchForm = reactive({
  key: '',
  description: '',
  type: undefined as number | undefined,
})
// API调用相关
const {
  fetchData: fetchList,
  data: listData,
  success: listSuccess,
  msg: listMsg,
  loading,
  total,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getGlobalConfigList()

const {
  fetchData: fetchCreate,
  success: createSuccess,
  msg: createMsg,
} = createGlobalConfig()

const {
  fetchData: fetchUpdate,
  success: updateSuccess,
  msg: updateMsg,
} = updateGlobalConfig()

// const {
//   fetchData: fetchDelete,
//   success: deleteSuccess,
//   msg: deleteMsg,
// } = deleteGlobalConfig()
// 表格配置
const tableConfig = computed(() => ({
  showPagition: true,
  showSlotNums: true,
  // showOperate: true,
  height: 'auto',
  // operateWidth: '200',
  showSort: false,
  loading: loading.value,
  page: page.value,
  size: size.value,
  total: total.value,
  handleSizeChange,
  handleCurrentChange,
}))

// 配置类型选项
const typeOptions = [
  { label: '文本', value: 1 },
  { label: '单选', value: 2 },
  { label: '多选', value: 3 },
  { label: '开关', value: 4 },
]

// 表格列配置
const columnList = ref([
  {
    field: 'id',
    title: '事件ID',
    minWidth: 80,
  },
  {
    field: 'key',
    title: '配置名称',
    minWidth: 150,
  },
  {
    field: 'description',
    title: '描述',
    minWidth: 200,
  },
  {
    field: 'type_name',
    title: '配置类型',
    minWidth: 100,
  },
  {
    field: 'options',
    title: '配置值',
    minWidth: 200,
  },
  {
    field: 'options_presets',
    title: '预设值',
    minWidth: 150,
  },
  {
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    minWidth: 160,
    isDate: true,
  },
  {
    field: 'update_time',
    title: '更新时间',
    minWidth: 160,
    isDate: true,
  },
])

// 表单验证规则
const formRules = {
  key: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择配置类型', trigger: 'change' },
  ],
  options: [
    { required: true, message: '请输入配置值', trigger: 'blur' },
  ],
}

const formRef = ref()

// 获取列表数据
async function getList() {
  await fetchList({
    ...searchForm,
  })

  if (!listSuccess.value)
    ElMessage.error(listMsg.value || '获取数据失败')
}

// 防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 执行搜索
function performSearch() {
  getList()
}

// 清空搜索条件
function clearSearch() {
  Object.assign(searchForm, {
    key: '',
    description: '',
    type: undefined,
  })
}

// 监听搜索表单变化，实现自动搜索
watch(
  () => searchForm,
  () => {
    // 清除之前的定时器
    if (searchTimer)
      clearTimeout(searchTimer)

    // 设置新的定时器，300ms后执行搜索
    searchTimer = setTimeout(() => {
      performSearch()
    }, 300)
  },
  { deep: true },
)

// 监听配置类型变化，重置选项数据
watch(
  () => formData.type,
  (newType) => {
    if ([2, 3].includes(newType)) {
      // 如果切换到单选或多选，重置选项数据
      if (optionItems.value.length === 0 || (optionItems.value.length === 1 && !optionItems.value[0].label && !optionItems.value[0].value))
        optionItems.value = [{ label: '', value: '' }]
    }
    else {
      // 如果切换到其他类型，清空options
      formData.options = ''
    }
  },
)

// 新增
// function handleAdd() {
//   dialogTitle.value = '新增配置'
//   isEdit.value = false
//   resetForm()
//   dialogVisible.value = true
// }
//
// // 编辑
// function handleEdit(row: any) {
//   dialogTitle.value = '编辑配置'
//   isEdit.value = true
//   Object.assign(formData, row)
//   stringToOptions() // 将options字符串转换为选项数组
//   dialogVisible.value = true
// }

// 删除
// async function handleDelete(row: any) {
//   try {
//     await ElMessageBox.confirm('确定要删除这条配置吗？', '提示', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning',
//     })
//
//     await fetchDelete({ id: row.id })
//
//     if (deleteSuccess.value) {
//       ElMessage.success('删除成功')
//       getList()
//     }
//     else {
//       ElMessage.error(deleteMsg.value || '删除失败')
//     }
//   }
//   catch (error) {
//     if (error !== 'cancel')
//       ElMessage.error('删除失败')
//   }
// }

// 删除状态切换功能，因为新API中没有状态字段

// 保存
async function handleSave() {
  try {
    await formRef.value?.validate()

    // 如果是单选或多选，将选项数组转换为JSON字符串
    optionsToString()

    if (isEdit.value) {
      await fetchUpdate({ ...formData })
      if (updateSuccess.value) {
        ElMessage.success('更新成功')
        dialogVisible.value = false
        getList()
      }
      else {
        ElMessage.error(updateMsg.value || '更新失败')
      }
    }
    else {
      await fetchCreate(formData)
      if (createSuccess.value) {
        ElMessage.success('创建成功')
        dialogVisible.value = false
        getList()
      }
      else {
        ElMessage.error(createMsg.value || '创建失败')
      }
    }
  }
  catch (error) {
    if (error !== false)
      ElMessage.error('保存失败')
  }
}

// 添加选项
function addOption() {
  optionItems.value.push({ label: '', value: '' })
}

// 删除选项
function removeOption(index: number) {
  optionItems.value.splice(index, 1)
}

// 将选项数组转换为JSON字符串
function optionsToString() {
  if ([2, 3].includes(formData.type)) {
    // 过滤掉空的选项
    const validOptions = optionItems.value.filter(item => item.label.trim() && item.value.trim())
    formData.options = JSON.stringify(validOptions)
  }
}

// 将JSON字符串转换为选项数组
// function stringToOptions() {
//   if ([2, 3].includes(formData.type) && formData.options) {
//     try {
//       const parsed = JSON.parse(formData.options)
//       if (Array.isArray(parsed) && parsed.length > 0) {
//         optionItems.value = parsed.map(item => ({
//           label: item.label || '',
//           value: item.value || '',
//         }))
//       }
//       else {
//         optionItems.value = [{ label: '', value: '' }]
//       }
//     }
//     catch (error) {
//       optionItems.value = [{ label: '', value: '' }]
//     }
//   }
//   else {
//     optionItems.value = [{ label: '', value: '' }]
//   }
// }

// 重置表单
// function resetForm() {
//   Object.assign(formData, {
//     id: undefined,
//     key: '',
//     description: '',
//     options: '',
//     options_presets: '',
//     remark: '',
//     type: 1,
//   })
//   optionItems.value = [{ label: '', value: '' }]
//   formRef.value?.clearValidate()
// }

// 页面初始化
onMounted(() => {
  performSearch()
})
</script>

<template>
  <div class="list-page">
    <!-- 搜索区域 -->
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="配置名称:">
          <template #content>
            <el-input
              v-model="searchForm.key"
              placeholder="请输入配置名称"
              clearable
              style="width: 200px"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="描述:">
          <template #content>
            <el-input
              v-model="searchForm.description"
              placeholder="请输入描述"
              clearable
              style="width: 200px"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="配置类型:">
          <template #content>
            <el-select
              v-model="searchForm.type"
              placeholder="请选择配置类型"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="">
          <template #content>
            <el-button @click="clearSearch">
              清空搜索
            </el-button>
            <!-- <el-button type="success" @click="handleAdd">
              新增配置
            </el-button> -->
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <!-- 表格区域 -->
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <Table
        :config="tableConfig"
        :table-list="listData?.list || []"
        :column-list="columnList"
      >
        <!-- 操作列 -->
        <!-- <template #operate="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="handleDelete(row)">
            删除
          </el-button>
        </template> -->
      </Table>
    </FildCard>

    <!-- 新增/编辑弹窗 -->
    <vxe-modal
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800"
      height="auto"
      show-footer
      :mask="false"
      :lock-view="false"
      :esc-closable="true"
      resize
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="配置名称" prop="key">
          <el-input
            v-model="formData.key"
            placeholder="请输入配置名称"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="配置类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择配置类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预设值">
          <!-- 文本类型 -->
          <el-input
            v-if="formData.type === 1"
            v-model="formData.options"
            type="textarea"
            :rows="3"
            placeholder="请输入配置值"
          />
          <!-- 单选/多选类型 -->
          <div v-if="[2, 3].includes(formData.type)" class="option-config">
            <div
              v-for="(item, index) in optionItems"
              :key="index"
              class="option-item"
            >
              <el-row :gutter="10" align="middle">
                <el-col :span="10">
                  <el-input
                    v-model="item.label"
                    placeholder="请输入选项名称"
                  />
                </el-col>
                <el-col :span="10">
                  <el-input
                    v-model="item.value"
                    placeholder="请输入选项值"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button
                    type="primary"
                    :icon="Plus"
                    size="small"
                    @click="addOption"
                  />
                  <el-button
                    type="danger"
                    :icon="Minus"
                    size="small"
                    @click="removeOption(index)"
                  />
                </el-col>
              </el-row>
            </div>
            <div v-if="!optionItems.length" class="option-add-btn cursor-pointer">
              <div type="primary" @click="addOption">
                <el-icon><Plus /></el-icon>
              </div>
            </div>
          </div>
          <!-- 开关类型 -->
          <el-switch
            v-if="formData.type === 4"
            :model-value="formData.options === 'true'"
            active-text="开启"
            inactive-text="关闭"
            @change="(val: boolean) => formData.options = val.toString()"
          />
        </el-form-item>
        <el-form-item label="配置值" props="options">
          <el-input
            v-model="formData.options_presets"
            placeholder="请输入预设值，多个值用逗号分隔"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleSave">
          保存
        </el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<style lang="scss" scoped>
.global-config-container {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.option-config {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .option-item {

    .option-label {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }

  .option-add-btn {
    margin-top: 16px;
    text-align: center;
    padding: 12px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    background-color: #fafafa;

    &:hover {
      border-color: #409eff;
      background-color: #f0f9ff;
    }
  }
}
</style>
