<script setup lang="ts">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  debounce,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import CoverImage from '@/components/UploadFile/CoverImage/index.vue'
import { DeleteCarouselBanner, GetCarouselBannerList, UpdatecarouselBannerStatus } from '@/api/colorCardManagement/banner'
import { ColorCardStatusEnum } from '@/enum/colorCardEnum'

const state = reactive({
  tableData: [],
  filterData: {
    title: '',
    jump_type: '',
    status: '',
  },
  selectProductIds: [] as any[],
  multipleSelection: [] as any[],
})

const {
  fetchData: ApiBannerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = GetCarouselBannerList()

// 获取数据
const getData = debounce(async () => {
  await ApiBannerList(
    getFilterData({
      ...state.filterData,
    }),
  )
}, 400)

onMounted(() => {
  getData()
})
const tableConfig = ref({
  fieldApiKey: 'FinishedProductColorInformation',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  height: '100%',
  showSort: false,
  show_footer: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

watch(
  () => state.filterData,
  () => {
    clearSelectEvent()
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    title: '预览',
    field: 'prev_view_url',
    fixed: 'left',
    soltName: 'image_code',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'title',
    title: '轮播标题',
    fixed: 'left',
    minWidth: 100,
  },
  // {
  //   sortable: true,
  //   field: 'link',
  //   title: '链接',
  //   fixed: 'left',
  //   minWidth: 100,
  //   soltName: 'link',
  // },
  {
    sortable: true,
    field: 'jump_type_name',
    title: '跳转类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sort',
    title: '排序',
    minWidth: '6%',
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: '6%',
  },
  {
    sortable: true,
    field: 'status_name',
    title: '启用状态',
    minWidth: 100,
    soltName: 'status',
  },
  {
    field: 'create_time',
    title: '创建时间',
    width: 150,
    sortable: true,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    width: 150,
    isDate: true,
  },

])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

onActivated(() => {
  getData()
})
const router = useRouter()

function handleAdd() {
  router.push({
    name: 'ColorCardBannerAdd',
  })
}

function handEdit(row: any) {
  router.push({
    name: 'ColorCardBannerEdit',
    params: {
      id: row.id,
    },
  })
}

function handDetail(row: any) {
  router.push({
    name: 'ColorCardBannerDetail',
    params: {
      id: row.id,
    },
  })
}

// 删除数据
const {
  fetchData: deleteFetch,
  success: deleteSuccess,
  msg: deleteMsg,
} = DeleteCarouselBanner()

async function handDelete(row: any) {
  const res = await deleteToast('是否确认删除该信息')
  if (res) {
    await deleteFetch({ id: row.id })
    if (deleteSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteMsg.value)
    }
  }
}

// 批量删除
// async function handAllDelete() {
//   if (!state.multipleSelection.length)
//     return ElMessage.warning('请至少选择一条数据！')
//   await deleteToast('确认删除该记录嘛？')
//   const ids: any[] = []
//   state.multipleSelection.forEach((item: any) => {
//     ids.push(item.id)
//   })
//   await deleteFetch({ id: ids.toString() })
//   if (deleteSuccess.value) {
//     ElMessage.success('成功')
//     state.multipleSelection = []
//     getData()
//   }
//   else {
//     ElMessage.error(deleteMsg.value)
//   }
// }

// 批量修改状态
const { fetchData: fetchDataStatus, success: successStatus, msg: msgStatus } = UpdatecarouselBannerStatus()
async function handAll(val: number) {
  if (state.multipleSelection.length <= 0)
    return ElMessage.error('请先勾选数据')
  const status_msg = val === ColorCardStatusEnum.Enable ? '启用' : '禁用'
  ElMessageBox.confirm(`确认${status_msg}该数据？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  })
    .then(async () => {
      const ids = state.multipleSelection.map((item: any) => item.id)
      await fetchDataStatus({ id: ids, status: val })
      if (successStatus.value) {
        getData()
        state.multipleSelection = []
        ElMessage.success('修改成功')
      }
      else { ElMessage.error(msgStatus.value) }
    })
}

const tableRefSelect = ref()
function clearSelectEvent() {
  state.selectProductIds = []
  const $table = tableRefSelect.value
  if ($table)
    $table.tableRef.clearCheckboxRow()
}

const tablesRef = ref()
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '76px' }">
        <DescriptionsFormItem label="轮播标题:">
          <template #content>
            <el-input
              v-model="state.filterData.title"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跳转类型:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.jump_type"
              api="GetCarouselBannerJumpType"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              api="GetCarouselBannerStatus"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-1 overflow-hidden">
      <FildCard
        title=""
        class="table-card-full"
        tool-bar
      >
        <template #right-top>
          <el-button
            v-has="'ColorCardBannerAdd'"
            style="margin-left: 10px"
            type="primary"
            :icon="Plus"
            @click="handleAdd"
          >
            新建
          </el-button>
          <el-dropdown>
            <span class="el-dropdown-link">
              <el-button class="mx-[10px]">批量操作</el-button>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <div v-has="'ColorCardBannerDelete'">
                  <el-dropdown-item @click="handAllDelete">
                    批量删除
                  </el-dropdown-item>
                </div> -->
                <div v-has="'ColoCardBannerStatus'">
                  <el-dropdown-item @click="handAll(ColorCardStatusEnum.Enable)">
                    批量启用
                  </el-dropdown-item>
                </div>
                <div v-has="'ColoCardBannerStatus'">
                  <el-dropdown-item @click="handAll(ColorCardStatusEnum.Disable)">
                    批量禁用
                  </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <Table
          ref="tablesRef"
          :config="tableConfig"
          :table-list="data?.list"
          :column-list="columnList"
        >
          <template #image_code="{ row }">
            <CoverImage :file-url="row.prev_view_url" />
          </template>
          <template #link="{ row }">
            {{ row.link || '-' }}
          </template>
          <template #status="{ row }">
            <div :class="row.status === ColorCardStatusEnum.Enable ? 'yuan_font' : 'yuan_font_active'">
              {{ row.status_name }}
            </div>
          </template>
          <template #operate="{ row }">
            <el-space :size="10">
              <el-link
                v-has="'ColorCardBannerDetail'"
                :underline="false"
                type="primary"
                @click="handDetail(row)"
              >
                查看
              </el-link>
              <el-link
                v-has="'ColorCardBannerEdit'"
                :underline="false"
                type="primary"
                @click="handEdit(row)"
              >
                编辑
              </el-link>
              <el-link
                v-has="'ColorCardBannerDelete'"
                :underline="false"
                type="primary"
                @click="handDelete(row)"
              >
                删除
              </el-link>
            </el-space>
          </template>
        </Table>
      </FildCard>
    </div>
  </div>
</template>

<style lang="scss" scoped>
</style>
