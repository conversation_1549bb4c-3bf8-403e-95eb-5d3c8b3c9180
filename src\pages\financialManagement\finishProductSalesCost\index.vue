<script setup lang="ts" name="FinishProductSalesCost">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { formatDate, formatHashTag, formatLengthDiv, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'

import BottonExcel from '@/components/BottonExcel/index.vue'
import { useListExport } from '@/hooks/useListExport'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import { EmployeeType } from '@/common/enum'
import { ExportShouldCollectOrderCalculationList, getShouldCollectOrderCalculationList } from '@/api/inventory/finishedProduct/finishedProductReport'

const state = reactive({
  filterData: {
    devierDate: '',
    product_id: '',
    color_id: '',
    status: '', // 对应 audit_status
    dyelot_number: '',
    customer_id: '',
    sale_system_id: null,
    sale_user_id: null,
    sale_follower_id: null,
    sale_order_no: '',
    should_collect_order_no: '',
    collect_status: '', // 新增收款状态
  },
})

const { fetchData, data, success, msg, loading, page, size, total, handleSizeChange, handleCurrentChange } = getShouldCollectOrderCalculationList()
const { handleExport, loadingExcel } = useListExport()
function getQuery() {
  const query: any = {
    ...state.filterData,
    start_time: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[0]) : '',
    end_time: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[1]) : '',
    audit_status: state.filterData.status,
    page: page.value,
    size: size.value,
  }

  delete query.devierDate
  return getFilterData(query)
}

// 获取数据
async function getData() {
  await fetchData(getQuery())
  if (!success.value)
    return ElMessage.error(msg.value)
}

onMounted(async () => {
  await getData()
})
function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'
      if (['grey_fabric_roll', 'roll', 'paper_tube_total_price', 'purchase_roll', 'grey_fabric_total_price', 'daf_total_price', 'other_price', 'price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, column.property) as any)}`

      if (['purchase_weight', 'grey_fabric_weight', 'weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property) as any)}`

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['return_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'return_roll') as any)}`

      if (['return_length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'return_length') as any)}`

      if (['return_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'return_weight') as any)}`

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['weight_error'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight_error') as any)}`

      if (['actually_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_weight') as any)}`

      if (['settle_error_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_error_weight') as any)}`

      if (['settle_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_weight') as any)}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length') as any)}`

      if (['settle_price', 'settle_price_remove_tax'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, column.property) as any)}`

      return null
    }),
  ]
}

const tableConfig = ref({
  loading,
  scrollY: {
    enabled: true,
  },
  showStatus: true,
  showPagition: false,
  showSlotNums: true,
  page,
  size,
  total,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  showOperate: true,
  operateWidth: '180',
  height: '100%',
  showSort: false,
  fieldApiKey: 'finishProductSalesCost',
  footerMethod: (val: any) => FooterMethod(val),
})

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const columnList = ref([
  {
    sortable: true,
    field: 'product_customer_name',
    title: '客户名称',
    fixed: 'left',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'product',
    fixed: 'left',
    title: '成品名称',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'color',
    fixed: 'left',
    title: '色号颜色',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_price',
    title: '销售单价',
    isUnitPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'offset_sale_price',
    title: '优惠幅度',
    isUnitPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'roll',
    title: '匹数',
    isRoll: true,
    width: 100,
  },
  {
    sortable: true,
    field: 'out_weight',
    title: '出仓数量',
    isWeight: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'actually_weight',
    title: '码单数量',
    isWeight: true,
    minWidth: 120,
  },
  {
    field: 'settle_error_weight',
    title: '结算空差',
    width: 100,
    sortable: true,
    isWeight: true,
  },
  {
    field: 'settle_weight',
    title: '结算数量',
    isWeight: true,
    width: 100,
    sortable: true,
  },
  {
    sortable: true,
    field: 'other_price',
    title: '其他金额',
    isPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'sale_tax_rate',
    title: '税率',
    minWidth: 80,
  },
  {
    sortable: true,
    field: 'settle_price',
    title: '销售金额',
    isPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'settle_price_remove_tax',
    title: '销售金额（不含税）',
    isPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'buoyant_weight_price',
    title: '成本单价',
    isUnitPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'total_cost_price',
    title: '成本金额',
    isPrice: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'gross_profit',
    title: '毛利',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gross_margin',
    soltName: 'gross_margin',
    title: '毛利率',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '日期',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'sale_order_no',
    soltName: 'sale_order_no',
    title: '销售单',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'arrange_order_no',
    soltName: 'arrange_order_no',
    title: '配布单',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'should_collect_order_no',
    soltName: 'should_collect_order_no',
    title: '送货单',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'fpm_sale_out_order_no',
    soltName: 'fpm_sale_out_order_no',
    title: '出仓单',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '销售员',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_follower_name',
    title: '销售跟单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'arrange_warehouse_name',
    title: '配布仓库',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'warehouse_name',
    title: '出仓仓库',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'address',
    title: '收货地址',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'audit_status',
    soltName: 'audit_status',
    title: '应收状态',
    minWidth: 100,
    showOrder_status: true,
  },
  {
    sortable: true,
    field: 'collect_status_name',
    soltName: 'collect_status_name',
    title: '收款状态',
    minWidth: 100,
  },
])

const tableConfig_fabric = ref({
  showSlotNums: true,
  height: '100%',
  footerMethod: (val: any) => FooterMethod(val),
})

const columnList_fabric = ref([
  {
    field: 'roll',
    title: '匹数',
    minWidth: 50,
    isPrice: true,
  },
  {
    field: 'warehouse_bin',
    title: '仓位',
    minWidth: 100,
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 80,
  },
  {
    field: 'base_unit_weight',
    title: '基本单位数量',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒重量',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '码单空差',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'actually_weight',
    title: '码单数量',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'settle_error_weight',
    title: '结算空差',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'settle_weight',
    title: '结算数量',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'length',
    title: '辅助数量',
    minWidth: 80,
    isLength: true,
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 80,
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 80,
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 80,
  },
])

const router = useRouter()
function goSaleOrderDetail(row: Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemData) {
  router.push({
    name: 'ProductSaleDetail',
    query: { id: row.sale_order_id },
  })
}
function goArrangeOrderDetail(row: Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemData) {
  router.push({
    name: 'CashCommodityClothOrderDetail',
    query: { id: row.arrange_order_id },
  })
}

function goDeliveryOrderDetail(row: Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemData) {
  router.push({
    name: 'SaleDeliverDetail',
    query: { id: row.should_collect_order_id },
  })
}
function goFpmSaleOutOrderDetail(row: Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemData) {
  router.push({
    name: 'FpSaleDeliverFromGodownOrderDetail',
    query: { id: row.fpm_sale_out_order_id },
  })
}
function selectCustomerValueChange(val: any) {
  state.customer_name = val.name
  state.filterData.customer_id = val.id
}
const ximaData = ref<Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemFcData[]>([])
const showModal = ref(false)
function handleXima(row: Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemData) {
  showModal.value = true
  ximaData.value = row.fc_list
}
function handleStructure(row: Api.GetShouldCollectOrderCalculationList.ShouldCollectOrderCostCalculationListItemData) {
  router.push({
    name: 'FinishProductCostAccounting',
    query: {
      product_id: row.product_id,
      product_name: row.product,
      color_id: row.product_color_id,
      color_name: row.color,
      dyelot_number: row.dyelot_number,
    },
  })
}
const exportTableName = computed(() => {
  if (!state.filterData.devierDate)
    return '销售成本'

  return `${dayjs(state.filterData.devierDate[0]).format('YYYYMMDD')}-${dayjs(state.filterData.devierDate[1]).format('YYYYMMDD')}_成本核算`
})
function handleClickExcel() {
  if (!state.filterData.devierDate)
    return ElMessage.error('请先选择时间')
  handleExport({
    tableList: data.value?.list,
    apiRequest: ExportShouldCollectOrderCalculationList,
    query: getQuery(),
    tableName: exportTableName.value,
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="日期">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称" width="330">
          <template #content>
            <SelectMergeComponent
              v-model="state.filterData.product_id"
              :custom-label="(row:any) => `${formatHashTag(row.finish_product_code, row.finish_product_name)}`"
              :multiple="false"
              api-name="GetFinishProductDropdownList"
              remote
              remote-key="finish_product_code_or_name"
              remote-show-suffix
              placeholder="成品编号、成品名称"
              value-field="id"
              @change="state.filterData.color_id = ''"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号颜色">
          <template #content>
            <SelectMergeComponent
              v-model="state.filterData.color_id"
              :custom-label="(row:any) => `${formatHashTag(row.product_color_code, row.product_color_name)}`"
              :multiple="false"
              :disabled="!state.filterData.product_id"
              :query="{
                finish_product_id: state.filterData.product_id,
              }"
              api-name="GetFinishProductColorDropdownList"
              remote
              remote-key="product_color_code_or_name"
              remote-show-suffix
              placeholder="成品颜色、色号"
              value-field="id"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称">
          <template #content>
            <SelectCustomerDialog
              v-model:customer_id="state.filterData.customer_id"
              @change-value="selectCustomerValueChange"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:">
          <template #content>
            <el-input v-model="state.filterData.dyelot_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单状态">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              :exclude="[4]"
              :multiple="false"
              style="width: 200px"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款状态">
          <template #content>
            <SelectComponents
              v-model="state.filterData.collect_status"
              :multiple="false"
              style="width: 200px"
              api="CollectStatus"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:" required>
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.filterData.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <SelectComponents v-model="state.filterData.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单员:">
          <template #content>
            <SelectComponents v-model="state.filterData.sale_follower_id" :query="{ duty: EmployeeType.follower }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售单号:">
          <template #content>
            <el-input v-model="state.filterData.sale_order_no" placeholder="销售单号" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="送货单号:">
          <template #content>
            <el-input v-model="state.filterData.should_collect_order_no" clearable placeholder="送货单号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <BottonExcel
              :loading="loadingExcel"
              title="导出Excel"
              @on-click-excel="handleClickExcel"
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #sale_order_no="{ row }">
          <el-link type="primary" :underline="false" @click.stop="goSaleOrderDetail(row)">
            {{ row.sale_order_no }}
          </el-link>
        </template>
        <template #arrange_order_no="{ row }">
          <el-link type="primary" :underline="false" @click.stop="goArrangeOrderDetail(row)">
            {{ row.arrange_order_no }}
          </el-link>
        </template>
        <template #should_collect_order_no="{ row }">
          <el-link type="primary" :underline="false" @click.stop="goDeliveryOrderDetail(row)">
            {{ row.should_collect_order_no }}
          </el-link>
        </template>
        <template #fpm_sale_out_order_no="{ row }">
          <el-link type="primary" :underline="false" @click.stop="goFpmSaleOutOrderDetail(row)">
            {{ row.fpm_sale_out_order_no }}
          </el-link>
        </template>
        <template #gross_margin="{ row }">
          <span>{{ row.gross_margin }}%</span>
        </template>
        <template #collect_status_name="{ row }">
          <el-tag v-if="row?.collect_status === 3" type="primary">
            {{ row.collect_status_name }}
          </el-tag>
          <el-tag v-if="row?.collect_status === 2" type="success">
            {{ row.collect_status_name }}
          </el-tag>
          <el-tag v-if="row?.collect_status === 1" type="danger">
            {{ row.collect_status_name }}
          </el-tag>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-button type="primary" text link @click="handleXima(row)">
              细码
            </el-button>
            <el-button type="primary" text link @click="handleStructure(row)">
              成本结构
            </el-button>
          </el-space>
        </template>
      </Table>
      <div class="flex justify-between">
        <el-row class="flex-1">
          <el-col :span="4">
            <el-statistic title="总匹数" :value="data?.total_roll || 0" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="销售总额" :value="formatPriceDiv(data?.total_sale_price || 0)">
              <template #prefix>
                ￥
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic title="成本总额" :value="formatPriceDiv(data?.total_cost_price || 0)">
              <template #prefix>
                ￥
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="毛利总额"
              :value="formatPriceDiv(data?.total_gross_profit || 0)"
              :value-style="{
                color: (data?.total_gross_profit || 0) >= 0 ? '#f56c6c' : '#67c23a',
              }"
            >
              <template #prefix>
                <span
                  :style="{
                    color: (data?.total_gross_profit || 0) >= 0 ? '#f56c6c' : '#67c23a',
                  }"
                >
                  ￥
                </span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="平均毛利率"
              :value="data?.total_gross_margin"
              :value-style="{
                color: (data?.total_gross_margin || 0) >= 0 ? '#f56c6c' : '#67c23a',
              }"
            >
              <template #suffix>
                <span
                  :style="{
                    color: (data?.total_gross_margin || 0) >= 0 ? '#f56c6c' : '#67c23a',
                  }"
                >
                  %
                </span>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
        <el-pagination
          :current-page="page"
          :page-size="size"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </FildCard>
  </div>
  <vxe-modal
    v-model="showModal"
    destroy-on-close
    show-zoom
    resize
    show-footer
    title="细码"
    width="1200"
    height="500"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
  >
    <div class="list-page">
      <div class="table-card-full">
        <Table
          :config="tableConfig_fabric"
          :table-list="ximaData"
          :column-list="columnList_fabric"
        />
      </div>
    </div>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.el-col {
  text-align: center;
}
</style>
