import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const Business_unitsale_arealist = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_area/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建销售区域
export const Business_unitsale_areaPost = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_area',
    method: 'post',
  })
}

// 更新销售区域
export const Business_unitsale_areaPut = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_area',
    method: 'put',
  })
}

// 删除销售区域
export const Business_unitsale_areadelete = () => {
  return useRequest({
    url: '/admin/v1/business_unit/sale_area',
    method: 'delete',
  })
}
