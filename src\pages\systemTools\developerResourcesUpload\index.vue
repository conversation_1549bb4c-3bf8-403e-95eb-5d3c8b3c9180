<template>
  <FildCard title="上传图片到CDN" :tool-bar="false">
    <template v-slot:right-top>
      <el-button type="primary" @click="handleShowUpload(UploadWay.TEST)">上传测试</el-button>
      <el-button type="primary" @click="handleShowUpload(UploadWay.PROD)">上传正式</el-button>
    </template>
    <el-form label-position="left" :rules="rules" label-width="100px" :model="formData" style="max-width: 460px" ref="formRef">
      <el-form-item label="图片" prop="texture_url">
        <div class="image-content">
          <!--        <div @click="showImage = true">-->
          <!--          <el-image :src="formatUrl(`${formData.prev_view_url}!w400`)" fit="cover" style="width: 150px; height: 150px">-->
          <!--            <template #error>-->
          <!--              <div class="el-image__error">暂无</div>-->
          <!--            </template>-->
          <!--          </el-image>-->
          <!--        </div>-->
          <UploadFile
            ref="uploadFileRef"
            :secene="formData.scene"
            :status="formData.status"
            :autoUpload="false"
            :multiple="false"
            :showSubmitBtn="false"
            :fileList="formData.texture_url"
            @onUploadSuccess="handUpload"
          />
        </div>
      </el-form-item>
      <el-form-item label="场景值" prop="scene">
        <el-input v-model.trim="formData.scene" />
      </el-form-item>
      <el-form-item label="测试环境" prop="test_url">
        <el-input v-model.trim="formData.test_url" />
      </el-form-item>
      <el-form-item label="正式环境" prop="url">
        <el-input v-model.trim="formData.url" />
      </el-form-item>
    </el-form>
    <!--    <UploadFile :defaultImage="state.form.cover_texture_url" multiple :showSubmitBtn="false" :fileList="fileList" @onUploadSuccess="handUpload" @onDefaultImage="getDefaultImage" />-->
    <!--    <UploadImageTool ref="uploadEl" :scene="formData.scene" :status="formData.status" accept="image/*,.pdf" @changeConfirm="value => (formData.prev_view_url = value)" />-->
  </FildCard>
</template>

<script lang="ts" setup name="DeveloperResourcesUpload">
import { reactive, ref, watch, computed } from 'vue'
import { breakupUrl, formatUrl, jointUrl } from '@/common/util'
import UploadFile from '@/components/UploadFile/index.vue'
import FildCard from '@/components/FildCard.vue'
import uploadCDNImg, { UploadWay } from '@/common/uploadImage'
import { ElForm, ElMessage } from 'element-plus'
import { GET_IMG_CND_Prefix } from '@/common/constant'

const formData = reactive({
  texture_url: [] as string[],
  test_url: '',
  url: '',
  status: 1,
  base_url: '',
  scene: 'product',
  show_url: '',
})

const formRef = ref<InstanceType<typeof ElForm> | null>(null)

const rules = ref({
  // texture_url: [
  //   {
  //     required: true,
  //     validator: (rule: any, value: any, callback: any) => {
  //

  //       if (value.length <= 0) {
  //         callback(new Error('请上传图片'))
  //       } else {
  //         callback()
  //       }
  //     },
  //     trigger: 'change',
  //   },
  // ],
  scene: [
    {
      required: true,
      message: '请输入场景值',
      trigger: 'change',
    },
  ],
})

const handUpload = (list: string[]) => {
  ElMessage({
    type: 'success',
    message: '上传成功',
  })
  formData.texture_url = list
  if (formData.status === UploadWay.TEST) {
    formData.test_url = GET_IMG_CND_Prefix('test') + breakupUrl(list[0])
  } else {
    formData.url = GET_IMG_CND_Prefix('production') + breakupUrl(list[0])
  }
}

const uploadFileRef = ref()
const handleShowUpload = async (num: UploadWay) => {
  if (!formRef.value) return
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      formData.status = num
      uploadFileRef.value.uploadFun()
      // Promise.all([
      //   formData.texture_url.map(async url => {
      //     return await uploadCDNImg(url, formData.scene, 'product', num)
      //   }),
      // ])
      //   .then(res => {
      //

      //     ElMessage({
      //       type: 'success',
      //       message: '上传成功',
      //     })
      //     formData.texture_url = []
      //   })
      //   .catch(err => {
      //     ElMessage({
      //       type: 'error',
      //       message: '上传失败',
      //     })
      //   })
    } else {
      ElMessage({
        type: 'error',
        message: '请选择图片',
      })
    }
  })
}

watch(
  () => formData.prev_view_url,
  () => {
    if (formData.status === 1) {
      formData.test_url = formData.prev_view_url
    } else {
      formData.url = formData.prev_view_url
    }
  }
)
</script>

<style lang="scss" scoped>
.image-content {
  display: flex;
}

.image-content button {
  margin-left: 10px;
}

.tips {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}

.jump-target {
  margin-top: 10px;
}

.other {
  border: 1px solid #eee;
  padding: 10px;
}
</style>
