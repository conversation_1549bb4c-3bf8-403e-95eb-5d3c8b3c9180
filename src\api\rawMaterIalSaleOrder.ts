import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function SaleOrderList() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function SaleOrderListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/raw_material/sale_order/list',
    method: 'get',
    nameFile,
  })
}

// 获取详情
export function SaleOrderDetail() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/detail',
    method: 'get',
  })
}

// 添加
export function SaleOrderAdd() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order',
    method: 'post',
  })
}
// 修改
export function SaleOrderEdit() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order',
    method: 'put',
  })
}

// 消审
export function SaleOrderCancel() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/cancel',
    method: 'put',
  })
}

// 审核
export function SaleOrderPass() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/pass',
    method: 'put',
  })
}

// 驳回
export function SaleOrderReject() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/reject',
    method: 'put',
  })
}

// 作废
export function SaleOrderVoid() {
  return useRequest({
    url: '/admin/v1/raw_material/sale_order/void',
    method: 'put',
  })
}
