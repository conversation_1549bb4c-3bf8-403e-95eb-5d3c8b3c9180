{"name": "erp_service", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a", "scripts": {"prepare": "husky install", "dev": "vite --mode test --host", "dev:tian2": "vite --mode tian2 --host", "dev:tian3": "vite --mode tian3 --host", "dev:zhan2": "vite --mode zhan2 --host", "dev:xmh1": "vite --mode xmh1 --host", "dev:xmh2": "vite --mode xmh2 --host", "dev:kdyb": "vite --mode kdyb --host", "dev:pre": "vite --mode pre --host", "dev:mes": "vite --mode mes --host", "dev:prod": "vite --mode production --host", "build": "vite build", "preview": "vite preview", "buildtest": "vite build --mode development", "previewtest": "vite preview --mode development --host", "buildpre": "vite build --mode pre", "previewpre": "vite preview --mode pre --host", "buildkdyb": "vite build --mode kdyb", "previewkdyb": "vite preview --mode kdyb --host", "buildprod": "vite build --mode production", "previewmes": "vite preview --mode mes --host", "buildmes": "vite build --mode mes", "svg": "vsvg -s ./src/assets/icons/svg -t ./src/icons/icons", "fix-memory-limit": "cross-env LIMIT=10240 increase-memory-limit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@better-scroll/core": "^2.5.1", "@better-scroll/mouse-wheel": "^2.5.1", "@better-scroll/observe-dom": "^2.5.1", "@element-plus/icons-vue": "^2.1.0", "@fortaine/fetch-event-source": "^3.0.6", "@fortawesome/fontawesome-free": "^6.3.0", "@types/jquery": "^3.5.29", "@types/lodash-es": "^4.17.12", "@types/marked": "^4.0.8", "@types/qs": "^6.9.7", "@vueuse/core": "^10.11.0", "arcdash": "^0.0.51", "axios": "^1.3.4", "currency.js": "^2.0.4", "dayjs": "^1.11.11", "decimal.js": "^10.4.3", "echarts": "^5.4.2", "element-plus": "^2.9.6", "fs": "^0.0.1-security", "highlight.js": "^11.9.0", "html5-qrcode": "^2.3.8", "lint-staged": "^15.0.2", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-highlightjs": "^4.2.0", "marked": "^4.3.0", "mathjs": "^11.7.0", "mddir": "^1.1.1", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.0.33", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.1.0", "pinyin-pro": "^3.19.7", "qs": "^6.11.1", "screenfull": "^6.0.2", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.0", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.2", "vue-cropper": "^0.6.2", "vue-draggable-plus": "^0.2.6", "vue-hooks-plus": "^2.2.1", "vue-plugin-hiprint": "^0.0.56", "vue-router": "^4.1.6", "vue-svgicon": "^3.3.2", "vxe-pc-ui": "^4.0.31", "vxe-table": "^4.7.33", "xe-utils": "^3.5.7"}, "devDependencies": {"@antfu/eslint-config": "^2.6.4", "@babel/core": "^7.21.0", "@types/big.js": "^6.1.6", "@types/node": "^18.14.6", "@types/nprogress": "^0.2.0", "@vitejs/plugin-vue": "^5.0.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.14", "big.js": "^6.2.1", "cross-env": "^7.0.3", "daisyui": "^4.11.1", "eslint": "^8.57.0", "husky": "^8.0.3", "increase-memory-limit": "^1.0.7", "postcss": "^8.4.21", "postcss-pxtorem": "^6.1.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.58.3", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3.2.7", "typescript": "*", "unplugin-auto-import": "^0.15.1", "unplugin-vue-components": "^0.24.1", "unplugin-vue-define-options": "^1.3.2", "vite": "^5.0.5", "vite-plugin-vue-inspector": "^5.1.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^1.8.27"}}