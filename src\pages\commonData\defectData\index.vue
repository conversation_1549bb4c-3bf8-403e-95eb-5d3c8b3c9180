<script setup lang="ts" name="DefectData">
import { Delete, Plus } from '@element-plus/icons-vue'
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import AddDialog from './components/AddDialog.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { addInfoBasicDefect, deleteInfoBasicDefect, getInfoBasicDefectList, updateInfoBasicDefect, updateInfoBasicDefectStatus } from '@/api/defectData'
import { debounce, deleteToast, getFilterData, orderStatusConfirmBox, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'

const state = reactive({
  tableData: [],
  filterData: {
    name: '',
    status: '',
  },
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getInfoBasicDefectList()
const tableRef = ref()
// 获取数据
const getData = debounce(async () => {
  await ApiCustomerList(getFilterData(state.filterData))
  tableRef.value?.tableRef.sort('sort', 'asc')
  state.multipleSelection = []
}, 400)

onMounted(() => {
  getData()
})
const tableConfig = ref({
  loading,
  fieldApiKey: fieldApiKeyList.DefectData,
  showPagition: true,
  showSlotNums: false,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '6%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const AddDialogRef = ref()

const columnList = ref([
  {
    sortable: true,
    field: 'code',
    title: '疵点编号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'name',
    title: '疵点名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'measurement_unit_name',
    title: '单位名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sort',
    title: '排序',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    width: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    width: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'status',
    fixed: 'right',
    title: '状态',
    soltName: 'status',
    width: '5%',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handleAdd() {
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '验布疵点定义'
  AddDialogRef.value.state.form.code = ''
  AddDialogRef.value.state.form.measurement_unit_id = 0
  AddDialogRef.value.state.form.sort = 0
  AddDialogRef.value.state.form.name = ''
  AddDialogRef.value.state.form.remark = ''
  AddDialogRef.value.state.form.id = -1
}

function handEdit(row: any) {
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '验布疵点定义'
  AddDialogRef.value.state.form.code = row.code
  AddDialogRef.value.state.form.measurement_unit_id = row.measurement_unit_id
  AddDialogRef.value.state.form.sort = row.sort
  AddDialogRef.value.state.form.name = row.name
  AddDialogRef.value.state.form.remark = row.remark
  AddDialogRef.value.state.form.id = row.id
}

const { fetchData: AddFetch, msg: AddMsg, success: AddSuccess } = addInfoBasicDefect()

const { fetchData: putFetch, msg: putMsg, success: putSuccess } = updateInfoBasicDefect()

// 新建、编辑往来单位类型
async function handleSure(form: any) {
  const query = {
    ...form,
    sort: Number(form.sort),
  }
  form.id === -1 ? await AddFetch(query) : await putFetch(query)
  if (form.id === -1 ? AddSuccess.value : putSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    getData()
  }
  else {
    ElMessage.error(form.id === -1 ? AddMsg.value : putMsg.value)
  }
}

// 删除数据
async function handDelete(row: any) {
  await orderStatusConfirmBox({ id: row.id.toString(), audit_status: '1', message: { desc: '是否删除该疵点？', title: '删除提醒' }, api: deleteInfoBasicDefect })
  getData()
}

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const ids: any[] = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  await orderStatusConfirmBox({ id: ids.toString(), audit_status: '1', message: { desc: '是否批量删除疵点？', title: '删除提醒' }, api: deleteInfoBasicDefect })
  // await deleteFetch({ id: ids.toString() })
  // if (deleteSuccess.value) {
  //   ElMessage.success('成功')
  //   getData()
  // } else {
  //   ElMessage.error(deleteMsg.value)
  // }
  getData()
}

// 编辑状态
const { fetchData: statusFetch, msg: StatusMsg, success: StatusSuccess } = updateInfoBasicDefectStatus()

async function handStatus(row: any) {
  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    await statusFetch({ id: row.id.toString(), status: row.status === 1 ? 2 : 1 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      AddDialogRef.value.state.showModal = false
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 批量修改状态
async function handAll(val: number) {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    await statusFetch({ id: ids.toString(), status: val === 1 ? 1 : 2 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="名称:">
          <template #content>
            <el-input v-model="state.filterData.name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents v-model="state.filterData.status" style="width: 200px" api="StatusListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" tool-bar>
      <template #right-top>
        <el-button v-has="'DefectData_add'" style="margin-right: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button>批量操作</el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <div v-has="'MeasuringUnit_del'">
                <el-dropdown-item @click="handAllDelete">
                  批量删除
                </el-dropdown-item>
              </div>
              <div v-has="'MeasuringUnit_status'">
                <el-dropdown-item @click="handAll(1)">
                  批量启用
                </el-dropdown-item>
              </div>
              <div v-has="'MeasuringUnit_status'">
                <el-dropdown-item @click="handAll(2)">
                  批量禁用
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <Table ref="tableRef" :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'DefectData_delete'" :underline="false" type="primary" @click="handDelete(row)">
              删除
            </el-link>
            <el-link v-has="'DefectData_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-has="'DefectData_status'" type="primary" :underline="false" @click="handStatus(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
  <AddDialog ref="AddDialogRef" @handle-sure="handleSure" />
</template>

<style lang="scss" scoped>
::v-deep(.el-descriptions__cell) {
  width: 300px !important;
}

::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
