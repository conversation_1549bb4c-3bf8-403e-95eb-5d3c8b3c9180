<script setup lang="ts" name="MarketingSystemAdd">
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import Address from './components/address/index.vue'
import {
  AddSaleSystemApi,
  GetSaleSystemApi,
  UpdateSaleSystemApi,
} from '@/api/marketingSystem'
import { SettleType } from '@/common/enum'
import {
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
} from '@/common/format'
import { FAX_REGEXP, PHONE_REGEXP } from '@/common/rule'
import { getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
const data = ref<any>({
  name: '',
  code: '',
  contacts: '',
  phone: '',
  fax_number: '',
  address: '',
  email: '',
  remark: '',
  sale_radix_point_sign: '',
  sale_total_radix_point_sign: '',
  sale_tax_rate_definition: '',
  low_sale_weight_limit: '',
  high_sale_weight_limit: '',
  fin_pro_pur_radix_point_sign: '',
  default_physical_warehouse: '',
  fin_pro_pur_total_radix_point_sign: '',
  low_cloth_sale_weight_limit: '',
  high_cloth_sale_weight_limit: '',
  settle_type: SettleType.cashType,
  settle_cycle: 7,
  credit_limit: '',
  is_settle_limit: '',
  sale_order_prefix: '',
  return_order_prefix: '',
  arrange_order_prefix: '',
  purchase_order_prefix: '',
  allocate_order_prefix: '',
  produce_order_prefix: '',
  dyeing_order_prefix: '',
  delivery_order_prefix: '',
  default_customer_id: undefined, // 所属客户名称id
  low_sale_price_limit: 0, // 销售单价范围最小值
  high_sale_price_limit: 0, // 销售单价范围最大值
  default_last_sale_price: false, // 默认按最后一次销售报价
})

const route = useRoute()
const isEdit = route.params.id?.length > 0

const rules = ref<any>({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  phone: [
    {
      required: true,
      message: '请输入联系电话',
      trigger: 'blur',
    },
    {
      // pattern: PHONE_REGEXP,
      validator: (rule: any, value: any) => {
        // val匹配PHONE_REGEXP和FAX_REGEXP，有一个匹配上就返回true
        return PHONE_REGEXP.test(value) || FAX_REGEXP.test(value)
      },
      message: '手机号格式不对',
      trigger: 'blur',
    },
  ],
  // fax_number: [
  //   {
  //     pattern: FAX_REGEXP,
  //     message: '传真号格式不对',
  //     trigger: 'blur',
  //   },
  // ],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
  email: [{ type: 'email', message: '邮箱格式不正确' }],
  remark: [{ max: 100, message: '备注最多只能输入100字符' }],
  sale_weight_limit: [{ validator: validateSaleWeight, trigger: 'blur' }],
  cloth_sale_weight_limit: [
    { validator: validateClothSaleWeight, trigger: 'blur' },
  ],
  sale_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  return_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  arrange_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  purchase_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  allocate_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  produce_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  dyeing_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
  delivery_order_prefix: [{ max: 15, message: '最多只能输入15字符' }],
})

onMounted(async () => {
  if (route.params.id) {
    await getData()
    if (
      data.value.settle_type === SettleType.dayType
      || data.value.settle_type === SettleType.moonType
    ) {
      rules.value.custom_cycle = [
        { validator: validateNumber, trigger: 'blur' },
      ]
    }
  }
})

const { fetchData, data: dataList } = GetSaleSystemApi()
async function getData() {
  await fetchData({ id: route.params.id })
  data.value = dataList.value
  data.value.default_customer_id = dataList.value.default_customer_id ? dataList.value.default_customer_id : undefined
  data.value.credit_limit = formatPriceDiv(data.value.credit_limit || 0)
  data.value.low_cloth_sale_weight_limit = formatWeightDiv(
    data.value.low_cloth_sale_weight_limit || 0,
  )
  data.value.high_cloth_sale_weight_limit = formatWeightDiv(
    data.value.high_cloth_sale_weight_limit || 0,
  )
  data.value.low_sale_weight_limit = formatWeightDiv(
    data.value.low_sale_weight_limit || 0,
  )
  data.value.high_sale_weight_limit = formatWeightDiv(
    data.value.high_sale_weight_limit || 0,
  )
}

function validateSaleWeight(rule: any, value: any, callback: any) {
  if (data.value.low_sale_weight_limit > data.value.high_sale_weight_limit)
    callback(new Error('销售数量极限最大值不能小于最小值'))
  else callback()
}

function validateClothSaleWeight(rule: any, value: any, callback: any) {
  if (
    data.value.low_cloth_sale_weight_limit
    > data.value.high_cloth_sale_weight_limit
  )
    callback(new Error('坯布销售数量极限最大值不能小于最小值'))
  else callback()
}

function validateNumber(rule: any, value: any, callback: any) {
  if (value <= 0)
    callback(new Error('请输入大于0数字'))
  else callback()
}

const formRef = ref()
const {
  fetchData: fetchDataEdit,
  data: updateData,
  success: successEdit,
  msg: msgEdit,
} = UpdateSaleSystemApi()
const {
  fetchData: fetchDataAdd,
  data: addData1,
  success: successAdd,
  msg: msgAdd,
} = AddSaleSystemApi()
async function addData() {
  formRef.value!.validate(async (valid: any) => {
    if (valid) {
      const queryData = cloneDeep({
        ...data.value,
        low_sale_weight_limit: formatWeightMul(
          data.value.low_sale_weight_limit || 0,
        ),
        high_sale_weight_limit: formatWeightMul(
          data.value.high_sale_weight_limit || 0,
        ),
        low_cloth_sale_weight_limit: formatWeightMul(
          data.value.low_cloth_sale_weight_limit || 0,
        ),
        high_cloth_sale_weight_limit: formatWeightMul(
          data.value.high_cloth_sale_weight_limit || 0,
        ),
        low_sale_price_limit: formatUnitPriceMul(data.value.low_sale_price_limit || 0),
        high_sale_price_limit: formatUnitPriceMul(data.value.high_sale_price_limit || 0),
        credit_limit: formatPriceMul(data.value.credit_limit || 0),
      })
      if (route.params.id) {
        await fetchDataEdit(
          getFilterData({
            id: Number.parseInt(route.params.id as string),
            ...queryData,
          }),
        )
        if (successEdit.value) {
          ElMessage({
            type: 'success',
            message: '编辑成功',
            showClose: true,
          })
          getData()
          routerList.push({
            name: 'MarketingSystemDetail',
            params: { id: updateData.value.id },
          })
        }
        else {
          ElMessage({
            type: 'error',
            message: msgEdit.value,
            showClose: true,
          })
        }
      }
      else {
        await fetchDataAdd(getFilterData({ ...queryData }))
        if (successAdd.value) {
          ElMessage({
            type: 'success',
            message: '添加成功',
            showClose: true,
          })
          routerList.push({
            name: 'MarketingSystemDetail',
            params: { id: addData1.value.id },
          })
        }
        else {
          ElMessage({
            type: 'error',
            message: msgAdd.value,
            showClose: true,
          })
        }
      }
    }
  })
}

function chnageSettleType(val: any) {
  data.value.settle_cycle = ''
  data.value.custom_cycle = ''
  if (val.id === SettleType.dayType || val.id === SettleType.moonType) {
    rules.value.custom_cycle = [{ validator: validateNumber, trigger: 'blur' }]
  }
  else {
    data.value.settle_cycle = 7
    rules.value.custom_cycle = []
  }
}
</script>

<template>
  <div class="block">
    <div class="detail-container">
      <el-form ref="formRef" :model="data" :rules="rules" status-icon>
        <div class="mb-10">
          <div class="flex justify-between">
            <h2 style="font-weight: 700">
              基础信息
            </h2>
            <el-button type="primary" @click="addData">
              提交
            </el-button>
          </div>

          <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
            <DescriptionsFormItem required label="营销体系名称:">
              <template #content>
                <el-form-item prop="name">
                  <el-input
                    v-model="data.name"
                    clearable
                    placeholder="请输入营销体系名称"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="营销体系编号:">
              <template #content>
                <el-form-item prop="code">
                  <el-input
                    v-model="data.code"
                    clearable
                    placeholder="请输入营销体系编号"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="联系人:">
              <template #content>
                <el-form-item prop="contacts">
                  <el-input
                    v-model="data.contacts"
                    clearable
                    placeholder="请输入联系人"
                    maxlength="255"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem required label="联系电话:">
              <template #content>
                <el-form-item prop="phone">
                  <el-input
                    v-model="data.phone"
                    clearable
                    placeholder="请输入联系电话"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <!-- <DescriptionsFormItem required label="传真号:">
              <template #content>
                <el-form-item prop="fax_number">
                  <el-input
                    v-model="data.fax_number"
                    clearable
                    placeholder="请输入传真号"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem> -->
            <DescriptionsFormItem required label="地址:" copies="2">
              <template #content>
                <el-form-item prop="address">
                  <el-input
                    v-model="data.address"
                    type="textarea"
                    clearable
                    placeholder="请输入地址"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="邮箱:">
              <template #content>
                <el-form-item prop="email">
                  <el-input
                    v-model="data.email"
                    clearable
                    placeholder="请输入邮箱"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="备注:" copies="2">
              <template #content>
                <el-form-item prop="remark">
                  <el-input
                    v-model="data.remark"
                    type="textarea"
                    clearable
                    placeholder="请输入备注"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
          </div>
        </div>
        <div class="mb-10">
          <h2 style="font-weight: 700">
            销售信息
          </h2>
          <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
            <DescriptionsFormItem label="金额小数位:" width="375">
              <template #content>
                <el-form-item prop="sale_radix_point_sign">
                  <SelectComponents
                    v-model="data.sale_radix_point_sign"
                    api="GetRadixPointSignApi"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="总额小数位:" width="375">
              <template #content>
                <el-form-item prop="sale_total_radix_point_sign">
                  <SelectComponents
                    v-model="data.sale_total_radix_point_sign"
                    api="GetRadixPointSignApi"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="默认销售税率:" width="375">
              <template #content>
                <el-form-item prop="sale_tax_rate_definition">
                  <el-input
                    v-model="data.sale_tax_rate_definition"
                    clearable
                    placeholder="请输入"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <!-- <DescriptionsFormItem label="采购金额小数位:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.fin_pro_pur_radix_point_sign"
                    api="GetRadixPointSignApi"
                    placeholder="请输入成品采购金额小数位"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="成品采购小数位:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.fin_pro_pur_total_radix_point_sign"
                    api="GetRadixPointSignApi"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem> -->
            <!-- <DescriptionsFormItem label="预约默认仓库:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.default_physical_warehouse"
                    api="GetPhysicalWarehouseDropdownList"
                    label-field="name"
                    value-field="id"
                    clearable
                    placeholder="请选择预约默认仓库"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>

            <DescriptionsFormItem label="销售待送仓库:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.default_physical_warehouse"
                    api="GetPhysicalWarehouseDropdownList"
                    label-field="name"
                    value-field="id"
                    clearable
                    placeholder="请选择销售待送仓库"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>

            <DescriptionsFormItem label="默认胚布仓库:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.default_physical_warehouse"
                    api="GetPhysicalWarehouseDropdownList"
                    label-field="name"
                    value-field="id"
                    clearable
                    placeholder="请选择默认胚布仓库"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem> -->

            <DescriptionsFormItem label="所属客户名称:" width="375">
              <template #content>
                <el-form-item prop="default_customer_id">
                  <SelectComponents
                    v-model="data.default_customer_id"
                    api="GetCustomerEnumList"
                    label-field="name"
                    value-field="id"
                    clearable
                    placeholder="请选择所属客户名称"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="成品数量范围:" width="375">
              <template #content>
                <el-form-item prop="sale_weight_limit">
                  <el-input-number
                    v-model="data.low_sale_weight_limit"
                    style="width: 135px !important"
                    :precision="4"
                    controls-position="right"
                  />
                  -
                  <el-input-number
                    v-model="data.high_sale_weight_limit"
                    style="width: 137.5px !important"
                    :precision="4"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="坯布数量范围:" width="375">
              <template #content>
                <el-form-item prop="sale_weight_limit">
                  <el-input-number
                    v-model="data.low_cloth_sale_weight_limit"
                    style="width: 135px !important"
                    :precision="4"
                    controls-position="right"
                  />
                  -
                  <el-input-number
                    v-model="data.high_cloth_sale_weight_limit"
                    style="width: 137.5px !important"
                    :precision="4"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="销售单价范围:" width="375">
              <template #content>
                <el-form-item prop="sale_price_limit">
                  <el-input-number
                    v-model="data.low_sale_price_limit"
                    style="width: 135px !important"
                    :precision="4"
                    controls-position="right"
                  />
                  -
                  <el-input-number
                    v-model="data.high_sale_price_limit"
                    style="width: 137.5px !important"
                    :precision="4"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
          </div>
        </div>
        <div class="mb-10">
          <h2 style="font-weight: 700">
            结算信息
          </h2>
          <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
            <DescriptionsFormItem label="默认结算类型:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.settle_type"
                    api="GetSettleTypeApi"
                    label-field="name"
                    value-field="id"
                    @select="chnageSettleType"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem
              v-if="data.settle_type === SettleType.weekType"
              label="结算周期:"
            >
              <template #content>
                <el-form-item prop="settle_cycle">
                  <SelectComponents
                    v-model="data.settle_cycle"
                    api="GetSettleCycleApi"
                    label-field="name"
                    value-field="id"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem
              v-if="data.settle_type === SettleType.dayType"
              label="自定义天数:"
            >
              <template #content>
                <el-form-item prop="custom_cycle">
                  <el-input-number
                    v-model="data.custom_cycle"
                    :precision="0"
                    :min="0"
                    style="width: 130px"
                    placeholder="请输入默认结算天数"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem
              v-if="data.settle_type === SettleType.moonType"
              label="自定义月份:"
            >
              <template #content>
                <el-form-item prop="custom_cycle">
                  <el-input-number
                    v-model="data.custom_cycle"
                    :precision="0"
                    :min="0"
                    style="width: 130px"
                    placeholder="请输入默认结算天数"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="默认信用额度:">
              <template #content>
                <el-form-item prop="code">
                  <el-input-number
                    v-model="data.credit_limit"
                    :precision="0"
                    :min="0"
                    style="width: 130px"
                    placeholder="请输入默认信用额度"
                    controls-position="right"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="信用等级:">
              <template #content>
                <el-form-item prop="code">
                  <SelectComponents
                    v-model="data.credit_level"
                    api="CreditLevel"
                    label-field="name"
                    value-field="id"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem>
              <template #content>
                <el-form-item prop="code">
                  <el-checkbox v-model="data.default_last_sale_price" label="默认按最后一次销售报价" size="large" />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
          </div>
        </div>
        <h2 style="font-weight: 700">
          单号前缀及关联信息
        </h2>
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="销售单号前缀:">
            <template #content>
              <el-form-item prop="sale_order_prefix">
                <el-input
                  v-model="data.sale_order_prefix"
                  placeholder="请输入销售单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="退货单号前缀:">
            <template #content>
              <el-form-item prop="return_order_prefix">
                <el-input
                  v-model="data.return_order_prefix"
                  placeholder="请输入退货单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="配布单号前缀:">
            <template #content>
              <el-form-item prop="arrange_order_prefix">
                <el-input
                  v-model="data.arrange_order_prefix"
                  placeholder="请输入配布单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="采购单号前缀:">
            <template #content>
              <el-form-item prop="purchase_order_prefix">
                <el-input
                  v-model="data.purchase_order_prefix"
                  placeholder="请输入采购单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="调拨单号前缀:">
            <template #content>
              <el-form-item prop="allocate_order_prefix">
                <el-input
                  v-model="data.allocate_order_prefix"
                  placeholder="请输入调拨单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="生产单号前缀:">
            <template #content>
              <el-form-item prop="produce_order_prefix">
                <el-input
                  v-model="data.produce_order_prefix"
                  placeholder="请输入生产单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="染整单号前缀:">
            <template #content>
              <el-form-item prop="dyeing_order_prefix">
                <el-input
                  v-model="data.dyeing_order_prefix"
                  placeholder="请输入染整单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="送货单号前缀:">
            <template #content>
              <el-form-item prop="delivery_order_prefix">
                <el-input
                  v-model="data.delivery_order_prefix"
                  placeholder="请输入送货单号前缀"
                  clearable
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>

        <div v-if="isEdit">
          <h2 style="font-weight: 700">
            回货地址
          </h2>
          <Address :sale_system_id="route.params.id as string" />
        </div>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-form-item) {
  margin-bottom: 0;
}
::v-deep(.el-descriptions) {
  margin-bottom: 10px;
}
</style>
