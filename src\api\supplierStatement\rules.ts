export interface GetSupplierReconciliationListData {
  id: number
  createTime: string
  updateTime: string
  creatorId: number
  createUserName: string
  updaterId: number
  updateUserName: string
  orderId: number
  orderNo: string
  orderTime: string
  orderType: number
  orderTypeName: string
  code: string
  name: string
  yarnBatch: string
  brand: string
  batchNum: string
  productColorCode: string
  productColorName: string
  dyeColorCode: string
  dyeColorName: string
  dyelotNumber: string
  measurementUnitId: number
  measurementUnitName: string
  roll: number
  weight: number
  salePrice: number
  otherPrice: number
  settlePrice: number
  discountPrice: number
  chargebackMoney: number
  totalPrice: number
  orderTotalPrice: number
  finishingUnitPrice: number
  paperTubeUnitPrice: number
  plasticBagUnitPrice: number
  balance: number
}
