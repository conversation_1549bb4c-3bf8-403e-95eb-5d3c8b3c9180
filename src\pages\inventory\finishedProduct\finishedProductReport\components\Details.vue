<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { DetailsTable, FinfshProductColor, FinishProductColorColumn, flattenObject } from './pullDown'
import { GetFinishProductReportList, GetFinishProductReportListExport } from '@/api/inventory/finishedProduct/finishedProductReport'
import GridTable from '@/components/GridTable'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { vueEffect } from '@/use/vueEffect'
import { formatDate, sumNum } from '@/common/format'
import { getFilterData } from '@/common/util'
import { processDataOut } from '@/common/handBinary'
import { useListExport } from '@/hooks/useListExport'
import { WarehouseTypeIdEnum } from '@/common/enum'

import BottonExcel from '@/components/BottonExcel/index.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const { handleExport, loadingExcel } = useListExport()
const tableRef = ref() // 当前表格的实例

const getDataLoading = ref(false) // 获取表格数据的 loading
const query = ref<any>({ // 获取表格的请求参数
  product_id: '', // 成品id
  product_color_id: '', // 颜色id
  date: [new Date(), new Date()], // 日期
  warehouse_id: [], // 仓库id
  product_level_id: '', // 成品等级id
  product_remark: '', // 成品备注
  page: 1,
  size: 50,
})
// 用于筛选的临时存储变量
const componentRemoteSearch = ref({
  finish_product_code: '', // 临时的成品编号
  finish_product_name: '', // 临时的成品名称
  color_name: '', // 颜色编号
  color_code: '', // 颜色名称
})
// 选中成品编号或者成品名称
function changeProductSelect(val: any) {
  query.value.product_id = val?.id || '' // 成品 id
  componentRemoteSearch.value.finish_product_code = val?.finish_product_code || ''
  componentRemoteSearch.value.finish_product_name = val?.finish_product_name || ''

  query.value.product_color_id = ''
  componentRemoteSearch.value.color_name = ''
  componentRemoteSearch.value.color_code = ''
}
// 选择颜色
function changeColor(val: any) {
  query.value.product_color_id = val?.id || ''
  componentRemoteSearch.value.color_code = val?.product_color_code || ''
  componentRemoteSearch.value.color_name = val?.product_color_name || ''
}

// 日期的快捷选中
const shortcuts: any = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

const { fetchData: getSummaryReport, total } = GetFinishProductReportList() // 获取表格数据
const tableData = ref([]) // 暂时存储表格数据
// 分页配置
const elPaginationConfig = computed(() => ({
  defaultPageSize: 50,
  pageSizes: [50, 100, 500, 1000],
  page: query.value.page,
  size: query.value.size,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: total.value,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))
// 当页码发生变化的时候
function handleSizeChange(val: number) {
  query.value.page = 1
  query.value.size = val
}
// 当每页数量发生变化的时候
function handleCurrentChange(val: number) {
  query.value.page = val
}

function getQuery() {
  const reqData = {
    product_id: query.value.product_id,
    product_color_id: query.value.product_color_id,
    product_level_id: query.value.product_level_id,
    warehouse_id: query.value.warehouse_id.length ? query.value.warehouse_id.join(',') : null,
    start_date: query.value.date && query.value.date.length && query.value.date.length ? formatDate(query.value.date[0]) : '',
    end_date: query.value.date && query.value.date.length && query.value.date.length ? formatDate(query.value.date[1]) : '',
    page: query.value.page,
    size: query.value.size,
    product_remark: query.value.product_remark,
    type: 2,
  }
  return getFilterData(reqData)
}

async function getData() {
  tableData.value = []
  getDataLoading.value = true
  // 组装请求数据
  const result = await getSummaryReport(getQuery())
  if (result.success) {
    const temData = processDataOut(result.data.list)
    const flattenedArr = temData.map((item: any) => flattenObject(item)) || []
    //
    tableData.value = flattenedArr
  }
  getDataLoading.value = false
}

vueEffect(async () => {
  await getData()
}, [query.value], 300, false, 'debounce')

onMounted(async () => {
  await getData()
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'
      const periodField = ['period_in_process_roll', 'period_in_process_weight', 'period_in_purchase_roll', 'period_in_purchase_weight', 'period_in_return_roll', 'period_in_return_weight', 'period_in_transfer_roll', 'period_in_transfer_weight', 'period_in_other_roll', 'period_in_other_weight', 'period_out_process_roll', 'period_out_process_weight', 'period_out_purchase_roll', 'period_out_purchase_weight', 'period_out_return_roll', 'period_out_return_weight', 'period_out_transfer_roll', 'period_out_transfer_weight', 'period_out_other_roll', 'period_out_other_weight', 'current_stock_roll', 'current_stock_weight', 'period_adjust_roll', 'period_adjust_weight']

      if (
        [
          'last_stock_roll',
          'last_stock_weight',
          ...periodField,
          'ending_stock_roll',
          'ending_stock_weight',
        ].includes(column.property)
      )
        return `${(periodField.includes(column.property) ? sumNum(data, column.property) || '' : sumNum(data, column.property))}`
      return null
    }),
  ]
}
</script>

<template>
  <!-- 筛选信息 -->
  <FildCard title="" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="日期:" copies="2">
        <template #content>
          <el-date-picker
            v-model="query.date"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="仓库名称:">
        <template #content>
          <SelectComponents v-model="query.warehouse_id" :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }" api="GetPhysicalWarehouseDropdownList" multiple label-field="name" value-field="id" clearable />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品编号:">
        <template #content>
          <SelectProductDialog
            v-model="query.product_id"
            :label-name="componentRemoteSearch.finish_product_code"
            field="finish_product_code"
            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }"
            @on-input="(val) => (componentRemoteSearch.finish_product_code = val)"
            @change-value="changeProductSelect"
          />
          <!--          <SelectDialog -->
          <!--            v-model="query.product_id" -->
          <!--            label-field="finish_product_code" -->
          <!--            :label-name="componentRemoteSearch.finish_product_code" -->
          <!--            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }" -->
          <!--            api="GetFinishProductDropdownList" -->
          <!--            :column-list="FinishedProduct" -->
          <!--            :table-column="[FinishProductTableColumn[0]]" -->
          <!--            @on-input="(val) => (componentRemoteSearch.finish_product_code = val)" -->
          <!--            @change-value="changeProductSelect" -->
          <!--          /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品名称:">
        <template #content>
          <SelectProductDialog
            v-model="query.product_id"
            :label-name="componentRemoteSearch.finish_product_name"
            field="finish_product_name"
            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }"
            @on-input="(val) => (componentRemoteSearch.finish_product_name = val)"
            @change-value="changeProductSelect"
          />
          <!--          <SelectDialog -->
          <!--            v-model="query.product_id" -->
          <!--            label-field="finish_product_name" -->
          <!--            :label-name="componentRemoteSearch.finish_product_name" -->
          <!--            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }" -->
          <!--            api="GetFinishProductDropdownList" -->
          <!--            :column-list="FinishedProduct" -->
          <!--            :table-column="[FinishProductTableColumn[1]]" -->
          <!--            @on-input="(val) => (componentRemoteSearch.finish_product_name = val)" -->
          <!--            @change-value="changeProductSelect" -->
          <!--          /> -->
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色编号:">
        <template #content>
          <SelectDialog
            key="color2"
            v-model="query.product_color_id"
            :disabled="!query.product_id"
            :label-name="componentRemoteSearch.color_code"
            :query="{
              finish_product_id: query.product_id,
              product_color_code: componentRemoteSearch.color_code,
            }"
            :column-list="FinfshProductColor"
            :table-column="[FinishProductColorColumn[0]]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            @on-input="(val) => (componentRemoteSearch.color_code = val)"
            @change-value="changeColor"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色名称:">
        <template #content>
          <SelectDialog
            key="color1"
            v-model="query.product_color_id"
            :disabled="!query.product_id"
            :label-name="componentRemoteSearch.color_name"
            :query="{
              finish_product_id: query.product_id,
              product_color_name: componentRemoteSearch.color_name,
            }"
            :column-list="FinfshProductColor"
            :table-column="[FinishProductColorColumn[1]]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            @on-input="(val) => (componentRemoteSearch.color_name = val)"
            @change-value="changeColor"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品等级:">
        <template #content>
          <SelectComponents
            v-model="query.product_level_id"
            placeholder="成品等级"
            api="GetInfoBaseFinishedProductLevelEnumList"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品备注:">
        <template #content>
          <vxe-input v-model="query.product_remark" placeholder="成品备注" />
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <!-- 表格 -->
  <FildCard title="" class="flex-1 flex flex-col overflow-y-hidden">
    <template #right-top>
      <BottonExcel
        v-has="'FinishedProductReportDetailsExport'"
        :loading="loadingExcel"
        title="导出文件"
        @on-click-excel="handleExport({
          tableList: tableData,
          apiRequest: GetFinishProductReportListExport,
          query: getQuery(),
          tableName: '成品颜色汇总',
        })"
      />
    </template>
    <GridTable
      ref="tableRef"
      :columns="DetailsTable"
      :data="tableData"
      :config="{
        loading: getDataLoading,
        fieldApiKey: 'finishedProductReportDetails',
        rowConfig: { keyField: 'id' },
        filterConfig: {
          showIcon: false,
        },
        footerMethod: (val: any) => FooterMethod(val) }"
      height="100%"
      show-pagition
      :el-pagination-config="elPaginationConfig"
    />
  </FildCard>
</template>

<style></style>
