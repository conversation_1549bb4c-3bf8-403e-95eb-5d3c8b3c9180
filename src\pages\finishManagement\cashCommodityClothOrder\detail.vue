<script setup lang="ts" name="cashCommodityClothOrderDetail">
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import AccordingLibAdd from '../components/AccordingRepertoryAdd.vue'
import FineSizeAdd from '../components/FineSizeCashCommodityClothAdd.vue'
import FineSizeSelectStockDetail from '../components/FineSizeSelectStockDetail.vue'
import {
  getFpmArrangeOrder,
  outFpmArrangeOrder,
  updateFpmArrangeOrder,
  updateFpmArrangeOrderStatusCancel,
  updateFpmArrangeOrderStatusPass,
  updateFpmArrangeOrderStatusReject,
  updateFpmArrangeOrderStatusWait,
} from '@/api/cashCommodityClothOrder'
import { formatDate, formatLengthDiv, formatLengthMul, formatPriceDiv, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { deleteToast, orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { deliverFromGoDownJumpPage } from '@/pages/finishManagement/cashCommodityClothOrder/util'
import { WarehouseGoodOutTypeEnum } from '@/enum'

const route = useRoute()
const tablesRef = ref()

const form_options = [
  {
    text: '来源单号',
    key: 'src_order_no',
  },
  {
    text: '出货类型',
    key: 'out_order_type_name',
  },
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '配布日期',
    key: 'arrange_time',
  },
  {
    text: '调至仓库名称',
    key: 'arrange_to_warehouse_name',
  },
  {
    text: '仓库名称',
    key: 'warehouse_name',
  },
  {
    text: '往来单位名称',
    key: 'biz_unit_name',
  },
  {
    text: '加工厂名称',
    key: 'process_factory_name',
  },
  {
    text: '收货地址',
    key: 'receive_addr',
  },
  {
    text: '收货电话',
    key: 'receive_phone',
  },
  {
    text: '收货标签',
    key: 'receive_tag',
  },
  {
    text: '司机名称',
    key: 'driver_name',
  },
  {
    text: '销售员',
    key: 'sale_user_name',
  },
  {
    text: '销售跟单员',
    key: 'sale_follower_name',
  },
  {
    text: '仓管员',
    key: 'store_keeper_name',
  },
  {
    text: '物流公司',
    key: 'logistics_company_name',
  },
  {
    text: '订单类型',
    key: 'sale_mode_name',
  },
  {
    text: '内部备注',
    key: 'internal_remark',
  },
  {
    text: '销售备注',
    key: 'sale_remark',
  },
]
let uuid = 0
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const componentKey = ref(0)
const state = reactive<any>({
  baseData: {
    order_no: '',
    audit_status_name: '',
    audit_status: 1,
    arrange_order_no: '',
  },
})

const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    fieldApiKey: 'CashCommodityClothOrderDetail',
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: false,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (
          ['arrange_weight', 'arrange_roll', 'actually_weight', 'settle_weight', 'sum_stock_roll', 'sum_stock_weight', 'sum_stock_length', 'weight_error', 'settle_error_weight'].includes(column.field)
        )
          return sumNum(data, column.field, '', 'float')

        if (['push_roll'].includes(column.field))
          return formatPriceDiv(sumNum(data, 'push_roll', '', 'float'))

        if (['push_weight'].includes(column.field))
          return formatWeightDiv(sumNum(data, 'push_weight', '', 'float'))

        if (['arrange_length'].includes(column.field))
          return sumNum(data, 'arrange_length', '', 'int')

        if (['other_price'].includes(column.field))
          return sumNum(data, 'other_price', '￥', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      field: 'A',
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'push_roll',
          title: '下推匹数',
          minWidth: 100,
          isPrice: true,
        },
        {
          field: 'push_weight',
          title: '下推数量',
          isWeight: true,
          minWidth: 100,
        },
      ],
    },
    {
      field: 'C',
      title: '库存信息',
      childrenList: [
        {
          field: 'sum_stock_roll',
          title: '可用匹数',
          minWidth: 100,
        },
        {
          field: 'sum_stock_weight',
          title: '可用数量',
          minWidth: 100,
        },
        {
          field: 'sum_stock_length',
          title: '库存辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'D',
      title: '出仓数量信息',
      childrenList: [
        {
          field: 'arrange_roll',
          title: '出仓匹数',
          minWidth: 100,
        },
        {
          field: 'arrange_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'E',
      title: '出仓辅助数量信息',
      childrenList: [
        {
          field: 'arrange_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'F',
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'G',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
  handleSureFineSize: (list: any) => {
    // 刷新组件
    componentKey.value++

    let arrange_weight = 0
    let weight_error = 0
    let settle_error_weight = 0
    let arrange_length = 0
    let roll = 0

    list.forEach((item: any) => {
      arrange_weight += Number(item.base_unit_weight)
      weight_error += Number(item.weight_error)
      settle_error_weight += Number(item.settle_error_weight)
      arrange_length += Number(item.length)
      roll += Number(item.roll)
    })
    // const arrange_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)
    const actually_weight = arrange_weight - weight_error
    // const settle_error_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_error_weight), 0)
    const settle_weight = actually_weight - settle_error_weight
    // 出仓匹数
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].arrange_roll = roll
    // const arrange_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].arrange_weight = Number(arrange_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].arrange_length = Number(arrange_length.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number(settle_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].actually_weight = Number(actually_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_error_weight = Number(settle_error_weight.toFixed(2))

    // 强制更新表格尾
    nextTick(() => {
      tablesRef.value.tableRef?.updateFooter()
    })
  },
})

const special = computed(() => {
  return finishProductionOptions.datalist.some(item => item.arrange_roll === 0)
})
function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
}

function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex

  FineSizeAddRef.value.showDialog(
    {
      ...row,
      out_roll: row.arrange_roll,
      push_roll: row.push_roll,
      same_color_same_dye_lot: state.baseData.same_color_same_dye_lot,
    },
    { warehouse_id: state.baseData.warehouse_id },
  )
}
// const formRef = ref()
// 提交所有数据
const { fetchData: updateFetch, success: updateSuccess, msg: updateMsg } = updateFpmArrangeOrder()
async function submitAddAllData() {
  const query = {
    ...state.baseData,
    id: Number(route.query.id),
    store_keeper_id: state.baseData.store_keeper_id || 0,
    arrange_time: formatDate(state.baseData.arrange_time),
    item_data: [],
  }
  if (!query.arrange_time)
    return ElMessage.error('配布日期为必填项')

  // 校验成品信息
  if (!finishProductionOptions.datalist.length)
    return ElMessage.error('请添加成品信息')

  // let FSLengthFlag = true
  // finishProductionOptions.datalist.forEach((item: any) => {
  //   const FSLen = item.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
  //   if (FSLen !== Number(item.arrange_length)) {
  //     FSLengthFlag = false
  //   }
  // })

  // if (!FSLengthFlag) {
  //   return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
  // }

  for (const item of finishProductionOptions.datalist) {
    const item_fc_data = item.item_fc_data.map((v: any) => {
      return {
        ...v,
        roll: formatTwoDecimalsMul(Number(v.roll)),
        base_unit_weight: formatWeightMul(Number(v.base_unit_weight)),
        paper_tube_weight: formatWeightMul(Number(v.paper_tube_weight)),
        weight_error: formatWeightMul(Number(v.weight_error)),
        settle_error_weight: formatWeightMul(Number(v.settle_error_weight)),
        settle_weight: formatWeightMul(Number(v.settle_weight)),
        actually_weight: formatWeightMul(Number(v.actually_weight)),
        length: formatLengthMul(Number(v.length)),
      }
    })
    if (!item_fc_data.length)
      return ElMessage.error('请录入细码信息')

    query.item_data.push({
      ...item,
      arrange_roll: formatTwoDecimalsMul(Number(item.arrange_roll)), // 100
      sum_stock_roll: formatTwoDecimalsMul(Number(item.sum_stock_roll)), // 100
      sum_stock_weight: formatWeightMul(Number(item.sum_stock_weight)), // weight
      sum_stock_length: formatLengthMul(Number(item.sum_stock_length)), // 100
      arrange_weight: formatWeightMul(Number(item.arrange_weight)), // weight
      weight_error: formatWeightMul(Number(item.weight_error)), // weight
      settle_error_weight: formatWeightMul(Number(item.settle_error_weight)),
      actually_weight: formatWeightMul(Number(item.actually_weight)),
      settle_weight: formatWeightMul(Number(item.settle_weight)), // weight
      unit_price: formatUnitPriceMul(Number(item.unit_price)), // price
      arrange_length: formatLengthMul(Number(item.arrange_length)), // 100
      length_unit_price: formatUnitPriceMul(Number(item.length_unit_price)), // price
      other_price: formatTwoDecimalsMul(Number(item.other_price)), // 100
      total_price: formatTwoDecimalsMul(Number(item.total_price)), // 100
      item_fc_data,
    })
  }
  // query.item_data = finishProductionOptions.datalist.map((item: any) => {
  //   const item_fc_data = item.item_fc_data.map((v: any) => {
  //     return {
  //       ...v,
  //       roll: formatTwoDecimalsMul(Number(v.roll)),
  //       base_unit_weight: formatWeightMul(Number(v.base_unit_weight)),
  //       paper_tube_weight: formatWeightMul(Number(v.paper_tube_weight)),
  //       weight_error: formatWeightMul(Number(v.weight_error)),
  //       settle_error_weight: formatWeightMul(Number(v.settle_error_weight)),
  //       settle_weight: formatWeightMul(Number(v.settle_weight)),
  //       actually_weight: formatWeightMul(Number(v.actually_weight)),
  //       length: formatTwoDecimalsMul(Number(v.length)),
  //     }
  //   })
  //   return {
  //     ...item,
  //     arrange_roll: formatTwoDecimalsMul(Number(item.arrange_roll)), // 100
  //     sum_stock_roll: formatTwoDecimalsMul(Number(item.sum_stock_roll)), // 100
  //     sum_stock_weight: formatWeightMul(Number(item.sum_stock_weight)), // weight
  //     sum_stock_length: formatTwoDecimalsMul(Number(item.sum_stock_length)), // 100
  //     arrange_weight: formatWeightMul(Number(item.arrange_weight)), // weight
  //     weight_error: formatWeightMul(Number(item.weight_error)), // weight
  //     settle_error_weight: formatWeightMul(Number(item.settle_error_weight)),
  //     actually_weight: formatWeightMul(Number(item.actually_weight)),
  //     settle_weight: formatWeightMul(Number(item.settle_weight)), // weight
  //     unit_price: formatUnitPriceMul(Number(item.unit_price)), // price
  //     arrange_length: formatTwoDecimalsMul(Number(item.arrange_length)), // 100
  //     length_unit_price: formatUnitPriceMul(Number(item.length_unit_price)), // price
  //     other_price: formatTwoDecimalsMul(Number(item.other_price)), // 100
  //     total_price: formatTwoDecimalsMul(Number(item.total_price)), // 100
  //     item_fc_data,
  //   }
  // })

  await updateFetch({
    ...query,
    id: Number(route.query.id),
  })
  if (updateSuccess.value) {
    ElMessage.success('提交成功')
    getData()
  }
  else {
    ElMessage.error(updateMsg.value)
  }
}

const { fetchData, data: detailData } = getFpmArrangeOrder()
async function getData() {
  await fetchData({ id: route.query.id })
  state.baseData = {
    ...detailData.value,
    arrange_time: formatDate(detailData.value.arrange_time),
  }
  finishProductionOptions.datalist = detailData.value.item_data.map((item: any) => {
    const item_fc_data
      = item.item_fc_data?.map((v: any) => {
        return {
          ...v,
          roll: formatTwoDecimalsDiv(Number(v.roll)),
          base_unit_weight: formatWeightDiv(Number(v.base_unit_weight)),
          paper_tube_weight: formatWeightDiv(Number(v.paper_tube_weight)),
          weight_error: formatWeightDiv(Number(v.weight_error)),
          settle_weight: formatWeightDiv(Number(v.settle_weight)),
          actually_weight: formatWeightDiv(Number(v.actually_weight)),
          settle_error_weight: formatWeightDiv(Number(v.settle_error_weight)),
          length: formatLengthDiv(Number(v.length)),
        }
      }) || []

    return {
      uuid: ++uuid,
      ...item,
      arrange_roll: formatTwoDecimalsDiv(Number(item.arrange_roll)), // 100
      sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)), // 100
      sum_stock_weight: formatWeightDiv(Number(item.sum_stock_weight)), // weight
      sum_stock_length: formatLengthDiv(Number(item.sum_stock_length)), // 100
      actually_weight: formatWeightDiv(Number(item.actually_weight)), // weight
      arrange_weight: formatWeightDiv(Number(item.arrange_weight)), // weight
      weight_error: formatWeightDiv(Number(item.weight_error)), // weight
      settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)), // weight
      settle_weight: formatWeightDiv(Number(item.settle_weight)), // weight
      unit_price: formatUnitPriceDiv(Number(item.unit_price)), // price
      arrange_length: formatLengthDiv(Number(item.arrange_length)), // 100
      length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)), // price
      other_price: formatTwoDecimalsDiv(Number(item.other_price)), // 100
      total_price: formatTwoDecimalsDiv(Number(item.total_price)), // 100
      item_fc_data,
    }
  })
}
onMounted(() => {
  getData()
})

async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateFpmArrangeOrderStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateFpmArrangeOrderStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateFpmArrangeOrderStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateFpmArrangeOrderStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}
const { fetchData: outFetch, data, success: outSuccess, msg: outMsg } = outFpmArrangeOrder()
async function deliverFromGodown() {
  const res = await deleteToast('是否确认出仓？')
  if (res) {
    await outFetch({
      id: Number(route.query.id),
    })
    if (outSuccess.value) {
      ElMessage.success('成功')
      getData()
      // 齐单提货&销售出仓单不跳转
      if (state.baseData.pick_up_goods_in_order && state.baseData.out_order_type === WarehouseGoodOutTypeEnum.Sale)
        return

      // 根据出货类型需要跳转到对应的详情页面
      deliverFromGoDownJumpPage(state.baseData, data.value)
    }
    else {
      ElMessage.error(outMsg.value)
    }
  }
}

const FineSizeCashCommodityClothDetailRef = ref()
function showDialog(row: any) {
  FineSizeCashCommodityClothDetailRef.value.state.isCloth = true
  FineSizeCashCommodityClothDetailRef.value.showDialog({ ...row, same_color_same_dye_lot: detailData.value.same_color_same_dye_lot }, true)
}

const canSubmit = computed(() => {
  let listSize = 0
  finishProductionOptions.datalist.forEach((item) => {
    if (item.item_fc_data.length > 0)
      listSize++
  })
  return listSize > 0
})
</script>

<template>
  <StatusColumn
    :order_no="state.baseData.order_no"
    :status="state.baseData.audit_status"
    :status_name="state.baseData.audit_status_name"
    :business_status="state.baseData.business_status"
    :business_status_name="state.baseData.business_status_name"
    permission_print_key=""
    permission_wait_key="CashCommodityClothOrder_wait"
    permission_reject_key="CashCommodityClothOrder_reject"
    permission_pass_key="CashCommodityClothOrder_pass"
    permission_cancel_key="CashCommodityClothOrder_cancel"
    permission_deliver_key="CashCommodityClothOrder_deliver"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
    @deliver-from-godown="deliverFromGodown"
  >
    <el-button v-if="special" v-has="'CashCommodityClothOrder_deliver'" type="primary" @click="deliverFromGodown">
      确认出仓
    </el-button>
  </StatusColumn>
  <FildCard title="基础信息" class="mt-[5px]" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :label="`${item.text}:`">
        <template #content>
          {{ state?.baseData[item.key] }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <Table ref="tablesRef" :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
      <!-- 出仓匹数 -->
      <template #arrange_roll="{ row }">
        <vxe-input v-model="row.arrange_roll" min="0" type="float" />
      </template>
      <!-- 出仓辅助数量 -->
      <template #arrange_length="{ row }">
        <vxe-input v-model="row.arrange_length" min="0" type="float" />
      </template>
      <!-- 其他金额 -->
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" min="0" type="float" />
      </template>
      <!-- 结算金额 -->
      <template #total_price="{ row }">
        ￥{{ row?.total_price }}
      </template>
      <!-- 单价 -->
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" min="0" type="float" />
      </template>
      <!-- 辅助数量单价 -->
      <template #length_unit_price="{ row }">
        <vxe-input v-model="row.length_unit_price" min="0" type="float" />
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" max-length="200" type="text" />
      </template>
      <!-- 细码 -->
      <template #xima="{ row, rowIndex }">
        <!-- 进仓数量 -->
        <div v-if="state.baseData.business_status < 3 && state.baseData.audit_status !== 4">
          <el-button type="primary" text link @click="showFineSizeDialog(row, rowIndex)">
            录入
            <div v-if="row.push_roll !== 0">
              <span v-if="formatPriceDiv(row.push_roll) - getSFRoll(row) > 0" style="color: red">({{ formatPriceDiv(row?.push_roll) - getSFRoll(row) }}条未录)</span>
              <span v-if="formatPriceDiv(row.push_roll) === getSFRoll(row)" style="color: #ccc">{{ '(已录入)' }}</span>
            </div>
            <div v-else>
              <span v-if="!row.item_fc_data?.length" style="color: red">(未录)</span>
              <span v-else style="color: #ccc">{{ '(已录入)' }}</span>
            </div>
          </el-button>
          <!--          <span v-else style="color: #ccc">请先输入出仓匹数</span> -->
        </div>
        <div v-else>
          <el-button type="primary" text link @click="showDialog(row)">
            查看
          </el-button>
        </div>
      </template>
      <!-- 操作 -->
      <!-- <template #operate="{ row }">
          <el-button type="text" @click="finishProductionOptions.handleRowDel(row)">删除</el-button>
        </template> -->
    </Table>

    <div class="flex_button">
      <el-button v-if="state.baseData.business_status < 3 && state.baseData.audit_status !== 4" type="primary" :disabled="!canSubmit" @click="submitAddAllData">
        提交
      </el-button>
    </div>
  </FildCard>
  <AccordingLibAdd ref="AccordingLibAddRef" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" :key="componentKey" @handle-sure="finishProductionOptions.handleSureFineSize" />
  <FineSizeSelectStockDetail ref="FineSizeCashCommodityClothDetailRef" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

div {
  color: #333;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 120px;
    }

    .value {
      width: 80%;
    }
  }
}
</style>
