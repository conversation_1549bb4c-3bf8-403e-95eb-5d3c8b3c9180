<script setup lang="ts" name="FpSaleDeliverFromGodownOrderEdit">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import AccordingLibAdd from '../components/AccordingRepertoryAdd.vue'
import AssociatedPurchasingSalesReturn from '../components/AssociatedPurchasingSalesReturn.vue'
import FineSizeAdd from '../components/FineSizeSaleOrderAdd.vue'
import FineSizeSelectStockDetail from '../components/FineSizeSelectStockDetail.vue'
import { isFinishEnter } from './utils'
import { getFpmSaleOutOrder, updateFpmSaleOutOrder } from '@/api/fpSaleDeliverFromGodownOrder'
import { EmployeeType, WarehouseTypeIdEnum } from '@/common/enum'
import { formatDate, formatLengthDiv, formatLengthMul, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { deleteToast, getFilterData, isMainUnit } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import { isCustomerBook } from '@/components/SelectSaleMode/common'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const routerList = useRouterList()
let uuid = 0
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const state = reactive<any>({
  formInline: {
    arrange_order_no: '',
    sale_system_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    warehouse_out_time: new Date(),
    store_keeper_id: '',
    remark: '',
    customer_id: '',
    process_factory_id: '',
    receive_addr: '',
    receive_phone: '',
    receive_tag: '',
    sale_user_id: '',
    sale_follower_id: '',
    arrange_user_id: '',
    driver_id: [],
    logistics_company_id: '',
    logistics_company_area: '',
    internal_remark: '',
    sale_remark: '',
    sale_mode: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    warehouse_out_time: [{ required: true, message: '请选择出仓日期', trigger: 'change' }],
    warehouse_id: [{ required: true, message: '请选择仓库名称', trigger: 'change' }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
})

const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    fieldApiKey: 'fpSaleDeliverFromGodownOrderEdit',
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['actually_weight', 'out_roll', 'settle_weight', 'out_length', 'total_weight'].includes(column.field))
          return sumNum(data, column.field, '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      field: 'B',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'out_roll',
          soltName: 'out_roll',
          title: '出仓匹数',
          minWidth: 100,
          required: true,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 70,
        },
      ],
    },
    {
      title: '库存信息',
      field: 'C',
      childrenList: [
        {
          field: 'sum_stock_roll',
          title: '可用匹数',
          minWidth: 100,
        },
        {
          field: 'sum_stock_weight',
          title: '可用数量',
          minWidth: 100,
        },
        {
          field: 'sum_stock_length',
          title: '库存辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '结算单位',
      field: 'F',
      childrenList: [
        {
          field: 'auxiliary_unit_id',
          title: '结算单位',
          width: 100,
          soltName: 'auxiliary_unit_id',
        },
      ],
    },
    {
      title: '数量信息',
      field: 'D',
      childrenList: [
        {
          field: 'total_weight',
          title: '出仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: 100,
        },
        {
          field: 'actually_weight',
          title: '码单数量',
          minWidth: 100,
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: 100,
        },
        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '辅助数量信息',
      field: 'E',
      childrenList: [
        {
          field: 'out_length',
          soltName: 'out_length',
          title: '辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      title: '单据备注信息',
      field: 'F',
      childrenList: [
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      field: 'xima',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          fixed: 'right',
          width: 100,
        },
      ],
    },
    {
      title: '',
      field: 'operate',
      childrenList: [
        {
          field: 'operate',
          soltName: 'operate',
          fixed: 'right',
          title: '操作',
          width: 100,
        },
      ],
    },
  ],
  handleSure: (list: any) => {
    // 成品成分
    // 取配布单不可修改
    list = list.map((item: any) => {
      const temp = {
        // ...item,
        uuid: ++uuid,
        sum_stock_id: item.stock_product_id,
        quote_order_no: '',
        product_code: item.product_code,
        product_name: item.product_name,
        product_id: item.product_id,
        customer_name: item.customer_name,
        customer_id: item.customer_id,
        product_color_code: item.product_color_code,
        product_color_id: item.product_color_id,
        product_color_name: item.product_color_name,
        dye_factory_dyelot_number: item.dyelot_number,
        product_level_name: item.product_level_name,
        product_level_id: item.product_level_id,
        product_remark: item.product_remark,
        product_craft: item.finish_product_craft,
        sum_stock_roll: item.available_roll,
        sum_stock_weight: item.available_weight,
        sum_stock_length: item.length,
        unit_name: item.measurement_unit_name,
        unit_id: item.measurement_unit_id,
        product_ingredient: item.finish_product_ingredient,
        warehouse_bin_id: item.warehouse_bin_id,
        id: item.id,
        item_fc_data: [],
        remark: '',
        out_roll: 0,
        total_weight: 0,
        weight_error: 0,
        actually_weight: 0,
        settle_error_weight: 0,
        settle_weight: 0,
        out_length: 0,
      }
      return conductUnitPrice(temp, true)
    })
    finishProductionOptions.datalist = [...finishProductionOptions.datalist, ...list]
  },
  handleSureFineSize: (list: any) => {
    let total_weight = 0
    let weight_error = 0
    let out_length = 0
    let settle_error_weight = 0
    list.forEach((item: any) => {
      total_weight += Number(item.base_unit_weight) || 0
      weight_error += Number(item.weight_error) || 0
      out_length += Number(item.length) || 0
      settle_error_weight += Number(item.settle_error_weight) || 0
    })
    // const total_weight = list.reduce((pre: any, val: any) => pre + Number(val.base_unit_weight), 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + Number(val.weight_error), 0)
    // const out_length = list.reduce((pre: any, val: any) => pre + Number(val.length), 0)
    // const settle_error_weight = list.reduce((pre: any, val: any) => pre + Number(val.settle_error_weight), 0)
    const actually_weight = Number(total_weight) - Number(weight_error)
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].item_fc_data = list
    // 出仓数量 = 细码数量之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_weight = Number(total_weight.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].out_length = Number(out_length.toFixed(2))
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].out_roll = Number(sumNum(list, 'roll', '', 'float'))
    // 空差 = 细码空差之和
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].weight_error = Number(weight_error.toFixed(2))
    //   码单数量 = 出仓数量 - 码单空差
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].actually_weight = Number(actually_weight.toFixed(2))
    // 结算空差
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_error_weight = Number(settle_error_weight.toFixed(2))
    // 结算数量
    finishProductionOptions.datalist[finishProductionOptions.rowIndex].settle_weight = Number((Number(actually_weight) - settle_error_weight).toFixed(2))
    computedKeys()
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.id === id)
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit,
})
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}

function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'out_roll',
    title: '出仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'out_length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  const ids = finishProductionOptions.multipleSelection.map((item: any) => item.uuid)

  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid)) {
      if (row.field === 'out_roll' && value[row.field] > item.sum_stock_roll)
        item[row.field] = item.sum_stock_roll
      else
        item[row.field] = value[row.field]
    }
  })
  ElMessage.success('设置成功')
  handBulkClose()
  computedKeys()
}

function handBulkClose() {
  bulkShow.value = false
}

function getSFRoll(row: any) {
  return row.item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
}

function showLibDialog() {
  AccordingLibAddRef.value.state.showModal = true
  AccordingLibAddRef.value.state.filterData.customer_id = isCustomerBook(state.formInline.sale_mode) ? state.formInline.customer_id : ''
  AccordingLibAddRef.value.state.customer_name = isCustomerBook(state.formInline.sale_mode) ? state.formInline.customer_name : ''
}

function computedKeys() {
  // 自动计算退货金额
  // finishProductionOptions.datalist.forEach((item: any) => {
  //   const { unit_price, settle_weight, length_unit_price, out_length, other_price } = item
  //   item.total_price = Number(unit_price) * Number(settle_weight) + Number(length_unit_price) * Number(out_length) + Number(other_price)
  //   if (Number.isNaN(item.total_price)) {
  //     item.total_price = 0
  //   } else {
  //     item.total_price = item.total_price.toFixed(2)
  //   }
  // })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    // computedKeys()
    nextTick(() => {
      tablesRef.value.tableRef.updateFooter()
    })
  },
  { deep: true },
)
// 点击录入细码
function showFineSizeDialog(row: any, rowIndex: number) {
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.showDialog(row, { warehouse_id: state.formInline.warehouse_id })
}
const formRef = ref()
const route = useRoute()
// 提交所有数据
const { fetchData: EditFetch, data: successData, success: EditSuccess, msg: EditMsg } = updateFpmSaleOutOrder()
function submitAddAllData() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const query = {
        ...state.formInline,
        process_factory_id: state.formInline.process_factory_id || 0,
        sale_user_id: state.formInline.sale_user_id || 0,
        sale_follower_id: state.formInline.sale_follower_id || 0,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        arrange_user_id: state.formInline.arrange_user_id || 0,
        logistics_company_id: state.formInline.logistics_company_id || 0,
        driver_id: state.formInline.driver_id.join(','),
        warehouse_out_time: formatDate(state.formInline.warehouse_out_time),
        item_data: [],
      }

      // 校验成品信息
      if (!finishProductionOptions.datalist.length)
        return ElMessage.error('请添加成品信息')

      // let FSRollFlag = true
      // let FSLengthFlag = true
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        const item = finishProductionOptions.datalist[i]
        if (finishProductionOptions.datalist[i].out_roll === '')
          return ElMessage.error('出仓匹数为必填项')

        if (!item.auxiliary_unit_id)
          return ElMessage.error(`成品编号为${item.product_code}的数据,结算单位不能为空`)

        if (!Number(item.total_weight))
          return ElMessage.error(`成品编号为${item.product_code}的数据,出仓数量不能为空且不能为0`)

        if (!Number(item.out_length) && !isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量不能为空且不能为0`)

        let FSRoll = 0
        let FSLen = 0
        finishProductionOptions.datalist[i].item_fc_data.forEach((item: any) => {
          FSRoll += Number(item.roll)
          FSLen += Number(item.length)
        })
        FSRoll = Number(FSRoll.toFixed(2))
        FSLen = Number(FSLen.toFixed(2))
        if (Number(finishProductionOptions.datalist[i].out_roll)) {
          // const FSRoll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          if (FSRoll !== Number(finishProductionOptions.datalist[i].out_roll))
            return ElMessage.error('出仓匹数和录入细码匹数不匹配')
        }
        // const FSLen = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
        if (FSLen !== Number(finishProductionOptions.datalist[i].out_length))
          return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
      }
      // if (!FSRollFlag) {
      //   return ElMessage.error('出仓匹数和录入细码匹数不匹配')
      // }
      // if (!FSLengthFlag) {
      //   return ElMessage.error('细码总辅助数量与分录行的辅助数量不匹配')
      // }
      query.item_data = finishProductionOptions.datalist.map((item: any) => {
        const item_fc_data = item.item_fc_data.map((v: any) => {
          return {
            ...v,
            roll: formatTwoDecimalsMul(Number(v.roll)),
            base_unit_weight: formatWeightMul(Number(v.base_unit_weight)),
            paper_tube_weight: formatWeightMul(Number(v.paper_tube_weight)),
            weight_error: formatWeightMul(Number(v.weight_error)),
            settle_weight: formatWeightMul(Number(v.settle_weight)),
            length: formatLengthMul(Number(v.length)),
            actually_weight: formatWeightMul(Number(v.actually_weight)),
            settle_error_weight: formatWeightMul(Number(v.settle_error_weight)),
          }
        })
        return {
          ...item,
          out_roll: formatTwoDecimalsMul(Number(item.out_roll)),
          sum_stock_roll: formatTwoDecimalsMul(Number(item.sum_stock_roll)),
          sum_stock_weight: formatWeightMul(Number(item.sum_stock_weight)),
          sum_stock_length: formatLengthMul(Number(item.sum_stock_length)),
          total_weight: formatWeightMul(Number(item.total_weight)),
          weight_error: formatWeightMul(Number(item.weight_error)),
          settle_weight: formatWeightMul(Number(item.settle_weight)),
          unit_price: formatUnitPriceMul(Number(item.unit_price)),
          out_length: formatLengthMul(Number(item.out_length)),
          length_unit_price: formatUnitPriceMul(Number(item.length_unit_price)),
          other_price: formatTwoDecimalsMul(Number(item.other_price)),
          total_price: formatTwoDecimalsMul(Number(item.total_price)),
          actually_weight: formatWeightMul(Number(item.actually_weight)),
          settle_error_weight: formatWeightMul(Number(item.settle_error_weight)),
          item_fc_data,
        }
      })
      await EditFetch(
        getFilterData({
          id: Number(route.query.id),
          ...query,
        }),
      )
      if (EditSuccess.value) {
        ElMessage.success('提交成功')
        getData()
        routerList.push({
          name: 'FpSaleDeliverFromGodownOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(EditMsg.value)
      }
    }
  })
}

const { fetchData: detailFetch, data: detailData } = getFpmSaleOutOrder()
async function getData() {
  await detailFetch({
    id: route.query.id,
  })
  const driver_id = []
  detailData.value.driver_id.split(',').forEach((item: any) => {
    if (item)
      driver_id.push(Number(item))
  })
  state.formInline = {
    ...detailData.value,
    store_keeper_id: detailData.value.store_keeper_id || '',
    sale_user_id: detailData.value.sale_user_id || '',
    process_factory_id: detailData.value.process_factory_id || '',
    sale_follower_id: detailData.value.sale_follower_id || '',
    arrange_user_id: detailData.value.arrange_user_id || '',
    logistics_company_id: detailData.value.logistics_company_id || '',
    driver_id,
    warehouse_out_time: formatDate(detailData.value.warehouse_out_time),
  }
  // customerRef.value.inputLabel = detailData.value.customer_name

  finishProductionOptions.datalist = detailData.value.item_data.map((item: any) => {
    const item_fc_data = item.item_fc_data.map((v: any) => {
      return {
        ...v,
        roll: formatTwoDecimalsDiv(Number(v.roll)),
        base_unit_weight: formatWeightDiv(Number(v.base_unit_weight)),
        paper_tube_weight: formatWeightDiv(Number(v.paper_tube_weight)),
        weight_error: formatWeightDiv(Number(v.weight_error)),
        settle_weight: formatWeightDiv(Number(v.settle_weight)),
        length: formatLengthDiv(Number(v.length)),
        actually_weight: formatWeightDiv(Number(v.actually_weight)),
        settle_error_weight: formatWeightDiv(Number(v.settle_error_weight)),
      }
    })
    const temp = {
      ...item,
      out_roll: formatTwoDecimalsDiv(Number(item.out_roll)),
      sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)),
      sum_stock_weight: formatWeightDiv(Number(item.sum_stock_weight)),
      sum_stock_length: formatLengthDiv(Number(item.sum_stock_length)),
      total_weight: formatWeightDiv(Number(item.total_weight)),
      weight_error: formatWeightDiv(Number(item.weight_error)),
      settle_weight: formatWeightDiv(Number(item.settle_weight)),
      unit_price: formatUnitPriceDiv(Number(item.unit_price)),
      out_length: formatLengthDiv(Number(item.out_length)),
      length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
      other_price: formatTwoDecimalsDiv(Number(item.other_price)),
      total_price: formatTwoDecimalsDiv(Number(item.total_price)),
      actually_weight: formatWeightDiv(Number(item.actually_weight)),
      settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)),
      item_fc_data,
    }
    return conductUnitPrice(temp, true)
  })
}
onMounted(() => {
  getData()
})

function clearCustomer(item: any) {
  state.formInline.warehouse_id = item?.default_physical_warehouse || ''
}

// 加工厂带出数据
function changeFactory(row: any) {
  // 收货地址 收货电话 收货标签
  state.formInline.receive_addr = row?.address
  state.formInline.receive_phone = row?.phone
  state.formInline.receive_tag = row?.print_tag
}
// 物流公司带出物流区域
function ChangeCompany(row: any) {
  state.formInline.logistics_company_area = row?.logistics_area
}

const FineSizeSelectStockDetailRef = ref()
function showDialog(row: any) {
  FineSizeSelectStockDetailRef.value.showDialog(row, true)
}

function setSell(item: any) {
  state.formInline.sale_user_id = item?.seller_id
  state.formInline.sale_system_id = item.select_sale_system_id
}

// 处理字段不一致
function isMainUnitFormat(item: any) {
  return isMainUnit(item, 'unit_id')
}
/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.unit_id
    item.auxiliary_unit_name = item.unit_name
  }
  return item
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="!finishProductionOptions.datalist.length" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="配布单号:">
          <template #content>
            {{ state.formInline.arrange_order_no }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="客户名称:">
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.formInline.customer_id"
                :disabled="state.formInline.arrange_order_no.length ? true : false"
                is-merge
                field="name"
                :default-value="{
                  id: state.formInline.customer_id,
                  name: state.formInline.customer_name,
                  code: state.formInline.customer_code,
                }"
                show-choice-system
                @change-value="setSell"
              />
              <!--              <SelectDialog -->
              <!--                ref="customerRef" -->
              <!--                v-model="state.formInline.customer_id" -->
              <!--                :disabled="state.formInline.arrange_order_no.length ? true : false" -->
              <!--                :query="{ name: componentRemoteSearch.customer_name }" -->
              <!--                api="GetCustomerEnumList" -->
              <!--                :table-column="[ -->
              <!--                  { -->
              <!--                    field: 'name', -->
              <!--                    title: '客户名称', -->
              <!--                    defaultData: { -->
              <!--                      id: state.formInline.customer_id, -->
              <!--                      name: state.formInline.customer_name, -->
              <!--                    }, -->
              <!--                  }, -->
              <!--                ]" -->
              <!--                :column-list="[ -->
              <!--                  { -->
              <!--                    title: '客户编号', -->
              <!--                    minWidth: 100, -->
              <!--                    required: true, -->
              <!--                    colGroupHeader: true, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        field: 'code', -->
              <!--                        isEdit: true, -->
              <!--                        title: '客户编号', -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                  { -->
              <!--                    title: '客户名称', -->
              <!--                    minWidth: 100, -->
              <!--                    colGroupHeader: true, -->
              <!--                    required: true, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        isEdit: true, -->
              <!--                        field: 'name', -->
              <!--                        title: '客户名称', -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                  { -->
              <!--                    title: '电话', -->
              <!--                    colGroupHeader: true, -->
              <!--                    minWidth: 100, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        field: 'phone', -->
              <!--                        isEdit: true, -->
              <!--                        title: '电话', -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                  { -->
              <!--                    title: '销售员', -->
              <!--                    minWidth: 100, -->
              <!--                    colGroupHeader: true, -->
              <!--                    childrenList: [ -->
              <!--                      { -->
              <!--                        field: 'seller_name', -->
              <!--                        title: '销售员', -->
              <!--                        soltName: 'seller_name', -->
              <!--                        isEdit: true, -->
              <!--                        minWidth: 100, -->
              <!--                      }, -->
              <!--                    ], -->
              <!--                  }, -->
              <!--                ]" -->
              <!--                @change-input="val => (componentRemoteSearch.customer_name = val)" -->
              <!--                @change-value="setSell" -->
              <!--              /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                :disabled="state.formInline.arrange_order_no.length ? true : false"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="clearCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents
                v-model="state.formInline.warehouse_id"
                :disabled="state.formInline.arrange_order_no.length ? true : false"
                api="GetPhysicalWarehouseDropdownList"
                label-field="name"
                value-field="id"
                clearable
                :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="出仓日期:">
          <template #content>
            <el-form-item prop="warehouse_out_time">
              <el-date-picker v-model="state.formInline.warehouse_out_time" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工厂名称:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.process_factory_id"
                :query="{ biz_unit_id: state.formInline.customer_id }"
                :disabled="!state.formInline.customer_id"
                api="FactoryLogisticsEnum"
                label-field="name"
                value-field="id"
                clearable
                @change-value="changeFactory"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.formInline.receive_addr" style="width: 100%" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货电话:">
          <template #content>
            <el-form-item prop="receive_phone">
              <vxe-input v-model="state.formInline.receive_phone" style="width: 100%" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货标签:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.formInline.receive_tag" clearable style="width: 100%" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.formInline.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.sale_follower_id"
                :query="{
                  duty: EmployeeType.follower,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.store_keeper_id"
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="配布员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.arrange_user_id"
                :disabled="state.formInline.arrange_order_no.length ? true : false"
                :query="{ duty: EmployeeType.distributionCloth }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="司机名称:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.formInline.driver_id" :query="{ duty: EmployeeType.driver }" multiple api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流公司:">
          <template #content>
            <el-form-item>
              <SelectComponents v-model="state.formInline.logistics_company_id" api="GetInfoSaleLogisticsCompanyEnumList" label-field="name" value-field="id" clearable @change-value="ChangeCompany" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流区域:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.formInline.logistics_company_area" disabled style="width: 100%" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:" :copies="2">
          <template #content>
            <el-form-item>
              <SelectSaleMode v-model="state.formInline.sale_mode" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="内部备注:" copies="2">
          <template #content>
            <el-form-item>
              <vxe-textarea v-model="state.formInline.internal_remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售备注:" copies="2">
          <template #content>
            <el-form-item>
              <vxe-textarea v-model="state.formInline.sale_remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]" :tool-bar="true">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button type="primary" @click="showLibDialog">
        根据库存添加
      </el-button>
    </template>
    <Table
      v-if="finishProductionOptions.datalist.length"
      ref="tablesRef"
      :config="finishProductionOptions.tableConfig"
      :table-list="finishProductionOptions.datalist"
      :column-list="finishProductionOptions.columnList"
    >
      <!-- 出仓匹数 -->
      <template #out_roll="{ row }">
        <vxe-input v-model="row.out_roll" :disabled="state.formInline.arrange_order_no.length" :max="row.sum_stock_roll" type="float" />
      </template>
      <!-- 出仓辅助数量 -->
      <template #out_length="{ row }">
        <vxe-input v-model="row.out_length" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 其他金额 -->
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 结算金额 -->
      <template #total_price="{ row }">
        ￥{{ row.total_price }}
      </template>
      <!-- 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" />
      </template>
      <!-- 单价 -->
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 辅助数量单价 -->
      <template #length_unit_price="{ row }">
        <vxe-input v-model="row.length_unit_price" :disabled="state.formInline.arrange_order_no.length" type="float" @change="computedKeys" />
      </template>
      <!-- 备注 -->
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" :disabled="state.formInline.arrange_order_no.length" max-length="200" type="text" />
      </template>
      <!-- 细码 -->
      <template #xima="{ row, rowIndex }">
        <div v-if="state.formInline.arrange_order_no.length">
          <el-link style="color: #409eff" @click="showDialog(row)">
            查看
          </el-link>
        </div>
        <div v-else>
          <!-- 进仓数量 -->
          <el-button v-if="row.out_roll !== ''" type="text" @click="showFineSizeDialog(row, rowIndex)">
            录入
            <span v-if="isFinishEnter(row)" style="color: #ccc">(已录入)</span>
            <span v-else style="color: red">({{ row.out_roll - getSFRoll(row) }}条未录)</span>
          </el-button>
          <span v-else style="color: #ccc">请先输入出仓匹数</span>
        </div>
      </template>
      <!-- 操作 -->
      <template #operate="{ row }">
        <el-button v-if="!state.formInline.arrange_order_no.length" type="text" @click="finishProductionOptions.handleRowDel(row)">
          删除
        </el-button>
      </template>
    </Table>
    <div v-else class="no_data" style="color: #999">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <AccordingLibAdd ref="AccordingLibAddRef" :warehouse_id="state.formInline.warehouse_id" @handle-sure="finishProductionOptions.handleSure" />
  <FineSizeAdd ref="FineSizeAddRef" @handle-sure="finishProductionOptions.handleSureFineSize" />
  <AssociatedPurchasingSalesReturn />
  <FineSizeSelectStockDetail ref="FineSizeSelectStockDetailRef" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
