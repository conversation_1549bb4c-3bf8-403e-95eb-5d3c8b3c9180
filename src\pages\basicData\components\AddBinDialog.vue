<script setup lang="ts">
import { reactive, ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'

const emits = defineEmits(['handleSure'])

const state = reactive({
  showModal: false,
  modalName: '',
  form: {
    code: '', // 仓库编号
    name: '', // 仓库名称
    remark: '',
  },
  fromRules: {
    code: [{ required: true, message: '请选择仓库编号', trigger: 'blur' }],
    name: [{ required: true, message: '请选择仓库名称', trigger: 'blur' }],
    // type: [{ required: true, message: '请选择仓库类型', trigger: 'blur' }],
  },
  tableConfig: {
    showOperate: true,
    operateWidth: '100',
    height: 400,
  },
  list: [],
})

const columnList = ref([
  {
    field: '',
    title: '仓位编号',
    soltName: 'code',
  },
  {
    field: '',
    title: '仓位名称',
    soltName: 'name',
  },
  {
    field: '',
    title: '仓位区域',
    soltName: 'area',
  },
])

function handCancel() {
  state.showModal = false
}

const ruleFormRef = ref()
async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: any) => {
    if (valid)
      emits('handleSure', state)
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1000" height="750" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" size="default" :model="state.form" :rules="state.fromRules">
      <el-descriptions title="" :column="2" border size="small">
        <el-descriptions-item label="">
          <template #label>
            <div class="cell-item" style="display: flex">
              仓库编号
              <div style="color: red">
                *
              </div>
            </div>
          </template>
          <el-form-item prop="name">
            <!-- <SelectComponents></SelectComponents> -->
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="">
          <template #label>
            <div class="cell-item" style="display: flex">
              仓库名称
              <div style="color: red">
                *
              </div>
            </div>
          </template>
          <el-form-item prop="type">
            <!-- <SelectComponents></SelectComponents> -->
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
      <div class="mt-[20px] mb-[20px]">
        <el-button type="primary" :icon="Plus" @click="handAdd">
          添加
        </el-button>
      </div>
      <Table :config="state.tableConfig" :table-list="state?.list" :column-list="columnList">
        <!--        <template #code="{ row }"> -->
        <!--          &lt;!&ndash; <el-input clearable v-model="row?.aa" placeholder="请输入"></el-input> &ndash;&gt; -->
        <!--        </template> -->
        <!--        <template #name="{ row }"> -->
        <!--          &lt;!&ndash; <el-input clearable v-model="row?.aa" placeholder="请输入"></el-input> &ndash;&gt; -->
        <!--        </template> -->
        <!--        <template #area="{ row }"> -->
        <!--          &lt;!&ndash; <el-input clearable v-model="row?.aa" placeholder="请输入"></el-input> &ndash;&gt; -->
        <!--        </template> -->
        <template #operate="{ row }">
          <el-button type="primary" text link @click="handCopy(row)">
            复制
          </el-button>
          <el-button type="primary" text link @click="handDelete(row)">
            删除
          </el-button>
        </template>
      </Table>
      <el-descriptions title="" :column="2" border size="small" class="mt-[20px]">
        <el-descriptions-item label="备注">
          <el-input v-model="state.form.remark" clearable />
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}

::v-deep(.el-form-item--default) {
  margin-bottom: 0 !important;
}
</style>
