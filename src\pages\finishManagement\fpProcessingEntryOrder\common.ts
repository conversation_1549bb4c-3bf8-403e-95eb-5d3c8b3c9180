import currency from 'currency.js'
import { DyeingTypeEnum } from '@/enum/grayFabricEnum'

// 根据成品信息自动分配返布用坯数据 - 暂时废弃旧的自动录入逻辑 useAutoEnter
export function autoEnterData(list: any) {
  list?.forEach((item: any) => {
    // 当前成品输入的数据
    const input_roll = Number(item.use_gf_roll)
    const input_weight = Number(item.use_gf_weight)

    const isGrayFabricDyeing = item.dnf_type === DyeingTypeEnum.GrayFabricDyeing // 坯布

    // 先按照source_time升序排序，source_time一致的再使用出坯单id进行排序
    item.item_bum_data.sort((a: any, b: any) => {
      // Compare source_time
      let a_source_time = 0
      let b_source_time = 0

      let a_id = 0
      let b_id = 0

      if (isGrayFabricDyeing) {
        a_source_time = a.source_time || a.gf_stock_info?.source_time
        b_source_time = b.source_time || b.gf_stock_info?.source_time

        a_id = a.source_id || a.gf_stock_info.gf_stock_id
        b_id = b.source_id || b.gf_stock_info.gf_stock_id
      }
      else {
        a_source_time = a.date || a.f_out_info.date
        b_source_time = b.date || b.f_out_info.date

        a_id = a.order_id || a.f_out_info.order_id
        b_id = b.order_id || b.f_out_info.order_id
      }

      if (a_source_time !== b_source_time)
        return (+new Date(a_source_time)) - (+new Date(b_source_time))

      // Compare id
      return a_id - b_id
    })

    // 根据输入的数据自动分配用坯匹数和用坯数量，按顺序先把前面的先录完
    let residue_roll = input_roll // 当前剩余可录入的匹数
    let residue_weight = input_weight // 当前剩余可录入的数量
    item.item_bum_data.forEach((item: any) => {
      // 这里计算可分配数，存在item.use_roll_max则是在返布用坯已经进行计算（编辑页数据），没有则进行计算（新增的数据）
      const use_roll_max = item.use_roll_max || item.not_rtn_piece_count// 最大用坯匹数 未返匹数
      const use_weight_max = item.use_weight_max || item.not_rtn_weight// 最大用坯数量 未返数量

      item.use_roll = residue_roll > use_roll_max ? use_roll_max : residue_roll
      item.use_weight = residue_weight > use_weight_max ? use_weight_max : residue_weight
      // 减去当前已分配的匹数和数量
      residue_roll = currency(residue_roll).subtract(item.use_roll).value
      residue_roll = residue_roll < 0 ? 0 : residue_roll
      residue_weight = currency(residue_weight).subtract(item.use_weight).value
      residue_weight = residue_weight < 0 ? 0 : residue_weight
    })
  })
  return list
}
