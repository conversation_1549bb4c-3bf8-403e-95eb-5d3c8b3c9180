<script setup lang="ts" name="SystemSettings">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import FildCard from '@/components/FildCard.vue'
import { getGlobalConfigList, updateGlobalConfig } from '@/api/globalConfig'

// 配置列表
const configList = ref<Api.GlobalConfigList.Response[]>([])
// 配置值对象
const configValues = reactive<Record<string, any>>({})
// 加载状态
const loading = ref(false)

// API调用相关
const {
  fetchData: fetchList,
  data: listData,
  success: listSuccess,
  msg: listMsg,
} = getGlobalConfigList()

const {
  fetchData: fetchUpdate,
  success: updateSuccess,
  msg: updateMsg,
} = updateGlobalConfig()

// 获取配置列表
async function getConfigList() {
  loading.value = true
  try {
    await fetchList({
      page: 1,
      size: 100, // 获取所有配置项
    })

    if (listSuccess.value && listData.value?.list) {
      configList.value = listData.value.list
      // 初始化配置值
      initConfigValues()
    }
    else {
      ElMessage.error(listMsg.value || '获取配置失败')
    }
  }
  catch (error) {
    ElMessage.error('获取配置失败')
  }
  finally {
    loading.value = false
  }
}

// 初始化配置值
function initConfigValues() {
  configList.value.forEach((config) => {
    if (config.key) {
      // 根据配置类型初始化值
      if (config.type === 3) {
        // 多选类型，初始化为数组
        // 当前值从options字段获取，如果是逗号分隔的字符串则分割为数组
        try {
          configValues[config.key] = config.options
            ? config.options.split(',').map(item => item.trim())
            : []
        }
        catch {
          configValues[config.key] = []
        }
      }
      else if (config.type === 4) {
        // 开关类型，初始化为布尔值
        // 当前值从options字段获取
        configValues[config.key] = config.options === 'true'
      }
      else {
        // 文本和单选类型
        // 当前值从options字段获取
        configValues[config.key] = config.options || ''
      }
    }
  })
}

// 解析配置选项
function getConfigOptions(optionsStr?: string) {
  if (!optionsStr)
    return []

  // 首先尝试解析JSON格式
  try {
    const options = JSON.parse(optionsStr)
    if (Array.isArray(options))
      return options
  }
  catch {
    // JSON解析失败，尝试解析逗号分隔格式
  }

  // 处理逗号分隔的字符串格式，如 "同步,不同步"
  const items = optionsStr.split(',').map(item => item.trim()).filter(item => item)
  return items.map(item => ({
    label: item,
    value: item,
  }))
}

// 处理配置变更
async function handleConfigChange(config: Api.GlobalConfigList.Response) {
  if (!config.key)
    return

  const value = configValues[config.key]
  let newOptions = ''

  // 根据配置类型处理值
  if (config.type === 3) {
    // 多选类型，转换为逗号分隔的字符串
    newOptions = Array.isArray(value) ? value.join(',') : ''
  }
  else if (config.type === 4) {
    // 开关类型，转换为字符串
    newOptions = value ? 'true' : 'false'
  }
  else {
    // 文本和单选类型
    newOptions = String(value || '')
  }

  try {
    await fetchUpdate({
      id: config.id,
      key: config.key,
      description: config.description,
      options: newOptions, // 更新当前值到options字段
      options_presets: config.options_presets, // 保持预设值不变
      remark: config.remark,
      type: config.type,
    })

    if (updateSuccess.value) {
      ElMessage.success('配置更新成功')
    }
    else {
      ElMessage.error(updateMsg.value || '配置更新失败')
      // 恢复原值
      initConfigValues()
    }
  }
  catch (error) {
    ElMessage.error('配置更新失败')
    // 恢复原值
    initConfigValues()
  }
}

// 页面初始化
onMounted(() => {
  getConfigList()
})
</script>

<template>
  <div class="system-settings">
    <FildCard title="系统配置" :tool-bar="false">
      <el-form v-if="!loading" :model="configValues" label-width="150px" class="config-form">
        <div v-for="config in configList" :key="config.id" class="config-item">
          <!-- 文本类型配置 -->
          <el-form-item v-if="config.type === 1">
            <template #label>
              <span>{{ config.key }}</span>
              <el-tooltip :content="config.description" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input
              v-model="configValues[config.key!]"
              :placeholder="`请输入${config.description}`"
              @blur="handleConfigChange(config)"
            />
            <div v-if="config.remark" class="config-remark">
              {{ config.remark }}
            </div>
          </el-form-item>

          <!-- 单选类型配置 -->
          <el-form-item v-if="config.type === 2">
            <template #label>
              <span>{{ config.key }}</span>
              <el-tooltip :content="config.description" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-radio-group
              v-model="configValues[config.key!]"
              @change="handleConfigChange(config)"
            >
              <el-radio
                v-for="option in getConfigOptions(config.options_presets)"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            <div v-if="config.remark" class="config-remark">
              {{ config.remark }}
            </div>
          </el-form-item>

          <!-- 多选类型配置 -->
          <el-form-item v-if="config.type === 3">
            <template #label>
              <span>{{ config.key }}</span>
              <el-tooltip :content="config.description" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-checkbox-group
              v-model="configValues[config.key!]"
              @change="handleConfigChange(config)"
            >
              <el-checkbox
                v-for="option in getConfigOptions(config.options_presets)"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
            <div v-if="config.remark" class="config-remark">
              {{ config.remark }}
            </div>
          </el-form-item>

          <!-- 开关类型配置 -->
          <el-form-item v-if="config.type === 4">
            <template #label>
              <span>{{ config.key }}</span>
              <el-tooltip :content="config.description" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-switch
              v-model="configValues[config.key!]"
              active-text="开启"
              inactive-text="关闭"
              @change="handleConfigChange(config)"
            />
            <div v-if="config.remark" class="config-remark">
              {{ config.remark }}
            </div>
          </el-form-item>
        </div>
      </el-form>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <el-empty v-if="!loading && configList.length === 0" description="暂无配置项" />
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.system-settings {
  .config-form {
    .config-item {
      margin-bottom: 24px;

      .config-remark {
        margin-top: 8px;
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
      }
    }
  }

  .loading-container {
    padding: 20px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #303133;
  }

  :deep(.el-radio-group) {
    .el-radio {
      margin-right: 20px;
    }
  }

  :deep(.el-checkbox-group) {
    .el-checkbox {
      margin-right: 20px;
      margin-bottom: 8px;
    }
  }

  :deep(.el-switch) {
    .el-switch__label {
      font-size: 14px;
    }
  }

  .config-label {
    cursor: help;
    border-bottom: 1px dashed #409eff;
    color: #409eff;

    &:hover {
      color: #337ecc;
      border-bottom-color: #337ecc;
    }
  }
}
</style>
