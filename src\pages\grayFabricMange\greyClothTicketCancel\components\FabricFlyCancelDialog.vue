<script setup lang="ts">
import { computed, ref } from 'vue'
import FabricFlyCancelContent from './FabricFlyCancelContent.vue'

// Props 定义
interface Props {
  modelValue: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '布飞取消',
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [data: any]
}>()

// 对话框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 内容组件引用
const contentRef = ref()

// 处理成功事件
function handleSuccess(data: { barcode: string }) {
  emit('success', data)
  visible.value = false
}

// 处理取消事件
function handleCancel() {
  visible.value = false
}

// 代理保存方法
function handleSave() {
  contentRef.value?.handleSave()
}

// 代理清空方法
function handleClear() {
  contentRef.value?.handleClear()
}
</script>

<template>
  <vxe-modal
    v-model="visible"
    :title="title"
    width="800px"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    destroy-on-close
    show-footer
  >
    <FabricFlyCancelContent
      ref="contentRef"
      mode="dialog"
      :show-header="false"
      :show-footer="false"
      @success="handleSuccess"
      @cancel="handleCancel"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSave"
        >
          保存 (F12)
        </el-button>
        <el-button @click="handleClear">
          清空 (F1)
        </el-button>
      </div>
    </template>
  </vxe-modal>
</template>

<style scoped>
/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
