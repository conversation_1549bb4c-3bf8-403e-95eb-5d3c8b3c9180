<script setup lang="ts" name="FinishedProductPurchaseReturns">
import { Delete, Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  GetPurchaseProductReturnOrder,
  GetPurchaseProductReturnOrderList,
  UpdatePurchaseProductReturnOrderStatusPass,
  UpdatePurchaseProductReturnOrderStatusWait,
} from '@/api/finishedProductPurchaseReturns'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import {
  debounce,
  getFilterData,
  orderStatusConfirmBox,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'

const state = reactive({
  filterData: {
    order_no: '',
    supplier_id: '',
    warehouse_return_id: '',
    audit_status: '',
    begin_time: '',
    end_time: '',
  },
  multipleSelection: [],
  tableData: [] as any[],
  tableUserData: [] as any[],
})
const componentRemoteSearch = reactive({
  supplieName: '',
})

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

const purchase_time = ref()
watch(
  () => purchase_time.value,
  (value: any) => {
    state.filterData.begin_time = formatDate(value?.[0]) || ''
    state.filterData.end_time = formatDate(value?.[1]) || ''
  },
)

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '9%',
    soltName: 'order_no',
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    width: 120,
  },
  {
    sortable: true,
    field: 'warehouse_return_name',
    title: '退货仓库',
    width: 120,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    width: 120,
  },
  {
    sortable: true,
    field: 'sale_mode_name',
    title: '订单类型',
    width: 100,
  },
  {
    sortable: true,
    field: 'return_time',
    title: '退货日期',
    width: 100,
    isDate: true,
    formatTime: 'YYYY-MM-DD',
  },
  {
    sortable: true,
    field: 'total_price',
    title: '单据金额',
    isPrice: true,
    width: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 120,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 120,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 120,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '订单状态',
    showOrder_status: true,
    fixed: 'right',
    soltName: 'audit_status',
    width: '5%',
  },
])

const userColumnList = ref([
  {
    sortable: true,
    field: 'product_code',
    title: '成品编号',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'product_name',
    title: '成品名称',
    fixed: 'left',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_purchase_order_no',
    title: '采购单号',
    fixed: 'left',
    width: 130,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_color_code',
    title: '色号',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_color_name',
    title: '颜色名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_craft', // TODO 后端没返回
    title: '成品工艺',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_level_name',
    title: '成品等级',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_ingredient',
    title: '成品成分',
    width: 100,
  },
  {
    sortable: true,
    field: 'product_remark',
    title: '成品备注',
    width: 100,
  },
  {
    sortable: true,
    field: 'return_roll',
    title: '退货数',
    width: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'avg_weight',
    title: '均重',
    width: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'return_weight', // TODO 后端没返回
    title: '退货数量',
    width: '5%',
    isWeight: true,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    width: '5%',
  },
  {
    field: 'auxiliary_unit_name',
    title: '结算单位',
    width: '5%',
  },
  {
    sortable: true,
    field: 'weight_unit_price',
    title: '数量单价',
    width: '5%',
    isUnitPrice: true,
  },
  {
    sortable: true,
    field: 'return_length',
    title: '辅助数量',
    width: '5%',
    isLength: true,
  },
  {
    sortable: true,
    field: 'length_unit_price',
    title: '辅助数量单价',
    width: '5%',
    isUnitPrice: true,
  },
  {
    sortable: true,
    field: 'return_price',
    title: '退货金额',
    width: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'out_roll',
    title: '出仓匹数',
    width: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'out_weight',
    title: '出仓数量',
    width: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'out_length',
    title: '出仓辅助数量',
    width: '5%',
    isLength: true,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    width: 100,
  },
])

onMounted(() => {
  getData()
})
const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = GetPurchaseProductReturnOrderList()
async function getData() {
  const audit_status = (
    (state.filterData.audit_status as unknown as []) || []
  ).join(',')
  await fetchDataList(getFilterData({ ...state.filterData, audit_status }))
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}

const selectRow = ref<any[]>([])
function handAllSelect({ records }: any) {
  selectRow.value = records
}

function handleSelectionChange({ records }: any) {
  selectRow.value = records
}
const router = useRouter()

function handAdd() {
  router.push({ name: 'FinishedProductPurchaseReturnsAdd' })
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handEdit(row: any) {
  router.push({
    name: 'FinishedProductPurchaseReturnsEdit',
    params: { id: row?.id },
  })
}

const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = GetPurchaseProductReturnOrder()
const userShow = ref(false)
async function showDetail(row: any) {
  await fetchDataDetail({ id: row?.id })
  state.tableUserData = row.item_data
  userShow.value = true
}

function handDetail(row: any) {
  router.push({
    name: 'FinishedProductPurchaseReturnsDetail',
    params: { id: row?.id },
  })
}

async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({
    id: id.toString(),
    message: msg,
    api: UpdatePurchaseProductReturnOrderStatusPass,
  })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({
    id: id.toString(),
    message: msg,
    api: UpdatePurchaseProductReturnOrderStatusWait,
  })
  getData()
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['product_code'].includes(column.field))
        return '合计'

      if (['return_roll'].includes(column.field))
        return formatPriceDiv(sumNum(data, column.field))

      if (['return_weight'].includes(column.field))
        return formatWeightDiv(sumNum(data, column.field))

      if (['return_price'].includes(column.field))
        return `¥${formatPriceDiv(sumNum(data, column.field))}`
    }),
  ]
  return footerData
}

const tableConfig = ref({
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '9%',
  showSort: false,
  show_footer: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
  fieldApiKey: 'FinishedProductPurchaseReturns_A',
})

const userTableConfig = ref({
  loading: loadingDetail,
  showSlotNums: true,
  footerMethod,
  height: '100%',
  fieldApiKey: 'FinishedProductPurchaseReturns_B',
})

onActivated(() => {
  getData()
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="state.filterData.order_no"
              placeholder="单据编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.supplier_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct, name: componentRemoteSearch.supplieName }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '供应商名称',
                  minWidth: 100,
                  isEdit: true,
                },
                {
                  field: 'code',
                  title: '供应商编号',
                  minWidth: 100,
                  isEdit: true,
                },
                {
                  field: 'address',
                  title: '地址',
                  minWidth: 100,
                  isEdit: true,
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称');
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @change-input="val => (componentRemoteSearch.supplieName = val)"
            />
            <!-- <SelectComponents style="width: 200px" api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="退货仓库:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.warehouse_return_id"
              :query="{
                warehouse_type_id: WarehouseTypeIdEnum.finishProduction,
              }"
              style="width: 200px"
              api="GetPhysicalWarehouseDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="退货日期:" width="330">
          <template #content>
            <SelectDate v-model="purchase_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              :multiple="true"
              style="width: 200px"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="'FinishedProductPurchaseReturnsAdd'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel title="导出文件" />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>

        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FinishedProductPurchaseReturnsDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FinishedProductPurchaseReturnsEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FinishedProductPurchaseReturns_Pass'"
              :underline="false"
              type="primary"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FinishedProductPurchaseReturns_Wait'"
              :underline="false"
              type="primary"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="userTableConfig"
        :table-list="dataDetail.item_data"
        :column-list="userColumnList"
      />
    </FildCard>
  </div>
</template>

<style lang="scss">
.yuan {
  width: 10px;
  height: 10px;
  background: #51c41b;
  border-radius: 50%;
  margin-right: 10px;
}

.yuan_red {
  width: 10px;
  height: 10px;
  background: #f5232d;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
