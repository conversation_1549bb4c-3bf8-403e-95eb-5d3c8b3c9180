import { useRequest } from '@/use/useRequest'

// 获取列表
export const getProductionChangeList = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/getProductionChangeOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取坯布信息
export const byIdGreyInfoProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/getListGreyFabricItems',
    method: 'get',
  })
}

// 获取用料比例
export const byIdMaterialProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/getListMaterialRatioItems',
    method: 'get',
  })
}

// 获取工艺要求
export const byIdTechnologicalRequirementProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/getListTechnologicalRequirementItems',
    method: 'get',
  })
}

// 审核
export const checkSheetOfProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/updateProductionChangeOrderStatusAudit',
    method: 'put',
  })
}
// 消审
export const cancelApprovedSheetOfProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/updateProductionChangeOrderStatusWait',
    method: 'put',
  })
}
// 作废
export const deleteCancelSheetOfProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/updateProductionChangeOrderStatusCancel',
    method: 'put',
  })
}
// 驳回
export const rejectSheetOfProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/updateProductionChangeOrderStatusReject',
    method: 'put',
  })
}

// 添加变更单
export const AddProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/addProductionChangeOrder',
    method: 'post',
  })
}

// 编辑变更单
export const EditProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/updateProductionChangeOrder',
    method: 'put',
  })
}

// 获取变更单
export const getProductionChange = () => {
  return useRequest({
    url: '/admin/v1/produce/productionChangeOrder/getProductionChangeOrder',
    method: 'get',
  })
}
