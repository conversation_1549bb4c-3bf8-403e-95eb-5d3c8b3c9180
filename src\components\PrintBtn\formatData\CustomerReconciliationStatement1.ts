/*
 * @LastEditTime: 2024-11-18 17:58:46
 * @Description:客户对账表处理
 */
import currency from 'currency.js'
import { formatHashTag, formatPriceSymbol, mergeWeightUnit, sumNum } from '@/common/format'
import { processDataOut } from '@/common/handBinary'

export default (data: any) => {
  data = processDataOut(data)
  const table: any[] = (data?.list || []).map((item: any) => {
    const amount = currency(item.weight).multiply(item.sale_price).value // 金额：结算数量*单价
    const otherReceivable = currency(item.remove_money).add(item.discount_price).add(item.chargeback_money).value // 其他收款：优惠+折扣+扣款
    return {
      ...item,
      product_code_name: formatHashTag(item.code, item.name), // 品名：编号#颜色
      product_color_code_name: formatHashTag(item?.product_color_code, item?.product_color_name), // 颜色：编号#颜色
      amount,
      amount_format: formatPriceSymbol({ value: amount, prefix: '￥' }),
      otherReceivable,
      otherReceivable_format: formatPriceSymbol({ value: otherReceivable, prefix: '￥' }), // 其他收款
      other_price_format: formatPriceSymbol({ value: item.other_price, prefix: '￥' }), // 其他应收
      actually_collect_price_format: formatPriceSymbol({ value: item.actually_collect_price, prefix: '￥' }), // 实收款
      balance_format: formatPriceSymbol({ value: item.balance, prefix: '￥', showZero: true }), // 结余
      settle_price_format: formatPriceSymbol({ value: item.settle_price, prefix: '￥' }), // 应收金额
      sale_price_format: formatPriceSymbol({ value: item.sale_price, suffix: '元' }), // 单价
      weight_format: formatPriceSymbol({ value: item.weight, suffix: item.measurement_unit_name === '-' ? '' : item.measurement_unit_name }), // 结算数量
    }
  })

  const printData = {
    ...data,
    dateRange: data?.summary?.start_time ? `${data?.summary.start_time}至${data?.summary.end_time}` : '-', // 日期处理
    should_collect_money_format: formatPriceSymbol({ value: data.summary?.should_collect_money || 0, showZero: true }), // 应收金额
    collected_money_format: formatPriceSymbol({ value: data.summary?.collected_money || 0, showZero: true }), // 已收金额
    end_period_format: formatPriceSymbol({ value: data.summary?.end_period || 0, showZero: true }), // 累欠金额/本期结余 - 新
    last_balance_price_format: formatPriceSymbol({ value: data.summary?.last_balance_price || 0, showZero: true }), // 前欠金额/上期结余
    table,
    // 汇总当前页-
    countPageTotal: sumNum,
    // 汇总当前页-前缀追加￥
    countPageTotalFormat: (list: any, key: string) => formatPriceSymbol({ value: sumNum(list, key), prefix: '￥' }),
    // 合并数量
    mergeWeightUnit,
  }
  return printData
}
