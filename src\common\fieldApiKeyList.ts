export const fieldApiKeyList = {
  MeetMangeActualPayAdd: 'MEETMANGE_ACTUAL_PAY_ADD',
  MeetMangeActualPayAddDataDetail: 'MEETMANGE_ACTUAL_PAY_ADD_DATA_DETAIL',
  MentMangeActualPayDetail: 'MENT_MANGE_ACTUAL_PAY_DETAIL',
  MentMangeActualPayDetailDataDetail: 'MENT_MANGE_ACTUAL_PAY_DETAIL',
  MentMangeActualPayEdit: 'MENT_MANGE_ACTUAL_PAY_EDIT',
  MentMangeActualPayEditDetail: 'MENT_MANGE_ACTUAL_PAY_EDIT_DETAIL',
  MeetMangeActualPayList: 'MEET_MANGE_ACTUAL_PAY_LIST',
  BusinessAnalysisList: 'BUSINESS_ANALYSIS_LIST',
  DyeingPayDetail: 'DYEING_PAY_DETAIL',
  DyeingPayEdit: 'DYEING_PAY_EDIT',
  DyeingPayOrderWriteOffList: 'DYEING_PAY_ORDER_WRITE_OFF_LIST',
  DyeingPayList: 'DYEING_PAY_LIST',
  MeetMangeFinishPayDetail: 'MEET_MANGE_FINISH_PAY_DETAIL',
  MeetMangeFinishPayDetailRecord: 'MEET_MANGE_FINISH_PAY_DETAIL_RECORD',
  MeetMangeFinishPayEdit: 'MEET_MANGE_FINISH_PAY_EDIT',
  FinishPayList: 'FINISH_PAY_LIST',
  MeetMangeGreyclothPayDetail: 'MEET_MANGE_GREYCLOTH_PAY_DETAIL',
  MeetMangeGreyclothPayDetailRecord: 'MEET_MANGE_GREYCLOTH_PAY_DETAIL_RECORD',
  MeetMangeGreyclothPayEdit: 'MEET_MANGE_GREYCLOTH_PAY_EDIT',
  GreyclothPayList: 'GREYCLOTH_PAY_LIST',
  GrossProfitAnalysisOfProductSales: 'GROSS_PROFIT_ANALYSIS_OF_PRODUCT_SALES',
  GrossProfitAnalysisOfProductSalesList:
    'GROSS_PROFIT_ANALYSIS_OF_PRODUCT_SALES_LIST',
  SalesGrossProfitTable: 'SALES_GROSS_PROFIT_TABLE',
  ImprestPayAdd: 'IMPREST_PAY_ADD',
  ImprestPayEdit: 'IMPREST_PAY_EDIT',
  ImprestPayList: 'IMPREST_PAY_LIST',
  MeetMangeImprestPayDetail: 'MEET_MANGE_IMPREST_PAY_DETAIL',
  MeetMangeImprestPayUseRecord: 'MEET_MANGE_IMPREST_PAY_USE_RECORD',
  MeetMangeOtherPayAdd: 'MEET_MANGE_OTHER_PAY_ADD',
  OtherPayDetail: 'OTHER_PAY_DETAIL',
  OtherPayDetailRecord: 'OTHER_PAY_DETAIL_RECORD',
  OtherPayEdit: 'OTHER_PAY_EDIT',
  OtherPayList: 'OTHER_PAY_LIST',
  MeetMangeProcessPayDetail: 'MEET_MANGE_PROCESS_PAY_DETAIL',
  MeetMangeProcessPayDetailRecord: 'MEET_MANGE_PROCESS_PAY_DETAIL_RECORD',
  ProcessPayEdit: 'PROCESS_PAY_EDIT',
  ProcessPayList: 'PROCESS_PAY_LIST',
  MentMangeRawPayEdit: 'MENT_MANGE_RAW_PAY_EDIT',
  RawPayList: 'RAW_PAY_LIST',
  MeetMangeSupplierBalanceSheet: 'MEET_MANGE_SUPPLIER_BALANCE_SHEET',
  MeetMangeSupplierStatement: 'MEET_MANGE_SUPPLIER_STATEMENT',
  KindDyeingInformationList: 'KIND_DYEING_INFORMATION_LIST',
  KindDyeingInformationListTable2: 'KIND_DYEING_INFORMATION_LIST_TABLE2',
  WarehouseInfoDetail: 'WAREHOUSE_INFO_DETAIL',
  RawInfoDetail: 'RAW_INFO_DETAIL',
  AccordingSubscribeStockAdd: 'ACCORDING_SUBSCRIBE_STOCK_ADD',
  FpSubscribeWarehouseOrderAdd: 'FP_SUBSCRIBE_WAREHOUSE_ORDER_ADD',
  AddDistributionDialog: 'AddDistributionDialog',
  AccordingdyePlanAdd: 'AccordingdyePlanAdd',
  AccordingProcessingAdd: 'AccordingProcessingAdd',
  FpProcessingReturnEntryOrder: 'FpProcessingReturnEntryOrder',
  FpProcessingReturnEntryOrderEdit: 'FpProcessingReturnEntryOrderEdit',
  FineSizeProcessingOrderAdd: 'FineSizeProcessingOrderAdd',
  SelectInformationDialog: 'SelectInformationDialog',
  GreyPushDownListRecord: 'GreyPushDownListRecord',
  DyeingNoticeDetailProductInfo: 'DyeingNoticeDetailProductInfo',
  DyeingNoticeDetailProductOther: 'DyeingNoticeDetailProductOther',
  FinishPurchaseWarehouseEntryAdd: 'FinishPurchaseWarehouseEntryAdd',
  QualityCheckManagementStockDialog: 'QualityCheckManagementStockDialog',
}
