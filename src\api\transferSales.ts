import { useRequest } from '@/use/useRequest'

// 获取列表
export function getShortageProductOrderList() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/getShortageProductOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新增
export function addSaleTransferOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/addSaleTransferOrder',
    method: 'post',
  })
}

// 编辑
export function saleTransferOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder',
    method: 'put',
  })
}

// 编辑退货
export function UpdateSaleTransferOrderReturn() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/return',
    method: 'put',
  })
}

// 详情
export function getSaleTransferOrder() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/getSaleTransferOrder',
    method: 'get',
  })
}

// 审核
export function saleTransferOrderauditPass() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/auditPass',
    method: 'put',
  })
}

// 消审
export function saleTransferOrderauditWait() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/auditWait',
    method: 'put',
  })
}

// 作废
export function saleTransferOrderauditCancel() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/auditCancel',
    method: 'put',
  })
}

// 驳回
export function saleTransferOrderauditReject() {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/auditReject',
    method: 'put',
  })
}

export function getSaleSimpleReportList() {
  return useRequest({
    url: '/admin/v1/should_collect_order/report_forms/getSaleSimpleReportList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// AI报表分析
export function GetAIReportAnalysis() {
  return useRequest({
    url: '/admin/v1/ai/analysis/sale',
    method: 'get',
  })
}
