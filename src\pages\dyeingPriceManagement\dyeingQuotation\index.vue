<script setup lang="ts">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { dyeingcancel, dyeinglist, dyeinglistExport, dyeingpass } from '@/api/dyeingQuotation'
import { formatDate } from '@/common/format'
import { debounce, deepClone, deleteToast, getFilterData, resetData } from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { BusinessUnitIdEnum } from '@/common/enum'

const state = reactive<any>({
  filterData: {
    order_no: '',
    dye_factory_id: '',
    sale_system_id: '',
    status: '',
    quote_order_no: '',
    effect_time: '',
    deadline_time: '',
    creator_id: '',
    create_time: '',
    auditor_id: '',
    audit_time: '',
    updater_id: '',
    edit_time: '',
    quote_type: '',
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = dyeinglist()

const options = ref([
  {
    label: '新报价',
    value: 1,
  },
  {
    label: '调价',
    value: 2,
  },
])

const tableConfig = ref({
  fieldApiKey: 'DyeingQuotationIndex',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '9%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

// 获取数据
const getData = debounce(() => {
  const obj = deepClone(state.filterData)
  if (state.filterData.status.length)
    obj.status = obj.status.join(',')

  const query: any = {
    effect_start_time: state.filterData.effect_time && state.filterData.effect_time !== '' && state.filterData.effect_time.length ? formatDate(state.filterData.effect_time[0]) : '',
    effect_end_time: state.filterData.effect_time && state.filterData.effect_time !== '' && state.filterData.effect_time.length ? formatDate(state.filterData.effect_time[1]) : '',
    deadline_start_time: state.filterData.deadline_time && state.filterData.deadline_time !== '' && state.filterData.deadline_time.length ? formatDate(state.filterData.deadline_time[0]) : '',
    deadline_end_time: state.filterData.deadline_time && state.filterData.deadline_time !== '' && state.filterData.deadline_time.length ? formatDate(state.filterData.deadline_time[1]) : '',
    create_start_time: state.filterData.create_time && state.filterData.create_time !== '' && state.filterData.create_time.length ? formatDate(state.filterData.create_time[0]) : '',
    create_end_time: state.filterData.create_time && state.filterData.create_time !== '' && state.filterData.create_time.length ? formatDate(state.filterData.create_time[1]) : '',
    update_start_time: state.filterData.edit_time && state.filterData.edit_time !== '' && state.filterData.edit_time.length ? formatDate(state.filterData.edit_time[0]) : '',
    update_end_time: state.filterData.edit_time && state.filterData.edit_time !== '' && state.filterData.edit_time.length ? formatDate(state.filterData.edit_time[1]) : '',
    audit_start_time: state.filterData.audit_time && state.filterData.audit_time !== '' && state.filterData.audit_time.length ? formatDate(state.filterData.audit_time[0]) : '',
    audit_end_time: state.filterData.audit_time && state.filterData.audit_time !== '' && state.filterData.audit_time.length ? formatDate(state.filterData.audit_time[1]) : '',
    ...state.filterData,
    status: obj.status,
  }
  delete query.effect_time
  delete query.deadline_time
  delete query.audit_time
  delete query.create_time
  delete query.edit_time
  delete query.devierDate
  ApiCustomerList(getFilterData(query))
}, 400)
onMounted(() => {
  getData()
})
watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = dyeingpass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}

// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = dyeingcancel()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '染费报价单'
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = dyeinglistExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

function handDetail(row: any) {
  router.push({
    name: 'DyeingQuotationDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'DyeingQuotationEdit',
    query: { id: row.id },
  })
}

function handleAdd() {
  router.push({
    name: 'DyeingQuotationAdd',
  })
}

const columnList = ref([
  {
    field: 'order_no',
    title: '单据编号',
    fixed: 'left',
    width: '8%',
    soltName: 'link',
  },
  {
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 120,
  },
  {
    field: 'dyeing_factory_name',
    title: '染厂名称',
    minWidth: 120,
  },
  {
    field: 'quote_type_name',
    title: '报价类型',
    minWidth: 120,
  },
  {
    field: 'quote_order_no',
    title: '染厂报价单号',
    minWidth: 150,
  },
  {
    field: 'quote_date',
    title: '染厂报价日期',
    minWidth: 120,
    is_date: true,
  },
  {
    field: 'effect_time',
    title: '生效时间',
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'deadline',
    title: '截止时间',
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'remark',
    title: '单据备注',
    minWidth: 150,
  },
  {
    field: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    field: 'update_user_name',
    title: '修改人',
    width: 100,
  },
  {
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    field: 'auditor_name',
    title: '审核人',
    width: 100,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 150,
  },
  {
    field: 'status',
    title: '单据状态',
    fixed: 'right',
    soltName: 'status',
    showOrder_status: true,
    width: '5%',
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents v-model="state.filterData.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂名称">
          <template #content>
            <el-form-item prop="dye_factory_id">
              <SelectDialog
                v-model="state.filterData.dye_factory_id"
                api="BusinessUnitSupplierEnumlist"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="报价类型:">
          <template #content>
            <el-select v-model="state.filterData.quote_type" clearable class="m-2" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂报价单号:">
          <template #content>
            <el-input v-model="state.filterData.quote_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生效时间:">
          <template #content>
            <!-- <el-date-picker v-model="state.filterData.effect_start_time" type="datetime" placeholder="生效时间" format="YYYY/MM/DD HH:mm:ss" /> -->
            <SelectDate v-model="state.filterData.effect_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="截止时间:">
          <template #content>
            <!-- <el-date-picker v-model="state.filterData.deadline_start_time" type="datetime" placeholder="截止时间" format="YYYY/MM/DD HH:mm:ss" /> -->
            <SelectDate v-model="state.filterData.deadline_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建人:">
          <template #content>
            <SelectComponents v-model="state.filterData.creator_id" api="GetUserDropdownList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核人:">
          <template #content>
            <SelectComponents v-model="state.filterData.auditor_id" api="GetUserDropdownList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核时间:">
          <template #content>
            <SelectDate v-model="state.filterData.audit_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="最后修改人:">
          <template #content>
            <SelectComponents v-model="state.filterData.updater_id" api="GetUserDropdownList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="最后修改时间:">
          <template #content>
            <SelectDate v-model="state.filterData.edit_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents v-model="state.filterData.status" api="GetAuditStatusEnum" multiple label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button v-has="'DyeingQuotationAdd_add'" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
        <BottonExcel v-has="'DyeingQuotationIndex_export'" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
      </template>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'DyeingQuotationDetail_detail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="row.status === 1 || row.status === 3" v-has="'DyeingQuotationEdit_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.status === 1" v-has="'DyeingQuotationIndex_pass'" type="primary" :underline="false" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.status === 2" v-has="'DyeingQuotationIndex_wait'" type="primary" :underline="false" @click="handApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>
