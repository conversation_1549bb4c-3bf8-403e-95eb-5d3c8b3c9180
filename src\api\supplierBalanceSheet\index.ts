import { useRequest } from '@/use/useRequest'
import { type ResponseList } from '@/api/commonTs'
import { useDownLoad } from '@/use/useDownLoad'
import { type GetPayStatus, type GetSupplierBalanceSheetListData } from '@/api/supplierBalanceSheet/rules'

export const GetSupplierBalanceSheetList = () => {
  return useRequest<any, ResponseList<GetSupplierBalanceSheetListData>>({
    url: '/admin/v1/payable/report_forms/getSupplierOweMoneyList',
    method: 'get',
  })
}

export const ExportSupplierBalanceSheetList = ({ nameFile }: any) => {
  return useDownLoad<any>({
    url: '/admin/v1/payable/report_forms/getSupplierOweMoneyList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

export const GetPayStatus = () => {
  return useRequest<any, ResponseList<GetPayStatus>>({
    url: '/admin/v1/payable/enum/payStatus',
    method: 'get',
  })
}
