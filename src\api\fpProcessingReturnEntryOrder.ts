import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmProcessReturnInOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/getFpmProcessReturnInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmProcessReturnInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/addFpmProcessReturnInOrder',
    method: 'post',
  })
}

// 获取详情
export const getFpmProcessReturnInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/getFpmProcessReturnInOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmProcessReturnInOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmProcessReturnInOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmProcessReturnInOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmProcessReturnInOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmProcessReturnInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrder',
    method: 'put',
  })
}

// 获取加工出仓单枚举列表
export const getProcessOutItemEnumList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmProcessOutOrder/getProcessOutItemEnumList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
