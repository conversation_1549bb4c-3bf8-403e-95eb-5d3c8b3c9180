<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: number | string
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 44,
  color: '#004dff',
})
const diff = computed(() => {
  if (typeof props.size === 'number')
    return `${props.size / 2}px`

  // 如果是字符串类型，需要提取数值和单位
  const match = String(props.size).match(/^([\d.]+)([a-z%]*)$/i)
  if (match) {
    const [, value, unit] = match
    return `${Number(value) / 2}${unit}`
  }
  // 默认返回像素单位
  return '22px'
})
const minus = computed(() => {
  return `-${diff.value}`
})
const background = computed(() => {
  return `${props.color.replace(/^#/, 'rgba(').replace(/([0-9a-f]{6}|[0-9a-f]{3})/i, (...args) => {
    const hex = args[1]
    if (hex.length === 3) {
      const [r, g, b] = hex.split('')
      return `${Number.parseInt(r + r, 16)},${Number.parseInt(g + g, 16)},${Number.parseInt(b + b, 16)}`
    }
    return `${Number.parseInt(hex.slice(0, 2), 16)},${Number.parseInt(hex.slice(2, 4), 16)},${Number.parseInt(hex.slice(4, 6), 16)}`
  })},0.2)`
})
</script>

<template>
  <div class="spinner" v-bind="$attrs" :style="{ width: typeof size === 'number' ? `${size}px` : size, height: typeof size === 'number' ? `${size}px` : size }">
    <div />
    <div />
    <div />
    <div />
    <div />
    <div />
  </div>
</template>

<style scoped>
.spinner {
 width: 44px;
 height: 44px;
 animation: spinner-y0fdc1 2s infinite ease;
 transform-style: preserve-3d;
}
.spinner > div {
  background-color: v-bind(background);
  height: 100%;
  position: absolute;
  width: 100%;
  border: 2px solid v-bind(color);
}

.spinner div:nth-of-type(1) {
 transform: translateZ(v-bind(minus)) rotateY(180deg);
}

.spinner div:nth-of-type(2) {
 transform: rotateY(-270deg) translateX(50%);
 transform-origin: top right;
}

.spinner div:nth-of-type(3) {
 transform: rotateY(270deg) translateX(-50%);
 transform-origin: center left;
}

.spinner div:nth-of-type(4) {
 transform: rotateX(90deg) translateY(-50%);
 transform-origin: top center;
}

.spinner div:nth-of-type(5) {
 transform: rotateX(-90deg) translateY(50%);
 transform-origin: bottom center;
}

.spinner div:nth-of-type(6) {
 transform: translateZ(v-bind(diff));
}

@keyframes spinner-y0fdc1 {
 0% {
  transform: rotate(45deg) rotateX(-25deg) rotateY(25deg);
 }

 50% {
  transform: rotate(45deg) rotateX(-385deg) rotateY(25deg);
 }

 100% {
  transform: rotate(45deg) rotateX(-385deg) rotateY(385deg);
 }
}
</style>
