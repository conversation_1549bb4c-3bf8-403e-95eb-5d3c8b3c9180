<script setup lang="ts" name="WeavingConfig">
import { computed } from 'vue'
import type { Carry, DecimalPoint } from '@/common/enum'
import { CarryLabels, DecimalPointLabels } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

interface WeavingConfigData {
  fabric_decimal_places?: DecimalPoint | number
  rounding_type?: Carry | number
  fabric_weight_min?: string | number
  fabric_weight_max?: string | number
}

interface Props {
  modelValue?: WeavingConfigData
  mode?: 'edit' | 'view' // 编辑模式或查看模式
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
  disabled: false,
  modelValue: () => ({}),
})

const emit = defineEmits<{
  'update:modelValue': [value: WeavingConfigData]
}>()

// 计算属性，用于双向绑定
const localValue = computed({
  get: () => props.modelValue || {},
  set: (value: WeavingConfigData) => {
    emit('update:modelValue', value)
  },
})

// 更新单个字段
function updateField(field: keyof WeavingConfigData, value: any) {
  const newValue = { ...localValue.value, [field]: value }
  emit('update:modelValue', newValue)
}

// 格式化显示文本
const decimalPlacesText = computed(() => {
  const value = props.modelValue?.fabric_decimal_places
  if (value === undefined || value === null)
    return '-'
  return DecimalPointLabels[Number(value) as DecimalPoint] || '-'
})

const roundingTypeText = computed(() => {
  const value = props.modelValue?.rounding_type
  if (value === undefined || value === null)
    return '-'
  return CarryLabels[Number(value) as Carry] || '-'
})

const weightRangeText = computed(() => {
  const min = props.modelValue?.fabric_weight_min
  const max = props.modelValue?.fabric_weight_max
  if (!min && !max)
    return '-'
  return `${min || 0}KG ~ ${max || 0}KG`
})
</script>

<template>
  <div class="weaving-config">
    <div class="flex-box">
      <div class="sale">
        织造配置
      </div>
    </div>
    <div class="line" />
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <!-- 坯布位数 -->
      <DescriptionsFormItem label="坯布位数:">
        <template #content>
          <template v-if="mode === 'edit'">
            <el-form-item prop="fabric_decimal_places" style="width: 130px;">
              <el-select
                :model-value="modelValue?.fabric_decimal_places"
                placeholder="请选择坯布位数"
                clearable
                :disabled="disabled"
                style="width: 130px;"
                @update:model-value="updateField('fabric_decimal_places', $event)"
              >
                <el-option
                  v-for="(label, value) in DecimalPointLabels"
                  :key="value"
                  :label="label"
                  :value="Number(value)"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else>
            {{ decimalPlacesText }}
          </template>
        </template>
      </DescriptionsFormItem>

      <!-- 进位 -->
      <DescriptionsFormItem label="进位:">
        <template #content>
          <template v-if="mode === 'edit'">
            <el-form-item prop="rounding_type">
              <el-radio-group
                :model-value="modelValue?.rounding_type"
                :disabled="disabled"
                @update:model-value="updateField('rounding_type', $event)"
              >
                <el-radio
                  v-for="(label, value) in CarryLabels"
                  :key="value"
                  :value="Number(value)"
                >
                  {{ label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-else>
            {{ roundingTypeText }}
          </template>
        </template>
      </DescriptionsFormItem>

      <!-- 坯布重量限制 -->
      <DescriptionsFormItem label="坯布重量限制:" width="390">
        <template #content>
          <template v-if="mode === 'edit'">
            <el-form-item>
              <el-input-number
                :model-value="Number(modelValue?.fabric_weight_min) || undefined"
                placeholder="最小重量"
                :disabled="disabled"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 130px !important"
                @update:model-value="updateField('fabric_weight_min', $event)"
              />
              <span style="margin-left: 8px;">KG</span>
              <span style="margin: 0 8px;">~</span>
              <el-input-number
                :model-value="Number(modelValue?.fabric_weight_max) || undefined"
                placeholder="最大重量"
                :disabled="disabled"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 130px !important"
                @update:model-value="updateField('fabric_weight_max', $event)"
              />
              <span style="margin: 0 8px;">KG</span>
            </el-form-item>
          </template>
          <template v-else>
            {{ weightRangeText }}
          </template>
        </template>
      </DescriptionsFormItem>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.weaving-config {
  .line {
    background: #efefef;
    width: 100%;
    height: 3px;
    margin-top: 15px;
    margin-bottom: 20px;
  }

  .flex-box {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .sale {
      font-weight: 600;
    }
  }
}
</style>
