<script setup lang="ts" name="FpProcessingDeliverFromGodownOrder">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import FineSizeSelectStockDetail from '../components/FineSizeSelectStockDetail.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import {
  getFpmProcessOutOrder,
  getFpmProcessOutOrderList,
  updateFpmProcessOutOrderStatusPass,
  updateFpmProcessOutOrderStatusWait,
} from '@/api/fpProcessingDeliverFromGodownOrder'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'

// import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { sumNum } from '@/util/tableFooterCount'
import { usePageQuery } from '@/use/usePageQuery'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const { formatFilterObj, formatDateRange } = usePageQuery()

const FineSizeSelectStockDetailRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  process_id: '',
  warehouse_id: '',
  audit_status: [],
}))
const out_time = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmProcessOutOrderStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmProcessOutOrderStatusWait()

const {
  fetchData: fetchDataList,
  data: mainList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = getFpmProcessOutOrderList()
const mainOptions = reactive<any>({
  tableConfig: {
    fieldApiKey: 'FpProcessingDeliverFromGodownOrder',
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '9%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'process_name',
      title: '加工单位',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_out_time',
      title: '出仓日期',
      minWidth: 100,
      is_date: true,
    },

    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_price',
      title: '单据金额',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  showToolBar: true,
  //   导出
  handleExport: async () => {
    //   if (mainOptions.mainList.length < 1) return ElMessage.warning('当前无数据可导出')
    //   const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getFpmProcessOutOrderList()
    //   mainOptions.exportOptions.loadingExcel.value = true
    //   await getFetch({
    //     ...getFilterData(mainOptions.mainList),
    //     download: 1,
    //   })
    //   if (getSuccess.value) {
    //     exportExcel()
    //     ElMessage({
    //       type: 'success',
    //       message: '成功',
    //     })
    //   } else {
    //     ElMessage({
    //       type: 'error',
    //       message: getMsg.value,
    //     })
    //   }
    //   mainOptions.exportOptions.loadingExcel.value = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

// const tableRef = ref<any>()
// const exportExcel = () => {
//   tableRef.value.tableRef.exportData({
//     filename: `成品采购退货出仓单列表${formatTime(new Date())}`,
//     // isFooter: true,
//   })
// }
async function getData() {
  const query = {
    ...filterData,
  }
  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (out_time?.value?.length) {
    query.warehouse_out_time_begin = formatDate(out_time.value[0])
    query.warehouse_out_time_end = formatDate(out_time.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainList.value?.list)
    showDetail(mainList.value.list[0])
}

watch(
  () => mainList.value,
  () => {
    mainOptions.mainList
      = mainList.value?.list?.map((item: any) => {
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(Number(item.total_roll)),
          total_weight: formatWeightDiv(Number(item.total_weight)),
          total_length: formatLengthDiv(Number(item.total_length)),
          total_price: formatTwoDecimalsDiv(Number(item.total_price)),
        }
      }) || []
  },
  { deep: true },
)

onMounted(() => {
  getData()
})
onActivated(() => {
  getData()
})
watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      // 审核通过自动生产进仓单
      // router.push({
      //   name:'FpProcessingDeliverFromGodownOrderEdit',
      //   query:{
      //     id:auditData.value.create_id
      //   }
      // })
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

function handDetail(row: any) {
  router.push({
    name: 'FpProcessingDeliverFromGodownOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpProcessingDeliverFromGodownOrderEdit',
    query: {
      id: row.id,
    },
  })
}
function handAdd() {
  router.push({
    name: 'FpProcessingDeliverFromGodownOrderAdd',
  })
}

// 成品信息
const finishProductionOptions = reactive({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'FpProcessingDeliverFromGodownOrder_B',
    showSlotNums: false,
    showSpanHeader: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 出仓数量 结算数量 辅助数量 其他金额 结算金额
        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['out_length'].includes(column.field))
          return sumNum(data, 'out_length', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      field: 'A',
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'out_roll',
          title: '出仓匹数',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'C',
      title: '出仓数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'total_weight',
          title: '出仓数量',
          minWidth: '5%',
        },
        {
          field: 'weight_error',
          title: '码单空差',
          minWidth: '5%',
        },
        {
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_weight',
          title: '结算数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'D',
      title: '出仓辅助数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'out_length',
          title: '辅助数量',
          minWidth: '5%',
        },
      ],
    },

    {
      field: 'E',
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'F',
      title: '出仓信息',
      childrenList: [
        {
          sortable: true,
          field: 'return_roll',
          title: '退货匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'return_weight',
          title: '退货数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'return_length',
          title: '退货辅助数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'dye_roll',
          title: '已排染匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'dye_weight',
          title: '已排染数量',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'G',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          minWidth: 100,
        },
      ],
    },
  ],
  showXima: (row: any) => {
    FineSizeSelectStockDetailRef.value.showDialog(row)
  },
})

function changeDate() {
  // out_time.value = [row.date_min, row.date_max]
  getData()
}
// 获取成品信息
function showDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

const { fetchData: DetailFetch, data: finishProData } = getFpmProcessOutOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        out_roll: formatTwoDecimalsDiv(item.out_roll),
        total_weight: formatWeightDiv(item.total_weight),
        weight_error: formatWeightDiv(item.weight_error),
        settle_error_weight: formatWeightDiv(item.settle_error_weight),
        settle_weight: formatWeightDiv(item.settle_weight),
        out_length: formatLengthDiv(item.out_length),
        return_roll: formatTwoDecimalsDiv(item.return_roll),
        return_weight: formatWeightDiv(item.return_weight),
        return_length: formatLengthDiv(item.return_length),
        dye_roll: formatTwoDecimalsDiv(item.dye_roll),
        dye_weight: formatWeightDiv(item.dye_weight),
        item_fc_data: item.item_fc_data || [],
      }
    },
  )
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工单位:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.process_unit_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.dyeFactory,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 200px"
              api="GetPhysicalWarehouseDropdownList"
              :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出仓日期:" width="310">
          <template #content>
            <SelectDate v-model="out_time" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="mainOptions.showToolBar">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          v-has="'FpProcessingDeliverFromGodownOrder_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpProcessingDeliverFromGodownOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpProcessingDeliverFromGodownOrder_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpProcessingDeliverFromGodownOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpProcessingDeliverFromGodownOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      title=""
      class="table-card-bottom"
    >
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #xima="{ row }">
          <el-link @click="finishProductionOptions.showXima(row)">
            查看
          </el-link>
        </template>
      </Table>
    </FildCard>
  </div>

  <FineSizeSelectStockDetail ref="FineSizeSelectStockDetailRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}

.flex_btns {
  display: flex;
  justify-content: center;
}
</style>
