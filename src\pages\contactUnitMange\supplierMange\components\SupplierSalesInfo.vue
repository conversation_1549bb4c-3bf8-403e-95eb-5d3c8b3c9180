<script lang="ts" setup>
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { EmployeeType } from '@/common/enum'

interface Props {
  modelValue: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理表单数据更新
const updateForm = (field: string, value: any) => {
  emit('update:modelValue', { ...props.modelValue, [field]: value })
}
</script>

<template>
  <div>
    <div class="sale">
      销售信息
    </div>
    <div class="line" />
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="销售员:">
        <template #content>
          <el-form-item prop="saleUser">
            <SelectComponents 
              :model-value="modelValue.saleUser" 
              style="width: 300px" 
              api="Adminemployeelist" 
              :query="{ duty: EmployeeType.salesman }" 
              label-field="name" 
              value-field="id" 
              clearable 
              @update:model-value="updateForm('saleUser', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>
      
      <DescriptionsFormItem label="跟单员:">
        <template #content>
          <el-form-item prop="merchandiser">
            <SelectComponents 
              :model-value="modelValue.merchandiser" 
              style="width: 300px" 
              :query="{ duty: EmployeeType.follower }" 
              api="Adminemployeelist" 
              label-field="name" 
              value-field="id" 
              clearable 
              @update:model-value="updateForm('merchandiser', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>
      
      <DescriptionsFormItem label="跟单员QC:">
        <template #content>
          <el-form-item prop="order_qc_user_id">
            <SelectComponents
              :model-value="modelValue.order_qc_user_id"
              :query="{ duty: EmployeeType.followerQC }"
              style="width: 300px"
              api="Adminemployeelist"
              label-field="name"
              value-field="id"
              clearable
              @update:model-value="updateForm('order_qc_user_id', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>
      
      <DescriptionsFormItem label="销售区域:">
        <template #content>
          <el-form-item prop="saleArea">
            <SelectComponents 
              :model-value="modelValue.saleArea" 
              style="width: 300px" 
              api="Adminbusiness_unitsale_arealist" 
              label-field="name" 
              value-field="id" 
              clearable 
              @update:model-value="updateForm('saleArea', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>
      
      <DescriptionsFormItem label="销售群体:">
        <template #content>
          <el-form-item prop="salePopulation">
            <SelectComponents 
              :model-value="modelValue.salePopulation" 
              style="width: 300px" 
              api="Adminbusiness_unitsale_grouplist" 
              label-field="name" 
              value-field="id" 
              clearable 
              @update:model-value="updateForm('salePopulation', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}

.sale {
  margin-top: 40px;
  font-weight: 600;
}
</style>
