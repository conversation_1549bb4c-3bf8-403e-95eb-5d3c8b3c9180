# SelectSaleSystemDialog 组件使用说明

## 概述
`SelectSaleSystemDialog` 组件用于提供一个弹窗，让用户选择营销体系。当存在多个营销体系时，会以单选框的形式展示供用户选择；若没有营销体系，则会提示用户前往维护。

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| showModal | boolean | false | 是否显示弹窗，可以使用 v-model 双向绑定 |
| list | any[] | [] | 当前客户的所属营销体系 |

## 事件

| 事件名                | 参数 | 说明 |
|--------------------| --- | --- |
| handleSure        | val: object | 选中营销体系 |
