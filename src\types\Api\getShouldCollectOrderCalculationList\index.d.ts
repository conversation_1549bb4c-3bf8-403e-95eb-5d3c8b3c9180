declare namespace Api.GetShouldCollectOrderCalculationList {
  export interface Request {
    /**
     * 订单状态，多选
     */
    audit_status?: string
    /**
     * 审核人ID
     */
    auditor_id?: number
    /**
     * 所属客户id
     */
    customer_id?: number
    /**
     * download
     */
    download?: number
    /**
     * 审核时间
     */
    end_audit_date?: string
    /**
     * 单据结束日期
     */
    end_order_time?: string
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * 应收单号
     */
    order_no?: string
    /**
     * page
     */
    page?: number
    /**
     * 营销体系ID
     */
    sale_system_id?: number
    /**
     * size
     */
    size?: number
    /**
     * 审核时间
     */
    start_audit_date?: string
    /**
     * 单据开始日期
     */
    start_order_time?: string
    /**
     * 凭证单号
     */
    voucher_number?: string
    [property: string]: any
  }
  /**
   * should_collect_order.CostCalculationListData
   */
  export interface Response {
    list?: ShouldCollectOrderCostCalculationListItemData[]
    total_cost_price?: number
    total_gross_margin?: number
    total_gross_profit?: number
    total_roll?: number
    total_sale_price?: number
    total?: number
    [property: string]: any
  }

  /**
   * should_collect_order.CostCalculationListItemData
   */
  export interface ShouldCollectOrderCostCalculationListItemData {
    /**
     * 码单数量(细码数量-码单空差)
     */
    actually_weight?: number
    /**
     * 收货地址
     */
    address?: string
    /**
     * 配布单id
     */
    arrange_order_id?: number
    /**
     * 配布单号
     */
    arrange_order_no?: string
    /**
     * 订单状态 1待审核 2已审核 3已驳回 4已作废
     */
    audit_status?: number
    /**
     * 订单状态 1待审核 2已审核 3已驳回 4已作废
     */
    audit_status_name?: string
    /**
     * 毛重成本单价
     */
    buoyant_weight_price?: number
    /**
     * 收款状态 1未收款 2已收部分 3已收全款
     */
    collect_status?: number
    /**
     * 收款状态 1未收款 2已收部分 3已收全款
     */
    collect_status_name?: string
    /**
     * 色号#颜色
     */
    color?: string
    /**
     * 日期(送货单创建日期)
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 客户名称
     */
    customer_name?: string
    /**
     * 缸号
     */
    dyelot_number?: string
    fc_list?: ShouldCollectOrderCostCalculationListItemFcData[]
    /**
     * 成品销售出仓单id
     */
    fpm_sale_out_order_id?: number
    /**
     * 成品销售出仓单号
     */
    fpm_sale_out_order_no?: string
    /**
     * 毛利率(毛利 / 销售金额 * 100%)
     */
    gross_margin?: string
    /**
     * 毛利(销售金额 -成本金额)
     */
    gross_profit?: number
    /**
     * 记录ID
     */
    id?: number
    /**
     * 优惠幅度（标准报价-最终的售价）
     */
    offset_sale_price?: number
    /**
     * 其他金额
     */
    other_price?: number
    /**
     * 面料编号#名称
     */
    product?: string
    /**
     * 匹数(件)
     */
    roll?: number
    /**
     * 销售跟单员id
     */
    sale_follower_id?: number
    /**
     * 销售跟单员
     */
    sale_follower_name?: string
    /**
     * 销售单id
     */
    sale_order_id?: number
    /**
     * 销售单号
     */
    sale_order_no?: string
    /**
     * 销售单价(销售报价-优惠单价)(大货 散剪)
     */
    sale_price?: number
    /**
     * 营销体系ID
     */
    sale_system_id?: number
    /**
     * 营销体系名称
     */
    sale_system_name?: string
    /**
     * 销售税率
     */
    sale_tax_rate?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    /**
     * 销售员
     */
    sale_user_name?: string
    /**
     * 结算空差数量
     */
    settle_error_weight?: number
    /**
     * 结算金额(销售单价 *结算数量 +其他金额)
     */
    settle_price?: number
    /**
     * 结算数量(码单数量 - 结算空差)
     */
    settle_weight?: number
    /**
     * 应收单id
     */
    should_collect_order_id?: number
    /**
     * 应收单号
     */
    should_collect_order_no?: string
    /**
     * 成本金额(毛重单价 * 码单数量)
     */
    total_cost_price?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 仓库
     */
    warehouse_name?: string
    [property: string]: any
  }

  /**
   * should_collect_order.CostCalculationListItemFcData
   */
  export interface ShouldCollectOrderCostCalculationListItemFcData {
    actually_weight?: number
    buoyant_weight_price?: number
    dyelot_number?: string
    finish_product_gram_weight?: string
    finish_product_width?: string
    length?: number
    paper_tube_weight?: number
    roll?: number
    settle_error_weight?: number
    settle_weight?: number
    volume_number?: number
    warehouse_bin_id?: number
    weight_error?: number
    [property: string]: any
  }
}
