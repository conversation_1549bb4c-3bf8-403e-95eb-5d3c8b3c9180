import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const getInfoSaleLogisticsCompanyList = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleLogisticsCompany/getInfoSaleLogisticsCompanyList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新建类型
export const addInfoSaleLogisticsCompany = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleLogisticsCompany/addInfoSaleLogisticsCompany',
    method: 'post',
  })
}

// 更新类型
export const updateInfoSaleLogisticsCompany = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleLogisticsCompany/updateInfoSaleLogisticsCompany',
    method: 'put',
  })
}

// 删除类型
export const deleteTypeIntercourseUnits = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleLogisticsCompany/deleteInfoSaleLogisticsCompany',
    method: 'delete',
  })
}

// 修改状态
export const updateTypeIntercourseUnitsStatus = () => {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoSaleLogisticsCompany/updateInfoSaleLogisticsCompanyStatus',
    method: 'put',
  })
}
