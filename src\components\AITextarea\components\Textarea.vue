<script lang="ts" setup>
import { ref, watch } from 'vue'
import { Promotion } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
  placeholder?: string
  modelValue?: string
}>(), {
  placeholder: '输入或黏贴下单内容',
  modelValue: '',
})

const emit = defineEmits(['update:modelValue', 'submit'])

const showFullScreen = ref(false)
const value = ref(props.modelValue)

// 监听值变化
watch(() => props.modelValue, (newVal) => {
  value.value = newVal
})

watch(() => value.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 提交内容
function handleSubmit() {
  if (!value.value)
    return
  emit('submit', value.value)
}

// 全屏切换
function handFullScreen() {
  showFullScreen.value = !showFullScreen.value
}
</script>

<template>
  <div class="AITextarea-card__content flex flex-col">
    <el-input
      v-model="value"
      :class="showFullScreen ? 'textarea-full-screen' : ''"
      class="w-full border-none h-full"
      type="textarea"
      show-word-limit
      :rows="3"
      v-bind="$attrs"
      :placeholder="placeholder"
      @keydown.enter="(e) => {
        if (!e.shiftKey) {
          e.preventDefault()
          handleSubmit()
        }
      }"
    />
    <div class="flex justify-end items-center gap-x-1">
      <!-- 全屏按钮 -->
      <div
        class="cursor-pointer rounded flex justify-center items-center hover:bg-[#f1f2f3] p-1"
        @click="handFullScreen"
      >
        <el-icon v-if="!showFullScreen" size="20px" color="#3d3d43">
          <FullScreen />
        </el-icon>
        <span v-if="showFullScreen" class="text-[20px] text-black">
          <i class="fas fa-compress" />
        </span>
      </div>
      <!-- 提交按钮 -->
      <div
        class="cursor-pointer rounded flex justify-center items-center hover:bg-[#f1f2f3] p-1"
        @click="handleSubmit"
      >
        <el-icon size="20px">
          <Promotion />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.AITextarea-card__content {
  background: #fff;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
:deep(.el-textarea__inner) {
  background: #fff;
  box-shadow: none;
  outline: none;
  height: 100%;
  padding: 0;
  &:focus {
    box-shadow: none;
  }
  &:hover {
    box-shadow: none;
  }
}

.textarea-full-screen :deep(.el-textarea__inner) {
  height: 80vh;
}
</style>
