<script setup lang="ts" name="SalesReport">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { formatDate } from '@/common/format'
import { debounce, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { EmployeeType } from '@/common/enum'
import { getSaleSimpleReportList } from '@/api/transferSales'
import AITextarea from '@/components/AITextarea/index.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    order_no: '',
    sale_system_id: '',
    sale_order_no: '',
    src_order_no: '',
    devierDate: '',
    auditor_id: '',
    updater_id: '',
    create_time: '',
    audit_time: '',
    edit_time: '',
    audit_status: '',
    customer_id: '',
    voucher_number: '',
    sale_user_id: '',
    product_color_id: '',
    product_id: '',
    seller_id: '',
  },
  multipleSelection: [],
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getSaleSimpleReportList()

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})
function formatParams(data: any) {
  const query: any = {
    order_date_start:
      data?.devierDate
      && data.devierDate !== ''
      && data.devierDate.length
        ? formatDate(data.devierDate[0])
        : '',
    order_date_end:
      data?.devierDate
      && data.devierDate !== ''
      && data.devierDate.length
        ? formatDate(data.devierDate[1])
        : '',
    ...data,
  }
  delete query?.devierDate
  return query
}
// 获取数据
const getData = debounce(() => {
  const query: any = formatParams(state.filterData)
  ApiCustomerList(getFilterData(query))
}, 400)

onMounted(() => {
  getData()
})

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: false,
  showOperate: false,
  operateWidth: '220',
  showSort: false,
  height: '100%',
  fieldApiKey: 'SalesReport',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  headerCellClassName: (val: any) => headerCellClassName(val),
  rowColor: {
    roll: 'purple',
    return_roll: 'purple',
    total_roll: 'purple',
    weight: 'green',
    return_weight: 'green',
    total_weight: 'green',
    sale_price: 'lightYellow',
    return_price: 'lightYellow',
    total_price: 'lightYellow',
  },
  needCellColor: false,
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

function headerCellClassName(val: any) {
  if (val.field === 'roll')
    return 'col-blue'
}

const columnList = ref([
  {
    sortable: true,
    field: 'material_code',
    title: '面料编号',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'material_name',
    title: '面料名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'product_color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'product_color_name',
    title: '颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'measurement_unit_name',
    title: '计量单位',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'roll',
    title: '匹数',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'return_roll',
    title: '退货匹数',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'total_roll',
    title: '总匹数',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'weight',
    title: '销售数量',
    minWidth: '5%',
    isWeight: true,
  },
  {
    field: 'return_weight',
    title: '退货数量',
    sortable: true,
    minWidth: '5%',
    isWeight: true,
  },
  {
    field: 'total_weight',
    title: '总数量',
    sortable: true,
    minWidth: '5%',
    isWeight: true,
  },
  {
    field: 'sale_price',
    title: '销售金额',
    sortable: true,
    isPrice: true,
    minWidth: '5%',
  },
  {
    field: 'return_price',
    title: '退货金额',
    sortable: true,
    isPrice: true,
    minWidth: 140,
  },
  {
    field: 'total_price',
    title: '总金额',
    sortable: true,
    isPrice: true,
    minWidth: 140,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

onActivated(() => {
  getData()
})
</script>

<template>
  <div class="flex flex-col overflow-hidden h-full">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="订单日期" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.product_code,
              }"
              @on-input="(val) => (componentRemoteSearch.product_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.product_name,
              }"
              @on-input="(val) => (componentRemoteSearch.product_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色">
          <template #content>
            <SelectDialog
              v-model="state.filterData.product_color_id"
              :query="{
                finish_product_id: state.filterData.product_id,
                name: componentRemoteSearch.color_name,
              }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="(val) => (componentRemoteSearch.color_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号">
          <template #content>
            <SelectDialog
              v-model="state.filterData.product_color_id"
              :query="{
                finish_product_id: state.filterData.product_id,
                code: componentRemoteSearch.color_code,
              }"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="(val) => (componentRemoteSearch.color_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称">
          <template #content>
            <el-form-item>
              <SelectDialog
                v-model="state.filterData.customer_id"
                :query="{ name: componentRemoteSearch.customer_name }"
                api="GetCustomerEnumList"
                :column-list="[
                  {
                    title: '客户编号',
                    minWidth: 100,
                    required: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '客户编号',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '客户名称',
                    minWidth: 100,
                    colGroupHeader: true,
                    required: true,
                    childrenList: [
                      {
                        isEdit: true,
                        field: 'name',
                        title: '客户名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '电话',
                    colGroupHeader: true,
                    minWidth: 100,
                    childrenList: [
                      {
                        field: 'phone',
                        isEdit: true,
                        title: '电话',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    title: '销售员',
                    minWidth: 100,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'seller_name',
                        title: '销售员',
                        soltName: 'seller_name',
                        isEdit: true,
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="
                  (val) => (componentRemoteSearch.customer_name = val)
                "
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员">
          <template #content>
            <SelectComponents
              v-model="state.filterData.seller_id"
              api="Adminemployeelist"
              :query="{ duty: EmployeeType.salesman }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard
      class="mt-[5px] flex flex-1 flex-col h-full overflow-hidden"
    >
      <template #title>
        <div class="flex items-center mb-2">
          <AITextarea v-has="'AIReportAnalysis'" type="output" :output-params="formatParams(state.filterData)" text="AI报表分析" class="ml-2" :loading="loading" placeholder="输入或黏贴下单内容" :max-length="500" />
        </div>
      </template>
      <Table
        class="mytable-style"
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      />
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.mytable-style.vxe-table .vxe-header--column.col-blue) {
  background-color: #c2c1ff;
}
</style>
