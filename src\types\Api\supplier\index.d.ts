declare namespace Api.Supplier {
  /**
   * system.UpdateSupplierParams
   */
  export interface Request {
  /**
   * 地址
   */
    address?: string
    /**
     * 布飞前缀
     */
    bf_prefix?: string
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule?: number
    /**
     * 进位
     */
    carry?: number
    /**
     * 进位
     */
    carry_name?: string
    /**
     * 类别 1客户 2供应商
     */
    category?: number
    /**
     * 编号
     */
    code?: string
    /**
     * 联系人名称
     */
    contact_name?: string
    /**
     * 社会统一信用代码
     */
    credit_code?: string
    /**
     * 信用等级
     */
    credit_level?: number
    /**
     * 信用额度
     */
    credit_limit?: number
    /**
     * 坯布位数
     */
    decimal_point?: number
    /**
     * 坯布位数
     */
    decimal_point_name?: string
    /**
     * 染费收费方式
     */
    dnf_charging_method?: number
    /**
     * 邮箱
     */
    email?: string
    /**
     * 坯布最大重量
     */
    fabric_max_weight?: number
    /**
     * 坯布最小重量
     */
    fabric_min_weight?: number
    /**
     * 传真
     */
    fax_number?: string
    /**
     * 全称
     */
    full_name?: string
    id?: number
    /**
     * 是否创建简单（绕过非空判断）
     */
    is_simple_add?: boolean
    /**
     * 省市区
     */
    location?: string
    /**
     * 主要类型id
     */
    main_unit_type_id?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 跟单员id
     */
    order_follower_id?: number
    /**
     * 跟单QC员id
     */
    order_qc_user_id?: number
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 拼音
     */
    pin_yin?: string
    /**
     * 企业微信客户
     */
    qywx_customers?: SystemQYWXCustomer[]
    /**
     * 企业微信群聊
     */
    qywx_groups?: SystemQYWXGroupChat[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 销售区域id
     */
    sale_area_id?: number
    /**
     * 销售群体id
     */
    sale_group_id?: number
    /**
     * 所属营销体系id
     */
    sale_system_id?: number
    /**
     * 所属营销体系ids
     */
    sale_system_ids?: number[]
    /**
     * 销售员id
     */
    seller_id?: number
    /**
     * 结算天数
     */
    settle_cycle?: number
    /**
     * 结算类型
     */
    settle_type?: number
    /**
     * 类型id
     */
    unit_type_id?: number[]
    /**
     * 织造入库方式
     */
    weaving_storage_method?: number
    /**
     * 称重验布流程
     */
    wfi_process?: number
    [property: string]: any
  }

  /**
   * system.QYWXCustomer
   */
  export interface SystemQYWXCustomer {
    id?: string
    name?: string
    type?: string
    [property: string]: any
  }

  /**
   * system.QYWXGroupChat
   */
  export interface SystemQYWXGroupChat {
    id?: string
    name?: string
    notify_type?: number
    notify_type_name?: string
    [property: string]: any
  }
}
