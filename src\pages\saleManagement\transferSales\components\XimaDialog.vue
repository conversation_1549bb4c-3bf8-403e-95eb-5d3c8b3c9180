<script setup lang="ts">
import Decimal from 'decimal.js'
import { nextTick, reactive, ref, watch } from 'vue'
import { ElMessageBox } from 'element-plus'
import { sumNum } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Table from '@/components/Table.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  showModal: false,
  modalName: '细码录入',
  code: '',
  name: '',
  horsepower: 0,
  id: -1,
  isDisabled: false,
  rowIndex: -1,
  info: {
    finish_product_code: '',
    finish_product_name: '',
    product_color_code: '',
    product_color_name: '',
    dyelot: '',
    supplier_name: '',
    totalSaleSettleWeight: 0,
    measurement_unit_name: '',
    supplier_id: '',
  },
})

const tableData = ref([
  {
    sale_weight: 0,
    sale_settle_weight: 0,
    supplier_weight: 0,
    sale_weight_error: 0,
    supplier_weight_error: 0,
    supplier_selltle_weight: 0,
  },
])

const totalSettleWeight = ref(0)

const tableConfig = ref({
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: state.isDisabled,
  operateWidth: '100',
  height: '100%',
  showFooterBtn: state.isDisabled,
  clickFooterItem: () => clickFooterItem(),
  footerCellClassName: (row: any) => footerCellClassName(row),
})

watch(
  () => state.showModal,
  () => {
    if (!state.isDisabled) {
      tableConfig.value.showOperate = true
      tableConfig.value.showFooterBtn = true
    }
    else {
      tableConfig.value.showOperate = false
      tableConfig.value.showFooterBtn = false
    }
  },
)

function footerCellClassName(_row: any) {
  // if (row.columnIndex === 0 && row.$rowIndex === 0) {
  return 'col-grey'
  // }
  // return 'col-grey'
}

const columnList = ref([
  {
    field: 'sale_weight',
    title: '细码',
    minWidth: 100,
    soltName: 'sale_weight',
  },
  {
    field: 'sale_weight_error',
    title: '空差',
    minWidth: 100,
    soltName: 'sale_weight_error',
  },
  {
    field: 'sale_settle_weight',
    title: '结算数量',
    minWidth: 100,
  },
  {
    field: 'supplier_weight',
    title: '供应商细码',
    minWidth: 100,
    soltName: 'supplier_weight',
  },
  {
    field: 'supplier_weight_error',
    title: '供应商空差',
    minWidth: 100,
    soltName: 'supplier_weight_error',
  },
  {
    field: 'supplier_selltle_weight',
    title: '供应商结算数量',
    minWidth: 100,
  },
])

function handCancel() {
  state.showModal = false
}

/**
 * 在条码数据有变动才会触发emit
 */
async function handleSure() {
  const settleWeight = Number(totalSettleWeight.value)
  const params = { ...state, tableData: tableData.value }
  if (settleWeight !== 0) {
    if (settleWeight !== Number(state.info.totalSaleSettleWeight)) {
      ElMessageBox.confirm('录入的总细码与原先列表录入的数据不一致，是否继续覆盖原先数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          emits('handleSure', params)
        })
        .catch(() => {
          // handCancel()
        })
    }
    else {
      emits('handleSure', params)
    }
  }
  else {
    handCancel()
  }
}
function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['sale_weight'].includes(column.property))
        return `${sumNum(data, 'sale_weight')}`

      if (['sale_weight_error'].includes(column.property))
        return `${sumNum(data, 'sale_weight_error')}`

      if (['sale_settle_weight'].includes(column.property)) {
        totalSettleWeight.value = sumNum(data, 'sale_settle_weight')
        return `${totalSettleWeight.value}`
      }
      if (['supplier_weight'].includes(column.property))
        return `${sumNum(data, 'supplier_weight')}`

      if (['supplier_weight_error'].includes(column.property))
        return `${sumNum(data, 'supplier_weight_error')}`

      if (['supplier_selltle_weight'].includes(column.property))
        return `${sumNum(data, 'supplier_selltle_weight')}`

      return null
    }),
  ]
}

const tableRef = ref()
const { addOrNextLineFocus } = useTableEnterAutoFocus(
  tableRef,
  tableData,
  handAdd,
)
watch(
  () => tableData.value,
  () => {
    if (tableData.value.length > 0) {
      tableData.value.map((item: any) => {
        item.sale_weight_error = Number(item.sale_weight_error)
        item.supplier_weight_error = Number(item.supplier_weight_error)
        item.sale_weight = Number(item.sale_weight)
        item.supplier_weight = Number(item.supplier_weight)
        item.sale_settle_weight = Number(new Decimal(Number(item.sale_weight)).minus(new Decimal(Number(item.sale_weight_error))))
        item.supplier_selltle_weight = Number(new Decimal(Number(item.supplier_weight)).minus(new Decimal(Number(item.supplier_weight_error))))
        return item
      })
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handAdd() {
  //   if (state.total_roll >= state.canEnter) {
  //     return ElMessage.error('匹数不可大于原库存匹数')
  //   }
  tableData.value.push({
    sale_weight: '',
    sale_settle_weight: '',
    supplier_weight: '',
    sale_weight_error: tableData.value[tableData.value.length - 1]?.sale_weight_error || '',
    supplier_weight_error: tableData.value[tableData.value.length - 1]?.supplier_weight_error || '',
    supplier_selltle_weight: '',
  })
  // nextTick(() => {
  //   refMap[0].focus()
  // })
  tableData.value = [...tableData.value]
}

const refMap: Record<string, any> = {}

const refMapTwo: Record<string, any> = {}

const refMapThree: Record<string, any> = {}

const refMapFour: Record<string, any> = {}

function setRefMapOne(el: any, index: number) {
  if (el)
    refMap[`${index}`] = el
}

function setRefMapTwo(el: any, index: number) {
  if (el)
    refMapTwo[`${index}`] = el
}

function setRefMapThree(el: any, index: number) {
  if (el)
    refMapThree[`${index}`] = el
}

function setRefMapFour(el: any, index: number) {
  if (el)
    refMapFour[`${index}`] = el
}

function handDelete(rowIndex: number) {
  tableData.value.splice(rowIndex, 1)
}

function clickFooterItem() {
  tableData.value = []
}

defineExpose({
  state,
  tableData,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="h-full flex flex-col">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:" width="220">
          <template #content>
            {{ state.info.finish_product_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:" width="220">
          <template #content>
            {{ state.info.finish_product_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号:" width="220">
          <template #content>
            {{ state.info.product_color_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色:" width="220">
          <template #content>
            {{ state.info.product_color_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:" width="220">
          <template #content>
            {{ state.info.dyelot }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:" width="220">
          <template #content>
            {{ state.info.supplier_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商单位:" width="220">
          <template #content>
            <!--          {{ state.info.measurement_unit_name }} -->
            <SelectComponents
              v-if="!state.isDisabled"
              v-model="state.info.supplier_id"
              api="getInfoBaseMeasurementUnitEnumList"
              label-field="name"
              value-field="id"
              clearable
            />
            <div v-else>
              {{ state.info?.measurement_unit_name }}
            </div>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="匹数:">
          <template #content>
            <vxe-input v-if="!state.isDisabled" v-model="state.horsepower" type="float" clearable />
            <div v-else>
              {{ state.horsepower }}
            </div>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem>
          <template #content>
            <el-button v-if="!state.isDisabled" type="primary" @click="handAdd">
              新增
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-1 overflow-y-hidden">
        <Table ref="tableRef" :config="tableConfig" :column-list="columnList" :table-list="tableData">
          <template #sale_weight="{ row, rowIndex }">
            <vxe-input
              v-if="!state.isDisabled"
              :id="`sale_weight${rowIndex}`"
              :ref="(el:any) => setRefMapOne(el, rowIndex)"
              v-model="row.sale_weight"
              @blur="row.supplier_weight = row.sale_weight"
              @keydown="addOrNextLineFocus(rowIndex, $event, 'sale_weight')"
            />
            <div v-else>
              {{ row.sale_weight }}
            </div>
          </template>
          <template #sale_weight_error="{ row, rowIndex }">
            <vxe-input
              v-if="!state.isDisabled"
              :id="`sale_weight_error${rowIndex}`"
              :ref="(el:any) => setRefMapTwo(el, rowIndex)"
              v-model="row.sale_weight_error"
              type="float"
              @blur="row.supplier_weight_error = row.sale_weight_error"
              @keydown="addOrNextLineFocus(rowIndex, $event, 'sale_weight_error')"
            />
            <div v-else>
              {{ row.sale_weight_error }}
            </div>
          </template>

          <template #supplier_weight="{ row, rowIndex }">
            <vxe-input
              v-if="!state.isDisabled"
              :id="`supplier_weight${rowIndex}`"
              :ref="(el:any) => setRefMapThree(el, rowIndex)"
              v-model="row.supplier_weight"
              type="float"
              placeholder=""
              @keydown="addOrNextLineFocus(rowIndex, $event, 'supplier_weight')"
            />
            <div v-else>
              {{ row.supplier_weight }}
            </div>
          </template>
          <template #supplier_weight_error="{ row, rowIndex }">
            <vxe-input
              v-if="!state.isDisabled"
              :id="`supplier_weight_error${rowIndex}`"
              :ref="(el:any) => setRefMapFour(el, rowIndex)"
              v-model="row.supplier_weight_error"
              :min="0"
              type="float"
              placeholder=""
              @keydown="addOrNextLineFocus(rowIndex, $event, 'supplier_weight_error')"
            />
            <div v-else>
              {{ row.supplier_weight_error }}
            </div>
          </template>
          <template v-if="!state.isDisabled" #operate="{ rowIndex }">
            <el-button text type="danger" @click="handDelete(rowIndex)">
              删除
            </el-button>
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <div v-if="!state.isDisabled" class="buttom-oper" style="margin-top: 20px">
        <el-button @click="handCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleSure">
          确认
        </el-button>
      </div>
    </template>
  </vxe-modal>
</template>
