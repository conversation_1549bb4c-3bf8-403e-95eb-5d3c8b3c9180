<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  cancelApprovedSheetOfProductionChange,
  checkSheetOfProductionChange,
  deleteCancelSheetOfProductionChange,
  getProductionChange,
  rejectSheetOfProductionChange,
} from '@/api/productionChange'
import { formatDate, formatPriceDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { deleteToast } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'

const route = useRoute()
const form_options = [
  {
    text: '源单单号',
    key: 'production_notify_order_no',
    order_no: true,
  },
  {
    text: '生产计划单号',
    key: 'production_plan_order_no',
    pro_plan_order_no: true,
  },
  {
    text: '通知日期',
    key: 'notify_date',
  },
  {
    text: '营销体系',
    key: 'sale_system_name',
  },
  {
    text: '织厂名称',
    key: 'weaving_mill_name',
  },
  {
    text: '织厂跟单',
    key: 'weaving_mill_order_follower_name',
  },
  {
    text: '跟单电话',
    key: 'weaving_mill_order_follower_phone',
  },
  {
    text: '客户名称',
    key: 'customer_name',
  },
  {
    text: '销售员',
    key: 'sale_user_name',
  },
  {
    text: '跟单QC员',
    key: 'order_qc_user_name',
  },
  {
    text: '付款期限',
    key: 'payment_term_name',
  },
  {
    text: '交坯日期',
    key: 'receipt_grey_fabric_date',
  },
  {
    text: '收坯地址',
    key: 'receipt_grey_fabric_address',
    copies: 2,
  },
  {
    text: '发票抬头',
    key: 'invoice_header_name',
    copies: 2,
  },
  {
    text: '单据备注',
    key: 'order_remark',
    copies: 2,
  },
]
const required_options = [
  {
    text: '织造规格',
    key: 'weaving_specifications_name_list',
  },
  {
    text: '针寸数',
    key: 'needle_size',
  },
  {
    text: '总针数',
    key: 'total_needle_size',
  },
  {
    text: '织造损耗',
    key: 'weaving_loss',
    unit: '%',
  },
  {
    text: '织机品牌',
    key: 'loom_brand',
  },
  {
    text: '织机机型',
    key: 'loom_model_name',
  },
  {
    text: '安排机台数',
    key: 'machines_num',
  },
  {
    text: '上针',
    key: 'upper_needle',
    copies: 2,
  },
  {
    text: '纱批',
    key: 'yarn_batch',
    copies: 2,
  },
  {
    text: '下针',
    key: 'lower_needle',
    copies: 2,
  },
  {
    text: '排纱',
    key: 'yarn_arrange',
    copies: 2,
  },
  {
    text: '纱长',
    key: 'yarn_length',
    copies: 2,
  },
  {
    text: '包装要求',
    key: 'packaging_require',
    copies: 2,
  },
]
const state = reactive<any>({
  baseDataForm: {},
  greyList: [],
  technologicalRequirementForm: {},
  masterList: [],
})

const { fetchData: detailFetch, data, success: detailSuccess, msg: detailMsg } = getProductionChange()

onMounted(() => {
  getData()
})

async function getData() {
  await detailFetch({ id: route.query.id })
  if (detailSuccess.value) {
    // 基础信息
    state.baseDataForm = {
      ...data.value,
      notify_date: formatDate(data.value.notify_date),
      receipt_grey_fabric_date: formatDate(data.value.receipt_grey_fabric_date),
    }
    // 坯布信息
    state.greyList = [
      {
        ...data.value,
        scheduling_roll: formatTwoDecimalsDiv(Number(data.value.scheduling_roll)),
        change_roll: formatTwoDecimalsDiv(Number(data.value.change_roll)),
        change_weight: formatWeightDiv(Number(data.value.change_weight)),
        scheduling_weight: formatWeightDiv(Number(data.value.scheduling_weight)),
        final_roll: formatTwoDecimalsDiv(Number(data.value.final_roll)),
      },
    ]
    // 用料比例
    state.masterList
      = data.value?.production_notify_material_ratio?.map((item: any) => {
        return {
          ...item,
          yarn_ratio: formatTwoDecimalsDiv(item.yarn_ratio),
          yarn_loss: formatTwoDecimalsDiv(item.yarn_loss),
          grey_fabric_color_name: data.value.grey_fabric_color_name,
          use_yarn_quantity: formatWeightDiv(item.use_yarn_quantity),
          send_yarn_quantity: formatWeightDiv(item.send_yarn_quantity),
          change_use_yarn_quantity: formatWeightDiv(item.change_use_yarn_quantity),
          change_send_yarn_quantity: formatWeightDiv(item.change_send_yarn_quantity),
          yarn_length: data.value.yarn_length,
        }
      }) || []
    // 工艺要求
    state.technologicalRequirementForm = {
      ...data.value,
      weaving_loss: `${formatTwoDecimalsDiv(data.value.weaving_loss)}%`,
      weaving_specifications_name_list: data.value.weaving_specifications
        .map((v: any) => v.weaving_specifications_name)
        .join(','),
    }
  }
  else {
    ElMessage.error(detailMsg.value)
  }
}

// 坯布信息表格配置
const columnList_fabic_config = ref({
  fieldApiKey: 'ProductionChangeDetail_A',
  showSlotNums: false,
})
const columnList_fabic = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'weight_of_fabric',
    soltName: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'process_price',
    soltName: 'process_price',
    title: '加工单价',
    minWidth: 100,
  },
  {
    field: 'scheduling_roll',
    title: '原匹数',
    minWidth: 100,
  },
  {
    field: 'change_roll',
    soltName: 'change_roll',
    title: '变更匹数',
    minWidth: 100,
  },
  {
    field: 'scheduling_weight',
    title: '原数量',
    minWidth: 150,
  },
  {
    field: 'change_weight',
    soltName: 'change_weight',
    title: '变更数量',
    minWidth: 100,
  },
])

// 用料比例表格配置
const columnList_master_config = ref({
  fieldApiKey: 'ProductionChangeDetail_B',
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
})
const columnList_master = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'weaving_category',
    title: '织造类别',
    minWidth: 100,
  },
  {
    field: 'yarn_length',
    title: '纱长',
    minWidth: 100,
  },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方',
    minWidth: 100,
  },
  {
    field: 'mill_private_yarn',
    soltName: 'mill_private_yarn',
    title: '织厂出料',
    minWidth: 100,
  },
  {
    field: 'yarn_ratio',
    soltName: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    soltName: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
  },
  {
    field: 'use_yarn_quantity',
    title: '原用纱量',
    minWidth: 100,
  },
  {
    field: 'change_use_yarn_quantity',
    soltName: 'change_use_yarn_quantity',
    title: '变更用纱量',
    minWidth: 100,
  },
  {
    field: 'send_yarn_quantity',
    title: '原发纱量',
    minWidth: 100,
  },
  {
    field: 'change_send_yarn_quantity',
    soltName: 'change_send_yarn_quantity',
    title: '变更发纱量',
    minWidth: 100,
  },

  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])
function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['yarn_ratio'].includes(column.field))
        return sumNum(data, 'yarn_ratio', '%')

      if (['use_yarn_quantity'].includes(column.field))
        return sumNum(data, 'use_yarn_quantity', '')

      if (['send_yarn_quantity'].includes(column.field))
        return sumNum(data, 'send_yarn_quantity', '')

      return null
    }),
  ]
  return footerData
}
const router = useRouter()
// 跳转单号详情
async function jumpPlanDetail(id: number, type: number) {
  const options: any = {
    1: 'ProductionNoticeDetail',
    2: 'SheetOfProductionPlanDetail',
  }
  router.push({
    name: options[type],
    query: {
      id,
    },
  })
}
// 作废
const {
  fetchData: deleteCancelFetch,
  success: deleteCancelSuccess,
  msg: deleteCancelMsg,
} = deleteCancelSheetOfProductionChange()
async function cancel(status: number) {
  const res = await deleteToast('确认作废嘛？')
  if (res) {
    await deleteCancelFetch({ status, id: route.query.id })
    if (deleteCancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteCancelMsg.value)
    }
  }
}
// 驳回
const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = rejectSheetOfProductionChange()
async function reject(status: number) {
  const res = await deleteToast('确认驳回嘛？')
  if (res) {
    await rejectFetch({ status, id: route.query.id })
    if (rejectSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(rejectMsg.value)
    }
  }
}
// 审核
const { fetchData: auditlFetch, success: auditlSuccess, msg: auditlMsg } = checkSheetOfProductionChange()
async function audit(status: number) {
  await auditlFetch({ status, id: route.query.id })
  if (auditlSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(auditlMsg.value)
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = cancelApprovedSheetOfProductionChange()
async function eliminate(status: number) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ status, id: route.query.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}

const customActiveName = ref('first')
const tableConfig1 = ref({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: false,
  showOperate: false,
  height: 300,
  operateWidth: '100',
  showSort: false,
})
const columnList1 = ref([
  {
    field: 'remark',
    title: '经纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
const columnList2 = ref([
  {
    field: 'remark',
    title: '纬纱组合',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    isWeight: true,
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    isWeight: true,
  },
])
</script>

<template>
  <StatusColumn
    :order_no="state.baseDataForm.order_no"
    :order_id="state.baseDataForm.id"
    :status_name="state.baseDataForm.status_name"
    :status="state.baseDataForm.status"
    permission_wait_key="ProductionChange_wait"
    permission_reject_key="ProductionChange_reject"
    permission_pass_key="ProductionChange_pass"
    permission_cancel_key="ProductionChange_cancel"
    permission_edit_key="ProductionChange_edit"
    edit_router_name="ProductionChangeEdit"
    @cancel="cancel"
    @audit="audit"
    @reject="reject"
    @eliminate="eliminate"
  />
  <FildCard title="基础信息" class="mt-[5px]" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem
        v-for="(item, index) in form_options"
        :key="index"
        :label="`${item.text}:`"
        :copies="item.copies || 1"
      >
        <template #content>
          <el-link v-if="item.order_no" @click="jumpPlanDetail(state.baseDataForm.production_notify_order_id, 1)">
            {{ state.baseDataForm[item.key] }}
          </el-link>
          <el-link
            v-else-if="item.pro_plan_order_no"
            @click="jumpPlanDetail(state.baseDataForm.production_plan_order_id, 2)"
          >
            {{ state.baseDataForm[item.key] }}
          </el-link>
          <span v-else>{{ state.baseDataForm[item.key] }}</span>
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <Table :config="columnList_fabic_config" :table-list="state.greyList" :column-list="columnList_fabic">
      <!-- 布匹定重 -->
      <template #weight_of_fabric="{ row }">
        {{ formatWeightDiv(row.weight_of_fabric) }}{{ row.unit_name }}
      </template>
      <!-- 加工单价 -->
      <template #process_price="{ row }">
        {{ formatUnitPriceDiv(row.process_price) }}{{ row.unit_name ? `元/${row.unit_name}` : '' }}
      </template>
      <!-- 变更匹数 -->
      <template #change_roll="{ row }">
        <span v-if="row.change_roll > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_roll }}</span>
        <span v-else-if="row.change_roll < 0" class="negative"> <el-icon><Bottom /></el-icon> {{ Math.abs(row?.change_roll) }}</span>
        <span v-else>0</span>
      </template>
      <!-- 变更数量 -->
      <template #change_weight="{ row }">
        <span v-if="row.change_weight > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_weight }}</span>
        <span v-else-if="row.change_weight < 0" class="negative"> <el-icon><Bottom /></el-icon> {{ Math.abs(row?.change_weight) }}</span>
        <span v-else>0</span>
        {{ row.unit_name }}
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
      <template #finish_product_width="{ row }">
        {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
      </template>
      <template #finish_product_gram_weight="{ row }">
        {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <FildCard title="用料比例" class="mt-[5px]">
    <Table :config="columnList_master_config" :table-list="state.masterList" :column-list="columnList_master">
      <!-- 用纱比例 -->
      <template #yarn_ratio="{ row }">
        {{ row.yarn_ratio }}%
      </template>
      <!-- 用纱损耗 -->
      <template #yarn_loss="{ row }">
        {{ row.yarn_loss }}%
      </template>
      <!-- 用纱量 -->
      <template #change_use_yarn_quantity="{ row }">
        <span v-if="row.change_use_yarn_quantity > 0" class="positive_number">
          <el-icon><Top /></el-icon>
          {{ row?.change_use_yarn_quantity }}{{ row.unit_name }}
        </span>
        <span v-else-if="row.change_use_yarn_quantity < 0" class="negative">
          <el-icon><Bottom /></el-icon>
          {{ Math.abs(row?.change_use_yarn_quantity) }}
          {{ row.unit_name }}</span>
        <span v-else>0{{ row.unit_name }}</span>
      </template>
      <!-- 发纱量 -->
      <template #change_send_yarn_quantity="{ row }">
        <span v-if="row.change_send_yarn_quantity > 0" class="positive_number"><el-icon><Top /></el-icon>{{ row?.change_send_yarn_quantity }}{{ row.unit_name }}</span>
        <span v-else-if="row.change_send_yarn_quantity < 0" class="negative">
          <el-icon><Bottom /></el-icon>
          {{ Math.abs(row?.change_send_yarn_quantity) }}
          {{ row.unit_name }}
        </span>
        <span v-else>0{{ row.unit_name }}</span>
      </template>
      <!-- 织厂出料 -->
      <template #mill_private_yarn="{ row }">
        <el-checkbox v-model="row.mill_private_yarn" disabled />
      </template>
    </Table>
  </FildCard>
  <FildCard title="工艺要求" class="mt-[5px]" :tool-bar="false">
    <el-tabs v-model="customActiveName" class="demo-tabs">
      <el-tab-pane label="织造工艺" name="first">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem
            v-for="(item, index) in required_options"
            :key="index"
            :label="`${item.text}:`"
            :copies="item.copies || 1"
          >
            <template #content>
              {{ state.technologicalRequirementForm[item.key] }}
            </template>
          </DescriptionsFormItem>
        </div>
      </el-tab-pane>
      <el-tab-pane label="梭织工艺" name="second">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="经密:">
            <template #content>
              {{ formatPriceDiv(data.warp_density) }}根/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纬密:">
            <template #content>
              {{ formatPriceDiv(data.weft_density) }}根/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="钢筘内幅:">
            <template #content>
              {{ formatPriceDiv(data.reed_inner_width) }}cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="钢筘边幅:">
            <template #content>
              {{ formatPriceDiv(data.reed_outer_width) }}cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="筘号:">
            <template #content>
              {{ formatPriceDiv(data.reed_no) }}齿/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="穿入数:">
            <template #content>
              {{ formatPriceDiv(data.penetration_number) }}穿
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="上机纬密:">
            <template #content>
              {{ formatPriceDiv(data.upper_weft_density) }}牙
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="下机纬密:">
            <template #content>
              {{ formatPriceDiv(data.lower_weft_density) }}根/cm
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="理论坯布克重:">
            <template #content>
              {{ formatPriceDiv(data.gf_theory_gram_width) }}g/m
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="总经根数:">
            <template #content>
              {{ formatPriceDiv(data.total_warp_pieces) }}根
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="经纱排列:" copies="2">
            <template #content>
              {{ data.warp_arrangement }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纬纱排列:" copies="2">
            <template #content>
              {{ data.weft_arrangement }}
            </template>
          </DescriptionsFormItem>
        </div>
        <Table :config="tableConfig1" :table-list="data.warp_datas" :column-list="columnList1" />
        <Table :config="tableConfig1" :table-list="data.weft_datas" :column-list="columnList2" />
      </el-tab-pane>
    </el-tabs>
  </FildCard>
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
      min-width: 100px;
      text-align: right;
    }
  }
}

.flex_center {
  display: flex;
  justify-content: center;
  margin: 20px 0 10px;
}

.el-link {
  color: #0e7eff;
}
.positive_number {
  color: green;
}

.negative {
  color: red;
}
</style>
