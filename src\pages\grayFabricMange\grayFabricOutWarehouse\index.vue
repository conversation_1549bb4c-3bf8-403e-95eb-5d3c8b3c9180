<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import { debounce, deepClone, getFilterData } from '@/common/util'
import { processDataOut } from '@/common/handBinary'
import { formatDate, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { getRouterByMeta } from '@/router/routerUtils'
import { GetGfmWarehouseInOutOrderList, GetGfmWarehouseInOutOrderListExport } from '@/api/warehouse'
import SelectComponents from '@/components/SelectComponents/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    date: [dayjs().startOf('month').toString(), dayjs().endOf('month').toString()],
    source_id: '',
    order_no: '',
    grey_fabric_id: '',
    customer_id: '',
    audit_status: '',
    out_unit_id: '',
    consignee: '',
    yarn_batch: '',
    machine_number: '',
    volume_number: '',
    warehouse_in_type: [],
  },
  multipleSelection: [],
  information: false,
})

const { fetchData, data, success, msg, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetGfmWarehouseInOutOrderList()

const tableConfig = computed(() => ({
  fieldApiKey: 'GrayFabricOutWarehouse',
  loading: loading.value,
  showPagition: true,
  showSlotNums: true,
  page: page.value,
  size: size.value,
  total: total.value,
  height: '100%',
  showCheckBox: false,
  showOperate: true,
  operateWidth: '100',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  footerMethod: (val: any) => FooterMethod(val),
}))

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`
      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`
      if (['total_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'total_price') as any)}`
      return null
    }),
  ]
}

async function getData() {
  await fetchData(getFilterData({
    page: page.value,
    size: size.value,
    start_time: formatDate(state.filterData.date[0]),
    end_time: formatDate(state.filterData.date[1]),
    order_no: state.filterData.order_no,
    grey_fabric_id: state.filterData.grey_fabric_id,
    machine_number: state.filterData.machine_number,
    yarn_batch: state.filterData.yarn_batch,
    query_str: state.filterData.volume_number,
    type: state.filterData.warehouse_in_type?.join?.(','),
    resource_unit_id: state.filterData.source_id,
    receive_unit_id: state.filterData.consignee,
    default_in: false, // 默认查询出仓数据
  }))
  if (!success.value)
    return ElMessage.error(msg.value)
}

watch(
  () => state.filterData,
  debounce(getData, 300),
  {
    deep: true,
  },
)

onMounted(() => {
  getData()
})
const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '出仓单号',
    fixed: 'left',
    minWidth: 150,
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'warehouse_in_time',
    title: '出仓日期',
    fixed: 'left',
    is_date: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'type_name',
    title: '进出类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'resource_unit_name',
    title: '货源单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receive_unit_name',
    title: '出货单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'needle_size',
    title: '针寸数',
    minWidth: 120,
  },
  {
    field: 'machine_number',
    title: '机台号',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'gray_fabric_color_name',
    title: '坯布颜色',
    sortable: true,
    minWidth: 150,
  },
  // {
  //   field: 'width_and_wight',
  //   title: '幅宽克重',
  //   sortable: true,
  //   minWidth: 150,
  // },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'roll',
    title: '匹数',
    sortable: true,
    isPrice: true,
    minWidth: 150,
  },
  {
    field: 'weight',
    title: '数量',
    isWeight: true,
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'single_price',
    title: '单价',
    isUnitPrice: true,
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'total_price',
    title: '金额',
    sortable: true,
    isPrice: true,
    minWidth: 150,
  },
  {
    field: 'store_keeper_name',
    title: '仓管员',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'remark',
    title: '备注',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'creator_name',
    title: '制单人',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'create_time',
    title: '制单时间',
    sortable: true,
    minWidth: 150,
    is_date: true,
  },
  {
    field: 'auditor_name',
    title: '审核人',
    sortable: true,
    minWidth: 150,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    sortable: true,
    minWidth: 150,
    is_date: true,
  },
])
const router = useRouter()
function handleDetail(row: Api.GetGfmWarehouseInOutOrderList.GreyFabricManageGfmWarehouseInOutOrderItem) {
  const matchRoute = getRouterByMeta('sourceWarehouseType', row.type)
  if (!matchRoute?.name)
    return
  const routerTemp: any = {
    name: matchRoute.name,
  }
  matchRoute.path.includes('/:id') ? routerTemp.params = { id: row?.order_id } : routerTemp.query = { id: row?.order_id }
  router.push(routerTemp)
}
const AddXimaDialogRef = ref()
function handleSeeDetail(row: Api.GetGfmWarehouseInOutOrderList.GreyFabricManageGfmWarehouseInOutOrderItem) {
  AddXimaDialogRef.value.state.showModal = true
  let listData = deepClone(row?.fc_list?.map((item: Api.GetGfmWarehouseInOutOrderList.GreyFabricManageGfmWarehouseInOutOrderFCItem) => {
    item.position = item.warehouse_bin_name
    return item
  }) || [])
  listData = processDataOut(listData)
  AddXimaDialogRef.value.state.tableData = listData || []
  AddXimaDialogRef.value.state.canEnter = formatPriceDiv(row.roll!)
  AddXimaDialogRef.value.state.horsepower = formatPriceDiv(row.roll!)
  AddXimaDialogRef.value.state.weightOfFabric = formatWeightDiv(row.weight_of_fabric!)
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.type = row.type
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.reference_weight = formatWeightDiv(row.weight!)
  AddXimaDialogRef.value.state.isDisabled = true
}
// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '坯布出仓表'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = GetGfmWarehouseInOutOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
    default_in: false,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const componentRemoteSearch = reactive({
  name: '',
  queryStr: '',
  customer_name: '',
  consignee: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})
function handleChangeGreyFabric(queryString: string) {
  componentRemoteSearch.queryStr = queryString
}
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <FildCard is-collapse default-open :tool-bar="false" title="条件筛选">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="出仓日期">
          <template #content>
            <SelectDate v-model="state.filterData.date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="接收单位">
          <template #content>
            <SelectComponents
              v-model="state.filterData.consignee"
              placeholder="请选择接收单位"
              remote
              :remote-method="(name: string) => componentRemoteSearch.consignee = name"
              remote-show-suffix
              :query="{
                key: '1', // 为了内部生成不同的key，避免第二个相同的GetBusinessUnitListApi的SelectComponents不生效
                name: componentRemoteSearch.consignee,
              }"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="货源单位">
          <template #content>
            <SelectComponents
              v-model="state.filterData.source_id"
              placeholder="请选择货源单位"
              remote
              :remote-method="(name: string) => componentRemoteSearch.unit_name = name"
              remote-show-suffix
              :query="{
                key: '2',
                name: componentRemoteSearch.unit_name,
              }"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称">
          <template #content>
            <SelectComponents
              v-model="state.filterData.grey_fabric_id"
              placeholder="请选择坯布名称"
              show-slot
              remote
              :remote-method="handleChangeGreyFabric"
              remote-show-suffix
              :query="{
                query_str: componentRemoteSearch.queryStr,
              }"
              solt-right-label-field="code"
              solt-left-label-field="name"
              api="GetGreyFabricInfoListUseByOthers"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="纱批">
          <template #content>
            <el-input v-model="state.filterData.yarn_batch" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="机台号">
          <template #content>
            <el-input v-model="state.filterData.machine_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出仓单号">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="卷号（条码）">
          <template #content>
            <el-input v-model="state.filterData.volume_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出仓类型">
          <template #content>
            <SelectComponents
              v-model="state.filterData.warehouse_in_type"
              placeholder="请选择进仓类型"
              api="GetGfmWarehouseOutTypeEnum"
              label-field="name"
              multiple
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard
      title=""
      class="mt-[5px] flex flex-col h-full overflow-hidden"
    >
      <template #right-top>
        <BottonExcel
          v-has="'GreyFabricSales_export'"
          :loading="loadingExcel"
          title="导出"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="handleDetail(row)"
          >
            {{ row.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-button
            v-has="'GreyFabricSales_detail'"
            type="primary"
            text
            link
            @click="handleSeeDetail(row)"
          >
            查看布号
          </el-button>
        </template>
      </Table>
    </FildCard>
    <AddXimaDialog ref="AddXimaDialogRef" />
  </div>
</template>

<style scoped>

</style>
