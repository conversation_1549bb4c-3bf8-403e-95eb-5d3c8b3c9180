<script lang="ts" setup name="DyeingSchedule">
import {
  computed,
  nextTick,
  reactive,
  ref,
  watch,
} from 'vue'
import { createColumnList } from '../common/rechargeColumn'
import {
  debounce,
  deepClone,
  getFilterData,
} from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import GridTable from '@/components/GridTable/index.vue'
import { processDataOut } from '@/common/handBinary'
import { GetRechargeHistoryList } from '@/api'

interface Props {
  row: any
}
const props = withDefaults(defineProps<Props>(), {
  row: {},
})
const showModal = defineModel({
  default: false,
})
const state = reactive<any>({
  filterData: {},
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = GetRechargeHistoryList()

const TableRef = ref<InstanceType<typeof GridTable> | null>()
// 获取数据
const getQuery = function () {
  const query = deepClone(state.filterData)
  query.id = props.row?.id
  return query
}
const getData = debounce(async () => {
  const query = getQuery()
  await ApiCustomerList(getFilterData(query))
}, 400)

const resultData = ref([])
watch(() => data.value.list, () => {
  resultData.value = data.value?.list?.map((item: any) => {
    return processDataOut(item)
  })
  nextTick(() => {
    TableRef.value?.TableRef?.loadData(resultData.value)
  })
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

watch(showModal, (val) => {
  val && getData()
})

const tableConfig = ref({
  showSeq: true,
  fieldApiKey: 'RechargeColorCardRecordDialog',
  loading,
  showPagition: true,
  showSlotNums: true,
  operateWidth: '140',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

const elPaginationConfig = computed(() => ({
  defaultPageSize: 500,
  page: page.value,
  pageSizes: [50, 100, 500, 1000],
  size: size.value,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: total.value,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))

const columnList = createColumnList()
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="充值记录" width="70vw" height="80vh" :mask="false" :esc-closable="true" resize>
    <FildCard title="" class="h-full flex flex-col">
      <GridTable
        ref="TableRef"
        :el-pagination-config="elPaginationConfig"
        :columns="columnList"
        :data="data?.list"
        :config="tableConfig"
        show-pagition
        height="90%"
      />
    </FildCard>
  </vxe-modal>
</template>

<style></style>
