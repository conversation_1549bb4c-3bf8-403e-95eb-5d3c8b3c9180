import { ref } from 'vue'

export const columnList = ref([
  {
    field: 'A-1',
    title: '',
    fixed: 'left',
    childrenList: [
      {
        field: 'product_code',
        title: '成品信息',
        minWidth: 100,
        fixed: 'left',
        soltName: 'product_code',
      },
      // {
      //   field: 'product_name',
      //   title: '成品名称',
      //   minWidth: 100,
      //   fixed: 'left',
      // },
      {
        field: 'product_color_code',
        title: '色号颜色',
        minWidth: 100,
        fixed: 'left',
        soltName: 'product_color_code',
      },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
        fixed: 'left',
      },
      // {
      //   field: 'product_color_name',
      //   title: '颜色',
      //   minWidth: 60,
      //   fixed: 'left',
      // },
    ],
  },
  {
    field: 'A',
    title: '',
    childrenList: [
      {
        field: 'customer_account_num',
        title: '款号',
        minWidth: 100,
        soltName: 'customer_account_num',
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        minWidth: 80,
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        minWidth: 100,
      },
      {
        field: 'product_level_name',
        title: '成品等级',
        minWidth: 100,
      },
      {
        field: 'product_remark',
        title: '成品备注',
        minWidth: 100,
      },
      {
        field: 'stock_remark',
        title: '库存备注',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 70,
      },
    ],
  },
  {
    title: '结算单位',
    field: 'J',
    fixed: 'left',
    childrenList: [
      {
        field: 'auxiliary_unit_id',
        title: '结算单位',
        width: 100,
        soltName: 'auxiliary_unit_id',
      },
    ],
  },
  {
    field: 'B',
    title: '数量单价',
    childrenList: [
      {
        field: 'roll',
        title: '匹数',
        width: 90,
        soltName: 'roll',
        required: true,
      },
      {
        field: 'weight',
        title: '数量',
        width: 100,
        soltName: 'weight',
        // required: true,
      },
      {
        field: 'standard_sale_price',
        title: '销售报价',
        width: 90,
        // isUnitPrice: true,
        soltName: 'standard_sale_price',
      },
      // {
      //   field: 'sale_level_name',
      //   title: '优惠等级',
      //   width: 90,
      // },
      // {
      //   field: 'offset_sale_price',
      //   title: '优惠单价',
      //   width: 100,
      //   isUnitPrice: true,
      // },
      {
        field: 'sale_price',
        title: '数量单价',
        width: 80,
        soltName: 'sale_price',
      },
      {
        field: 'quantity_price',
        title: '上次价',
        width: 80,
        isUnitPrice: true,
        soltName: 'quantity_price',
      },
      // {
      //   field: 'quantity_price_fluctuation',
      //   title: '价格浮动',
      //   width: 100,
      //   soltName: 'quantity_price_fluctuation',
      // },
      {
        field: 'weight_error',
        title: '标准空差',
        width: 75,
        isWeight: true,
      },
      {
        field: 'offset_weight_error',
        title: '优惠空差',
        width: 75,
      },
      {
        field: 'adjust_weight_error',
        title: '调整空差',
        width: 80,
        soltName: 'adjust_weight_error',
      },
      {
        field: 'settle_weight_error',
        title: '结算空差',
        width: 75,
      },
    ],
  },
  {
    field: 'C',
    title: '辅助数量单价',
    childrenList: [
      {
        field: 'length',
        title: '辅助数量',
        width: 80,
        soltName: 'length',
      },
      {
        field: 'standard_length_cut_sale_price',
        title: '销售报价',
        width: 90,
        soltName: 'standard_length_cut_sale_price',
        // isUnitPrice: true,
      },
      // {
      //   field: 'offset_length_cut_sale_price',
      //   title: '优惠单价',
      //   width: 100,
      //   isUnitPrice: true,
      // },
      {
        field: 'length_cut_sale_price',
        title: '辅助数量单价',
        width: 100,
        soltName: 'length_cut_sale_price',
      },
      {
        field: 'length_price',
        title: '上次价',
        width: 80,
        isUnitPrice: true,
        soltName: 'length_price',
      },
      // {
      //   field: 'length_price_fluctuation',
      //   title: '价格浮动',
      //   width: 100,
      //   soltName: 'length_price_fluctuation',
      // },
    ],
  },
  {
    field: 'D',
    title: '其他金额',
    childrenList: [
      {
        field: 'other_price',
        title: '其他金额',
        width: 80,
        soltName: 'other_price',
      },
    ],
  },
  {
    field: 'E',
    title: '库存信息',
    childrenList: [
      {
        field: 'warehouse_name',
        title: '出货仓库',
        width: 100,
      },
      {
        field: 'stock_roll',
        title: '可用匹数',
        width: 75,
        isPrice: true,
      },
      {
        field: 'stock_weight',
        title: '可用数量',
        width: 75,
        isWeight: true,
      },
      {
        field: 'book_roll',
        title: '预约匹数',
        width: 75,
        soltName: 'book_roll',
        required: true,
      },
    ],
  },
  // {
  //   field: 'G',
  //   title: '采购信息',
  //   childrenList: [
  //     {
  //       field: 'purchase_roll',
  //       title: '匹数',
  //       width: 100,
  //       soltName: 'purchase_roll',
  //     },
  //     {
  //       field: 'purchase_weight',
  //       title: '数量',
  //       width: 100,
  //       soltName: 'purchase_weight',
  //     },
  //     {
  //       field: 'purchase_length',
  //       title: '辅助数量',
  //       width: 100,
  //       soltName: 'purchase_length',
  //     },
  //   ],
  // },
  {
    field: 'H',
    title: '欠货信息',
    childrenList: [
      {
        field: 'shortage_roll',
        title: '匹数',
        width: 75,
        soltName: 'shortage_roll',
      },
      // {
      //   field: 'shortage_weight',
      //   title: '数量',
      //   width: 100,
      //   soltName: 'shortage_weight',
      // },
      // {
      //   field: 'shortage_length',
      //   title: '辅助数量',
      //   width: 100,
      //   soltName: 'shortage_length',
      // },
    ],
  },
  {
    field: 'I',
    title: '其他信息',
    childrenList: [
      {
        field: 'customer_account_num',
        title: '款号',
        width: 75,
        soltName: 'customer_account_num',
      },
      {
        field: 'product_color_kind_name',
        title: '颜色类别',
        width: 75,
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 75,
      },
      {
        field: 'remark',
        title: '备注',
        width: 100,
        soltName: 'remark',
      },
    ],
  },
])
