<script lang="ts" setup name="DyeingSchedule">
import {
  computed,
  nextTick,
  reactive,
  ref,
  watch,
} from 'vue'
import { createColumnList } from '../common/consumeColumn'
import {
  situation_list,
} from '@/api/dyeingSchedule'
import {
  formatDate,
} from '@/common/format'
import {
  debounce,
  deepClone,
  getFilterData,
} from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import GridTable from '@/components/GridTable/index.vue'
import { processDataOut } from '@/common/handBinary'

const showModal = defineModel({
  default: false,
})
const state = reactive<any>({
  filterData: {
    order_no: '',
    status: '',
    dyelot: '',
    dye_factory_id: '',
    finish_product_id: '',
    color_id: '',
    product_color_id: '',
    devierDate: '',
    finish_status: '',
    situ_id: '',
  },
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = situation_list()

const TableRef = ref<InstanceType<typeof GridTable> | null>()
// 获取数据
const getQuery = function () {
  const obj = deepClone(state.filterData)
  if (state.filterData.status.length)
    obj.status = obj.status.join(',')

  const query: any = {
    dnf_start_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[0])
        : '',
    dnf_end_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[1])
        : '',
    ...state.filterData,
    status: obj.status,
  }
  delete query.audit_time
  delete query.create_time
  delete query.devierDate
  return query
}
const getData = debounce(async () => {
  const query = getQuery()
  await ApiCustomerList(getFilterData(query))
}, 400)

const resultData = ref([])
watch(() => data.value.list, () => {
  resultData.value = data.value?.list?.map((item: any) => {
    return processDataOut(item)
  })
  nextTick(() => {
    TableRef.value?.TableRef?.loadData(resultData.value)
  })
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

watch(showModal, (val) => {
  val && getData()
})

const tableConfig = ref({
  showSeq: true,
  fieldApiKey: 'DyeingSchedule',
  loading,
  showPagition: true,
  showSlotNums: true,
  operateWidth: '140',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

const elPaginationConfig = computed(() => ({
  defaultPageSize: 500,
  page: page.value,
  pageSizes: [50, 100, 500, 1000],
  size: size.value,
  pageLayout: 'total, sizes, prev, pager, next, jumper',
  total: total.value,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))

const columnList = createColumnList()
</script>

<template>
  <vxe-modal v-model="showModal" show-footer title="消耗记录" width="70vw" height="80vh" :mask="false" :esc-closable="true" resize>
    <FildCard title="" class="h-full flex flex-col">
      <GridTable
        ref="TableRef"
        :el-pagination-config="elPaginationConfig"
        :columns="columnList"
        :data="data?.list"
        :config="tableConfig"
        show-pagition
        height="90%"
      />
    </FildCard>
  </vxe-modal>
</template>

<style></style>
