import { useRequest } from '@/use/useRequest'

// 获取快速排产单列表
export function GetQuickScheduleProductionList() {
  return useRequest<Api.QuickScheduleProduction.Request, ResponseList<Api.QuickScheduleProduction.Response>>({
    url: '/admin/v1/produce/productionScheduleOrder/getWeaveAndMachineGroupList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 更新停机状态
export function UpdateMachineStopStatus() {
  return useRequest<Api.UpdateMachineStopStatus.Request, Api.UpdateMachineStopStatus.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/updateMachineStopStatus',
    method: 'put',
  })
}

