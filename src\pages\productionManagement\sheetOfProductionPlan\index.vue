<script setup lang="ts" name="SheetOfProductionPlan">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { byIdSheetOfProductionPlanGreyInfo, cancelApprovedSheetOfProductionPlan, checkSheetOfProductionPlan, getSheetOfProductionPlanList } from '@/api/sheetOfProductionPlan'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
// import BottonExcel from '@/components/BottonExcel/index.vue'
import { formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { sumNum } from '@/util/tableFooterCount'
import { BusinessUnitIdEnum } from '@/common/enum'
import SelectDialog from '@/components/SelectDialog/index.vue'

const router = useRouter()
const state = reactive<any>({
  tableData: [],
  filterData: {
    order_no: '',
    sale_plan_order_no: '',
    biz_unit_id: '',
    status: [],
  },
  greyList: [],
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data: datalist, total, loading, page, size, handleSizeChange, handleCurrentChange, success, msg }: any = getSheetOfProductionPlanList()

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

// 获取数据
const getData = debounce(async () => {
  await ApiCustomerList(
    getFilterData({
      ...state.filterData,
      status: state.filterData.status.join(','),
    }),
  )
  if (datalist.value?.list)
    handleSearchGreyInfo(datalist.value.list[0].id)
  if (!success.value)
    ElMessage.error(msg.value)
}, 400)
// 首次加载数据
onMounted(() => {
  getData()
})
// 实时过滤数据
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)
// 生产计划单表格列配置
const tableConfig = ref({
  fieldApiKey: 'SheetOfProductionPlan_A',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
// 坯布信息表格配置
const columnList_fabic_config = ref({
  fieldApiKey: 'SheetOfProductionPlan_B',
  showSlotNums: false,
  height: '100%',
  footerMethod: (val: any) => FooterMethod(val),
})
function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['plan_roll'].includes(column.field))
        return sumNum(data, 'plan_roll', '匹')

      if (['plan_weight'].includes(column.field))
        return sumNum(data, 'plan_weight', 'kg', 'float')

      if (['use_stock_roll'].includes(column.field))
        return sumNum(data, 'use_stock_roll', '匹')

      return null
    }),
  ]
  return footerData
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}
// 生产计划单表格列配置
const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    soltName: 'order_no',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'biz_unit_name',
    title: '织厂名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'biz_unit_order_follower_name',
    title: '织厂跟单',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'biz_unit_order_follower_phone',
    title: '跟单电话',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receipt_grey_fabric_date',
    title: '交坯日期',
    fixed: 'left',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'receipt_grey_fabric_address',
    title: '收坯地址',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '审核时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    soltName: 'status',
    fixed: 'right',
    showOrder_status: true,
    width: '5%',
  },
])
// 坯布信息表格列配置
const columnList_fabic = ref([
  {
    sortable: true,
    field: 'sale_plan_order_no',
    title: '销售计划单号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'process_price',
    title: '加工单价',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'production_plan_type_name',
    title: '计划类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'plan_roll',
    title: '计划匹数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'plan_weight',
    soltName: 'plan_weight',
    title: '计划数量',
    minWidth: 100,
    fixed: 'right',
  },
  {
    sortable: true,
    field: 'use_stock_roll',
    title: '调库存匹数',
    minWidth: 100,
    fixed: 'right',
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 100,
    fixed: 'right',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
// const loadingExcel = ref(false)
// const handleExport = async () => {
// if (datalist.value?.list?.length < 1) return ElMessage.warning('当前无数据可导出')
// const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSheetOfProductionPlanList()
// loadingExcel.value = true
// await getFetch({
//   ...getFilterData(state.filterData),
//   download: 1,
// })
// if (getSuccess.value) {
//   exportExcel()
//   ElMessage({
//     type: 'success',
//     message: '成功',
//   })
// } else {
//   ElMessage({
//     type: 'error',
//     message: getMsg.value,
//   })
// }
// loadingExcel.value = false
// }
const tableRef = ref<any>()
// 新建
function handleAdd() {
  router.push({ name: 'SheetOfProductionPlanAdd' })
}
// 查看
function handDetail(row: any) {
  router.push({
    name: 'SheetOfProductionPlanDetail',
    query: { id: row?.id },
  })
}
// 编辑
function handEdit(row: any) {
  router.push({
    name: 'SheetOfProductionPlanEdit',
    query: { id: row?.id },
  })
}
// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = checkSheetOfProductionPlan()
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 驳回
// const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = rejectSheetOfProductionPlan()
// const handReject = async (row: any) => {
//   const res = await deleteToast('确认驳回嘛？')
//   if (res) {
//     await rejectFetch({ status: 3, id: row.id.toString() })
//     if (rejectSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(rejectMsg.value)
//     }
//   }
// }
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = cancelApprovedSheetOfProductionPlan()
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 获取坯布信息
const { fetchData: searchGrayFetch, data, success: searchGraySuccess, msg: searchGraylMsg } = byIdSheetOfProductionPlanGreyInfo()
async function handleSearchGreyInfo(id: number) {
  await searchGrayFetch({ id })

  if (searchGraySuccess.value) {
    state.greyList = data.value?.list?.map((item: any) => {
      return {
        ...item,
        production_plan_type_name: item.production_plan_type_name.join(','),
        finish_product_gram_weight: item.finish_product_gram_weight,
        grey_fabric_gram_weight: item.grey_fabric_gram_weight,
        plan_roll: formatTwoDecimalsDiv(Number(item.plan_roll)),
        use_stock_roll: formatTwoDecimalsDiv(Number(item.use_stock_roll)),
        weight_of_fabric: `${formatWeightDiv(Number(item.weight_of_fabric))}kg`,
        plan_weight: formatWeightDiv(Number(item.plan_weight)),
        process_price: `${formatUnitPriceDiv(Number(item.process_price))}元/kg`,
      }
    })
  }
  else {
    ElMessage.error(searchGraylMsg.value)
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售计划单号:">
          <template #content>
            <el-input v-model="state.filterData.sale_plan_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.biz_unit_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.unit_name }"
              api="GetBusinessUnitListApi"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents v-model="state.filterData.status" multiple api="getAuditStatusEnums" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <!-- <BottonExcel :loading="loadingExcel" @onClickExcel="handleExport" title="导出文件"></BottonExcel> -->
        <el-button v-has="'SheetOfProductionPlan_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
      </template>
      <Table ref="tableRef" :config="tableConfig" :table-list="datalist?.list || []" :column-list="columnList">
        <template #order_no="{ row }">
          <el-link type="primary" @click="handleSearchGreyInfo(row.id)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'SheetOfProductionPlan_detail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="row.status !== 2 && row.status !== 4" v-has="'SheetOfProductionPlan_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.status === 1" v-has="'SheetOfProductionPlan_pass'" type="primary" :underline="false" @click="handAudit(row)">
              审核
            </el-link>
            <!-- <el-link v-if="row.status === 1" text="danger" type="danger" @click="handReject(row)">驳回</el-link> -->
            <el-link v-if="row.status === 2" v-has="'SheetOfProductionPlan_wait'" type="primary" :underline="false" @click="handApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table :config="columnList_fabic_config" :table-list="state?.greyList" :column-list="columnList_fabic">
        <template #plan_weight="{ row }">
          {{ row.plan_weight }}kg
        </template>
        <template #grey_fabric_width="{ row }">
          {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
        </template>
        <template #grey_fabric_gram_weight="{ row }">
          {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
        </template>
        <template #finish_product_width="{ row }">
          {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
        </template>
        <template #finish_product_gram_weight="{ row }">
          {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

::v-deep(.el-button.el-button--danger.is-text) {
  padding: 0;
  font-size: 14px;
}

.el-link {
  color: #0e7eff;
}
</style>
