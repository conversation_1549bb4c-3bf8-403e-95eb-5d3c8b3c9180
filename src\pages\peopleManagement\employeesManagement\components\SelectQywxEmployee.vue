<script setup lang="ts">
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import BindQywxEmployee from './BindQywxEmployee.vue'

interface Props {
  modelValue: Api.GetQywxUsers.QywxGetQYWXUsersResponseItem[]
}

const props = defineProps<Props>()

const emit = defineEmits(['update:modelValue', 'onDelete'])

const bindQywxRef = ref()

// 绑定员工
function bindEmployee() {
  bindQywxRef.value.showModal = true
  bindQywxRef.value.selectedRow = props.modelValue
}

// 处理删除员工
async function handleDeleteEmployee(index: number) {
  try {
    await ElMessageBox.confirm(
      '确认要解除该企微员工绑定吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
    const newEmployees = [...props.modelValue]
    newEmployees.splice(index, 1)
    emit('update:modelValue', newEmployees)
    emit('onDelete', newEmployees)
    // ElMessage.success('解绑成功')
  }
  catch {
    // 用户取消操作
  }
}

// 处理新增员工
function handleEmployeeSure(newEmployees: any) {
  emit('update:modelValue', newEmployees)
}
</script>

<template>
  <div class="qywx-employees">
    <template v-if="modelValue.length">
      <div v-for="(employee, index) in modelValue" :key="index" class="employee-item">
        {{ employee.name }}
        <el-icon class="delete-icon" @click="handleDeleteEmployee(index)">
          <Close />
        </el-icon>
      </div>
    </template>
    <el-link type="primary" :underline="false" @click="bindEmployee">
      选择
    </el-link>
  </div>
  <BindQywxEmployee ref="bindQywxRef" @handle-sure="handleEmployeeSure" />
</template>

<style lang="scss" scoped>
.qywx-employees {
  .employee-item {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 4px;
    margin-right: 8px;
    font-size: 14px;
    background-color: var(--el-fill-color-light);

    .delete-icon {
      margin-left: 4px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      cursor: pointer;

      &:hover {
        color: var(--el-color-danger);
      }
    }
  }

  .el-link {
    margin-left: 8px;
  }
}
</style>
