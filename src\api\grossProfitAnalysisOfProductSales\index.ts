import { useRequest } from '@/use/useRequest'
import { type ResponseList } from '@/api/commonTs'
import {
  type GetGrossProfitGroupColorListData,
  type GetGrossProfitGroupCustomerListData,
  type GetGrossProfitGroupMaterialListData,
  type GetGrossProfitGroupSellerListData,
} from '@/api/grossProfitAnalysisOfProductSales/rules'

// 获取毛利分析（品名）
export const GetGrossProfitGroupMaterialList = () => {
  return useRequest<any, ResponseList<GetGrossProfitGroupMaterialListData>>({
    url: '/admin/v1/should_collect_order/report_forms/getGrossProfitGroupMaterialList',
    method: 'get',
  })
}

// 获取毛利分析（颜色）
export const GetGrossProfitGroupColorList = () => {
  return useRequest<any, ResponseList<GetGrossProfitGroupColorListData>>({
    url: '/admin/v1/should_collect_order/report_forms/getGrossProfitGroupColorList',
    method: 'get',
  })
}

// 获取毛利分析（销售员）
export const GetGrossProfitGroupSellerList = () => {
  return useRequest<any, ResponseList<GetGrossProfitGroupSellerListData>>({
    url: '/admin/v1/should_collect_order/report_forms/GetGrossProfitGroupSellerList',
    method: 'get',
  })
}

// 获取毛利分析（客户）
export const GetGrossProfitGroupCustomerList = () => {
  return useRequest<any, ResponseList<GetGrossProfitGroupCustomerListData>>({
    url: '/admin/v1/should_collect_order/report_forms/getGrossProfitGroupCustomerList',
    method: 'get',
  })
}
