<script setup lang="ts" name="UnderWeightOrder">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { getProductionShortageOrderList, updateProductionShortageOrderStatusPass, updateProductionShortageOrderStatusWait } from '@/api/underWeightOrder'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate } from '@/common/format'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'

const create_time = ref([])
const audit_date = ref([])
const update_time = ref([])
const router = useRouter()
const state = reactive<any>({
  tableData: [],
  filterData: {
    order_no: '',
    sale_system_id: '',
    voucher_number: '',
    receive_unit_id: '',
    auditor_id: '',
    updater_id: '',
    status: [],
  },
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data: datalist, total, loading, page, size, handleSizeChange, handleCurrentChange }: any = getProductionShortageOrderList()

// 获取数据
const getData = debounce(() => {
  const query = {
    ...state.filterData,
  }

  if (query.status.length)
    query.status = query.status.join(',')

  if (create_time?.value?.length) {
    query.begin_create_time = formatDate(create_time.value[0])
    query.end_create_time = formatDate(create_time.value[1])
  }
  if (audit_date?.value?.length) {
    query.begin_audit_date = formatDate(audit_date.value[0])
    query.end_audit_date = formatDate(audit_date.value[1])
  }
  if (update_time?.value?.length) {
    query.begin_update_time = formatDate(update_time.value[0])
    query.end_update_time = formatDate(update_time.value[1])
  }
  ApiCustomerList(getFilterData(query))
}, 400)
// 首次加载数据
onMounted(() => {
  getData()
})
// 实时过滤数据
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)
// 生产计划单表格列配置
const tableConfig = ref({
  fieldApiKey: 'UnderWeightOrder',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}
// 生产计划单表格列配置
const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '8%',
    soltName: 'order_no',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receive_unit_name',
    title: '收货单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'shortage_date',
    title: '欠重日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'remark',
    title: '单据备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    width: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '审核时间',
    width: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    width: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    soltName: 'status',
    fixed: 'right',
    showOrder_status: true,
    width: '5%',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const tableRef = ref<any>()
// 新建
function handleAdd() {
  router.push({ name: 'UnderWeightOrderAdd' })
}
// 查看
function handDetail(row: any) {
  router.push({
    name: 'UnderWeightOrderDetail',
    query: { id: row?.id },
  })
}
// 编辑
function handEdit(row: any) {
  router.push({
    name: 'UnderWeightOrderEdit',
    query: { id: row?.id },
  })
}
// 审核
const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = updateProductionShortageOrderStatusPass()
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = updateProductionShortageOrderStatusWait()
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
function changeDateCreateTime() {
  // create_time.value = [row.date_min, row.date_max]
  getData()
}

function changeDateAuditDate() {
  // audit_date.value = [row.date_min, row.date_max]
  getData()
}
function changeDateUpdateTime() {
  // update_time.value = [row.date_min, row.date_max]
  getData()
}

const unitQuery = computed(() => {
  return {
    sale_system_id: state.filterData.sale_system_id,
    unit_type_id: BusinessUnitIdEnum.knittingFactory,
  }
})

onActivated(getData)
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:" :width="330">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:" :width="330">
          <template #content>
            <SelectComponents v-model="state.filterData.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable @change-value="clearCustomer" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:" :width="330">
          <template #content>
            <el-input v-model="state.filterData.voucher_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货单位:" :width="330">
          <template #content>
            <SelectComponents v-model="state.filterData.receive_unit_id" :query="unitQuery" api="business_unitlist" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建日期:" :width="330">
          <template #content>
            <SelectDate v-model="create_time" style="width: 100%" @change-date="changeDateCreateTime" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核人:" :width="330">
          <template #content>
            <SelectComponents v-model="state.filterData.auditor_id" api="Adminemployeelist" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核日期:" :width="330">
          <template #content>
            <SelectDate v-model="audit_date" style="width: 100%" @change-date="changeDateAuditDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="最后修改人:" :width="330">
          <template #content>
            <SelectComponents v-model="state.filterData.updater_id" api="Adminemployeelist" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="最后修改日期:" :width="330">
          <template #content>
            <SelectDate v-model="update_time" style="width: 100%" @change-date="changeDateUpdateTime" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:" :width="330">
          <template #content>
            <SelectComponents v-model="state.filterData.status" api="getAuditStatusEnums" multiple label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <!-- <BottonExcel :loading="loadingExcel" @onClickExcel="handleExport" title="导出文件"></BottonExcel> -->
        <el-button v-has="'UnderWeightOrder_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
      </template>
      <Table ref="tableRef" :config="tableConfig" :table-list="datalist.list" :column-list="columnList">
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row?.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'UnderWeightOrder_detail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="!(row.status === 2 || row.status === 4)" v-has="'UnderWeightOrder_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.status === 1" v-has="'UnderWeightOrder_pass'" type="primary" :underline="false" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.status === 2" v-has="'UnderWeightOrder_wait'" type="primary" :underline="false" @click="handApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

::v-deep(.el-button.el-button--danger.is-text) {
  padding: 0;
  font-size: 14px;
}

.el-link {
  color: #0e7eff;
}
</style>
