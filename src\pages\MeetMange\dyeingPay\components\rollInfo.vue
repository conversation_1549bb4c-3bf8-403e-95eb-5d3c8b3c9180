<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { sumNum } from '@/util/tableFooterCount'
import { UpdateBuoyantWeightPrice, getFpmProcessInOrder } from '@/api/fpProcessingEntryOrder'
import Table from '@/components/Table.vue'
import { formatHashTag, formatPriceDiv, formatPriceMul, formatUnitPriceMul } from '@/common/format'
import FildCard from '@/components/FildCard.vue'
import { processDataOut } from '@/common/handBinary'
import { DnfChargingMethod } from '@/enum'

export interface Props {
  srcId: number
  dnfChargingMethod: number // 染整收费方式
  // eslint-disable-next-line vue/prop-name-casing
  item_bum_data?: any[]
  parentList?: any[]
  type?: string
}

const props = withDefaults(defineProps<Props>(), {
  parentList: () => [],
  item_bum_data: () => [],
  dnfChargingMethod: 0,
  srcId: 0,
  type: 'add', // add | detail
})
const { fetchData: updateList, loading, success, msg }: any = UpdateBuoyantWeightPrice()

const firstCategory = reactive<any>({
  columnList1: [
    {
      field: 'code',
      title: '收货信息',
      childrenList: [
        {
          field: 'product',
          title: '成品名称',
          minWidth: 100,
        },
        {
          field: 'color',
          title: '色号颜色',
          minWidth: 100,
        },
        {
          field: 'dyelot',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'in_weight',
          title: '本仓进仓数量',
          minWidth: 100,
        },
        {
          field: 'dnf_order_no',
          title: '染整通知单',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'code',
      title: '用坯/用布',
      childrenList: [
        {
          field: 'grey_fabric',
          title: '名称',
          minWidth: 100,
        },
        {
          field: 'grey_fabric_color',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'source_no',
          title: '出坯单号',
          minWidth: 100,
        },
        {
          field: 'source_time',
          title: '出坯日期',
          is_date: true,
          minWidth: 100,
        },
        {
          field: 'source_remark',
          title: '出坯备注',
          minWidth: 100,
        },
        // {
        //   field: 'use_roll_max',
        //   title: '可用库存匹数',
        //   minWidth: 100,
        // },
        // {
        //   field: 'use_weight_max',
        //   title: '可用库存数量',
        //   minWidth: 100,
        // },
        {
          field: 'use_roll',
          soltName: 'use_roll',
          title: '实际用坯/用布匹数',
          minWidth: 100,
        },
        {
          field: 'use_weight',
          soltName: 'use_weight',
          title: '实际用坯/用布数量',
          minWidth: 100,
        },
        {
          field: 'buoyant_weight_price',
          soltName: 'buoyant_weight_price',
          title: '成本单价',
          minWidth: 100,
        },
        {
          field: 'gfm_cost_price',
          soltName: 'gfm_cost_price',
          title: '成本金额',
          minWidth: 100,
        },
      ],
    },
  ],
})

const spanFields = [
  'product',
  'color',
  'dyelot',
  'in_weight',
  'dnf_order_no',
]
const datalist1 = ref([])
const tableConfig2 = computed(() => ({
  showCheckBox: false,
  loading: loading.value,
  showSlotNums: true,
  colspanMethod: ({ row, _rowIndex, column, visibleData }: any) => {
    const cellValue = row[column.field]
    // cellValue为0时，合并单元格
    if (String(cellValue) && spanFields.includes(column.field)) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      if (
        prevRow
        && prevRow[column.field] === cellValue
        && prevRow.srcId === row.srcId
      ) {
        return { rowspan: 0, colspan: 0 }
      }
      else {
        let countRowspan = 1
        while (
          nextRow
          && nextRow[column.field] === cellValue
          && nextRow.srcId === row.srcId
        )
          nextRow = visibleData[++countRowspan + _rowIndex]

        if (countRowspan > 1)
          return { rowspan: countRowspan, colspan: 1 }
      }
    }
  },
  footerMethod: (val: any) => FooterMethod(val),
}))

// const targetGroupItem = ref<DistributableGroupType>({
//   quote_order_id: 0,
//   product_color_id: 0,
//   totalDistributablePieceCount: 0,
//   totalDistributableWeight: 0,
// })

// const targetGroupItem = ref<DistributableGroupType>()

// watch(
//   [() => props.rowQuery.item_id, () => props.group, () => props.rowQuery.id],
//   () => {
//     if (props.rowQuery.id)
//       targetGroupItem.value = props.group.find(item => item.item_id === props.rowQuery.item_id)
//   },
//   {
//     deep: true,
//     immediate: true,
//   },
// )

// watch(
//   () => targetGroupItem.value,
//   (newValue) => {
//     if (!newValue)
//       return
//     setGroup(newValue)
//   },
//   {
//     deep: true,
//   },
// )

// function setGroup(newGroupItem: DistributableGroupType) {
//   const newGroup = props.group.map((item) => {
//     if (item.quote_order_id === props.rowQuery.id && item.product_color_id === props.row.product_color_id)
//       item = newGroupItem
//
//     return item
//   })
//   emits('update:group', newGroup)
// }

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '总计'

      if (['use_roll'].includes(column.field))
        return sumNum(data, column.field, '')

      if (['gfm_cost_price'].includes(column.field))
        return sumNum(data, column.field, '￥')

      if (['use_weight', 'weight'].includes(column.field))
        return sumNum(data, column.field, '')

      if (['piece_count', 'rtn_piece_count', 'rtn_weight', 'use_roll_max', 'use_weight_max'].includes(column.field))
        return sumNum(data, column.field, '')

      return null
    }),
  ]
}

// const row = ref()
// const use_gf_roll = ref(0)
// const use_gf_weight = ref(0)
// const group = ref<DistributableGroupType[]>()
// watch(
//   () => props,
//   (newProps) => {
//     row.value = newProps.row
//     use_gf_roll.value = newProps.row.use_gf_roll
//     use_gf_weight.value = newProps.row.use_gf_weight
//     group.value = newProps.group
//     // setMatchData()
//   },
//   {
//     deep: true,
//     immediate: true,
//   },
// )

// const { exec } = useAutoEnter(datalist2, row, group)
// const { autoEnterInstance } = toRefs(props)
// 可以使用日期的判断方式去自动填入用坯匹数和用坯数量
// const canUseDateEnter = ref(false)

const { fetchData: detailFetch, data: detailData, success: processsInOrderSuccess, msg: processInOrderMsg } = getFpmProcessInOrder()
const detailList = ref([])
async function getData() {
  // 重置数据
  datalist1.value = []
  await detailFetch({ id: props.srcId })
  if (!processsInOrderSuccess.value)
    return ElMessage.error(processInOrderMsg.value)

  const temp = detailData.value.item_data.flatMap((item) => {
    return item.item_bum_data?.map((it) => {
      it.srcId = item.id
      it.dnf_type = item.dnf_type
      return it
    })
  }) || []

  detailList.value = processDataOut(temp.map((item) => {
    if (!item)
      return {}
    const target = props.parentList.find(it => it.src_id === item.srcId)
    item.product = formatHashTag(target.product_code, target.product_name) || ''
    item.color = formatHashTag(target.color_code, target.color)
    item.dnf_order_no = target.dnf_order_no
    item.dyelot = target.dyelot
    item.in_weight = target.total_weight
    item.gfm_cost_price = formatPriceDiv(item.gfm_cost_price)
    return item
  }))
  // for (const item of props.parentList) {
  // const uniqueId = `${item.quote_order_item_id}_${it.id}`
  // const targetBumData = item.item_bum_data?.find(it => it.uniqueId === uniqueId)
  // // if (index > 0) {
  // //   // 如果上一条数据的出坯日期和当前数据的出坯日期不一致，可以使用日期的判断方式去自动填入用坯匹数和用坯数量
  // //   if (it.gf_stock_info.source_time && temp[index - 1].gf_stock_info.source_time && it.gf_stock_info.source_time !== temp[index - 1].gf_stock_info.source_time)
  // //     canUseDateEnter.value = true
  // // }
  // //   // 最大用坯匹数=未返匹数
  // const use_roll_max = formatPriceDiv(it.not_rtn_piece_count) // 未返匹数
  // //   // 最大用坯数量=未返数量
  // const use_weight_max = formatWeightDiv(it.not_rtn_weight) // 未返数量
  // let subName = ''
  // let subColor = ''
  // let source_code = ''
  // let date = ''
  // let source_remark = ''
  // // 染整
  // if (item.dnf_type === DnfType.GreyDnf) {
  //   subName = formatHashTag(it.gf_stock_info.grey_fabric_code, it.gf_stock_info.grey_fabric_name) || ''
  //   subColor = it.gf_stock_info.gray_fabric_color_name
  //   source_code = it.gf_stock_info.source_code
  //   date = it.gf_stock_info.source_time
  //   source_remark = it.gf_stock_info.source_remark
  // }
  // else {
  //   // 成品
  //   subName = formatHashTag(it.f_out_info.fabric_code, it.f_out_info.fabric_name) || ''
  //   subColor = formatHashTag(it.f_out_info.color_code, it.f_out_info.color_name) || ''
  //   source_code = it.f_out_info.order_no
  //   date = it.f_out_info.date
  //   source_remark = it.f_out_info.remark
  // }
  // return {
  //   dnf_type: item.dnf_type, // 用于区分用坯还是用布
  //   product_id: item.product_id,
  //   product: formatHashTag(item.product_code, item.product_name),
  //   color: formatHashTag(item.product_color_code, item.product_color_name),
  //   dyelot: item.dye_factory_dyelot_number,
  //   in_weight: item.in_weight,
  //   subName,
  //   subColor,
  //   source_code,
  //   date,
  //   source_remark,
  //   num: formatTwoDecimalsDiv(it?.gf_stock_info?.num),
  //   // weight: formatWeightDiv(it.gf_stock_info.weight),
  //   dye_order_id: it.id,
  //   parentId: target.id,
  //   piece_count: formatPriceDiv(it.piece_count),
  //   weight: formatWeightDiv(it.weight),
  //   back_use_manage_id: it.id,
  //   uniqueId: `${item.quote_order_item_id}_${it.id}`,
  //   use_roll: targetBumData.use_roll,
  //   use_weight: targetBumData.use_weight,
  //   code: target.code,
  //   name: target.name,
  //   color_name: target.color_name,
  //   color_no: target.color_no,
  //   in_roll: props.rowQuery.in_roll,
  //   num_weight: formatWeightDiv(it?.gf_stock_info?.weight),
  //   use_roll_max, // 最大用坯匹数
  //   use_weight_max, // 最大用坯数量
  //   rtn_piece_count: formatTwoDecimalsDiv(it.rtn_piece_count), // 已返匹数
  //   rtn_weight: formatWeightDiv(it.rtn_weight), // 已返数量
  // }
  // datalist1.value.push(...item.item_bum_data)
  // setMatchData()
  // if (!props.row.isAllocated && props.type === 'add') {
  //   // 执行自动录入
  //   handleAutoEnter()
  // }
  // emits('setItemBumData', datalist1.value)
  // }

  // if (!target)
  //   return ElMessage.info('未找到该成品')
  //
  // if (!targetGroupItem.value)
  //   return ElMessage.info('未找到可分配分组内的成品')
  //
  // datalist1.value = target.use_fabric?.map((item: any) => {
  //   return {
  //     ...item.gf_stock_info,
  //     num: formatTwoDecimalsDiv(item.gf_stock_info.num),
  //     // weight: formatWeightDiv(item.gf_stock_info.weight),
  //     dye_order_id: props.rowQuery.id,
  //     piece_count: formatPriceDiv(item.piece_count),
  //     weight: formatWeightDiv(item.weight),
  //   }
  // })
  //
  // const temp = target.use_fabric
  // datalist2.value = temp?.map((item: any, index: number) => {
  //   if (index > 0) {
  //     // 如果上一条数据的出坯日期和当前数据的出坯日期不一致，可以使用日期的判断方式去自动填入用坯匹数和用坯数量
  //     if (item.gf_stock_info.source_time && temp[index - 1].gf_stock_info.source_time && item.gf_stock_info.source_time !== temp[index - 1].gf_stock_info.source_time)
  //       canUseDateEnter.value = true
  //   }
  //
  //   // 最大用坯匹数=未返匹数
  //   const use_roll_max = formatPriceDiv(item.not_rtn_piece_count) // 未返匹数
  //
  //   // 最大用坯数量=未返数量
  //   const use_weight_max = formatWeightDiv(item.not_rtn_weight) // 未返数量
  //   return {
  //     ...item.gf_stock_info,
  //     back_use_manage_id: item.id,
  //     uniqueId: `${props.row.item_id}_${item.id}`,
  //     parent_id: target.finish_product_id,
  //     use_roll: 0,
  //     use_weight: 0,
  //     code: target.code,
  //     name: target.name,
  //     color_name: target.color_name,
  //     color_no: target.color_no,
  //     in_roll: props.rowQuery.in_roll,
  //     in_weight: props.rowQuery.in_weight,
  //     num: formatTwoDecimalsDiv(item.gf_stock_info.num),
  //     num_weight: formatWeightDiv(item.gf_stock_info.weight),
  //     use_roll_max, // 最大用坯匹数
  //     use_weight_max, // 最大用坯数量
  //     piece_count: formatPriceDiv(item.piece_count), // 染整匹数
  //     weight: formatWeightDiv(item.weight), // 染整数量
  //     dye_order_id: props.rowQuery.id,
  //     rtn_piece_count: formatTwoDecimalsDiv(item.rtn_piece_count), // 已返匹数
  //     rtn_weight: formatWeightDiv(item.rtn_weight), // 已返数量
  //   }
  // })
  //
  // setMatchData()
  //
  // // if (!props.row.isAllocated && props.type === 'add') {
  // //   // 执行自动录入
  // //   handleAutoEnter()
  // // }
  // emits('setItemBumData', datalist2.value)
}

// 录入成品处理好的分配数
// function setMatchData() {
//   if (datalist1.value.length && props.row.item_bum_data.length) {
//     datalist1.value.forEach((item: any) => {
//       props.row.item_bum_data.forEach((it: any) => {
//         if (it.uniqueId === item.uniqueId) {
//           item.use_roll = it.use_roll
//           item.use_weight = it.use_weight
//         }
//       })
//     })
//   }
// }

const tablesRef2 = ref()

// 匹数/数量触发更新
// function setRollAndWeight(row: any) {
//   const rowIndex = props.parentList.findIndex((item) => {
//     return row.parentId === item.quote_order_item_id
//   })
//   const useFabric = datalist1.value.filter((item) => {
//     return props.parentList[rowIndex].quote_order_item_id === item.parentId
//   })
//   emits('setItemBumData', useFabric, rowIndex)
//   updateFooter()
// }

// 更新表格汇总
function updateFooter() {
  tablesRef2.value?.tableRef?.updateFooter()
}

watch(
  () => props.srcId,
  (id) => {
    if (!id)
      return
    // if (newId !== prevId) {
    //   scope.stop()
    // }
    getData()
  },
  { immediate: true },
)
function emitUpdateData(row) {
  detailList.value.forEach((item) => {
    if (row.src_id === item.srcId)
      item.in_weight = [DnfChargingMethod.DnfWeight, DnfChargingMethod.finishProductWeight].includes(props.dnfChargingMethod) ? row.weight : row.length
  })
}
async function handleEditPrice(row) {
  await updateList({
    bum_id: row.id,
    buoyant_weight_price: formatUnitPriceMul(row.buoyant_weight_price),
    gfm_cost_price: formatPriceMul(row.gfm_cost_price),
  })
  if (!success.value)
    ElMessage.error(msg.value)
  getData()
}
defineExpose({
  updateFooter,
  emitUpdateData,
  tableList: datalist1,
})
</script>

<template>
  <FildCard title="" class="" :tool-bar="false">
    <Table ref="tablesRef2" :config="tableConfig2" :table-list="detailList" :column-list="firstCategory.columnList1">
      <template #subName="{ row }">
        {{ formatHashTag(row.code, row.name) }}
      </template>
      <template #source_code="{ row }">
        {{ row.source_code }}
        <!--        <el-link type="primary" :underline="false" @click="handleClickLink(row)"> -->
        <!--          {{ row.source_code }} -->
        <!--        </el-link> -->
      </template>
      <template #subColor="{ row }">
        {{ formatHashTag(row.color_no, row.color_name) }}
      </template>
      <!-- 用坯匹数 -->
      <template #use_roll="{ row }">
        {{ row.use_roll }}
      </template>
      <!-- 用坯数量 -->
      <template #use_weight="{ row }">
        {{ row.use_weight }}
      </template>
      <!-- 用坯匹数 -->
      <template #buoyant_weight_price="{ row }">
        <span v-if="props.type === 'detail'">
          {{ row.buoyant_weight_price }}
        </span>
        <vxe-input v-else v-model="row.buoyant_weight_price" type="float" :min="0" @blur="handleEditPrice(row)" />
      </template>
      <!-- 用坯数量 -->
      <template #gfm_cost_price="{ row }">
        ￥{{ row.gfm_cost_price }}
      </template>
    </Table>
  </FildCard>
</template>

<style lang="scss" scoped></style>
