<script setup lang="ts" name="CustomerEdit">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { nextTick, onActivated, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { throttle } from 'lodash-es'
import type { Contact } from '../components/SelectWechatFriendLink.vue'
import SelectWechatFriendLink from '../components/SelectWechatFriendLink.vue'
import SelectQyGroupLink from '../components/SelectQyGroupLink.vue'
import WeavingConfig from './components/WeavingConfig/index.vue'
import { Business_unitcustomerDetail, Business_unitcustomerPut } from '@/api/customerMange'
import type { Carry } from '@/common/enum'
import { DecimalPoint, EmployeeType, SettleType } from '@/common/enum'
import { formatPriceDiv, formatPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import useRouterList from '@/use/useRouterList'
import { SearchForSomeSupplierField } from '@/api/search'
import CascaderAddress from '@/components/CascaderAddress/index.vue'
import type { AddressFormData } from '@/components/Address/types'
import { AddressForm, List as AddressList } from '@/components/Address'

const routerList = useRouterList() // 导入useRouterList

const { fetchData, data } = Business_unitcustomerDetail()

const route = useRoute() // 实例化路由
const locationStatus = ref(false) // 地区是否选择的状态

function checkSpace(rule: any, value: any, callback: any) { // 校验手机号是否格式正确
  const reg = /^\S.*$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('不允许输入空格'))
}

function checkEmail(rule: any, value: any, callback: any) { // 校验邮箱
  const reg = /^[A-Za-z0-9\u4E00-\u9FA5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  if (reg.test(value) || value === '')
    callback()
  else
    callback(new Error('请输入正确的邮箱地址'))
}

function validateNumber(rule: any, value: any, callback: any) { // 校验结算周期
  if (value <= 0)
    callback(new Error('请输入大于0数字'))
  else
    callback()
}

const formData = ref<any>({ // 需要提交的表单数据
  name: '', // 客户名称
  full_name: '', // 客户全称
  code: '', // 客户编号
  unit_type_id: [], // 客户类型
  sale_system_ids: undefined, // 所属营销体系
  contact_name: '', // 联系人
  phone: '', // 联系电话
  location: [], // 地区（省市区）
  address: '', // 详细地址
  email: '', // 邮箱
  is_blacklist: false, // 是否为黑名单
  credit_code: '', // 社会统一信用代码
  remark: '', // 备注
  seller_id: undefined, // 销售员
  order_follower_id: undefined, // 跟单员
  sale_area_id: undefined, // 销售区域
  sale_group_id: undefined, // 销售群体
  blank_fabric_min: undefined, // 胚布销售最小数量
  blank_fabric_max: undefined, // 胚布销售最大数量
  settle_type: SettleType.cashType, // 结算类型
  settle_cycle: undefined, // 结算周期
  credit_limit: undefined, // 信用额度
  credit_level: undefined, // 信用等级
})

// 织造配置数据
const weavingConfigData = ref({
  fabric_decimal_places: DecimalPoint.DecimalPointTwo as DecimalPoint | number | undefined, // 坯布位数，默认2位小数
  rounding_type: undefined as Carry | number | undefined, // 进位方式
  fabric_weight_min: undefined as string | number | undefined, // 坯布重量最小值
  fabric_weight_max: undefined as string | number | undefined, // 坯布重量最大值
})
const rules = ref<any>({ // 校验规则
  name: [{ required: true, message: '请填写客户名称', trigger: 'blur' }],
  phone: [{ required: true, trigger: 'blur', message: '请填写联系电话' }, { validator: checkSpace }],
  // location: [{ required: true, message: '请选择地区', trigger: 'change' }],
  // address: [{ required: true, message: '请填写地址', trigger: 'blur' }],
  unit_type_id: [{ required: true, message: '请选择客户类型', trigger: 'blur' }],
  sale_system_ids: [{ required: true, message: '请选择所属营销体系', trigger: 'blur' }],
  email: [{ trigger: 'blur', validator: checkEmail }],
})
const addressList = ref<AddressFormData[]>([]) // 地址列表
const dialogVisible = ref(false) // 控制地址弹窗是否弹出
const currentEditData = ref<Partial<AddressFormData>>({}) // 当前编辑的地址数据

// 添加响应式数据
const wechatContacts = ref<Contact[]>([])

const boundQyGroups = ref<Api.CustomerAdd.SystemQYWXGroupChat[]>([])
async function getData() { // 初始化获取数据进行编辑
  locationStatus.value = false
  const result = await fetchData({ id: route.query.id })
  if (result.success) {
    const newAddress = result.data.location ? result.data.location.split(',') : []
    formData.value.name = result.data.name // 客户名称 string
    formData.value.full_name = result.data.full_name // 客户全称 string
    formData.value.code = result.data.code // 客户编号 string
    formData.value.unit_type_id = result.data.unit_type_id // 客户类型 []
    formData.value.sale_system_ids = result.data.sale_system_ids // 所属营销体系
    formData.value.contact_name = result.data.contact_name // 联系人
    formData.value.phone = result.data.phone // 联系电话
    formData.value.location = newAddress // 地区
    formData.value.email = result.data.email // 详细地址
    formData.value.address = result.data.address // 邮箱
    formData.value.is_blacklist = result.data.is_blacklist ? result.data.is_blacklist : false // 是否为黑名单
    formData.value.credit_code = result.data.credit_code // 社会统一信用代码
    formData.value.remark = result.data.remark // 备注
    formData.value.seller_id = result.data.seller_id // 销售员
    formData.value.order_follower_id = result.data.order_follower_id // 跟单员
    formData.value.sale_area_id = result.data.sale_area_id // 销售区域
    formData.value.sale_group_id = result.data.sale_group_id // 销售群体
    formData.value.blank_fabric_min = formatWeightDiv(result.data.blank_fabric_min) // 胚布销售最小数量
    formData.value.blank_fabric_max = formatWeightDiv(result.data.blank_fabric_max) // 胚布销售最大数量
    formData.value.credit_limit = formatPriceDiv(result.data.credit_limit) // 信用额度
    formData.value.credit_level = result.data.credit_level // 信用等级
    formData.value.settle_cycle = result.data.settle_cycle
    // 织造配置
    weavingConfigData.value.fabric_decimal_places = result.data.decimal_point // 坯布位数
    weavingConfigData.value.rounding_type = result.data.carry // 进位方式
    weavingConfigData.value.fabric_weight_min = formatWeightDiv(result.data.fabric_min_weight) // 坯布重量最小值
    weavingConfigData.value.fabric_weight_max = formatWeightDiv(result.data.fabric_max_weight) // 坯布重量最大值
    if (result.data.settle_type === SettleType.dayType || result.data.settle_type === SettleType.moonType)
      rules.value.settle_cycle = [{ validator: validateNumber, trigger: 'blur' }]

    if (result.data.factory_logistics && result.data.factory_logistics.length) { // 如果地址数组有数据 而且 长度不为 0
      addressList.value = result.data.factory_logistics.map((item: AddressFormData | any, index: number) => ({
        ...item,
        id: index + 10,
        location: item.location ? item.location.split(',') : [],
      }))
    }
    if (data.value.qywx_customers) {
      wechatContacts.value = data.value.qywx_customers.map((item: any) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        platform: item.type === 'wx' ? '微信' : `企业微信`,
      }))
    }
    if (data.value.qywx_groups) {
      boundQyGroups.value = data.value.qywx_groups.map((item: any) => ({
        id: item.id,
        name: item.name,
        notify_type: item.notify_type,
        notify_type_name: item.notify_type_name,
      }))
    }
  }
  else {
    ElMessage.error(msg.value)
  }
  nextTick(() => {
    locationStatus.value = true
  })
}

onActivated(getData)
onMounted(async () => { // 第一次获取数据的时候
  await getData()
})

const ruleFormRef = ref()

const { fetchData: postFetch, data: addData, success: postSuccess, msg: postMsg } = Business_unitcustomerPut()

// const router = useRouter()

async function handleSure() {
  ruleFormRef.value.validate((valid: any) => {
    if (valid)
      handSubmit()
  })
}

async function handSubmit() {
  const req = {
    ...formData.value,
    id: Number(route.query.id),
    location: (formData.value.location || []).join(','),
    blank_fabric_max: formatWeightMul(formData.value.blank_fabric_max),
    blank_fabric_min: formatWeightMul(formData.value.blank_fabric_min),
    credit_limit: formatPriceMul(formData.value.credit_limit),
    // 织造配置
    decimal_point: weavingConfigData.value.fabric_decimal_places,
    carry: weavingConfigData.value.rounding_type,
    fabric_min_weight: formatWeightMul(weavingConfigData.value.fabric_weight_min),
    fabric_max_weight: formatWeightMul(weavingConfigData.value.fabric_weight_max),
    factoryLogistics: addressList.value.map((item: AddressFormData, index: number) => ({
      ...item,
      id: index,
      name: item.contact_name,
      location: Array.isArray(item.location) ? item.location.join(',') : item.location,
    })),
    qywx_customers: wechatContacts.value.map(item => ({
      id: item.id,
      name: item.name,
      type: item.type,
    })),
    qywx_groups: boundQyGroups.value.map(item => ({
      id: item.id,
      name: item.name,
      notify_type: item.notify_type,
      notify_type_name: item.notify_type_name,
    })),
  }
  await postFetch(req)
  if (postSuccess.value) {
    ElMessage.success('成功')
    formData.value = resetData(formData.value)
    formData.value.blacklist = false
    formData.value.settle_type = SettleType.cashType
    addressList.value = []
    routerList.push({
      name: 'CustomerDetail',
      query: { id: addData.value.id },
    })
  }
  else {
    ElMessage.error(postMsg.value)
  }
}

// const AddDialogRef = ref()

function handleAdd() {
  currentEditData.value = {} // 重置表单
  dialogVisible.value = true // 打开弹窗
}

function handleEdit(item: AddressFormData) {
  currentEditData.value = { ...item }
  setTimeout(() => {
    dialogVisible.value = true
  }, 0)
}

function handleFormSubmit(formData: { mode: string, data: AddressFormData }) {
  const { data } = formData

  // 添加防御性检查
  if (!Array.isArray(addressList.value))
    addressList.value = [] // 如果不是数组，初始化为空数组

  // 查找是否存在相同ID的地址
  const existingIndex = addressList.value.findIndex(item => item.id === data.id)

  if (existingIndex !== -1) {
    // ID存在，执行编辑操作
    addressList.value[existingIndex] = { ...data }
    ElMessage.success('地址编辑成功')
  }
  else {
    // ID不存在，执行新增操作
    addressList.value.push(data)
    ElMessage.success('地址新增成功')
  }

  // 如果设置了默认地址，需要更新其他地址的默认状态
  if (data.is_default) {
    addressList.value.forEach((item) => {
      if (item.id !== data.id)
        item.is_default = false
    })
  }

  // 关闭弹窗
  dialogVisible.value = false
}

function chnageSettleType(val: any) {
  formData.value.settle_cycle = undefined // 清空自定义周期
  if (val.id === SettleType.dayType || val.id === SettleType.moonType)
    rules.value.settle_cycle = [{ validator: validateNumber, trigger: 'blur' }]
  else
    formData.value.settle_cycle = 7 // 默认7
}

const { fetchData: searchCodes, data: searchedData } = SearchForSomeSupplierField()
const filterDatas = ref<any>([])
async function getSearchedCodes(val: string) {
  // 节流
  throttle(async () => {
    await searchCodes({
      category: 2,
      code: val,
    })
    filterDatas.value = searchedData.value.list
  }, 500)()
}

function selectConfirm(e: any) {
  const value = e.target.value
  if (value)
    formData.value.code = value
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <div class="line" />
    <el-form ref="ruleFormRef" :model="formData" :rules="rules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="客户名称:">
          <template #content>
            <el-form-item prop="name">
              <el-input v-model.trim="formData.name" placeholder="请输入客户名称" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户全称:">
          <template #content>
            <el-form-item prop="full_name">
              <el-input v-model.trim="formData.full_name" placeholder="请输入客户全称" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户编号:">
          <template #content>
            <el-form-item prop="code">
              <el-select v-model="formData.code" filterable :filter-method="getSearchedCodes" placeholder="请输入或选择客户编号" allow-create default-first-option class="w-full" @blur="selectConfirm">
                <el-option v-for="item in filterDatas" :key="item?.id" :label="item?.code" :value="item?.code" disabled>
                  <span style="float: left">{{ item?.code }}</span>
                  <span style="float: right; color: var(--el-text-color-secondary); font-size: 10px">已存在</span>
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="客户类型:">
          <template #content>
            <el-form-item prop="unit_type_id">
              <SelectComponents v-model="formData.unit_type_id" multiple api="AdminuenumsupplierType" label-field="name" value-field="id" placeholder="请选择客户类型" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="所属营销体系:">
          <template #content>
            <el-form-item prop="sale_system_ids">
              <SelectComponents v-model="formData.sale_system_ids" multiple style="width: 300px" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" placeholder="请选择营销体系" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人:">
          <template #content>
            <el-form-item prop="contact_name">
              <el-input v-model="formData.contact_name" placeholder="请输入联系人" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="联系电话:">
          <template #content>
            <el-form-item prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="地区:">
          <template #content>
            <el-form-item prop="location">
              <CascaderAddress v-if="locationStatus" v-model="formData.location" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="地址:">
          <template #content>
            <el-form-item prop="address">
              <el-input v-model.trim="formData.address" placeholder="请输入地址" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="邮箱:">
          <template #content>
            <el-form-item prop="email">
              <el-input v-model.trim="formData.email" placeholder="请输入邮箱" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="是否为黑名单:">
          <template #content>
            <el-form-item prop="is_blacklist">
              <el-switch v-model="formData.is_blacklist" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="社会统一信用代码:">
          <template #content>
            <el-input v-model.trim="formData.credit_code" placeholder="社会统一信用代码" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model.trim="formData.remark" show-word-limit maxlength="100" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-has="'QYWechatFloatButtond'" label="企微客户绑定:" :copies="2">
          <template #content>
            <SelectWechatFriendLink v-model="wechatContacts" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-has="'QYWechatFloatButtond'" label="企微群绑定:" :copies="2">
          <template #content>
            <SelectQyGroupLink v-model="boundQyGroups" :clickable="wechatContacts.length" />
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-box">
        <div class="sale">
          销售信息
        </div>
      </div>
      <div class="line" />
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <el-form-item prop="seller_id">
              <SelectComponents v-model="formData.seller_id" style="width: 300px" :query="{ duty: EmployeeType.salesman }" api="Adminemployeelist" label-field="name" value-field="id" placeholder="请选择销售员" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跟单员:">
          <template #content>
            <el-form-item prop="order_follower_id">
              <SelectComponents v-model="formData.order_follower_id" style="width: 300px" :query="{ duty: EmployeeType.follower }" api="Adminemployeelist" label-field="name" value-field="id" placeholder="请选择跟单员" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售区域:">
          <template #content>
            <el-form-item prop="sale_area_id">
              <SelectComponents v-model="formData.sale_area_id" style="width: 300px" api="Adminbusiness_unitsale_arealist" label-field="name" placeholder="请选择销售区域" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售群体:">
          <template #content>
            <el-form-item prop="sale_group_id">
              <SelectComponents v-model="formData.sale_group_id" style="width: 300px" api="Adminbusiness_unitsale_grouplist" label-field="name" placeholder="请选择销售群体" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-box">
        <div class="sale">
          结算信息
        </div>
      </div>
      <div class="line" />
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="默认结算类型:">
          <template #content>
            <el-form-item prop="settle_type">
              <SelectComponents v-model="formData.settle_type" style="width: 300px" api="AdminenumsettleType" label-field="name" value-field="id" clearable @select="chnageSettleType" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="formData.settle_type === SettleType.weekType" label="结算周期:">
          <template #content>
            <el-form-item prop="settle_cycle">
              <SelectComponents v-model="formData.settle_cycle" api="GetSettleCycleApi" label-field="name" value-field="id" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="formData.settle_type === SettleType.dayType" label="自定义天数:">
          <template #content>
            <el-form-item prop="settle_cycle">
              <el-input-number v-model="formData.settle_cycle" :precision="0" :min="0" style="width: 130px" placeholder="请输入默认结算天数" controls-position="right" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="formData.settle_type === SettleType.moonType" label="自定义月份:">
          <template #content>
            <el-form-item prop="settle_cycle">
              <el-input-number v-model="formData.settle_cycle" :precision="0" :min="0" style="width: 130px" placeholder="请输入默认结算天数" controls-position="right" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="信用额度:">
          <template #content>
            <el-form-item prop="credit_limit">
              <el-input-number v-model="formData.credit_limit" :precision="0" :min="0" style="width: 130px" placeholder="信用额度" controls-position="right" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="信用等级:">
          <template #content>
            <el-form-item prop="credit_level">
              <SelectComponents v-model="formData.credit_level" style="width: 300px" api="CreditLevel" label-field="name" value-field="id" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-box">
        <div class="sale">
          收货地址
        </div>
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
      </div>
      <div class="line" />
      <div style="width: 100%;padding: 0px;box-sizing: border-box">
        <AddressList
          v-if="addressList.length > 0"
          :list="addressList"
          types="CanEdit"
          @edit="handleEdit"
        />
        <span v-else>收货地址为空</span>
        <!-- 引入表单弹窗 -->
        <AddressForm
          v-model:visible="dialogVisible"
          :edit-data="currentEditData"
          @submit="handleFormSubmit"
        />
      </div>
      <!-- 织造配置 -->
      <WeavingConfig v-model="weavingConfigData" mode="edit" />
    </el-form>
  </FildCard>
  <!-- <AddDialog ref="AddDialogRef" @handle-sure="handleSubmit" /> -->
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}
.flex-box {
  margin-top: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .sale {
    font-weight: 600;
  }
}
.address_box {
  min-width: 300px;
  max-width: 500px;
  .address_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top_left {
      display: flex;
      align-items: center;
      .com_name {
        font-weight: 600;
        margin-right: 10px;
      }
      .moren {
        width: 40px;
        height: 20px;
        border-radius: 6px;
        background: #ef5757;
        color: #fff;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
      }
    }
    .top_right {
      display: flex;
      align-items: center;
    }
  }
  .bottom_line {
    background: #efefef;
    width: 100%;
    height: 3px;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
}
.weixin-contacts {
  .contact-item {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 4px;
    margin-right: 8px;
    font-size: 14px;

    .platform {
      &[class*="wx"] {
        color: var(--el-color-success);
      }

      &[class*="qywx"] {
        color: #c59436;  // 修改企业微信的颜色
      }
      margin-left: 4px;
    }

    .delete-icon {
      margin-left: 4px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      cursor: pointer;

      &:hover {
        color: var(--el-color-danger);
      }
    }
  }

  .el-link {
    margin-left: 8px;
  }
}
.qy-group-wrapper {
  .qy-group-table {
    min-width: 600px;
  }

  :deep(.el-table) {
    --el-table-border-color: #EBEEF5;
    --el-table-header-bg-color: #F5F7FA;

    th {
      background-color: var(--el-table-header-bg-color);
      color: var(--el-text-color-regular);
      font-weight: 500;
      padding: 8px 0;
    }

    td {
      padding: 8px 0;
    }
  }
}

.mt-3 {
  margin-top: 12px;
}
</style>
