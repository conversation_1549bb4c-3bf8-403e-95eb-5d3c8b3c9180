<script lang="ts" setup name="ColorNoticeEdit">
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { cloneDeep, isArray } from 'lodash-es'
import DrawerSide from '../components/DrawerSide.vue'
import AddDyeingProductDialog from './components/AddDyeingProductDialog.vue'
import ProductTable from './components/ProductTable.vue'
import { redye_orderldetail, redye_orderlput } from '@/api/colorNotice'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { formatDate, formatPriceDiv, formatPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deepClone } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Accordion from '@/components/Accordion/index.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectReturnAddress from '@/components/SelectReturnAddress/index'

const routerList = useRouterList()

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^(?:(?:\+|00)86)?1\d{10}$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}

const state = reactive<any>({
  form: {
    sale_system_id: '', // 营销体系
    factory_id: '',
    dye_factory_name: '', // 染厂名称
    date: '', // 染整日期
    factory_follow: '', // 染厂跟单
    remark: '',
    lights: '',
    packing: '',
    finishing: '',
    sale_system_name: '',
    order_follower_name: '',
    phone: '',
    return_address: '',
    tenant_receive_addr_id: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    factory_id: [{ required: true, message: '请选择染厂名称', trigger: 'change' }],
    date: [{ required: true, message: '请选择染整日期', trigger: 'blur' }],
    phone: [{ trigger: 'blur', validator: checkPhone }],
  },
  multipleSelection: [],
  info: {},
  tableList: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

const route = useRoute()
const DrawerSideRef = ref()

onMounted(() => {
  getInfomation()
})

const { fetchData: getFetch, data: fabricList } = redye_orderldetail()

// 获取坯布信息
async function getInfomation() {
  await getFetch({ id: route.query.id })
}

watch(
  () => fabricList.value,
  () => {
    state.form.sale_system_id = fabricList.value.sale_system_id
    state.form.sale_system_name = fabricList.value.sale_system_name

    state.form.factory_id = fabricList.value.dye_factory_id
    state.form.dye_factory_name = fabricList.value.dye_factory_name
    state.form.factory_follow = fabricList.value.order_follower_id
    state.form.order_follower_name = fabricList.value.order_follower_name
    state.form.phone = fabricList.value.order_follower_phone
    state.form.date = fabricList.value.dnf_date
    state.form.remark = fabricList.value.remark
    state.form.return_address = fabricList.value.return_address
    state.form.tenant_receive_addr_id = fabricList.value.tenant_receive_addr_id
    state.form.finishing = fabricList.value.dnf_requirement
    state.form.lights = fabricList.value.light_requirement
    state.form.packing = fabricList.value.pack_requirement

    // 等待组件渲染完成再给值过去
    nextTick(() => {
      let obj: any = { selectList: [] }
      obj = {
        ...JSON.parse(fabricList.value?.craft_requirement || '{}'),
        is_length_cut: fabricList.value.is_length_cut,
      }
      if (obj.selectList) {
        obj.selectList = obj.selectList?.map((item: any) => {
          if (item?.show_type === 2) {
            if (item.selectValue === '') {
              item.selectValue = []
              return item
            }
            item.selectValue = item.selectValue.split(',').map((value: string) => (value.startsWith('-') ? value : Number(value)))
          }
          return item
        })
      }
      state.info = obj
      DrawerSideRef.value.state.info = obj
    })

    state.tableList = fabricList.value?.items

    // 数据清洗，讲金额和数量格式化单位
    for (let i = 0; i < state.tableList?.length; i++) {
      state.tableList[i].increase_weight = formatWeightDiv(state.tableList[i]?.increase_weight)
      state.tableList[i].piece_count = Number(formatPriceDiv(state.tableList[i].piece_count))
      state.tableList[i].weight = formatWeightDiv(state.tableList[i]?.weight)
      state.tableList[i].order_type = state.tableList[i]?.final_src_type
      state.tableList[i].dnf_date = state.tableList[i]?.src_dnf_date
      state.tableList[i].order_no = state.tableList[i]?.src_order_no
      state.tableList[i].color_id = state.tableList[i]?.src_color_id
      state.tableList[i].dnf_loss = formatPriceDiv(state.tableList[i].dnf_loss)

      state.tableList[i].isEdit = true

      for (let q = 0; q < state.tableList[i].use_fabric?.length; q++) {
        state.tableList[i].use_fabric[q].weight = Number(formatWeightDiv(state.tableList[i].use_fabric[q].weight))
        state.tableList[i].use_fabric[q].piece_count = Number(formatPriceDiv(state.tableList[i].use_fabric[q].piece_count))
      }
    }
  },
)

// const handOpen = () => {
//   DrawerSideRef.value.state.showModal = true
// }

const AddDyeingProductDialogRef = ref()

function handAdd() {
  AddDyeingProductDialogRef.value.state.showModal = true
  AddDyeingProductDialogRef.value.state.filterData.dye_factory_id = state.form.factory_id
}

function handleSureAdd(list: any) {
  list.forEach((item: any) => {
    state.tableList.push({
      order_no: item.order_no,
      dnf_date: item.dnf_date,
      sale_plan_order_no: item.sale_plan_order_no,
      customer_id: item.customer_id,
      customer_name: item.customer_name,
      code: item.code,
      finish_product_id: item.finish_product_id,
      name: item.name,
      src_color_no: item.color_no,
      src_color_name: item.color_name,
      src_df_color_no: item.df_color_no,
      src_dyelot: item.dyelot,
      color_no: item.color_no,
      color_name: item.color_name,
      color_id: item.color_id,
      df_color_no: item.df_color_no,
      dyelot: item.dyelot,
      level_code: item.level_code,
      level: item.level_name,
      level_id: item.level_id,
      craft: item.craft,
      dnf_craft: item.dnf_craft,
      paper_tube_specs: item.paper_tube_specs,
      tape_specs: item.tape_specs,
      dnf_craft_ids: item.dnf_craft_ids || [],
      paper_tube_ids: item.paper_tube_ids || [],
      plastics_bag_ids: item.dnf_craft_ids || [],
      hand_feeling: item.hand_feeling,
      gram_weight_unit_id: item.gram_weight_unit_id,
      gram_weight: item.gram_weight,
      finish_product_gram_weight_and_unit_name: item.finish_product_gram_weight_and_unit_name,
      width_unit_id: item.width_unit_id,
      width: item.width,
      finish_product_width_and_unit_name: item.finish_product_width_and_unit_name,
      dnf_loss: formatPriceDiv(item.dnf_loss),
      order_type: item.final_src_type,
      unit: item.unit,
      piece_count: 0,
      weight: 0,
      delivery_date: '',
      remark: '',
      use_fabric: item.use_fabric,
      increase_weight: formatWeightDiv(item.increase_weight),
    })
  })
  AddDyeingProductDialogRef.value.state.showModal = false
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = redye_orderlput()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条成品信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].customer_id === '')
      return ElMessage.error('请选择所属客户')

    if (list[i].color_no === '')
      return ElMessage.error('请选择色号')

    if (list[i].piece_count === '')
      return ElMessage.error('匹数不可为空')

    if (list[i].weight === '')
      return ElMessage.error('数量不可为空')

    if (Number(sumNum(list[i]?.use_fabric, 'piece_count')) > list[i].piece_count && Number(list[i].piece_count) !== 0)
      return ElMessage.error('不可大于总匹数')

    list[i].increase_weight = Number(formatWeightMul(list[i].increase_weight))
    list[i].weight = Number(formatWeightMul(list[i].weight))
    list[i].piece_count = Number(formatPriceMul(list[i].piece_count))
    list[i].dnf_loss = Number(formatPriceMul(list[i].dnf_loss))

    for (let q = 0; q < list[i].use_fabric?.length; q++) {
      list[i].use_fabric[q].weight = Number(formatWeightMul(list[i].use_fabric[q].weight))
      list[i].use_fabric[q].piece_count = Number(formatPriceMul(list[i].use_fabric[q].piece_count))
      list[i].use_fabric[q].delivery_date = formatDate(list[i].use_fabric[q].delivery_date)
      if (!list[i].isEdit)
        list[i].use_fabric[q].src_id = list[i].use_fabric[q].id
    }
  }

  // 把工艺要求的数据处理成后端需要的格式
  const stateInfo = cloneDeep(state.info)
  stateInfo?.selectList?.map((item: any) => {
    let selectValue = ''
    // 如果item?.selectValue是数组，就把item?.selectValue这个数组的值拼接成字符串，用逗号隔开
    if (isArray(item?.selectValue))
      selectValue = item?.selectValue.join(',')
    else
      selectValue = item?.selectValue

    item.selectValue = selectValue
  })

  const query = {
    remark: state.form.remark,
    dnf_requirement: state.form.finishing,
    dnf_date: formatDate(state.form.date),
    dye_factory_id: state.form.factory_id,
    dye_factory_name: state.form.dye_factory_name,
    light_requirement: state.form.lights,
    order_follower_id: state.form.factory_follow,
    order_follower_name: state.form.order_follower_name,
    order_follower_phone: state.form.phone,
    pack_requirement: state.form.packing,
    return_address: state.form.return_address,
    tenant_receive_addr_id: state.form.tenant_receive_addr_id,
    sale_system_id: state.form.sale_system_id,
    sale_system_name: state.form.sale_system_name,
    craft_requirement: JSON.stringify(stateInfo),
    is_length_cut: state.info.is_length_cut,
    items: list,
    id: Number(route.query.id),
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'ColorNoticeDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

// 批量操作
const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

const bulkSetting = ref<any>({})

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    title: '客户名称',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'level_code',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
    labelField: 'name',
    valueField: 'code',
  },
  // {
  //   field: 'machine_number',
  //   title: '机台号',
  //   component: 'input',
  //   type: 'text',
  // },
  {
    field: 'increase_weight',
    title: '加重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'piece_count',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'weight',
    title: '数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function changSystem() {
  state.tableList.map((item: any) => {
    item.customer_id = ''
    return item
  })
  bulkList[0].query = { sale_system_id: state.form.sale_system_id }
}

async function bulkSubmit({ row, value }: any, val: any, field: string) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })

  if (field === 'customer_id') {
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        item.customer_name = val.name
        return item
      }
    })
  }

  if (field === 'level_code') {
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        item.level = val.name
        item.level_id = val.id
        item.level_code = val.code
        return item
      }
    })
  }

  bulkShow.value = false
  ElMessage.success('设置成功')
}

function returnAddressSelectChange(val: any) {
  state.form.return_address = val?.addr
  state.form.tenant_receive_addr_id = val?.id
}

// 监听state.tableList，获取第一个的成品id，传给工艺要求
const activeFirstProductId = computed(() => {
  return state.tableList[0]?.finish_product_id
})
</script>

<template>
  <FildCard title="" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
      <!-- <el-button type="primary" @click="handOpen">编辑工艺要求</el-button> -->
    </template>
  </FildCard>
  <div class="flex">
    <div class="mt-[5px] !min-w-[auto] flex-1 overflow-hidden">
      <FildCard title="基础信息" class="mt-[5px]" :tool-bar="false">
        <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
          <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
            <DescriptionsFormItem label="营销体系:" required>
              <template #content>
                <el-form-item prop="sale_system_id">
                  <SelectComponents
                    v-model="state.form.sale_system_id"
                    api="AdminsaleSystemgetSaleSystemDropdownList"
                    label-field="name"
                    value-field="id"
                    clearable
                    @select="changSystem"
                    @change-value="val => (state.form.sale_system_name = val.name)"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="染厂名称:" required>
              <template #content>
                <el-form-item prop="factory_id">
                  <SelectDialog
                    v-model="state.form.factory_id"
                    :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.name }"
                    api="BusinessUnitSupplierEnumlist"
                    :column-list="[
                      {
                        field: 'name',
                        title: '名称',
                        minWidth: 100,
                        isEdit: true,
                        colGroupHeader: true,
                        childrenList: [
                          {
                            field: 'name',
                            isEdit: true,
                            title: '名称',
                            minWidth: 100,
                          },
                        ],
                      },
                      {
                        field: 'code',
                        title: '编号',
                        minWidth: 100,
                        isEdit: true,
                        colGroupHeader: true,
                        childrenList: [
                          {
                            field: 'code',
                            isEdit: true,
                            title: '编号',
                            minWidth: 100,
                          },
                        ],
                      },
                    ]"
                    @change-value="val => ((state.form.dye_factory_name = val?.name), (state.form.factory_follow = val?.order_follower_id), (state.form.phone = val?.order_follower_phone))"
                    @change-input="val => (componentRemoteSearch.name = val)"
                  />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="染厂跟单:">
              <template #content>
                <SelectComponents
                  v-model="state.form.factory_follow"
                  :query="{ duty: EmployeeType.follower }"
                  api="Adminemployeelist"
                  label-field="name"
                  value-field="id"
                  clearable
                  @change-value="val => ((state.form.order_follower_name = val.name), (state.form.phone = val.phone))"
                />
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="跟单电话:">
              <template #content>
                <el-form-item prop="phone">
                  <el-input v-model="state.form.phone" type="number" maxlength="11" />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="染整日期:" required>
              <template #content>
                <el-form-item prop="date">
                  <el-date-picker v-model="state.form.date" type="date" placeholder="染整日期" />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="回货地址:">
              <template #content>
                <!--                <el-input v-model="state.form.address" clearable></el-input> -->
                <SelectReturnAddress v-model="state.form.return_address" :sale-system-id="state.form.sale_system_id" @select-change="returnAddressSelectChange" />
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="单据备注:" copies="2">
              <template #content>
                <el-form-item prop="remark">
                  <el-input v-model="state.form.remark" placeholder="单据备注" />
                </el-form-item>
              </template>
            </DescriptionsFormItem>
          </div>
        </el-form>
      </FildCard>
      <FildCard title="成品信息" class="mt-[5px]">
        <template #right-top>
          <el-button type="primary" @click="bulkHand">
            批量操作
          </el-button>
          <el-button :disabled="state.form.sale_system_id === '' || state.form.factory_id === ''" type="primary" @click="handAdd">
            从染整进度中添加成品
          </el-button>
        </template>
        <ProductTable v-model:select-list="state.multipleSelection" v-model="state.tableList" :info="state.form" />
      </FildCard>
      <FildCard title="其他要求" :tool-bar="false" class="mt-[5px]">
        <div class="flex flex-row">
          <div class="flex flex-1">
            <div>对灯要求：</div>
            <div class="w-[300px]">
              <el-input v-model="state.form.lights" maxlength="200" show-word-limit :autosize="{ minRows: 5, maxRows: 15 }" type="textarea" />
            </div>
          </div>
          <div class="flex flex-1">
            <div>包装要求：</div>
            <div class="w-[300px]">
              <el-input v-model="state.form.packing" maxlength="200" show-word-limit :autosize="{ minRows: 5, maxRows: 15 }" type="textarea" />
            </div>
          </div>
          <div class="flex flex-1">
            <div>染整要求：</div>
            <div class="w-[300px]">
              <el-input v-model="state.form.finishing" maxlength="2000" show-word-limit :autosize="{ minRows: 5, maxRows: 15 }" type="textarea" />
            </div>
          </div>
        </div>
      </FildCard>
    </div>
    <div class="ml-[20px] mt-[10px]">
      <Accordion style="min-height: 100%" :open-status="true">
        <DrawerSide ref="DrawerSideRef" v-model:info="state.info" :is-edit="true" :dye_factory_id="state.form.factory_id" :active-first-product-id="activeFirstProductId" />
      </Accordion>
    </div>
  </div>
  <!-- <DrawerSide :is-edit="true" ref="DrawerSideRef" v-model:info="state.info"></DrawerSide> -->
  <AddDyeingProductDialog ref="AddDyeingProductDialogRef" @handle-sure="handleSureAdd" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>
