<script setup lang="ts" name="GreyFabricInventory">
import { Delete, Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import AddAdjustXimaDialog from '../components/AddAdjustXimaDialog.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
// import router from '@/router'
import SelectComponents from '@/components/SelectComponents/index.vue'
import {
  getGfmStockAdjustOrder,
  getGfmStockAdjustOrderList,
  getGfmStockAdjustOrderListExport,
  updateGfmStockAdjustOrderStatusPass,
  updateGfmStockAdjustOrderStatusWait,
} from '@/api/greyFabricInventory'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import SelectDate from '@/components/SelectDate/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    order_no: '',
    adjust_unit_id: '',
    date: '',
    audit_status: '',
  },
  multipleSelection: [],
  information: false,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getGfmStockAdjustOrderList()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.audit_status.length)
    obj.audit_status = obj.audit_status.join(',')

  const query: any = {
    begin_adjust_time:
      state.filterData.date
      && state.filterData.date !== ''
      && state.filterData.date.length
        ? formatDate(state.filterData.date[0])
        : '',
    end_adjust_time:
      state.filterData.date
      && state.filterData.date !== ''
      && state.filterData.date.length
        ? formatDate(state.filterData.date[1])
        : '',
    ...state.filterData,
    audit_status: obj.audit_status,
  }
  delete query.date
  await ApiCustomerList(getFilterData(query))
  if (data.value?.list)
    getInfomation(data.value.list[0])
}, 400)
onActivated(getData)

onMounted(() => {
  getData()
})

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '9%',
  showSort: false,
  height: '100%',
  fieldApiKey: 'GreyFabricInventoryIndex',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const columnList_fabic_config = ref({
  // FooterMethod: (val: any) => FooterMethod(val),
  fieldApiKey: 'GreyFabricInventoryIndexFabic',
  showSlotNums: true,
  showOperate: true,
  height: '100%',
  operateWidth: '120',
  showSpanHeader: true,
})

// const FooterMethod = ({ columns, data }: any) => {
//

//   return [
//     columns.map((column: any, _columnIndex: number) => {
//       if ([0].includes(_columnIndex)) {
//         return '汇总'
//       }
//       if (['code'].includes(column.property)) {
//         return `200 kg`
//       }
//       return null
//     }),
//   ]
// }

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '坯布库存盘点单号',
    fixed: 'left',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'adjust_unit_name',
    title: '调整单位',
    // fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'store_keeper_name',
    title: '仓管员',
    // fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'adjust_time',
    title: '调整日期',
    minWidth: 120,
    is_date: true,
  },
  {
    field: 'create_time',
    title: '创建时间',
    sortable: true,
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    sortable: true,
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '单据状态',
    fixed: 'right',
    soltName: 'audit_status',
    showOrder_status: true,
    width: '5%',
  },
])

const columnList_fabic = ref([
  {
    title: '调整前',
    childrenList: [
      {
        sortable: true,
        field: 'grey_fabric_code',
        title: '坯布编号',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'grey_fabric_name',
        title: '坯布名称',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供方名称',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'yarn_batch',
        title: '纱批',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'machine_number',
        title: '机台号',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'grey_fabric_width_and_unit_name',
        title: '坯布幅宽',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'grey_fabric_gram_weight_and_unit_name',
        title: '坯布克重',
        minWidth: '5%',
        // isWeight: true,
      },
      {
        sortable: true,
        field: 'needle_size',
        title: '针寸数',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'component',
        title: '成分',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'gray_fabric_color_name',
        title: '织坯颜色',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'grey_fabric_level_name',
        title: '坯布等级',
        minWidth: '5%',
      },
      {
        sortable: true,
        field: 'raw_material_batch_num',
        title: '原料批号',
        minWidth: '5%',
      },
      // {
      //   field: 'single_price',
      //   title: '磅重',
      //   minWidth: '5%',
      //   isWeight: true,
      // },
      {
        sortable: true,
        field: 'single_price',
        title: '单价',
        minWidth: '5%',
        isUnitPrice: true,
      },
      {
        sortable: true,
        field: 'grey_fabric_remark',
        title: '坯布备注',
        minWidth: 100,
      },
    ],
  },

  {
    title: '调整后',
    childrenList: [
      {
        sortable: true,
        field: 'after_customer_name',
        title: '所属客户',
        minWidth: 100,
        soltName: 'after_customer_name',
      },
      {
        sortable: true,
        field: 'after_machine_number',
        title: '机台号',
        minWidth: 100,
        soltName: 'after_machine_number',
      },
      {
        sortable: true,
        field: 'after_yarn_batch',
        title: '纱批',
        minWidth: '5%',
        soltName: 'after_yarn_batch',
      },
      {
        sortable: true,
        field: 'after_gray_fabric_color_name',
        title: '织坯颜色',
        minWidth: '5%',
        soltName: 'after_gray_fabric_color_name',
      },
      {
        sortable: true,
        field: 'after_grey_fabric_level_name',
        title: '坯布等级',
        minWidth: '5%',
        soltName: 'after_grey_fabric_level_name',
      },
      {
        sortable: true,
        field: 'after_raw_material_batch_num',
        title: '原料批号',
        minWidth: '5%',
        soltName: 'after_raw_material_batch_num',
      },
      {
        sortable: true,
        field: 'after_single_price',
        title: '单价',
        minWidth: '5%',
        isUnitPrice: true,
        soltName: 'after_single_price',
      },
      {
        sortable: true,
        field: 'after_grey_fabric_remark',
        title: '坯布备注',
        minWidth: 100,
        soltName: 'after_grey_fabric_remark',
      },
    ],
  },

  {
    title: '涉及匹数',
    childrenList: [
      {
        sortable: true,
        field: 'roll',
        title: '匹数',
        minWidth: '5%',
        isPrice: true,
      },
      {
        sortable: true,
        field: 'total_weight',
        title: '总数量',
        minWidth: '5%',
        isWeight: true,
      },
    ],
  },
])

// const handShowSort = () => {
//   tableConfig.value.showSort = true
// }

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '坯布库存调整单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = getGfmStockAdjustOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'GreyFabricInventoryAdd',
  })
}

const { fetchData: getFetchDetail, data: fabricList }
  = getGfmStockAdjustOrder()

// 获取坯布信息
async function getInfomation(row: any) {
  await getFetchDetail({ id: row.id })

  state.information = true
}

function handDetail(row: any) {
  router.push({
    name: 'GreyFabricInventoryDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'GreyFabricInventoryEdit',
    query: { id: row.id },
  })
}

const AddAdjustXimaDialogRef = ref()

function handSeeXima(row: any, rowIndex: number) {
  const listData = deepClone(row?.item_fc_data || [])
  listData?.map((item: any) => {
    item.roll = Number(formatPriceDiv(item?.roll))
    item.weight = Number(formatWeightDiv(item?.weight))
    return item
  })

  const info = {
    supplier_name: row.supplier_name,
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    gray_fabric_color_name: row.gray_fabric_color_name,
    grey_fabric_level_name: row.grey_fabric_level_name,
    yarn_batch: row.yarn_batch,
    machine_number: row.machine_number,
    customer_name: row.customer_name,
  }

  const filterData = {
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    customer_id: row.customer_id,
    customer_name: row.customer_name,
    yarn_batch: row.yarn_batch,
    supplier_id: row.supplier_id,
    gray_fabric_color_id: row.gray_fabric_color_id,
    grey_fabric_level_id: row.grey_fabric_level_id,
    raw_material_batch_brand: row.raw_material_batch_brand,
    raw_material_batch_num: row.raw_material_batch_num,
    raw_material_yarn_name: row.raw_material_yarn_name,
    source_remark: row.grey_fabric_remark,
    warehouse_id: row.warehouse_id,
    warehouse_sum_id: row?.warehouse_sum_id || 0,
  }

  AddAdjustXimaDialogRef.value.state.filterData = filterData
  AddAdjustXimaDialogRef.value.state.info = info
  AddAdjustXimaDialogRef.value.state.canEnter = row.roll
  AddAdjustXimaDialogRef.value.state.showModal = true
  AddAdjustXimaDialogRef.value.state.rowIndex = rowIndex
  AddAdjustXimaDialogRef.value.state.ximaList = listData
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateGfmStockAdjustOrderStatusPass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateGfmStockAdjustOrderStatusWait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="调整单位:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.adjust_unit_id"
              :query="{ name: componentRemoteSearch.unit_name }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="调整日期:" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              multiple
              api="getAuditStatusEnums"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button
          v-has="'GreyFabricInventory_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="'GreyFabricInventory_export'"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="getInfomation(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'GreyFabricInventory_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'GreyFabricInventory_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'GreyFabricInventory_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'GreyFabricInventory_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="columnList_fabic_config"
        :table-list="fabricList?.item_data"
        :column-list="columnList_fabic"
      >
        <template #after_customer_name="{ row }">
          <div
            :class="
              row.customer_name !== row.after_customer_name
                ? 'text-[#ed8a94]'
                : ''
            "
          >
            {{ row.after_customer_name }}
          </div>
        </template>
        <template #after_machine_number="{ row }">
          <div
            :class="
              row.machine_number !== row.after_machine_number
                ? 'text-[#ed8a94]'
                : ''
            "
          >
            {{ row.after_machine_number }}
          </div>
        </template>
        <template #after_yarn_batch="{ row }">
          <div
            :class="
              row.yarn_batch !== row.after_yarn_batch ? 'text-[#ed8a94]' : ''
            "
          >
            {{ row.after_yarn_batch }}
          </div>
        </template>
        <template #after_gray_fabric_color_name="{ row }">
          <div
            :class="
              row.gray_fabric_color_name !== row.after_gray_fabric_color_name
                ? 'text-[#ed8a94]'
                : ''
            "
          >
            {{ row.after_gray_fabric_color_name }}
          </div>
        </template>
        <template #after_grey_fabric_level_name="{ row }">
          <div
            :class="
              row.grey_fabric_level_name !== row.after_grey_fabric_level_name
                ? 'text-[#ed8a94]'
                : ''
            "
          >
            {{ row.after_grey_fabric_level_name }}
          </div>
        </template>
        <template #after_raw_material_batch_num="{ row }">
          <div
            :class="
              row.raw_material_batch_num !== row.after_raw_material_batch_num
                ? 'text-[#ed8a94]'
                : ''
            "
          >
            {{ row.after_raw_material_batch_num }}
          </div>
        </template>
        <template #after_single_price="{ row }">
          <div
            :class="
              row.single_price !== row.after_single_price ? 'text-[#ed8a94]' : ''
            "
          >
            {{ formatUnitPriceDiv(row.after_single_price) }}
          </div>
        </template>
        <template #after_grey_fabric_remark="{ row }">
          <div
            :class="
              row.grey_fabric_remark !== row.after_grey_fabric_remark
                ? 'text-[#ed8a94]'
                : ''
            "
          >
            {{ row.after_grey_fabric_remark }}
          </div>
        </template>
        <template #operate="{ row, rowIndex }">
          <el-button type="primary" text link @click="handSeeXima(row, rowIndex)">
            查看细码调整
          </el-button>
        </template>
      </Table>
    </FildCard>
  </div>
  <AddAdjustXimaDialog ref="AddAdjustXimaDialogRef" :is-edit="false" />
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
