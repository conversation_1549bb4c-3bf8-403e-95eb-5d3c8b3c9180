<script setup lang="ts" name="RawMaterialSourcing">
import { Delete, Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import AddressDetail from './components/AddressDetail/index.vue'
import type { TableColumnItem } from '@/api/commonTs'
import {
  PurchaseOrderRawMaterialCancel,
  PurchaseOrderRawMaterialDetail,
  PurchaseOrderRawMaterialPass,
  RawMaterialList,
  RawMaterialListExport,
} from '@/api/rawMaterialSourcing'
import type {
  SystemGetRawMaterialPurchaseOrderListItem,
  SystemRawMaterialPurchaseOrderDetailItem,
} from '@/api/rawMaterialSourcing/rules'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import {
  debounce,
  getFilterData,
  orderStatusConfirmBox,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import Table from '@/components/Table.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'

const state = reactive({
  filterData: {
    order_num: '',
    supplier_id: '',
    receipt_unit_id: '',
    status: '',
    purchase_start_date: '',
    purchase_end_date: '',
  },
  address_list: [] as any,
  multipleSelection: [],
  tableData: [] as any[],
  tableUserData: [] as any[],
})

const componentRemoteSearch = reactive({
  supplieName: '',
})

const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = RawMaterialList()
const getData = debounce(async () => {
  await fetchDataList(getListQuery())
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}, 400)
watch(
  () => state.filterData,
  () => getData(),
  { deep: true },
)

const purchase_time = ref()
watch(
  () => purchase_time.value,
  (value: any) => {
    state.filterData.purchase_start_date = formatDate(value?.[0]) || ''
    state.filterData.purchase_end_date = formatDate(value?.[1]) || ''
  },
)
const showAddress = ref(false)
function openAddress(row: any) {
  showAddress.value = true

  state.address_list = row || []
}

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypePurOrder,
  dataType: PrintDataType.Raw,
})

const columnList = ref<
  TableColumnItem<SystemGetRawMaterialPurchaseOrderListItem>
>([
  {
    sortable: true,
    field: 'order_num',
    title: '订单编号',
    soltName: 'order_num',
    width: '8%',
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'receipt_unit_name',
    title: '收货单位',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'receipt_date',
    title: '收货日期',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'purchase_date',
    title: '采购日期',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '订单金额',
    isPrice: true,
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 160,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    fixed: 'right',
    showOrder_status: true,
    soltName: 'status',
    width: '5%',
  },
])

const userColumnList = ref<
  TableColumnItem<SystemRawMaterialPurchaseOrderDetailItem>
>([
  {
    sortable: true,
    field: 'raw_material_code',
    title: '原料编号',
    fixed: 'left',
    width: 150,
  },
  {
    sortable: true,
    field: 'raw_material_name',
    title: '原料名称',
    fixed: 'left',
    width: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    width: 100,
  },
  {
    sortable: true,
    field: 'brand',
    title: '原料品牌',
    width: 100,
  },
  {
    sortable: true,
    field: 'craft',
    title: '原料工艺',
    width: 100,
  },
  {
    sortable: true,
    field: 'color_scheme',
    title: '原料颜色',
    width: 100,
  },
  {
    sortable: true,
    field: 'production_date',
    title: '生产日期',
    width: 100,
  },
  {
    sortable: true,
    field: 'blank_fabric_code',
    title: '坯布编号',
    width: 100,
  },
  {
    sortable: true,
    field: 'blank_fabric_name',
    title: '坯布名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'production_order_num',
    title: '生产通知单号',
    width: 100,
  },
  {
    sortable: true,
    field: 'level_name',
    title: '原料等级',
    width: 100,
  },
  {
    sortable: true,
    field: 'tax_included',
    title: '含税',
    width: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'package_price',
    title: '包装价格',
    width: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'spinning_type',
    title: '纺纱类型',
    width: '5%',
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    width: 100,
  },
  {
    sortable: true,
    field: 'logistics',
    title: '收货地址',
    fixed: 'right',
    width: '5%',
    soltName: 'logistics',
  },
  {
    sortable: true,
    field: 'whole_piece_count',
    isPrice: true,
    title: '整件件数',
    fixed: 'right',
    width: '5%',
  },
  {
    sortable: true,
    field: 'whole_piece_weight',
    title: '件重',
    fixed: 'right',
    width: '5%',
    soltName: 'whole_piece_weight',
  },
  {
    sortable: true,
    field: 'whole_weight',
    title: '数量总计',
    fixed: 'right',
    width: '5%',
    soltName: 'whole_weight',
  },
  {
    sortable: true,
    field: 'bulk_piece_count',
    isPrice: true,
    title: '散件件数',
    fixed: 'right',
    width: '5%',
  },
  {
    sortable: true,
    field: 'bulk_weight',
    title: '散装总重',
    fixed: 'right',
    width: '5%',
    soltName: 'bulk_weight',
  },
  {
    sortable: true,
    field: 'total_weight',
    title: '总数量',
    fixed: 'right',
    width: '5%',
    soltName: 'total_weight',
  },
  {
    sortable: true,
    field: 'unit_price',
    title: '单价(kg/元)',
    fixed: 'right',
    width: '5%',
    soltName: 'unit_price',
  },
  {
    sortable: true,
    field: 'total_price',
    title: '金额',
    fixed: 'right',
    width: '5%',
    soltName: 'total_price',
  },
])
const router = useRouter()

onMounted(() => {
  getData()
})
onActivated(() => {
  getData()
})
// 获取请求参数
function getListQuery() {
  const status = ((state.filterData.status as unknown as []) || []).join(',')
  return getFilterData({ ...state.filterData, status })
}

const selectRow = ref<any[]>([])
function handAllSelect({ records }: any) {
  selectRow.value = records
}

function handleSelectionChange({ records }: any) {
  selectRow.value = records
}

function handAdd() {
  router.push({ name: 'RawMaterialSourcingAdd' })
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialSourcingEdit',
    params: { id: row?.id },
  })
}

const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = PurchaseOrderRawMaterialDetail()
const userShow = ref(false)
async function showDetail(row: any) {
  await fetchDataDetail({ id: row.id })
  userShow.value = true
}

function handDetail(row: any) {
  router.push({ name: 'RawMaterialSourcingDetail', params: { id: row?.id } })
}

async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({
    id,
    message: msg,
    api: PurchaseOrderRawMaterialPass,
  })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({
    id,
    message: msg,
    api: PurchaseOrderRawMaterialCancel,
  })
  getData()
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  const name_str = '原料采购'
  const { fetchData: getFetch, success: getSuccess, msg: getMsg } = RawMaterialListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getListQuery(),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const tableConfig = ref({
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '11%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect,
  handleSelectionChange,
  fieldApiKey: 'RawMaterialSourcing_A',
})

const userTableConfig = ref({
  loading: loadingDetail,
  showSlotNums: true,
  show_footer: false,
  height: '100%',
  fieldApiKey: 'RawMaterialSourcing_B',
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="订单编号:">
          <template #content>
            <el-input
              v-model="state.filterData.order_num"
              placeholder="订单编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商:">
          <template #content>
            <!-- <SelectComponents
              :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial }"
              style="width: 200px"
              api="BusinessUnitSupplierEnumlist"
              label-field="name"
              value-field="id"
              v-model="state.filterData.supplier_id"
              clearable
            /> -->
            <SelectDialog
              v-model="state.filterData.supplier_id"
              :query="{
                unit_type_id: BusinessUnitIdEnum.rawMaterial,
                name: componentRemoteSearch.supplieName,
              }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '供应商名称',
                  minWidth: 100,
                  isEdit: true,
                },
                {
                  field: 'code',
                  title: '供应商编号',
                  minWidth: 100,
                  isEdit: true,
                },
                {
                  field: 'address',
                  title: '地址',
                  minWidth: 100,
                  isEdit: true,
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称');
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @on-input="val => (componentRemoteSearch.supplieName = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货单位:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.receipt_unit_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
              style="width: 200px"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="采购日期:" width="330">
          <template #content>
            <SelectDate v-model="purchase_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              :multiple="true"
              style="width: 200px"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="`RawMaterialSourcingAdd`"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel v-has="'RawMaterialSourcing_export'" :loading="loadingExcel" title="导出文件" @on-click-excel="handleExport" />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_num="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row?.order_num
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="`RawMaterialSourcingDetail`"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="`RawMaterialSourcingEdit`"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="`RawMaterialSourcing_pass`"
              type="primary"
              :underline="false"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="`RawMaterialSourcing_wait`"
              type="primary"
              :underline="false"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
            <PrintPopoverBtn
              :id="row.id"
              style="width: auto"
              is-link
              print-btn-text="打印"
              print-btn-type="text"
              api="PurchaseOrderRawMaterialDetail"
              :options="options"
            />
          </el-space>
        <!--        <PrintBtn
          type="rawMaterialSourcing"
          btnType="text"
          :tid="1656332409467136"
          api="PurchaseOrderRawMaterialDetail"
          :id="row.id"
        /> -->
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="userTableConfig"
        :table-list="dataDetail.items"
        :column-list="userColumnList"
      >
        <template #logistics="{ row }">
          <el-link type="primary" @click="openAddress(row)">
            查看
          </el-link>
        </template>
        <template #whole_piece_weight="{ row }">
          {{ formatWeightDiv(row?.whole_piece_weight) }}
        </template>
        <template #whole_weight="{ row }">
          {{ formatWeightDiv(row?.whole_weight) }}
        </template>
        <template #bulk_weight="{ row }">
          {{ formatWeightDiv(row?.bulk_weight) }}
        </template>
        <template #total_weight="{ row }">
          {{ formatWeightDiv(row?.total_weight) }}
        </template>
        <template #unit_price="{ row }">
          {{ formatUnitPriceDiv(row?.unit_price) }}
        </template>
        <template #total_price="{ row }">
          {{ formatPriceDiv(row?.total_price) }}
        </template>
      </Table>
    </FildCard>
    <AddressDetail v-model="showAddress" :row="state.address_list" />
  </div>
</template>

<style lang="scss">
.yuan {
  width: 10px;
  height: 10px;
  background: #51c41b;
  border-radius: 50%;
  margin-right: 10px;
}

.yuan_red {
  width: 10px;
  height: 10px;
  background: #f5232d;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
