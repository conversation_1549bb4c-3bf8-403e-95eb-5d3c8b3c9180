<script setup lang="ts" name="SalesGrossProfitTable">
import { onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { formatDate, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { GetGrossProfitGroupSellerList } from '@/api/grossProfitAnalysisOfProductSales'

import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import { EmployeeType } from '@/common/enum'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'

const filterData = reactive({
  date: [dayjs().startOf('month').toString(), dayjs().endOf('month').toString()],
  seller_id: '',
  order_date_start: formatDate(dayjs().startOf('month').toString()),
  order_date_end: formatDate(dayjs().endOf('month').toString()),
})

const {
  fetchData: fetchDataList,
  data: mainDataList,
  total: totalList,
  msg: msgList,
  page: pageList,
  size: sizeList,
  success: successList,
  loading: productLoading,
  handleSizeChange: productSizeChange,
  handleCurrentChange: productCurrentChange,
} = GetGrossProfitGroupSellerList()

// 获取列表数据
async function getData() {
  await fetchDataList(getFilterData(filterData, ['date']))
  if (!successList.value)
    return ElMessage.error(msgList.value)
}

watch(
  () => filterData.date,
  (newDate) => {
    if (newDate) {
      const [startTime, endTime] = newDate
      filterData.order_date_start = formatDate(startTime)
      filterData.order_date_end = formatDate(endTime)
    }
    else {
      filterData.order_date_start = ''
      filterData.order_date_end = ''
    }
  },
)

onMounted(() => {
  getData()
})

const columnList = ref([
  {
    sortable: true,
    field: 'seller_name',
    title: '销售员',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_total_price',
    title: '销售金额',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'out_total_cost',
    title: '成本金额',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gross_profit',
    title: '毛利',
    minWidth: 100,
    isPrice: true,
  },
])

watch(
  filterData,
  debounce(() => getData(), 400),
)

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([1].includes(_columnIndex))
        return '总计'

      if (['roll'].includes(column.property))
        return `${sumNum(data, column.property) as unknown as number}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property) as unknown as number)}`

      if (['out_total_cost', 'sale_total_price', 'gross_profit'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, column.property) as unknown as number)}`

      return null
    }),
  ]
}

const tableConfig = ref({
  fieldApiKey: fieldApiKeyList.SalesGrossProfitTable,
  footerMethod,
  showSlotNums: true,
  loading: productLoading.value,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: false,
  showOperate: false,
  operateWidth: '150',
  height: '100%',
  showSort: false,
  handleSizeChange: productSizeChange,
  handleCurrentChange: productCurrentChange,
})
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '96px' }">
        <DescriptionsFormItem label="审核日期:" width="330">
          <template #content>
            <SelectDate v-model="filterData.date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <SelectComponents v-model="filterData.seller_id" :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard :tool-bar="true" class="table-card-full">
      <Table :config="tableConfig" :table-list="mainDataList?.list" :column-list="columnList" />
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
