<!-- <template>
  <span v-has="props.p_key">
    <slot></slot>
  </span>
</template> -->

<script setup lang="ts">
// import { usePermissionStore } from '@/stores/user'
// import { useRoute } from 'vue-router'
// import keyList from './register'

// export interface Props {
//   p_key: keyof typeof keyList
// }

// const props = withDefaults(defineProps<Props>(), {})

// const permissionStore = usePermissionStore()
// const route = useRoute()
// if (props.p_key && route.name) {
//   // permissionStore.setKey(route.name as string, props.p_key)
// }
// console.log(`权限——${permissionStore.routeName}`, permissionStore.keyList)
</script>
