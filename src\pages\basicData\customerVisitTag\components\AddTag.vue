<script setup lang="ts">
import { reactive, ref } from 'vue'
import { AddVisitTag, UpdateVisitTag } from '@/api/customerVisit'
import SelectComponents from '@/components/SelectComponents/index.vue'
// import SelectComponents from '@/components/SelectComponents/index.vue'

const props = defineProps<{
  type: 'add' | 'edit'
}>()
const emits = defineEmits(['handleSure'])
const ruleFormRef = ref()
function checkSpace(rule: any, value: any, callback: any) {
  const reg = /^\S.*$/
  if (reg.test(value))
    callback()
  else
    callback(new Error('不允许输入空格'))
}
const { fetchData: addVisitApi, msg: addMsg, success: addSuccess } = AddVisitTag()
const { fetchData: updateVisitApi, msg: updateMsg, success: updateSuccess } = UpdateVisitTag()
const state = reactive({
  showModal: false,
  ids: [],
  form: {
    sort: 1,
    name: '',
    remark: '',
    status: true,
    visiting_modes: '',
    max_range_day: 0,
    min_range_day: 0,
  },
  fromRules: {
    sort: [{ required: false, message: '请填写序号', trigger: 'blur' }],
    name: [{ required: true, message: '请填写名称', trigger: 'blur' }, { validator: checkSpace }],
    visiting_modes: [{ required: true, message: '请选择类型', trigger: 'blur' }],
  },
})

function handCancel() {
  state.showModal = false
  state.form = {
    sort: 1,
    name: '',
    remark: '',
    status: true,
    visiting_modes: '',
    max_range_day: 0,
    min_range_day: 0,
  }
}

async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (props.type === 'add') {
        await addVisitApi({
          max_range_day: state.form.max_range_day,
          min_range_day: state.form.min_range_day,
          name: state.form.name,
          remark: state.form.remark,
          sort: state.form.sort,
          status: state.form.status ? 1 : 2,
          visiting_modes: state.form.visiting_modes,
        })
        if (!addSuccess.value)
          return ElMessage.error(addMsg.value)
      }

      else if (props.type === 'edit') {
        await updateVisitApi({
          visiting_modes: state.form.visiting_modes,
          max_range_day: state.form.max_range_day,
          min_range_day: state.form.min_range_day,
          name: state.form.name,
          remark: state.form.remark,
          sort: state.form.sort,
          status: state.form.status ? 1 : 2,
          ids: state.ids,
        })

        if (!updateSuccess.value)
          return ElMessage.error(updateMsg.value)
      }

      emits('handleSure', state.form)
      handCancel()
    }
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="props.type === 'add' ? '新建标签' : '编辑标签'" width="1000" height="auto" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" :model="state.form" label-width="100px" label-position="right" :rules="state.fromRules">
      <el-form-item label="标签类型" prop="visiting_modes">
        <SelectComponents v-model="state.form.visiting_modes" label-field="name" value-field="id" api="EnumVisitingMode" multiple clearable />
      </el-form-item>
      <el-form-item label="标签名称" prop="name">
        <el-input v-model="state.form.name" :maxlength="10" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="state.form.remark"
          :autosize="{ minRows: 2, maxRows: 4 }"
          placeholder="备注信息"
          type="textarea"
          :maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="state.form.status"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-row>
            <el-col :span="11">
              <el-form-item label="提醒范围" prop="min_range_day">
                <el-input-number
                  v-model="state.form.min_range_day"
                  controls-position="right"
                  :min="0"
                  :max="365"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="text-center">
              至
            </el-col>
            <el-col :span="11">
              <el-form-item prop="max_range_day" class="maxRange">
                <el-input-number
                  v-model="state.form.max_range_day"
                  :max="365"
                  :min="0"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="state.form.sort" controls-position="right" :min="1" :max="9999" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.maxRange{
  :deep(.el-form-item__content){
    margin-left: 0 !important;
  }
}
</style>
