import { useRequest } from '@/use/useRequest'

// 获取列表  hcscm/admin/v1/fpmQualityCheckoutReport/list
export function GetqualityInspectionReportList() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport/list',
    method: 'get',
  })
}

// 获取详情  hcscm/admin/v1/fpmQualityCheckoutReport/list
export function GetqualityInspectionReport() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport',
    method: 'get',
  })
}

// Get 获取缸号 /hcscm/admin/v1/product/fpmQualityCheck/listByDyelotNumber
export function GetListByDyelotNumber() {
  return useRequest({
    url: '/admin/v1/product/fpmQualityCheck/listByDyelotNumber',
    method: 'get',
  })
}

// 新增质检报告 http://192.168.1.24:50001/hcscm/admin/v1/fpmQualityCheckoutReport
export function SetFpmQualityCheckoutReport() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport',
    method: 'post',
  })
}
// 编辑质检报告
export function PutFpmQualityCheckoutReport() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport',
    method: 'put',
  })
}

// 审核 /hcscm/admin/v1/product/fpmQualityCheckoutReport/pass
export function FpmQualityCheckoutReportPass() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport/pass',
    method: 'put',
  })
}

// 驳回 //hcscm/admin/v1/fpmQualityCheckoutReport/reject
export function FpmQualityCheckoutReportReject() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport/reject',
    method: 'put',
  })
}

// 消审 /hcscm/admin/v1/fpmQualityCheckoutReport/wait
export function FpmQualityCheckoutReportWait() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport/wait',
    method: 'put',
  })
}

// 作废 /hcscm/admin/v1/fpmQualityCheckoutReport/cancel
export function FpmQualityCheckoutReportCancel() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport/cancel',
    method: 'put',
  })
}

// 打印 http://192.168.1.24:50001/hcscm/admin/v1/fpmQualityCheckoutReport/print?id=1811699441425408
export function FpmQualityCheckoutReportPrint() {
  return useRequest({
    url: '/admin/v1/fpmQualityCheckoutReport/print',
    method: 'get',
  })
}
