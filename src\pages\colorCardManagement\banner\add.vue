<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import { truncate } from 'lodash-es'
import { useRouter } from 'vue-router'
import { AddCarouselBanner } from '@/api/colorCardManagement/banner'
import { formatHashTag } from '@/common/format'
import { getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { ColorCardJumpTypeEnum, ColorCardStatusEnum } from '@/enum/colorCardEnum'
import UploadFile from '@/components/UploadFile/index.vue'
import { imgTypeAccept, videoTypeAccept } from '@/common/uploadImage'
import AccordingLibAdd from '@/pages/finishManagement/components/AccordingLibAdd.vue'
import { formValidatePass } from '@/common/rule'

const router = useRouter() // 路由跳转

const state = reactive<any>({
  form: {
    jump_type: '',
    title: '',
    targetTitle: '',
    sort: 0, // 默认0
    status: ColorCardStatusEnum.Enable, // 默认启用,
    logo: [],
    imgList: [],
    videoList: [],
    target_id: '',
  },
  formRules: {
    title: [{ required: true, message: '请输入轮播图标题', trigger: 'blur' }],
    logo: [{ required: true, message: '请选择轮播图', trigger: 'blur' }],
    jump_type: [{ required: true, message: '请选择跳转类型', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'blur', validator: formValidatePass.id(true) }],
  },
})

const ruleFormRef = ref()

// 提交表格数据
async function handleSure() {
  ruleFormRef.value.validate((valid: any) => {
    if (valid) {
      const { logo, imgList, jump_type, videoList, target_id } = state.form
      // 轮播图
      if (!logo || !logo.length)
        return ElMessage.error('请上传轮播图')
      else if (!target_id && jump_type === ColorCardJumpTypeEnum.DETAIL)
        return ElMessage.error('请选择跳转目标')
      else if (!imgList.length && jump_type === ColorCardJumpTypeEnum.IMG)
        return ElMessage.error('请上传图片预览')
      else if (!videoList.length && jump_type === ColorCardJumpTypeEnum.VIDEO)
        return ElMessage.error('请上传视频预览')
      handSubmit()
    }
  })
}

const { fetchData: postFetch, data: addData, success: postSuccess, msg: postMsg } = AddCarouselBanner()
async function handSubmit() {
  const data = {
    ...state.form,
    prev_view_url: state.form.logo?.[0],
    video_url: state.form.videoList?.[0],
    img_url: state.form.imgList?.[0],
    target_id: state.form.target_id || '',
    link: state.form.jump_type === ColorCardJumpTypeEnum.DETAIL ? state.form.targetTitle || '' : '',
  }
  await postFetch(getFilterData(data))
  if (postSuccess.value) {
    ElMessage.success('添加成功')
    router.push({
      name: 'ColorCardBannerDetail',
      params: { id: addData.value.id },
    })
  }
  else {
    ElMessage.error(postMsg.value)
  }
}
// 选择跳转方式
function onChangeJumpType(val: any) {
  state.form.targetTitle = val.name
  // 清空选择的数据
  state.form.target_id = ''
  state.form.imgList = []
  state.form.videoList = []
}
const AccordingLibAddRef = ref()
// 根据资料添加
function showFinishProductionDialog() {
  AccordingLibAddRef.value.tableConfig.showCheckBox = false
  AccordingLibAddRef.value.tableConfig.showRadio = true
  AccordingLibAddRef.value.state.showModal = true
}
// 选择面料
function handleSelectProduct(list: any) {
  const selectProduct = list?.[0]
  state.form.target_id = selectProduct?.id
  // state.form.targetTitle = selectProduct?.id
  state.form.targetTitle = formatHashTag(selectProduct?.finish_product_code, selectProduct?.finish_product_name)
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :inline="true" :model="state.form" :rules="state.formRules">
      <el-form-item prop="title" label="轮播图标题:" class="w-[20%]" required>
        <el-input v-model.trim="state.form.title" clearable placeholder="请输入轮播图标题" />
      </el-form-item>
      <el-form-item prop="paper_tube_specs" label="排序:">
        <div class="flex items-center">
          <el-input-number v-model="state.form.sort" :max="999" :min="0" size="small" />
          <span class="text-[#999] font-[12px] ml-2">0 ~ 999 数值设置越大 排序越靠前</span>
        </div>
      </el-form-item>
      <el-form-item prop="jump_type" label="跳转类型:">
        <SelectComponents
          v-model="state.form.jump_type"
          size="small"
          label-field="name"
          value-field="id"
          clearable
          api="GetCarouselBannerJumpType"
          @change-value="onChangeJumpType"
        />
      </el-form-item>
      <el-form-item prop="target_id" label="跳转目标:">
        <div class="flex items-center">
          <el-input v-model.trim="state.form.targetTitle" disabled style="width: 200px" type="text" />
          <el-button v-if="[ColorCardJumpTypeEnum.DETAIL].includes(state.form.jump_type)" class="ml-1" type="primary" @click="showFinishProductionDialog">
            选择面料
          </el-button>
        </div>
      </el-form-item>
      <el-form-item prop="status" label="状态:">
        <SelectComponents v-model="state.form.status" style="width: 200px" api="GetCarouselBannerStatus" label-field="name" value-field="id" clearable />
      </el-form-item>
      <el-form-item prop="remark" label="备注:" class="w-[20%]">
        <el-input v-model.trim="state.form.remark" type="textarea" clearable placeholder="备注" :autosize="{ minRows: 2, maxRows: 6 }" show-word-limit :maxlength="240" />
      </el-form-item>
    </el-form>
  </FildCard>
  <FildCard title="" :tool-bar="false" class="mt-[5px]">
    <template #title>
      <span class="text-[var(--el-color-danger)]">*</span>轮播图
    </template>
    <UploadFile
      v-model:file-list="state.form.logo"
      :auto-upload="true"
      :multiple="false"
      :show-submit-btn="false"
      additional-text="建议尺寸：200*200px"
      :accept="imgTypeAccept"
    />
  </FildCard>
  <FildCard v-if="[ColorCardJumpTypeEnum.IMG].includes(state.form.jump_type)" title="预览图片" :tool-bar="false" class="mt-[5px]">
    <UploadFile
      v-model:file-list="state.form.imgList"
      :auto-upload="true"
      :multiple="false"
      :show-submit-btn="false"
      :accept="imgTypeAccept"
    />
  </FildCard>
  <FildCard v-if="[ColorCardJumpTypeEnum.VIDEO].includes(state.form.jump_type)" title="预览视频" :tool-bar="false" class="mt-[5px]">
    <UploadFile
      v-model:file-list="state.form.videoList"
      :auto-upload="true"
      :multiple="false"
      :show-submit-btn="false"
      :accept="videoTypeAccept"
    />
  </FildCard>
  <AccordingLibAdd
    ref="AccordingLibAddRef"
    @handle-sure="handleSelectProduct"
  />
</template>

<style lang="scss" scoped></style>
