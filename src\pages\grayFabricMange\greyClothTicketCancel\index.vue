<script setup lang="ts">
import { useRouter } from 'vue-router'
import FabricFlyCancelContent from './components/FabricFlyCancelContent.vue'

const router = useRouter()

// 处理成功事件
function handleSuccess(data: { barcode: string }) {
  console.log('布飞取消成功:', data)
  // 可以在这里添加其他成功后的处理逻辑
}

// 处理取消事件
function handleCancel() {
  router.go(-1)
}
</script>

<template>
  <FabricFlyCancelContent
    mode="page"
    title="布飞取消"
    @success="handleSuccess"
    @cancel="handleCancel"
  />
</template>

<style scoped>
/* 页面级别的样式可以在这里添加 */
</style>
