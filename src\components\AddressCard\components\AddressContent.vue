<script setup lang="ts">
import { RefreshLeft } from '@element-plus/icons-vue'
import type { AddressTypes } from '@/api/addressCard'

defineProps<{
  addressList: AddressTypes[]
  currentAddress: AddressTypes
  showOperations: boolean
}>()

const emit = defineEmits<{
  create: []
  change: []
}>()
</script>

<template>
  <template v-if="addressList.length || JSON.stringify(currentAddress) !== '{}'">
    <el-form-item class="form-init-items" style="margin-bottom: 0;">
      <el-text>收货地址：</el-text>
      <el-text>{{ currentAddress.location.join(" ") }} {{ currentAddress.address }}</el-text>
    </el-form-item>

    <el-form-item class="form-init-items" style="margin-bottom: 0;">
      <template
        v-for="(item, key) in {
          联系人: `${currentAddress.contact_name} ${currentAddress.phone}`,
          物流公司: currentAddress.logistics_company,
          物流区域: currentAddress.logistics_area,
          加工厂: currentAddress.name,
        }" :key="key"
      >
        <template v-if="item">
          <el-text>{{ key }}：</el-text>
          <el-text>{{ item }}</el-text>
          <el-divider direction="vertical" />
        </template>
      </template>

      <el-link
        v-if="showOperations"
        type="primary"
        :underline="false"
        @click="emit('change')"
      >
        更换地址<el-icon><RefreshLeft /></el-icon>
      </el-link>
    </el-form-item>
  </template>

  <el-form-item v-else-if="showOperations">
    <el-text>
      该用户未添加地址，请先添加地址
    </el-text>
  </el-form-item>
</template>
