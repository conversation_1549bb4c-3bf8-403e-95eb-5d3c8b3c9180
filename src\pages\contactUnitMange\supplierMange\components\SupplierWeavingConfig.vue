<script lang="ts" setup>
import {
  BFSequenceNumberRuleEnum,
  BFSequenceNumberRuleLabels,
  WeavingStorageMethodEnum,
  WeavingStorageMethodLabels,
  WeighingAndFabricInspectionProcessEnum,
  WeighingAndFabricInspectionProcessLabels,
} from '@/common/enum'

interface Props {
  modelValue: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理表单数据更新
function updateForm(field: string, value: any) {
  emit('update:modelValue', { ...props.modelValue, [field]: value })
}
</script>

<template>
  <div>
    <div class="sale">
      织造配置
    </div>
    <div class="line" />

    <el-form-item label="织厂布飞前缀：" prop="fabric_prefix">
      <el-input
        :model-value="modelValue.fabric_prefix"
        style="width: 200px"
        placeholder="请输入织厂布飞前缀"
        @input="updateForm('fabric_prefix', $event)"
      />
    </el-form-item>

    <el-form-item label="称重验布流程：" prop="weighing_inspection_process">
      <el-radio-group
        :model-value="modelValue.weighing_inspection_process"
        @update:model-value="updateForm('weighing_inspection_process', $event)"
      >
        <el-radio :value="WeighingAndFabricInspectionProcessEnum.INSPECTION">
          {{ WeighingAndFabricInspectionProcessLabels[WeighingAndFabricInspectionProcessEnum.INSPECTION] }}
        </el-radio>
        <el-radio :value="WeighingAndFabricInspectionProcessEnum.DROP_AND_INSPECTION">
          {{ WeighingAndFabricInspectionProcessLabels[WeighingAndFabricInspectionProcessEnum.DROP_AND_INSPECTION] }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="织造入库方式：" prop="weaving_storage_method">
      <el-radio-group
        :model-value="modelValue.weaving_storage_method"
        @update:model-value="updateForm('weaving_storage_method', $event)"
      >
        <el-radio :value="WeavingStorageMethodEnum.ONE_PERSON_ONE_ORDER">
          {{ WeavingStorageMethodLabels[WeavingStorageMethodEnum.ONE_PERSON_ONE_ORDER] }}
        </el-radio>
        <el-radio :value="WeavingStorageMethodEnum.BY_SHIFT">
          {{ WeavingStorageMethodLabels[WeavingStorageMethodEnum.BY_SHIFT] }}
        </el-radio>
        <el-radio :value="WeavingStorageMethodEnum.SCAN_CODE">
          {{ WeavingStorageMethodLabels[WeavingStorageMethodEnum.SCAN_CODE] }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="布飞卷号规则：" prop="fabric_roll_number_rule">
      <el-radio-group
        :model-value="modelValue.fabric_roll_number_rule"
        @update:model-value="updateForm('fabric_roll_number_rule', $event)"
      >
        <el-radio :value="BFSequenceNumberRuleEnum.PRODUCTION_ORDER">
          {{ BFSequenceNumberRuleLabels[BFSequenceNumberRuleEnum.PRODUCTION_ORDER] }}
        </el-radio>
        <el-radio :value="BFSequenceNumberRuleEnum.MACHINE">
          {{ BFSequenceNumberRuleLabels[BFSequenceNumberRuleEnum.MACHINE] }}
        </el-radio>
        <el-radio :value="BFSequenceNumberRuleEnum.PRODUCTION_ORDER_AND_MACHINE">
          {{ BFSequenceNumberRuleLabels[BFSequenceNumberRuleEnum.PRODUCTION_ORDER_AND_MACHINE] }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
  </div>
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}

.sale {
  margin-top: 40px;
  font-weight: 600;
}
</style>
