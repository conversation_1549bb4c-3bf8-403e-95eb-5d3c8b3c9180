<script setup lang="ts">
import Decimal from 'decimal.js'
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import SelectRawDialog from './SelectRawDialog.vue'
import SelectStockDialog from './SelectStockDialog.vue'
import { getProductStockDetail } from '@/api/materialPlan'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatPriceDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import { deleteToast } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { typeDisabledStrategy } from '@/pages/saleManagement/components/shared'

const props = withDefaults(defineProps<{ isEdit: boolean }>(), {
  isEdit: true,
})

const emits = defineEmits(['handleSure'])

const tableConfig = ref({
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: true,
  operateWidth: '200',
  fieldApiKey: 'RawMaterialPlanEditDrog',
})

const componentRemoteSearch = reactive({
  name: '',
  code: '',
  customer_name: '',
  unit_name: '',
  raw_unit_name: '',
  grey_unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
  weave_factory_name: '',
  dye_factory_name: '',
  product_unit_name: '',
})

const state = reactive<any>({
  showModal: false,
  customer_name: '',
  customer_id: '',
  modalName: '计划分配',
  tableData: [],
  row_id: -1,
  isDisabled: false,
  rowIndex: -1,
  info: {},
  unit_id: '',
})

// watch(
//   () => state.showModal,
//   () => {
//     if (state.showModal && !state.tableData.length) {
//       nextTick(() => {
//         state.tableData.push({
//           raw_material_id: state.rowInfo.raw_material_id,
//           raw_material_color_code: state.rowInfo?.grey_fabric_color_id,
//           product_supplier_id: '',
//           roll: state.horsepower,
//           dye_raw_material_factory_id: '',
//           weave_factory_name_id: '',
//           push_type: '',
//           weight: state.horsepower,
//           raw_material_supplier_id: '',
//           delivery_time: '',
//         })
//       })
//     }
//   }
// )

const columnList = ref([
  {
    field: 'push_type',
    title: '计划类型',
    minWidth: 140,
    soltName: 'push_type',
    fixed: 'left',
    required: true,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 140,
    soltName: 'weight',
    required: true,
  },
  {
    field: 'raw_material_id',
    title: '用纱编号',
    minWidth: 140,
    soltName: 'code',
  },
  {
    field: 'raw_material_name',
    title: '用纱名称',
    minWidth: 140,
  },
  {
    field: 'raw_material_color_code',
    title: '用纱色号',
    minWidth: 140,
    soltName: 'raw_material_color_code',
  },
  {
    field: 'raw_material_color_name',
    title: '用纱颜色',
    minWidth: 140,
    soltName: 'raw_material_color_name',
  },
  {
    field: 'unit_name',
    title: '用纱单位',
    minWidth: 140,
  },
  {
    field: 'raw_material_weight',
    title: '用纱数量',
    minWidth: 140,
    soltName: 'raw_material_weight',
  },
  {
    field: 'shipping_unit_id',
    title: '出货单位',
    minWidth: 140,
    soltName: 'shipping_unit_id',
  },
  // {
  //   field: 'roll',
  //   title: '匹数',
  //   minWidth: 140,
  //   soltName: 'horsepower',
  // },

  {
    field: 'raw_material_supplier_id',
    title: '原料供应商',
    minWidth: 140,
    soltName: 'raw_material_supplier_id',
  },
  {
    field: 'weave_factory_id',
    title: '织厂',
    minWidth: 140,
    soltName: 'weave_factory_id',
  },
  {
    field: 'dye_raw_material_factory_id',
    title: '染纱厂',
    minWidth: 140,
    soltName: 'dye_raw_material_factory_id',
  },
  {
    field: 'delivery_time',
    title: '交期',
    minWidth: 140,
    soltName: 'delivery_time',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 140,
    soltName: 'remark',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  // if (state.total_roll > state.canEnter) {
  //   return ElMessage.error('匹数不可大于原库存匹数')
  // }
  for (let i = 0; i < state.tableData.length; i++) {
    if (state.tableData[i].push_type === '' || state.tableData[i].weight === '')
      return ElMessage.error('计划类型和数量是必填内容')

    if (state.tableData[i].push_type === 1 && state.tableData[i].shipping_unit_id === '')
      return ElMessage.error('出货单位不可以为空')

    if (state.tableData[i].raw_material_id !== '' && state.tableData[i].raw_material_weight === '')
      return ElMessage.error('用纱数量不可以为空')
  }
  emits('handleSure', state)
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property)) {
        state.total_roll = sumTotal(data, 'roll')
        state.plan_roll = state.total_roll
        return `${sumNum(data, 'roll')}`
      }
      if (['weight'].includes(column.property)) {
        state.plan_weight = sumTotal(data, 'weight')
        return `${sumNum(data, 'weight')}`
      }
      return null
    }),
  ]
}

const tableRef = ref()

watch(
  () => state.tableData,
  () => {
    if (state.tableData.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handAdd() {
  // if (state.total_roll >= state.canEnter) {
  //   return ElMessage.error('计划匹数不能大于需求匹数')
  // }
  state.tableData.push({
    raw_material_id: '',
    raw_material_name: '',
    raw_material_color_code: '',
    // product_supplier_id: '',
    roll: 0,
    dye_raw_material_factory_id: '',
    weave_factory_name_id: '',
    push_type: '',
    weight: 0,
    raw_material_supplier_id: '',
    delivery_time: '',
    count: 0,
    is_new: true,
    raw_material_color_name: '',
    unit_name: '',
    raw_material_weight: '',
    shipping_unit_id: '',
  })
  state.tableData = [...state.tableData]
}

const SelectStockDialogRef = ref()

const { fetchData, data } = getProductStockDetail()

async function handSelect(row: any, rowIndex: number) {
  SelectStockDialogRef.value.state.showModal = true
  SelectStockDialogRef.value.state.rowIndex = rowIndex
  SelectStockDialogRef.value.state.useWeight = row?.weight
  SelectStockDialogRef.value.state.filterData.raw_material_id = state.info?.raw_material_id
  SelectStockDialogRef.value.state.filterData.supplier_id = state.customer_id
  state.unit_id = row?.shipping_unit_id

  if (row.count === 0)
    await fetchData({ id: row.id })

  const arr = row.count > 0 ? row?.stock_message_list : data?.value?.list
  const newArr = arr.map((item: any) => {
    // item.is_edit_id = !!row?.is_new
    // item.edit_id = row?.is_new ? item.id : null
    item.id = item.stock_product_id
    item.pmc_weight = row.count === 0 ? formatWeightDiv(item.pmc_weight) : item.pmc_weight
    item.pmc_bulk_piece_count = row.count === 0 ? formatPriceDiv(item.pmc_bulk_piece_count) : item.pmc_bulk_piece_count
    item.pmc_whole_piece_count = row.count === 0 ? formatPriceDiv(item.pmc_whole_piece_count) : item.pmc_whole_piece_count
    return item
  })

  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.stock_message_list = newArr
      item.count += 1
      return item
    }
  })
  SelectStockDialogRef.value.state.multipleSelection = newArr
}

async function handSeeStock(row: any) {
  await fetchData({ id: row.id })
  SelectStockDialogRef.value.state.showModal = true

  data?.value?.list.map((item: any) => {
    // item.is_edit_id = !!row?.is_new
    // item.edit_id = row?.is_new ? item.id : null
    item.pmc_weight = formatWeightDiv(item.pmc_weight)
    return item
  })

  SelectStockDialogRef.value.state.multipleSelection = data?.value?.list
}

const SelectComponentsRef = ref()

async function changType(val: any, rowIndex: number, row: any) {
  if (val.id !== 1 && row.old_push_type === 1 && row.push_type !== '') {
    const res = await deleteToast('确认更换计划类型？已选的库存信息将被清空')
    if (res) {
      state.tableData.map((item: any, index: number) => {
        if (index === rowIndex) {
          item.stock_message_list = []
          return item
        }
      })
      SelectComponentsRef.value.SelectOptionsRef.selectRef.blur()
    }
    else {
      state.tableData.map((item: any, index: number) => {
        if (index === rowIndex) {
          item.push_type = 1
          return item
        }
      })
      SelectComponentsRef.value.SelectOptionsRef.selectRef.blur()
    }
  }
  // TODO: 记住选择前的值以便重置
  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.old_push_type = val.id
      return item
    }
  })
}

function handSureStock(list: any, rowIndex: number) {
  // const arr = list.map((item: any) => {
  //   item.pmc_roll = item.use_roll
  //   return item
  // })
  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.stock_message_list = list
      // item.raw_material_weight = list.reduce((acc, cur) => currency(acc).add(cur.pmc_weight).value, 0)
      return item
    }
  })
  SelectStockDialogRef.value.state.showModal = false
}

function handDelete(rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}

const SelectRawDialogRef = ref()

function focusCode(rowIndex: number) {
  SelectRawDialogRef.value.state.showModal = true
  SelectRawDialogRef.value.state.rowIndex = rowIndex
  SelectRawDialogRef.value.state.info = state.info
}

function handleSureRaw(val: any) {
  state.tableData.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.raw_material_name = val.multipleSelection[0].name
      item.raw_material_code = val.multipleSelection[0].code
      item.raw_material_id = val.multipleSelection[0].src_raw_matl_id || val.multipleSelection[0].id

      item.unit_name = val.multipleSelection[0].unit_name

      const nums = new Decimal(1).plus(new Decimal(formatWeightDiv(val.multipleSelection[0]?.wastage)))

      item.raw_material_weight = new Decimal(item.weight).times(new Decimal(formatWeightDiv(val.multipleSelection[0]?.ratio))).times(new Decimal(nums)) || ''

      return item
    }
  })
  SelectRawDialogRef.value.state.showModal = false
}
function handleChangeMaterialColor(val: any, row: any) {
  row.raw_material_color_name = val.name
}
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="80vw" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="原料编号:">
        <template #content>
          {{ state.info?.raw_material_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料名称:">
        <template #content>
          {{ state.info?.raw_material_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料类型:">
        <template #content>
          {{ state.info?.type_name }}
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="原料成分:">
        <template #content>
          {{ state.info?.ingredient }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料工艺:">
        <template #content>
          {{ state.info?.craft }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="原料支数:">
        <template #content>
          {{ state.info?.count }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色编号:">
        <template #content>
          {{ state.info?.raw_material_color_code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色名称:">
        <template #content>
          {{ state.info?.raw_material_color_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单位:">
        <template #content>
          {{ state.info?.measurement_unit_name }}
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="数量:">
        <template #content>
          {{ formatWeightDiv(state.info?.weight) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="备注:">
        <template #content>
          {{ state.info?.remark }}
        </template>
      </DescriptionsFormItem>
    </div>

    <FildCard title="" class="mt-[5px]" no-shadow>
      <template #right-top>
        <div v-if="props.isEdit" class="buttom-oper">
          <el-button type="primary" @click="handAdd()">
            新增
          </el-button>
        </div>
      </template>
      <Table ref="tableRef" :config="tableConfig" :column-list="columnList" :table-list="state.tableData">
        <!-- 用纱编号 -->
        <template #code="{ row, rowIndex }">
          <SelectComponents
            v-model="row.raw_material_code"
            visible-change-close
            :disabled="!props.isEdit || row.push_type !== 6"
            api="GetGreyFabricInfoListUseByOthersMenu"
            label-field="code"
            value-field="id"
            clearable
            @focus="focusCode(rowIndex)"
          />
        </template>
        <template #shipping_unit_id="{ row }">
          <SelectDialog
            v-model="row.shipping_unit_id"
            :disabled="!props.isEdit"
            api="BusinessUnitSupplierEnumlist"
            :label-name="row.shipping_unit_name"
            label-field="name"
            :query="{ unit_type_id: '11,20', name: componentRemoteSearch.unit_name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.unit_name = val)"
          />
        </template>
        <template #raw_material_color_code="{ row }">
          <SelectDialog
            v-model="row.raw_material_color_id"
            api="GetlistFactoryEnum"
            label-field="code"
            :query="{ raw_matl_id: row.raw_material_id, code: row.color_code }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'code',
                title: '原料色号',
                minWidth: 100,
              },
            ]"
            @on-input="val => (row.color_code = val)"
            @change-value="handleChangeMaterialColor($event, row)"
          />
        </template>
        <template #raw_material_color_name="{ row }">
          {{ row.raw_material_color_name }}
        </template>
        <template #product_supplier_id="{ row }">
          <SelectDialog
            v-model="row.product_supplier_id"
            :disabled="!props.isEdit"
            :label-name="row.product_supplier_name"
            :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct, name: componentRemoteSearch.product_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.product_unit_name = val)"
          />
        </template>
        <template #raw_material_supplier_id="{ row }">
          <SelectDialog
            v-model="row.raw_material_supplier_id"
            :label-name="row.raw_material_supplier_name"
            :disabled="!props.isEdit || row.push_type !== 7"
            :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial, name: componentRemoteSearch.raw_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.raw_unit_name = val)"
          />
        </template>
        <template #weave_factory_id="{ row }">
          <SelectDialog
            v-model="row.weave_factory_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'weave_factory_id')"
            :label-name="row.weave_factory_name"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.weave_factory_name }"
            api="GetBusinessUnitListApi"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.weave_factory_name = val)"
          />
        </template>
        <template #grey_fabric_supplier_id="{ row }">
          <SelectDialog
            v-model="row.grey_fabric_supplier_id"
            :label-name="row.grey_fabric_supplier_name"
            :disabled="!props.isEdit"
            :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric, name: componentRemoteSearch.grey_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.grey_unit_name = val)"
          />
        </template>
        <template #horsepower="{ row }">
          <vxe-input v-model="row.roll" :disabled="!props.isEdit" type="float" placeholder="" />
        </template>
        <template #weight="{ row }">
          <vxe-input v-model="row.weight" :disabled="!props.isEdit" type="float" placeholder="" />
        </template>
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" :disabled="!props.isEdit" placeholder="" />
        </template>

        <template #raw_material_weight="{ row }">
          <vxe-input v-model="row.raw_material_weight" :disabled="!props.isEdit" type="float" placeholder="" />
        </template>
        <template #dye_raw_material_factory_id="{ row }">
          <SelectDialog
            v-model="row.dye_raw_material_factory_id"
            :label-name="row.dye_raw_material_factory_name"
            :disabled="!props.isEdit || row.push_type !== 6"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill, name: componentRemoteSearch.dye_factory_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.dye_factory_name = val)"
          />
        </template>

        <template #weave_factory_name_id="{ row }">
          <SelectDialog
            v-model="row.weave_factory_name_id"
            :disabled="!props.isEdit"
            :label-name="row.weave_factory_name_name"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.weave_factory_name }"
            api="GetBusinessUnitListApi"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.weave_factory_name = val)"
          />
        </template>
        <template #delivery_time="{ row }">
          <vxe-input v-model="row.delivery_time" :disabled="!props.isEdit" type="date" placeholder="" transfer />
        </template>
        <!--        计划类型 -->
        <template #push_type="{ row, rowIndex }">
          <SelectComponents
            ref="SelectComponentsRef"
            v-model="row.push_type"
            :disabled="!props.isEdit"
            api="PushType"
            label-field="name"
            value-field="id"
            :query="{ plan_type: 3 }"
            clearable
            @change-value="val => changType(val, rowIndex, row)"
          />
        </template>
        <template #operate="{ row, rowIndex }">
          <el-button v-if="props.isEdit && row.push_type === 1" text type="primary" @click="handSelect(row, rowIndex)">
            选取库存
          </el-button>
          <el-button v-if="!props.isEdit && row.push_type === 1" text type="primary" @click="handSeeStock(row)">
            查看库存
          </el-button>
          <el-button v-if="props.isEdit" text type="danger" @click="handDelete(rowIndex)">
            删除
          </el-button>
        </template>
      </Table>
    </FildCard>
    <template #footer>
      <div v-if="props.isEdit" class="buttom-oper" style="margin-top: 20px">
        <el-button @click="handCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleSure">
          确认
        </el-button>
      </div>
    </template>
  </vxe-modal>

  <SelectStockDialog ref="SelectStockDialogRef" :is-edit="props.isEdit" :unit_id="state.unit_id" @submit="handSureStock" />
  <SelectRawDialog ref="SelectRawDialogRef" @handle-sure="handleSureRaw" />
</template>

<style></style>
