<script lang="ts" setup name="DyeingPayEdit">
import { onMounted, reactive, ref, watch, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import FildCard from '@/components/FildCard.vue'
import { dnfdetail, dnfput } from '@/api/dyeingPay'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { formatDate, formatLengthDiv, formatLengthMul, formatPriceDiv, formatPriceMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deepClone, getCurrentDate } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import { DnfChargingMethod } from '@/enum'
import RollInfo from '@/pages/MeetMange/dyeingPay/components/rollInfo.vue'

const routerList = useRouterList()
const rourte = useRoute()

const state = reactive<any>({
  form: {
    src_order_type_name: '',
    src_order_type: '',
    sale_system_name: '',
    voucher_num: '',
    remark: '',
    src_order_no: '',
    pay_date: '',
    sale_system_id: '',
    src_order_id: '',
    supplier_id: '',
    handler_id: '',
  },
  tableList: [],
  useList: [],
  formRules: {
    pay_date: [{ required: true, message: '请选择应付日期', trigger: 'blur' }],
  },
})

const rollInfoRef = ref()

const { fetchData, data } = dnfdetail()

onMounted(async () => {
  await fetchData({ id: rourte.query.id })
  if (data.value) {
    state.form.src_order_type_name = data.value?.src_order_type_name
    state.form.src_order_type = data.value?.src_order_type
    state.form.sale_system_name = data.value?.sale_system_name
    state.form.voucher_num = data.value?.voucher_num
    state.form.remark = data.value?.remark
    state.form.src_order_no = data.value?.src_order_no
    if (data.value?.pay_date === '')
      state.form.pay_date = formatDate(data.value?.pay_date)
    else
      state.form.pay_date = getCurrentDate()

    state.form.sale_system_id = data.value?.sale_system_id
    state.form.supplier_id = data.value?.supplier_id
    state.form.handler_id = data.value?.handler_id || ''

    state.tableList = data.value.items
    state.tableList.map((item: any) => {
      item.roll = formatPriceDiv(item.roll)
      // item.total_weight = formatWeightDiv(item.total_weight)
      item.total_length = formatLengthDiv(item.total_length)
      item.gf_roll = formatPriceDiv(item.gf_roll)
      item.gf_weight = formatWeightDiv(item.gf_weight)
      item.dyeing_price = formatPriceDiv(item.dyeing_price)
      item.finishing_price = formatPriceDiv(item.finishing_price)
      item.paper_tube_price = formatPriceDiv(item.paper_tube_price)
      item.plastics_bag_price = formatPriceDiv(item.plastics_bag_price)
      item.price = formatPriceDiv(item.price)
      item.piece_count = formatPriceDiv(item.piece_count)
      item.weight = formatWeightDiv(item.weight)
      item.length = [DnfChargingMethod.DnfWeight, DnfChargingMethod.finishProductWeight].includes(data.value?.dnf_charging_method) ? '' : formatLengthDiv(item.length)
      item.dnf_unit_price = formatUnitPriceDiv(item.dnf_unit_price)
      item.finishing_unit_price = formatUnitPriceDiv(item.finishing_unit_price)
      item.paper_tube_unit_price = formatUnitPriceDiv(item.paper_tube_unit_price)
      item.plastic_bag_unit_price = formatUnitPriceDiv(item.plastic_bag_unit_price)
      item.other_price = formatPriceDiv(item.other_price)

      // item.paper_tube_price = 0
      // item.plastics_bag_price = 0
      // item.plastics_bag_price = item?.plastic_bag_unit_price * item?.piece_count
      // item.price = 0
      return item
    })
  }
})

// watch(
//   () => state.tableList,
//   () => {
//     if (state.tableList.length) {
//       state.tableList.map((item: any) => {
//         if ([1, 3].includes(data.value?.dnf_charging_method)) {
//           item.dyeing_price = formatPriceMul(Number(item.dnf_unit_price) * Number(item.weight)).toFixed(2)
//           item.finishing_price = formatPriceMul(Number(item.finishing_unit_price) * Number(item.weight)).toFixed(2)
//         } else {
//           item.dyeing_price = formatPriceMul(Number(item.dnf_unit_price) * Number(item.length)).toFixed(2)
//           item.finishing_price = formatPriceMul(Number(item.finishing_unit_price) * Number(item.length)).toFixed(2)
//         }
//         item.paper_tube_price = formatPriceMul(Number(item.paper_tube_unit_price) * Number(item.piece_count)).toFixed(2)
//
//         item.plastics_bag_price = formatPriceMul(Number(item.plastic_bag_unit_price) * Number(item.piece_count)).toFixed(2)
//         item.price = Number(item.dyeing_price) + Number(item.finishing_price) + Number(item.paper_tube_price) + Number(item.plastics_bag_price) + +Number(item.other_price)
//         return item
//       })
//     }
//   },
//   { deep: true }
// )
watchEffect(() => {
  state.tableList.map((item: any) => {
    if ([DnfChargingMethod.DnfWeight, DnfChargingMethod.finishProductWeight].includes(data.value?.dnf_charging_method)) {
      item.dyeing_price = currency(item.dnf_unit_price).multiply(item.weight).value
      item.finishing_price = currency(item.finishing_unit_price).multiply(item.weight).value
    }
    else {
      item.dyeing_price = currency(item.dnf_unit_price).multiply(item.length).value
      item.finishing_price = currency(item.finishing_unit_price).multiply(item.length).value
    }
    item.paper_tube_price = currency(item.paper_tube_unit_price).multiply(item.piece_count).value
    item.plastics_bag_price = currency(item.plastic_bag_unit_price).multiply(item.piece_count).value
    item.price = currency(item.dyeing_price).add(item.finishing_price).add(item.paper_tube_price).add(item.plastics_bag_price).add(item.other_price).value
    return item
  })
})

const tableConfig = ref({
  showSlotNums: true,
  height: '100%',
  fieldApiKey: fieldApiKeyList.DyeingPayEdit,
  showSpanHeader: true,
  // cellDBLClickEvent: (val: any) => cellDBLClickEvent(val),
  footerMethod: (val: any) => FooterMethod(val),
})
const tableRef = ref()
watch(() => state.tableList, () => {
  tableRef.value.tableRef?.updateFooter()
}, {
  deep: true,
})

// const tableConfig_use = ref({
//   showSlotNums: true,
//   height: 400,
// })
//
// const tableConfig_return = ref({
//   showSlotNums: true,
//   height: 400,
// })

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['gf_roll', 'gf_weight', 'piece_count', 'weight', 'length'].includes(column.property))
        return `${sumNum(data, column.property)}`

      return null
    }),
  ]
}

// const { fetchData: detailFetch, data: detalData } = notice_orderdetail()

// async function cellDBLClickEvent(val: any) {
//   state.useList = []
//   await detailFetch({ id: val.row.dnf_order_id })
//   if (detalData.value) {
//     let arr = []
//     state.useList = detalData.value.items[0]?.use_fabric || []
//     arr = state.useList?.map((item: any) => {
//       return {
//         product_code: detalData.value.items[0]?.code,
//         product_name: detalData.value.items[0]?.name,
//         color_no: detalData.value.items[0]?.color_no,
//         color_name: detalData.value.items[0]?.color_name,
//         dyelot: detalData.value.items[0]?.dyelot,
//         dye_piece_count: detalData.value.items[0]?.piece_count,
//         dye_weight: detalData.value.items[0]?.weight,
//
//         ...item?.gf_stock_info,
//         ...item,
//       }
//     })
//     state.useList = arr
//     state.returnList = arr
//   }
// }

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = dnfput()

// 提交数据
async function handleSure() {
  const list = deepClone(state.tableList)
  for (let i = 0; i < list.length; i++) {
    list[i].roll = formatPriceMul(list[i].roll)
    // list[i].total_weight = formatWeightMul(list[i].total_weight)
    list[i].total_length = formatLengthMul(list[i].total_length)
    list[i].gf_roll = formatPriceMul(list[i].gf_roll)
    list[i].gf_weight = formatWeightMul(list[i].gf_weight)
    list[i].dyeing_price = formatPriceMul(list[i].dyeing_price)
    list[i].finishing_price = formatPriceMul(list[i].finishing_price)
    list[i].paper_tube_price = formatPriceMul(list[i].paper_tube_price)
    list[i].plastics_bag_price = formatPriceMul(list[i].plastics_bag_price)
    list[i].price = formatPriceMul(list[i].price)
    list[i].piece_count = formatPriceMul(list[i].piece_count)
    list[i].weight = formatWeightMul(list[i].weight)
    list[i].length = formatLengthMul(list[i].length)
    list[i].dnf_unit_price = formatUnitPriceMul(list[i].dnf_unit_price)
    list[i].finishing_unit_price = formatUnitPriceMul(list[i].finishing_unit_price)
    list[i].paper_tube_unit_price = formatUnitPriceMul(list[i].paper_tube_unit_price)
    list[i].plastic_bag_unit_price = formatUnitPriceMul(list[i].plastic_bag_unit_price)
    list[i].other_price = formatPriceMul(list[i].other_price)
  }
  state.form.pay_date = formatDate(state.form.pay_date)
  const query = {
    src_order_no: data?.value.src_order_no,
    items: list,
    id: Number(rourte.query.id),
    handler_id: state.form.handler_id || 0,
    remark: state.form.remark,
    pay_date: formatDate(state.form.pay_date),
    sale_system_id: data.value.sale_system_id || 0,
    src_order_id: data.value.src_order_id || 0,
    src_order_type: data.value.src_order_type || 0,
    supplier_id: data.value.supplier_id || 0,
    voucher_num: state.form.voucher_num,
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'DyeingPayDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const columnList = ref([

  {
    field: 'order_no',
    title: '单号',
    minWidth: 100,
  },
  {
    field: 'dnf_order_no',
    soltName: 'dnf_order_no',
    title: '染整通知单号',
    minWidth: 100,
  },
  {
    field: 'order_type_name',
    title: '单据类型',
    minWidth: 100,
  },
  {
    field: 'dnf_date',
    title: '染整日期',
    minWidth: 100,
  },
  {
    field: 'enter_date',
    title: '进仓日期',
    minWidth: 100,
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'color_code',
    title: '色号',
    minWidth: 80,
  },
  {
    field: 'color',
    title: '颜色',
    minWidth: 80,
  },
  {
    field: 'dye_factory_color_code',
    title: '染厂色号',
    minWidth: 80,
  },
  {
    field: 'dyelot',
    title: '对色缸号',
    minWidth: 80,
  },
  {
    field: 'dnf_craft',
    title: '染整工艺',
    minWidth: 100,
  },

  {
    field: 'unit',
    title: '单位',
    minWidth: 80,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 80,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 80,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '空差',
    minWidth: 60,
    isWeight: true,
  },
  {
    field: 'total_length',
    title: '辅助数量',
    minWidth: 80,
  },
  {
    field: 'gf_roll',
    title: '用坯匹数',
    minWidth: 80,
  },
  {
    field: 'gf_weight',
    title: '用坯数量',
    minWidth: 80,
  },
  {
    field: 'piece_count',
    title: '计产匹数',
    minWidth: 100,
    soltName: 'piece_count',
  },
  {
    field: 'weight',
    title: '计产数量',
    minWidth: 100,
    soltName: 'weight',
  },
  {
    field: 'length',
    title: '计产辅助数量',
    minWidth: 100,
    soltName: 'length',
  },
  {
    title: '染费',
    childrenList: [
      {
        field: 'dnf_unit_price',
        title: '单价',
        minWidth: 80,
        soltName: 'dnf_unit_price',
      },
      {
        field: 'dyeing_price',
        title: '金额',
        minWidth: 80,
      },
    ],
  },
  {
    title: '后整',
    childrenList: [
      {
        field: 'finishing_unit_price',
        title: '单价',
        minWidth: 100,
        soltName: 'finishing_unit_price',
      },
      {
        field: 'finishing_price',
        title: '金额',
        minWidth: 80,
      },
    ],
  },
  {
    title: '纸筒胶带',
    childrenList: [
      {
        field: 'paper_tube_unit_price',
        title: '纸筒单价',
        minWidth: 100,
        soltName: 'paper_tube_unit_price',
      },
      {
        field: 'paper_tube_price',
        title: '纸筒金额',
        minWidth: 80,
      },
      {
        field: 'plastic_bag_unit_price',
        title: '胶袋单价',
        minWidth: 100,
        soltName: 'plastic_bag_unit_price',
      },
      {
        field: 'plastics_bag_price',
        title: '胶袋金额',
        minWidth: 80,
      },
    ],
  },
  {
    title: '应付信息',
    childrenList: [
      {
        field: 'other_price',
        title: '其他应付',
        minWidth: 100,
        soltName: 'other_price',
      },
      {
        field: 'price',
        title: '总金额',
        minWidth: 80,
      },
    ],
  },
  {
    title: '其他',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
        soltName: 'remark',
      },
    ],
  },
])
function handleClickLink(row: any) {
  routerList.push({
    name: 'DyeingNoticeDetail',
    query: { id: row.dnf_order_id },
    close_self: false,
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard title="基础信息" :tool-bar="false">
      <template #right-top>
        <el-button v-btnAntiShake="handleSure" type="primary">
          提交
        </el-button>
      </template>
      <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="源单类型:">
            <template #content>
              {{ data?.src_order_type_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="源单单号:">
            <template #content>
              {{ data?.src_order_no }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="营销体系:">
            <template #content>
              {{ data?.sale_system_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="凭证单号:">
            <template #content>
              <el-input v-model="state.form.voucher_num" clearable />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="供方名称:">
            <template #content>
              {{ data?.supplier_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="应付日期:" required>
            <template #content>
              <el-form-item prop="pay_date">
                <el-date-picker v-model="state.form.pay_date" type="date" placeholder="应付日期" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="经手人:">
            <template #content>
              <SelectComponents v-model="state.form.handler_id" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="单据备注:" copies="2">
            <template #content>
              <el-input v-model="state.form.remark" clearable />
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </FildCard>
    <FildCard title="成品信息" :tool-bar="true" class="table-card-full">
      <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
        <template #piece_count="{ row }">
          <vxe-input v-model="row.piece_count" :min="data.src_order_type !== 2 ? 0 : -10000" type="float" />
        </template>
        <template #dnf_order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handleClickLink(row)">
            {{ row.dnf_order_no }}
          </el-link>
        </template>
        <template #weight="{ row }">
          <vxe-input v-model="row.weight" :disabled="[DnfChargingMethod.Length].includes(data?.dnf_charging_method)" :min="data.src_order_type !== 2 ? 0 : -10000" type="float" @blur="handleBlur(row)" />
        </template>
        <template #length="{ row }">
          <vxe-input v-model="row.length" :disabled="[DnfChargingMethod.DnfWeight, DnfChargingMethod.finishProductWeight].includes(data?.dnf_charging_method)" :min="data.src_order_type !== 2 ? 0 : -10000" type="float" @blur="handleBlur(row)" />
        </template>
        <template #dnf_unit_price="{ row }">
          <vxe-input v-model="row.dnf_unit_price" type="float" />
        </template>
        <template #finishing_unit_price="{ row }">
          <vxe-input v-model="row.finishing_unit_price" type="float" />
        </template>
        <template #paper_tube_unit_price="{ row }">
          <vxe-input v-model="row.paper_tube_unit_price" type="float" />
        </template>
        <template #plastic_bag_unit_price="{ row }">
          <vxe-input v-model="row.plastic_bag_unit_price" type="float" />
        </template>
        <template #other_price="{ row }">
          <vxe-input v-model="row.other_price" :min="data.src_order_type !== 2 ? 0 : -10000" type="float" />
        </template>
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" />
        </template>
      </Table>
    </FildCard>
    <!--  <FildCard title="" :tool-bar="false" class="mt-[5px]"> -->
    <!--    <el-tabs v-model="activeName" class="demo-tabs"> -->
    <!--      <el-tab-pane label="用坯信息" name="first"> -->
    <!--        <Table :config="tableConfig_use" :table-list="state.useList" :column-list="columnList_use" /> -->
    <!--      </el-tab-pane> -->
    <!--      <el-tab-pane label="返布用坯信息" name="second"> -->
    <!--        <Table :config="tableConfig_return" :table-list="state.returnList" :column-list="columnList_return" /> -->
    <!--      </el-tab-pane> -->
    <!--    </el-tabs> -->
    <!--  </FildCard> -->
    <RollInfo
      ref="rollInfoRef"
      class="table-card-bottom"
      :dnf-charging-method="data.dnf_charging_method"
      :parent-list="data.items"
      :src-id="data.src_order_id"
      type="add"
    />
  </div>
</template>

<style></style>
