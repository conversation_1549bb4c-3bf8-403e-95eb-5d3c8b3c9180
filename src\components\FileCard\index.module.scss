.image-container {
  position: relative;
  margin: 5px 5px;
  border-radius: 5px;
}
.upload--picture-card {
  background-color: var(--el-fill-color-lighter);
  border: 1px dashed var(--el-border-color-darker);
  border-radius: 6px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  cursor: pointer;
  vertical-align: top;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  vertical-align: middle;
  display: inline-block;
  position: relative;
  z-index: 1;
  background-color: #fff;
}

.upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.2s;
  z-index: 1;

  &::after {
    display: inline-block;
    content: '';
    height: 100%;
    vertical-align: middle;
  }

  span {
    display: none;
    cursor: pointer;
  }

  span + span {
    margin-left: 15px;
  }

  .upload-list__item-delete {
    position: static;
    font-size: inherit;
    color: inherit;
  }

  &:hover {
    opacity: 1;

    span {
      display: inline-block;
    }
  }
}
.file-svg {
  width: 60px;
  height: 60px;
  vertical-align: middle;
  display: inline-block;
  position: relative;
  z-index: 1;
  background-color: #fff;
}
