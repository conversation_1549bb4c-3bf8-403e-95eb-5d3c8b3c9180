import { useRequest } from '@/use/useRequest'

// 获取列表
export const getFpmSaleAllocateInOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/getFpmSaleAllocateInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addFpmSaleAllocateInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/addFpmSaleAllocateInOrder',
    method: 'post',
  })
}

// 获取
export const getFpmSaleAllocateInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/getFpmSaleAllocateInOrder',
    method: 'get',
  })
}

// 作废
export const updateFpmSaleAllocateInOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmSaleAllocateInOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmSaleAllocateInOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmSaleAllocateInOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusWait',
    method: 'put',
  })
}

// 更新
export const updateFpmSaleAllocateInOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrder',
    method: 'put',
  })
}
