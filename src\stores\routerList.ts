import { defineStore } from 'pinia'
import { GetMenus } from '@/api/menu'
import { ElMessage } from 'element-plus'

export interface listParam {
  path: any
  title: string
  closable: boolean
  query: any
  params: any
}
const { fetchData: getMenus, data: menus, success: getMenuSuccess, msg: getMenuMsg } = GetMenus()
export const routeListStore = defineStore('routeList', {
  state: () => {
    return {
      isInit: false,
      list: [] as listParam[],
      tabs: [
        {
          path: 'Dashboard',
          title: '首页',
          closable: false,
          query: {},
          params: {},
        },
      ] as listParam[],
    }
  },
  getters: {},
  actions: {
    init: async function () {
      // 获取菜单信息
      await getMenus()
      if (!getMenuSuccess.value) {
        ElMessage.error(getMenuMsg.value)
        return
      }
      this.setList(menus.value.list)
      this.isInit = true
    },
    clear() {
      // 重置全部信息
      this.isInit = false
      this.list = []
      this.tabs = [
        {
          path: 'Dashboard',
          title: '首页',
          closable: false,
          query: {},
          params: {},
        },
      ]
    },
    flattenSubMenu: function (menu: any) {
      const that = this
      return menu?.reduce(function (flat: any, item: any) {
        if (item.sub_menu.length > 0) {
          flat = flat.concat(that.flattenSubMenu(item.sub_menu))
        }
        if (item.resource_router_name !== '') {
          flat.push(item)
        }
        return flat
      }, [])
    },
    setList(list: any[]) {
      this.list = list
    },
  },
})
