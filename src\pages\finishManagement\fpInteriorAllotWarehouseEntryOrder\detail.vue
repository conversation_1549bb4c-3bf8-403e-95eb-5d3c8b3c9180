<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import FineSizeSaleAllotEntryDetail from '../components/FineSizeSaleAllotEntryDetail.vue'
import {
  getFpmInternalAllocateInOrder,
  updateFpmInternalAllocateInOrderStatusCancel,
  updateFpmInternalAllocateInOrderStatusPass,
  updateFpmInternalAllocateInOrderStatusReject,
  updateFpmInternalAllocateInOrderStatusWait,
} from '@/api/fpInteriorAllotWarehouseEntryOrder'
import { formatDate, formatLengthDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const form_options = [
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '仓库名称',
    key: 'warehouse_name',
  },
  {
    text: '调出仓库名称',
    key: 'out_warehouse_name',
  },
  {
    text: '进仓日期',
    key: 'warehouse_in_time',
  },
  {
    text: '仓管员',
    key: 'store_keeper_name',
  },
  {
    text: '备注',
    key: 'remark',
    copies: 2,
  },
]
const route = useRoute()

const state = reactive<any>({
  baseData: {
    order_no: '',
    audit_status_name: '',
    audit_status: 1,
    src_order_no: '',
  },
})
const { fetchData, data: detailData } = getFpmInternalAllocateInOrder()
onMounted(() => {
  getData()
})

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '', 'float')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '', 'float')

        if (['in_length'].includes(column.field))
          return sumNum(data, 'in_length', '', 'float')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'in_roll',
          title: '进仓匹数',
          minWidth: 100,
        },
      ],
    },
    {
      title: '进仓数量信息',
      childrenList: [
        {
          field: 'total_weight',
          title: '进仓数量',
          minWidth: 100,
        },
        {
          field: 'weight_error',
          title: '空差',
          minWidth: 100,
        },

        {
          field: 'settle_weight',
          title: '结算数量',
          minWidth: 100,
        },
        {
          field: 'unit_name',
          title: '单位',
          minWidth: 100,
        },
      ],
    },
    {
      title: '进仓辅助数量信息',
      childrenList: [
        {
          field: 'in_length',
          title: '进仓辅助数量',
          minWidth: 100,
        },
      ],
    },

    {
      title: '单据备注信息',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
})
async function getData() {
  await fetchData({ id: route.query.id })
  state.baseData = {
    ...detailData.value,
    warehouse_in_time: formatDate(detailData.value.warehouse_in_time),
  }
  finishProductionOptions.datalist = detailData.value.item_data.map((item: any) => {
    return {
      ...item,
      product_level_id: item.product_level_id || '',
      in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
      total_weight: formatWeightDiv(Number(item.total_weight)),
      weight_error: formatWeightDiv(Number(item.weight_error)),
      settle_weight: formatWeightDiv(Number(item.settle_weight)),
      unit_price: formatUnitPriceDiv(Number(item.unit_price)),
      in_length: formatLengthDiv(Number(item.in_length)),
      length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)),
      other_price: formatTwoDecimalsDiv(Number(item.other_price)),
      total_price: formatTwoDecimalsDiv(Number(item.total_price)),
    }
  })
}

const closeLoading = ref(false)
const cancelLoading = ref(false)
const rejectLoading = ref(false)
const auditLoading = ref(false)
const eliminateLoading = ref(false)
async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateFpmInternalAllocateInOrderStatusWait,
      loadingRef: eliminateLoading,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateFpmInternalAllocateInOrderStatusPass,
      loadingRef: auditLoading,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateFpmInternalAllocateInOrderStatusReject,
      loadingRef: rejectLoading,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateFpmInternalAllocateInOrderStatusCancel,
      loadingRef: cancelLoading,
    },
  }
  await orderStatusConfirmBox({ id, audit_status, ...options[audit_status] })
  getData()
}

const FineSizeSaleAllotEntryDetailRef = ref()
function showDialog(row: any) {
  FineSizeSaleAllotEntryDetailRef.value.showDialog(row)
}
</script>

<template>
  <StatusColumn
    :close-loading="closeLoading"
    :cancel-loading="cancelLoading"
    :reject-loading="rejectLoading"
    :audit-loading="auditLoading"
    :eliminate-loading="eliminateLoading"
    :order_no="state.baseData.order_no"
    :order_id="state.baseData.id"
    :out_warehouse_order_no="state.baseData.src_order_no"
    :status="state.baseData.audit_status"
    :status_name="state.baseData.audit_status_name"
    permission_print_key=""
    permission_wait_key="FpInteriorAllotWarehouseEntryOrder_wait"
    permission_reject_key="FpInteriorAllotWarehouseEntryOrder_reject"
    permission_pass_key="FpInteriorAllotWarehouseEntryOrder_pass"
    permission_cancel_key="FpInteriorAllotWarehouseEntryOrder_cancel"
    permission_edit_key="FpInteriorAllotWarehouseEntryOrder_edit"
    edit_router_name="FpInteriorAllotWarehouseEntryOrderEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :copies="item.copies || 1" :label="`${item.text}:`">
        <template #content>
          {{ state.baseData[item.key] }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard :tool-bar="false" title="成品信息" class="mt-[5px]">
    <Table :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
      <template #unit_price="{ row }">
        ￥{{ row.unit_price }}
      </template>
      <template #length_unit_price="{ row }">
        ￥{{ row.length_unit_price }}
      </template>
      <template #other_price="{ row }">
        ￥{{ row.other_price }}
      </template>
      <template #total_price="{ row }">
        ￥{{ row.total_price }}
      </template>
      <template #xima="{ row }">
        <el-link @click="showDialog(row)">
          查看
        </el-link>
      </template>
    </Table>
  </FildCard>
  <FineSizeSaleAllotEntryDetail ref="FineSizeSaleAllotEntryDetailRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
    }
  }
}

.el-link {
  color: #0e7eff;
}
</style>
