<script lang="ts" setup name="GreyFabricPurchaseReturnAdd">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import AddInventoryDialog from '../components/AddInventoryDialog.vue'
import InventoryXima from '../components/InventoryXima.vue'
import SelectOrderCodeDialog from '../components/SelectOrderCodeDialog.vue'
import { addGfmPurchaseReturn } from '@/api/greyFabricPurchaseReturn'
import { formatDate, formatPriceMul, formatUnitPriceMul, formatWeightMul, sumNum } from '@/common/format'
import { deepClone, getCurrentDate, getDefaultSaleSystem, isXimaAlreadyEntered } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import useRouterList from '@/use/useRouterList'
import { BusinessUnitIdEnum } from '@/common/enum'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    marketingSystem: '', // 营销体系
    consignee: '', // 货源单位
    destprovidername: '', // 接收单位
    date: '', // 退货日期
    remark: '',
  },
  formRules: {
    marketingSystem: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    consignee: [{ required: true, message: '请选择货源单位', trigger: 'change' }],
    destprovidername: [{ required: true, message: '请选择接收单位', trigger: 'change' }],
    date: [{ required: true, message: '请选择退货日期', trigger: 'blur' }],
  },
  tableList: [],
  isInformation: true,
  multipleSelection: [],
  otherValue: '',
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

onMounted(() => {
  state.form.date = getCurrentDate()
  const resDes = getDefaultSaleSystem()
  state.form.marketingSystem = resDes?.default_sale_system_id
})

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList?.length > 0) {
      state.tableList?.map((item: any) => {
        item.total_price = Number(Number(item?.single_price) * Number(item?.total_weight) + Number(item?.other_price)).toFixed(2) || 0
        return item
      })
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const columnList = ref<any>([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 150,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 150,
    // isWeight: true,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 150,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 150,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 150,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_purchase_code',
    title: '坯布采购单号',
    minWidth: 150,
    soltName: 'orderCode',
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 150,
    soltName: 'horsepower',
    required: true,
  },
  {
    field: 'remark',
    title: '细码',
    minWidth: 150,
    soltName: 'xima',
    required: true,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 150,
  },
  {
    field: 'single_price',
    title: '单价',
    minWidth: 150,
    soltName: 'unitPrice',
    required: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 150,
    soltName: 'otherPrice',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 150,
  },

  {
    field: 'remark',
    title: '备注',
    minWidth: 150,
    soltName: 'remark',
  },
])

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['total_weight'].includes(column.property))
        return `${sumNum(data, 'total_weight')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      return null
    }),
  ]
}

const AddInventoryDialogRef = ref()

function handAdd() {
  AddInventoryDialogRef.value.state.filterData.warehouse_id = state.form.consignee
  AddInventoryDialogRef.value.state.filterData.supplier_id = state.form.destprovidername
  AddInventoryDialogRef.value.state.showModal = true
}

// 添加坯布信息
function handleSureFabric(list: any) {
  list.forEach((item: any) => {
    // const idNum = Math.floor(Math.random() * 10000)
    state.tableList.push({
      ...item,
      reference_weight: item.stock_weight,
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      customer_name: item.customer_name,
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      needle_size: item.needle_size,
      yarn_batch: item.yarn_batch,
      gray_fabric_color_name: item.gray_fabric_color_name,
      grey_fabric_level_name: item.grey_fabric_level_name,
      machine_number: item.machine_number,
      total_weight: 0,
      total_price: 0,
      order_code: '',
      roll: 0,
      single_price: 0,
      other_price: 0,
      raw_material_yarn_name: item?.raw_material_yarn_name,
      raw_material_batch_num: item?.raw_material_batch_num,
      raw_material_batch_brand: item?.raw_material_batch_brand,
      supplier_id: item.supplier_id,
      custom_id: item.customer_id,
      gray_fabric_color_id: item.gray_fabric_color_id,
      grey_fabric_level_id: item.grey_fabric_level_id,
      grey_fabric_remark: item.source_remark,
      remark: '',
      item_fc_data: [],
      grey_fabric_id: item?.grey_fabric_id || 0,
      warehouse_sum_id: item?.id || 0,
    })
  })
  AddInventoryDialogRef.value.state.showModal = false
}

// 录入细码
const InventoryXimaRef = ref()

function handWrite(row: any, index: number) {
  if (row.roll === '')
    return ElMessage.error('请先输入匹数')

  const info = {
    grey_fabric_code: row.grey_fabric_code,
    grey_fabric_name: row.grey_fabric_name,
    customer_id: row.customer_id,
    customer_name: row.customer_name,
    yarn_batch: row.yarn_batch,
    supplier_id: row.supplier_id,
    gray_fabric_color_id: row.gray_fabric_color_id,
    grey_fabric_level_id: row.grey_fabric_level_id,
    raw_material_batch_brand: row.raw_material_batch_brand,
    raw_material_batch_num: row.raw_material_batch_num,
    raw_material_yarn_name: row.raw_material_yarn_name,
    source_remark: row.source_remark,
    warehouse_id: state.form.consignee,
    warehouse_sum_id: row?.warehouse_sum_id || 0,
  }
  InventoryXimaRef.value.state.info = info

  InventoryXimaRef.value.state.showModal = true
  InventoryXimaRef.value.state.ximaList = deepClone(row?.item_fc_data) || []
  InventoryXimaRef.value.state.canEnter = row.roll
  InventoryXimaRef.value.state.id = row.id
  InventoryXimaRef.value.state.rowIndex = index
}

function handleSureXima(val: any) {
  state.tableList?.map((item: any, index: number) => {
    if (index === val.rowIndex) {
      item.item_fc_data = val.ximaList
      item.total_weight = Number(sumNum(val.ximaList, 'weight'))
      return item
    }
  })
  InventoryXimaRef.value.state.showModal = false
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addGfmPurchaseReturn()

// 提交数据
async function handleSure() {
  if (!state.tableList.length)
    return ElMessage.error('至少添加一条坯布信息')

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].roll === '')
      return ElMessage.error('匹数不可为空')

    if (Number(list[i].roll) - Number(sumNum(list[i]?.item_fc_data, 'roll')) !== 0 && Number(list[i].roll) !== 0)
      return ElMessage.error(`第${i + 1}行的细码必须录完`)

    if (list[i].single_price === '')
      return ElMessage.error('单价不可为空')

    list[i].total_weight = Number(formatWeightMul(list[i]?.total_weight))
    list[i].total_price = Number(formatPriceMul(list[i]?.total_price))
    list[i].roll = Number(formatPriceMul(list[i]?.roll))

    list[i].single_price = Number(formatUnitPriceMul(list[i].single_price))
    list[i].other_price = Number(formatPriceMul(list[i].other_price))
    // 将坯布信息的数量乘以一千给后端
    for (let q = 0; q < list[i].item_fc_data?.length; q++) {
      if (list[i].item_fc_data[q].weight === '')
        return ElMessage.error('细码数量不可为空')

      if (list[i].item_fc_data[q].roll === '')
        return ElMessage.error('细码匹数不可为空')

      list[i].item_fc_data[q].roll = Number(formatPriceMul(list[i].item_fc_data[q].roll))
      list[i].item_fc_data[q].grey_fabric_stock_id = Number(list[i].item_fc_data[q].id)
      list[i].item_fc_data[q].weight = Number(formatWeightMul(list[i].item_fc_data[q].weight))
    }
  }
  const query = {
    item_data: list,
    remark: state.form.remark,
    return_entity_id: state.form.consignee,
    return_time: formatDate(state.form.date),
    sale_system_id: state.form.marketingSystem,
    supplier_id: state.form.destprovidername,
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)

      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreyFabricPurchaseReturnDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

function handDelete(index: number) {
  state.tableList.splice(index, 1)
}

const bulkShow = ref(false)

const bulkSetting = ref<any>({})

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      if (row.field === 'grey_fabric_purchase_code')
        item.grey_fabric_purchase_item_id = Number(state.otherValue)

      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

// const changeValue = (val: any, rowIndex: number) => {
//   state.tableList?.map((item: any, index: number) => {
//     if (rowIndex === index) {
//       item.grey_fabric_purchase_item_id = val.id
//       return item
//     }
//   })
// }

// 选择采购单号

const SelectComponentsRef = ref()

const SelectOrderCodeDialogRef = ref()

function focus(row: any, rowIndex: number) {
  SelectOrderCodeDialogRef.value.state.showModal = true
  SelectOrderCodeDialogRef.value.state.rowIndex = rowIndex
}

function handSelectOrderCode(list: any, rowIndex: number) {
  state.tableList.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.grey_fabric_purchase_code = list[0]?.order_code
      item.grey_fabric_purchase_item_id = list[0]?.id
    }
    return item
  })
  SelectOrderCodeDialogRef.value.state.showModal = false
}

const bulkList = reactive<any>([
  {
    field: 'grey_fabric_purchase_code',
    title: '坯布采购单号',
    component: 'select',
    labelField: 'grey_fabric_purchase_code',
    valueField: 'order_code',
    api: 'GetPurchaseGreyFabricItemList',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'single_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="marketingSystem">
              <SelectComponents v-model="state.form.marketingSystem" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="货源单位:">
          <template #content>
            <el-form-item prop="consignee">
              <SelectDialog
                v-model="state.form.consignee"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: componentRemoteSearch.unit_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.unit_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="接收单位:">
          <template #content>
            <el-form-item prop="destprovidername">
              <SelectDialog
                v-model="state.form.destprovidername"
                api="BusinessUnitSupplierEnumlist"
                :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric, name: componentRemoteSearch.name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '供应商编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '供应商编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.name = val)"
              />
              <!-- <SelectComponents
                :query="{ unit_type_id: 14 }"
                style="width: 300px"
                api="BusinessUnitSupplierEnumlist"
                label-field="name"
                value-field="id"
                v-model="state.form.destprovidername"
                clearable
              /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="退货日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="退货日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button :disabled="state.form.marketingSystem === '' || state.form.consignee === '' || state.form.destprovidername === ''" type="primary" @click="handAdd">
        从库存中添加
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #orderCode="{ row, rowIndex }">
        <SelectComponents
          ref="SelectComponentsRef"
          v-model="row.grey_fabric_purchase_code"
          visible-change-close
          api="GetPurchaseGreyFabricItemList"
          label-field="grey_fabric_purchase_code"
          value-field="order_code"
          clearable
          @focus="focus(row, rowIndex)"
        />
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
      <template #horsepower="{ row }">
        <vxe-input v-model="row.roll" :min="0" type="float" placeholder="必填" />
      </template>
      <template #xima="{ row, rowIndex }">
        <div class="flex items-center">
          <el-button type="primary" text link @click="handWrite(row, rowIndex)">
            录入
          </el-button>
          <div v-if="isXimaAlreadyEntered(row, { rollKey: 'roll', weightKey: 'total_weight' })" class="text-[#b5b39f] ml-[5px]">
            (已录完)
          </div>
          <div v-else class="text-[#efa6ae] ml-[5px]">
            ({{ (row?.roll - Number(sumNum(row?.item_fc_data, 'roll'))).toFixed(2) }}匹未录)
          </div>
        </div>
      </template>
      <template #unitPrice="{ row }">
        <vxe-input v-model="row.single_price" type="float" placeholder="必填" />
      </template>
      <template #otherPrice="{ row }">
        <vxe-input v-model="row.other_price" type="float" placeholder="请输入" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" placeholder="请输入" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <AddInventoryDialog ref="AddInventoryDialogRef" @handle-sure="handleSureFabric" />
  <InventoryXima ref="InventoryXimaRef" @handle-sure="handleSureXima" />
  <BulkSetting v-model:otherValue="state.otherValue" v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
  <SelectOrderCodeDialog ref="SelectOrderCodeDialogRef" @handle-sure="handSelectOrderCode" />
</template>

<style></style>
