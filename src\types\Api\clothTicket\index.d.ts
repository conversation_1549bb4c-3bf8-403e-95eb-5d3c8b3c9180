declare namespace Api.GetFineCodeDetail {
  export interface Request {
    /**
     * 条形码
     */
    fabric_piece_code?: string
    /**
     * 细码ID
     */
    id?: number
    [property: string]: any
  }
  /**
   * produce.GetProductionScheduleOrderFineCodeDetailData
   */
  export interface Response {
  /**
   * 条形码
   */
    fabric_piece_code?: string
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色名称
     */
    grey_fabric_color_name?: string
    /**
     * 生产排产单相关信息
     */
    grey_fabric_id?: number
    /**
     * 坯布等级ID
     */
    grey_fabric_level_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 细码基本信息
     */
    id?: number
    /**
     * 进仓条数
     */
    in_warehouse_roll?: number
    /**
     * 进仓重量
     */
    in_warehouse_weight?: number
    /**
     * 验布条数
     */
    inspection_roll?: number
    /**
     * 验布时间
     */
    inspection_time?: string
    /**
     * 验布重量
     */
    inspection_weight?: number
    /**
     * 织机机型ID
     */
    loom_model_id?: number
    /**
     * 织机类型
     */
    loom_model_name?: string
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 本月称重条数
     */
    month_weighing_count?: number
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 出仓条数
     */
    out_warehouse_roll?: number
    /**
     * 出仓重量
     */
    out_warehouse_weight?: number
    /**
     * 生产备注
     */
    produce_remark?: string
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 生产通知单相关信息
     */
    production_notify_order_no?: string
    /**
     * 生产排产单ID
     */
    production_schedule_order_id?: number
    /**
     * 生产排产单号
     */
    production_schedule_order_no?: string
    /**
     * 质量备注
     */
    quality_remark?: string
    /**
     * 排产条数
     */
    schedule_roll?: number
    /**
     * 库存条数
     */
    stock_roll?: number
    /**
     * 库存重量
     */
    stock_weight?: number
    /**
     * 统计信息
     */
    today_weighing_count?: number
    /**
     * 总针数
     */
    total_needle_size?: string
    /**
     * 卷号
     */
    volume_number?: number
    /**
     * 称重条数
     */
    weighing_roll?: number
    /**
     * 称重时间
     */
    weighing_time?: string
    /**
     * 实际重量
     */
    weighing_weight?: number
    /**
     * 布匹定重
     */
    weight_of_fabric?: number
    /**
     * 纱批
     */
    yarn_batch?: string
    /**
     * 纱名（子表的原料名称、原料品牌、原料批号、原料缸号、原料颜色组合起来，每个子表间数据用"+"分开）
     */
    yarn_name?: string
    [property: string]: any
  }

}
declare namespace Api.WeighingFineCode {
  /**
   * produce.WeighingProductionScheduleOrderFineCodeParam
   */
  export interface Request {
  /**
   * 实际重量
   */
    actual_weight: number
    /**
     * 电子秤重量
     */
    electronic_scale_weight: number
    /**
     * 细码ID
     */
    id: number
    /**
     * 是否为交班织工
     */
    is_shift_weaver?: boolean
    /**
     * 交班转速
     */
    shift_speed?: number
    /**
     * 交班织工ID
     */
    shift_weaver_id?: number
    /**
     * 交班织工名称
     */
    shift_weaver_name?: string
    /**
     * 织工ID
     */
    weaver_id: number
    /**
     * 织工名称
     */
    weaver_name: string
    [property: string]: any
  }
  /**
   * produce.WeighingProductionScheduleOrderFineCodeData
   */
  export interface Response {
  /**
   * 更新的生产排产单细码表ID
   */
    id?: number
    [property: string]: any
  }
}
