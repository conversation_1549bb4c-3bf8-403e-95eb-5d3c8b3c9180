<script setup lang="ts">
import { Minus, Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import { ElMessage } from 'element-plus'
import { ref, watch } from 'vue'
import currency from 'currency.js'
import { cloneDeep } from 'lodash-es'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { DictionaryType } from '@/common/enum'

interface Props {
  defaultList?: Arr[]
  isEdit: boolean
}
interface Arr { fibre_id: string, fibre_code: string, count: number }
const props = withDefaults(defineProps<Props>(), {
  defaultList: () => [],
  isEdit: true,
})
const emits = defineEmits(['change'])
const defalutComposition = [{ fibre_id: '', fibre_code: '', count: 0 }] // 默认成分空一行
const composition_arr = ref<{ fibre_id: string, fibre_code: string, count: number }[]>([{ fibre_id: '', fibre_code: '', count: 0 }])

function compositionAdd(index: null | number = null) {
  if (!composition_arr.value) {
    composition_arr.value = [{ fibre_id: '', fibre_code: '', count: 0 }]
  }
  else {
    if (index == null)
      composition_arr.value.push({ fibre_id: '', fibre_code: '', count: 0 })
    else
      composition_arr.value.splice(index + 1, 0, { fibre_id: '', fibre_code: '', count: 0 })
  }
}

function compositionDel(index: number) {
  composition_arr.value.splice(index, 1)
  checkData()
}

function checkData(index: number | null = null) {
  const composition: string[] = []
  const arr = JSON.parse(JSON.stringify(composition_arr.value))
  let count = 0
  for (let i = 0; i < arr.length; i++)
    count = currency(arr[i].count).add(count).value

  if (index !== null && count > 100) {
    arr[index].count = composition_arr.value[index].count = Big(composition_arr.value[index].count).minus(Big(count).minus(100)).toNumber()
    ElMessage.error('比例不能大于100')
  }
  const new_arr = arr?.filter((item: any) => {
    if (item.fibre_code && item.count) {
      composition.push(`${item.fibre_code + item.count.toString()}%`)
      return true
    }
  })
  emits('change', { composition: composition.join('/'), composition_arr: new_arr, count })
}

watch(
  () => props.defaultList,
  () => {
    updateIncomingData()
  },
  { deep: true, immediate: true },
)

// 获取传入的数据
function updateIncomingData() {
  if (props.defaultList.length)
    composition_arr.value = cloneDeep(props.defaultList)
  else composition_arr.value = cloneDeep(defalutComposition)
  if (composition_arr.value?.length)
    checkData()
}

function getFibre(item: any, val: any) {
  item.fibre_code = val.code
  item.fibre_name = val.name
  checkData()
}

defineExpose({
  compositionAdd,
  updateIncomingData,
})
</script>

<template>
  <div v-for="(item, index) in composition_arr" :key="index" class="flex justify-between mb-2 gap-4">
    <SelectComponents
      v-model="item.fibre_id"
      style="width: 150px"
      api="GetDictionaryDetailEnumListApi"
      placeholder="请选择"
      label-field="name"
      value-field="id"
      :query="{ dictionary_id: DictionaryType.fibre }"
      clearable
      :disabled="!props.isEdit"
      @change-value="(val:any) => getFibre(item, val)"
    />
    <el-input-number
      v-model.trim="item.count"
      style="width: 150px"
      :min="0"
      :max="100"
      :precision="2"
      controls-position="right"
      clearable
      placeholder="请输入比例"
      :disabled="!props.isEdit"
      @blur="checkData(index)"
    />
    <el-button v-if="props.isEdit" :icon="Plus" type="primary" @click="compositionAdd(index)" />
    <el-button v-if="props.isEdit && composition_arr.length > 1" :icon="Minus" type="danger" @click="compositionDel(index)" />
  </div>
</template>
