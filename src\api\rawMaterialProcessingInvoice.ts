import { useRequest } from '@/use/useRequest'

// 获取列表
export const raw_process_orderoutlist = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新增
export const raw_process_orderoutpost = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out',
    method: 'post',
  })
}

// 编辑
export const raw_process_orderoutput = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out',
    method: 'put',
  })
}

// 详情
export const raw_process_orderoutputdetail = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out',
    method: 'get',
  })
}

// 审核
export const raw_process_orderoutputpass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out/pass',
    method: 'put',
  })
}

// 消审
export const raw_process_orderoutputwait = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out/wait',
    method: 'put',
  })
}

// 作废
export const raw_process_orderoutputcancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out/cancel',
    method: 'put',
  })
}

// 驳回
export const raw_process_orderoutputreject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/raw_process_order/out/reject',
    method: 'put',
  })
}
