import { useRequest } from '@/use/useRequest'

// 获取列表
export const advancePayOrderList = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/getAdvancePayOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const advancePayOrderdetail = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/getAdvancePayOrder',
    method: 'get',
  })
}

// 审核
export const advancePayOrderpass = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/updateAdvancePayOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const advancePayOrdercancel = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/updateAdvancePayOrderStatusWait',
    method: 'put',
  })
}

// 驳回
export const advancePayOrderreject = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/updateAdvancePayOrderStatusReject',
    method: 'put',
  })
}

// 作废
export const advancePayOrdervoid = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/updateAdvancePayOrderStatusCancel',
    method: 'put',
  })
}

//  更新
export const updateAdvancePayOrder = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/updateAdvancePayOrder',
    method: 'put',
  })
}

//  新增
export const advancePayOrderpost = () => {
  return useRequest({
    url: '/admin/v1/payable/advancePayOrder/addAdvancePayOrder',
    method: 'post',
  })
}
