<script lang="ts" setup name="GreySalePlanAdd">
import { Decimal } from 'decimal.js'
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import currency from 'currency.js'
import { orderDate } from '../productSalePlan/common'
import { addSaleProductPlanOrder } from '@/api/productSalePlan'
import { DictionaryType, EmployeeType } from '@/common/enum'
import { formatDate, formatPriceDiv, formatPriceMul, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deepClone } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectSettleTypeDialog from '@/components/SelectSettleTypeDialog/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'
import useRouterList from '@/use/useRouterList'
import { formValidatePass } from '@/common/rule'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import AddressCard from '@/components/AddressCard/index.vue'

const routerList = useRouterList()
const addressCardRef = ref() // 获取地址卡引用

const state = reactive<any>({
  form: {
    sale_system_id: '',
    customer_id: '',
    sale_user_id: '',
    voucherNumber: '',
    date: '',
    delivery_date: '',
    money: '',
    sale_tax_rate: '',
    is_with_tax_rate: true,
    customer_name: '',
    settle_method_id: '', // 结算方式
  },
  formRules: {
    sale_system_id: [{ required: true, validator: formValidatePass.sale_system_id(), message: '请选择营销体系', trigger: 'blur' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    date: [{ required: true, message: '请选择订单日期', trigger: 'blur' }],
    sale_user_id: [{ required: true, message: '请选择销售人员', trigger: 'blur' }],
    delivery_date: [{ required: true, message: '请选择交货日期', trigger: 'blur' }],
  },
  fileList: [],
  multipleSelection: [],
  use_yarnList: [],
})

const TableList = ref<any>([])

const showAdd = ref(false)

onMounted(() => {
  state.form.date = orderDate.date
  state.form.delivery_date = orderDate.delivery_date
})

const tableConfig = ref({
  fieldApiKey: 'GreySalePlanAdd',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '100',
  filterStatus: false,
  //   footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => cellClick(val),
  footerMethod: (val: any) => FooterMethod(val),
})

const tableConfig_ls = ref({
  fieldApiKey: 'GreySalePlanAdd_b',
  showSlotNums: true,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
})

function computed_total_price(row: any) {
  row.total_price = currency(row.weight).multiply(row.unit_price).add(row.other_price).value
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${sumNum(data, 'roll')}`

      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      if (['yarn_ratio'].includes(column.property))
        return `${sumNum(data, 'yarn_ratio')}`

      if (['use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'use_yarn_quantity')}`

      return null
    }),
  ]
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handAdd() {
  showAdd.value = true
}

function onSubmit(list: any) {
  list.forEach((item: any) => {
    const idNum = Math.floor(Math.random() * 10000)
    item.raw_material_info?.map((it: any) => {
      it.yarn_loss = formatPriceDiv(it.material_loss)
      it.yarn_ratio = formatPriceDiv(it.material_ratio)
      it.raw_material_color_name = it.color
      return it
    })
    TableList.value.push({
      record_id: idNum,
      code: item.code,
      name: item.name,
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_width_unit_id: item.grey_fabric_width_unit_id,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      grey_fabric_gram_weight_unit_id: item.grey_fabric_gram_weight_unit_id,
      needle_size: item.needle_size,
      total_needle_size: item.total_needle_size,
      gray_fabric_color_id: item.gray_fabric_color_id,
      measurement_unit_id: item.unit_id,
      unit_name: item.unit_name,
      weight_of_fabric: formatWeightDiv(item.weight_of_fabric),
      type_grey_fabric_id: item.type_grey_fabric_id,
      roll: '',
      weight: '',
      upper_limit: '',
      lower_limit: '',
      unit_price: '',
      other_price: '',
      total_price: 0,
      selected: false,
      remark: '',
      raw_material_info: item?.raw_material_info || [],
      grey_fabric_id: item.id,
      gray_fabric_level_id: '',
      id: item.id,
    })
  })

  showAdd.value = false
}

const tableRef = ref()

const tableRefOne = ref()

watch(
  () => TableList.value,
  () => {
    if (TableList.value?.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function blur(row: any) {
  state.use_yarnList.map((item: any) => {
    item.weight = row.weight
    item.record_id = row.record_id
    const tax = 1 + formatPriceDiv(item.yarn_loss)
    const total: any = new Decimal(Number(item.weight)).times(new Decimal(formatPriceDiv(item.yarn_ratio))).times(new Decimal(tax))

    item.use_yarn_quantity = Number.isNaN(total) ? 0 : total.toFixed(2)
    return item
  })
  nextTick(() => {
    tableRefOne.value.tableRef?.updateFooter()
  })
  // TODO: 每编辑一次数据，都要将他实时保存起来
  TableList.value.map((item: any) => {
    if (item.record_id === row.record_id) {
      item.raw_material_info = state.use_yarnList
      return item
    }
  })
}

function handBlur(row: any) {
  TableList.value.map((item: any) => {
    if (item.record_id === row.record_id) {
      item.raw_material_info = state.use_yarnList
      return item
    }
  })
  nextTick(() => {
    tableRefOne.value.tableRef?.updateFooter()
  })
}

function handDelete(index: number) {
  TableList.value.splice(index, 1)
}

let appendRecordId = 0
function handCopy(index: number) {
  const copyData = deepClone(TableList.value[index])
  copyData.id = 0
  copyData.record_id = appendRecordId++
  delete copyData._X_ROW_KEY_TABLE
  TableList.value.push(copyData)
}

function cellClick(val: any) {
  state.use_yarnList = val.row?.raw_material_info?.map((item: any) => {
    // item.yarn_loss = formatPriceDiv(item.material_loss)
    // item.yarn_ratio = formatPriceDiv(item.material_ratio)
    // item.total_yarn = 0
    item.weight = Number(val.row?.weight)
    item.record_id = val.row.record_id
    return item
  })
}

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = addSaleProductPlanOrder()

const ruleFormRef = ref()

async function handleSure() {
  const { addressData } = await addressCardRef.value?.getFormData()
  if (!TableList.value.length)
    return ElMessage.error('至少添加一条原料信息')

  const list = deepClone(TableList.value)

  for (let i = 0; i < list.length; i++) {
    if (list[i].weight === '')
      return ElMessage.error('数量不可为空')

    if (list[i].unit_price === '')
      return ElMessage.error('单价不可为空')

    if (Number(sumNum(list[i]?.raw_material_info, 'yarn_ratio')) !== 100 && list[i]?.raw_material_info.length)
      return ElMessage.error('用纱比例的和必须为100')

    for (let j = 0; j < list[i].raw_material_info.length; j++) {
      if (list[i].raw_material_info[j].yarn_ratio === '')
        return ElMessage.error('请填写用纱比例')

      list[i].raw_material_info[j].yarn_ratio = formatPriceMul(list[i].raw_material_info[j].yarn_ratio)
      list[i].raw_material_info[j].yarn_loss = formatPriceMul(list[i].raw_material_info[j].yarn_loss)
      list[i].raw_material_info[j].use_yarn_quantity = formatWeightMul(list[i].raw_material_info[j].use_yarn_quantity)
    }

    list[i].weight = formatWeightMul(list[i].weight)
    list[i].upper_limit = formatPriceMul(list[i].upper_limit)
    list[i].lower_limit = formatPriceMul(list[i].lower_limit)
    list[i].unit_price = formatUnitPriceMul(list[i].unit_price)
    list[i].total_price = formatPriceMul(list[i].total_price)
    list[i].other_price = formatPriceMul(list[i].other_price)
    list[i].weight_of_fabric = formatWeightMul(list[i].weight_of_fabric)
    list[i].gray_fabric_level_id = Number(list[i].gray_fabric_level_id)
    list[i].gray_fabric_color_id = Number(list[i].gray_fabric_color_id)
    list[i].roll = formatPriceMul(list[i].roll)
    list[i].grey_fabric_width_unit_id = Number(list[i].grey_fabric_width_unit_id)
    list[i].grey_fabric_gram_weight_unit_id = Number(list[i].grey_fabric_gram_weight_unit_id)
    list[i].material_ratio = list[i].raw_material_info
    list[i].product_kind_id = list[i].type_grey_fabric_id
  }

  const query = {
    customer_id: state.form.customer_id,
    sale_system_id: state.form.sale_system_id,
    sale_user_id: state.form.sale_user_id,
    voucher_number: state.form.voucher_number,
    order_time: formatDate(state.form.date),
    receipt_time: formatDate(state.form.delivery_date),
    deposit: state.form.deposit,
    plan_type: 2,
    sale_tax_rate: formatPriceMul(state.form.sale_tax_rate),
    internal_remark: state.form.internal_remark,
    is_with_tax_rate: state.form.is_with_tax_rate,
    item_data: list,
    settle_method_id: Number(state.form.settle_method_id) || 0,
    // delivery_type_id: Number(state.form.delivery_type_id) || 0,
    texture_url: state.fileList ? state.fileList.join(',') : '',
    // 以下是地址卡的收货方式数据 addressInfos
    location: addressData.location.join(' '), // 收货地址(省市区)
    receipt_address: addressData.address, // 收货地址(详情地址)
    contact_name: addressData.contact_name, // 联系人
    customer_phone: addressData.phone, // 联系电话
    logistics_area: addressData.logistics_area, // 物流区域
    is_default: addressData.is_default, // 是否默认地址
    logistics_company: addressData.logistics_company, // 物流公司名称
    process_factory: addressData.name, // 加工厂昵称
    print_tag: state.form.print_tag || addressData.print_tag, // 出货标签
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'GreySalePlanDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
// 选择客户
async function customerChange(val: any) {
  state.form.customer_name = val?.name
  state.form.customer_code = val?.code
  state.form.sale_user_id = !val.seller_id ? null : val.seller_id
  state.form.sale_system_id = val?.select_sale_system_id || ''
}

const bulkShow = ref(false)

const bulkSetting = ref<any>({})

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  TableList.value?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

function change(row: any) {
  row.weight = (row.weight_of_fabric * row.roll).toFixed(2)

  blur(row)
}

const bulkList = reactive<any>([
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    component: 'input',
    type: 'text',
  },
  {
    field: 'grey_fabric_width_unit_id',
    title: '坯布幅宽单位',
    component: 'select',
    api: 'GetDictionaryDetailEnumListApi',
    labelField: 'name',
    valueField: 'id',
  },
  {
    field: 'grey_fabric_gram_weight_unit_id',
    title: '坯布克重单位',
    component: 'select',
    api: 'GetDictionaryDetailEnumListApi',
    labelField: 'name',
    valueField: 'id',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    component: 'input',
    type: 'text',
  },
  {
    field: 'total_needle_size',
    title: '总针数',
    component: 'input',
    type: 'text',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
    labelField: 'name',
    valueField: 'id',
  },
  {
    field: 'gray_fabric_level_id',
    title: '坯布等级',
    component: 'select',
    api: 'getInfoBaseGreyFabricLevelList',
    labelField: 'name',
    valueField: 'id',
  },
  {
    field: 'weight_of_fabric',
    title: '坯布定重',
    component: 'input',
    type: 'number',
  },
  {
    field: 'roll',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'weight',
    title: '数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'upper_limit',
    title: '上限',
    component: 'input',
    type: 'float',
  },
  {
    field: 'lower_limit',
    title: '下限',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'float',
  },
])

const columnList = ref([
  {
    field: 'code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 250,
    soltName: 'grey_fabric_width',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 250,
    soltName: 'grey_fabric_gram_weight',
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 150,
    soltName: 'needle_size',
  },
  {
    field: 'total_needle_size',
    title: '总针数',
    minWidth: 150,
    soltName: 'total_needle_size',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    minWidth: 100,
    soltName: 'gray_fabric_color_id',
  },
  {
    field: 'gray_fabric_level_id',
    title: '坯布等级',
    minWidth: 150,
    soltName: 'gray_fabric_level_id',
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 150,
  },
  {
    field: 'weight_of_fabric',
    title: '坯布定重',
    minWidth: 150,
    soltName: 'weight_of_fabric',
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    soltName: 'roll',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    soltName: 'weight',
    required: true,
  },
  {
    field: 'upper_limit',
    title: '上限',
    minWidth: 100,
    soltName: 'upper_limit',
  },
  {
    field: 'lower_limit',
    title: '下限',
    minWidth: 100,
    soltName: 'lower_limit',
  },
  {
    field: 'unit_price',
    title: '单价',
    minWidth: 100,
    soltName: 'unit_price',
    required: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    soltName: 'other_price',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
])

const columnList_ls = ref([
  {
    field: 'code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'raw_material_color_name',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
    soltName: 'yarn_loss',
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
    soltName: 'yarn_ratio',
    required: true,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
    soltName: 'use_yarn_quantity',
  },
])

const { 聚焦下一行 } = useTableEnterAutoFocus(
  tableRef,
  TableList,
)
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户名称:" required>
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                is-merge
                field="name"
                :default-value="{
                  id: state.form.customer_id,
                  name: state.form.customer_name,
                  code: state.form.customer_code,
                }"
                show-choice-system
                @change-value="customerChange"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable placeholder="请选择" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="订单日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="订单日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="交货日期:">
          <template #content>
            <el-form-item prop="delivery_date">
              <el-date-picker v-model="state.form.delivery_date" type="date" placeholder="交货日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="销售员:">
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents v-model="state.form.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="合同编号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" placeholder="请输入" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="合同定金:">
          <template #content>
            <vxe-input v-model="state.form.deposit" type="float" clearable placeholder="请输入">
              <template #suffix>
                元
              </template>
            </vxe-input>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="结算方式:">
          <template #content>
            <SelectSettleTypeDialog
              v-model="state.form.settle_method_id"
              field="name"
            />
            <!--            <SelectComponents v-model="state.form.settle_method_id" style="width: 100%" api="GetInfoSaleSettlementMethodEnumList" label-field="name" value-field="id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:">
          <template #content>
            <el-input v-model="state.form.internal_remark" placeholder="请输入" :maxlength="200" />
          </template>
        </DescriptionsFormItem>
        <div style="display: flex; align-items: center">
          <div style="min-width: 96px; text-align: right; margin-right: 15px">
            <el-checkbox
              v-model="state.form.is_with_tax_rate"
              label="是否含税"
            />
          </div>
          <vxe-input
            v-model="state.form.sale_tax_rate"
            placeholder="税率"
            :min="0"
            type="float"
            :controls="false"
            clearable
            :disabled="!state.form.is_with_tax_rate"
            style="width: 170px"
          >
            <template #suffix>
              %
            </template>
          </vxe-input>
        </div>
      </div>
    </el-form>
    <!-- 地址卡片 -->
    <div class="m-[10px]">
      <AddressCard
        ref="addressCardRef"
        type="Add"
        :is-show-delivery="false"
        :sale-system-ids="state.form.sale_system_id"
        :customer-ids="state.form.customer_id"
        :customer-name="state.form.customer_name"
      />
    </div>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <template #right-top>
      <el-button @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" @click="handAdd">
        从资料中添加
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="TableList" :column-list="columnList">
      <template #grey_fabric_width="{ row }">
        <el-input v-model="row.grey_fabric_width" style="width: 220px" clearable placeholder="坯布幅宽">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        <el-input v-model="row.grey_fabric_gram_weight" style="width: 220px" clearable placeholder="坯布克重">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #needle_size="{ row, rowIndex }">
        <vxe-input :id="`needle_size-${rowIndex}`" v-model="row.needle_size" placeholder="请输入" @keydown="聚焦下一行(rowIndex, $event, 'needle_size')" />
      </template>
      <template #total_needle_size="{ row, rowIndex }">
        <vxe-input :id="`total_needle_size-${rowIndex}`" v-model="row.total_needle_size" placeholder="请输入" @keydown="聚焦下一行(rowIndex, $event, 'total_needle_size')" />
      </template>
      <template #gray_fabric_color_id="{ row }">
        <SelectComponents v-model="row.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
      </template>
      <template #gray_fabric_level_id="{ row }">
        <SelectComponents v-model="row.gray_fabric_level_id" api="getInfoBaseGreyFabricLevelList" label-field="name" value-field="id" clearable />
      </template>
      <template #weight_of_fabric="{ row, rowIndex }">
        <vxe-input :id="`weight_of_fabric-${rowIndex}`" v-model="row.weight_of_fabric" clearable @blur="change(row)" @keydown="聚焦下一行(rowIndex, $event, 'weight_of_fabric')" />
      </template>
      <template #roll="{ row, rowIndex }">
        <vxe-input :id="`roll-${rowIndex}`" v-model="row.roll" :min="0" type="float" clearable @blur="change(row)" @keydown="聚焦下一行(rowIndex, $event, 'roll')" />
      </template>
      <template #weight="{ row, rowIndex }">
        <vxe-input :id="`weight-${rowIndex}`" v-model="row.weight" :min="0" type="float" clearable @blur="blur(row)" @change="computed_total_price(row)" @keydown="聚焦下一行(rowIndex, $event, 'weight')" />
      </template>
      <template #number_cash="{ row, rowIndex }">
        <vxe-input :id="`number_cash-${rowIndex}`" v-model="row.number_cash" type="float" clearable @keydown="聚焦下一行(rowIndex, $event, 'number_cash')" />
      </template>
      <template #upper_limit="{ row, rowIndex }">
        <vxe-input :id="`upper_limit-${rowIndex}`" v-model="row.upper_limit" :min="0" type="float" clearable @keydown="聚焦下一行(rowIndex, $event, 'upper_limit')">
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #lower_limit="{ row, rowIndex }">
        <vxe-input :id="`lower_limit-${rowIndex}`" v-model="row.lower_limit" :min="0" type="float" clearable @keydown="聚焦下一行(rowIndex, $event, 'lower_limit')">
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #unit_price="{ row, rowIndex }">
        <vxe-input :id="`unit_price-${rowIndex}`" v-model="row.unit_price" type="float" clearable @change="computed_total_price(row)" @keydown="聚焦下一行(rowIndex, $event, 'unit_price')" />
      </template>
      <template #other_price="{ row, rowIndex }">
        <vxe-input :id="`other_price-${rowIndex}`" v-model="row.other_price" :min="0" type="float" clearable @change="computed_total_price(row)" @keydown="聚焦下一行(rowIndex, $event, 'other_price')" />
      </template>
      <template #remark="{ row, rowIndex }">
        <vxe-input :id="`remark-${rowIndex}`" v-model="row.remark" clearable @keydown="聚焦下一行(rowIndex, $event, 'remark')" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text link type="primary" @click="handCopy(rowIndex)">
          复制
        </el-button>
        <el-button text link type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱信息" class="mt-[5px]">
    <Table ref="tableRefOne" :config="tableConfig_ls" :table-list="state.use_yarnList" :column-list="columnList_ls">
      <template #yarn_loss="{ row }">
        <vxe-input v-model="row.yarn_loss" type="float" clearable @blur="blur(row)">
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #yarn_ratio="{ row }">
        <vxe-input v-model="row.yarn_ratio" type="float" clearable @blur="blur(row)">
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #use_yarn_quantity="{ row }">
        <vxe-input v-model="row.use_yarn_quantity" type="float" clearable @blur="handBlur(row)" />
      </template>
    </Table>
  </FildCard>
  <FildCard title="凭证信息" class="mt-[5px]" :tool-bar="false">
    <TextureMapWall v-model:image-list="state.fileList" text="" />
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style></style>
