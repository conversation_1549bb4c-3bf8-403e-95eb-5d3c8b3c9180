import { useRequest } from '@/use/useRequest'

/**
 * 获取原料扣款出货单列表
 */
export const GetReturnReceiveOrderList = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive/list`,
    method: 'get',
  })
}

/**
 * 获取原料扣款出货单详情
 */
export const GetReturnReceiveOrderDetail = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive`,
    method: 'get',
  })
}

/**
 * 更新原料扣款出货单
 */
export const UpdateReturnReceiveOrder = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive`,
    method: 'put',
  })
}

/**
 * 添加原料扣款出货单
 */
export const AddReturnReceiveOrder = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive`,
    method: 'post',
  })
}

/**
 * 作废原料扣款出货单
 */
export const CancelReturnReceiveOrder = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive/cancel`,
    method: 'put',
  })
}

/**
 * 审核原料扣款出货单
 */
export const PassReturnReceiveOrder = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive/pass`,
    method: 'put',
  })
}

/**
 * 驳回原料扣款出货单
 */
export const RejectReturnReceiveOrder = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive/reject`,
    method: 'put',
  })
}

/**
 * 消审原料扣款出货单
 */
export const WaitReturnReceiveOrder = () => {
  return useRequest({
    url: `/admin/v1/raw_material/raw_process_order/return_receive/wait`,
    method: 'put',
  })
}
