<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { parseAddress } from './addressParser'
import type { AddressFormData } from './types'
import CascaderAddress from '@/components/CascaderAddress/index.vue'
import { GetDistrictEnumList } from '@/api/addressCard'

const props = defineProps<{
  visible: boolean
  editData?: Partial<AddressFormData>
}>()

const emit = defineEmits(['update:visible', 'submit'])

// 生成唯一ID的工具函数
function generateUniqueId(): number {
  return Date.now() + Math.floor(Math.random() * 1000)
}

const formRef = ref()
const form = reactive<AddressFormData>({ // 更新表单初始值
  id: 0,
  name: '',
  phone: '',
  location: [],
  address: '',
  logistics_company: '',
  logistics_area: '',
  is_default: false,
  biz_uint_id: 0,
  contact_name: '',
  print_tag: '',
  intelligent_iden: '', // 智能识别的值
})
// 表单验证规则
const rules = reactive({
  contact_name: [
    { required: true, message: '联系人名称不能为空', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' },
  ],
  location: [
    { required: true, message: '省市区不能为空', trigger: 'change' },
  ],
  address: [
    { required: true, message: '收货地址不能为空', trigger: 'blur' },
  ],
})

// 修改判断模式的逻辑
const isEditMode = computed(() => {
  return !!(props.editData && (props.editData.id && props.editData.id !== 0))
})

// 修改标题显示的逻辑
const dialogTitle = computed(() => {
  return isEditMode.value ? '编辑收货地址' : '新增收货地址'
})

// 重置表单时的处理
function resetForm() {
  const defaultForm = {
    id: 0,
    name: '',
    phone: '',
    location: [],
    address: '',
    logistics_company: '',
    logistics_area: '',
    is_default: false,
    biz_uint_id: 0,
    contact_name: '',
    print_tag: '',
  }
  // 如果是编辑模式，使用编辑数据，否则使用默认值
  if (isEditMode.value && props.editData)
    Object.assign(form, props.editData)

  else Object.assign(form, defaultForm)
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
  // 延迟重置表单，等待关闭动画结束
  setTimeout(() => {
    formRef.value?.resetFields()
    resetForm()
  }, 300)
}

// 提交表单
const cascaderAddressRef = ref()
async function handleSubmit() {
  const selectLabel = cascaderAddressRef.value?.cascaderRef?.getCheckedNodes()?.[0]?.label
  if (!selectLabel)
    form.location = [] // 清空location字段

  if (!formRef.value)
    return

  try {
    await formRef.value.validate()

    // 检查并生成ID
    if (!form.id || form.id === 0)
      form.id = generateUniqueId()

    const submitData = {
      ...form,
      // 确保其他必要字段都已填写
      location: Array.isArray(form.location) ? form.location : [],
      is_default: form.is_default || false,
      biz_uint_id: form.biz_uint_id || 0,
    }

    // 提交时携带模式信息
    emit('submit', {
      mode: isEditMode.value ? 'edit' : 'add',
      data: JSON.parse(JSON.stringify(submitData)),
    })

    // ElMessage.success(`${isEditMode.value ? '编辑' : '新增'}成功`)
    handleClose()
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 监听弹窗显示状态，初始化表单
watch(() => props.visible, (newVal) => {
  if (newVal)
    resetForm()
}, { immediate: true, deep: true })

// 获取省市区数据
const { fetchData: getDistrictEnumList } = GetDistrictEnumList()
let addressMap: any = []
onMounted(async () => {
  const res = await getDistrictEnumList()
  addressMap = res?.data?.list || []
})
// 智能识别
function handleIntelligentIden() {
  const val = form.intelligent_iden
  if (!val)
    return ElMessage.error('请输入需要智能识别的地址信息')
  const parseResult = parseAddress(val, addressMap)
  // 更新表单数据
  form.contact_name = parseResult.name
  form.phone = parseResult.phone
  let location = [parseResult?.address?.province?.name || '', parseResult?.address?.city?.name || '', parseResult.address?.district?.name || '']
  // 去除空值
  location = location.filter(item => item)
  form.location = location
  form.address = parseResult?.address?.detail || ''
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    class="address-dialog"
    width="800px"
    :close-on-click-modal="false"
    @update:model-value="emit('update:visible', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="95"
      label-position="left"
      class="address-form"
    >
      <div class="relative">
        <el-input
          v-model="form.intelligent_iden"
          class="mb-4"
          type="textarea"
          :rows="3"
          resize="none"
          clearable
          placeholder="粘贴地址自动识别，如：陆先生，136xxxxxxx，广东省深圳市南山区xx路xx号"
        />
        <el-button class="absolute right-[5px] bottom-[20px]" type="primary" :disabled="!form.intelligent_iden" @click="handleIntelligentIden">
          智能识别
        </el-button>
        <div />
      </div>
      <div class="form-row">
        <el-form-item label="联系人" prop="contact_name" class="form-item">
          <el-input
            v-model="form.contact_name"
            clearable
            placeholder="请输入联系人姓名"
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="phone" class="form-item">
          <el-input
            v-model="form.phone"
            maxlength="11"
            clearable
            placeholder="请输入联系电话"
          />
        </el-form-item>
      </div>

      <el-form-item label="省市区" prop="location">
        <CascaderAddress ref="cascaderAddressRef" v-model="form.location" />
      </el-form-item>

      <el-form-item label="收货地址" prop="address">
        <el-input
          v-model="form.address"
          type="textarea"
          :rows="4"
          resize="none"
          clearable
          placeholder="请输入详细的收货地址信息"
        />
      </el-form-item>

      <div class="form-row">
        <el-form-item label="加工厂名称" prop="name" class="form-item">
          <el-input
            v-model="form.name"
            clearable
            placeholder="请输入加工厂名称"
          />
        </el-form-item>

        <el-form-item label="物流公司" prop="logistics_company" class="form-item">
          <el-input
            v-model="form.logistics_company"
            clearable
            placeholder="请输入物流公司名称"
          />
        </el-form-item>
      </div>

      <el-form-item label="物流区域" prop="logistics_area">
        <el-input
          v-model="form.logistics_area"
          clearable
          placeholder="请输入物流区域"
        />
      </el-form-item>

      <!-- 更新表单项的属性名 -->
      <el-form-item label="出货标签" prop="print_tag">
        <el-input
          v-model="form.print_tag"
          clearable
          placeholder="请输入打印标签"
        />
      </el-form-item>

      <el-form-item label=" ">
        <el-switch
          v-model="form.is_default"
          class="default-switch"
          active-text="设置默认地址"
        />
      </el-form-item>

      <div class="form-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
        >
          {{ isEditMode ? '保存' : '确定' }}
        </el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<style lang="scss" scoped>
.address-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 30px 30px;
  }
}

.address-form {
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 0;

    .form-item {
      flex: 1;
      margin-bottom: 22px;
      align-items: baseline;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 22px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      padding-right: 12px;
    }

    // 必填项星号样式
    .el-form-item__label::before {
      margin-right: 4px;
      color: var(--el-color-danger);
    }

    .el-input__wrapper,
    .el-textarea__inner {
      box-shadow: 0 0 0 1px #dcdfe6 inset;

      &:hover {
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      }
    }
  }

  .default-switch {
    margin-left: 0;
  }

  .form-footer {
    display: flex;
    justify-content: end;
    gap: 12px;
    margin-top: 30px;

    .el-button {
      min-width: 100px;
      padding: 12px 20px;
    }
  }
}
</style>
