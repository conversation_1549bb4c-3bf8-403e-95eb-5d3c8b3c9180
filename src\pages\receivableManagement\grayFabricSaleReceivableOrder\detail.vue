<script setup lang="ts">
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { getGreyFabric, updateAuditStatusCancel, updateAuditStatusPass, updateAuditStatusReject, updateAuditStatusWait } from '@/api/grayFabricSaleReceivableOrder'
import { formatDate, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DetailPageInfo from '@/components/DetailPageInfo/index.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'

const baseInfoKeys = [
  {
    key: 'sale_system_name',
    label: '营销体系名称',
  },
  {
    key: 'customer_name',
    label: '客户名称',
  },
  {
    key: 'order_time',
    label: '单据日期',
  },
  {
    key: 'sale_user_name',
    label: '销售员',
  },
  {
    key: 'order_remark',
    label: '备注',
    copies: 2,
  },
]
const settleInfoKeys = [
  {
    key: 'total_settle_money',
    label: '销售金额',
    preUnit: '￥',
  },
  {
    key: 'total_should_collect_money',
    label: '应收金额',
    preUnit: '￥',
  },
  {
    key: 'total_remove_money',
    label: '优惠金额',
    preUnit: '￥',
  },
  {
    key: 'total_discount_money',
    label: '折扣金额',
    preUnit: '￥',
  },
  {
    key: 'total_chargeback_money',
    label: '扣款金额',
    preUnit: '￥',
  },
  {
    key: 'total_collected_money',
    label: '已收金额',
    preUnit: '￥',
  },
  {
    key: 'total_uncollect_money',
    label: '未收金额',
    preUnit: '￥',
  },
  {
    key: 'settle_type_name',
    label: '结算类型',
  },
  {
    key: 'payment_deadline',
    label: '回款截止日期',
    is_date: true,
  },
  {
    key: 'liquidated_day',
    label: '逾期天数',
    unit: '天',
  },
]
const state = reactive<any>({
  statusData: {
    order_no: '',
    audit_status: '',
    audit_status_name: '',
    src_order_no: '', // 来源单号
    collect_status_name: '', // 收款状态
  },
  // 基础信息
  baseInfoData: {
    order_no: '',
    remark: '',
  },
  //   结算信息
  settleInfoData: {
    order_no: '',
  },
})

// 表格信息
const rawMaterialInfo = reactive<any>({
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    fieldApiKey: 'GrayFabricSaleReceivableDetailRawMaterialInfo',
    footerMethod: (val: any) => rawMaterialInfo.footerMethod(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['roll'].includes(column.field))
          return sumNum(data, 'roll', '')

        if (['weight'].includes(column.field))
          return sumNum(data, 'weight', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['settle_price'].includes(column.field))
          return sumNum(data, 'settle_price', '')

        if (['other_price'].includes(column.field))
          return sumNum(data, 'other_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      title: '',
      fixed: 'left',
      childrenList: [
        {
          field: 'code',
          title: '坯布编号',
          minWidth: 100,
        },
        {
          field: 'name',
          title: '坯布名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '坯布信息',
      childrenList: [
        {
          field: 'grey_fabric_width',
          soltName: 'grey_fabric_width',
          title: '幅宽',
          minWidth: 100,
        },
        {
          field: 'grey_fabric_gram_weight',
          soltName: 'grey_fabric_gram_weight',
          title: '克重',
          minWidth: 100,
        },
        {
          field: 'needle_size',
          title: '针寸数',
          minWidth: 100,
        },
        {
          field: 'yarn_batch',
          title: '纱批',
          minWidth: 100,
        },
        {
          field: 'gray_fabric_color_name',
          title: '织坯颜色',
          minWidth: 100,
        },
        {
          field: 'grey_fabric_level_name',
          title: '坯布等级',
          minWidth: 100,
        },
        {
          field: 'machine_number',
          title: '机台号',
          minWidth: 100,
        },
        {
          field: 'grey_fabric_remark',
          title: '坯布备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '数量信息',
      childrenList: [
        {
          field: 'roll',
          title: '匹数',
          minWidth: 100,
        },
        // {
        //   field: 'weight',
        //   title: '件重（kg）',
        //   minWidth: 100,
        // },
        {
          field: 'settle_weight',
          title: '总数量（kg）',
          minWidth: 100,
        },
      ],
    },
    {
      title: '金额信息',
      childrenList: [
        {
          field: 'other_price',
          title: '其他金额',
          width: 100,
        },
        {
          field: 'sale_price',
          soltName: 'sale_price',
          title: '单价',
          minWidth: 100,
        },
        {
          field: 'settle_price',
          title: '总金额',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      childrenList: [
        {
          field: 'remark',
          title: '备注',
          width: 100,
        },
      ],
    },
    // {
    //   title: '',
    //   fixed: 'right',
    //   childrenList: [
    //     {
    //       field: 'operate',
    //       soltName: 'operate',
    //       title: '操作',
    //       width: 100,
    //     },
    //   ],
    // },
  ],
})

// 回款信息
const returnedMoneyInfo = reactive<any>({
  tableConfig: {
    showSlotNums: false,
    fieldApiKey: 'GrayFabricSaleReceivableDetailReturnedMoneyInfo',
    footerMethod: (val: any) => returnedMoneyInfo.footerMethod(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['collect_price'].includes(column.field))
          return sumNum(data, 'collect_price', '')

        if (['offset_price'].includes(column.field))
          return sumNum(data, 'offset_price', '')

        if (['discount_price'].includes(column.field))
          return sumNum(data, 'discount_price', '')

        if (['deduction_price'].includes(column.field))
          return sumNum(data, 'deduction_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'actually_collect_order_no',
      title: '单据编号',
      minWidth: '8%',
    },
    {
      field: 'settle_type_name',
      title: '结算类型',
      minWidth: 100,
    },
    {
      field: 'collect_price',
      title: '收款金额',
      minWidth: 100,
    },
    {
      field: 'offset_price',
      title: '优惠金额',
      minWidth: 100,
    },
    {
      field: 'discount_price',
      title: '折扣金额',
      minWidth: 100,
    },
    {
      field: 'deduction_price',
      title: '扣款金额',
      minWidth: 100,
    },
    {
      field: 'operator_name',
      title: '操作人',
      minWidth: 100,
    },
    {
      field: 'auditor_name',
      title: '审核人',
      minWidth: 100,
    },
    {
      field: 'actually_collect_date',
      title: '收款时间',
      minWidth: 100,
      is_date: true,
    },
  ],
})

const route = useRoute()
const { fetchData: DetailFetch, data: detailData } = getGreyFabric()

// 获取数据
async function getData() {
  await DetailFetch({ id: route.query.id })
  state.statusData = {
    ...detailData.value,
  }
  state.baseInfoData = {
    ...detailData.value,
    order_time: formatDate(detailData.value.order_time),
  }
  state.settleInfoData = {
    ...detailData.value,
    total_should_collect_money: formatTwoDecimalsDiv(detailData.value.total_should_collect_money),
    total_settle_money: formatTwoDecimalsDiv(detailData.value.total_settle_money),
    total_remove_money: formatTwoDecimalsDiv(detailData.value.total_remove_money),
    total_discount_money: formatTwoDecimalsDiv(detailData.value.total_discount_money),
    total_chargeback_money: formatTwoDecimalsDiv(detailData.value.total_chargeback_money),
    total_collected_money: formatTwoDecimalsDiv(detailData.value.total_collected_money),
    total_uncollect_money: formatTwoDecimalsDiv(detailData.value.total_uncollect_money),
    payment_deadline: formatDate(detailData.value.payment_deadline),
  }

  rawMaterialInfo.list
    = detailData.value?.items?.map((item: any) => {
      const settle_price = formatTwoDecimalsDiv(Number(item.settle_price))

      return {
        ...item,
        roll: formatTwoDecimalsDiv(Number(item.roll)),
        other_price: formatTwoDecimalsDiv(Number(item.other_price)),
        sale_price: formatUnitPriceDiv(Number(item.sale_price)),
        return_price: formatUnitPriceDiv(Number(item.return_price)),
        settle_price,
        weight: formatWeightDiv(Number(item.weight)),
        settle_weight: formatWeightDiv(Number(item.settle_weight)),
      }
    }) || []
  returnedMoneyInfo.list
    = detailData.value?.collect_records?.map((item: any) => {
      return {
        ...item,
        collect_price: formatTwoDecimalsDiv(Number(item.collect_price)),
        offset_price: formatTwoDecimalsDiv(Number(item.offset_price)),
        discount_price: formatTwoDecimalsDiv(Number(item.discount_price)),
        deduction_price: formatTwoDecimalsDiv(Number(item.deduction_price)),
      }
    }) || []
}
onMounted(() => {
  getData()
})

// 操作-审核、驳回、作废、消审
async function updateStatus(audit_status: number) {
  const id: any = route.query.id?.toString()
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateAuditStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateAuditStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateAuditStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateAuditStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}
</script>

<template>
  <StatusColumn
    :order_no="state.statusData.order_no"
    :order_id="state.statusData.id"
    :src_order_no="state.statusData.src_order_no"
    :status="state.statusData.audit_status"
    :status_name="state.statusData.audit_status_name"
    :collect_status_name="state.statusData.collect_status_name"
    permission_print_key=""
    permission_wait_key="GrayFabricSaleReceivableOrder_wait"
    permission_reject_key="GrayFabricSaleReceivableOrder_reject"
    permission_pass_key="GrayFabricSaleReceivableOrder_pass"
    permission_cancel_key="GrayFabricSaleReceivableOrder_cancel"
    permission_edit_key="GrayFabricSaleReceivableOrder_edit"
    edit_router_name="GrayFabricSaleReceivableOrderEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  >
    <!-- <template #print>
      <el-button>打印</el-button>
      <el-button>预览</el-button>
    </template> -->
  </StatusColumn>
  <!--  -->
  <!-- 基础信息 -->
  <DetailPageInfo key="baseInfo" class="mt-[5px]" title="基础信息" :info-keys="baseInfoKeys" :info-data="state.baseInfoData" />
  <!-- 原料信息 -->
  <FildCard class="mt-[5px]" title="原料信息" :tool-bar="true">
    <Table :config="rawMaterialInfo.tableConfig" :table-list="rawMaterialInfo.list" :column-list="rawMaterialInfo.columnList">
      <template #sale_price="{ row }">
        <span v-if="[2, 4, 6].includes(state.statusData.collect_type)">{{ row.return_price }}</span>
        <span v-else>{{ row.sale_price }}</span>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }}
        {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <!-- 结算信息 -->
  <DetailPageInfo key="settleInfo" class="mt-[5px]" title="结算信息" :info-keys="settleInfoKeys" :info-data="state.settleInfoData" />
  <!-- 回款信息 -->
  <FildCard class="mt-[5px]" title="回款信息" :tool-bar="true">
    <Table :config="returnedMoneyInfo.tableConfig" :table-list="returnedMoneyInfo.list" :column-list="returnedMoneyInfo.columnList" />
  </FildCard>
</template>
