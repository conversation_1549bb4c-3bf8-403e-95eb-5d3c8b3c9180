<script setup lang="ts" name="FinishPurchaseWarehouseEntryAdd">
import { ElButton, ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import currency from 'currency.js'
import AccordingLibAdd from '../components/AccordingLibAdd.vue'
import FinishProductionAdd from '../components/AccordingPurchaseAdd.vue'
import FineSizeAdd from '../components/FineSizeRepertoryAdd.vue'
import { editColumnsConfig, getSFRoll, isFinishEnter } from './utils'
import { addFpmPrcInOrder } from '@/api/finishPurchaseWarehouseEntry'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import {
  BusinessUnitIdEnum,
  DictionaryType,
  EmployeeType,
} from '@/common/enum'
import {
  formatDate,
  formatHashTag,
  formatLengthMul,
  formatTwoDecimalsMul,
  formatUnitPriceMul,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import {
  deleteToast,
  getDefaultSaleSystem,
  getFilterData,
  isMainUnit,
} from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import { SaleModeEnum } from '@/enum/orderEnum'

const routerList = useRouterList()
const AccordingLibAddRef = ref()
const FineSizeAddRef = ref()
const FinishProductionAddRef = ref()
const formRef = ref()
const state = reactive<any>({
  formInline: {
    sale_system_id: '',
    biz_unit_id: '',
    warehouse_id: '',
    voucher_number: '',
    sale_mode: SaleModeEnum.Bulk, // 订单类型
    store_keeper_id: '',
    warehouse_in_time: new Date(),
    remark: '',
  },
  formRules: {
    sale_system_id: [
      { required: true, message: '请选择营销体系', trigger: 'change' },
    ],
    biz_unit_id: [
      { required: true, message: '请选择供应商', trigger: 'change' },
    ],
    warehouse_id: [
      { required: true, message: '请选择仓库', trigger: 'change' },
    ],
    warehouse_in_time: [
      { required: true, message: '请选择进仓日期', trigger: 'change' },
    ],
  },
  defaultData: {
    customer_id: 0,
  },
})
// 选择成品颜色
function changeProductColor(row: any, val: any) {
  row.product_color_code = val.product_color_code
  row.product_color_name = val.product_color_name
}

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  color_code: '',
})

let uuid = 0

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '总计'

      if (['paper_tube_weight'].includes(column.field))
        return sumNum(data, 'paper_tube_weight')

      if (['quote_roll'].includes(column.field))
        return sumNum(data, 'quote_roll')

      if (['quote_total_weight'].includes(column.field))
        return sumNum(data, 'quote_total_weight')

      if (['total_weight'].includes(column.field))
        return sumNum(data, 'total_weight')

      if (['weight_error'].includes(column.field))
        return sumNum(data, 'weight_error')

      if (['settle_weight'].includes(column.field))
        return sumNum(data, 'settle_weight')

      if (['quote_length'].includes(column.field))
        return sumNum(data, 'quote_length')

      if (['in_length'].includes(column.field))
        return sumNum(data, 'in_length')

      if (['total_price'].includes(column.field))
        return `￥${sumNum(data, 'total_price')}`

      return null
    }),
  ]
}

const finishProductionOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  datalist: [],
  columnList: editColumnsConfig,
  // 确认添加带出数据
  handleSure: (list: any) => {
    // 自动带出字段
    list = list.map((item: any) => {
      const temp = {
        ...item,
        uuid: ++uuid,
        dye_factory_color_code: item.dye_factory_color_num,
        customer_id: state.defaultData.customer_id,
        product_id: item.id,
        product_code: item.finish_product_code, // 成品编号
        product_name: item.finish_product_name, // 成品名称
        product_width: item.finish_product_width, // 成品幅宽
        finish_product_width_unit_name: item.finish_product_width_unit_name, // 成品幅宽
        finish_product_width_unit_id: item.finish_product_width_unit_id, // 成品幅宽
        product_gram_weight: item.finish_product_gram_weight, // 成品克重
        finish_product_gram_weight_unit_name:
          item.finish_product_gram_weight_unit_name, // 成品克重
        finish_product_gram_weight_unit_id:
          item.finish_product_gram_weight_unit_id, // 成品克重
        product_craft: item.finish_product_craft, // 成品工艺
        product_ingredient: item.finish_product_ingredient, // 成品成分
        unit_name: item.measurement_unit_name, // 单位
        unit_id: item.measurement_unit_id, // 单位
        product_weight_error: item.weight_error, // 成品资料空差
        product_paper_tube_weight: item.paper_tube_weight, // 成品资料纸筒质量
        item_fc_data: [],
        in_length: 0,
        length_unit_price: '',
        other_price: 0,
        unit_price: '',
        settle_weight: 0,
        paper_tube_weight: 0,
        weight_error: 0,
        in_roll: 0,
      }
      return conductUnitPrice(temp, true)
    })
    finishProductionOptions.datalist = [
      ...finishProductionOptions.datalist,
      ...list,
    ]
  },
  // 确认录入，需要统计部分字段
  handleSureFineSize: (list: any) => {
    const currentIndex = finishProductionOptions.rowIndex
    finishProductionOptions.datalist[
      currentIndex
    ].item_fc_data = list
    let paper_tube_weight = 0
    let total_weight = 0
    let weight_error = 0
    let in_length = 0
    list.forEach((item: any) => {
      paper_tube_weight = currency(paper_tube_weight).add(Number(item.paper_tube_weight || 0)).value
      total_weight = currency(total_weight).add(Number(item.base_unit_weight || 0)).value
      weight_error = currency(weight_error).add(Number(item.weight_error || 0)).value
      in_length = currency(in_length).add(Number(item.length || 0)).value
    })
    // const paper_tube_weight = list.reduce((pre: any, val: any) => pre + val.paper_tube_weight, 0)
    // const total_weight = list.reduce((pre: any, val: any) => pre + val.base_unit_weight, 0)
    // const weight_error = list.reduce((pre: any, val: any) => pre + val.weight_error, 0)
    const settle_weight = total_weight - weight_error
    // const in_length = list.reduce((pre: any, val: any) => pre + val.length, 0)
    finishProductionOptions.datalist[
      currentIndex
    ].paper_tube_weight = Number(paper_tube_weight.toFixed(2))
    finishProductionOptions.datalist[
      currentIndex
    ].total_weight = Number(total_weight.toFixed(2))
    finishProductionOptions.datalist[
      currentIndex
    ].weight_error = Number(weight_error.toFixed(2))
    finishProductionOptions.datalist[
      currentIndex
    ].in_length = Number(in_length.toFixed(2))
    finishProductionOptions.datalist[currentIndex].in_roll
      = Number(sumNum(list, 'roll'))
    finishProductionOptions.datalist[
      currentIndex
    ].settle_weight = Number(settle_weight.toFixed(2))
    finishProductionOptions.datalist[currentIndex].pt_weight_and_weight_error = currency(paper_tube_weight || 0).add(weight_error || 0).value
    finishProductionOptions.datalist[currentIndex].receive_weight = total_weight

    // 进仓金额 = (数量单价*结算数量)+(辅助数量单价*进仓辅助数量)+其他金额
    computedTotalPrice(finishProductionOptions.datalist[
      finishProductionOptions.rowIndex
    ])
    // const {unit_price,length_unit_price,other_price} = finishProductionOptions.datalist[finishProductionOptions.rowIndex]
    // finishProductionOptions.datalist[finishProductionOptions.rowIndex].total_price = (Number(unit_price)*settle_weight)+(Number(length_unit_price)*in_length)+Number(other_price)
  },
  // 确认添加带出数据
  handleSureFinishProduciton(list: any) {
    // 采购单号 采购匹数 采购总数量 采购辅助数量
    list = list.map((item: any) => {
      const temp = {
        ...item,
        customer_id: item.customer_id,
        product_code: item.finish_product_code,
        product_name: item.finish_product_name,
        product_id: item.finish_product_id,
        quote_order_no: item.order_no, // 采购单号
        quote_order_item_id: item.id,
        src_id: item.order_id,
        src_order_no: item.order_no,
        quote_weight: item.total_wait_collect_weight,
        finishProductionColorId: item.color_id,
        product_color_id: item.color_id,
        product_color_name: item.color_Name, // 颜色
        product_color_code: item.color_code, // 色号
        dye_factory_dyelot_number: item.dye_factory_vat_code, // 染厂缸号
        dye_factory_color_code: item.dye_factory_color_num, // 染厂色号
        product_width: item.finish_product_width, // 成品幅宽
        product_gram_weight: item.finish_product_gram_weight, // 成品克重
        finish_product_width_unit_name: item.finish_product_width_unit_name, // 成品幅宽
        finish_product_width_unit_id: item.finish_product_width_unit_id, // 成品幅宽
        finish_product_gram_weight_unit_name:
          item.finish_product_gram_weight_unit_name, // 成品克重
        finish_product_gram_weight_unit_id:
          item.finish_product_gram_weight_unit_id, // 成品克重
        product_ingredient: item.finish_product_ingredient, // 成品成分
        product_craft: item.finish_product_craft, // 成品工艺
        product_level_id: item.finish_product_level_id || 0, // 成品等级
        quote_roll: item.piece_count, // 采购匹数
        quote_length: item.length, // 采购辅助数量
        quote_total_weight: item.total_weight, // 采购总数量
        item_fc_data: [],
        in_length: 0,
        in_roll: 0,
        length_unit_price: '',
        other_price: 0,
        unit_price: '',
        settle_weight: 0,
        product_remark: item.remark,
        unit_name: item.unit,
        unit_id: item.measurement_unit_id,
        paper_tube_weight: 0,
        total_price: 0,
        total_weight: 0,
      }
      return conductUnitPrice(temp, true)
    })
    finishProductionOptions.datalist = [
      ...finishProductionOptions.datalist,
      ...list,
    ]
  },
  //   删除
  handleRowDel: async ({ id }: any) => {
    const res = await deleteToast('是否确认删除该成品')
    if (!res)
      return
    const index = finishProductionOptions.datalist.findIndex(
      (item: any) => item.id === id,
    )
    finishProductionOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit: () => handEdit,
})

const tableConfig = ref({
  showSlotNums: false,
  showSpanHeader: true,
  showCheckBox: true,
  fieldApiKey: 'FinishPurchaseWarehouseEntry',
  footerMethod: FooterMethod,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

// 处理字段不一致
function isMainUnitFormat(item: any) {
  return isMainUnit(item, 'unit_id')
}
/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.unit_id
    item.auxiliary_unit_name = item.unit_name
  }
  if (isMainUnitFormat(item))
    item.length_unit_price = 0 // 主单位-把辅助单价置0

  else
    item.unit_price = 0 // 辅助单位-把单价置0
  computedTotalPrice(item)
  return item
}

// 计算进仓金额
function computedTotalPrice(row: any) {
  // 进仓金额 = (数量单价*结算数量)+(辅助数量单价*进仓辅助数量)+其他金额
  const {
    unit_price = 0,
    other_price = 0,
  } = row

  // row.pt_weight_and_weight_error = currency(row.weight_error).add(row.paper_tube_weight).value

  // 应付数量
  row.should_pay_weight = currency(row.receive_weight).subtract(row.pt_weight_and_weight_error).value
  // 应付金额 应付单价 * 应付数量+其他应付
  row.total_price = currency(unit_price).multiply(row.should_pay_weight).add(other_price).value
}
// 色号和颜色联动
// function setFinishProductColor(item: any, row: any) {
//   row.finishProductionColorId = item?.id
//   row.product_color_id = item?.id
//   row.product_color_name = item?.product_color_name
//   row.product_color_code = item?.product_color_code
//   row.product_gram_weight = item?.finish_product_gram_weight
//   row.product_width = item?.finish_product_width
//   row.remark = item?.remark
// }

// 表格选中事件
function handAllSelect({ records }: any) {
  finishProductionOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  finishProductionOptions.multipleSelection = records
}

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  if (finishProductionOptions.multipleSelection.length < 1)
    return ElMessage.error('请选择数据')

  bulkShow.value = true
}
const bulkSetting = ref<any>({})

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_id',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: {
      sale_system_id: state.formInline.sale_system_id,
    },
  },
  // {
  //   field: 'finishProductionColorId',
  //   valueField: 'id',
  //   labelField: 'product_color_code',
  //   title: '色号',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  // },
  // {
  //   field: 'finishProductionColorId',
  //   valueField: 'id',
  //   labelField: 'product_color_name',
  //   title: '颜色',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  // },
  {
    field: 'dye_factory_color_code',
    title: '染厂色号',
    component: 'input',
  },
  {
    field: 'dye_factory_dyelot_number',
    title: '染厂缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'product_width',
    title: '成品幅宽',
    component: 'input',
  },
  {
    field: 'product_gram_weight',
    title: '成品克重(g)',
    component: 'input',
    type: 'text',
  },
  {
    field: 'product_level_id',
    field_name: 'product_level_id',
    title: '成品等级',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'product_remark',
    title: '成品备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'in_roll',
    title: '进仓匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '应付单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'in_length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  // {
  //   field: 'length_unit_price',
  //   title: '进仓辅助数量单价',
  //   component: 'input',
  //   type: 'float',
  // },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
function handleBlur(row: any) {
  row.should_pay_weight = currency(row.receive_weight || 0).subtract(row.pt_weight_and_weight_error || 0).value
  row.total_price = currency(row.unit_price || 0).multiply(row.should_pay_weight || 0).add(row.other_price || 0).value
}
async function bulkSubmit({ row, value }: any, val: any) {
  const ids = finishProductionOptions.multipleSelection.map(
    (item: any) => item.uuid,
  )
  finishProductionOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid)) {
      item[row.field] = value[row.field]
      if (row.field === 'finishProductionColorId') {
        item.product_color_name = val?.product_color_name
        item.product_color_code = val?.product_color_code
        item.product_color_id = val?.id
      }
    }
  })
  if (row.field)
    ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 根据资料添加
function showLibDialog() {
  AccordingLibAddRef.value.state.showModal = true
}

// 默认生成出仓条数数据
function showFineSizeDialog(row: any, rowIndex: number) {
  if (!state.formInline.warehouse_id)
    return ElMessage.error('请选择仓库')

  // FineSizeAddRef.value.state.showModal = true
  finishProductionOptions.rowIndex = rowIndex
  FineSizeAddRef.value.showDialog({
    ...row,
    finish_product_width_and_unit_name: `${row.product_width || ''}${row.finish_product_width_unit_name || ''}`,
    finish_product_gram_weight_and_unit_name: `${row.product_gram_weight || ''}${row.finish_product_gram_weight_unit_name || ''}`,
  }, state.formInline)
}
// 根据采购单添加
function showFinishProductionDialog() {
  FinishProductionAddRef.value.state.showModal = true
}

// 新增提交
const {
  fetchData: addFetch,
  data: successData,
  success: addSuccess,
  msg: addMsg,
} = addFpmPrcInOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 成品信息必填项
      // 判断戏码匹数和还有辅助数量和
      // let ximaLengthFlag = true // xima必须和进仓匹数一致
      // let lengthFlag = true // 辅助数量和与分录行辅助数量不一致
      for (let i = 0; i < finishProductionOptions.datalist.length; i++) {
        const item = finishProductionOptions.datalist[i]
        if (!finishProductionOptions.datalist[i].customer_id)
          return ElMessage.error('所属客户不能为空')

        if (!finishProductionOptions.datalist[i].product_code)
          return ElMessage.error('成品编号不能为空')

        if (!finishProductionOptions.datalist[i].product_name)
          return ElMessage.error('成品名称不能为空')

        if (!finishProductionOptions.datalist[i].finishProductionColorId)
          return ElMessage.error('色号颜色不能为空')

        // if (!finishProductionOptions.datalist[i].product_color_code)
        //   return ElMessage.error('色号不能为空')

        // if (finishProductionOptions.datalist[i].unit_price === '')
        //   return ElMessage.error('基本单位单价不能为空')

        if (!finishProductionOptions.datalist[i].dye_factory_dyelot_number)
          return ElMessage.error('染厂缸号不能为空')

        if (finishProductionOptions.datalist[i].in_roll === '')
          return ElMessage.error('进仓匹数不能为空')

        if (!Number(finishProductionOptions.datalist[i].total_weight))
          return ElMessage.error('进仓数量不能为空且不能为0')

        if (!item.auxiliary_unit_id)
          return ElMessage.error(`成品编号为${item.product_code}的数据,结算单位不能为空`)

        if (item.unit_price === '')
          return ElMessage.error(`成品编号为${item.product_code}的数据,基本单位单价不能为空`)

        if (!Number(item.in_length) && !isMainUnitFormat(item))
          return ElMessage.error(`成品编号为${item.product_code}的数据,进仓辅助数量不能为空且不能为0`)

        // if (item.length_unit_price === '' && !isMainUnitFormat(item))
        //   return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量单价不能为空`)

        if (finishProductionOptions.datalist[i]?.item_fc_data?.length) {
          let roll = 0
          let length = 0
          finishProductionOptions.datalist[i].item_fc_data.forEach(
            (item: any) => {
              roll += Number(item.roll)
              length += Number(item.length)
            },
          )
          // const roll = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.roll), 0)
          if (
            Number(finishProductionOptions.datalist[i].in_roll)
            !== Number(roll.toFixed(2))
          )
            return ElMessage.error('进仓匹数与细码匹数总量不一致')

          // const length = finishProductionOptions.datalist[i].item_fc_data.reduce((pre: any, val: any) => pre + Number(val.length), 0)
          if (
            Number(finishProductionOptions.datalist[i].in_length)
            !== Number(length.toFixed(2))
          )
            return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
        }
        else {
          return ElMessage.error('进仓匹数与细码匹数总量不一致')
        }
      }
      // if (!ximaLengthFlag) {
      //   return ElMessage.error('进仓匹数与细码匹数总量不一致')
      // }
      // if (!lengthFlag) {
      //   return ElMessage.error('进仓辅助数量与细码辅助数量总量不一致')
      // }
      // 整理参数
      const query = {
        ...state.formInline,
        store_keeper_id: state.formInline.store_keeper_id || 0,
        warehouse_in_time: formatDate(state.formInline.warehouse_in_time),
        item_data: finishProductionOptions.datalist.map((item: any) => {
          const item_fc_data = item.item_fc_data.map((v: any) => {
            return {
              ...v,
              roll: formatTwoDecimalsMul(v.roll),
              length: formatLengthMul(v.length),
              weight_error: formatWeightMul(v.weight_error),
              paper_tube_weight: formatWeightMul(v.paper_tube_weight),
              base_unit_weight: formatWeightMul(v.base_unit_weight),
            }
          })
          return {
            ...item,
            product_color_id: item.finishProductionColorId,
            product_level_id: item.product_level_id || 0,
            paper_tube_weight: formatWeightMul(Number(item.paper_tube_weight)),
            in_roll: formatTwoDecimalsMul(Number(item.in_roll)), // 100
            quote_roll: formatTwoDecimalsMul(Number(item.quote_roll)), // 100
            quote_total_weight: formatWeightMul(
              Number(item.quote_total_weight),
            ), // weight
            receive_weight: formatWeightMul(Number(item.receive_weight)), // weight
            pt_weight_and_weight_error: formatWeightMul(Number(item.pt_weight_and_weight_error)), // weight
            should_pay_weight: formatWeightMul(item.should_pay_weight),
            base_unit_weight: formatWeightMul(Number(item.base_unit_weight)), // weight
            total_weight: formatWeightMul(Number(item.total_weight)), // weight
            weight_error: formatWeightMul(Number(item.weight_error)), // weight
            settle_weight: formatWeightMul(Number(item.settle_weight)), // weight
            unit_price: formatUnitPriceMul(Number(item.unit_price)), // price
            quote_length: formatLengthMul(Number(item.quote_length)), // 100
            in_length: formatLengthMul(Number(item.in_length)), // 100
            length_unit_price: formatUnitPriceMul(
              Number(item.length_unit_price),
            ), // price
            other_price: formatTwoDecimalsMul(Number(item.other_price)), // 100
            total_price: formatTwoDecimalsMul(Number(item.total_price)), // 100
            item_fc_data,
          }
        }),
      }
      await addFetch(getFilterData(query))
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        routerList.push({
          name: 'FinishPurchaseWarehouseEntryDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => finishProductionOptions.datalist,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)
function clearCustomer(item: any) {
  state.formInline.warehouse_id = item?.default_physical_warehouse || ''
  state.defaultData.customer_id = item?.default_customer_id || 0
}

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  state.formInline.sale_system_id = res.default_sale_system_id
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <ElButton
        type="primary"
        :disabled="finishProductionOptions.datalist.length ? false : true"
        @click="submitAddAllData"
      >
        提交
      </ElButton>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '84px' }">
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                default-status
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="clearCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="供应商名称:">
          <template #content>
            <el-form-item prop="biz_unit_id">
              <!-- <SelectComponents
                api="BusinessUnitSupplierEnumlist"
                :query="{
                  unit_type_id: BusinessUnitIdEnum.finishedProduct,
                }"
                label-field="name"
                value-field="id"
                v-model="state.formInline.biz_unit_id"
                clearable
              /> -->
              <SelectDialog
                v-model="state.formInline.biz_unit_id"
                :query="{
                  unit_type_id: BusinessUnitIdEnum.finishedProduct,
                  name: componentRemoteSearch.name,
                }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="(val) => (componentRemoteSearch.name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="仓库名称:">
          <template #content>
            <el-form-item prop="warehouse_id">
              <SelectComponents
                v-model="state.formInline.warehouse_id"
                warehouse_type_id="finishProduction"
                api="GetPhysicalWarehouseDropdownList"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="进仓日期:">
          <template #content>
            <el-form-item prop="warehouse_in_time">
              <el-date-picker
                v-model="state.formInline.warehouse_in_time"
                type="date"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓管员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.store_keeper_id"
                :query="{
                  duty: EmployeeType.warehouseManager,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:">
          <template #content>
            <el-form-item prop="voucher_number">
              <el-input v-model="state.formInline.voucher_number" placeholder="凭证号" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:">
          <template #content>
            <SelectSaleMode v-model="state.formInline.sale_mode" :show-customer-book="false" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :copies="2" label="备注:">
          <template #content>
            <el-form-item>
              <vxe-textarea
                v-model="state.formInline.remark"
                style="width: 100%"
                maxlength="500"
                show-word-count
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[10px]" :tool-bar="true">
    <template #right-top>
      <ElButton type="primary" @click="handEdit">
        批量操作
      </ElButton>
      <ElButton type="primary" @click="showLibDialog">
        根据资料添加
      </ElButton>
      <ElButton
        :disabled="
          !(state.formInline.sale_system_id && state.formInline.biz_unit_id)
        "
        type="primary"
        @click="showFinishProductionDialog"
      >
        根据采购单添加
      </ElButton>
    </template>
    <div v-show="finishProductionOptions.datalist.length">
      <Table
        ref="tablesRef"
        :config="tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <!-- 所属客户 -->
        <template #customer_id="{ row }">
          <SelectDialog
            v-model="row.customer_id"
            :query="{ name: componentRemoteSearch.customer_name }"
            api="GetCustomerEnumList"
            :label-name="row.customer_name"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="(val) => (componentRemoteSearch.customer_name = val)"
          />
          <!-- <SelectComponents api="GetCustomerEnumList" label-field="name" value-field="id" v-model="row.customer_id" clearable /> -->
        </template>
        <template #product_code="{ row }">
          {{ formatHashTag(row.product_code, row.product_name) }}
        </template>
        <!-- 色号 -->
        <template #product_color_code="{ row }">
          <SelectMergeComponent
            v-model="row.finishProductionColorId"
            :custom-label="(row:any) => `${formatHashTag(row.product_color_code, row.product_color_name)}`"
            :query="{
              finish_product_id: row.product_id,
            }"
            :multiple="false"
            api-name="GetFinishProductColorDropdownList"
            remote
            remote-key="product_color_code_or_name"
            remote-show-suffix
            placeholder="色号、颜色"
            value-field="id"
            @change="changeProductColor(row, $event)"
          />
        </template>
        <!-- 染厂色号 -->
        <template #dye_factory_color_code="{ row }">
          <vxe-input
            v-model="row.dye_factory_color_code"
            maxlength="200"
          />
        </template>
        <!-- 染厂缸号 -->
        <template #dye_factory_dyelot_number="{ row }">
          <vxe-input
            v-model="row.dye_factory_dyelot_number"
            maxlength="200"
          />
        </template>
        <!-- 成品幅宽 -->
        <template #product_width="{ row }">
          <!-- <vxe-input v-model="row.product_width"></vxe-input> -->
          <el-input
            v-model="row.product_width"
            clearable
            placeholder="成品幅宽"
          >
            <template #append>
              <SelectComponents
                v-model="row.finish_product_width_unit_id"
                placeholder="单位"
                style="width: 80px"
                :query="{ dictionary_id: DictionaryType.width_unit }"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="(val:any) => row.finish_product_width_unit_name = val?.name || ''"
              />
            </template>
          </el-input>
        </template>
        <!-- 成品克重 -->
        <template #product_gram_weight="{ row }">
          <!-- <vxe-input v-model="row.product_gram_weight"></vxe-input> -->
          <el-input
            v-model="row.product_gram_weight"
            clearable
            placeholder="成品克重"
          >
            <template #append>
              <SelectComponents
                v-model="row.finish_product_gram_weight_unit_id"
                placeholder="单位"
                style="width: 80px"
                :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="(val:any) => row.finish_product_gram_weight_unit_name = val?.name || ''"
              />
            </template>
          </el-input>
        </template>
        <!-- 成品等级 -->
        <template #product_level_id="{ row }">
          <SelectComponents
            v-model="row.product_level_id"
            api="GetInfoBaseFinishedProductLevelEnumList"
            label-field="name"
            value-field="id"
            clearable
          />
        </template>
        <template #sale_plan_order_item_no="{ row }">
          <!-- <el-link type="primary" :underline="false" @click="handDetail(row)"> -->
          {{ row.sale_plan_order_item_no }}
          <!-- </el-link> -->
        </template>
        <!-- 成品备注 -->
        <template #product_remark="{ row }">
          <vxe-input v-model="row.product_remark" maxlength="200" />
        </template>
        <!-- 进仓匹数 -->
        <template #in_roll="{ row }">
          <vxe-input v-model="row.in_roll" type="float" />
        </template>
        <!-- 结算单位 -->
        <template #unit_name="{ row }">
          <SelectComponents v-model="row.unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice(row)" />
        </template>
        <!-- 应付单价 -->
        <template #unit_price="{ row }">
          <vxe-input v-model="row.unit_price" type="float" @change="computedTotalPrice(row)" />
          <!--          <span v-show="!isMainUnitFormat(row)">{{ row.unit_price }}</span> -->
        </template>
        <!--        应付数量 -->
        <template #receive_weight="{ row }">
          <vxe-number-input v-model="row.receive_weight" type="float" @blur="handleBlur(row)" />
        </template>
        <template #pt_weight_and_weight_error="{ row }">
          <vxe-number-input v-model="row.pt_weight_and_weight_error" type="float" @blur="handleBlur(row)" />
        </template>
        <template #should_pay_weight="{ row }">
          {{ row.should_pay_weight }}
        </template>
        <!-- 辅助数量 -->
        <template #in_length="{ row }">
          <vxe-input
            v-model="row.in_length"
            type="float"
            @change="computedTotalPrice(row)"
          />
        </template>
        <!-- 辅助数量单价 -->
        <template #length_unit_price="{ row }">
          <vxe-input v-show="!isMainUnitFormat(row)" v-model="row.length_unit_price" type="float" @change="computedTotalPrice(row)" />
          <span v-show="isMainUnitFormat(row)">{{ row.length_unit_price }}</span>
        </template>
        <!-- 其他金额 -->
        <template #other_price="{ row }">
          <vxe-input
            v-model="row.other_price"
            type="float"
            @change="computedTotalPrice(row)"
          />
        </template>
        <!-- 应付金额 -->
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input
            v-model="row.remark"
            maxlength="200"
            type="text"
          />
        </template>
        <!-- 细码 -->
        <template #xima="{ row, rowIndex }">
          <!-- 进仓数量 -->
          <ElButton
            v-if="row.in_roll !== ''"
            type="text"
            @click="showFineSizeDialog(row, rowIndex)"
          >
            录入
            <span v-if="isFinishEnter(row)" style="color: #ccc">(已录入)</span>
            <span v-else style="color: red">({{ row.in_roll - getSFRoll(row) }}条未录)</span>
          </ElButton>
          <span v-else style="color: #ccc">请先输入进仓匹数</span>
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <ElButton
            type="text"
            @click="finishProductionOptions.handleRowDel(row)"
          >
            删除
          </ElButton>
        </template>
      </Table>
    </div>
    <div v-if="finishProductionOptions.datalist.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请选择仓库
      </div>
    </div>
  </FildCard>
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  />
  <AccordingLibAdd
    ref="AccordingLibAddRef"
    @handle-sure="finishProductionOptions.handleSure"
  />
  <FinishProductionAdd
    ref="FinishProductionAddRef"
    :query="{
      sale_system_id: state.formInline.sale_system_id,
      supplier_id: state.formInline.biz_unit_id,
    }"
    @handle-sure="finishProductionOptions.handleSureFinishProduciton"
  />
  <!--  录入细码 -->
  <FineSizeAdd
    ref="FineSizeAddRef"
    @handle-sure="finishProductionOptions.handleSureFineSize"
  />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
