<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Delete } from '@element-plus/icons-vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { formatDate, formatPriceDiv, sumNum } from '@/common/format'
import { debounce, deepClone, deleteToast, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import { getActuallyPayOrderListForOther, updateActuallyPayOrderStatusPassForOther, updateActuallyPayOrderStatusWaitForOther } from '@/api/otherReceipts'
import { EmployeeType } from '@/common/enum'

const state = reactive({
  tableData: [],
  filterData: {
    order_no: '',
    sale_system_id: '',
    sale_order_no: '',
    src_order_no: '',
    devierDate: '',
    auditor_id: '',
    updater_id: '',
    create_time: '',
    audit_time: '',
    edit_time: '',
    order_status: '',
    customer_id: '',
    voucher_number: '',
    seller_id: '',
  },
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getActuallyPayOrderListForOther()

// 获取数据
const getData = debounce(() => {
  const obj = deepClone(state.filterData)
  if (state.filterData.order_status.length)
    obj.order_status = obj.order_status.join(',')

  const query: any = {
    start_time: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[0]) : '',
    end_time: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[1]) : '',
    ...state.filterData,
    order_status: obj.order_status,
  }
  delete query.devierDate
  ApiCustomerList(getFilterData(query))
}, 400)
onMounted(() => {
  getData()
})

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  showSort: false,
  height: '100%',
  fieldApiKey: 'OtherReceipts',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['collect_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'collect_price') as any)}`

      return null
    }),
  ]
}

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'receive_collect_date',
    title: '收入日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'collect_price',
    title: '合计金额',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '单据备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'seller_name',
    title: '经办人',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'auditor_name',
    title: '审核人',
    width: 100,
    sortable: true,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    minWidth: 140,
    isDate: true,
  },
  {
    field: 'update_user_name',
    title: '修改人',
    width: 100,
    sortable: true,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '最后修改时间',
    isDate: true,
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const router = useRouter()

function handAdd() {
  router.push({
    name: 'OtherReceiptsAdd',
  })
}

function handDetail(row: any) {
  router.push({
    name: 'OtherReceiptsDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'OtherReceiptsEdit',
    query: { id: row.id },
  })
}

const { fetchData: auditFetch, success: auditSuccess, msg: auditMsg } = updateActuallyPayOrderStatusPassForOther()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = updateActuallyPayOrderStatusWaitForOther()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单号">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收入日期" width="330">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="经办人">
          <template #content>
            <SelectComponents v-model="state.filterData.seller_id" :query="{ duty: EmployeeType.salesman }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态">
          <template #content>
            <SelectComponents v-model="state.filterData.order_status" multiple api="GetAuditStatusEnum" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button v-has="'OtherReceipts_wait'" v-btnAntiShake="handAdd" type="primary">
          新建
        </el-button>
      </template>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handDetail(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'OtherReceiptsDetail'" type="primary" :underline="false" @click="handDetail(row)">
              查看
            </el-link>
            <el-link v-if="row.audit_status === 1 || row.audit_status === 3" v-has="'OtherReceiptsEdit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-if="row.audit_status === 1" v-has="'OtherReceipts_pass'" type="primary" :underline="false" @click="handAudit(row)">
              审核
            </el-link>
            <el-link v-if="row.audit_status === 2" v-has="'OtherReceipts_wait'" type="primary" :underline="false" @click="handApproved(row)">
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
