/* eslint-disable */
export const toUpperCase = (str: string) => {
  if (str[0]) {
    return str.replace(str[0], str[0].toUpperCase())
  } else {
    return ''
  }
}

export const toLowerCase = (str: string) => {
  if (str[0]) {
    return str.replace(str[0], str[0].toLowerCase())
  } else {
    return ''
  }
}

// 驼峰转换下划线
export const toSQLLine = (str: string) => {
  if (str === 'ID') return 'ID'
  let res = str.replace(/([A-Z])/g, '_$1').toLowerCase()
  return res[0] === '_' ? res.slice(1) : res
}

// 下划线转换驼峰
export const toHump = (name: string) => {
  return name.replace(/\_(\w)/g, function (all, letter) {
    return letter.toUpperCase()
  })
}

/**
 * 移除字符串末尾指定的后缀（如果存在）
 * @param {string} str 原始字符串
 * @param {string} suffix 要移除的后缀
 * @returns {string} 处理后的字符串
 */
export const removeSuffix = (str: string, suffix: string): string => {
  if (str.endsWith(suffix)) {
    return str.slice(0, -suffix.length)
  }
  return str
}
