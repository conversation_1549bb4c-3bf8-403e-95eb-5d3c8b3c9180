<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { AddPrintTemplate, UpdatePrintTemplate } from '@/api/print'
import { getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { getDataHandler } from '@/components/PrintBtn/formatData'

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  type: 'add',
})
const emit = defineEmits(['update:modelValue', 'success'])
const router = useRouter()
export interface Row {
  parent_id: number
  parent_name: string
  name: string
  avatar_url: string
  resource_id: number
  sort: number
  is_hide: boolean
  id: number
}
export interface Props {
  modelValue: boolean
  row: any
}
const state = reactive({
  form: {
    order_name: '',
    front_handler: 0,
    front_handler_name: '',
    type: '',
    status: 1,
    id: 0,
    data_type: null,
  },
  fromRules: {
    order_name: [
      { required: true, message: '单据名称不能为空', trigger: 'change' },
    ],
    type: [{ required: true, message: '打印类型不能为空', trigger: 'change' }],
    status: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  },
  title: '添加打印信息',
})

const dataHandlerList = computed(() => {
  return getDataHandler(state.form.type) || []
})

const ruleFormRef = ref()
const showModal = ref<boolean>(false)
watch(
  () => [props.modelValue, props.row],
  () => {
    showModal.value = props.modelValue
    state.title = '添加打印信息'
    if (props.row) {
      state.title = '编辑打印信息'
      state.form.order_name = props.row.order_name
      state.form.front_handler = props.row.front_handler
      state.form.front_handler_name = props.row.front_handler_name
      state.form.type = props.row.type
      state.form.status = props.row.status
      state.form.id = props.row.id
      state.form.data_type = props.row.data_type || null
    }
    if (!showModal.value) {
      state.form.order_name = ''
      state.form.front_handler = 0
      state.form.front_handler_name = ''
      state.form.type = ''
      state.form.status = 1
      state.form.id = 0
      state.form.data_type = null
    }
  },
)

const { fetchData, success, msg } = AddPrintTemplate()
async function addSubmit() {
  await fetchData(
    getFilterData(
      {
        ...state.form,
        front_handler_name: dataHandlerList.value?.find(
          item => item.id === state.form.front_handler,
        ).name,
      },
      ['id'],
    ),
  )
  if (success.value) {
    ElMessage.success('添加成功')
    emit('success')
  }
  else {
    ElMessage.error(msg.value)
  }
}

const {
  fetchData: fetchDataUpdate,
  success: successUpdate,
  msg: msgUpdate,
} = UpdatePrintTemplate()
async function editSubmit() {
  await fetchDataUpdate(
    getFilterData({
      ...state.form,
      front_handler_name: dataHandlerList.value?.find(
        item => item.id === state.form.front_handler,
      ).name,
    }),
  )
  if (successUpdate.value) {
    ElMessage.success('修改成功')
    emit('success')
  }
  else {
    ElMessage.error(msgUpdate.value)
  }
}

function handCancel() {
  emit('update:modelValue', false)
}
function handleSure() {
  ruleFormRef.value?.validate(async (valid: any) => {
    if (valid) {
      if (!state.form.id)
        addSubmit()
      else
        editSubmit()

      emit('update:modelValue', false)
    }
  })
}

function openSetting() {
  router.push({
    name: 'Print',
    params: { id: state.form.id },
  })
}

function onClose() {
  emit('update:modelValue', false)
}
</script>

<template>
  <vxe-modal
    v-model="showModal"
    show-footer
    :title="state.title"
    width="700"
    height="auto"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    @close="onClose"
  >
    <el-form
      ref="ruleFormRef"
      size="default"
      :model="state.form"
      label-width="140px"
      label-position="left"
      :rules="state.fromRules"
    >
      <el-form-item label="单据名称" prop="order_name">
        <el-input
          v-model="state.form.order_name"
          clearable
          placeholder="单据名称"
        />
      </el-form-item>
      <el-form-item label="打印类型" prop="type">
        <SelectComponents
          v-model="state.form.type"
          style="width: 100%"
          api="PrintTemplateType"
          label-field="name"
          value-field="id"
          clearable
        />
      </el-form-item>
      <el-form-item label="数据类型" prop="type">
        <SelectComponents
          v-model="state.form.data_type"
          style="width: 100%"
          api="GetDataType"
          label-field="name"
          value-field="id"
          clearable
        />
      </el-form-item>
      <el-form-item label="数据处理" prop="type">
        <SelectComponents
          v-model="state.form.front_handler"
          style="width: 100%"
          :options="dataHandlerList"
          label-field="name"
          value-field="id"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <SelectComponents
          v-model="state.form.status"
          style="width: 100%"
          api="StatusListApi"
          label-field="name"
          value-field="id"
          clearable
        />
      </el-form-item>
      <el-form-item label="打印模板">
        <el-button type="primary" @click="openSetting">
          设置模板
        </el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
