<script setup lang="ts" name="RawMaterialStockAdjustment">
import { Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import {
  AdjustOrderCancel,
  AdjustOrderDetail,
  AdjustOrderList,
  AdjustOrderListDownLoad,
  AdjustOrderPass,
} from '@/api/rawMaterialStockAdjustment'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import {
  debounce,
  exportList,
  getFilterData,
  orderStatusConfirmBox,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'

const filterData = reactive({
  order_no: '',
  sale_system_id: '',
  adjust_unit_id: '',
  adjust_date: '',
  status: '',
  adjust_start_date: '',
  adjust_end_date: '',
})

onMounted(() => {
  getData()
})

const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = AdjustOrderList()
async function getData() {
  const status = ((filterData.status as unknown as []) || []).join(',')
  await fetchDataList(
    getFilterData({ ...filterData, status }, ['adjust_date']),
  )
  if (dataList.value?.list)
    showDetail(dataList.value.list[0])
}
onActivated(getData)

const selectRow = ref()
const detailShow = ref()
const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = AdjustOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: selectRow.value.id })
}

watch(
  () => filterData.adjust_date,
  (value: any) => {
    filterData.adjust_start_date = formatDate(value?.[0]) || ''
    filterData.adjust_end_date = formatDate(value?.[1]) || ''
  },
)

watch(
  () => filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

function showDetail(row: any) {
  selectRow.value = row
  detailShow.value = true
  getDataDetail()
}

function handDetail(row: any) {
  router.push({
    name: 'RawMaterialStockAdjustmentDetail',
    params: { id: row?.id },
  })
}
async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({ id, message: msg, api: AdjustOrderPass })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({ id, message: msg, api: AdjustOrderCancel })
  getData()
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialStockAdjustmentEdit',
    params: { id: row?.id },
  })
}

function handAdd() {
  router.push({
    name: 'RawMaterialStockAdjustmentAdd',
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '订单编号',
    soltName: 'order_no',
    width: '8%',
  },
  {
    sortable: true,
    field: 'adjust_unit_name',
    title: '调整单位名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'adjust_date',
    title: '调整时间',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'warehouse_manager_name',
    title: '仓管员',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    showOrder_status: true,
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const cColumnList = ref([
  {
    title: '',
    field: 'A',
    childrenList: [
      {
        sortable: true,
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        minWidth: 150,
      },
      {
        sortable: true,
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        minWidth: 100,
      },
    ],
  },
  {
    field: 'B',
    title: '调整前',
    childrenList: [
      {
        sortable: true,
        field: 'customer_name_stock',
        title: '所属客户',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'brand_stock',
        title: '原料品牌',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'craft_stock',
        title: '原料工艺',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'supplier_name_stock',
        title: '供应商',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'color_scheme_stock',
        title: '原料色系',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'batch_num_stock',
        title: '原料批号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'level_name_stock',
        title: '原料等级',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'carton_num_stock',
        title: '装箱单号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'fapiao_num_stock',
        title: '发票号',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'production_date_stock',
        title: '生产日期',
        minWidth: 100,
        isDate: true,
      },
      {
        sortable: true,
        field: 'spinning_type_stock',
        title: '纺纱类型',
        minWidth: 100,
      },

      {
        sortable: true,
        field: 'cotton_origin_stock',
        title: '棉花产地',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'yarn_origin_stock',
        title: '棉纱产地',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'whole_piece_count_stock',
        title: '整件库存件数',
        minWidth: '5%',
      },
      // {
      //   field: 'whole_piece_weight_stock',
      //   title: '整件数量总计(kg)',
      //   width: '5%',
      // },
      // {
      //   field: 'whole_piece_weight_stock',
      //   title: '整件件重',
      //   width: '5%',
      //   isWeight: true,
      // },
      {
        sortable: true,
        field: 'bulk_piece_count_stock',
        title: '散件库存件数',
        minWidth: '5%',
      },
      // {
      //   field: 'bulk_weight_stock',
      //   title: '散件数量总计(kg)',
      //   width: '5%',
      //   isWeight: true,
      // },
      {
        sortable: true,
        field: 'total_weight_stock',
        title: '数量总计(kg)',
        minWidth: '5%',
        isWeight: true,
      },
      {
        sortable: true,
        field: 'unit_price_stock',
        title: '单价',
        minWidth: '5%',
        isUnitPrice: true,
      },
      {
        sortable: true,
        field: 'total_price_stock',
        title: '金额',
        minWidth: '5%',
        isPrice: true,
      },
    ],
  },
  {
    field: 'C',
    title: '调整后',
    childrenList: [
      {
        sortable: true,
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
        soltName: 'customer_name',
      },
      {
        sortable: true,
        field: 'brand',
        title: '原料品牌',
        minWidth: 100,
        soltName: 'brand',
      },
      {
        sortable: true,
        field: 'craft',
        title: '原料工艺',
        minWidth: 100,
        soltName: 'craft',
      },
      {
        sortable: true,
        field: 'supplier_name',
        title: '供应商',
        minWidth: 100,
        soltName: 'supplier_name',
      },
      {
        sortable: true,
        field: 'color_scheme',
        title: '原料色系',
        minWidth: 100,
        soltName: 'color_scheme',
      },
      {
        sortable: true,
        field: 'batch_num',
        title: '原料批号',
        minWidth: 100,
        soltName: 'batch_num',
      },
      {
        sortable: true,
        field: 'level_name',
        title: '原料等级',
        minWidth: 100,
        soltName: 'level_name',
      },
      {
        sortable: true,
        field: 'carton_num',
        title: '装箱单号',
        minWidth: 100,
        soltName: 'carton_num',
      },
      {
        sortable: true,
        field: 'fapiao_num',
        title: '发票号',
        minWidth: 100,
        soltName: 'fapiao_num',
      },
      {
        sortable: true,
        field: 'production_date',
        title: '生产日期',
        minWidth: 100,
        isDate: true,
        soltName: 'production_date',
      },
      {
        sortable: true,
        field: 'spinning_type',
        title: '纺纱类型',
        minWidth: 100,
        soltName: 'spinning_type',
      },

      {
        sortable: true,
        field: 'cotton_origin',
        title: '棉花产地',
        minWidth: 100,
        soltName: 'cotton_origin',
      },
      {
        sortable: true,
        field: 'yarn_origin',
        title: '棉纱产地',
        minWidth: 100,
        soltName: 'yarn_origin',
      },
      {
        sortable: true,
        field: 'whole_piece_count',
        title: '整件调整件数',
        minWidth: '5%',
        soltName: 'whole_piece_count',
      },
      // {
      //   field: 'whole_piece_weight',
      //   title: '整件数量总计(kg)',
      //   width: '5%',
      //   isWeight: true,
      //   soltName: 'whole_piece_weight',
      // },
      {
        sortable: true,
        field: 'bulk_piece_count',
        title: '散件调整件数',
        minWidth: '5%',
        soltName: 'bulk_piece_count',
      },
      // {
      //   field: 'bulk_weight',
      //   title: '散件数量总计(kg)',
      //   width: '5%',
      //   isWeight: true,
      //   soltName: 'bulk_weight',
      // },
      {
        sortable: true,
        field: 'total_weight',
        title: '数量总计(kg)',
        minWidth: '5%',
        isWeight: true,
        soltName: 'total_weight',
      },
      {
        sortable: true,
        field: 'unit_price',
        title: '单价',
        minWidth: '5%',
        isUnitPrice: true,
        soltName: 'unit_price',
      },
      {
        sortable: true,
        field: 'total_price',
        title: '金额',
        minWidth: '5%',
        isPrice: true,
        soltName: 'total_price',
      },
    ],
  },
])

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '总计'

      if (
        [
          'whole_piece_count',
          'whole_piece_count_stock',
          'bulk_piece_count',
          'bulk_piece_count_stock',
        ].includes(column.property)
      )
        return `${sumNum(data, column.property)}`

      if (
        [
          'whole_piece_weight',
          'bulk_weight',
          'total_weight_stock',
          'whole_piece_weight_stock',
          'total_weight',
          'bulk_weight_stock',
          'total_weight_stock',
        ].includes(column.property)
      ) {
        return `${formatWeightDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      if (
        [
          'other_price',
          'total_price',
          'other_price_stock',
          'total_price_stock',
        ].includes(column.property)
      ) {
        return `¥${formatPriceDiv(
          sumNum(data, column.property) as unknown as number,
        )}`
      }
      return null
    }),
  ]
}

const loadingExport = ref(false)
async function handleExport() {
  loadingExport.value = true
  await exportList({
    api: AdjustOrderListDownLoad,
    title: '原料采购退货单',
    filterData,
  })
  loadingExport.value = false
}

const tableConfig = ref({
  fieldApiKey: 'RawMaterialStockAdjustment_A',
  showSlotNums: true,
  loading: loadingList.value,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '9%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
})

const CTableConfig = ref({
  fieldApiKey: 'RawMaterialStockAdjustment_B',
  height: '100%',
  operateWidth: '150',
  showSort: false,
  showSpanHeader: true,
  footerMethod,
  loading: loadingDetail.value,
  rowColor: {
    whole_piece_count_stock: 'blue',
    whole_piece_weight_stock: 'blue',
    bulk_weight_stock: 'yellow',
    bulk_piece_count_stock: 'yellow',
    whole_piece_count: 'blue',
    whole_piece_weight: 'blue',
    bulk_weight: 'yellow',
    bulk_piece_count: 'yellow',
  },
})
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              placeholder="单据编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.sale_system_id"
              api="GetSaleSystemDropdownListApi"
              label-field="name"
              value-field="id"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="调整单位:">
          <template #content>
            <SelectComponents
              v-model="filterData.adjust_unit_id"
              style="width: 200px"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
              api="GetBusinessUnitListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="调整日期:" width="330">
          <template #content>
            <SelectDate v-model="filterData.adjust_date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.status"
              :multiple="true"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="`RawMaterialStockAdjustmentAdd`"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="`RawMaterialStockAdjustment_export`"
          :loading="loadingExport"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList.list"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="`RawMaterialStockAdjustmentDetail`"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="`RawMaterialStockAdjustmentEdit`"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="`RawMaterialStockAdjustment_pass`"
              type="primary"
              :underline="false"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="`RawMaterialStockAdjustment_cancel`"
              type="primary"
              :underline="false"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="CTableConfig"
        :table-list="dataDetail.items"
        :column-list="cColumnList"
      >
        <template #customer_name="{ row }">
          <span
            :class="{ my_input: row.customer_name !== row.customer_name_stock }"
          >{{ row.customer_name }}</span>
        </template>
        <template #brand="{ row }">
          <span :class="{ my_input: row.brand !== row.brand_stock }">{{
            row.brand
          }}</span>
        </template>
        <template #craft="{ row }">
          <span :class="{ my_input: row.craft !== row.craft_stock }">{{
            row.craft
          }}</span>
        </template>
        <template #supplier_name="{ row }">
          <span
            :class="{ my_input: row.supplier_name !== row.supplier_name_stock }"
          >{{ row.supplier_name }}</span>
        </template>
        <template #color_scheme="{ row }">
          <span
            :class="{ my_input: row.color_scheme !== row.color_scheme_stock }"
          >{{ row.color_scheme }}</span>
        </template>
        <template #batch_num="{ row }">
          <span :class="{ my_input: row.batch_num !== row.batch_num_stock }">{{
            row.batch_num
          }}</span>
        </template>
        <template #level="{ row }">
          <span :class="{ my_input: row.level !== row.level_stock }">{{
            row.level
          }}</span>
        </template>
        <template #carton_num="{ row }">
          <span :class="{ my_input: row.carton_num !== row.carton_num_stock }">{{
            row.carton_num
          }}</span>
        </template>
        <template #fapiao_num="{ row }">
          <span :class="{ my_input: row.fapiao_num !== row.fapiao_num_stock }">{{
            row.fapiao_num
          }}</span>
        </template>
        <template #production_date="{ row }">
          <span
            :class="{
              my_input: row.production_date !== row.production_date_stock,
            }"
          >{{ row.production_date }}</span>
        </template>
        <template #spinning_type="{ row }">
          <span
            :class="{ my_input: row.spinning_type !== row.spinning_type_stock }"
          >{{ row.spinning_type }}</span>
        </template>
        <template #cotton_origin="{ row }">
          <span
            :class="{ my_input: row.cotton_origin !== row.cotton_origin_stock }"
          >{{ row.cotton_origin }}</span>
        </template>
        <template #yarn_origin="{ row }">
          <span :class="{ my_input: row.yarn_origin !== row.yarn_origin_stock }">{{
            row.yarn_origin
          }}</span>
        </template>
        <template #whole_piece_count="{ row }">
          <span
            :class="{
              my_input: row.whole_piece_count !== row.whole_piece_count_stock,
            }"
          >{{ row.whole_piece_count }}</span>
        </template>
        <template #whole_piece_weight="{ row }">
          <span
            :class="{
              my_input: row.whole_piece_weight !== row.whole_piece_weight_stock,
            }"
          >{{ formatWeightDiv(row.whole_piece_weight) }}</span>
        </template>
        <template #bulk_piece_count="{ row }">
          <span
            :class="{
              my_input: row.bulk_piece_count !== row.bulk_piece_count_stock,
            }"
          >{{ row.bulk_piece_count }}</span>
        </template>
        <template #bulk_weight="{ row }">
          <span :class="{ my_input: row.bulk_weight !== row.bulk_weight_stock }">{{
            formatWeightDiv(row.bulk_weight)
          }}</span>
        </template>
        <template #total_weight="{ row }">
          <span
            :class="{ my_input: row.total_weight !== row.total_weight_stock }"
          >{{ formatWeightDiv(row.total_weight) }}</span>
        </template>
        <template #unit_price="{ row }">
          <span :class="{ my_input: row.unit_price !== row.unit_price_stock }">{{
            formatUnitPriceDiv(row.unit_price)
          }}</span>
        </template>
        <template #total_price="{ row }">
          <span :class="{ my_input: row.total_price !== row.total_price_stock }">{{
            formatPriceDiv(row.total_price)
          }}</span>
        </template>
      </Table>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.my_input {
  color: red;
}
</style>
