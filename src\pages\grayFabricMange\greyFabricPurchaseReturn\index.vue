<script setup lang="ts" name="GreyFabricPurchaseReturn">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import {
  UpdateGfmPurchaseReturnStatusPass,
  getGfmPurchaseReturn,
  getGfmPurchaseReturnList,
  getGfmPurchaseReturnListExport,
  updateGfmPurchaseReturnStatus,
} from '@/api/greyFabricPurchaseReturn'
import { formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import type { TableColumn } from '@/components/Table/type'

const state = reactive({
  tableData: [],
  filterData: {
    return_entity_id: '',
    supplier_id: '',
    audit_status: '',
    document_code: '',
  },
  multipleSelection: [],
  information: false,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getGfmPurchaseReturnList()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.audit_status.length)
    obj.audit_status = obj.audit_status.join(',')

  await ApiCustomerList(getFilterData(obj))
  if (data.value?.list)
    getInfomation(data.value.list[0])
}, 400)

onMounted(() => {
  getData()
})
onActivated(getData)

const tableConfig = ref({
  fieldApiKey: 'GreyFabricPurchaseReturn_A',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const columnList_fabic_config = ref({
  fieldApiKey: 'GreyFabricPurchaseReturn_B',
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  height: '100%',
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['total_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'total_weight') as any)}`

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['total_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'total_price') as any)}`

      return null
    }),
  ]
}

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'code',
    title: '单据编号',
    fixed: 'left',
    width: '9%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '接收单位',
    // fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'return_entity_name',
    title: '货源单位',
    // fixed: 'left',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'total_roll',
    title: '匹数',
    // fixed: 'left',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'total_weight',
    title: '总数量',
    // fixed: 'left',
    minWidth: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '总金额',
    // fixed: 'left',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'remark',
    title: '单据备注',
    // fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'return_time',
    title: '退货日期',
    minWidth: 120,
    is_date: true,
  },
  {
    field: 'create_time',
    title: '创建时间',
    sortable: true,
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    sortable: true,
    minWidth: 150,
    isDate: true,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '单据状态',
    fixed: 'right',
    width: '5%',
    soltName: 'audit_status',
    showOrder_status: true,
  },
])

const columnList_fabic = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'custom_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'needle_size',
    title: '针寸数',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'yarn_batch',
    title: '纱批',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'machine_number',
    title: '机台号',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_purchase_code',
    title: '坯布采购订单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'roll',
    title: '匹数',
    minWidth: '5%',
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'xima',
    title: '细码',
    minWidth: '5%',
    soltName: 'xima',
    fixed: 'right',
  },
  {
    sortable: true,
    field: 'total_weight',
    title: '总数量',
    minWidth: '5%',
    fixed: 'right',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'single_price',
    title: '单价',
    minWidth: '5%',
    fixed: 'right',
    isUnitPrice: true,
  },
  {
    sortable: true,
    field: 'other_price',
    title: '其他金额',
    minWidth: '5%',
    fixed: 'right',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '总金额',
    minWidth: '5%',
    fixed: 'right',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 100,
    fixed: 'right',
  },
])

// const handShowSort = () => {
//   tableConfig.value.showSort = true
// }

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '坯布采购退货单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = getGfmPurchaseReturnListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'GreyFabricPurchaseReturnAdd',
  })
}

const { fetchData: getFetchDetail, data: fabricList } = getGfmPurchaseReturn()

// 获取坯布信息
async function getInfomation(row: any) {
  await getFetchDetail({ id: row.id })

  state.information = true
}

// 查看细码
const AddXimaDialogRef = ref()

function handSeeXima(row: any) {
  AddXimaDialogRef.value.state.showModal = true
  const listData = deepClone(row?.item_fc_data || [])
  listData?.map((item: any) => {
    item.roll = Number(formatPriceDiv(item?.roll))
    item.weight = Number(formatWeightDiv(item?.weight))
    return item
  })
  AddXimaDialogRef.value.state.tableData = listData || []
  AddXimaDialogRef.value.state.canEnter = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.horsepower = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.isDisabled = true
}

function handEdit(row: any) {
  router.push({
    name: 'GreyFabricPurchaseReturnEdit',
    query: { id: row.id },
  })
}

function handDetail(row: any) {
  router.push({
    name: 'GreyFabricPurchaseReturnDetail',
    query: { id: row.id },
  })
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = UpdateGfmPurchaseReturnStatusPass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateGfmPurchaseReturnStatus()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="state.filterData.document_code"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="货源单位:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.return_entity_id"
              :query="{ name: componentRemoteSearch.unit_name }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="接收单位:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.supplier_id"
              :query="{ name: componentRemoteSearch.name }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '供应商编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '供应商编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }:any) {
                      if (cellValue === '') {
                        new Error('供应商名称');
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          <!-- <SelectComponents api="BusinessUnitSupplierEnumlist" label-field="name" value-field="id" v-model="state.filterData.supplier_id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.audit_status"
              multiple
              api="getAuditStatusEnums"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button
          v-has="'GreyFabricPurchaseReturn_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="'GreyFabricPurchaseReturn_export'"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="getInfomation(row)">
            {{ row.document_code }}
          </el-link>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'GreyFabricPurchaseReturn_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'GreyFabricPurchaseReturn_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'GreyFabricPurchaseReturn_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'GreyFabricPurchaseReturn_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="columnList_fabic_config"
        :table-list="fabricList?.item_data"
        :column-list="columnList_fabic"
      >
        <template #xima="{ row }">
          <el-button type="primary" text link @click="handSeeXima(row)">
            查看
          </el-button>
        </template>
      </Table>
    </FildCard>
  </div>
  <AddXimaDialog ref="AddXimaDialogRef" />
</template>

<style lang="scss" scoped>
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
