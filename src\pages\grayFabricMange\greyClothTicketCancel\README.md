# 布飞取消页面

## 功能概述

布飞取消页面用于管理布飞的取消和恢复操作，支持单个取消、批量取消以及已取消布飞的恢复功能。

## 页面结构

### 1. 主页面 (`index.vue`)
- **搜索功能**：支持按布飞条码、机台号、织工姓名、生产通知单号、状态等条件搜索
- **列表展示**：显示布飞的基本信息，包括条码、机台号、织工、重量、状态等
- **批量操作**：支持批量取消选中的布飞
- **单个操作**：支持单个布飞的取消和恢复
- **导出功能**：支持导出搜索结果到Excel

### 2. 详情页面 (`detail.vue`)
- **基本信息**：显示布飞的详细信息
- **称重信息**：显示称重相关数据
- **验布信息**：显示验布相关数据
- **操作记录**：显示历史操作记录时间线
- **操作按钮**：根据状态显示取消或恢复按钮

### 3. 取消原因对话框 (`components/CancelReasonDialog.vue`)
- **原因选择**：从枚举中选择取消原因
- **备注输入**：可选的备注信息输入
- **表单验证**：确保必填项完整

## API接口

### 接口列表
- `getFabricFlyList` - 获取布飞列表
- `getFabricFlyDetail` - 获取布飞详情
- `fabricFlyCancel` - 单个布飞取消
- `fabricFlyBatchCancel` - 批量布飞取消
- `fabricFlyRestore` - 布飞恢复
- `getFabricFlyCancelReasonEnum` - 获取取消原因枚举

### 接口路径
```
GET  /admin/v1/grey_fabric_manage/fabricFly/getFabricFlyList      # 获取列表
GET  /admin/v1/grey_fabric_manage/fabricFly/getFabricFlyDetail   # 获取详情
PUT  /admin/v1/grey_fabric_manage/fabricFly/cancel               # 取消
PUT  /admin/v1/grey_fabric_manage/fabricFly/batchCancel          # 批量取消
PUT  /admin/v1/grey_fabric_manage/fabricFly/restore              # 恢复
GET  /admin/v1/enum/fabricFlyCancelReason                        # 取消原因枚举
```

## 路由配置

```typescript
{
  path: 'fabricFlyCancel',
  name: 'FabricFlyCancel',
  title: '布飞取消',
  meta: {
    navName: ['坯布管理', '布飞取消'],
    title: '布飞取消',
  },
  component: () => import('@/pages/grayFabricMange/fabricFlyCancel/index.vue'),
},
{
  path: 'fabricFlyCancelDetail',
  name: 'FabricFlyCancelDetail',
  title: '布飞取消详情',
  hideType: true,
  meta: {
    navName: ['坯布管理', '布飞取消详情'],
    title: '布飞取消详情',
  },
  component: () => import('@/pages/grayFabricMange/fabricFlyCancel/detail.vue'),
}
```

## 数据状态

### 布飞状态
- `1` - 正常
- `2` - 已取消

### 取消原因（示例）
- `1` - 质量问题
- `2` - 设备故障
- `3` - 原料问题
- `4` - 其他原因

## 使用说明

### 取消布飞
1. 在列表页面选择要取消的布飞
2. 点击"取消"按钮或使用批量取消
3. 在弹出的对话框中选择取消原因
4. 可选填写备注信息
5. 确认取消操作

### 恢复布飞
1. 在列表页面找到已取消的布飞（状态为"已取消"）
2. 点击"恢复"按钮
3. 确认恢复操作

### 查看详情
1. 点击列表中的"查看"链接
2. 在详情页面查看完整信息
3. 可在详情页面进行取消或恢复操作

## 技术特点

- 使用Vue 3 Composition API
- TypeScript类型安全
- Element Plus UI组件
- 响应式设计
- 表单验证
- 错误处理
- 加载状态管理

## 文件结构

```
fabricFlyCancel/
├── index.vue                    # 主页面
├── detail.vue                   # 详情页面
├── components/
│   └── CancelReasonDialog.vue   # 取消原因对话框
└── README.md                    # 说明文档
```

## 依赖组件

- `FildCard` - 页面卡片容器
- `Table` - 数据表格
- `BottonExcel` - Excel导出按钮
- Element Plus 组件库

## 注意事项

1. 取消操作不可逆，请谨慎操作
2. 批量操作时会应用相同的取消原因
3. 恢复操作会清除取消原因和备注
4. 导出功能会导出当前搜索条件下的所有数据
