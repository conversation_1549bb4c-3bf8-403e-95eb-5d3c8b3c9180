import { useRequest } from '@/use/useRequest'

// 获取列表
export const getActuallyCollectOrderList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/getActuallyCollectOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 作废
export const updateActuallyCollectOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/updateActuallyCollectOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateActuallyCollectOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/updateActuallyCollectOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateActuallyCollectOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/updateActuallyCollectOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateActuallyCollectOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/updateActuallyCollectOrderStatusWait',
    method: 'put',
  })
}

// 获取详情
export const getActuallyCollectOrder = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/getActuallyCollectOrder',
    method: 'get',
  })
}

// 编辑
export const updateActuallyCollectOrder = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/updateActuallyCollectOrder',
    method: 'put',
  })
}

// 新建
export const addActuallyCollectOrder = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/actuallyCollectOrder/addActuallyCollectOrder',
    method: 'post',
  })
}

// 获取预收单枚举列表
export const getAdvanceCollectOrderListEnum = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/advanceCollectOrder/getAdvanceCollectOrderListEnum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取应收单枚举列表
export const getShouldCollectOrderDropdownList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/getDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 应收单类型枚举
export const CollectType = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/enum/collectType',
    method: 'get',
  })
}

// 收款状态枚举
export const CollectStatus = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/enum/collectStatus',
    method: 'get',
  })
}

// 核销状态枚举
export const GetEnumWriteOffStatusReverseIntMap = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/enum/getEnumWriteOffStatusReverseIntMap',
    method: 'get',
  })
}
// 实收自动核销
export const GetShouldCollectOrderAutoWriteOffList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/getAutoWriteOffList',
    method: 'get',
  })
}
