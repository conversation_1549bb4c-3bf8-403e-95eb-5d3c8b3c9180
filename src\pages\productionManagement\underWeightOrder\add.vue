<script setup lang="ts" name="UnderWeightOrderAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import MasterDialogAdd from './components/MasterDialogAdd.vue'
import { addProductionShortageOrder } from '@/api/underWeightOrder'
import { formatDate, formatPriceMul, formatTwoDecimalsMul, formatWeightMul } from '@/common/format'
import { deleteToast, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { BusinessUnitIdEnum } from '@/common/enum'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'

const routerList = useRouterList()
const MasterDialogAddRef = ref()
const formRef = ref()
const state = reactive<any>({
  formInline: {
    sale_system_id: '',
    receive_unit_id: '',
    voucher_number: '',
    shortage_date: new Date(),
    remark: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    receive_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    shortage_date: [{ required: true, message: '请选择欠重日期', trigger: 'change' }],
  },
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

let uuid = 0
const underWeightOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    showCheckBox: true,
    footerMethod: (val: any) => underWeightOptions.FooterMethod(val),
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '汇总'

        if (['piece_count'].includes(column.field))
          return sumNum(data, 'piece_count', '')

        if (['piece_weight'].includes(column.field))
          return sumNum(data, 'piece_weight', '')

        if (['gross_weight'].includes(column.field))
          return sumNum(data, 'gross_weight', '')

        if (['tare_weight'].includes(column.field))
          return sumNum(data, 'tare_weight', '')

        if (['shortage_weight'].includes(column.field))
          return sumNum(data, 'shortage_weight', '')

        if (['net_weight'].includes(column.field))
          return sumNum(data, 'net_weight', '')

        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '')

        if (['total_net_weight'].includes(column.field))
          return sumNum(data, 'total_net_weight', '')

        if (['total_shortage_weight'].includes(column.field))
          return sumNum(data, 'total_shortage_weight', '')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '每件',
      childrenList: [
        {
          field: 'rml_purchase_order_no',
          title: '采购收货单号',
          minWidth: 150,
        },
        {
          field: 'receive_date',
          title: '收货日期',
          minWidth: 100,
          is_date: true,
        },
        {
          field: 'rml_code',
          title: '原料编号',
          minWidth: 100,
        },
        {
          field: 'rml_name',
          title: '原料名称',
          minWidth: 100,
        },
        {
          field: 'supplier_name',
          title: '供方名称',
          minWidth: 100,
        },
        {
          field: 'raw_material_batch_brand',
          title: '品牌',
          minWidth: 100,
        },
        {
          field: 'raw_material_batch_num',
          title: '批号',
          minWidth: 100,
        },
        {
          field: 'color_scheme',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'piece_count',
          soltName: 'piece_count',
          title: '件数',
          minWidth: 100,
          required: true,
        },
        {
          field: 'piece_weight',
          soltName: 'piece_weight',
          title: '件重',
          minWidth: 100,
          required: true,
        },
        {
          field: 'gross_weight',
          soltName: 'gross_weight',
          title: '毛重',
          minWidth: 100,
        },
        {
          field: 'tare_weight',
          soltName: 'tare_weight',
          title: '皮重',
          minWidth: 100,
        },
        {
          field: 'shortage_weight',
          soltName: 'shortage_weight',
          title: '欠重',
          minWidth: 100,
        },

        {
          field: 'net_weight',
          title: '净重',
          minWidth: 100,
        },
        {
          field: 'shortage_weight_rate',
          title: '亏磅率(%)',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      childrenList: [
        {
          field: 'total_weight',
          title: '总数量',
          minWidth: 100,
        },
        {
          field: 'total_net_weight',
          title: '总净重',
          minWidth: 100,
        },
        {
          field: 'total_shortage_weight',
          title: '总欠重',
          minWidth: 100,
        },
        {
          field: 'remark',
          soltName: 'remark',
          title: '备注',
          minWidth: 100,
        },
        {
          field: '',
          soltName: 'operate',
          title: '操作',
          width: 100,
        },
      ],
    },
  ],
  // 确认添加带出数据
  handleSure: (list: any) => {
    // 自动带出字段 件数、件重、采购单号、单号id、原料id
    // 收货日期 原料编号 原料名称 供方名称&id 品牌名称 批号 颜色

    list = list.map((item: any) => {
      return {
        uuid: ++uuid,
        rml_purchase_order_no: item.order_num,
        rml_purchase_order_id: item.id,
        piece_count: item.whole_piece_count,
        piece_weight: item.whole_piece_weight,
        rml_id: item.raw_material_id,
        receive_date: item.receipt_date,
        rml_code: item.raw_material_code,
        rml_name: item.raw_material_name,
        supplier_name: item.supplier_name,
        supplier_id: item.supplier_id,
        raw_material_batch_brand: item.brand,
        raw_material_batch_num: item.batch_num,
        color_scheme: item.color_scheme,
        gross_weight: 0,
        tare_weight: 0,
        shortage_weight: 0,
        net_weight: 0,
        shortage_weight_rate: 0,
        total_weight: 0,
        total_net_weight: 0,
        total_shortage_weight: 0,
      }
    })
    underWeightOptions.datalist = [...underWeightOptions.datalist, ...list]
  },
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该欠重信息')
    if (!res)
      return
    const index = underWeightOptions.datalist.findIndex((item: any) => item.uuid === uuid)
    underWeightOptions.datalist.splice(index, 1)
  },
  //   批量操作
  handEdit: () => handEdit,
})

// 表格选中事件
function handAllSelect({ records }: any) {
  underWeightOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  underWeightOptions.multipleSelection = records
}

// 批量操作
const bulkShow = ref(false)
function handEdit() {
  if (underWeightOptions.multipleSelection.length < 1)
    return ElMessage.error('请选择数据')

  bulkShow.value = true
}
const bulkSetting = reactive<any>({})
const bulkList = reactive<any>([
  {
    field: 'piece_count',
    title: '件数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'piece_weight',
    title: '件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'gross_weight',
    title: '毛重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'tare_weight',
    title: '皮重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'shortage_weight',
    title: '欠重',
    component: 'input',
    type: 'float',
  },

  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  const ids = underWeightOptions.multipleSelection.map((item: any) => item.uuid)
  underWeightOptions.datalist.map((item: any) => {
    if (ids.includes(item.uuid))
      item[row.field] = value[row.field]
  })
  if (row.field)
    ElMessage.success('设置成功')
  handBulkClose()
}
function handBulkClose() {
  bulkShow.value = false
}
// 从采购收货单中添加
function showLibDialog() {
  // MasterDialogAddRef.value.state.showModal = true
  MasterDialogAddRef.value.showModal({
    sale_system_id: state.formInline.sale_system_id,
    receipt_unit_id: state.formInline.receive_unit_id,
  })
}

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = addProductionShortageOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!underWeightOptions.datalist.length)
        return ElMessage.error('请添加欠重信息')

      for (let i = 0; i < underWeightOptions.datalist.length; i++) {
        if (!Number(underWeightOptions.datalist[i].piece_count))
          return ElMessage.error('件数未必填项')

        if (!Number(underWeightOptions.datalist[i].piece_weight))
          return ElMessage.error('件重未必填项')
      }
      // 整理参数
      const query = {
        ...state.formInline,
        shortage_date: formatDate(state.formInline.shortage_date),
        item_data: [],
      }
      query.item_data = underWeightOptions.datalist.map((item: any) => {
        return {
          ...item,
          receive_date: formatDate(item.receive_date),
          piece_count: formatPriceMul(item.piece_count),
          piece_weight: formatWeightMul(Number(item.piece_weight)),
          gross_weight: formatWeightMul(Number(item.gross_weight)),
          tare_weight: formatWeightMul(Number(item.tare_weight)),
          shortage_weight: formatWeightMul(Number(item.shortage_weight)),
          net_weight: formatWeightMul(Number(item.net_weight)),
          shortage_weight_rate: formatTwoDecimalsMul(Number(item.shortage_weight_rate)),
          total_weight: formatWeightMul(Number(item.total_weight)),
          total_net_weight: formatWeightMul(Number(item.total_net_weight)),
          total_shortage_weight: formatWeightMul(Number(item.total_shortage_weight)),
        }
      })
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        routerList.push({
          name: 'UnderWeightOrderDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
watch(
  () => underWeightOptions.datalist,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

function computedShortageWeightRate(row: any) {
  row.shortage_weight_rate = Number((((Number(row.shortage_weight) || 0) / Number(row.piece_weight) || 0) * 100).toFixed(2))
}

function computedNetWeight(row: any) {
  const net_weight = (Number(row.gross_weight) || 0) - (Number(row.tare_weight) || 0)
  row.net_weight = Number(net_weight.toFixed(4))
  row.total_net_weight = Number(((Number(row.piece_count) || 0) * net_weight).toFixed(2))
}
function computedTotalWeight(row: any) {
  row.total_shortage_weight = Number(((Number(row.piece_count) || 0) * (Number(row.shortage_weight) || 0)).toFixed(2))
  row.total_net_weight = Number(((Number(row.piece_count) || 0) * (Number(row.net_weight) || 0)).toFixed(2))
  row.total_weight = Number(((Number(row.piece_count) || 0) * (Number(row.piece_weight) || 0)).toFixed(2))
}

function changePieceWeight(row: any) {
  computedTotalWeight(row)
  computedShortageWeightRate(row)
}

function clearCustomer() {
  state.formInline.receive_unit_id = 0
}

// const unitQuery = computed(() => {
//   return {
//     sale_system_id: state.formInline.sale_system_id,
//     unit_type_id: BusinessUnitIdEnum.knittingFactory,
//   }
// })

onMounted(() => {
  // 获取用户上的默认营销体系
  const res = getDefaultSaleSystem()
  state.formInline.sale_system_id = res.default_sale_system_id
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="underWeightOptions.datalist.length ? false : true" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.formInline" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '76px' }">
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.formInline.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable @change-value="clearCustomer" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-form-item>
              <el-input v-model="state.formInline.voucher_number" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="收货单位:">
          <template #content>
            <el-form-item prop="receive_unit_id">
              <SelectDialog
                v-model="state.formInline.receive_unit_id"
                api="business_unitlist"
                :query="{ sale_system_id: state.formInline.sale_system_id, unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-input="val => (componentRemoteSearch.name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="欠重日期:">
          <template #content>
            <el-form-item prop="shortage_date">
              <el-date-picker v-model="state.formInline.shortage_date" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" :copies="2">
          <template #content>
            <el-form-item prop="remark">
              <vxe-textarea v-model="state.formInline.remark" style="width: 100%" :maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="欠重信息" class="mt-[10px]" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button :disabled="!(state.formInline.sale_system_id && state.formInline.receive_unit_id)" type="primary" @click="showLibDialog">
        从采购收货单中添加
      </el-button>
    </template>
    <div>
      <Table ref="tablesRef" :config="underWeightOptions.tableConfig" :table-list="underWeightOptions.datalist" :column-list="underWeightOptions.columnList">
        <!-- 件数 -->
        <template #piece_count="{ row }">
          <vxe-input v-model="row.piece_count" type="float" @change="computedTotalWeight(row)" />
        </template>
        <!-- 件重 -->
        <template #piece_weight="{ row }">
          <vxe-input v-model="row.piece_weight" type="float" @change="changePieceWeight(row)" />
        </template>
        <!-- 毛重 -->
        <template #gross_weight="{ row }">
          <vxe-input v-model="row.gross_weight" type="float" @change="computedNetWeight(row)" />
        </template>
        <!-- 皮重 -->
        <template #tare_weight="{ row }">
          <vxe-input v-model="row.tare_weight" type="float" @change="computedNetWeight(row)" />
        </template>
        <!-- 欠重 -->
        <template #shortage_weight="{ row }">
          <vxe-input v-model="row.shortage_weight" type="float" @change="changePieceWeight(row)" />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" max-length="200" type="text" />
        </template>
        <!-- 操作 -->
        <template #operate="{ row }">
          <el-button type="text" @click="underWeightOptions.handleRowDel(row)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <MasterDialogAdd ref="MasterDialogAddRef" @handle-sure="underWeightOptions.handleSure" />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
</style>
