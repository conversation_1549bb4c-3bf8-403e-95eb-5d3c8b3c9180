declare namespace Api.BusinessUnit {
  export interface Request {
    /**
     * 地址
     */
    address?: string
    /**
     * 是否导出excel
     */
    download?: number
    /**
     * limit
     */
    limit?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 名称或电话
     */
    nop?: string
    /**
     * offset
     */
    offset?: number
    /**
     * or单位分类 1供应商 2客户
     */
    or_category?: number
    /**
     * 类型ids 配合 or_category用
     */
    or_unit_type_id?: string
    /**
     * page
     */
    page?: number
    /**
     * 电话
     */
    phone?: string
    /**
     * 销售区域id
     */
    sale_area_id?: number
    /**
     * 销售群体id
     */
    sale_group_id?: number
    /**
     * 营销系统ID
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    seller_id?: number
    /**
     * size
     */
    size?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 类型id
     */
    unit_type_id?: number
    [property: string]: any
  }
  /**
   * system.GetBizUnitListItem
   */
  export interface Response {
    /**
     * 地址
     */
    address?: string
    /**
     * 布飞前缀
     */
    bf_prefix?: string
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule?: number
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule_name?: string
    /**
     * 进位
     */
    carry?: number
    /**
     * 进位
     */
    carry_name?: string
    /**
     * 供应商 | 客户
     */
    category?: number
    /**
     * 供应商 | 客户
     */
    category_name?: string
    /**
     * 编号
     */
    code?: string
    /**
     * 联系人名称
     */
    contact_name?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 社会统一信用代码
     */
    credit_code?: string
    /**
     * 信用等级
     */
    credit_level?: number
    /**
     * 信用等级名称
     */
    credit_level_name?: string
    /**
     * 信用额度
     */
    credit_limit?: number
    /**
     * 坯布位数
     */
    decimal_point?: number
    /**
     * 坯布位数
     */
    decimal_point_name?: string
    /**
     * 染费收费方式 1坯布数量 2长度 3成品数量
     */
    dnf_charging_method?: number
    /**
     * 邮箱
     */
    email?: string
    /**
     * 企微客户Id
     */
    external_user_id?: string
    /**
     * 企微客户名称
     */
    external_user_name?: string
    /**
     * 坯布最大重量
     */
    fabric_max_weight?: number
    /**
     * 坯布最小重量
     */
    fabric_min_weight?: number
    /**
     * 传真
     */
    fax_number?: string
    /**
     * 全称
     */
    full_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 是否黑名单
     */
    is_blacklist?: boolean
    /**
     * 是否黑名单转义
     */
    is_blacklist_name?: string
    /**
     * 省市区
     */
    location?: string
    /**
     * 主要类型id
     */
    main_unit_type_id?: number
    /**
     * 主要类型名称
     */
    main_unit_type_name?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 跟单员id
     */
    order_follower_id?: number
    /**
     * 跟单员名称
     */
    order_follower_name?: string
    /**
     * 跟单电话
     */
    order_follower_phone?: string
    /**
     * 跟单QC员id
     */
    order_qc_user_id?: number
    /**
     * 跟单QC员名称
     */
    order_qc_user_name?: string
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 拼音
     */
    pin_yin?: string
    /**
     * 企业微信客户
     */
    qywx_customers?: SystemQYWXCustomer[]
    /**
     * 企业微信群聊
     */
    qywx_groups?: SystemQYWXGroupChat[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 销售区域id
     */
    sale_area_id?: number
    /**
     * 销售区域名称
     */
    sale_area_name?: string
    /**
     * 销售群体id
     */
    sale_group_id?: number
    /**
     * 销售群体名称
     */
    sale_group_name?: string
    /**
     * 所属营销体系id
     */
    sale_system_id?: number
    /**
     * 所属营销体系ids
     */
    sale_system_ids?: number[]
    /**
     * 主要所属营销体系名称
     */
    sale_system_name?: string
    /**
     * 所属营销体系名称
     */
    sale_system_names?: string
    /**
     * 销售员id
     */
    seller_id?: number
    /**
     * 销售员名称
     */
    seller_name?: string
    /**
     * 结算天数
     */
    settle_cycle?: number
    /**
     * 结算类型
     */
    settle_type?: number
    /**
     * 结算类型名称
     */
    settle_type_name?: string
    /**
     * 状态
     */
    status?: number
    /**
     * 类型id
     */
    unit_type_id?: number[]
    /**
     * 类型名称
     */
    unit_type_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 织造入库方式
     */
    weaving_storage_method?: number
    /**
     * 织造入库方式
     */
    weaving_storage_method_name?: string
    /**
     * 称重验布流程
     */
    wfi_process?: number
    /**
     * 称重验布流程
     */
    wfi_process_name?: string
    [property: string]: any
  }

  /**
   * system.QYWXCustomer
   */
  export interface SystemQYWXCustomer {
    id?: string
    name?: string
    type?: string
    [property: string]: any
  }

  /**
   * system.QYWXGroupChat
   */
  export interface SystemQYWXGroupChat {
    id?: string
    name?: string
    notify_type?: number
    notify_type_name?: string
    [property: string]: any
  }
}
