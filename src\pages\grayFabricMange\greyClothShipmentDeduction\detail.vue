<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import {
  getGfmDeductionDeliveryOrder,
  updateGfmDeductionDeliveryOrderStatusCancel,
  updateGfmDeductionDeliveryOrderStatusPass,
  updateGfmDeductionDeliveryOrderStatusReject,
  updateGfmDeductionDeliveryOrderStatusWait,
} from '@/api/greyClothShipmentDeduction'
import { formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { deepClone, orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { processDataOut } from '@/common/handBinary'

const rourte = useRoute()

const { fetchData, data } = getGfmDeductionDeliveryOrder()

onMounted(() => {
  fetchData({ id: rourte.query.id })
})

const columnList_fabic_config = ref({
  fieldApiKey: 'GreyClothShipmentDeductionDetail',
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['total_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'total_weight') as any)} KG`

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['total_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'total_price') as any)}`

      if (['roll_return'].includes(column.property))
        return `${sumNum(data, 'roll_return')}`

      return null
    }),
  ]
}

const columnList_fabic = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
    // isWeight: true,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: '',
    title: '细码',
    minWidth: 100,
    soltName: 'xima',
    fixed: 'right',
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 100,
    fixed: 'right',
    isWeight: true,
  },
  // {
  //   field: 'avg_weight',
  //   title: '平均数量',
  //   minWidth: 100,
  //   fixed: 'right',
  //   isWeight: true,
  // },
  {
    field: 'single_price',
    title: '单价',
    minWidth: 100,
    fixed: 'right',
    isUnitPrice: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'total_price',
    title: '金额',
    minWidth: 100,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    fixed: 'right',
  },
])

const AddXimaDialogRef = ref()

function handSeeXima(row: any) {
  AddXimaDialogRef.value.state.showModal = true
  let listData = deepClone(row?.item_fc_data || [])
  listData = processDataOut(listData)
  AddXimaDialogRef.value.state.tableData = listData || []
  AddXimaDialogRef.value.state.canEnter = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.horsepower = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.isDisabled = true
}

async function updateStatus(audit_status: number) {
  const id: any = rourte.query.id?.toString()
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: updateGfmDeductionDeliveryOrderStatusCancel })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: updateGfmDeductionDeliveryOrderStatusReject })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: updateGfmDeductionDeliveryOrderStatusPass })

  if (audit_status === 1)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: updateGfmDeductionDeliveryOrderStatusWait })

  fetchData({ id: rourte.query.id })
}
</script>

<template>
  <StatusColumn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="GreyClothShipmentDeduction_wait"
    permission_reject_key="GreyClothShipmentDeduction_reject"
    permission_pass_key="GreyClothShipmentDeduction_pass"
    permission_cancel_key="GreyClothShipmentDeduction_cancel"
    permission_edit_key="GreyClothShipmentDeduction_edit"
    edit_router_name="GreyClothShipmentDeductionEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="存货单位:">
        <template #content>
          {{ data?.stock_unit_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供方名称:">
        <template #content>
          {{ data?.supplier_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="出货日期:">
        <template #content>
          {{ data?.delivery_time }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据备注:" copies="2">
        <template #content>
          {{ data?.remark }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <Table :config="columnList_fabic_config" :table-list="data?.item_data" :column-list="columnList_fabic">
      <template #xima="{ row }">
        <el-button type="primary" text link @click="handSeeXima(row)">
          查看
        </el-button>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <AddXimaDialog ref="AddXimaDialogRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
