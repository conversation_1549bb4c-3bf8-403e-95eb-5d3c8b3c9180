import { useRequest } from '@/use/useRequest'

// 成品采购退货单列表
export const GetPurchaseProductReturnOrderList = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/getPurchaseProductReturnOrderList`,
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 成品采购退货单详情
export const GetPurchaseProductReturnOrder = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/getPurchaseProductReturnOrder`,
    method: 'get',
  })
}

// 成品采购退货单编辑
export const UpdatePurchaseProductReturnOrder = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/updatePurchaseProductReturnOrder`,
    method: 'put',
  })
}

// 成品采购退货单添加
export const AddPurchaseProductReturnOrder = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/addPurchaseProductReturnOrder`,
    method: 'post',
  })
}

// 成品采购退货单消审
export const UpdatePurchaseProductReturnOrderStatusWait = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/updatePurchaseProductReturnOrderStatusWait`,
    method: 'put',
  })
}

// 成品采购退货单审核
export const UpdatePurchaseProductReturnOrderStatusPass = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/updatePurchaseProductReturnOrderStatusPass`,
    method: 'put',
  })
}

// 成品采购退货单驳回
export const UpdatePurchaseProductReturnOrderStatusReject = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/updatePurchaseProductReturnOrderStatusReject`,
    method: 'put',
  })
}

// 成品采购退货单作废
export const UpdatePurchaseProductReturnOrderStatusCancel = () => {
  return useRequest({
    url: `/admin/v1/purchase/purchaseProductReturnOrder/updatePurchaseProductReturnOrderStatusCancel`,
    method: 'put',
  })
}
