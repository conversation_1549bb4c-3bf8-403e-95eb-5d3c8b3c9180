import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export const redye_orderlist = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const redye_orderlistExoprt = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/dyeing_and_finishing/redye_order/list',
    method: 'get',
    nameFile,
  })
}

// 从染整进度中添加成品
export const list_enum = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/situation/list_enum',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 详情
export const redye_orderldetail = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order/detail',
    method: 'get',
  })
}

// 新建
export const redye_orderlpost = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order',
    method: 'post',
  })
}

// 更新
export const redye_orderlput = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order',
    method: 'put',
  })
}

// 审核
export const redye_orderlpass = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order/pass',
    method: 'put',
  })
}

// 消审
export const redye_orderlcancel = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order/cancel',
    method: 'put',
  })
}

// 作废
export const redye_orderlvoid = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order/void',
    method: 'put',
  })
}

// 驳回
export const redye_orderlreject = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/redye_order/reject',
    method: 'put',
  })
}

// 染整单项详情
export const itemsdetail = () => {
  return useRequest({
    url: '/admin/v1/dyeing_and_finishing/order/items_detail',
    method: 'get',
  })
}
