# 佛山南计电子秤通信协议数据格式

本文档描述了电子秤的数据通信协议格式，包含了不同地址的输出格式规范。

## 协议概述

电子秤支持多种地址模式的数据输出格式，每种格式都有特定的数据结构和编码方式。当电子台秤的地址（Adr）选择不同的值时，电子台秤将通过串口，以Modus ASCII protocol 方式传送重量数据。格式如下：

当电子台秤上显示-1234.5kg 毛重
| 数据 | X8 | X7 | X6 | X5 | X4 | X3 | X2 | X1 |
|------|----|----|----|----|----|----|----|----|
| 重量 -1234.5 | - | 0 | 1 | 2 | 3 | 4 | . | 5 |

状态字S
| 位 | 7 | 6 | 5 | 4 | 3 | 2 | 1 | 0 |
|------|---|---|---|---|---|---|---|---|
|0| 0 | 0 | 1 | 0 | 0 | 称重不稳定 | 毛重 | 正 |
|1| 0 | 0 | 1 | 0 | 0 | 称重稳定 | 净重 | 负 |
## 数据格式规范

### Adr = 00 输出格式（重量数据）

| 字节 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 |
|------|---|---|---|---|---|---|---|---|---|----|----|
| **数据说明** | S | X7 | X6 | X5 | X4 | X3 | X2 | X1 | CR | LF | D |
| **HEX** | 25 | 30 | 31 | 32 | 33 | 34 | 2B | 35 | 0D | 0A | - |
| **ASCII码** | % | 0 | 1 | 2 | 3 | 4 | . | 5 | CR | LF | - |

> **示例说明：** 显示 -1234.5 kg 毛重

### Adr = 97 输出格式

| 字节 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 |
|------|---|---|---|---|---|---|---|---|---|
| **数据说明** | = | X1 | X2 | X3 | X4 | X5 | X6 | X7 | S |
| **HEX** | 3D | 35 | 2E | 34 | 33 | 32 | 31 | 30 | 25 |
| **ASCII码** | = | 5 | . | 4 | 3 | 2 | 1 | 0 | % |

### Adr = 98 输出格式

| 字节 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 |
|------|---|---|---|---|---|---|---|---|---|
| **数据说明** | = | S | X7 | X6 | X5 | X4 | X3 | X2 | X1 |
| **HEX** | 3D | 25 | 30 | 31 | 32 | 33 | 34 | 2B | 35 |
| **ASCII码** | = | % | 0 | 1 | 2 | 3 | 4 | . | 5 |

### Adr = 99 输出格式

| 字节 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 |
|------|---|---|---|---|---|---|---|---|---|----|----|
| **数据说明** | = | X8 | X7 | X6 | X5 | X4 | X3 | X2 | X1 | CR | LF |
| **HEX** | 3D | 2D | 30 | 31 | 32 | 33 | 34 | 2B | 35 | 0D | 0A |
| **ASCII码** | = | - | 0 | 1 | 2 | 3 | 4 | . | 5 | CR | LF |

## 字段说明

- **S**: 符号位（正负号）
- **X1-X8**: 数据位（权重值）
- **CR**: 回车符 (0x0D)
- **LF**: 换行符 (0x0A)
- **D**: 数据结束标识
- **=**: 数据开始标识

## 使用说明

1. 不同的地址模式适用于不同的应用场景
2. Adr = 00 格式包含完整的重量数据和控制字符
3. Adr = 97-99 格式为简化的数据传输格式
4. 所有格式都使用ASCII编码进行数据传输
