import { useRequest } from '@/use/useRequest'

// 获取生产通知单列表
export function getProductionNotice() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取坯布信息
export function byIdGreyInfoProductionNotice() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getListGreyFabricItems',
    method: 'get',
  })
}

// 获取用料比例
export function byIdMaterialProductionNotice() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getListMaterialRatioItems',
    method: 'get',
  })
}

// 获取工艺要求
export function byIdTechnologicalRequirementProductionNotice() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getListTechnologicalRequirementItems',
    method: 'get',
  })
}
// 获取变更记录
export function byIdOrderChangeLogItemsProductionNotice() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderChangeLogItems',
    method: 'get',
  })
}

// 获取生产计划单
export function getProductionPlanOrder() {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/getProductionPlanOrderTotalGreyFabricList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取生产计划单枚举
export function GetProductionPlanOrderEsc() {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/getProductionPlanOrderDropdownList',
    method: 'get',
  })
}

// 获取付款期限
export function GetInfoProductPaymentTermList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoProductPaymentTerm/getInfoProductPaymentTermList',
    method: 'get',
  })
}

// 根据坯布id获取工艺要求
export function getByIdProcessRequirement() {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getGreyFabricInfoUseByOther',
    method: 'get',
  })
}

// 根据坯布id获取原料信息
export function getByIdMasterInfo() {
  return useRequest({
    url: '/admin/v1/grey_fabric_info/greyFabricInfo/getRawMaterialInfoListEnum',
    method: 'get',
  })
}

// 获取基础资料中原料列表
export function getRawMaterialList() {
  return useRequest({
    url: '/admin/v1/basic_data/raw_material/dropdownList',
    method: 'get',
    query: {
      status: 1,
    },
    pagination: true,
    pageSize: 50,
  })
}

// 添加生产通知单
export function addProductionNotifyOrder() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/addProductionNotifyOrder',
    method: 'post',
  })
}

// 获取生产通知单
export function getProductionNotifyOrder() {
  return useRequest<Api.GetProductionNotifyOrder.Request, Api.GetProductionNotifyOrder.Response>({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrder',
    method: 'get',
  })
}

// 获取生产通知单枚举
export function GetProductionNotifyOrderDropdownList() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderDropdownList',
    method: 'get',
  })
}

// 获取生产通知单枚举（分页）
export function GetProductionNotifyOrderDropdownListPage() {
  return useRequest<Api.ProductionNoticeOrderDownList.Request, ResponseList<Api.ProductionNoticeOrderDownList.Response>>({
    url: '/admin/v1/produce/productionNotifyOrder/getProductionNotifyOrderDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 审核
export function checkSheetOfProductionNotify() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/updateProductionNotifyOrderStatusAudit',
    method: 'put',
  })
}
// 消审
export function cancelApprovedSheetOfProductionNotify() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/updateProductionNotifyOrderStatusWait',
    method: 'put',
  })
}
// 作废
export function deleteCancelSheetOfProductionNotify() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/updateProductionNotifyOrderStatusCancel',
    method: 'put',
  })
}
// 驳回
export function rejectSheetOfProductionNotify() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/updateProductionNotifyOrderStatusReject',
    method: 'put',
  })
}

// 更新生产通知单
export function editfProductionNotify() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/updateProductionNotifyOrder',
    method: 'put',
  })
}
// 业务关闭按钮生产通知单
export function closeProductionNotify() {
  return useRequest({
    url: '/admin/v1/produce/productionNotifyOrder/updateProductionNotifyOrderBusinessClose',
    method: 'put',
  })
}

// 获取销售计划单列表
export function getSaleProductPlanOrderGfDropdownListUseNotify() {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/getSaleProductPlanOrderGfDropdownListUseNotify',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 添加生产排产单
export function AddProductionScheduleOrder() {
  return useRequest<Api.AddProductionScheduleOrder.Request, Api.AddProductionScheduleOrder.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/add',
    method: 'post',
  })
}
// 更新生产排产单
export function UpdateProductionScheduleOrder() {
  return useRequest<Api.UpdateProductionScheduleOrder.Request, Api.UpdateProductionScheduleOrder.Response>({
    url: '/admin/v1/produce/productionScheduleOrder/update',
    method: 'put',
  })
}
