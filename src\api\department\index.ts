import { useRequest } from '@/use/useRequest'
import { type ResponseList } from '../commonTs'
import { type SystemGetDepartmentData } from './rule'

// 部门列表
export const GetDepartmentApi = () => {
  return useRequest<any, ResponseList<SystemGetDepartmentData>>({
    url: '/admin/v1/department',
    method: 'get',
  })
}
// 部门编辑
export const PostDepartmentApi = () => {
  return useRequest({
    url: '/admin/v1/department',
    method: 'post',
  })
}
// 部门删除
export const DeleteDepartmentApi = () => {
  return useRequest({
    url: '/admin/v1/department',
    method: 'delete',
  })
}
// 部门修改
export const EditDepartmentApi = () => {
  return useRequest({
    url: '/admin/v1/department',
    method: 'put',
  })
}
