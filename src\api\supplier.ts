import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function Business_unitsupplier_list() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 启用数据
export function Business_unitsupplierenable() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/enable',
    method: 'put',
  })
}

// 禁用数据
export function Business_unitsupplierdisable() {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/disable',
    method: 'put',
  })
}

// 详情
export function Business_unitsupplierdetail() {
  return useRequest<Api.SupplierDetail.Request, Api.SupplierDetail.Response>({
    url: '/admin/v1/business_unit/supplier/detail',
    method: 'get',
  })
}

// 删除供应商
export function Business_unitsupplierdelete() {
  return useRequest<Api.SupplierDetail.Request, void>({
    url: '/admin/v1/business_unit/supplier',
    method: 'delete',
  })
}

// 导出数据
export function Business_unitsupplier_listExpord({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/business_unit/supplier/list',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 新增供应商
export function Business_unitsupplierPost() {
  return useRequest<Api.AddSupplier.Request, void>({
    url: '/admin/v1/business_unit/supplier',
    method: 'post',
  })
}

// 修改数据

export function Business_unitsupplierPut() {
  return useRequest<Api.Supplier.Request, void>({
    url: '/admin/v1/business_unit/supplier',
    method: 'put',
  })
}

// 染费收费枚举
export function DnfChargingMethodEnum() {
  return useRequest({
    url: '/admin/v1/business_unit/dnf_charging_method/enum',
    method: 'get',
  })
}
