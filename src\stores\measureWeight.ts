import { defineStore } from 'pinia'
import type { MeasureWeightServiceReturnType } from '@/use/useMeasureWeightService'
import { useMeasureWeightService } from '@/use/useMeasureWeightService'

interface State {
  Loglist: string[]
  measureWeightState: MeasureWeightServiceReturnType
}
export const useMeasureWeightStore = defineStore('measureWeight', {
  state: (): State => {
    const measureWeightService = useMeasureWeightService()
    return {
      Loglist: JSON.parse(localStorage.getItem('measureWeightLogList')!) || [],
      measureWeightState: measureWeightService,
    }
  },
  actions: {
    /**
     * 设置打印日志列表
     * @param {object} state
     * @param {Array} data
     */
    setLogList(data: State['Loglist']) {
      this.Loglist = data
      localStorage.setItem('measureMeterLogList', JSON.stringify(data))
    },
    /**
     * 清除打印日志列表
     * @param {object} state
     */
    clearLogList() {
      this.Loglist = []
      localStorage.removeItem('measureMeterLogList')
    },
    setCommand(data: string) {
      this.measureWeightState.command = data
    },
    /**
     * 设置默认打印日志
     * @param {*} state
     * @param {*} data
     */
    setMeasureWeightState(data: MeasureWeightServiceReturnType) {
      this.measureWeightState = data
      localStorage.setItem('measureWeightState', JSON.stringify(data))
    },
    /**
     * 清除默认打印日志
     * @param {*} state
     */
    clearMeasurWeightState() {
      this.measureWeightState = null
      localStorage.removeItem('measureWeightState')
    },
  },
})
