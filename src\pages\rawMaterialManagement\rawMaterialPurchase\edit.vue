<script lang="ts" setup name="RawMaterialPurchaseReceiptEdit">
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import SelectProductionNotice from '../../procurementManaging/rawMaterialSourcing/components/SelectProductionNotice/index.vue'
import SelectInformation from './components/SelectInformation/index.vue'
import SelectPurchasing from './components/SelectPurchasing/index.vue'
import { PurchaseReceiveOrderDetail, PurchaseReceiveOrderEdit } from '@/api/rawMaterIalPurchase'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import {
  formatDate,
  formatPriceDiv,
  formatPriceMul,
  formatTwoDecimalsMul,
  formatUnderweightRateDiv,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { filterNumber, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { GetGreyFabricInfoListUseByOthersMenu } from '@/api/greyFabricInformation'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: 0,
    supplier_id: 0,
    receipt_unit_id: '',
    receipt_date: '',
    receiving_time: '',
    invoice_header_id: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    warehouse_manager_id: '',
    warehouse_manager_name: '',
    voucher_num: '',
    items: [],
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    receipt_date: [{ required: true, message: '请选择收货日期', trigger: 'change' }],
  },
})

const showAdd = ref(false)
const showPurchasing = ref(false)
const multipleSelection = ref<any[]>([])

function handDel(row: any, rowIndex: number) {
  ElMessageBox.confirm('是否删除该数据?', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      state.tableData.splice(rowIndex, 1)
      const selectIndex = multipleSelection.value.findIndex(e => e._X_ROW_KEY_TABLE === row._X_ROW_KEY_TABLE)
      if (selectIndex !== -1)
        multipleSelection.value.splice(selectIndex, 1)
    })
    .catch(() => {})
}
function handAdd() {
  showAdd.value = true
}
function handAddPurchasing() {
  if (!state.form.sale_system_id || !state.form.supplier_id)
    return ElMessage.error('请先选择营销体系名称和供应商名称')

  showPurchasing.value = true
}

const { fetchData, data } = GetGreyFabricInfoListUseByOthersMenu()

async function getEnum() {
  fetchData()
}

getEnum()

onMounted(() => {
  getDataDetail()
})

// 获取订单详情
const { fetchData: fetchDataDetail, data: dataDetail } = PurchaseReceiveOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: router.params.id })
}
watch(
  () => dataDetail.value,
  (val) => {
    if (val) {
      state.form.sale_system_id = val.sale_system_id
      state.form.supplier_id = val.supplier_id
      state.form.receipt_unit_id = val.receipt_unit_id
      state.form.receipt_date = val.receipt_date
      state.form.receiving_time = val.receiving_time
      state.form.invoice_header_id = val.invoice_header_id
      state.form.remark = val.remark
      state.form.sale_system_name = val.sale_system_name
      state.form.supplier_name = val.supplier_name
      state.form.receipt_unit_name = val.receipt_unit_name
      state.form.warehouse_manager_id = val.warehouse_manager_id
      state.form.warehouse_manager_name = val.warehouse_manager_name
      state.form.voucher_num = val.voucher_num
      state.tableData = formatPriceData(val.items || [])
    }
  },
)

// 整理金额和数量
function formatPriceData(data: any[]) {
  const res = data.map((item) => {
    return {
      ...item,
      bulk_piece_count: formatPriceDiv(item.bulk_piece_count),
      whole_piece_count: formatPriceDiv(item.whole_piece_count),
      whole_piece_weight: formatWeightDiv(item.whole_piece_weight),
      bulk_weight: formatWeightDiv(item.bulk_weight),
      total_weight: formatWeightDiv(item.total_weight),
      deduction_weight: formatWeightDiv(item.deduction_weight),
      avg_actl_gross_weight: formatWeightDiv(item.avg_actl_gross_weight),
      avg_actl_tare: formatWeightDiv(item.avg_actl_tare),
      avg_actl_net_weight: formatWeightDiv(item.avg_actl_net_weight),
      avg_actl_weight_shortage: formatWeightDiv(item.avg_actl_weight_shortage),
      actl_total_net_weight: formatWeightDiv(item.actl_total_net_weight),
      actl_total_weight_shortage: formatWeightDiv(item.actl_total_weight_shortage),
      other_price: formatPriceDiv(item.other_price),
      unit_price: formatUnitPriceDiv(item.unit_price),
      total_price: formatPriceDiv(item.total_price),
      weight_shortage_rate: formatUnderweightRateDiv(item.weight_shortage_rate),
      weight_shortage_loss_rate: formatUnderweightRateDiv(item.weight_shortage_loss_rate),
    }
  })
  return res
}

const columnList = ref([
  {
    title: '原料信息',
    childrenList: [
      {
        field: 'sale_plan_order_item_no',
        title: '销售计划详情单号',
        fixed: 'left',
        width: 150,
      },
      {
        field: 'raw_material_code',
        title: '原料编号',
        fixed: 'left',
        width: 150,
      },
      {
        field: 'raw_material_name',
        title: '原料名称',
        fixed: 'left',
        width: 130,
      },
      {
        field: 'pur_order_num',
        title: '采购单号',
        width: 130,
      },
      {
        field: 'customer_id',
        title: '所属客户',
        width: 130,
        soltName: 'customer_id',
      },
      {
        field: 'brand',
        title: '原料品牌',
        width: 100,
        soltName: 'brand',
      },
      {
        field: 'craft',
        title: '原料工艺',
        width: 100,
      },
      {
        field: 'color_scheme',
        title: '原料颜色',
        width: 100,
        soltName: 'color_scheme',
      },
      {
        field: 'measurement_unit_id',
        title: '计量单位',
        width: 100,
        soltName: 'measurement_unit_id',
      },
      {
        field: 'dyelot_number',
        title: '缸号',
        width: 100,
        soltName: 'dyelot_number',
      },
      {
        field: 'batch_num',
        title: '原料批号',
        width: 100,
        soltName: 'batch_num',
      },
      {
        field: 'production_order_num',
        title: '生产通知单号',
        width: 165,
        soltName: 'production_order_num',
      },
      {
        field: 'grey_fabric_code',
        title: '坯布编号',
        width: 130,
        soltName: 'grey_fabric_code',
      },
      {
        field: 'grey_fabric_name',
        title: '坯布名称',
        width: 130,
        soltName: 'grey_fabric_name',
      },
      {
        field: 'cotton_origin',
        title: '棉花产地',
        width: 100,
        soltName: 'cotton_origin',
      },
      {
        field: 'yarn_origin',
        title: '棉纱产地',
        width: 100,
        soltName: 'yarn_origin',
      },
      {
        field: 'carton_num',
        title: '装箱单号',
        width: 100,
        soltName: 'carton_num',
      },
      {
        field: 'fapiao_num',
        title: '发票号',
        width: 100,
        soltName: 'fapiao_num',
      },
      {
        field: 'weight_shortage_loss_rate',
        title: '欠重损耗率',
        width: 100,
        soltName: 'weight_shortage_loss_rate',
      },
      {
        field: 'spinning_type',
        title: '纺纱类型',
        width: 100,
        soltName: 'spinning_type',
      },
      {
        field: 'production_date',
        title: '生产日期',
        width: 100,
        soltName: 'production_date',
      },
      {
        field: 'level',
        title: '原料等级',
        width: 130,
        soltName: 'level',
      },
      {
        field: 'raw_material_remark',
        title: '原料备注',
        width: 100,
        soltName: 'raw_material_remark',
      },
    ],
  },
  {
    title: '整件',
    childrenList: [
      {
        field: 'whole_piece_count',
        title: '收货件数',
        width: 150,
        soltName: 'whole_piece_count',
        required: true,
      },
      {
        field: 'whole_piece_weight',
        title: '收货件重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'whole_piece_weight',
        required: true,
      },
    ],
  },
  {
    title: '散件',
    childrenList: [
      {
        field: 'bulk_piece_count',
        title: '收货件数',
        width: 150,
        soltName: 'bulk_piece_count',
      },
      {
        field: 'bulk_weight',
        title: '收货数量',
        width: 150,
        isWeight: true,
        soltName: 'bulk_weight',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'total_weight',
        title: '总收货数量',
        width: 150,
        isWeight: true,
        soltName: 'total_weight',
      },
      {
        title: '待收总数量',
        field: 'total_wait_collect_weight',
        isWeight: true,
        width: 100,
      },
      {
        field: 'deduction_weight',
        title: '扣款数量',
        width: 150,
        isWeight: true,
        soltName: 'deduction_weight',
      },
    ],
  },
  {
    title: '平均',
    childrenList: [
      {
        field: 'avg_actl_gross_weight',
        title: '实际毛重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'avg_actl_gross_weight',
      },
      {
        field: 'avg_actl_tare',
        title: '实际皮重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'avg_actl_tare',
      },
      {
        field: 'avg_actl_net_weight',
        title: '实际净重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'avg_actl_net_weight',
      },
      {
        field: 'avg_actl_weight_shortage',
        title: '实际欠重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'avg_actl_weight_shortage',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'actl_total_net_weight',
        title: '实际总净重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'actl_total_net_weight',
      },
      {
        field: 'actl_total_weight_shortage',
        title: '实际总欠重(kg)',
        width: 150,
        isWeight: true,
        soltName: 'actl_total_weight_shortage',
      },
      {
        field: 'weight_shortage_rate',
        title: '欠磅率(%)',
        width: 150,
        soltName: 'weight_shortage_rate',
        isUnderweightRate: true,
      },
    ],
  },
  {
    title: '金额信息',
    childrenList: [
      {
        field: 'other_price',
        title: '其他金额',
        width: 150,
        isPrice: true,
        soltName: 'other_price',
      },
      {
        field: 'unit_price',
        title: '单价',
        width: 150,
        isPrice: true,
        soltName: 'unit_price',
        required: true,
      },
      {
        field: 'total_price',
        title: '总金额',
        width: 150,
        isPrice: true,
        soltName: 'total_price',
      },
    ],
  },
  {
    title: '',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        width: 150,
        soltName: 'remark',
      },
    ],
  },
])

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
}

const saleSaleSystemInfo = ref()
function getSaleSystemInfo(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData()
}

const greyFabricStatus = ref(false)
function getProductionOrder(val: any, row: any) {
  if (val) {
    row.grey_fabric_id = val?.grey_fabric_id
    row.grey_fabric_code = val?.grey_fabric_code
    row.grey_fabric_name = val?.grey_fabric_name
    row.production_date = val?.notify_date

    greyFabricStatus.value = true
  }
  else {
    if (!val.initFirst) {
      greyFabricStatus.value = false
      row.grey_fabric_id = ''
      row.grey_fabric_code = ''
      row.grey_fabric_name = ''
      row.production_date = ''
    }
  }
}

const tablesRef = ref()
watch(
  () => [
    changeRow.value?.whole_piece_count,
    changeRow.value?.whole_piece_weight,
    changeRow.value?.bulk_piece_count,
    changeRow.value?.deduction_weight,
    changeRow.value?.bulk_weight,
    changeRow.value?.avg_actl_gross_weight,
    changeRow.value?.avg_actl_tare,
    changeRow.value?.avg_actl_net_weight,
    changeRow.value?.avg_actl_weight_shortage,
    changeRow.value?.unit_price,
    changeRow.value?.total_weight,
    changeRow.value?.other_price,
    changeRow.value?.actl_total_net_weight,
    changeRow.value?.actl_total_weight_shortage,
    changeRow.value?.weight_shortage_rate,
  ],
  () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

function getFabric(val: any, row: any) {
  row.grey_fabric_name = val?.name
  row.grey_fabric_code = val?.code
}

function computedData(row: any) {
  // 总采购数量
  row.total_weight = Number.parseFloat(
    Big(row.whole_piece_count || 0)
      .times(row?.whole_piece_weight || 0)
      .plus(row?.bulk_weight || 0)
      .toFixed(4),
  )

  // 平均实际净重
  row.avg_actl_net_weight = Number.parseFloat(
    Big(row.avg_actl_gross_weight || 0)
      .minus(row?.avg_actl_tare || 0)
      .toFixed(4),
  )

  // 平均实际欠重
  row.avg_actl_weight_shortage = Number.parseFloat(
    Big(row.whole_piece_weight || 0)
      .minus(row?.avg_actl_net_weight || 0)
      .toFixed(4),
  )

  // 实际总欠重
  row.actl_total_weight_shortage = Number.parseFloat(
    Big(row.avg_actl_weight_shortage || 0)
      .times(row?.whole_piece_count || 0)
      .toFixed(2),
  )

  // 实际总净重
  row.actl_total_net_weight = Number.parseFloat(
    Big(row.avg_actl_net_weight || 0)
      .times(row?.whole_piece_count || 0)
      .toFixed(2),
  )

  // 亏磅率
  if (filterNumber(row?.whole_piece_weight) && row?.whole_piece_weight !== 0) {
    row.weight_shortage_rate = Number.parseFloat(
      Big(row.avg_actl_weight_shortage || 0)
        .div(row?.whole_piece_weight || 0)
        .times(100)
        .toFixed(2),
    )
  }
  else {
    row.weight_shortage_rate = ''
  }

  // 金额
  const res = Big(row?.total_weight || 0).minus(row?.deduction_weight || 0)
  row.total_price = Number.parseFloat(
    res
      .times(row?.unit_price || 0)
      .plus(row?.other_price || 0)
      .toFixed(2),
  )

  tablesRef.value.tableRef.updateFooter()
}

const bulkShow = ref(false)
const bulkSetting = ref<any>({
  address: {},
})

async function bulkSubmit({ row, value }: any, selectRes: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  multipleSelection.value?.map((item: any) => {
    if (row.field === 'grey_fabric_code' || row.field === 'grey_fabric_name') {
      if (!item.production_order_num) {
        item.grey_fabric_code = selectRes?.code
        item.grey_fabric_name = selectRes?.name
        item.grey_fabric_id = selectRes?.id
      }
    }
    else if (row.field === 'production_order_num') {
      getProductionOrder(selectRes, item)
      item.production_order_num = selectRes?.order_no
    }
    else {
      item[row.field] = value[row.field]
    }
    computedData(item)
  })
  ElMessage.success('设置成功')
}

function bulkHand() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')
  bulkShow.value = true
}

function onSubmit(row: any) {
  showAdd.value = false
  const data = row?.map((item: any) => {
    return {
      raw_material_code: item.code,
      raw_material_name: item.name,
      craft: item.craft,
      raw_material_id: item.id,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || 0,
      whole_piece_count: 0,
      whole_piece_weight: 0,
      bulk_piece_count: 0,
      bulk_weight: 0,
      deduction_weight: 0,
      avg_actl_gross_weight: 0,
      avg_actl_tare: 0,
      other_price: 0,
      unit_price: 0,
      color_id: '',
      measurement_unit_id: item.unit_id,
      dyelot_number: '',
    }
  })
  state.tableData = [...state.tableData, ...data]
}

function onSubmitPurchasing(row: any) {
  showPurchasing.value = false
  const data = row?.map((item: any) => {
    const info = {
      ...item,
      selected: false,
      raw_material_code: item.raw_material_code,
      raw_material_name: item.raw_material_name,
      craft: item.craft,
      brand: item.brand,
      color_scheme: item.color_scheme,
      batch_num: item.batch_num,
      level: item.level,
      grey_fabric_code: item.blank_fabric_code,
      grey_fabric_name: item.blank_fabric_name,
      grey_fabric_id: item.blank_fabric_id || 0,
      production_order_num: item.production_order_num,
      pur_order_num: item.pur_order_num,
      raw_material_id: item.raw_material_id,
      pur_order_item_id: item.id,
      bulk_piece_count: item.bulk_piece_count_unrec || 0,
      bulk_weight: formatWeightDiv(item.bulk_piece_weight_unrec || 0),
      whole_piece_count: item.whole_piece_count_unrec || 0,
      whole_piece_weight: formatWeightDiv(item.whole_piece_weight || 0),
      customer_id: item?.customer_id || 0,
      unit_price: formatUnitPriceDiv(item.unit_price || 0),
      receipt_unit_id: item.receipt_unit_id || 0,
      color_id: item.color_id,
      measurement_unit_id: item.measurement_unit_id,
      dyelot_number: '',
    }
    computedData(info)
    return info
  })
  state.form.receipt_unit_id = state.form.receipt_unit_id ? state.form.receipt_unit_id : data[0]?.receipt_unit_id
  state.tableData = [...state.tableData, ...data]
}

const { fetchData: fetchDataEdit, data: addData, success: successAdd, msg: msgAdd } = PurchaseReceiveOrderEdit()
const routerList = useRouterList()
const ruleFormRef = ref()
async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const res = fomatData()
      await fetchDataEdit(
        getFilterData({
          ...state.form,
          receipt_date: formatDate(state.form.receipt_date),
          receiving_time: formatDate(state.form.receiving_time),
          items: res || [],
          id: Number.parseInt(router.params.id as string),
        }),
      )
      if (successAdd.value) {
        getDataDetail()
        routerList.push({
          name: 'RawMaterialPurchaseReceiptDetail',
          params: { id: addData.value.id },
        })
        ElMessage.success('编辑成功')
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '原料信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (item.whole_piece_count === '') {
        msg = `编号为${item.raw_material_code}的数据,整件收货件数不能为空`
        return true
      }
      if (item.whole_piece_weight === '') {
        msg = `编号为${item.raw_material_code}的数据,整件收货件重不能为空`
        return true
      }
      if (item.bulk_weight === '') {
        msg = `编号为${item.raw_material_code}的数据,散件收货数量不能为空`
        return true
      }
      if (!item.total_weight || item.total_weight <= 0) {
        msg = `编号为${item.raw_material_code}的数据,总收货数量不能小于0`
        return true
      }
      if (!item.unit_price) {
        msg = `编号为${item.code}的数据,单价不能为空`
        return true
      }
    })
  }
  msg && ElMessage.error(msg)
  return msg
}

// // 整理提交信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    const {
      actl_total_net_weight,
      actl_total_weight_shortage,
      deduction_weight,
      avg_actl_gross_weight,
      avg_actl_net_weight,
      bulk_weight,
      avg_actl_tare,
      bulk_piece_count,
      avg_actl_weight_shortage,
      other_price,
      total_price,
      total_weight,
      unit_price,
      weight_shortage_loss_rate,
      weight_shortage_rate,
      whole_piece_count,
      whole_piece_weight,
    } = item
    return {
      ...item,
      actl_total_net_weight: formatWeightMul(actl_total_net_weight),
      production_date: formatDate(item.production_date),
      actl_total_weight_shortage: formatWeightMul(actl_total_weight_shortage),
      avg_actl_gross_weight: formatWeightMul(avg_actl_gross_weight),
      avg_actl_net_weight: formatWeightMul(avg_actl_net_weight),
      avg_actl_tare: formatWeightMul(avg_actl_tare),
      avg_actl_weight_shortage: formatWeightMul(avg_actl_weight_shortage),
      bulk_piece_count: formatPriceMul(bulk_piece_count),
      bulk_weight: formatWeightMul(bulk_weight),
      deduction_weight: formatWeightMul(deduction_weight),
      other_price: formatPriceMul(other_price),
      total_price: formatPriceMul(total_price),
      total_weight: formatWeightMul(total_weight),
      unit_price: formatUnitPriceMul(unit_price),
      weight_shortage_loss_rate: formatTwoDecimalsMul(weight_shortage_loss_rate),
      weight_shortage_rate: formatTwoDecimalsMul(weight_shortage_rate),
      whole_piece_count: formatPriceMul(whole_piece_count),
      whole_piece_weight: formatWeightMul(whole_piece_weight),
      color_id: Number(item.color_id),
      measurement_unit_id: Number(item.measurement_unit_id),
    }
  })
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'brand',
    title: '原料品牌',
    component: 'input',
    type: 'text',
  },
  {
    field: 'color_id',
    title: '原料颜色',
    component: 'select',
    api: 'GetRawMaterialColor',
  },
  {
    field: 'batch_num',
    title: '原料批号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'production_order_num',
    title: '生产通知单号',
    field_name: 'production_order_num',
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthersMenu',
    labelField: 'code',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    component: 'select',
    api: 'GetGreyFabricInfoListUseByOthersMenu',
  },
  {
    field: 'level',
    title: '原料等级',
    component: 'select',
    api: 'GetInfoBaseRawMaterialLevelEnumList',
  },
  {
    field: 'production_date',
    title: '生产日期',
    component: 'selectDate',
    type: 'date',
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    component: 'input',
    type: 'text',
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'weight_shortage_loss_rate',
    title: '欠重损耗率',
    component: 'input',
    type: 'float',
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_remark',
    title: '原料备注',
    component: 'input',
    type: 'text',
  },
  {
    field: 'whole_piece_count',
    title: '整件采购件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'whole_piece_weight',
    title: '整件件重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'bulk_piece_count',
    title: '散件采购件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'bulk_weight',
    title: '散件采购数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'deduction_weight',
    title: '扣款数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'avg_actl_gross_weight',
    title: '实际毛重(kg)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'avg_actl_tare',
    title: '实际皮重(kg)',
    component: 'input',
    type: 'float',
    digits: 4,
  },
  {
    field: 'other_price',
    title: '其他金额(kg)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
    digits: 2,
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'textarea',
  },
])

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['raw_material_code'].includes(column.field))
        return '合计'

      if (
        [
          'whole_piece_count',
          'whole_piece_weight',
          'bulk_piece_count',
          'bulk_weight',
          'total_weight',
          'deduction_weight',
          'avg_actl_gross_weight',
          'avg_actl_tare',
          'avg_actl_net_weight',
          'avg_actl_weight_shortage',
          'actl_total_net_weight',
          'actl_total_weight_shortage',
          'weight_shortage_rate',
          'other_price',
          'total_price',
        ].includes(column.field)
      )
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}
function handBulkClose() {
  bulkShow.value = false
}

let oneStatus = true
function clearData() {
  if (!oneStatus) {
    state.tableData?.map((item) => {
      item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
      item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
    })
  }
  oneStatus = false
  bulkList[0].query = { sale_system_id: state.form.sale_system_id }
  bulkSetting.value.customer_id = ''
}

// #region 生产通知单
const showProduct = ref(false)
const selectAssociationsRow = ref()
const selectAssociationsBatch = ref() // 是否批量选择

// 点击打开选择生产通知单号
function openAssociations(row: any, rowIndex: number) {
  showProduct.value = true
  selectAssociationsRow.value = { ...row, rowIndex }
  selectAssociationsBatch.value = false
}
// 点击打开选择生产通知单号-批量操作
function openAssociationsSlot() {
  showProduct.value = true
  selectAssociationsBatch.value = true
}
// 清除选择生产通知单号
function clearProductionOrderNum(row: any) {
  row.production_order_num = ''
  row.grey_fabric_code = ''
  row.grey_fabric_name = ''
  row.grey_fabric_id = 0
  row.production_date = ''
}

// 确定选择生产通知单
function getNotificationOrder(val: any) {
  // 批量设置
  if (selectAssociationsBatch.value) {
    multipleSelection.value?.map((selectItem: any) => {
      state.tableData.forEach((item: any) => {
        // 匹配一致的数据
        if (selectItem._X_ROW_KEY_TABLE === item._X_ROW_KEY_TABLE) {
          item.production_order_num = val.order_no
          item.grey_fabric_code = val.grey_fabric_code
          item.grey_fabric_name = val.grey_fabric_name
          item.grey_fabric_id = val.grey_fabric_id
          item.production_date = val.notify_date
        }
      })
    })

    bulkShow.value = false
    showProduct.value = false
    ElMessage.success('设置成功')
  }
  else {
    // 单个选择
    showProduct.value = false
    const { rowIndex } = selectAssociationsRow.value
    state.tableData[rowIndex].production_order_num = val.order_no
    state.tableData[rowIndex].grey_fabric_code = val.grey_fabric_code
    state.tableData[rowIndex].grey_fabric_name = val.grey_fabric_name
    state.tableData[rowIndex].grey_fabric_id = val.grey_fabric_id
    state.tableData[rowIndex].production_date = val.notify_date
  }
}

// #endregion

const tableConfig = ref({
  showOperate: true,
  operateWidth: 100,
  bulkSubmit,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod,
  showSpanHeader: true,
  fieldApiKey: 'RawMaterialPurchaseReceiptEdit',
})

function handleChangeCustomer(val: any, row: any) {
  row.customer_name = val.name
  row.customer_code = val.code
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="营销体系名称:" required>
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @change-value="row => getSaleSystemInfo(row)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="供应商名称:" required>
            <template #content>
              <el-form-item prop="supplier_id">
                <SelectBusinessDialog
                  v-model="state.form.supplier_id"
                  api="BusinessUnitSupplierEnumlist"
                  :query="{
                    unit_type_id: `${BusinessUnitIdEnum.rawMaterial},${BusinessUnitIdEnum.dyeingMill}`,
                  }"
                  :default-value="{
                    id: dataDetail.supplier_id,
                    name: dataDetail.supplier_name,
                  }"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货单位名称:" required>
            <template #content>
              <el-form-item prop="receipt_unit_id">
                <SelectBusinessDialog
                  v-model="state.form.receipt_unit_id"
                  api="GetBusinessUnitListApi"
                  :query="{ unit_type_id: `${BusinessUnitIdEnum.knittingFactory},${BusinessUnitIdEnum.dyeingMill}` }"
                  :default-value="{
                    id: dataDetail.receipt_unit_id,
                    name: dataDetail.receipt_unit_name,
                  }"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货日期:" required>
            <template #content>
              <el-form-item prop="receipt_date">
                <SelectDate v-model="state.form.receipt_date" type="date" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="仓管员:">
            <template #content>
              <el-form-item prop="warehouse_manager_id">
                <SelectComponents
                  v-model="state.form.warehouse_manager_id"
                  api="Adminemployeelist"
                  :query="{ duty: EmployeeType.warehouseManager }"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.warehouse_manager_name = row?.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="凭证号:">
            <template #content>
              <el-form-item prop="voucher_num">
                <el-input v-model="state.form.voucher_num" placeholder="凭证号" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="userName">
                <el-input v-model="state.form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据资料添加
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAddPurchasing">
        根据采购单添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectCustomerDialog
          v-model="row.customer_id"
          field="name" :sale_system_id="state.form.sale_system_id" :default-value="{ id: row.customer_id,
                                                                                     name: row.customer_name,
                                                                                     code: row.customer_code }" @change-value="handleChangeCustomer($event, row)" @on-input="val => (row.customer_name = val)"
        />
      </template>
      <template #brand="{ row }">
        <vxe-input v-model="row.brand" size="mini" maxlength="200" />
      </template>
      <template #color_scheme="{ row }">
        <SelectDialog
          v-model="row.color_id"
          :query="{
            raw_matl_id: row.raw_material_id,
            name: row.color_name,
          }"
          api="GetRawMaterialColor"
          label-field="name"
          :column-list="[
            {
              field: 'name',
              title: '原料颜色',
              minWidth: 100,
            },
            {
              field: 'code',
              title: '原料色号',
              minWidth: 100,
            },
          ]"
          :table-column="[
            {
              field: 'name',
              title: '原料颜色',
              defaultData: {
                id: row?.color_id,
                name: row?.name,
                code: row?.code,
              },
            },
          ]"
          @on-input="(val) => row.color_name = val"
        />
      </template>
      <template #measurement_unit_id="{ row }">
        <SelectComponents v-model="row.measurement_unit_id" disabled api="getInfoBaseMeasurementUnitEnumList" label-field="name" value-field="id" clearable />
      </template>
      <template #dyelot_number="{ row }">
        <vxe-input v-model="row.dyelot_number" clearable />
      </template>
      <template #batch_num="{ row }">
        <vxe-input v-model="row.batch_num" size="mini" />
      </template>
      <template #production_order_num="{ row, rowIndex }">
        <el-tag v-if="row.production_order_num" closable @close="clearProductionOrderNum(row)">
          {{ row.production_order_num }}
        </el-tag>
        <el-link v-else type="primary" @click="openAssociations(row, rowIndex)">
          关联
        </el-link>
      </template>
      <!--      坯布编号 -->
      <template #grey_fabric_code="{ row }">
        <SelectDialog
          v-model="row.grey_fabric_id"
          :disabled="!!row.production_order_num"
          :query="{ code: row.grey_fabric_code }"
          :table-list="data.list"
          :label-name="row.grey_fabric_code"
          :column-list="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :table-column="[
            {
              field: 'code',
              title: '坯布编号',
              minWidth: 100,
            },
          ]"
          api="GetGreyFabricInfoListUseByOthersMenu"
          label-field="code"
          @on-input="val => (row.grey_fabric_code = val)"
          @change-value="val => getFabric(val, row)"
        />
      </template>
      <!--      坯布名称 -->
      <template #grey_fabric_name="{ row }">
        <SelectDialog
          v-model="row.grey_fabric_id"
          :disabled="!!row.production_order_num"
          :table-list="data.list"
          api="GetGreyFabricInfoListUseByOthersMenu"
          :query="{ name: row.grey_fabric_name }"
          :label-name="row.grey_fabric_name"
          :column-list="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :table-column="[
            {
              field: 'name',
              title: '坯布名称',
            },
          ]"
          @change-value="val => getFabric(val, row)"
          @on-input="val => (row.grey_fabric_name = val)"
        />
      </template>
      <template #level="{ row }">
        <SelectComponents v-model="row.level_id" size="small" api="GetInfoBaseRawMaterialLevelEnumList" label-field="name" value-field="id" />
      </template>
      <template #production_date="{ row }">
        <SelectDate v-model="row.production_date" size="small" type="date" />
      </template>
      <template #cotton_origin="{ row }">
        <vxe-input v-model="row.cotton_origin" size="mini" />
      </template>
      <template #yarn_origin="{ row }">
        <vxe-input v-model="row.yarn_origin" size="mini" />
      </template>
      <template #carton_num="{ row }">
        <vxe-input v-model="row.carton_num" size="mini" maxlength="200" />
      </template>
      <template #fapiao_num="{ row }">
        <vxe-input v-model="row.fapiao_num" size="mini" maxlength="200" />
      </template>
      <template #weight_shortage_loss_rate="{ row }">
        <vxe-input v-model="row.weight_shortage_loss_rate" type="float" :min="0" size="mini" />
      </template>
      <template #spinning_type="{ row }">
        <vxe-input v-model="row.spinning_type" maxlength="200" :min="0" size="mini" />
      </template>
      <template #raw_material_remark="{ row }">
        <vxe-input v-model="row.raw_material_remark" :min="0" size="mini" />
      </template>
      <template #whole_piece_count="{ row }">
        <vxe-input v-model="row.whole_piece_count" maxlength="200" type="float" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #whole_piece_weight="{ row }">
        <vxe-input v-model="row.whole_piece_weight" maxlength="200" type="float" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #bulk_piece_count="{ row }">
        <vxe-input v-model="row.bulk_piece_count" maxlength="200" type="float" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #bulk_weight="{ row }">
        <vxe-input v-model="row.bulk_weight" maxlength="200" type="float" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #total_weight="{ row }">
        {{ row.total_weight }}
      </template>
      <template #deduction_weight="{ row }">
        <vxe-input v-model="row.deduction_weight" maxlength="200" type="float" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #avg_actl_gross_weight="{ row }">
        <vxe-input v-model="row.avg_actl_gross_weight" maxlength="200" type="float" :digits="4" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #avg_actl_tare="{ row }">
        <vxe-input v-model="row.avg_actl_tare" maxlength="200" type="float" :digits="4" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #avg_actl_net_weight="{ row }">
        {{ row.avg_actl_net_weight }}
      </template>
      <template #avg_actl_weight_shortage="{ row }">
        {{ row.avg_actl_weight_shortage }}
      </template>
      <template #actl_total_net_weight="{ row }">
        {{ row.actl_total_net_weight }}
      </template>
      <template #actl_total_weight_shortage="{ row }">
        {{ row.actl_total_weight_shortage }}
      </template>
      <template #weight_shortage_rate="{ row }">
        {{ row.weight_shortage_rate }}
      </template>
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" maxlength="200" type="float" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" maxlength="200" type="float" :digits="2" :min="0" size="mini" @blur="changeData(row)" />
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" maxlength="200" size="mini" />
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDel(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <SelectInformation v-model="showAdd" @submit="onSubmit" />
  <SelectPurchasing v-model="showPurchasing" :sale_system_id="state.form.sale_system_id" :supplier_id="state.form.supplier_id" @submit="onSubmitPurchasing" />
  <!-- 选择生产通知单 -->
  <SelectProductionNotice v-model="showProduct" :order_no="selectAssociationsRow?.production_order_num" @submit="getNotificationOrder" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #production_order_num>
      <el-link type="primary" @click="openAssociationsSlot">
        关联
      </el-link>
    </template>
  </BulkSetting>
</template>

<style lang="scss" scoped></style>
