<script lang="ts" setup>
import { h, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElLink, ElMessage, ElMessageBox } from 'element-plus'
import RollInfo from './components/rollInfo.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import { dnfcancel, dnfdetail, dnfpass, dnfreject, dnfvoid } from '@/api/dyeingPay'
import { orderStatusConfirmBox } from '@/common/util'
import { formatDate, formatLengthDiv, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import Table from '@/components/Table.vue'
import { getPayOrderWriteOffList } from '@/api/actualPay'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import { OrderAuditStatusEnum } from '@/enum/orderEnum'
import useRouterList from '@/use/useRouterList'

const rourte = useRoute()

const { fetchData, data, success, msg } = dnfdetail()

const { fetchData: verificationFetch, data: verificationData } = getPayOrderWriteOffList()
async function getData() {
  await fetchData({ id: rourte.query.id })
  if (!success.value)
    return ElMessage.error(msg.value)
}
onMounted(async () => {
  getData()
  await verificationFetch({ id: rourte.query.id })
})

const tableConfig = ref({
  showSlotNums: true,
  height: 400,
  fieldApiKey: fieldApiKeyList.DyeingPayDetail,
  showSpanHeader: true,
  // cellDBLClickEvent: (val: any) => cellDBLClickEvent(val),
  footerMethod: (val: any) => FooterMethod(val),
})

const tableConfig_record = ref({
  showSlotNums: true,
  fieldApiKey: fieldApiKeyList.DyeingPayOrderWriteOffList,
  height: 400,
})

// const tableConfig_use = ref({
//   showSlotNums: true,
//   height: 400,
// })
//
// const tableConfig_return = ref({
//   showSlotNums: true,
//   height: 400,
// })

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, column.property))}`
      else if (['gf_roll', 'piece_count'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, column.property))}`
      else if (['gf_weight', 'weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property))}`

      return null
    }),
  ]
}
const router = useRouter()

async function updateStatus(audit_status: OrderAuditStatusEnum) {
  const id: any = Number(rourte.query.id)
  if (audit_status === OrderAuditStatusEnum.Cancellation)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: dnfvoid })

  if (audit_status === OrderAuditStatusEnum.TurnDown)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: dnfreject })

  if (audit_status === OrderAuditStatusEnum.Audited) {
    try {
      await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: dnfpass })
    }
    catch (e) {
      // await checkAuditable()
      if (e.code === 70125) {
        ElMessageBox({
          customStyle: '--el-messagebox-width: 500px',
          title: '',
          message: h('div', { class: 'pl-6' }, [
            h('h2', { class: 'flex items-center mb-2 text-base' }, ['该成品使用的坯布成本未确定，请先维护成本，', h(ElLink, { underline: false, type: 'primary', onClick: () => {
              router.push({
                name: 'DyeingFactoryTable',
              })
            } }, '前往>')]),
          ]),
        })
      }
      else if (e.code === 70124) {
        ElMessageBox({
          customStyle: '--el-messagebox-width: 500px',
          title: '',
          message: h('div', { class: 'pl-6' }, [
            h('h2', { class: 'flex items-center mb-2 text-base' }, ['该成品使用的坯布成本未确定，请先维护成本，', h(ElLink, { underline: false, type: 'primary', onClick: () => {
              router.push({
                name: 'RawStockTable',
              })
            } }, '前往>')]),
          ]),
        })
      }
    }
  }

  if (audit_status === OrderAuditStatusEnum.Pending)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' }, api: dnfcancel })

  fetchData({ id })
}
// const { fetchData: detailFetch, data: detalData } = notice_orderdetail()

// const activeName = ref('first')

// async function cellDBLClickEvent(val: any) {
//   state.useList = []
//   await detailFetch({ id: val.row.dnf_order_id })
//   if (detalData.value) {
//     let arr = []
//     state.useList = detalData.value.items[0]?.use_fabric || []
//     arr = state.useList?.map((item: any) => {
//       return {
//         product_code: detalData.value.items[0]?.code,
//         product_name: detalData.value.items[0]?.name,
//         color_no: detalData.value.items[0]?.color_no,
//         color_name: detalData.value.items[0]?.color_name,
//         dyelot: detalData.value.items[0]?.dyelot,
//         dye_piece_count: detalData.value.items[0]?.piece_count,
//         dye_weight: detalData.value.items[0]?.weight,
//
//         ...item?.gf_stock_info,
//         ...item,
//       }
//     })
//
//     state.useList = arr
//     state.returnList = arr
//   }
// }

const columnList = ref([

  {
    field: 'order_no',
    title: '单号',
    minWidth: 100,
  },
  {
    field: 'dnf_order_no',
    soltName: 'dnf_order_no',
    title: '染整通知单号',
    minWidth: 100,
  },
  {
    field: 'order_type_name',
    title: '单据类型',
    minWidth: 100,
  },
  {
    field: 'dnf_date',
    title: '染整日期',
    minWidth: 100,
  },
  {
    field: 'enter_date',
    title: '进仓日期',
    minWidth: 100,
  },
  {
    field: 'product_code',
    title: '成品编号',
    minWidth: 100,
  },
  {
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    field: 'color_code',
    title: '色号',
    minWidth: 100,
  },
  {
    field: 'color',
    title: '颜色',
    minWidth: 100,
  },
  {
    field: 'dye_factory_color_code',
    title: '染厂色号',
    minWidth: 100,
  },
  {
    field: 'dyelot',
    title: '对色缸号',
    minWidth: 100,
  },
  {
    field: 'dnf_craft',
    title: '染整工艺',
    minWidth: 100,
  },

  {
    field: 'unit',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '空差',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'total_length',
    title: '辅助数量',
    minWidth: 100,
    isLength: true,
  },
  {
    field: 'gf_roll',
    title: '用坯匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'gf_weight',
    title: '用坯数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'piece_count',
    title: '计产匹数',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'settle_weight',
    title: '计产数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'length',
    title: '计产辅助数量',
    minWidth: 100,
    isLength: true,
  },
  {
    title: '染费',
    childrenList: [
      {
        field: 'dnf_unit_price',
        title: '单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'dyeing_price',
        title: '金额',
        minWidth: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '后整',
    childrenList: [
      {
        field: 'finishing_unit_price',
        title: '单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'finishing_price',
        title: '金额',
        minWidth: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '纸筒胶带',
    childrenList: [
      {
        field: 'paper_tube_unit_price',
        title: '纸筒单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'paper_tube_price',
        title: '纸筒金额',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'plastic_bag_unit_price',
        title: '胶袋单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'plastics_bag_price',
        title: '胶袋金额',
        minWidth: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '应付信息',
    childrenList: [
      {
        field: 'other_price',
        title: '其他应付',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'price',
        title: '总金额',
        minWidth: 100,
        isPrice: true,
      },
    ],
  },
  {
    title: '其他',
    childrenList: [
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
      {
        field: 'net_weight_price',
        title: '净重单价',
        isWeight: true,
        minWidth: 100,
      },
      {
        field: 'buoyant_weight_price',
        isWeight: true,
        title: '毛重单价',
        minWidth: 100,
      },
    ],
  },
])

const columnList_record = ref([
  {
    field: 'actually_pay_order_no',
    title: '实付款单号',
    minWidth: 100,
  },
  {
    field: 'voucher_number',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    field: 'settle_type_name',
    title: '结算方式',
    minWidth: 100,
  },
  {
    field: 'offset_price',
    title: '优惠金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'discount_price',
    title: '折扣金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'deduction_price',
    title: '扣款金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'pay_price',
    title: '实付金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'write_off_price',
    title: '核销金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'actually_pay_data',
    title: '实付日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    minWidth: 100,
    isDate: true,
  },
])
const routerList = useRouterList()
function handleClickLink(row: any) {
  routerList.push({
    name: 'DyeingNoticeDetail',
    query: { id: row.dnf_order_id },
    close_self: false,
  })
}
</script>

<template>
  <StatusColumn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.status"
    :status_name="data.status_name"
    permission_wait_key="DyeingPayWait"
    permission_reject_key="DyeingPayReject"
    permission_pass_key="DyeingPayPass"
    permission_cancel_key="DyeingPayCancel"
    permission_edit_key="DyeingPayEdit"
    edit_router_name="DyeingPayEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="源单类型:">
        <template #content>
          {{ data?.src_order_type_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="源单单号:">
        <template #content>
          {{ data?.src_order_no }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="凭证单号:">
        <template #content>
          {{ data?.voucher_num }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="供方名称:">
        <template #content>
          {{ data?.supplier_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="应付日期:">
        <template #content>
          {{ formatDate(data?.pay_date) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="经手人:">
        <template #content>
          {{ data?.handler_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据备注:" copies="2">
        <template #content>
          {{ data?.remark }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard v-if="data.status === 2" title="结算信息" :tool-bar="false" class="mt-[5px]">
    <el-descriptions :column="4" border size="small">
      <el-descriptions-item label="应付总金额">
        ￥{{ formatPriceDiv(data?.total_price) }}
      </el-descriptions-item>
      <el-descriptions-item label="优惠金额">
        ￥{{ formatPriceDiv(data?.offset_price) }}
      </el-descriptions-item>
      <el-descriptions-item label="折扣金额">
        ￥{{ formatPriceDiv(data?.discount_price) }}
      </el-descriptions-item>
      <el-descriptions-item label="扣款金额">
        ￥{{ formatPriceDiv(data?.reduce_price) }}
      </el-descriptions-item>
      <el-descriptions-item label="已付金额">
        ￥{{ formatPriceDiv(data?.paid_price) }}
      </el-descriptions-item>
      <el-descriptions-item label="未付金额">
        ￥{{ formatPriceDiv(data?.unpaid_price) }}
      </el-descriptions-item>
    </el-descriptions>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]" tool-bar>
    <Table :config="tableConfig" :table-list="data?.items" :column-list="columnList">
      <template #dnf_order_no="{ row }">
        <ElLink type="primary" :underline="false" @click="handleClickLink(row)">
          {{ row.dnf_order_no }}
        </ElLink>
      </template>
    </Table>
  </FildCard>
  <!--  <FildCard title="" :tool-bar="false" class="mt-[5px]"> -->
  <!--    <el-tabs v-model="activeName" class="demo-tabs"> -->
  <!--      <el-tab-pane label="用坯信息" name="first"> -->
  <!--        <table :config="tableConfig_use" :table-list="state.useList" :column-list="columnList_use" /> -->
  <!--      </el-tab-pane> -->
  <!--      <el-tab-pane label="返布用坯信息" name="second"> -->
  <!--        <Table :config="tableConfig_return" :table-list="state.returnList" :column-list="columnList_return" /> -->
  <!--      </el-tab-pane> -->
  <!--    </el-tabs> -->
  <!--  </FildCard> -->
  <RollInfo
    :dnf-charging-method="data.dnf_charging_method"
    :parent-list="data.items"
    :src-id="data.src_order_id"
    type="detail"
  />
  <FildCard v-if="data?.status === 2" title="核销记录" class="mt-[5px]" tool-bar>
    <Table :config="tableConfig_record" :table-list="verificationData?.list" :column-list="columnList_record" />
  </FildCard>
</template>

<style></style>
