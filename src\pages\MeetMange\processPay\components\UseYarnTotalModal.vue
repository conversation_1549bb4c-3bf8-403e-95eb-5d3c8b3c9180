<script setup lang="ts">
import { computed, reactive } from 'vue'
import currency from 'currency.js'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { formatHashTag, formatWeightMul } from '@/common/format'
import {
  AutoUpdateBuoyantWeightPrice,
  UpdateBuoyantWeightPrice,
  getPRCUseYarnDataUseShouldPayListByItemID,
  getPRTUseYarnDataUseShouldPayListByItemID,
} from '@/api/processPay'
import { processDataOut } from '@/common/handBinary'

const emit = defineEmits<{
  (e: 'handleSure', data: any[]): void
  (e: 'handleUpdateSuccess'): void
  (e: 'handleYarnInfoSure', params: { row: any, rowIndex: number }): void
}>()

const state = reactive({
  showModal: false,
  id: 0,
  data: {},
  row: {},
  isEdit: true,
  tableData: [],
})

// 表格配置
const tableConfig = computed(() => ({
  showSlotNums: true,
  height: '100%',
  footerMethod,
}))

// 列定义
const columnList = computed(() => [
  {
    field: 'unit_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  // {
  //   field: 'customer_name',
  //   title: '所属客户',
  //   minWidth: 150,
  // },
  {
    field: 'brand',
    title: '纱牌',
    minWidth: 100,
  },
  {
    field: 'batch_num',
    title: '纱批',
    minWidth: 100,
  },
  // {
  //   field: 'raw_material_code',
  //   title: '原料编号',
  //   minWidth: 100,
  // },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 140,
    soltName: 'raw_material_name',
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  // {
  //   field: 'measurement_unit_name',
  //   title: '单位',
  //   minWidth: 150,
  // },
  // {
  //   field: 'level_name',
  //   title: '原料等级',
  //   minWidth: 150,
  // },
  // {
  //   field: 'raw_remark',
  //   title: '原料备注',
  //   minWidth: 150,
  // },
  // {
  //   field: 'production_date',
  //   title: '生产日期',
  //   minWidth: 150,
  //   is_date: true,
  // },
  // {
  //   field: 'spinning_type',
  //   title: '纺纱类型',
  //   minWidth: 150,
  // },
  // {
  //   field: 'cotton_origin',
  //   title: '棉花产地',
  //   minWidth: 150,
  // },
  // {
  //   field: 'carton_num',
  //   title: '装箱单号',
  //   minWidth: 150,
  // },
  // {
  //   field: 'fapiao_num',
  //   title: '发票号',
  //   minWidth: 150,
  // },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
    soltName: 'use_yarn_quantity',
  },

  {
    field: 'buoyant_weight_price',
    title: '原料单价',
    minWidth: 100,
    show: state.data?.src_order_type === 3,
    soltName: 'buoyant_weight_price',
  },

  {
    show: state.data?.src_order_type === 3,
    field: 'rm_cost_price',
    title: '原料金额',
    minWidth: 100,
  },
  // {
  //   field: 'remark',
  //   title: '备注',
  //   minWidth: 100,
  //   soltName: 'remark',
  // },
])

// 表格底部汇总方法
function footerMethod({ columns, data }) {
  return [
    columns.map((column, _columnIndex) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity', 'rm_cost_price'].includes(column.property))
        return `${sumNum(data, column.property)}`

      return null
    }),
  ]
}

// 辅助函数：计算数值之和
function sumNum(data, field) {
  if (!data || data.length === 0)
    return 0
  return data.reduce((sum, item) => {
    const value = Number(item[field] || 0)
    return sum + value
  }, 0).toFixed(3)
}

// 取消按钮
// function handCancel() {
//   state.showModal = false
// }
//
// // 确认按钮
// function handleSure() {
//   emit('handleSure', state.tableData)
//   state.showModal = false
// }

// 暴露方法给父组件
defineExpose({
  state,
})
const { fetchData, msg, success } = AutoUpdateBuoyantWeightPrice()

const { fetchData: useFetch, data: useData, success: useSuccess, msg: useMsg } = getPRCUseYarnDataUseShouldPayListByItemID()

const { fetchData: returnFetch, data: returnData, success: returnSuccess, msg: returnMsg } = getPRTUseYarnDataUseShouldPayListByItemID()
async function fetchSubTable() {
  if (state.data?.src_order_type === 3) {
    await useFetch({ id: state.row?.src_id })
    if (!useSuccess.value)
      return ElMessage.error(useMsg.value)

    state.tableData = processDataOut(useData?.value?.use_data_items.filter(item => item.use_yarn_id === state.row.id) || [])
  }
  else {
    await returnFetch({ id: state.row?.src_id })
    if (!returnSuccess.value)
      return ElMessage.error(returnMsg.value)

    state.tableData = processDataOut(returnData?.value?.use_data_items.filter(item => item.use_yarn_id === state.row.id) || [])
  }
}
// 按原料库存更新单价
async function handleUpdatePrice() {
  await fetchData({
    use_yarn_id: state.id,
  })
  if (!success.value)
    return ElMessage.error(msg.value)
  fetchSubTable()
  emit('handleUpdateSuccess')
}
const { fetchData: updateApi, msg: updateMsg, success: updateSuccess } = UpdateBuoyantWeightPrice()
async function handChange({ value }, row) {
  if (!Number(value))
    return ElMessage.error(`${state.row.grey_fabric_full_name}的${formatHashTag(row.raw_material_code, row.raw_material_name)}的原料单价不可为0或空`)

  await updateApi({
    buoyant_weight_price: formatWeightMul(value),
    use_yarn_item_id: row.id,
  })
  if (!updateSuccess.value)
    return ElMessage.error(updateMsg.value)
  ElMessage.success('修改成功')
  row.rm_cost_price = currency(row.use_yarn_quantity).multiply(value).value
  fetchSubTable()
  emit('handleUpdateSuccess')
}
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="用纱情况选择" width="80vw" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div v-if="state.isEdit && state.data?.src_order_type === 3" class="flex items-center">
        <el-link type="primary" :underline="false" @click="handleUpdatePrice">
          按原料库存更新单价
        </el-link>
      </div>
      <div class="flex flex-col h-full overflow-hidden">
        <Table :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
          <template #raw_material_name="{ row }">
            {{ formatHashTag(row.raw_material_code, row.raw_material_name) }}
          </template>
          <template #use_yarn_quantity="{ row }">
            {{ row.use_yarn_quantity }} {{ row.measurement_unit_name }}
          </template>
          <template #buoyant_weight_price="{ row }">
            <vxe-input v-if="state.isEdit" v-model="row.buoyant_weight_price" placeholder="请输入" :min="0" type="float" @blur="val => handChange(val, row)">
              <template #prefix>
                ￥
              </template>
            </vxe-input>
            <template v-else>
              ￥{{ row.buoyant_weight_price }}
            </template>
          </template>
        </Table>
      </div>
    </div>
  </vxe-modal>
</template>

<style scoped>

</style>
