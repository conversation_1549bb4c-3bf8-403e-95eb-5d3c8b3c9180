<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import {
  getWeightItemList,
  updateAuditStatusCancel,
  updateAuditStatusPass,
  updateAuditStatusReject,
  updateAuditStatusWait,
  updateget,
} from '@/api/saleDeliver'
import {
  formatDate,
  formatLengthDiv,
  formatPriceDiv,
  formatWeightDiv,
  sumNum,
} from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'

const state = reactive<any>({
  tableList: [],
  fabricList: [],
  isShow: false,
})
const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeShouldCollectOrder,
  dataType: PrintDataType.Product,
})
const tableConfig = ref({
  showSlotNums: true,
  fieldApiKey: 'SaleDeliverDetail',
  //   showOperate: true,
  //   operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showSpanHeader: true,
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val),
})

const tableConfig_fabric = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
})

const tableConfig_record = ref({
  showSlotNums: true,
  height: 400,
})

const { fetchData: getFetch, data } = updateget()

const { fetchData: getXima, data: ximaData } = getWeightItemList()

onMounted(() => {
  getInfomation()
})

const activeProduct = ref<any>({})
async function cellDBLClickEvent(val: any) {
  await getXima({ detail_id: val.row.id })
  data.value.xima = ximaData.value.list
  activeProduct.value = val.row
  state.isShow = true
}

const route = useRoute()

async function getInfomation() {
  await getFetch({ id: route.query.id })
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['return_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'return_roll') as any)}`

      if (['return_length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'return_length') as any)}`

      if (['return_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'return_weight') as any)}`

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['weight_error'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight_error') as any)}`

      if (['actually_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_weight') as any)}`

      if (['settle_error_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_error_weight') as any)}`

      if (['settle_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_weight') as any)}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length') as any)}`

      if (['settle_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'settle_price') as any)}`

      return null
    }),
  ]
}

async function updateStatus(audit_status: number) {
  const id: any = route?.query?.id?.toString()
  if (audit_status === 4) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateAuditStatusCancel,
    })
  }
  if (audit_status === 3) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateAuditStatusReject,
    })
  }
  if (audit_status === 2) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateAuditStatusPass,
    })
  }
  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: {
        desc: '点击消审后订单将变为待审核状态',
        title: '是否消审该订单？',
      },
      api: updateAuditStatusWait,
    })
  }
  getInfomation()
}

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'sale_order_no',
        title: '成品销售单号',
        minWidth: 150,
      },
      {
        field: 'fpm_sale_out_order_no',
        title: '成品销售出仓单号',
        minWidth: 150,
      },
      {
        field: 'arrange_order_no',
        title: '配布单号',
        minWidth: 150,
      },
      {
        field: 'warehouse_name',
        title: '仓库名称',
        minWidth: 100,
      },
      {
        field: 'product_code',
        title: '成品编号',
        minWidth: 100,
      },
      {
        field: 'product_name',
        title: '成品名称',
        minWidth: 100,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        field: 'product_color_code',
        title: '色号',
        minWidth: 100,
      },
      {
        field: 'product_color_name',
        title: '颜色',
        minWidth: 100,
      },
      {
        field: 'dyelot_number',
        title: '染厂缸号',
        minWidth: 100,
      },
      {
        field: 'product_craft',
        title: '成品工艺',
        minWidth: 100,
      },
      {
        field: 'product_level_name',
        title: '成品等级',
        minWidth: 100,
      },
      {
        field: 'product_ingredient',
        title: '成品成分',
        minWidth: 100,
      },
      {
        field: 'product_remark',
        title: '成品备注',
        minWidth: 100,
      },
    ],
  },
  {
    title: '出仓数量',
    childrenList: [
      {
        field: 'roll',
        title: '匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'weight',
        title: '数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'weight_error',
        title: '码单空差',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'actually_weight',
        title: '码单数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'settle_error_weight',
        title: '结算空差',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'settle_weight',
        title: '结算数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'length',
        title: '辅助数量',
        minWidth: 100,
        isLength: true,
      },
      {
        field: 'warehouse_out_remark',
        title: '出仓备注',
        minWidth: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        minWidth: 70,
      },
    ],
  },
  {
    title: '结算单位',
    childrenList: [
      {
        field: 'auxiliary_unit_name',
        title: '结算单位',
        minWidth: 100,
      },
    ],
  },
  {
    title: '数量单价',
    childrenList: [
      {
        field: 'standard_sale_price',
        title: '销售报价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'sale_level_name',
        title: '优惠等级',
        minWidth: 100,
      },
      {
        field: 'offset_sale_price',
        title: '优惠单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'sale_price',
        title: '销售单价',
        minWidth: 100,
        isUnitPrice: true,
      },
    ],
  },
  {
    title: '辅助数量单价',
    childrenList: [
      {
        field: 'standard_length_cut_sale_price',
        title: '销售报价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'offset_length_cut_sale_price',
        title: '优惠单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        field: 'length_cut_sale_price',
        title: '销售单价',
        minWidth: 100,
        isUnitPrice: true,
      },
    ],
  },
  {
    title: '结算信息',
    childrenList: [
      {
        field: 'sale_tax_rate',
        title: '税率',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'other_price',
        title: '其他金额',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'settle_price',
        title: '结算金额',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
    ],
  },
  {
    title: '退货信息',
    childrenList: [
      {
        field: 'return_roll',
        title: '退货匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        field: 'return_weight',
        title: '退货数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        field: 'return_length',
        title: '退货辅助数量',
        minWidth: 100,
        isLength: true,
      },
    ],
  },
])

const columnList_fabric = ref([
  {
    field: 'roll',
    title: '匹数',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'warehouse_bin_name',
    title: '仓位',
    minWidth: 150,
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 100,
  },
  {
    field: 'base_unit_weight',
    title: '基本单位数量',
    minWidth: 150,
    isWeight: true,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒重量',
    minWidth: 150,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '码单空差',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'actually_weight',
    title: '码单数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'settle_error_weight',
    title: '结算空差',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'settle_weight',
    title: '结算数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'length',
    title: '辅助数量',
    minWidth: 100,
    isLength: true,
  },
  {
    field: 'dye_factory_dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 100,
  },
])

const columnList_record = ref([
  {
    field: 'actually_collect_order_no',
    title: '单据编号',
    minWidth: 150,
  },
  {
    field: 'settle_type_name',
    title: '结算类型',
    minWidth: 100,
  },
  {
    field: 'collect_price',
    title: '收款金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'offset_price',
    title: '优惠金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'discount_price',
    title: '折扣金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'deduction_price',
    title: '扣款金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'operator_name',
    title: '操作人',
    minWidth: 100,
  },
  {
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    field: 'actually_collect_date',
    title: '收款时间',
    minWidth: 100,
    is_date: true,
  },
])

const printList = ref<any[]>([])
async function handlePrintXiMa(row?: any) {
  printList.value = []
  printList.value.push({
    finish_product_craft: row?.finish_product_craft,
    density: row?.density,
    product_kind_name: row?.product_kind_name,
    bleach_name: row?.bleach_name,
    finish_product_width: row?.finish_product_width,
    finish_product_gram_weight: row?.finish_product_gram_weight_and_unit_name,
    weaving_organization_name: row?.weaving_organization_name,
    product_name: activeProduct.value?.product_name,
    product_code: activeProduct.value?.product_code,
    yarn_count: row?.yarn_count,
    finish_product_ingredient: activeProduct.value?.product_ingredient,
    // weight: row.settle_weight,
    weight: row.actually_weight,
    dyelot_number: row?.dye_factory_dyelot_number,
    product_color_code: activeProduct.value?.product_color_code,
    measurement_unit_name: row?.measurement_unit_name,
    product_color_name: activeProduct.value?.product_color_name,
    qr_code: row?.qr_code, // 二维码
    bar_code: row?.bar_code, // 条形码
    volume_number: row?.volume_number, // 匹号
    print_date: row?.print_date, // 打印时间
  })
}
</script>

<template>
  <StatusColumn
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="SaleDeliverWait"
    permission_reject_key="SaleDeliverReject"
    permission_pass_key="SaleDeliverPass"
    permission_cancel_key="SaleDeliverCancel"
    permission_edit_key="SaleDeliverEdit"
    edit_router_name="SaleDeliverEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  >
    <template #print>
      <PrintPopoverBtn
        :id="route.query.id"
        api="updateget"
        :options="options"
        :query="{ with_all_weight: true }"
      />
      <!--      <el-popover -->
      <!--        placement="left" -->
      <!--        title="选择打印" -->
      <!--        :width="180" -->
      <!--        trigger="hover" -->
      <!--      > -->
      <!--        <template #reference> -->
      <!--          <el-button type="primary">打印</el-button> -->
      <!--        </template> -->
      <!--        <PrintBtn -->
      <!--          type="finishedProductDeliveryNote" -->
      <!--          btnText="打印模板1" -->
      <!--          class="!ml-0" -->
      <!--          :tid="1691994119131392" -->
      <!--          api="updateget" -->
      <!--          :id="route.query.id" -->
      <!--          :query="{ with_all_weight: true }" -->
      <!--        /> -->
      <!--        <PrintBtn -->
      <!--          type="finishedProductDeliveryNote2" -->
      <!--          btnText="打印模板2" -->
      <!--          class="!ml-0" -->
      <!--          :tid="1707313537156096" -->
      <!--          api="updateget" -->
      <!--          :id="route.query.id" -->
      <!--          :query="{ with_all_weight: true }" -->
      <!--        /> -->
      <!--      </el-popover> -->
    </template>
  </StatusColumn>
  <FildCard title="基础信息" :tool-bar="false">
    <el-form :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            {{ data?.sale_system_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            {{ data?.voucher_number }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="送货日期:">
          <template #content>
            {{ formatDate(data?.order_time) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货标签:">
          <template #content>
            {{ data?.receive_tag }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户编号:">
          <template #content>
            {{ data?.customer_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            {{ data?.customer_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售群体:">
          <template #content>
            {{ data?.sale_group_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算类型:">
          <template #content>
            {{ data?.settle_type_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            {{ data?.sale_user_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单:">
          <template #content>
            {{ data?.sale_follower_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工厂名称:">
          <template #content>
            {{ data?.process_factory_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人:">
          <template #content>
            {{ data?.contacts }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话:">
          <template #content>
            {{ data?.receive_phone }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流公司:">
          <template #content>
            {{ data?.logistics_company_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流区域:">
          <template #content>
            {{ data?.logistics_area }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="邮费项目:">
          <template #content>
            {{ data?.postage_items_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址:">
          <template #content>
            {{ data?.receive_addr }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="data.is_with_tax_rate" label="税率:">
          <template #content>
            {{ formatPriceDiv(data?.tax_rate) }}%
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="是否含税:">
          <template #content>
            {{ data?.is_with_tax_rate ? "是" : "否" }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:">
          <template #content>
            {{ data?.sale_mode_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="内部备注:" copies="2">
          <template #content>
            {{ data?.internal_remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货备注:" copies="2">
          <template #content>
            {{ data?.send_product_remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            {{ data?.order_remark }}
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="成品信息" class="mt-[5px]">
    <!-- <template v-slot:right-top>
      <el-checkbox style="margin-right: 10px" v-model="state.isShow">显示细码</el-checkbox>
    </template> -->
    <Table
      :config="tableConfig"
      :table-list="data.items"
      :column-list="columnList"
    />
  </FildCard>
  <FildCard v-if="state.isShow" title="细码" :tool-bar="false" class="mt-[5px]">
    <Table
      :config="tableConfig_fabric"
      :table-list="ximaData.list"
      :column-list="columnList_fabric"
    >
      <template #operate="{ row }">
        <PrintPopoverBtn
          :print-type="PrintType.PrintTemplateTypeStock"
          :data-type="PrintDataType.Product"
          :list="printList"
          @on-print="handlePrintXiMa(row)"
        />
      </template>
    </Table>
  </FildCard>
  <FildCard
    v-if="data?.audit_status === 2"
    title="结算信息"
    :tool-bar="false"
    class="mt-[5px]"
  >
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="销售金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_settle_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="应收金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_should_collect_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="优惠金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_remove_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="折扣金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_discount_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="扣款金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_chargeback_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="已收金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_collected_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="未收金额:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_uncollect_money) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="累计欠款:">
        <template #content>
          ￥{{ formatPriceDiv(data?.total_arrears_amount) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="应收期限:">
        <template #content>
          ￥{{ formatDate(data?.payment_deadline) }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="逾期天数:">
        <template #content>
          {{ data?.liquidated_day }}天
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="核销记录" class="mt-[5px]">
    <Table
      :config="tableConfig_record"
      :table-list="data?.collect_records"
      :column-list="columnList_record"
    />
  </FildCard>
</template>

<style></style>
