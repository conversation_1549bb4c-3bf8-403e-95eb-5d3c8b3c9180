<!--
/**
 * 一个简单table表单二次封装
 * 如果有后续需求的话自行往这里加或者修改
 */
-->
<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, inject, nextTick, reactive, ref, watch } from 'vue'
import type {
  VxeTableEvents,
  VxeTableInstance,
  VxeTablePropTypes,
} from 'vxe-table'
import type {
  VxeButtonEvents,
} from 'vxe-pc-ui'
import { cloneDeep, isEqual } from 'lodash-es'
import type { ItemProps } from './TableField/item.vue'
import {
  GetListHabitsList,
  UpdateAllListHabits,
  UpdateListHabits,
} from '@/api/tableField'
import {
  formatDateTable,
  formatDateTime,
  formatLengthDivTable,
  formatPriceDivTable,
  formatRollDivTable,
  formatUnderweightRateDivTable,
  formatUnitPriceDivTable,
  formatWeightDivTable,
  priceDigit,
  unitDigit,
} from '@/common/format'
import { deleteById } from '@/common/util'
import TableFile from '@/components/TableField/index.vue'
import useSortTable from '@/use/useSortTable'
import { INIT_DATA } from '@/components/Table/constant'
import type { TableColumn, TableColumnType } from '@/components/Table/type'

defineOptions({
  name: 'XTable',
})

const props = withDefaults(defineProps<TableProps>(), {
  config: () => {
    return {
      loading: false,
      page: 1,
      size: 400,
      total: 0,
      showPagition: false,
      showFooterBtn: false,
      minHeight: 100,
      maxHeight: '',
      scrollY: { enabled: false },
      // table的高度
      height: '100%', // 外部传进，不然不生效
      // 是否显示多选
      showCheckBox: false,
      // 是否显示单选
      showRadio: false,
      // 单选的宽度
      radioWidth: '48',
      // 展示操作栏
      showOperate: false,
      // 操作栏的宽度
      operateWidth: '80',
      // 是否展示前面的序号
      showSlotNums: true,
      // 是否显示拖拽图标
      showSort: false,
      // 否需要表格展开或者收缩
      expand: false,
      // 展示或者收缩插槽的名字
      expandAll: false,
      expandName: '',
      // 是否展示需要已勾选、回选的数据
      backSelection: false,
      // 展示要勾选回去的数据源
      backSelectionArr: [],
      // 设置列颜色：格式{字段名:自定义类}
      rowColor: null,
      // 复选框配置
      checkboxConfig: {
        checkField: 'selected',
        highlight: true,
        reserve: true,
      },
      show_footer: true,
      pageLayout: 'total, sizes, prev, pager, next, jumper',
      filterStatus: true, // 全局配置是否显示表头筛选
      fieldApiKey: '', // 获取根据key获取字段
      showFixed: true, // 自定义字段组件是否设置固定
      needCellColor: true,
    }
  },
  tableList: () => [],
  columnList: [] as any,
})

export interface TableProps {
  config: TableColumnType // 表单的配置项
  tableList?: any[] // 表格数组
  columnList: TableColumn[] // 行/列数据
  filedShowStatus?: boolean // 显示自定义配置字段
}

const state = reactive<any>({
  selectChecks: [],
})

const newConfig = computed<TableColumnType>(() => {
  const mergeConfig = { ...INIT_DATA, ...props.config }
  return mergeConfig
})

const tableFieldRef = ref() // 字段调整组件
const tableFieldcolumn = ref<TableColumn[]>([])

const tableRef = ref<VxeTableInstance>()

const { rowDrop, columnDrop } = useSortTable()

const showField: any = inject('toolbar')

nextTick(() => {
  // 定时器等待节点渲染完成后调用拖拽函数
  setTimeout(() => {
    columnDrop(tableRef.value)
    rowDrop(tableRef.value, props.tableList)
    // 将表格和工具栏进行关联
    // if (xToolbar.value) {
    //   const $table = tableRef?.value
    //   const $toolbar = xToolbar?.value
    //   $table.connect($toolbar)
    // }
  }, 50)
})

function checkField() {
  // 检查props.columnList里的每一项是否具有field字段且是否唯一
  // 创建一个集合来存储唯一的字段名
  const fieldSet = new Set<string>()

  // 遍历 columnList 中的每一项
  for (const item of props.columnList) {
    // 检查该项是否具有 'field' 键
    if ('field' in item) {
      const fieldName = item.field || null
      if (fieldName === null && !item.childrenList) {
        console.error('每个 props.columnList 中的项都必须具有一个名为 "field" 的键, item.title：', item.title)
        continue
      }
      // 检查字段名是否唯一
      if (fieldSet.has(fieldName))
        console.error(`在 props.columnList 中发现重复的字段名 '${fieldName}'`)
      else
        fieldSet.add(fieldName)
      return
    }
    if (!item.childrenList)
      console.error('每个 props.columnList 中的项都必须具有一个名为 "field" 的键, item.title：', item.title)
  }
}

const { fetchData: fetchDataField, data: dataField } = GetListHabitsList()
const tableFieldcolumnUser = ref()
const saveDefaultColumnList = ref<ItemProps[]>([])
async function getFieldData() {
  checkField()

  const filtered: TableColumn[] = cloneDeep(props.columnList).filter(
    item => item != null,
  )
  if (!props.config?.fieldApiKey) {
    tableFieldcolumnUser.value = filtered
  }
  else {
    await fetchDataField({ key: props.config.fieldApiKey })
    tableFieldcolumnUser.value = JSON.parse(
      JSON.stringify(
        (dataField.value?.habits
          ? JSON.parse(dataField.value?.habits) || []
          : filtered) || [],
      ),
    )
    saveDefaultColumnList.value = JSON.parse(
      JSON.stringify(
        dataField.value?.habits_temp
          ? JSON.parse(dataField.value?.habits_temp) || []
          : [],
      ),
    )
  }
  // 判断有无合并函数传入，有的就合进去，暂不考虑其他函数
  filtered.forEach((item) => {
    if (!item?.filterMethod)
      return

    tableFieldcolumnUser.value.forEach((userItem: any) => {
      if (item.field === userItem.field)
        userItem.filterMethod = item.filterMethod
    })
  })
  tableFieldcolumn.value = tableFieldcolumnUser.value
}

watch(
  () => [props.config.fieldApiKey, props.columnList],
  () => {
    getFieldData()
  },
  { immediate: true, deep: true },
)

function handleFilterKeyupEnter(event, $panel: any) {
  if (event.$event.key === 'Enter')
    $panel.confirmFilter()
}

const headerCellClassName: VxeTablePropTypes.HeaderCellClassName = ({
  column,
}: {
  column: any
}) => {
  if (props.config?.rowColor) {
    const styleArr = props.config.rowColor
    const fields = Object.keys(styleArr)
    if (fields.includes(column.field))
      return styleArr[column.field]
  }
  return null
}

const rowClassName: VxeTablePropTypes.RowClassName = ({ rowIndex }) => {
  if (rowIndex === props.config.rowIndex)
    return 'hight-row'
  else
    return null
}

const cellClassName: VxeTablePropTypes.CellClassName = ({
  column,
}: {
  column: any
}) => {
  if (
    props.config?.rowColor
    && (props.config?.needCellColor
    || typeof props.config?.needCellColor === 'undefined')
  ) {
    const styleArr = props.config.rowColor
    const fields = Object.keys(styleArr)
    if (fields.includes(column.field))
      return styleArr[column.field]
  }
  return null
}
// 将回选的数据也丢回出去
function handAllSelect({ records, checked }: any) {
  if (checked)
    state.selectChecks = records
  else
    state.selectChecks = []

  props.config.handAllSelect?.({
    records,
    checked,
    selectCheck: state.selectChecks,
  })
}

function handleSelectionChange({ records, checked, row, rowIndex }: any) {
  if (checked)
    state.selectChecks.push(row)
  else
    state.selectChecks = deleteById('id', row?.id, state.selectChecks)

  props.config.handleSelectionChange?.({
    records,
    checked,
    row,
    selectCheck: state.selectChecks,
    rowIndex,
  })
}

const bulkShow = ref<boolean>(false)
const bulkField = ref<any>()
function openBulk(item: any) {
  bulkShow.value = true
  bulkField.value = item
}

// 导出选择项
const exportSelectEvent: VxeButtonEvents.Click = () => {
  const $table = tableRef.value
  if ($table) {
    if (
      !$table.getCheckboxRecords()
      || $table.getCheckboxRecords()?.length === 0
    )
      return ElMessage.error('请选择要导出的数据')

    $table.exportData({
      data: $table.getCheckboxRecords(),
    })
  }
}

const { fetchData: fetchDataUpdate, success: successUpdate }
  = UpdateListHabits()
const { fetchData: fetchDataAllUpdate, success: successAllUpdate }
  = UpdateAllListHabits()
async function getSubmit(data: any[]) {
  if (!props.config.fieldApiKey)
    return ElMessage.warning('请先设置key')

  await fetchDataUpdate({
    key: props.config.fieldApiKey,
    habits: JSON.stringify(data || ''),
    habits_temp: JSON.stringify(props.columnList || ''),
  })
  if (successUpdate) {
    ElMessage.success('字段模板已更新')
    await getFieldData()
  }
  else {
    ElMessage.error('字段模板更新失败')
  }
  nextTick(() => {
    tableRef.value.refreshColumn()
  })
}

async function getSubmitAll(data: any[]) {
  ElMessageBox.confirm('该操作会覆盖所有用户的保存记录，确定要执行？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      if (!props.config.fieldApiKey)
        return ElMessage.warning('请先设置key')
      await fetchDataAllUpdate({
        key: props.config.fieldApiKey,
        habits: JSON.stringify(data),
      })
      if (successAllUpdate) {
        ElMessage.success('字段模板已更新')
        await getFieldData()
      }
      else {
        ElMessage.error('字段模板更新失败')
      }
      nextTick(() => {
        tableRef.value.refreshColumn()
      })
    })
    .catch(() => {})
}

defineExpose({
  tableRef,
  tableFieldRef,
  exportSelectEvent,
})

const filterAgeMethod: any = (
  { option, row }: any,
  field = '',
  filterMethod: Function | undefined,
) => {
  if (!filterMethod)
    return row[field]?.toString().includes(option.data)

  return filterMethod(option, row)
}

const ageOptions = ref<any>([{ data: '' }])

let tempRow = {}
function editActiveEvent(row: any) {
  tempRow = cloneDeep(row.row)
}
const editClosedEvent: VxeTableEvents.EditClosed = async ({ row }) => {
  const $table = tableRef.value
  if ($table) {
    // 检测是否有修改，isUpdateByRow判断不出其他组件库的修改，所以这里需要自己判断
    // if ($table.isUpdateByRow(row, field)) {
    if (!isEqual(tempRow, row)) {
      // 检查是否合理

      const originalValue = cloneDeep(tempRow)
      const errMap = await $table.validate()
      if (errMap) {
        ElMessage.error('校验失败.数据不合法！')

        await $table.reloadRow(originalValue, {})
        return
      }
      // 抛回父组件处理
      props.config?.handUpdateRow(row)
      await $table.reloadRow(row, {})
    }
  }
}

// ------------
// 我是真不想动这个文件的，但是它实在太烂了
function getFormatter(item) {
  if (item?.isDate) {
    return ({ cellValue }) => formatDateTime({
      cellValue,
      format: item.formatTime || 'YYYY-MM-DD HH:mm:ss',
    })
  }
  // field包含了 unit_price 才会加前缀
  if (item?.isUnitPrice) {
    if (item.field.includes('unit_price'))
      return ({ cellValue }) => formatUnitPriceDivTable({ cellValue }, unitDigit, '￥')
    return formatUnitPriceDivTable
  }

  // field包含了 _price 才会加前缀
  if (item?.isPrice) {
    if (item.field.includes('_price'))
      return ({ cellValue }) => formatPriceDivTable({ cellValue }, priceDigit, '￥')

    return formatPriceDivTable
  }

  if (item?.isRoll)
    return formatRollDivTable

  if (item?.isWeight)
    return formatWeightDivTable

  if (item?.isLength)
    return formatLengthDivTable

  if (item?.isUnderweightRate)
    return formatUnderweightRateDivTable

  if (item?.is_date)
    return formatDateTable

  return ''
}
</script>

<template>
  <div class="flex-1 flex flex-col overflow-hidden h-full">
    <div class="flex-1 overflow-y-hidden">
      <vxe-table
        ref="tableRef"
        size="mini"
        border
        stripe
        show-header-overflow
        show-overflow
        :show-footer="newConfig.show_footer && !!props.config?.footerMethod"
        highlight-hover-row
        highlight-current-row
        highlight-hover-column
        highlight-current-columnn
        :auto-resize="props.config?.autoResize"
        :loading="props.config?.loading"
        :footer-method="props.config?.footerMethod"
        :footer-span-method="props.config?.footerColspanMethod"
        :span-method="props.config?.colspanMethod"
        :min-height="newConfig?.minHeight"
        :max-height="newConfig?.maxHeight"
        :height="props.config?.height"
        :data="props.tableList"
        :row-config="{ isHover: true, keyField: '_X_ROW_KEY_TABLE', ...newConfig.rowConfig }"
        :sort-config="newConfig?.sortConfig"
        :column-config="{ useKey: true, resizable: true }"
        :checkbox-config="newConfig.checkboxConfig"
        :keep-selected="true"
        :scroll-y="newConfig.scrollY"
        :header-cell-class-name="headerCellClassName"
        :row-class-name="rowClassName"
        :cell-class-name="cellClassName"
        class="mytable-style"
        :mouse-config="{ selected: true }"
        :radio-config="{ highlight: true, ...newConfig.radioConfig }"
        :footer-cell-class-name="props.config?.footerCellClassName"
        :keep-source="!!props.config?.editConfig"
        :merge-cells="props.config?.mergeCells"
        :keyboard-config="{
          isArrow: true,
          isDel: true,
          isEnter: true,
          isTab: true,
          isEdit: true,
        }"
        :edit-config="props.config?.editConfig ? props.config?.editConfig : {}"
        :expand-config="props.config?.expandConfig ? props.config?.expandConfig : {}"
        :edit-rules="props.config?.validConfig ? props.config?.validConfig : {}"
        @checkbox-all="handAllSelect"
        @checkbox-change="handleSelectionChange"
        @toggle-row-expand="props.config?.toggleExpandChangeEvent"
        @cell-dblclick="props.config?.cellDBLClickEvent"
        @radio-change="props.config?.radioChangeEvent"
        @cell-click="props.config?.cellClick"
        @edit-closed="editClosedEvent"
        @edit-actived="editActiveEvent"
      >
        <!-- 点击图标实现行拖拽 -->
        <vxe-column v-if="props.config?.showSort" width="80" fixed="left">
          <template #default>
            <span class="drag-btn">
              <i class="vxe-icon--menu" />
            </span>
          </template>
        </vxe-column>
        <!-- 是否需要表格展开或者收缩 -->
        <vxe-column
          v-if="props?.config?.expand"
          type="expand"
          width="40"
          :fixed="props.config?.expandConfig?.fixed === 'unset' ? undefined : 'left'"
        >
          <template #content="{ row, rowIndex }">
            <slot
              :name="props?.config?.expandName"
              :row-index="rowIndex"
              :row="row"
            />
          </template>
        </vxe-column>
        <vxe-column
          v-if="props.config?.showCheckBox"
          type="checkbox"
          width="50"
          :fixed="props.config?.checkboxConfig?.fixed === 'unset' ? undefined : 'left'"
        />
        <vxe-column
          v-if="props.config?.showRadio"
          type="radio"
          :width="newConfig?.radioWidth"
          :fixed="props.config?.radioConfig?.fixed === 'unset' ? undefined : 'left'"
        >
          <template #header>
            <slot name="radioHeader">
              单选
            </slot>
          </template>
        </vxe-column>
        <vxe-column
          v-if="props.config?.showSlotNums"
          type="seq"
          :fixed="props.config?.slotConfig?.fixed === 'unset' ? undefined : 'left'"
          title="序号"
          width="50"
        />
        <template v-for="(item, index) in tableFieldcolumn" :key="index">
          <template v-if="item?.childrenList?.length">
            <vxe-colgroup
              v-if="item.show !== false"
              :key="index"
              :width="item?.width || ''"
              :fixed="item?.fixed"
              :align="item?.align"
              :field="item?.field"
              :title="item?.title"
              :sortable="item?.sortable"
              :cell-render="item?.cellRender"
            >
              <template v-if="item?.colGroupHeader" #header>
                {{ item?.headerSlot }}
                <template v-if="item?.headerSlot">
                  <component :is="item.headerSlot" />
                </template>
                <!-- 分组头部用于搜索  -->
                <vxe-input
                  v-else
                  :model-value="item?.headerInputValue"
                  class="search-input"
                  type="search"
                  clearable
                  placeholder="按回车确认筛选"
                  @input="item?.onInputSearch"
                />
              </template>
              <template v-for="(it, index) in item.childrenList">
                <vxe-column
                  v-if="it.show !== false"
                  :key="index"
                  :formatter="getFormatter(it)"
                  :width="it?.width || ''"
                  :min-width="it?.minWidth || ''"
                  :fixed="it?.fixed"
                  :align="it?.align"
                  :field="it?.field"
                  :title="it?.title"
                  :sortable="it?.sortable"
                  :cell-render="item?.cellRender"
                  show-overflow
                  :filters="
                    newConfig.filterStatus !== false
                      && item.filterStatus !== false
                      ? ageOptions
                      : null
                  "
                  :filter-method="
                    (val: any) =>
                      filterAgeMethod(val, it.field, it?.filterMethod)
                  "
                  :edit-render="props.config?.editConfig ? {} : void 0"
                >
                  <template #header>
                    <div class="flex items-center" @click="openBulk(item)">
                      {{ it?.title }}
                      <span v-if="it?.required" class="text-color-theme-o">*</span>
                    </div>
                  </template>
                  <template v-if="it?.isEdit" #edit="params">
                    <!--                在没有传入插槽的情况下，使用默认的input组件 -->
                    <vxe-input
                      v-if="it?.soltName == null"
                      v-model="params.row[it?.field]"
                      clearable
                    />
                    <slot
                      :name="it?.soltName"
                      :row="params.row"
                      :row-index="params.rowIndex"
                      @change="changeCellEvent(params)"
                    />
                  </template>
                  <template
                    v-if="it?.soltName && !it?.isEdit"
                    #default="{ row, rowIndex }"
                  >
                    <slot
                      v-if="!it.showStatus && !it.showOrder_status"
                      :name="it?.soltName"
                      :row="row"
                      :row-index="rowIndex"
                    />
                    <!-- 普通状态插槽 -->
                    <div v-else-if="it.showStatus" class="flex items-center">
                      <div
                        :class="row[it.soltName] === 1 ? 'yuan' : 'yuan_red'"
                      />
                      <div
                        :class="
                          row[it.soltName] === 1
                            ? 'yuan_font'
                            : 'yuan_font_active'
                        "
                      >
                        {{ row[it.soltName] === 1 ? "启用" : "禁用" }}
                      </div>
                    </div>
                    <!-- 单据状态插槽 -->
                    <div v-else-if="it.showOrder_status">
                      <div
                        v-if="row[it.soltName] === 2"
                        class="w-[60px] h-[20px] bg-[#d1f2e2] rounded-[20px] text-[#3fbd81] text-center"
                      >
                        已审核
                      </div>
                      <div
                        v-else-if="row[it.soltName] === 1"
                        class="w-[60px] h-[20px] bg-[#e6f2ff] rounded-[20px] text-[#4d94ff] text-center"
                      >
                        待审核
                      </div>
                      <div
                        v-else-if="
                          row[it.soltName] === 3 || row[it.soltName] === 4
                        "
                        class="w-[60px] h-[20px] bg-[#fff2f2] rounded-[20px] text-[#f58580] text-center"
                      >
                        {{ row[it.soltName] === 3 ? "已驳回" : "作废" }}
                      </div>
                    </div>
                  </template>
                  <template #filter="{ $panel, column }">
                    <vxe-input
                      v-for="(option, index) in column.filters"
                      :key="index"
                      v-model="option.data"
                      class="search-input"
                      placeholder="按回车确认筛选"
                      @input="
                        $panel.changeOption($event, !!option.data, option)
                      "
                      @keyup="handleFilterKeyupEnter($event, $panel)"
                    />
                  </template>
                </vxe-column>
              </template>
            </vxe-colgroup>
          </template>
          <template v-else>
            <vxe-column
              v-if="item.show !== false"
              :width="item?.width || ''"
              :fixed="item?.fixed"
              :align="item?.align"
              :min-width="item?.minWidth || ''"
              :field="item?.field"
              :title="item?.title"
              :sortable="item?.sortable"
              :formatter="getFormatter(item)"
              show-overflow
              :cell-render="item?.cellRender"
              :filters="
                newConfig.filterStatus !== false && item.filterStatus !== false
                  ? ageOptions
                  : null
              "
              :filter-method="
                (val: any) =>
                  filterAgeMethod(val, item.field, item?.filterMethod)
              "
              :sort-type="item?.sortType || 'string'"
              :edit-render="props.config?.editConfig ? {} : void 0"
            >
              <template #header>
                <div class="flex items-center" @click="openBulk(item)">
                  {{ item?.title }}
                  <!-- <el-icon size="15"><Operation /></el-icon> -->
                  <span v-if="item?.required" class="text-color-theme-o">*</span>
                </div>
              </template>
              <template v-if="item?.isEdit" #edit="params">
                <!--                在没有传入插槽的情况下，使用默认的input组件 -->
                <vxe-input
                  v-if="item?.soltName == null"
                  v-model="params.row[item?.field]"
                  clearable
                />

                <slot
                  :name="item?.soltName"
                  :row="params.row"
                  @change="changeCellEvent(params)"
                />
              </template>
              <template
                v-if="item?.soltName && !item?.isEdit"
                #default="{ row, rowIndex }"
              >
                <slot
                  v-if="!item.showStatus && !item.showOrder_status"
                  :name="item?.soltName"
                  :row="row"
                  :row-index="rowIndex"
                />
                <!-- 普通状态插槽 -->
                <div v-else-if="item.showStatus" class="flex items-center">
                  <div
                    :class="row[item.soltName] === 1 ? 'yuan' : 'yuan_red'"
                  />
                  <div
                    :class="
                      row[item.soltName] === 1
                        ? 'yuan_font'
                        : 'yuan_font_active'
                    "
                  >
                    {{ row[item.soltName] === 1 ? "启用" : "禁用" }}
                  </div>
                </div>
                <!-- 单据状态插槽 -->
                <div v-else-if="item.showOrder_status">
                  <div
                    v-if="row[item.soltName] === 2"
                    class="w-[60px] h-[20px] bg-[#d1f2e2] rounded-[20px] text-[#3fbd81] text-center"
                  >
                    已审核
                  </div>
                  <div
                    v-else-if="row[item.soltName] === 1"
                    class="w-[60px] h-[20px] bg-[#e6f2ff] rounded-[20px] text-[#4d94ff] text-center"
                  >
                    待审核
                  </div>
                  <div
                    v-else-if="
                      row[item.soltName] === 3 || row[item.soltName] === 4
                    "
                    class="w-[60px] h-[20px] bg-[#fff2f2] rounded-[20px] text-[#f58580] text-center"
                  >
                    {{ row[item.soltName] === 3 ? "已驳回" : "作废" }}
                  </div>
                </div>
              </template>
              <template #filter="{ $panel, column }">
                <vxe-input
                  v-for="(option, index) in column.filters"
                  :key="index"
                  v-model="option.data"
                  class="search-input"
                  placeholder="按回车确认筛选"
                  @input="$panel.changeOption($event, !!option.data, option)"
                  @keyup="handleFilterKeyupEnter($event, $panel)"
                />
              </template>
            </vxe-column>
          </template>
        </template>

        <vxe-table-column
          v-if="props.config.showOperate"
          title="操作"
          :width="props.config.operateWidth"
          fixed="right"
        >
          <template #default="{ row, rowIndex }">
            <slot name="operate" :row="row" :row-index="rowIndex" />
          </template>
          <template v-if="props.config.showFooterBtn" #footer>
            <vxe-button
              type="text"
              status="primary"
              size="mini"
              @click="props.config.clickFooterItem"
            >
              全部删除
            </vxe-button>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <div v-if="props.config.showPagition" class="buttom-oper">
      <el-pagination
        class="table-bottom-page"
        :default-page-size="newConfig.defaultPageSize"
        :current-page="props.config.page"
        :page-sizes="newConfig.pageSizes"
        :page-size="props.config.size"
        :layout="newConfig.pageLayout"
        :total="props.config.total"
        @size-change="props.config.handleSizeChange"
        @current-change="props.config.handleCurrentChange"
      />
      <!-- {{ props.config.total }} -->
    </div>
    <!-- <vxe-modal v-model="bulkShow" :title="`批量编辑-${bulkField?.title}`" show-footer>
    <template v-for="(item, index) in tableFieldcolumnt">
      <slot v-if="item.bulkStatus && item.field === bulkField.field" :name="item.field + '_bulk'" :row="item" :index="index"></slot>
    </template>
    <template #footer>
      <el-button size="small" type="primary" @click="bulkHandSave">提交</el-button>
    </template>
  </vxe-modal> -->
    <TableFile
      ref="tableFieldRef"
      v-model="showField"
      :default-column-list="props.columnList"
      :save-default-column-list="saveDefaultColumnList"
      :show-fixed="newConfig.showFixed"
      :column-list="tableFieldcolumnUser"
      @submit-all="getSubmitAll"
      @submit="getSubmit"
    />
  </div>
</template>

<style lang="scss">
.mytable-style {
  // 默认隐藏排序和筛选图标
  .vxe-header--column:not(:hover) {
    .vxe-cell--filter {
      display: none;
    }
    .vxe-cell--sort{
      display: none;
    }
  }

  // hover时显示排序和筛选图标
  .vxe-header--column:hover {
    .vxe-cell--sort{
      display: inline-flex;
    }
    .vxe-cell--filter {
      display: block;
    }
  }
}
.mytable-style .yellow {
  background-color: rgb(255, 247, 202);
}

.mytable-style .blue {
  background-color: #bceaff;
}

.mytable-style .purple {
  background-color: #c2c1ff;
}

.mytable-style .green {
  background-color: #b8ddb4;
}

.mytable-style .lightYellow {
  background-color: #dfd3af;
}

.hight-row td {
  background: rgba(66, 55, 55, 0.5);
  color: #fff;
}

.search-input {
  width: 100%;
}
//此处DescriptionsFormItem组件里的样式影响到分页样式显示
.table-bottom-page{
  --el-pagination-bg-color: '#f9f9fa';
  --el-pagination-button-disabled-bg-color:'#f9f9fa';
}
.table-bottom-page .el-pagination__sizes{
  width: 128px !important;
  //backgroud-color: '#f9f9fa' !important;
}
</style>
