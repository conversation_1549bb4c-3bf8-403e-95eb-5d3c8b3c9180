import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmSaleDeliveryOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/getGfmSaleDeliveryOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmSaleDeliveryOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/getGfmSaleDeliveryOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmSaleDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/getGfmSaleDeliveryOrder',
    method: 'get',
  })
}

// 新增数据
export const addGfmSaleDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/addGfmSaleDeliveryOrder',
    method: 'post',
  })
}

// 更新数据
export const updateGfmSaleDeliveryOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/updateGfmSaleDeliveryOrder',
    method: 'put',
  })
}

// 作废
export const updateGfmSaleDeliveryOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/updateGfmSaleDeliveryOrderStatusCancel',
    method: 'put',
  })
}

// 审核
export const updateGfmSaleDeliveryOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/updateGfmSaleDeliveryOrderStatusPass',
    method: 'put',
  })
}

// 驳回
export const updateGfmSaleDeliveryOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/updateGfmSaleDeliveryOrderStatusReject',
    method: 'put',
  })
}

// 消审
export const updateGfmSaleDeliveryOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmSaleDeliveryOrder/updateGfmSaleDeliveryOrderStatusWait',
    method: 'put',
  })
}
