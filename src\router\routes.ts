import { ArrowDown } from '@element-plus/icons-vue'
import LayOut from '@/components/LayOut/index.vue'
import { CollectTypeEnum, MonthTransferOrderEnum, OrderTypeEnum, SrcOrderType } from '@/enum/orderEnum'
import { InSourceWarehouseTypeEnum, SourceWarehouseTypeEnum } from '@/enum/grayFabricEnum'

let HomeComponents = null
HomeComponents = async () => await import('@/pages/home/<USER>')
// if (import.meta.env.MODE === 'pre')
//   HomeComponents = async () => await import('@/pages/home/<USER>')
// else
//   HomeComponents = async () => await import('@/pages/foundationDashboard/index_1120.vue')

const routes: any = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard',
    component: LayOut,
    kindNameLong: '管理工具',
    kindNameShort: '管理',
    icon: ArrowDown,
    meta: {
      navName: ['通用数据'],
      title: '基础版首页',
    },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        title: '基础版首页',
        component: HomeComponents,
      },
    ],
  },

  {
    path: '/systemTools',
    name: 'SystemTools',
    component: LayOut,
    kindNameLong: '系统管理',
    kindNameShort: '管理',
    icon: ArrowDown,
    redirect: '/systemTools/autoPkg',
    meta: {
      navName: ['系统管理'],
      title: '系统管理',
    },
    children: [
      {
        path: 'systemSettings',
        name: 'SystemSettings',
        title: '系统设置',
        meta: {
          navName: ['系统管理', '系统设置'],
        },
        component: async () => await import('@/pages/systemTools/systemSettings/index.vue'),
      },
      {
        path: 'developerResourcesUpload',
        name: 'DeveloperResourcesUpload',
        title: '开发者资源上传',
        meta: {
          navName: ['系统管理', '开发者资源上传'],
        },
        component: async () => await import('@/pages/systemTools/developerResourcesUpload/index.vue'),
      },
      {
        path: 'autoPkg',
        name: 'AutoPkg',
        title: '自动化package',
        meta: {
          navName: ['系统管理', 'package管理'],
          title: '包管理',
        },
        component: async () => await import('@/pages/systemTools/autoPkg/index.vue'),
      },
      {
        path: 'autoCode',
        name: 'AutoCode',
        title: '代码生成器',
        meta: {
          navName: ['系统管理', '代码生成器'],
          title: '代码生成器',
        },
        component: async () => await import('@/pages/systemTools/autoCode/index.vue'),
      },
      {
        path: 'dictionary',
        name: 'Dictionary',
        title: '字典管理',
        meta: {
          navName: ['系统管理', '字典管理'],
          title: '字典管理',
        },
        component: async () => await import('@/pages/systemTools/dictionary/index.vue'),
      },
      {
        path: 'globalConfig',
        name: 'GlobalConfig',
        title: '全局配置表',
        hideType: true,
        meta: {
          navName: ['系统管理', '全局配置表'],
          title: '全局配置表',
        },
        component: async () => await import('@/pages/systemTools/globalConfig/index.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        name: 'DictionaryDetail',
        title: '字典管理',
        props: true,
        hideType: true,
        meta: {
          navName: ['系统管理', '字典详情'],
          title: '字典详情',
        },
        component: async () => await import('@/pages/systemTools/dictionary/detail.vue'),
      },
      {
        path: '/printTemplate',
        name: 'PrintTemplate',
        title: '打印管理',
        meta: {
          navName: ['系统管理', '打印管理'],
          title: '打印管理',
        },
        component: async () => await import('@/pages/printTemplate/index.vue'),
      },
      {
        path: '/print/:id(\\d+)',
        name: 'Print',
        title: '打印设置',
        meta: {
          navName: ['系统管理', '打印设置'],
          title: '打印设置',
        },
        component: async () => await import('@/pages/print/index.vue'),
      },
      {
        path: 'userManagement',
        name: 'UserManagement',
        title: '用户管理',
        meta: {
          navName: ['系统设置', '用户管理'],
          title: '用户管理',
        },
        component: async () => await import('@/pages/systemTools/userManage/index.vue'),
      },
      {
        path: 'userManagementEdit',
        name: 'UserManagementEdit',
        title: '用户编辑',
        meta: {
          navName: ['系统设置', '用户编辑'],
          title: '用户编辑',
        },
        hideType: true,
        component: async () => await import('@/pages/systemTools/userManage/edit.vue'),
      },
      {
        path: 'userManagementDetail',
        name: 'UserManagementDetail',
        title: '用户详情',
        meta: {
          navName: ['系统设置', '用户详情'],
          title: '用户详情',
        },
        hideType: true,
        component: async () => await import('@/pages/systemTools/userManage/detail.vue'),
      },
      {
        path: 'roleManagement',
        name: 'RoleManagement',
        title: '角色管理',
        meta: {
          navName: ['系统设置', '角色管理'],
          title: '角色管理',
        },
        component: async () => await import('@/pages/systemTools/roleManage/index.vue'),
      },
      {
        path: 'roleManagementAdd',
        name: 'RoleManagementAdd',
        title: '新建角色',
        meta: {
          navName: ['系统设置', '新建角色'],
          title: '新建角色',
          keepAlive: true,
        },
        hideType: true,
        component: async () => await import('@/pages/systemTools/roleManage/add.vue'),
      },
      {
        path: 'roleManagementDetail',
        name: 'RoleManagementDetail',
        title: '角色详情',
        hideType: true,
        meta: {
          navName: ['系统设置', '角色详情'],
          title: '角色详情',
        },
        component: async () => await import('@/pages/systemTools/roleManage/detail.vue'),
      },
      {
        path: 'roleManagementEdit/:id(\\d+)',
        name: 'RoleManagementEdit',
        title: '编辑角色',
        hideType: true,
        meta: {
          navName: ['系统设置', '编辑角色'],
          title: '编辑角色',
          keepAlive: true,
        },
        component: async () => await import('@/pages/systemTools/roleManage/edit.vue'),
      },
      {
        path: 'personCenter',
        name: 'PersonCenter',
        title: '个人中心',
        meta: {
          navName: ['系统设置', '个人中心'],
          title: '个人中心',
        },
        component: async () => await import('@/pages/systemTools/personCenter.vue'),
      },
      {
        path: 'menu',
        name: 'Menu',
        title: '菜单管理',
        meta: {
          navName: ['系统设置', '菜单管理'],
          title: '菜单管理',
        },
        component: async () => await import('@/pages/systemTools/menu/index.vue'),
      },
      {
        path: 'resourceTree',
        name: 'ResourceTree',
        title: '资源树管理',
        meta: {
          navName: ['系统设置', '资源树管理'],
          title: '资源树管理',
        },
        component: async () => await import('@/pages/systemTools/resourceTree/index.vue'),
      },
      {
        path: 'upImage',
        name: 'UpImage',
        title: '上传图片',
        meta: {
          navName: ['系统设置', '上传图片'],
          title: '上传图片',
        },
        component: async () => await import('@/pages/systemTools/upImage/index.vue'),
      },
      {
        path: 'setting',
        name: 'Setting',
        title: '设备设置',
        meta: {
          navName: ['系统设置', '设备设置'],
          title: '设备设置',
        },
        component: async () => await import('@/pages/systemTools/setting/index.vue'),
      },
      {
        path: 'orderPrefixSettings',
        name: 'OrderPrefixSettings',
        title: '单据编号规则维护',
        meta: {
          navName: ['系统管理', '单据编号规则维护'],
        },
        component: async () => await import('@/pages/systemTools/orderPrefixSettings/index.vue'),
      },
    ],
  },
  {
    path: '/basicData',
    name: 'BasicData',
    component: LayOut,
    kindNameLong: '基础数据',
    kindNameShort: '基础',
    icon: ArrowDown,
    redirect: '/basicData/contactUnit',
    meta: {
      navName: ['基础数据'],
      title: '基础数据',
    },
    children: [
      {
        path: 'customerVisitTag',
        name: 'CustomerVisitTag',
        title: '客户拜访标签',
        meta: {
          navName: ['基础资料', '客户拜访标签'],
          title: '客户拜访标签',
        },
        component: async () => await import('@/pages/basicData/customerVisitTag/index.vue'),
      },
      {
        path: 'rawColorInfomation',
        name: 'RawColorInformation',
        title: '原料颜色资料',
        meta: {
          navName: ['基础资料', '原料颜色资料'],
          title: '原料颜色资料',
        },
        component: async () => await import('@/pages/basicData/rawColorInfomation/index.vue'),
      },
      {
        path: 'contactUnit',
        name: 'ContactUnit',
        title: '往来单位类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '往来单位类型'],
          title: '往来单位类型',
        },
        component: async () => await import('@/pages/basicData/contactUnit/index.vue'),
      },
      {
        path: 'warehouseType',
        name: 'WarehouseType',
        title: '仓库类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '仓库类型'],
          title: '仓库类型',
        },
        component: async () => await import('@/pages/basicData/warehouseType/index.vue'),
      },
      {
        path: 'rawaterialType',
        name: 'RawaterialType',
        title: '原料类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '原料类型'],
          title: '原料类型',
        },
        component: async () => await import('@/pages/basicData/rawaterialType/index.vue'),
      },
      {
        path: 'clothType',
        name: 'ClothType',
        title: '布料类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '布料类型'],
          title: '布料类型',
        },
        component: async () => await import('@/pages/basicData/clothType/index.vue'),
      },
      {
        path: 'productColorType',
        name: 'ProductColorType',
        title: '成品色号颜色类别',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品色号颜色类别'],
          title: '成品色号颜色类别',
        },
        component: async () => await import('@/pages/basicData/productColorType/index.vue'),
      },
      {
        path: 'embryoType',
        name: 'EmbryoType',
        title: '坯布订单类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '坯布订单类型'],
          title: '坯布订单类型',
        },
        component: async () => await import('@/pages/basicData/embryoType/index.vue'),
      },
      {
        path: 'expensesType',
        name: 'ExpensesType',
        title: '资金费用类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '资金费用类型'],
          title: '资金费用类型',
        },
        component: async () => await import('@/pages/basicData/expensesType/index.vue'),
      },
      {
        path: 'accountingType',
        name: 'AccountingType',
        title: '会计科目类型',
        meta: {
          navName: ['基础数据', '通用数据类型', '会计科目类型'],
          title: '会计科目类型',
        },
        component: async () => await import('@/pages/basicData/accountingType/index.vue'),
      },
      {
        path: 'settlementType',
        name: 'SettlementType',
        title: '收款账户',
        meta: {
          navName: ['基础数据', '通用数据类型', '收款账户'],
          title: '收款账户',
        },
        component: async () => await import('@/pages/basicData/settlementType/index.vue'),
      },
      {
        path: 'salesIssueType',
        name: 'SalesIssueType',
        title: '销售发货类型',
        meta: {
          navName: ['档案', '基础资料', '销售发货类型'],
          title: '销售发货类型',
        },
        component: async () => await import('@/pages/files/basicData/salesIssueType/index.vue'),
      },
      {
        path: 'measuringUnit',
        name: 'MeasuringUnit',
        title: '计量单位',
        meta: {
          navName: ['基础数据', '通用数据类型', '通用数据-计量单位'],
          title: '基础数据-计量单位',
        },
        component: async () => await import('@/pages/commonData/measuringUnit/index.vue'),
      },
      {
        path: 'productLevel',
        name: 'ProductLevel',
        title: '成品等级',
        meta: {
          navName: ['基础数据', '通用数据类型', '通用数据-成品等级'],
          title: '基础数据-成品等级',
        },
        component: async () => await import('@/pages/commonData/productLevel/index.vue'),
      },
      {
        path: 'fabricLevel',
        name: 'FabricLevel',
        title: '坯布等级',
        meta: {
          navName: ['基础数据', '通用数据类型', '通用数据-坯布等级'],
          title: '基础数据-坯布等级',
        },
        component: async () => await import('@/pages/commonData/fabricLevel/index.vue'),
      },
      {
        path: 'rawLevel',
        name: 'RawLevel',
        title: '原料等级',
        meta: {
          navName: ['基础数据', '通用数据类型', '通用数据-原料等级'],
          title: '基础数据-原料等级',
        },
        component: async () => await import('@/pages/commonData/rawLevel/index.vue'),
      },
      {
        path: 'dyeingProgress',
        name: 'DyeingProgress',
        title: '染整进度',
        meta: {
          navName: ['基础数据', '通用数据类型', '染整管理-染整进度'],
          title: '染整管理-染整进度',
        },
        component: async () => await import('@/pages/commonData/dyeingProgress/index.vue'),
      },
      {
        path: 'generalCurrent',
        name: 'GeneralCurrent',
        title: '流水方向',
        meta: {
          navName: ['基础数据', '通用数据类型', '财务管理-流水方向'],
          title: '财务管理-流水方向',
        },
        component: async () => await import('@/pages/commonData/generalCurrent/index.vue'),
      },
      {
        path: 'dyeingInformation',
        name: 'DyeingInformation',
        title: '染整工艺资料',
        meta: {
          navName: ['基础数据', '通用数据类型', '染整管理-染整工艺资料'],
          title: '染整管理-染整工艺资料',
        },
        component: async () => await import('@/pages/commonData/dyeingInformation/index.vue'),
      },
      {
        path: 'finishedProductInformation',
        name: 'FinishedProductInformation',
        title: '成品资料',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品资料'],
          title: '成品资料',
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/information/index.vue'),
      },
      {
        path: 'finishedProductInformationAdd',
        name: 'FinishedProductInformationAdd',
        title: '新建成品资料',
        meta: {
          navName: ['基础数据', '通用数据类型', '新建成品资料'],
          title: '新建成品资料',
          keepAlive: true,
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/information/add.vue'),
      },
      {
        path: 'finishedProductInformationDetail/:id(\\d+)',
        name: 'FinishedProductInformationDetail',
        title: '成品资料详情',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品资料详情'],
          title: '成品资料详情',
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/information/detail.vue'),
      },
      {
        path: 'finishedProductInformationEdit/:id(\\d+)',
        name: 'FinishedProductInformationEdit',
        title: '成品资料编辑',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品资料编辑'],
          title: '成品资料编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/information/edit.vue'),
      },

      {
        path: 'finishedProductColorInformation',
        name: 'FinishedProductColorInformation',
        title: '成品颜色资料',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品颜色资料'],
          title: '成品颜色资料',
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/colorInformation/index.vue'),
      },
      {
        path: 'finishedProductColorInformationAdd',
        name: 'FinishedProductColorInformationAdd',
        title: '新建成品颜色资料',
        meta: {
          navName: ['基础数据', '通用数据类型', '新建成品颜色资料'],
          title: '新建成品颜色资料',
          keepAlive: true,
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/colorInformation/add.vue'),
      },
      {
        path: 'finishedProductColorInformationDetail/:id(\\d+)',
        name: 'FinishedProductColorInformationDetail',
        title: '成品颜色资料详情',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品颜色资料详情'],
          title: '成品颜色资料详情',
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/colorInformation/detail.vue'),
      },
      {
        path: 'finishedProductColorInformationEdit/:id(\\d+)',
        name: 'FinishedProductColorInformationEdit',
        title: '成品颜色资料编辑',
        meta: {
          navName: ['基础数据', '通用数据类型', '成品颜色资料编辑'],
          title: '成品颜色资料编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/commonData/finishedProductInformation/colorInformation/edit.vue'),
      },
      // {
      //   path: 'contactUnitRecycle',
      //   name: 'ContactUnitRecycle',
      //   title: '往来单位类型回收站',
      //   hideType: true,
      //   meta: {
      //     navName: ['基础数据', '通用数据类型', '往来单位类型', '往来单位类型回收站'],
      //     title: '往来单位类型回收站',
      //   },
      //   component: async () => await import('@/pages/basicData/contactUnitRecycle.vue'),
      // },
      {
        path: 'invoiceTitle',
        name: 'InvoiceTitle',
        title: '发票抬头',
        meta: {
          navName: ['基础数据', '通用数据类型', '采购管理-发票抬头'],
          title: '采购管理-发票抬头',
        },
        component: async () => await import('@/pages/commonData/invoiceTitle/index.vue'),
      },
      {
        path: 'weavingSpecification',
        name: 'WeavingSpecification',
        title: '织造规格',
        meta: {
          navName: ['基础数据', '通用数据类型', '生产管理-织造规格'],
          title: '生产管理-织造规格',
        },
        component: async () => await import('@/pages/commonData/weavingSpecification/index.vue'),
      },
      {
        path: 'loomType',
        name: 'LoomType',
        title: '织机机型',
        meta: {
          navName: ['基础数据', '通用数据类型', '生产管理-织机机型'],
          title: '生产管理-织机机型',
        },
        component: async () => await import('@/pages/commonData/loomType/index.vue'),
      },
      {
        path: 'greyClothColor',
        name: 'GreyClothColor',
        title: '坯布颜色',
        meta: {
          navName: ['基础数据', '通用数据类型', '生产管理-坯布颜色'],
          title: '生产管理-坯布颜色',
        },
        component: async () => await import('@/pages/commonData/greyClothColor/index.vue'),
      },
      {
        path: 'needleInch',
        name: 'NeedleInch',
        title: '针寸数',
        meta: {
          navName: ['基础数据', '通用数据类型', '生产管理-针寸数'],
          title: '生产管理-针寸数',
        },
        component: async () => await import('@/pages/commonData/needleInch/index.vue'),
      },
      {
        path: 'creditPeriod',
        name: 'CreditPeriod',
        title: '付款期限',
        meta: {
          navName: ['基础数据', '通用数据类型', '生产管理-付款期限'],
          title: '生产管理-付款期限',
        },
        component: async () => await import('@/pages/commonData/creditPeriod/index.vue'),
      },
      {
        path: 'sourceProduct',
        name: 'SourceProduct',
        title: '成品来源',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-成品来源'],
          title: '销售管理-成品来源',
        },
        component: async () => await import('@/pages/commonData/sourceProduct/index.vue'),
      },
      {
        path: 'paymentsMeans',
        name: 'PaymentsMeans',
        title: '结算方式',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-结算方式'],
          title: '销售管理-结算方式',
        },
        component: async () => await import('@/pages/commonData/paymentsMeans/index.vue'),
      },
      {
        path: 'orderType',
        name: 'OrderType',
        title: '订单类别',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-订单类别'],
          title: '销售管理-订单类别',
        },
        component: async () => await import('@/pages/commonData/orderType/index.vue'),
      },
      {
        path: 'shrink',
        name: 'Shrink',
        title: '缩水',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-缩水'],
          title: '销售管理-缩水',
        },
        component: async () => await import('@/pages/commonData/shrink/index.vue'),
      },
      {
        path: 'lightFastness',
        name: 'LightFastness',
        title: '日晒牢度',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-日晒牢度'],
          title: '销售管理-日晒牢度',
        },
        component: async () => await import('@/pages/commonData/lightFastness/index.vue'),
      },
      {
        path: 'washing',
        name: 'Washing',
        title: '水洗',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-水洗'],
          title: '销售管理-水洗',
        },
        component: async () => await import('@/pages/commonData/washing/index.vue'),
      },
      {
        path: 'wetRubbing',
        name: 'WetRubbing',
        title: '湿擦',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-湿擦'],
          title: '销售管理-湿擦',
        },
        component: async () => await import('@/pages/commonData/wetRubbing/index.vue'),
      },
      {
        path: 'freight',
        name: 'Freight',
        title: '运费',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-运费'],
          title: '销售管理-运费',
        },
        component: async () => await import('@/pages/commonData/freight/index.vue'),
      },
      {
        path: 'logisticsCompany',
        name: 'LogisticsCompany',
        title: '物流公司',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-物流公司'],
          title: '销售管理-物流公司',
        },
        component: async () => await import('@/pages/commonData/logisticsCompany/index.vue'),
      },
      {
        path: 'taxInclusive',
        name: 'TaxInclusive',
        title: '含税项目',
        meta: {
          navName: ['基础数据', '通用数据类型', '销售管理-含税项目'],
          title: '销售管理-含税项目',
        },
        component: async () => await import('@/pages/commonData/taxInclusive/index.vue'),
      },
      {
        path: 'defectData',
        name: 'DefectData',
        title: '疵点资料',
        meta: {
          navName: ['基础数据', '通用数据类型', '疵点资料'],
          title: '疵点资料',
        },
        component: async () => await import('@/pages/commonData/defectData/index.vue'),
      },
      {
        path: 'marketingSystem',
        name: 'MarketingSystem',
        title: '营销体系管理',
        filterMenu: true,
        meta: {
          navName: ['基础数据', '营销体系管理'],
          title: '营销体系管理',
        },
        component: async () => await import('@/pages/marketingSystem/index.vue'),
      },
      {
        path: 'marketingSystemAdd',
        name: 'MarketingSystemAdd',
        title: '新建营销体系',
        hideType: true,
        meta: {
          navName: ['基础数据', '营销体系管理', '新建营销体系'],
          title: '新建营销体系管理',
          keepAlive: true,
        },
        component: async () => await import('@/pages/marketingSystem/add.vue'),
      },
      {
        path: 'marketingSystemDetail/:id(\\d+)',
        name: 'MarketingSystemDetail',
        title: '营销体系详情',
        hideType: true,
        meta: {
          navName: ['基础数据', '营销体系管理', '营销体系详情'],
          title: '营销体系详情',
        },
        component: async () => await import('@/pages/marketingSystem/detail.vue'),
      },
      {
        path: 'marketingSystemEdit/:id(\\d+)',
        name: 'MarketingSystemEdit',
        title: '编辑营销体系',
        hideType: true,
        meta: {
          navName: ['基础数据', '营销体系管理', '编辑营销体系'],
          title: '编辑营销体系',
          keepAlive: true,
        },
        component: async () => await import('@/pages/marketingSystem/add.vue'),
      },
      {
        path: 'warehouseInformation',
        name: 'WarehouseInformation',
        title: '仓库资料',
        meta: {
          navName: ['基础数据', '仓库资料'],
          title: '仓库资料',
        },
        component: async () => await import('@/pages/basicData/warehouseInformation/index.vue'),
      },
      {
        path: 'rawInformation',
        name: 'RawInformation',
        title: '原料资料',
        meta: {
          navName: ['基础数据', '原料资料'],
          title: '原料资料',
          keepAlive: true,
        },
        component: async () => await import('@/pages/commonData/rawInformation/index.vue'),
      },
      {
        path: 'rawInformationAdd',
        name: 'RawInformationAdd',
        title: '新建原料资料',
        meta: {
          navName: ['基础数据', '新建原料资料'],
          title: '新建原料资料',
        },
        component: async () => await import('@/pages/commonData/rawInformation/add.vue'),
      },
      {
        path: 'rawInformationDetail',
        name: 'RawInformationDetail',
        title: '原料资料详情',
        meta: {
          navName: ['基础数据', '原料资料详情'],
          title: '原料资料详情',
        },
        component: async () => await import('@/pages/commonData/rawInformation/detail.vue'),
      },
      {
        path: 'greyFabricInformation',
        name: 'GreyFabricInformation',
        title: '坯布资料',
        meta: {
          navName: ['基础数据', '坯布资料'],
          title: '坯布资料',
        },
        component: async () => await import('@/pages/commonData/greyFabricInformation/index.vue'),
      },
      {
        path: 'greyFabricInformationAdd',
        name: 'GreyFabricInformationAdd',
        title: '新建坯布资料',
        meta: {
          navName: ['基础数据', '新建坯布资料'],
          title: '新建坯布资料',
          keepAlive: true,
        },
        hideType: true,
        component: async () => await import('@/pages/commonData/greyFabricInformation/add.vue'),
      },
      {
        path: 'greyFabricInformationDetail',
        name: 'GreyFabricInformationDetail',
        title: '坯布资料详情',
        meta: {
          navName: ['基础数据', '坯布资料详情'],
          title: '坯布资料详情',
        },
        hideType: true,
        component: async () => await import('@/pages/commonData/greyFabricInformation/detail.vue'),
      },
      {
        path: 'greyFabricInformationEdit',
        name: 'GreyFabricInformationEdit',
        title: '编辑坯布资料',
        meta: {
          navName: ['基础数据', '编辑坯布资料'],
          title: '编辑坯布资料',
          keepAlive: true,
        },
        hideType: true,
        component: async () => await import('@/pages/commonData/greyFabricInformation/edit.vue'),
      },
      // {
      //   path: 'binInformation',
      //   name: 'BinInformation',
      //   title: '仓位资料',
      //   meta: {
      //     navName: ['基础数据', '仓库资料', '仓位资料'],
      //     title: '仓位资料',
      //   },
      //   component: async () => await import('@/pages/basicData/binInformation/index.vue'),
      // },
      {
        path: 'warehouseInformationDetail',
        name: 'WarehouseInformationDetail',
        title: '仓库资料详情',
        hideType: true,
        meta: {
          navName: ['基础数据', '仓库资料详情'],
          title: '仓库资料详情',
        },
        component: async () => await import('@/pages/basicData/warehouseInformation/detail.vue'),
      },
      {
        path: 'kindYeingInformation',
        name: 'KindDyeingInformation',
        title: '布种染整工艺资料',
        meta: {
          navName: ['基础数据', '染整管理-布种染整工艺资料'],
          title: '染整管理-布种染整工艺资料',
        },
        component: async () => await import('@/pages/basicData/dyeingInformation/index.vue'),
      },
    ],
  },
  {
    path: '/peopleManagement',
    name: 'PeopleManagement',
    component: LayOut,
    kindNameLong: '人事管理',
    kindNameShort: '人事管理',
    icon: ArrowDown,
    redirect: '/peopleManagement/departmentManagement',
    meta: {
      navName: ['人事管理'],
      title: '人事管理',
    },
    children: [
      {
        path: 'departmentManagement',
        name: 'DepartmentManagement',
        title: '部门管理',
        meta: {
          navName: ['人事管理', '部门管理'],
          title: '部门管理',
        },
        component: async () => await import('@/pages/peopleManagement/departmentManagement/index.vue'),
      },
      {
        path: 'employeesManagement',
        name: 'EmployeesManagement',
        title: '员工管理',
        meta: {
          navName: ['人事管理', '员工管理'],
          title: '员工管理',
        },
        component: async () => await import('@/pages/peopleManagement/employeesManagement/index.vue'),
      },
      {
        path: 'employeesManagementAdd',
        name: 'EmployeesManagementAdd',
        title: '新建员工',
        hideType: true,
        meta: {
          navName: ['人事管理', '员工管理', '新建员工'],
          title: '新建员工',
        },
        component: async () => await import('@/pages/peopleManagement/employeesManagement/add.vue'),
      },
      {
        path: 'employeesManagementEdit/:id(\\d+)',
        name: 'EmployeesManagementEdit',
        title: '编辑员工',
        hideType: true,
        meta: {
          navName: ['人事管理', '员工管理', '编辑员工'],
          title: '编辑员工',
        },
        component: async () => await import('@/pages/peopleManagement/employeesManagement/add.vue'),
      },
      {
        path: 'employeesManagementDetail/:id(\\d+)',
        name: 'EmployeesManagementDetail',
        title: '员工详情',
        hideType: true,
        meta: {
          navName: ['人事管理', '员工管理', '员工详情'],
          title: '员工详情',
        },
        component: async () => await import('@/pages/peopleManagement/employeesManagement/detail.vue'),
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    component: LayOut,
    redirect: '/404',
    hideType: true,
    children: [
      {
        path: '404',
        name: '404',
        meta: {
          navName: ['找不到网页了'],
          title: '找不到网页了',
        },
        component: async () => await import('@/pages/404.vue'),
      },
    ],
  },
  {
    path: '/403',
    name: '403',
    component: LayOut,
    hideType: true,
    children: [
      {
        path: '',
        name: 'NoPermission',
        meta: {
          navName: ['暂无权限'],
          title: '暂无权限',
        },
        component: async () => await import('@/pages/403.vue'),
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    hideType: true,
    component: async () => await import('@/pages/login.vue'),
  },
  {
    path: '/contactUnitMange',
    name: 'ContactUnitMange',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/contactUnitMange/supplierMange',
    meta: {
      navName: ['往来单位管理'],
      title: '往来单位管理',
    },
    children: [
      {
        path: 'supplierMange',
        name: 'SupplierMange',
        title: '供应商管理',
        meta: {
          navName: ['往来单位管理', '供应商管理'],
          title: '供应商管理',
        },
        component: async () => await import('@/pages/contactUnitMange/supplierMange/index.vue'),
      },
      {
        path: 'supplierAdd',
        name: 'SupplierAdd',
        title: '新建供应商',
        meta: {
          navName: ['往来单位管理', '新建供应商'],
          title: '新建供应商',
          keepAlive: true,
        },
        hideType: true,
        component: async () => await import('@/pages/contactUnitMange/supplierMange/add.vue'),
      },
      {
        path: 'supplierDetail',
        name: 'SupplierDetail',
        title: '供应商详情',
        meta: {
          navName: ['往来单位管理', '供应商详情'],
          title: '供应商详情',
        },
        hideType: true,
        component: async () => await import('@/pages/contactUnitMange/supplierMange/detail.vue'),
      },
      {
        path: 'supplierEdit',
        name: 'SupplierEdit',
        title: '供应商编辑',
        meta: {
          navName: ['往来单位管理', '供应商编辑'],
          title: '供应商编辑',
          keepAlive: true,
        },
        hideType: true,
        component: async () => await import('@/pages/contactUnitMange/supplierMange/edit.vue'),
      },
      {
        path: 'customerMange',
        name: 'CustomerMange',
        title: '客户管理',
        meta: {
          navName: ['往来单位管理', '客户管理'],
          title: '客户管理',
        },
        component: async () => await import('@/pages/contactUnitMange/customerMange/index.vue'),
      },
      {
        path: 'customerAdd',
        name: 'CustomerAdd',
        title: '新建客户',
        meta: {
          navName: ['往来单位管理', '新建客户'],
          title: '新建客户',
          keepAlive: true,
        },
        hideType: true,
        component: async () => await import('@/pages/contactUnitMange/customerMange/add.vue'),
      },
      {
        path: 'customerDetail',
        name: 'CustomerDetail',
        title: '客户详情',
        meta: {
          navName: ['往来单位管理', '客户详情'],
          title: '客户详情',
        },
        hideType: true,
        component: async () => await import('@/pages/contactUnitMange/customerMange/detail.vue'),
      },
      {
        path: 'customerEdit',
        name: 'CustomerEdit',
        title: '客户编辑',
        meta: {
          navName: ['往来单位管理', '客户编辑'],
          title: '客户编辑',
        },
        hideType: true,
        component: async () => await import('@/pages/contactUnitMange/customerMange/edit.vue'),
      },
      {
        path: 'salesGroup',
        name: 'SalesGroup',
        title: '销售群体',
        meta: {
          navName: ['往来单位管理', '销售群体'],
          title: '销售群体',
        },
        component: async () => await import('@/pages/contactUnitMange/salesGroup/index.vue'),
      },
      {
        path: 'salesArea',
        name: 'SalesArea',
        title: '销售区域',
        meta: {
          navName: ['往来单位管理', '销售区域'],
          title: '销售区域',
        },
        component: async () => await import('@/pages/contactUnitMange/salesArea/index.vue'),
      },
    ],
  },
  {
    path: '/procurementManaging',
    name: 'ProcurementManaging',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/contactUnitMange/supplierMange',
    meta: {
      navName: ['采购管理'],
      title: '采购管理',
    },
    children: [
      {
        path: 'rawMaterialSourcing',
        name: 'RawMaterialSourcing',
        title: '原料采购管理',
        meta: {
          navName: ['采购管理', '原料采购管理'],
          title: '原料采购管理',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcing/index.vue'),
      },
      {
        path: 'rawMaterialSourcingAdd',
        name: 'RawMaterialSourcingAdd',
        title: '新建原料采购订单',
        hideType: true,
        meta: {
          navName: ['采购管理', '新建原料采购订单'],
          title: '新建原料采购订单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcing/add.vue'),
      },
      {
        path: 'rawMaterialSourcingDetail/:id(\\d+)',
        name: 'RawMaterialSourcingDetail',
        title: '原料采购订单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '原料采购订单详情'],
          title: '原料采购订单详情',
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcing/detail.vue'),
      },
      {
        path: 'rawMaterialSourcingEdit/:id(\\d+)',
        name: 'RawMaterialSourcingEdit',
        title: '原料采购订单编辑',
        hideType: true,
        meta: {
          navName: ['采购管理', '原料采购订单编辑'],
          title: '原料采购订单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcing/edit.vue'),
      },
      {
        path: 'blanketManagement',
        name: 'BlanketManagement',
        title: '坯布采购订单管理',
        meta: {
          navName: ['采购管理', '坯布采购订单管理'],
          title: '坯布采购订单管理',
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagement/index.vue'),
      },
      {
        path: 'blanketManagementAdd',
        name: 'BlanketManagementAdd',
        title: '新建坯布采购订单',
        hideType: true,
        meta: {
          navName: ['采购管理', '新建坯布采购订单'],
          title: '新建坯布采购订单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagement/add.vue'),
      },
      {
        path: 'blanketManagementEdit/:id(\\d+)',
        name: 'BlanketManagementEdit',
        title: '编辑坯布采购订单',
        hideType: true,
        meta: {
          navName: ['采购管理', '编辑坯布采购订单'],
          title: '编辑坯布采购订单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagement/edit.vue'),
      },
      {
        path: 'blanketManagementDetail/:id(\\d+)',
        name: 'BlanketManagementDetail',
        title: '坯布采购订单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '坯布采购订单详情'],
          title: '坯布采购订单详情',
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagement/detail.vue'),
      },

      {
        path: 'finishedProductProcurement',
        name: 'FinishedProductProcurement',
        title: '成品采购订单',
        meta: {
          navName: ['采购管理', '成品采购订单'],
          title: '成品采购订单',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurement/index.vue'),
      },
      {
        path: 'finishedProductProcurementAdd',
        name: 'FinishedProductProcurementAdd',
        title: '新建成品采购订单',
        hideType: true,
        meta: {
          navName: ['采购管理', '新建成品采购订单'],
          title: '新建成品采购订单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurement/add.vue'),
      },
      {
        path: 'finishedProductProcurementEdit/:id(\\d+)',
        name: 'FinishedProductProcurementEdit',
        title: '编辑成品采购订单',
        hideType: true,
        meta: {
          navName: ['采购管理', '编辑成品采购订单'],
          title: '编辑成品采购订单',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurement/edit.vue'),
      },
      {
        path: 'finishedProductProcurementDetail/:id(\\d+)',
        name: 'FinishedProductProcurementDetail',
        title: '成品采购订单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '成品采购订单详情'],
          title: '成品采购订单详情',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurement/detail.vue'),
      },

      {
        path: 'finishedProductPurchaseReturns',
        name: 'FinishedProductPurchaseReturns',
        title: '成品采购退货单',
        meta: {
          navName: ['采购管理', '成品采购退货单'],
          title: '成品采购退货单',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductPurchaseReturns/index.vue'),
      },
      {
        path: 'finishedProductPurchaseReturnsAdd',
        name: 'FinishedProductPurchaseReturnsAdd',
        title: '新建成品采购退货单',
        hideType: true,
        meta: {
          navName: ['采购管理', '新建成品采购退货单'],
          title: '新建成品采购退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductPurchaseReturns/add.vue'),
      },
      {
        path: 'finishedProductPurchaseReturnsEdit/:id(\\d+)',
        name: 'FinishedProductPurchaseReturnsEdit',
        title: '编辑成品采购退货单',
        hideType: true,
        meta: {
          navName: ['采购管理', '编辑成品采购退货单'],
          title: '编辑成品采购退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductPurchaseReturns/edit.vue'),
      },
      {
        path: 'finishedProductPurchaseReturnsDetail/:id(\\d+)',
        name: 'FinishedProductPurchaseReturnsDetail',
        title: '成品采购退货单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '成品采购退货单详情'],
          title: '成品采购退货单详情',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductPurchaseReturns/detail.vue'),
      },
      {
        path: 'rawMaterialSourcingChangeOrder',
        name: 'RawMaterialSourcingChangeOrder',
        title: '原料采购变更单',
        hideType: true,
        meta: {
          navName: ['采购管理', '原料采购变更单'],
          title: '原料采购变更单',
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcingChangeOrder/index.vue'),
      },
      {
        path: 'rawMaterialSourcingChangeOrderDetail/:id(\\d+)',
        name: 'RawMaterialSourcingChangeOrderDetail',
        title: '原料采购变更单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '原料采购变更单详情'],
          title: '原料采购变更单详情',
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcingChangeOrder/detail.vue'),
      },
      {
        path: 'rawMaterialSourcingChangeOrderEdit/:id(\\d+)',
        name: 'RawMaterialSourcingChangeOrderEdit',
        title: '原料采购变更单编辑',
        hideType: true,
        meta: {
          navName: ['采购管理', '原料采购变更单编辑'],
          title: '原料采购变更单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/rawMaterialSourcingChangeOrder/edit.vue'),
      },
      {
        path: 'blankFabricManagementChangeOrder',
        name: 'BlankFabricManagementChangeOrder',
        title: '坯布采购变更单',
        hideType: true,
        meta: {
          navName: ['采购管理', '坯布采购变更单'],
          title: '坯布采购变更单',
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagementChangeOrder/index.vue'),
      },
      {
        path: 'blankFabricManagementChangeOrderDetail/:id(\\d+)',
        name: 'BlankFabricManagementChangeOrderDetail',
        title: '坯布采购变更单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '坯布采购变更单详情'],
          title: '坯布采购变更单详情',
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagementChangeOrder/detail.vue'),
      },
      {
        path: 'blankFabricManagementChangeOrderEdit/:id(\\d+)',
        name: 'BlankFabricManagementChangeOrderEdit',
        title: '坯布采购变更单编辑',
        hideType: true,
        meta: {
          navName: ['采购管理', '坯布采购变更单编辑'],
          title: '坯布采购变更单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/blankFabricManagementChangeOrder/edit.vue'),
      },

      {
        path: 'finishedProductProcurementChangeOrder',
        name: 'FinishedProductProcurementChangeOrder',
        title: '成品采购变更单',
        hideType: true,
        meta: {
          navName: ['采购管理', '成品采购变更单'],
          title: '成品采购变更单',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurementChangeOrder/index.vue'),
      },
      {
        path: 'finishedProductProcurementChangeOrderDetail/:id(\\d+)',
        name: 'FinishedProductProcurementChangeOrderDetail',
        title: '成品采购变更单详情',
        hideType: true,
        meta: {
          navName: ['采购管理', '成品采购变更单详情'],
          title: '成品采购变更单详情',
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurementChangeOrder/detail.vue'),
      },
      {
        path: 'finishedProductProcurementChangeOrderEdit/:id(\\d+)',
        name: 'FinishedProductProcurementChangeOrderEdit',
        title: '成品采购变更单编辑',
        hideType: true,
        meta: {
          navName: ['采购管理', '成品采购变更单编辑'],
          title: '成品采购变更单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/procurementManaging/finishedProductProcurementChangeOrder/edit.vue'),
      },
    ],
  },
  {
    path: '/grayFabricMange',
    name: 'GrayFabricMange',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/greyFabricPurchase',
    meta: {
      navName: ['坯布管理'],
      title: '坯布管理',
    },
    children: [
      {
        path: 'grayFabricInWarehouse',
        name: 'GrayFabricInWarehouse',
        title: '坯布进仓单',
        meta: {
          navName: ['坯布管理', '坯布进仓单', '坯布进仓单列表'],
          title: '坯布进仓单列表',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/grayFabricInWarehouse/index.vue'),
      },
      {
        path: 'grayFabricOutWarehouse',
        name: 'GrayFabricOutWarehouse',
        title: '坯布出仓单',
        meta: {
          navName: ['坯布管理', '坯布出仓单', '坯布出仓单列表'],
          title: '坯布出仓单列表',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/grayFabricOutWarehouse/index.vue'),
      },
      {
        path: 'greyFabricPurchase',
        name: 'GreyFabricPurchase',
        title: '坯布采购收货单',
        meta: {
          navName: ['坯布管理', '坯布采购收货单', '坯布采购收货单列表'],
          title: '坯布采购收货单列表',
          sourceWarehouseType: SourceWarehouseTypeEnum.PurchaseReceive, // 染厂库存-坯布台账单据类型
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchase/index.vue'),
      },
      {
        path: 'greyFabricPurchaseAdd',
        name: 'GreyFabricPurchaseAdd',
        title: '新建坯布采购收货单',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布采购收货单', '新建坯布采购收货单'],
          title: '新建坯布采购收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchase/add.vue'),
      },
      {
        path: 'greyFabricPurchaseDetail',
        name: 'GreyFabricPurchaseDetail',
        title: '坯布采购收货单详情',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布采购收货单', '坯布采购收货单详情'],
          title: '坯布采购收货单详情',
          inSourceWarehouseType: InSourceWarehouseTypeEnum.PurchaseReceive, // 染厂库存-坯布台账单据类型
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchase/detail.vue'),
      },
      {
        path: 'greyFabricPurchaseEdit',
        name: 'GreyFabricPurchaseEdit',
        title: '编辑坯布采购收货单',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布采购收货单', '编辑坯布采购收货单'],
          title: '编辑坯布采购收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchase/edit.vue'),
      },
      {
        path: 'greyFabricPurchaseReturn',
        name: 'GreyFabricPurchaseReturn',
        title: '坯布采购退货单',
        meta: {
          navName: ['坯布管理', '坯布采购退货单', '坯布采购退货单列表'],
          title: '坯布采购退货单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchaseReturn/index.vue'),
      },
      {
        path: 'greyFabricPurchaseReturnAdd',
        name: 'GreyFabricPurchaseReturnAdd',
        title: '新建坯布采购退货单',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布采购退货单', '新建坯布采购退货单'],
          title: '新建坯布采购退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchaseReturn/add.vue'),
      },
      {
        path: 'greyFabricPurchaseReturnEdit',
        name: 'GreyFabricPurchaseReturnEdit',
        title: '编辑坯布采购退货单',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布采购退货单', '编辑坯布采购退货单'],
          title: '编辑坯布采购退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchaseReturn/edit.vue'),
      },
      {
        path: 'greyFabricPurchaseReturnDetail',
        name: 'GreyFabricPurchaseReturnDetail',
        title: '坯布采购退货单详情',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布采购退货单', '坯布采购退货单详情'],
          title: '坯布采购退货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.PurchaseReturn,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricPurchaseReturn/detail.vue'),
      },
      {
        path: 'greyFabricSales',
        name: 'GreyFabricSales',
        title: '坯布销售出货单',
        meta: {
          navName: ['坯布管理', '坯布销售出单', '坯布销售出货单列表'],
          title: '坯布销售出货单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSales/index.vue'),
      },
      {
        path: 'greyFabricSalesAdd',
        name: 'GreyFabricSalesAdd',
        title: '新建坯布销售出货单',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布销售出单', '新建坯布销售出货单'],
          title: '新建坯布销售出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSales/add.vue'),
      },
      {
        path: 'greyFabricSalesEdit',
        name: 'GreyFabricSalesEdit',
        title: '编辑坯布销售出货单',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布销售出单', '编辑坯布销售出货单'],
          title: '编辑坯布销售出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSales/edit.vue'),
      },
      {
        path: 'greyFabricSalesDetail',
        name: 'GreyFabricSalesDetail',
        title: '坯布销售出货单详情',
        hideType: true,
        meta: {
          navName: ['坯布管理', '坯布销售出单', '坯布销售出货单详情'],
          title: '坯布销售出货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.SaleDelivery,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSales/detail.vue'),
      },
      {
        path: 'greyFabricSalesReturn',
        name: 'GreyFabricSalesReturn',
        title: '坯布销售退货单',
        meta: {
          navName: ['坯布管理', '坯布销售退货单', '坯布销售退货单列表'],
          title: '坯布销售退货单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSalesReturn/index.vue'),
      },
      {
        path: 'greyFabricSalesReturnAdd',
        name: 'GreyFabricSalesReturnAdd',
        title: '新建坯布销售退货单',
        meta: {
          navName: ['坯布管理', '坯布销售退货单', '新建坯布销售退货单'],
          title: '新建坯布销售退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSalesReturn/add.vue'),
      },
      {
        path: 'greyFabricSalesReturnEdit',
        name: 'GreyFabricSalesReturnEdit',
        title: '编辑坯布销售退货单',
        meta: {
          navName: ['坯布管理', '坯布销售退货单', '编辑坯布销售退货单'],
          title: '编辑坯布销售退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSalesReturn/edit.vue'),
      },
      {
        path: 'greyFabricSalesReturnDetail',
        name: 'GreyFabricSalesReturnDetail',
        title: '坯布销售退货单详情',
        meta: {
          navName: ['坯布管理', '坯布销售退货单', '坯布销售退货单详情'],
          title: '坯布销售退货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.SaleReturn,
          srcOrderType: SrcOrderType.SrcOrderTypeFabricSaleReturn,
          inSourceWarehouseType: InSourceWarehouseTypeEnum.SaleReturn,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricSalesReturn/detail.vue'),
      },
      {
        path: 'greyClothProduction',
        name: 'GreyClothProduction',
        title: '坯布生产收货单',
        meta: {
          navName: ['坯布管理', '坯布生产收货单', '坯布生产收货单列表'],
          title: '坯布生产收货单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProduction/index.vue'),
      },
      {
        path: 'greyClothProductionAdd',
        name: 'GreyClothProductionAdd',
        title: '新建坯布生产收货单',
        meta: {
          navName: ['坯布管理', '坯布生产收货单', '新建坯布生产收货单'],
          title: '新建坯布生产收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProduction/add.vue'),
      },
      {
        path: 'greyClothProductionEdit',
        name: 'GreyClothProductionEdit',
        title: '编辑坯布生产收货单',
        meta: {
          navName: ['坯布管理', '坯布生产收货单', '编辑坯布生产收货单'],
          title: '编辑坯布生产收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProduction/edit.vue'),
      },
      {
        path: 'greyClothProductionDetail',
        name: 'GreyClothProductionDetail',
        title: '坯布生产收货单详情',
        meta: {
          navName: ['坯布管理', '坯布生产收货单', '坯布生产收货单详情'],
          title: '坯布生产收货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.ProductReceive,
          inSourceWarehouseType: InSourceWarehouseTypeEnum.ProductReceive,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProduction/detail.vue'),
      },
      {
        path: 'greyClothProductionReturn',
        name: 'GreyClothProductionReturn',
        title: '坯布生产退货单',
        meta: {
          navName: ['坯布管理', '坯布生产退货单', '坯布生产退货单列表'],
          title: '坯布生产退货单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProductionReturn/index.vue'),
      },
      {
        path: 'greyClothProductionReturnAdd',
        name: 'GreyClothProductionReturnAdd',
        title: '新建坯布生产退货单',
        meta: {
          navName: ['坯布管理', '坯布生产退货单', '新建坯布生产退货单'],
          title: '新建坯布生产退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProductionReturn/add.vue'),
      },
      {
        path: 'greyClothProductionReturnEdit',
        name: 'GreyClothProductionReturnEdit',
        title: '编辑坯布生产退货单',
        meta: {
          navName: ['坯布管理', '坯布生产退货单', '编辑坯布生产退货单'],
          title: '编辑坯布生产退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProductionReturn/edit.vue'),
      },
      {
        path: 'greyClothProductionReturnDetail',
        name: 'GreyClothProductionReturnDetail',
        title: '坯布生产退货单详情',
        meta: {
          navName: ['坯布管理', '坯布生产退货单', '坯布生产退货单详情'],
          title: '坯布生产退货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.ProductReturn,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothProductionReturn/detail.vue'),
      },
      {
        path: 'greyClothShipmentDeduction',
        name: 'GreyClothShipmentDeduction',
        title: '坯布出货扣款',
        meta: {
          navName: ['坯布管理', '坯布出货扣款', '坯布出货扣款单列表'],
          title: '坯布出货扣款单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothShipmentDeduction/index.vue'),
      },
      {
        path: 'greyClothShipmentDeductionAdd',
        name: 'GreyClothShipmentDeductionAdd',
        title: '新建坯布出货扣款单',
        meta: {
          navName: ['坯布管理', '坯布出货扣款', '新建坯布出货扣款单'],
          title: '新建坯布出货扣款单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothShipmentDeduction/add.vue'),
      },
      {
        path: 'greyClothShipmentDeductionEdit',
        name: 'GreyClothShipmentDeductionEdit',
        title: '编辑坯布出货扣款单',
        meta: {
          navName: ['坯布管理', '坯布出货扣款', '编辑坯布出货扣款单'],
          title: '编辑坯布出货扣款单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothShipmentDeduction/edit.vue'),
      },
      {
        path: 'greyClothShipmentDeductionDetail',
        name: 'GreyClothShipmentDeductionDetail',
        title: '坯布出货扣款单详情',
        meta: {
          navName: ['坯布管理', '坯布出货扣款', '坯布出货扣款单详情'],
          title: '坯布出货扣款单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.DeductionOut,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothShipmentDeduction/detail.vue'),
      },
      {
        path: 'greyClothTransferForm',
        name: 'GreyClothTransferForm',
        title: '坯布调拨单',
        meta: {
          navName: ['坯布管理', '坯布调拨单', '坯布调拨单列表'],
          title: '坯布调拨单列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTransferForm/index.vue'),
      },
      {
        path: 'greyClothTransferFormAdd',
        name: 'GreyClothTransferFormAdd',
        title: '新建坯布调拨单',
        meta: {
          navName: ['坯布管理', '坯布调拨单', '新建坯布调拨单'],
          title: '新建坯布调拨单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTransferForm/add.vue'),
      },
      {
        path: 'greyClothTransferFormEdit',
        name: 'GreyClothTransferFormEdit',
        title: '编辑坯布调拨单',
        meta: {
          navName: ['坯布管理', '坯布调拨单', '编辑坯布调拨单'],
          title: '编辑坯布调拨单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTransferForm/edit.vue'),
      },
      {
        path: 'greyClothTransferFormDetail',
        name: 'GreyClothTransferFormDetail',
        title: '坯布调拨单详情',
        meta: {
          navName: ['坯布管理', '坯布调拨单', '坯布调拨单详情'],
          title: '坯布调拨单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.Allocate,
          inSourceWarehouseType: InSourceWarehouseTypeEnum.Allocate,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTransferForm/detail.vue'),
      },
      {
        path: 'greyClothOtherReceipt',
        name: 'GreyClothOtherReceipt',
        title: '坯布其他收货单',
        meta: {
          navName: ['坯布管理', '坯布其他收货单', '坯布其他收货列表'],
          title: '坯布其他收货列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherReceipt/index.vue'),
      },
      {
        path: 'greyClothTicketWeigh',
        name: 'GreyClothTicketWeigh',
        title: '布飞称重',
        meta: {
          navName: ['坯布管理', '布飞称重'],
          title: '布飞称重',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTicketWeigh/index.vue'),
      },
      {
        path: 'greyClothTicketInspection',
        name: 'GreyClothTicketInspection',
        title: '布飞称重',
        meta: {
          navName: ['坯布管理', '布飞验布'],
          title: '布飞验布',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTicketInspection/index.vue'),
      },
      {
        path: 'greyClothTicketCancel',
        name: 'GreyClothTicketCancel',
        title: '布飞取消',
        meta: {
          navName: ['坯布管理', '布飞取消'],
          title: '布飞取消',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothTicketCancel/index.vue'),
      },
      {
        path: 'greyClothOtherReceiptAdd',
        name: 'GreyClothOtherReceiptAdd',
        title: '新建坯布其他收货单',
        meta: {
          navName: ['坯布管理', '坯布其他收货单', '新建坯布其他收货单'],
          title: '新建坯布其他收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherReceipt/add.vue'),
      },
      {
        path: 'greyClothOtherReceiptEdit',
        name: 'GreyClothOtherReceiptEdit',
        title: '编辑坯布其他收货单',
        meta: {
          navName: ['坯布管理', '坯布其他收货单', '编辑坯布其他收货单'],
          title: '编辑坯布其他收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherReceipt/edit.vue'),
      },
      {
        path: 'greyClothOtherReceiptDetail',
        name: 'GreyClothOtherReceiptDetail',
        title: '坯布其他收货单详情',
        meta: {
          navName: ['坯布管理', '坯布其他收货单', '坯布其他收货单详情'],
          title: '坯布其他收货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.OtherReceive,
          inSourceWarehouseType: InSourceWarehouseTypeEnum.OtherReceive,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherReceipt/detail.vue'),
      },
      {
        path: 'greyClothOtherShippingNotes',
        name: 'GreyClothOtherShippingNotes',
        title: '坯布其他出货单',
        meta: {
          navName: ['坯布管理', '坯布其他出货单', '坯布其他出货列表'],
          title: '坯布其他出货列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherShippingNotes/index.vue'),
      },
      {
        path: 'greyClothOtherShippingNotesAdd',
        name: 'GreyClothOtherShippingNotesAdd',
        title: '新建坯布其他出货单',
        meta: {
          navName: ['坯布管理', '坯布其他出货单', '新建坯布其他出货单'],
          title: '新建坯布其他出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherShippingNotes/add.vue'),
      },
      {
        path: 'greyClothOtherShippingNotesEdit',
        name: 'GreyClothOtherShippingNotesEdit',
        title: '编辑坯布其他出货单',
        meta: {
          navName: ['坯布管理', '坯布其他出货单', '编辑坯布其他出货单'],
          title: '编辑坯布其他出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherShippingNotes/edit.vue'),
      },
      {
        path: 'greyClothOtherShippingNotesDetail',
        name: 'GreyClothOtherShippingNotesDetail',
        title: '坯布其他出货单详情',
        meta: {
          navName: ['坯布管理', '坯布其他出货单', '坯布其他出货单详情'],
          title: '坯布其他出货单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.OtherDelivery,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothOtherShippingNotes/detail.vue'),
      },
      {
        path: 'greyClothInventorySheet',
        name: 'GreyClothInventorySheet',
        title: '坯布盘点单',
        meta: {
          navName: ['坯布管理', '坯布盘点单', '坯布库存盘点列表'],
          title: '坯布库存盘点列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothInventorySheet/index.vue'),
      },
      {
        path: 'greyClothInventorySheetAdd',
        name: 'GreyClothInventorySheetAdd',
        title: '新建坯布盘点单',
        meta: {
          navName: ['坯布管理', '坯布盘点单', '新建坯布盘点单'],
          title: '新建坯布盘点单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothInventorySheet/add.vue'),
      },
      {
        path: 'greyClothInventorySheetEdit',
        name: 'GreyClothInventorySheetEdit',
        title: '编辑坯布盘点单',
        meta: {
          navName: ['坯布管理', '坯布盘点单', '编辑坯布盘点单'],
          title: '编辑坯布盘点单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothInventorySheet/edit.vue'),
      },
      {
        path: 'greyClothInventorySheetDetail',
        name: 'GreyClothInventorySheetDetail',
        title: '坯布盘点单详情',
        meta: {
          navName: ['坯布管理', '坯布盘点单', '坯布盘点单详情'],
          title: '坯布盘点单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.Check,
          inSourceWarehouseType: InSourceWarehouseTypeEnum.Check,
        },
        component: async () => await import('@/pages/grayFabricMange/greyClothInventorySheet/detail.vue'),
      },
      {
        path: 'greyFabricInventory',
        name: 'GreyFabricInventory',
        title: '坯布库存调整单',
        meta: {
          navName: ['坯布管理', '坯布库存调整单', '坯布库存调整列表'],
          title: '坯布库存调整列表',
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricInventory/index.vue'),
      },
      {
        path: 'greyFabricInventoryAdd',
        name: 'GreyFabricInventoryAdd',
        title: '新建坯布库存调整单',
        meta: {
          navName: ['坯布管理', '坯布库存调整单', '新建坯布库存调整单'],
          title: '新建坯布库存调整单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricInventory/add.vue'),
      },
      {
        path: 'greyFabricInventoryEdit',
        name: 'GreyFabricInventoryEdit',
        title: '编辑坯布库存调整单',
        meta: {
          navName: ['坯布管理', '坯布库存调整单', '编辑坯布库存调整单'],
          title: '编辑坯布库存调整单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricInventory/edit.vue'),
      },
      {
        path: 'greyFabricInventoryDetail',
        name: 'GreyFabricInventoryDetail',
        title: '坯布库存调整单详情',
        meta: {
          navName: ['坯布管理', '坯布库存调整单', '坯布库存调整单详情'],
          title: '坯布库存调整单详情',
          sourceWarehouseType: SourceWarehouseTypeEnum.Adjust,
          inSourceWarehouseType: InSourceWarehouseTypeEnum.Adjust,
        },
        component: async () => await import('@/pages/grayFabricMange/greyFabricInventory/detail.vue'),
      },
    ],
  },
  {
    path: '/rawMaterialManagement',
    name: 'RawMaterialManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/rawMaterialPurchaseReceipt',
    meta: {
      navName: ['原料管理'],
      title: '原料管理',
    },
    children: [
      {
        path: 'rawMaterialPurchaseReceipt',
        name: 'RawMaterialPurchaseReceipt',
        title: '原料采购收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料采购收货单'],
          title: '原料采购收货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialPurchase/index.vue'),
      },
      {
        path: 'rawMaterialPurchaseReceiptAdd',
        name: 'RawMaterialPurchaseReceiptAdd',
        title: '新建原料采购收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料采购收货单'],
          title: '新建原料采购收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialPurchase/add.vue'),
      },
      {
        path: 'rawMaterialPurchaseReceiptEdit/:id(\\d+)',
        name: 'RawMaterialPurchaseReceiptEdit',
        title: '编辑原料采购收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料采购收货单'],
          title: '编辑原料采购收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialPurchase/edit.vue'),
      },
      {
        path: 'rawMaterialPurchaseReceiptDetail/:id(\\d+)',
        name: 'RawMaterialPurchaseReceiptDetail',
        title: '详情原料采购收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料采购收货单详情'],
          title: '原料采购收货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialPurchase/detail.vue'),
      },
      {
        path: 'rawMaterialReturnOrder',
        name: 'RawMaterialReturnOrder',
        title: '原料采购退货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料采购退货单'],
          title: '原料采购退货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialReturnOrder/index.vue'),
      },
      {
        path: 'rawMaterialReturnOrderAdd',
        name: 'RawMaterialReturnOrderAdd',
        title: '新建原料采购退货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料采购退货单'],
          title: '新建原料采购退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialReturnOrder/add.vue'),
      },
      {
        path: 'rawMaterialReturnOrderEdit/:id(\\d+)',
        name: 'RawMaterialReturnOrderEdit',
        title: '编辑原料采购退货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料采购退货单'],
          title: '编辑原料采购退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialReturnOrder/edit.vue'),
      },
      {
        path: 'rawMaterialReturnOrderDetail/:id(\\d+)',
        name: 'RawMaterialReturnOrderDetail',
        title: '原料采购退货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料采购退货单详情'],
          title: '原料采购退货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialReturnOrder/detail.vue'),
      },
      {
        path: 'rawMaterialtransferOrder',
        name: 'RawMaterialtransferOrder',
        title: '原料调拨单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料调拨单'],
          title: '原料调拨单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialtransferOrder/index.vue'),
      },
      {
        path: 'rawMaterialtransferOrderAdd',
        name: 'RawMaterialtransferOrderAdd',
        title: '新建原料调拨单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料调拨单'],
          title: '新建原料调拨单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialtransferOrder/add.vue'),
      },
      {
        path: 'rawMaterialtransferOrderEdit/:id(\\d+)',
        name: 'RawMaterialtransferOrderEdit',
        title: '编辑原料调拨单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料调拨单'],
          title: '编辑原料调拨单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialtransferOrder/edit.vue'),
      },
      {
        path: 'rawMaterialtransferOrderDetail/:id(\\d+)',
        name: 'RawMaterialtransferOrderDetail',
        title: '原料调拨单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料调拨单详情'],
          title: '原料调拨单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialtransferOrder/detail.vue'),
      },
      {
        path: 'rawMaterialSalesOrder',
        name: 'RawMaterialSalesOrder',
        title: '原料销售出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料销售出货单'],
          title: '原料销售出货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesOrder/index.vue'),
      },
      {
        path: 'rawMaterialSalesOrderAdd',
        name: 'RawMaterialSalesOrderAdd',
        title: '新建原料销售出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料销售出货单'],
          title: '新建原料销售出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesOrder/add.vue'),
      },
      {
        path: 'rawMaterialSalesOrderEdit/:id(\\d+)',
        name: 'RawMaterialSalesOrderEdit',
        title: '编辑原料销售出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料销售出货单'],
          title: '编辑原料销售出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesOrder/edit.vue'),
      },
      {
        path: 'rawMaterialSalesOrderDetail/:id(\\d+)',
        name: 'RawMaterialSalesOrderDetail',
        title: '原料销售出货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料销售出货单详情'],
          title: '原料销售出货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesOrder/detail.vue'),
      },
      {
        path: 'rawMaterialSalesReturnOrder',
        name: 'RawMaterialSalesReturnOrder',
        title: '原料销售退货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料销售退货单'],
          title: '原料销售退货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesReturnOrder/index.vue'),
      },
      {
        path: 'rawMaterialSalesReturnOrderAdd',
        name: 'RawMaterialSalesReturnOrderAdd',
        title: '新建原料销售退货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料销售退货单'],
          title: '新建原料销售退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesReturnOrder/add.vue'),
      },
      {
        path: 'rawMaterialSalesReturnOrderEdit/:id(\\d+)',
        name: 'RawMaterialSalesReturnOrderEdit',
        title: '编辑原料销售退货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料销售退货单'],
          title: '编辑原料销售退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesReturnOrder/edit.vue'),
      },
      {
        path: 'rawMaterialSalesReturnOrderDetail/:id(\\d+)',
        name: 'RawMaterialSalesReturnOrderDetail',
        title: '原料销售退货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料销售退货单详情'],
          title: '原料销售退货单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeRawSaleReturn,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialSalesReturnOrder/detail.vue'),
      },
      {
        path: 'rawMaterialStockAdjustment',
        name: 'RawMaterialStockAdjustment',
        title: '原料库存调整单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料库存调整单'],
          title: '原料库存调整单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialStockAdjustment/index.vue'),
      },
      {
        path: 'rawMaterialStockAdjustmentAdd',
        name: 'RawMaterialStockAdjustmentAdd',
        title: '新建原料库存调整单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料库存调整单'],
          title: '新建原料库存调整单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialStockAdjustment/add.vue'),
      },
      {
        path: 'rawMaterialStockAdjustmentEdit/:id(\\d+)',
        name: 'RawMaterialStockAdjustmentEdit',
        title: '编辑原料库存调整单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料库存调整单'],
          title: '编辑原料库存调整单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialStockAdjustment/edit.vue'),
      },
      {
        path: 'rawMaterialStockAdjustmentDetail/:id(\\d+)',
        name: 'RawMaterialStockAdjustmentDetail',
        title: '原料库存调整单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料库存调整单详情'],
          title: '原料库存调整单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialStockAdjustment/detail.vue'),
      },
      {
        path: 'rawMaterialInventoryCountSheet',
        name: 'RawMaterialInventoryCountSheet',
        title: '原料库存盘点单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料库存盘点单'],
          title: '原料库存盘点单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialInventoryCountSheet/index.vue'),
      },
      {
        path: 'rawMaterialInventoryCountSheetAdd',
        name: 'RawMaterialInventoryCountSheetAdd',
        title: '新建原料库存盘点单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料库存盘点单'],
          title: '新建原料库存盘点单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialInventoryCountSheet/add.vue'),
      },
      {
        path: 'rawMaterialInventoryCountSheetEdit/:id(\\d+)',
        name: 'RawMaterialInventoryCountSheetEdit',
        title: '编辑原料库存盘点单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料库存盘点单'],
          title: '编辑原料库存盘点单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialInventoryCountSheet/edit.vue'),
      },
      {
        path: 'rawMaterialInventoryCountSheetDetail/:id(\\d+)',
        name: 'RawMaterialInventoryCountSheetDetail',
        title: '原料库存盘点单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料库存盘点单详情'],
          title: '原料库存盘点单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialInventoryCountSheet/detail.vue'),
      },
      {
        path: 'rawMaterialInventoryTable',
        name: 'RawMaterialInventoryTable',
        title: '原料库存',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料库存'],
          title: '原料库存',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialInventoryTable/index.vue'),
      },
      {
        path: 'rawMaterialProcessingReceipt',
        name: 'RawMaterialProcessingReceipt',
        title: '原料加工收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工收货单'],
          title: '原料加工收货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReceipt/index.vue'),
      },
      {
        path: 'rawMaterialProcessingReceiptAdd',
        name: 'RawMaterialProcessingReceiptAdd',
        title: '新建原料加工收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料加工收货单'],
          title: '新建原料加工收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReceipt/add.vue'),
      },
      {
        path: 'rawMaterialProcessingReceiptEdit',
        name: 'RawMaterialProcessingReceiptEdit',
        title: '编辑原料加工收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料加工收货单'],
          title: '编辑原料加工收货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReceipt/edit.vue'),
      },
      {
        path: 'rawMaterialProcessingReceiptDetail',
        name: 'RawMaterialProcessingReceiptDetail',
        title: '原料加工收货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工收货单详情'],
          title: '原料加工收货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReceipt/detail.vue'),
      },

      {
        path: 'rawMaterialProcessingInvoice',
        name: 'RawMaterialProcessingInvoice',
        title: '原料加工出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工出货单'],
          title: '原料加工出货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingInvoice/index.vue'),
      },
      {
        path: 'rawMaterialProcessingInvoiceAdd',
        name: 'RawMaterialProcessingInvoiceAdd',
        title: '新建原料加工出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料加工出货单'],
          title: '新建原料加工出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingInvoice/add.vue'),
      },
      {
        path: 'rawMaterialProcessingInvoiceEdit',
        name: 'RawMaterialProcessingInvoiceEdit',
        title: '编辑原料加工出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料加工出货单'],
          title: '编辑原料加工出货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingInvoice/edit.vue'),
      },
      {
        path: 'rawMaterialProcessingInvoiceDetail',
        name: 'RawMaterialProcessingInvoiceDetail',
        title: '原料加工出货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工出货单详情'],
          title: '原料加工出货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingInvoice/detail.vue'),
      },
      {
        path: 'rawMaterialProcessingDeductionShippingOrderList',
        name: 'RawMaterialProcessingDeductionShippingOrderList',
        title: '原料加工扣款出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工扣款出货单'],
          title: '原料加工扣款出货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingDeductionShippingOrder/index.vue'),
      },
      {
        path: 'rawMaterialProcessingDeductionShippingOrderAdd',
        name: 'RawMaterialProcessingDeductionShippingOrderAdd',
        title: '新增原料加工扣款出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料加工扣款出货单'],
          title: '新建原料加工扣款出货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingDeductionShippingOrder/addOrEdit.vue'),
      },
      {
        path: 'rawMaterialProcessingDeductionShippingOrderUpdate',
        name: 'RawMaterialProcessingDeductionShippingOrderUpdate',
        title: '编辑原料加工扣款出货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料加工扣款出货单'],
          title: '编辑原料加工扣款出货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingDeductionShippingOrder/addOrEdit.vue'),
      },
      {
        path: 'rawMaterialProcessingDeductionShippingOrderDetail',
        name: 'RawMaterialProcessingDeductionShippingOrderDetail',
        title: '原料加工扣款出货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工扣款出货单详情'],
          title: '原料加工扣款出货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingDeductionShippingOrder/detail.vue'),
      },
      {
        path: 'rawMaterialProcessingReturnReceiptList',
        name: 'RawMaterialProcessingReturnReceiptList',
        title: '原料加工退货收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工退货收货单'],
          title: '原料加工退货收货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReturnReceipt/index.vue'),
      },
      {
        path: 'rawMaterialProcessingReturnReceiptAdd',
        name: 'RawMaterialProcessingReturnReceiptAdd',
        title: '新建原料加工退货收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '新建原料加工退货收货单'],
          title: '新建原料加工退货收货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReturnReceipt/addOrEdit.vue'),
      },
      {
        path: 'rawMaterialProcessingReturnReceiptUpdate',
        name: 'RawMaterialProcessingReturnReceiptUpdate',
        title: '编辑原料加工退货收货单',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料加工退货收货单'],
          title: '编辑原料加工退货收货单',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReturnReceipt/addOrEdit.vue'),
      },
      {
        path: 'rawMaterialProcessingReturnReceiptDetail',
        name: 'RawMaterialProcessingReturnReceiptDetail',
        title: '原料加工退货收货单详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料加工退货收货单详情'],
          title: '原料加工退货收货单详情',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialProcessingReturnReceipt/detail.vue'),
      },
      {
        path: 'rawMaterialDmhList',
        name: 'RawMaterialDmhList',
        title: '原料染整费应付账',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料染整费应付账'],
          title: '原料染整费应付账',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialDmh/index.vue'),
      },
      {
        path: 'rawMaterialDmhEdit',
        name: 'RawMaterialDmhEdit',
        title: '编辑原料染整费应付账',
        hideType: true,
        meta: {
          navName: ['原料管理', '编辑原料染整费应付账'],
          title: '编辑原料染整费应付账',
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialDmh/edit.vue'),
      },
      {
        path: 'rawMaterialDmhDetail',
        name: 'RawMaterialDmhDetail',
        title: '原料染整费应付账详情',
        hideType: true,
        meta: {
          navName: ['原料管理', '原料染整费应付账详情'],
          title: '原料染整费应付账详情',
          orderType: OrderTypeEnum.OrderTypeRawDNF,
        },
        component: async () => await import('@/pages/rawMaterialManagement/rawMaterialDmh/detail.vue'),
      },
    ],
  },
  {
    path: '/productionManagement',
    name: 'ProductionManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/productionManagement/sheetOfProductionPlan',
    meta: {
      navName: ['生产管理'],
      title: '生产管理',
    },
    children: [
      {
        path: 'sheetOfProductionPlan',
        name: 'SheetOfProductionPlan',
        title: '生产计划单',
        meta: {
          navName: ['生产管理', '生产计划单', '生产计划单列表'],
          title: '生产计划单列表',
        },
        component: async () => await import('@/pages/productionManagement/sheetOfProductionPlan/index.vue'),
      },
      {
        path: 'sheetOfProductionPlanAdd',
        name: 'SheetOfProductionPlanAdd',
        title: '新建生产计划单',
        meta: {
          navName: ['生产管理', '生产计划单', '新建生产计划单'],
          title: '新建生产计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/sheetOfProductionPlan/add.vue'),
      },
      {
        path: 'sheetOfProductionPlanEdit',
        name: 'SheetOfProductionPlanEdit',
        title: '编辑生产计划单',
        meta: {
          navName: ['生产管理', '生产计划单', '编辑生产计划单'],
          title: '编辑生产计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/sheetOfProductionPlan/edit.vue'),
      },
      {
        path: 'sheetOfProductionPlanDetail',
        name: 'SheetOfProductionPlanDetail',
        title: '生产计划单详情',
        meta: {
          navName: ['生产管理', '生产计划单', '生产计划单详情'],
          title: '生产计划单详情',
        },
        component: async () => await import('@/pages/productionManagement/sheetOfProductionPlan/detail.vue'),
      },
      {
        path: 'productionNotice',
        name: 'ProductionNotice',
        title: '生产通知单',
        meta: {
          navName: ['生产管理', '生产通知单', '生产通知单列表'],
          keepAlive: true,
          title: '生产通知单列表',
        },
        component: async () => await import('@/pages/productionManagement/productionNotice/index.vue'),
      },
      {
        path: 'productionNoticeAdd',
        name: 'ProductionNoticeAdd',
        title: '新建生产通知单',
        meta: {
          navName: ['生产管理', '生产通知单', '新建生产通知单'],
          title: '新建生产通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/productionNotice/add.vue'),
      },
      {
        path: 'productionNoticeDetail',
        name: 'ProductionNoticeDetail',
        title: '生产通知单详情',
        meta: {
          navName: ['生产管理', '生产通知单', '生产通知单详情'],
          title: '生产通知单详情',
        },
        component: async () => await import('@/pages/productionManagement/productionNotice/detail.vue'),
      },
      {
        path: 'scheduleProduction',
        name: 'ScheduleProduction',
        title: '生产通知单',
        meta: {
          navName: ['生产管理', '生产排产单'],
          keepAlive: true,
          title: '生产排产单',
        },
        component: async () => await import('@/pages/productionManagement/scheduleProduction/index.vue'),
      },
      {
        path: 'quickScheduleProduction',
        name: 'QuickScheduleProduction',
        title: '快速排产单',
        meta: {
          navName: ['生产管理', '快速排产单'],
          keepAlive: true,
          title: '快速排产单',
        },
        component: async () => await import('@/pages/productionManagement/quickScheduleProduction/index.vue'),
      },
      {
        path: 'quickScheduleProductionEdit',
        name: 'QuickScheduleProductionEdit',
        title: '编辑快速排产单',
        hideType: true,
        meta: {
          navName: ['生产管理', '快速排产单', '编辑快速排产单'],
          title: '编辑快速排产单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/quickScheduleProduction/edit.vue'),
      },
      {
        path: 'quickScheduleProductionDetail',
        name: 'QuickScheduleProductionDetail',
        title: '快速排产单详情',
        hideType: true,
        meta: {
          navName: ['生产管理', '快速排产单', '快速排产单详情'],
          title: '快速排产单详情',
        },
        component: async () => await import('@/pages/productionManagement/quickScheduleProduction/detail.vue'),
      },
      {
        path: 'productionNoticeEdit',
        name: 'ProductionNoticeEdit',
        title: '生产通知单编辑',
        meta: {
          navName: ['生产管理', '生产通知单', '生产通知单编辑'],
          title: '生产通知单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/productionNotice/edit.vue'),
      },
      {
        path: 'productionChange',
        name: 'ProductionChange',
        title: '生产变更单',
        meta: {
          navName: ['生产管理', '生产变更单', '生产变更单列表'],
          title: '生产变更单',
        },
        component: async () => await import('@/pages/productionManagement/productionChange/index.vue'),
      },
      {
        path: 'productionChangeAdd',
        name: 'ProductionChangeAdd',
        title: '新建生产变更单',
        meta: {
          navName: ['生产管理', '生产变更单', '新建生产变更单'],
          title: '新建生产变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/productionChange/add.vue'),
      },
      {
        path: 'productionChangeDetail',
        name: 'ProductionChangeDetail',
        title: '生产变更单详情',
        meta: {
          navName: ['生产管理', '生产变更单', '生产变更单详情'],
          title: '生产变更单详情',
        },
        component: async () => await import('@/pages/productionManagement/productionChange/detail.vue'),
      },
      {
        path: 'productionChangeEdit',
        name: 'ProductionChangeEdit',
        title: '生产变更单编辑',
        meta: {
          navName: ['生产管理', '生产变更单', '生产变更单编辑'],
          title: '生产变更单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/productionChange/edit.vue'),
      },
      {
        path: 'underWeightOrder',
        name: 'UnderWeightOrder',
        title: '生产欠重单列表',
        meta: {
          navName: ['生产管理', '生产欠重单', '生产欠重单列表'],
          title: '生产欠重单列表',
        },
        component: async () => await import('@/pages/productionManagement/underWeightOrder/index.vue'),
      },
      {
        path: 'underWeightOrderAdd',
        name: 'UnderWeightOrderAdd',
        title: '新建生产欠重单',
        meta: {
          navName: ['生产管理', '生产欠重单', '新建生产欠重单'],
          title: '新建生产欠重单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/underWeightOrder/add.vue'),
      },
      {
        path: 'underWeightOrderDetail',
        name: 'UnderWeightOrderDetail',
        title: '生产欠重单详情',
        meta: {
          navName: ['生产管理', '生产欠重单', '生产欠重单详情'],
          title: '生产欠重单详情',
        },
        component: async () => await import('@/pages/productionManagement/underWeightOrder/detail.vue'),
      },
      {
        path: 'underWeightOrderEdit',
        name: 'UnderWeightOrderEdit',
        title: '生产欠重单编辑',
        meta: {
          navName: ['生产管理', '生产欠重单', '生产欠重单编辑'],
          title: '生产欠重单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/productionManagement/underWeightOrder/edit.vue'),
      },
    ],
  },
  {
    path: '/finishManagement',
    name: 'FinishManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/finishManagement/finishPurchaseWarehouseEntry',
    meta: {
      navName: ['成品管理'],
      title: '成品管理',
    },
    children: [
      {
        path: 'finishPurchaseWarehouseEntry',
        name: 'FinishPurchaseWarehouseEntry',
        title: '成品采购进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购进仓单列表'],
          title: '成品采购进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FinishPurchaseWarehouseEntry,
        },
        component: async () => await import('@/pages/finishManagement/finishPurchaseWarehouseEntry/index.vue'),
      },
      {
        path: 'finishPurchaseWarehouseEntryAdd',
        name: 'FinishPurchaseWarehouseEntryAdd',
        title: '新建成品采购进仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购进仓单', '新建成品采购进仓单'],
          title: '新建成品采购进仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/finishPurchaseWarehouseEntry/add.vue'),
      },
      {
        path: 'finishPurchaseWarehouseEntryEdit',
        name: 'FinishPurchaseWarehouseEntryEdit',
        title: '成品采购进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购进仓单', '成品采购进仓单编辑'],
          title: '成品采购进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/finishPurchaseWarehouseEntry/edit.vue'),
      },
      {
        path: 'finishPurchaseWarehouseEntryDetail',
        name: 'FinishPurchaseWarehouseEntryDetail',
        title: '成品采购进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购进仓单', '成品采购进仓单详情'],
          title: '成品采购进仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/finishPurchaseWarehouseEntry/detail.vue'),
      },
      {
        path: 'fpPurchaseReturnDeliverGodown',
        name: 'FpPurchaseReturnDeliverGodown',
        title: '成品采购退货出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购退货出仓单', '成品采购退货出仓单列表'],
          title: '成品采购退货出仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpPurchaseReturnDeliverGodown,
        },
        component: async () => await import('@/pages/finishManagement/fpPurchaseReturnDeliverGodown/index.vue'),
      },
      {
        path: 'fpPurchaseReturnDeliverGodownAdd',
        name: 'FpPurchaseReturnDeliverGodownAdd',
        title: '新建成品采购退货出仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购退货出仓单', '新建成品采购退货出仓单'],
          title: '新建成品采购退货出仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpPurchaseReturnDeliverGodown/add.vue'),
      },
      {
        path: 'fpPurchaseReturnDeliverGodownDetail',
        name: 'FpPurchaseReturnDeliverGodownDetail',
        title: '成品采购退货出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购退货出仓单', '成品采购退货出仓单详情'],
          title: '成品采购退货出仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpPurchaseReturnDeliverGodown/detail.vue'),
      },
      {
        path: 'fpPurchaseReturnDeliverGodownEdit',
        name: 'FpPurchaseReturnDeliverGodownEdit',
        title: '成品采购退货出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品采购退货出仓单', '成品采购退货出仓单编辑'],
          title: '成品采购退货出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpPurchaseReturnDeliverGodown/edit.vue'),
      },
      {
        path: 'fpSubscribeWarehouseOrder',
        name: 'FpSubscribeWarehouseOrder',
        title: '成品出仓预约单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品出仓预约单', '成品出仓预约单列表'],
          title: '成品出仓预约单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpSubscribeWarehouseOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpSubscribeWarehouseOrder/index.vue'),
      },
      {
        path: 'fpSubscribeWarehouseOrderAdd',
        name: 'FpSubscribeWarehouseOrderAdd',
        title: '新建成品出仓预约单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品出仓预约单', '新建成品出仓预约单'],
          title: '新建成品出仓预约单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSubscribeWarehouseOrder/add.vue'),
      },
      {
        path: 'fpSubscribeWarehouseOrderDetail',
        name: 'FpSubscribeWarehouseOrderDetail',
        title: '成品出仓预约单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品出仓预约单', '成品出仓预约单详情'],
          title: '成品出仓预约单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpSubscribeWarehouseOrder/detail.vue'),
      },
      {
        path: 'fpSubscribeWarehouseOrderEdit',
        name: 'FpSubscribeWarehouseOrderEdit',
        title: '成品出仓预约单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品出仓预约单', '成品出仓预约单编辑'],
          title: '成品出仓预约单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSubscribeWarehouseOrder/edit.vue'),
      },
      {
        path: 'cashCommodityClothOrder',
        name: 'CashCommodityClothOrder',
        title: '现货配布单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '现货配布单', '现货配布单列表'],
          title: '现货配布单列表',
          monthTransferOrder: MonthTransferOrderEnum.CashCommodityClothOrder,
        },
        component: async () => await import('@/pages/finishManagement/cashCommodityClothOrder/index.vue'),
      },
      {
        path: 'cashCommodityClothOrderEdit',
        name: 'CashCommodityClothOrderEdit',
        title: '现货配布单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '现货配布单', '现货配布单编辑'],
          title: '现货配布单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/cashCommodityClothOrder/edit.vue'),
      },
      {
        path: 'cashCommodityClothOrderDetail',
        name: 'CashCommodityClothOrderDetail',
        title: '现货配布单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '现货配布单', '现货配布单详情'],
          srcOrderType: SrcOrderType.SrcOrderTypeArrange,
          title: '现货配布单详情',
        },
        component: async () => await import('@/pages/finishManagement/cashCommodityClothOrder/detail.vue'),
      },
      {
        path: 'fpInteriorAllotDeliverFromGodownOrder',
        name: 'FpInteriorAllotDeliverFromGodownOrder',
        title: '成品内部调拨出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨出仓单', '成品内部调拨出仓单列表'],
          title: '成品内部调拨出仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpInteriorAllotDeliverFromGodownOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotDeliverFromGodownOrder/index.vue'),
      },
      {
        path: 'fpInteriorAllotDeliverFromGodownOrderAdd',
        name: 'FpInteriorAllotDeliverFromGodownOrderAdd',
        title: '成品内部调拨出仓单新建',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨出仓单', '成品内部调拨出仓单新建'],
          title: '成品内部调拨出仓单新建',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotDeliverFromGodownOrder/add.vue'),
      },
      {
        path: 'fpInteriorAllotDeliverFromGodownOrderDetail',
        name: 'FpInteriorAllotDeliverFromGodownOrderDetail',
        title: '成品内部调拨出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨出仓单', '成品内部调拨出仓单详情'],
          title: '成品内部调拨出仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotDeliverFromGodownOrder/detail.vue'),
      },
      {
        path: 'fpInteriorAllotDeliverFromGodownOrderEdit',
        name: 'FpInteriorAllotDeliverFromGodownOrderEdit',
        title: '成品内部调拨出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨出仓单', '成品内部调拨出仓单编辑'],
          title: '成品内部调拨出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotDeliverFromGodownOrder/edit.vue'),
      },
      {
        path: 'fpInteriorAllotWarehouseEntryOrder',
        name: 'FpInteriorAllotWarehouseEntryOrder',
        title: '成品内部调拨进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨进仓单', '成品内部调拨进仓单列表'],
          title: '成品内部调拨进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpInteriorAllotWarehouseEntryOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotWarehouseEntryOrder/index.vue'),
      },
      {
        path: 'fpInteriorAllotWarehouseEntryOrderDetail',
        name: 'FpInteriorAllotWarehouseEntryOrderDetail',
        title: '成品内部调拨进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨进仓单', '成品内部调拨进仓单详情'],
          title: '成品内部调拨进仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotWarehouseEntryOrder/detail.vue'),
      },
      {
        path: 'fpInteriorAllotWarehouseEntryOrderEdit',
        name: 'FpInteriorAllotWarehouseEntryOrderEdit',
        title: '成品内部调拨进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品内部调拨进仓单', '成品内部调拨进仓单编辑'],
          title: '成品内部调拨进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpInteriorAllotWarehouseEntryOrder/edit.vue'),
      },
      {
        path: 'fpProcessingDeliverFromGodownOrder',
        name: 'FpProcessingDeliverFromGodownOrder',
        title: '成品加工发料出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工发料出仓单', '成品加工发料出仓单列表'],
          title: '成品加工发料出仓单列表',
          monthTransferOrder: [MonthTransferOrderEnum.FpProcessingDeliverFromGodownOrder, MonthTransferOrderEnum.OrderTypeOutTypeRepair],
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingDeliverFromGodownOrder/index.vue'),
      },
      {
        path: 'fpProcessingDeliverFromGodownOrderAdd',
        name: 'FpProcessingDeliverFromGodownOrderAdd',
        title: '新建成品加工发料出仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工发料出仓单', '新建成品加工发料出仓单'],
          title: '新建成品加工发料出仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingDeliverFromGodownOrder/add.vue'),
      },
      {
        path: 'fpProcessingDeliverFromGodownOrderDetail',
        name: 'FpProcessingDeliverFromGodownOrderDetail',
        title: '成品加工发料出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工发料出仓单', '成品加工发料出仓单详情'],
          title: '成品加工发料出仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingDeliverFromGodownOrder/detail.vue'),
      },
      {
        path: 'fpProcessingDeliverFromGodownOrderEdit',
        name: 'FpProcessingDeliverFromGodownOrderEdit',
        title: '成品加工发料出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工发料出仓单', '成品加工发料出仓单编辑'],
          title: '成品加工发料出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingDeliverFromGodownOrder/edit.vue'),
      },
      {
        path: 'fpOtherDeliverFromGodownOrder',
        name: 'FpOtherDeliverFromGodownOrder',
        title: '成品其他出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他出仓单', '成品其他出仓单列表'],
          title: '成品其他出仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpOtherDeliverFromGodownOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpOtherDeliverFromGodownOrder/index.vue'),
      },
      {
        path: 'fpOtherDeliverFromGodownOrderAdd',
        name: 'FpOtherDeliverFromGodownOrderAdd',
        title: '新建成品其他出仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他出仓单', '新建成品其他出仓单'],
          title: '新建成品其他出仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpOtherDeliverFromGodownOrder/add.vue'),
      },
      {
        path: 'fpOtherDeliverFromGodownOrderDetail',
        name: 'FpOtherDeliverFromGodownOrderDetail',
        title: '成品其他出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他出仓单', '成品其他出仓单详情'],
          title: '成品其他出仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpOtherDeliverFromGodownOrder/detail.vue'),
      },
      {
        path: 'fpOtherDeliverFromGodownOrderEdit',
        name: 'FpOtherDeliverFromGodownOrderEdit',
        title: '成品其他出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他出仓单', '成品其他出仓单编辑'],
          title: '成品其他出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpOtherDeliverFromGodownOrder/edit.vue'),
      },
      {
        path: 'fpWithholdDeliverFromGodownOrder',
        name: 'FpWithholdDeliverFromGodownOrder',
        title: '成品扣款出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品扣款出仓单', '成品扣款出仓单列表'],
          title: '成品扣款出仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpWithholdDeliverFromGodownOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpWithholdDeliverFromGodownOrder/index.vue'),
      },
      {
        path: 'fpWithholdDeliverFromGodownOrderAdd',
        name: 'FpWithholdDeliverFromGodownOrderAdd',
        title: '新建成品扣款出仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品扣款出仓单', '新建成品扣款出仓单'],
          title: '新建成品扣款出仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpWithholdDeliverFromGodownOrder/add.vue'),
      },
      {
        path: 'fpWithholdDeliverFromGodownOrderDetail',
        name: 'FpWithholdDeliverFromGodownOrderDetail',
        title: '成品扣款出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品扣款出仓单', '成品扣款出仓单详情'],
          title: '成品扣款出仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpWithholdDeliverFromGodownOrder/detail.vue'),
      },
      {
        path: 'fpWithholdDeliverFromGodownOrderEdit',
        name: 'FpWithholdDeliverFromGodownOrderEdit',
        title: '成品扣款出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品扣款出仓单', '成品扣款出仓单编辑'],
          title: '成品扣款出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpWithholdDeliverFromGodownOrder/edit.vue'),
      },
      {
        path: 'fpSaleDeliverFromGodownOrder',
        name: 'FpSaleDeliverFromGodownOrder',
        title: '成品销售出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售出仓单', '成品销售出仓单列表'],
          title: '成品销售出仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpSaleDeliverFromGodownOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleDeliverFromGodownOrder/index.vue'),
      },
      {
        path: 'fpSaleDeliverFromGodownOrderAdd',
        name: 'FpSaleDeliverFromGodownOrderAdd',
        title: '新建成品销售出仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售出仓单', '新建成品销售出仓单'],
          title: '新建成品销售出仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleDeliverFromGodownOrder/add.vue'),
      },
      {
        path: 'fpSaleDeliverFromGodownOrderDetail',
        name: 'FpSaleDeliverFromGodownOrderDetail',
        title: '成品销售出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售出仓单', '成品销售出仓单详情'],
          title: '成品销售出仓单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeProductSaleOut,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleDeliverFromGodownOrder/detail.vue'),
      },
      {
        path: 'fpSaleDeliverFromGodownOrderEdit',
        name: 'FpSaleDeliverFromGodownOrderEdit',
        title: '成品销售出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售出仓单', '成品销售出仓单编辑'],
          title: '成品销售出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleDeliverFromGodownOrder/edit.vue'),
      },
      {
        path: 'fpSaleEntryOrder',
        name: 'FpSaleEntryOrder',
        title: '成品销售退货进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售退货进仓单', '成品销售退货进仓单列表'],
          title: '成品销售退货进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpSaleEntryOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleEntryOrder/index.vue'),
      },
      {
        path: 'fpSaleEntryOrderAdd',
        name: 'FpSaleEntryOrderAdd',
        title: '新建成品销售退货进仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售退货进仓单', '新建成品销售退货进仓单'],
          title: '新建成品销售退货进仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleEntryOrder/add.vue'),
      },
      {
        path: 'fpSaleEntryOrderDetail',
        name: 'FpSaleEntryOrderDetail',
        title: '成品销售退货进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售退货进仓单', '成品销售退货进仓单详情'],
          title: '成品销售退货进仓单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeProductSaleReturn,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleEntryOrder/detail.vue'),
      },
      {
        path: 'fpSaleEntryOrderEdit',
        name: 'FpSaleEntryOrderEdit',
        title: '成品销售退货进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售退货进仓单', '成品销售退货进仓单编辑'],
          title: '成品销售退货进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleEntryOrder/edit.vue'),
      },
      {
        path: 'fpOtherEntryOrder',
        name: 'FpOtherEntryOrder',
        title: '成品其他进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他进仓单', '成品其他进仓单列表'],
          title: '成品其他进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpOtherEntryOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpOtherEntryOrder/index.vue'),
      },
      {
        path: 'fpOtherEntryOrderAdd',
        name: 'FpOtherEntryOrderAdd',
        title: '新建成品其他进仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他进仓单', '新建成品其他进仓单'],
          title: '新建成品其他进仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpOtherEntryOrder/add.vue'),
      },
      {
        path: 'fpOtherEntryOrderDetail',
        name: 'FpOtherEntryOrderDetail',
        title: '成品其他进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他进仓单', '成品其他进仓单详情'],
          title: '成品其他进仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpOtherEntryOrder/detail.vue'),
      },
      {
        path: 'fpOtherEntryOrderEdit',
        name: 'FpOtherEntryOrderEdit',
        title: '成品其他进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品其他进仓单', '成品其他进仓单编辑'],
          title: '成品其他进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpOtherEntryOrder/edit.vue'),
      },
      {
        path: 'fpProcessingEntryOrder',
        name: 'FpProcessingEntryOrder',
        title: '成品加工进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工进仓单', '成品加工进仓单列表'],
          title: '成品加工进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpProcessingEntryOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingEntryOrder/index.vue'),
      },
      {
        path: 'fpProcessingEntryOrderAdd',
        name: 'FpProcessingEntryOrderAdd',
        title: '新建成品加工进仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工进仓单', '新建成品加工进仓单'],
          title: '新建成品加工进仓单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingEntryOrder/add.vue'),
      },
      {
        path: 'fpProcessingEntryOrderDetail',
        name: 'FpProcessingEntryOrderDetail',
        title: '成品加工进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工进仓单', '成品加工进仓单详情'],
          title: '成品加工进仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingEntryOrder/detail.vue'),
      },
      {
        path: 'fpProcessingEntryOrderEdit',
        name: 'FpProcessingEntryOrderEdit',
        title: '成品加工进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工进仓单', '成品加工进仓单编辑'],
          title: '成品加工进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingEntryOrder/edit.vue'),
      },
      {
        path: 'fpProcessingReturnEntryOrder',
        name: 'FpProcessingReturnEntryOrder',
        title: '成品加工退料进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工退料进仓单', '成品加工退料进仓单列表'],
          title: '成品加工退料进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpProcessingReturnEntryOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingReturnEntryOrder/index.vue'),
      },
      {
        path: 'fpProcessingReturnEntryOrderAdd',
        name: 'FpProcessingReturnEntryOrderAdd',
        title: '新建成品加工退料进仓单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工退料进仓单', '新建成品加工退料进仓单'],
          title: '新建成品加工退料进仓单',
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingReturnEntryOrder/add.vue'),
      },
      {
        path: 'fpProcessingReturnEntryOrderDetail',
        name: 'FpProcessingReturnEntryOrderDetail',
        title: '成品加工退料进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工退料进仓单', '成品加工退料进仓单详情'],
          title: '成品加工退料进仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingReturnEntryOrder/detail.vue'),
      },
      {
        path: 'fpProcessingReturnEntryOrderEdit',
        name: 'FpProcessingReturnEntryOrderEdit',
        title: '成品加工退料进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品加工退料进仓单', '成品加工退料进仓单编辑'],
          title: '成品加工退料进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpProcessingReturnEntryOrder/edit.vue'),
      },
      {
        path: 'fpStockCheckOrder',
        name: 'FpStockCheckOrder',
        title: '成品库存盘点单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存盘点单', '成品库存盘点单列表'],
          title: '成品库存盘点单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpStockCheckOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpStockCheckOrder/index.vue'),
      },
      {
        path: 'fpStockCheckOrderAdd',
        name: 'FpStockCheckOrderAdd',
        title: '新建成品库存盘点单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存盘点单', '新建成品库存盘点单'],
          title: '新建成品库存盘点单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpStockCheckOrder/add.vue'),
      },
      {
        path: 'fpStockCheckOrderEdit',
        name: 'FpStockCheckOrderEdit',
        title: '成品库存盘点单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存盘点单', '成品库存盘点单编辑'],
          title: '成品库存盘点单编辑',
        },
        component: async () => await import('@/pages/finishManagement/fpStockCheckOrder/edit.vue'),
      },
      {
        path: 'fpStockCheckOrderDetail',
        name: 'FpStockCheckOrderDetail',
        title: '成品库存盘点单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存盘点单', '成品库存盘点单详情'],
          title: '成品库存盘点单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpStockCheckOrder/detail.vue'),
      },
      {
        path: 'fpStockAdjustOrder',
        name: 'FpStockAdjustOrder',
        title: '成品库存调整单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存调整单', '成品库存调整单列表'],
          title: '成品库存调整单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpStockAdjustOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpStockAdjustOrder/index.vue'),
      },
      {
        path: 'fpStockAdjustOrderAdd',
        name: 'FpStockAdjustOrderAdd',
        title: '新建成品库存调整单',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存调整单', '新建成品库存调整单'],
          title: '新建成品库存调整单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpStockAdjustOrder/add.vue'),
      },
      {
        path: 'fpStockAdjustOrderEdit',
        name: 'FpStockAdjustOrderEdit',
        title: '成品库存调整单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存调整单', '成品库存调整单编辑'],
          title: '成品库存调整单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpStockAdjustOrder/edit.vue'),
      },
      {
        path: 'fpStockAdjustOrderDetail',
        name: 'FpStockAdjustOrderDetail',
        title: '成品库存调整单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存调整单', '成品库存调整单详情'],
          title: '成品库存调整单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpStockAdjustOrder/detail.vue'),
      },
      {
        path: 'saleCashCommodityClothChangeOrder',
        name: 'SaleCashCommodityClothChangeOrder',
        title: '销售配布变更单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '销售配布变更单', '销售配布变更单列表'],
          title: '销售配布变更单列表',
        },
        component: async () => await import('@/pages/finishManagement/saleCashCommodityClothChangeOrder/index.vue'),
      },
      {
        path: 'saleCashCommodityClothChangeOrderAdd',
        name: 'SaleCashCommodityClothChangeOrderAdd',
        title: '销售配布变更单新建',
        hideType: true,
        meta: {
          navName: ['成品管理', '销售配布变更单', '销售配布变更单新建'],
          title: '销售配布变更单新建',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/saleCashCommodityClothChangeOrder/add.vue'),
      },
      {
        path: 'saleCashCommodityClothChangeOrderEdit',
        name: 'SaleCashCommodityClothChangeOrderEdit',
        title: '销售配布变更单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '销售配布变更单', '销售配布变更单编辑'],
          title: '销售配布变更单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/saleCashCommodityClothChangeOrder/edit.vue'),
      },
      {
        path: 'saleCashCommodityClothChangeOrderDetail',
        name: 'SaleCashCommodityClothChangeOrderDetail',
        title: '销售配布变更单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '销售配布变更单', '销售配布变更单详情'],
          title: '销售配布变更单详情',
        },
        component: async () => await import('@/pages/finishManagement/saleCashCommodityClothChangeOrder/detail.vue'),
      },
      {
        path: 'fpSaleAllotReturnDeliverGodownOrder',
        name: 'FpSaleAllotReturnDeliverGodownOrder',
        title: '成品销售调拨出仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售调拨出仓单', '成品销售调拨出仓单列表'],
          title: '成品销售调拨出仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpSaleAllotReturnDeliverGodownOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleAllotReturnDeliverGodownOrder/index.vue'),
      },
      {
        path: 'fpSaleAllotReturnDeliverGodownOrderEdit',
        name: 'FpSaleAllotReturnDeliverGodownOrderEdit',
        title: '成品销售调拨出仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售调拨出仓单', '成品销售调拨出仓单编辑'],
          title: '成品销售调拨出仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleAllotReturnDeliverGodownOrder/edit.vue'),
      },
      {
        path: 'fpSaleAllotReturnDeliverGodownOrderDetail',
        name: 'FpSaleAllotReturnDeliverGodownOrderDetail',
        title: '成品销售调拨出仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售调拨出仓单', '成品销售调拨出仓单详情'],
          title: '成品销售调拨出仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpSaleAllotReturnDeliverGodownOrder/detail.vue'),
      },
      {
        path: 'fpSaleAllotEntryGodownOrder',
        name: 'FpSaleAllotEntryGodownOrder',
        title: '成品销售调拨进仓单列表',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售调拨进仓单', '成品销售调拨进仓单列表'],
          title: '成品销售调拨进仓单列表',
          monthTransferOrder: MonthTransferOrderEnum.FpSaleAllotEntryGodownOrder,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleAllotEntryGodownOrder/index.vue'),
      },
      {
        path: 'fpSaleAllotEntryGodownOrderEdit',
        name: 'FpSaleAllotEntryGodownOrderEdit',
        title: '成品销售调拨进仓单编辑',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售调拨进仓单', '成品销售调拨进仓单编辑'],
          title: '成品销售调拨进仓单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/fpSaleAllotEntryGodownOrder/edit.vue'),
      },
      {
        path: 'fpSaleAllotEntryGodownOrderDetail',
        name: 'FpSaleAllotEntryGodownOrderDetail',
        title: '成品销售调拨进仓单详情',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品销售调拨进仓单', '成品销售调拨进仓单详情'],
          title: '成品销售调拨进仓单详情',
        },
        component: async () => await import('@/pages/finishManagement/fpSaleAllotEntryGodownOrder/detail.vue'),
      },
      {
        path: 'finishedGoodsInventory',
        name: 'FinishedGoodsInventory',
        title: '成品库存',
        hideType: true,
        meta: {
          navName: ['成品管理', '成品库存'],
          title: '成品库存',
          keepAlive: true,
        },
        component: async () => await import('@/pages/finishManagement/finishedGoodsInventory/index.vue'),
      },
      {
        path: 'finishedProductReport',
        name: 'FinishedProductReport',
        title: '成品仓日报表',
        hideType: true,
        meta: {
          navName: ['库存', '成品', '成品仓日报表'],
          title: '成品仓日报表',
        },
        component: async () => await import('@/pages/inventory/finishedProduct/finishedProductReport/index.vue'),
      },
      {
        path: 'finishedProductMonthReport',
        name: 'FinishedProductMonthReport',
        title: '成品仓月结表',
        hideType: true,
        meta: {
          navName: ['库存', '成品', '成品仓月结表'],
          title: '成品仓月结表',
        },
        component: async () => await import('@/pages/inventory/finishedProduct/finishedProductMonthReport/index.vue'),
      },
    ],
  },
  {
    path: '/salePriceManagement',
    name: 'SalePriceManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/salePriceManagement/pricingLevel',
    meta: {
      navName: ['价格管理'],
      title: '价格管理',
    },
    children: [
      {
        path: 'salePricingLevel',
        name: 'SalePricingLevel',
        title: '销售定价等级',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售定价等级'],
          title: '销售定价等级',
        },
        component: async () => await import('@/pages/salePriceManagement/salePricingLevel/index.vue'),
      },
      {
        path: 'salesPriceList',
        name: 'SalesPriceList',
        title: '销售价目表',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售价目表'],
          title: '销售价目表',
        },
        component: async () => await import('@/pages/salePriceManagement/SalesPriceList/index.vue'),
      },
      {
        path: 'salesPriceAdjustmentOrderAdd',
        name: 'SalesPriceAdjustmentOrderAdd',
        title: '销售调价单调价',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售调价单调价'],
          title: '销售调价单调价',
          // keepAlive: true,
        },
        component: async () => await import('@/pages/salePriceManagement/salesPriceAdjustmentOrder/add.vue'),
      },
      {
        path: 'salesPriceAdjustmentOrderCreate',
        name: 'SalesPriceAdjustmentOrderCreate',
        title: '销售调价单新建',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售调价单新建'],
          title: '销售调价单新建',
          // keepAlive: true,
        },
        component: async () => await import('@/pages/salePriceManagement/salesPriceAdjustmentOrder/create.vue'),
      },
      {
        path: 'salesPriceAdjustmentOrder',
        name: 'SalesPriceAdjustmentOrder',
        title: '销售调价单',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售调价单'],
          title: '销售调价单',
        },
        component: async () => await import('@/pages/salePriceManagement/salesPriceAdjustmentOrder/index.vue'),
      },
      {
        path: 'salesPriceAdjustmentOrderEdit/:id(\\d+)',
        name: 'SalesPriceAdjustmentOrderEdit',
        title: '销售调价单编辑',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售调价单编辑'],
          title: '销售调价单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/salePriceManagement/salesPriceAdjustmentOrder/edit.vue'),
      },
      {
        path: 'salesPriceAdjustmentOrderDetail/:id(\\d+)',
        name: 'SalesPriceAdjustmentOrderDetail',
        title: '销售调价单详情',
        hideType: true,
        meta: {
          navName: ['价格管理', '销售调价单详情'],
          title: '销售调价单详情',
        },
        component: async () => await import('@/pages/salePriceManagement/salesPriceAdjustmentOrder/detail.vue'),
      },
      {
        path: 'customerLevelPricing',
        name: 'CustomerLevelPricing',
        title: '客户等级定价',
        hideType: true,
        meta: {
          navName: ['价格管理', '客户等级定价'],
          title: '客户等级定价',
        },
        component: async () => await import('@/pages/salePriceManagement/customerLevelPricing/index.vue'),
      },
    ],
  },
  {
    path: '/saleManagement',
    name: 'SaleManagement',
    component: LayOut,
    redirect: '/saleManagement/productSale',
    children: [
      {
        path: 'productSale',
        name: 'ProductSale',
        title: '成品销售单',
        hideType: true,
        meta: {
          navName: ['销售管理', '成品销售单'],
          title: '成品销售单',
          isRequireAuth: true,
          monthTransferOrder: MonthTransferOrderEnum.ProductSale,
          KeepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSale/index.vue'),
      },
      {
        path: 'productSaleAdd',
        name: 'ProductSaleAdd',
        title: '新建成品销售单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建成品销售单'],
          title: '新建成品销售单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSale/add.vue'),
      },
      {
        path: 'productSaleEdit',
        name: 'ProductSaleEdit',
        title: '编辑成品销售单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑成品销售单'],
          title: '编辑成品销售单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSale/edit.vue'),
      },
      {
        path: 'productSaleDetail',
        name: 'ProductSaleDetail',
        title: '成品销售单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '成品销售单详情'],
          title: '成品销售单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeProductSale,
        },
        component: async () => await import('@/pages/saleManagement/productSale/detail.vue'),
      },
      {
        path: 'productOutStock',
        name: 'ProductOutStock',
        title: '成品欠货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '成品欠货单'],
          title: '成品欠货单',
        },
        component: async () => await import('@/pages/saleManagement/productOutStock/index.vue'),
      },
      {
        path: 'productOutStockAdd',
        name: 'ProductOutStockAdd',
        title: '新建成品欠货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建成品欠货单'],
          title: '新建成品欠货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productOutStock/add.vue'),
      },
      {
        path: 'productOutStockEdit',
        name: 'ProductOutStockEdit',
        title: '编辑成品欠货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑成品欠货单'],
          title: '编辑成品欠货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productOutStock/edit.vue'),
      },
      {
        path: 'productOutStockDetail',
        name: 'ProductOutStockDetail',
        title: '成品欠货单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '成品欠货单详情'],
          title: '成品欠货单详情',
        },
        component: async () => await import('@/pages/saleManagement/productOutStock/detail.vue'),
      },
      {
        path: 'productSalePlan',
        name: 'ProductSalePlan',
        title: '销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售计划单'],
          title: '销售计划单',
        },
        component: async () => await import('@/pages/saleManagement/productSalePlan/index.vue'),
      },
      {
        path: 'productSalePlanAdd',
        name: 'ProductSalePlanAdd',
        title: '新建成品销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建成品销售计划单'],
          title: '新建成品销售计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSalePlan/add.vue'),
      },
      {
        path: 'productSalePlanEdit',
        name: 'ProductSalePlanEdit',
        title: '编辑成品销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑成品销售计划单'],
          title: '编辑成品销售计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSalePlan/edit.vue'),
      },
      {
        path: 'productSalePlanDetail',
        name: 'ProductSalePlanDetail',
        title: '成品销售计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '成品销售计划单详情'],
          title: '成品销售计划单详情',
        },
        component: async () => await import('@/pages/saleManagement/productSalePlan/detail.vue'),
      },
      {
        path: 'greySalePlanAdd',
        name: 'GreySalePlanAdd',
        title: '新建坯布销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建坯布销售计划单'],
          title: '新建坯布销售计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/greySalePlan/add.vue'),
      },
      {
        path: 'greySalePlanEdit',
        name: 'GreySalePlanEdit',
        title: '编辑坯布销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑坯布销售计划单'],
          title: '编辑坯布销售计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/greySalePlan/edit.vue'),
      },
      {
        path: 'greySalePlanDetail',
        name: 'GreySalePlanDetail',
        title: '坯布销售计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '坯布销售计划单详情'],
          title: '坯布销售计划单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeFabricSale,
        },
        component: async () => await import('@/pages/saleManagement/greySalePlan/detail.vue'),
      },
      {
        path: 'rawSalePlanAdd',
        name: 'RawSalePlanAdd',
        title: '新建原料销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建原料销售计划单'],
          title: '新建原料销售计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/rawSalePlan/add.vue'),
      },
      {
        path: 'rawSalePlanEdit',
        name: 'RawSalePlanEdit',
        title: '编辑原料销售计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑原料销售计划单'],
          title: '编辑原料销售计划单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/rawSalePlan/edit.vue'),
      },
      {
        path: 'rawSalePlanDetail',
        name: 'RawSalePlanDetail',
        title: '原料销售计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '原料销售计划单详情'],
          title: '原料销售计划单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeRawSale,
        },
        component: async () => await import('@/pages/saleManagement/rawSalePlan/detail.vue'),
      },
      {
        path: 'productSalePlanChange',
        name: 'ProductSalePlanChange',
        title: '销售计划变更单',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售计划变更单'],
          title: '销售计划变更单',
        },
        component: async () => await import('@/pages/saleManagement/productSalePlanChange/index.vue'),
      },
      {
        path: 'salePlanChangeAdd',
        name: 'SalePlanChangeAdd',
        title: '新建销售计划变更单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建销售计划变更单'],
          title: '新建销售计划变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSalePlanChange/detailMerge.vue'),
      },
      {
        path: 'salePlanChangeEdit',
        name: 'SalePlanChangeEdit',
        title: '编辑销售计划变更单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑销售计划变更单'],
          title: '编辑销售计划变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productSalePlanChange/detailMerge.vue'),
      },
      {
        path: 'salePlanChangeDetail',
        name: 'SalePlanChangeDetail',
        title: '销售计划变更单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售计划变更单详情'],
          title: '销售计划变更单详情',
        },
        component: async () => await import('@/pages/saleManagement/productSalePlanChange/detailMerge.vue'),
      },
      {
        path: 'saleDeliver',
        name: 'SaleDeliver',
        title: '销售送货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售送货单'],
          title: '销售送货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/saleDeliver/index.vue'),
      },
      {
        path: 'saleDeliverEdit',
        name: 'SaleDeliverEdit',
        title: '编辑销售送货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑销售送货单'],
          title: '编辑销售送货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/saleDeliver/edit.vue'),
      },
      {
        path: 'saleDeliverDetail',
        name: 'SaleDeliverDetail',
        title: '销售送货单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售送货单详情'],
          title: '销售送货单详情',
          collectType: CollectTypeEnum.CollectTypeProductSale,
        },
        component: async () => await import('@/pages/saleManagement/saleDeliver/detail.vue'),
      },
      {
        path: 'saleReturn',
        name: 'SaleReturn',
        title: '销售退货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售退货单'],
          title: '销售退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/saleReturn/index.vue'),
      },
      {
        path: 'saleReturnEdit',
        name: 'SaleReturnEdit',
        title: '编辑销售退货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑销售退货单'],
          title: '编辑销售退货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/saleReturn/edit.vue'),
      },
      {
        path: 'saleReturnDetail',
        name: 'SaleReturnDetail',
        title: '销售退货单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售退货单详情'],
          title: '销售退货单详情',
          collectType: CollectTypeEnum.CollectTypeProductReturn,
        },
        component: async () => await import('@/pages/saleManagement/saleReturn/detail.vue'),
      },
      {
        path: 'materialPlan',
        name: 'MaterialPlan',
        title: '物料计划单',
        hideType: true,
        meta: {
          navName: ['销售管理', '物料计划单'],
          title: '物料计划单',
        },
        component: async () => await import('@/pages/saleManagement/materialPlan/index.vue'),
      },
      // {
      //   path: 'materialPlanEdit',
      //   name: 'MaterialPlanEdit',
      //   title: '物料计划单编辑',
      //   hideType: true,
      //   meta: {
      //     navName: ['销售管理', '物料计划单编辑'],
      //     title: '物料计划单编辑',
      //     keepAlive: true,
      //   },
      //   component: async () => await import('@/pages/saleManagement/materialPlan/edit.vue'),
      // },
      // {
      //   path: 'materialPlanDetail',
      //   name: 'MaterialPlanDetail',
      //   title: '物料计划单详情',
      //   hideType: true,
      //   meta: {
      //     navName: ['销售管理', '物料计划单详情'],
      //     title: '物料计划单详情',
      //   },
      //   component: async () => await import('@/pages/saleManagement/materialPlan/detail.vue'),
      // },
      {
        path: 'productMaterialPlanEdit',
        name: 'ProductMaterialPlanEdit',
        title: '物料计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '物料计划单详情'],
          title: '物料计划单详情',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/productMaterialPlan/edit.vue'),
      },
      {
        path: 'greyMaterialPlanEdit',
        name: 'GreyMaterialPlanEdit',
        title: '物料计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '物料计划单详情'],
          title: '物料计划单详情',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/greyMaterialPlan/edit.vue'),
      },
      {
        path: 'rawMaterialPlanEdit',
        name: 'RawMaterialPlanEdit',
        title: '物料计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '物料计划单详情'],
          title: '物料计划单详情',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/rawMaterialPlan/edit.vue'),
      },
      {
        path: 'rawMaterialPlanDetail',
        name: 'RawMaterialPlanDetail',
        title: '物料计划单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '物料计划单详情'],
          title: '物料计划单详情',
        },
        component: async () => await import('@/pages/saleManagement/rawMaterialPlan/detail.vue'),
      },
      {
        path: 'transferSales',
        name: 'TransferSales',
        title: '调货销售单',
        hideType: true,
        meta: {
          navName: ['销售管理', '调货销售单'],
          title: '调货销售单',
          isRequireAuth: true,
          monthTransferOrder: MonthTransferOrderEnum.TransferSales,
        },
        component: async () => await import('@/pages/saleManagement/transferSales/index.vue'),
      },
      {
        path: 'transferSalesAdd',
        name: 'TransferSalesAdd',
        title: '新建调货销售单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建调货销售单'],
          title: '新建调货销售单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/transferSales/add.vue'),
      },
      {
        path: 'returnProductsAdd',
        name: 'ReturnProductsAdd',
        title: '新建退货调货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '新建退货调货单'],
          title: '新建退货调货单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/transferSales/addReturnProducts.vue'),
      },
      {
        path: 'returnProductsDetail',
        name: 'ReturnProductsDetail',
        title: '退货单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '退货单详情'],
          title: '退货单详情',
          srcOrderType: SrcOrderType.SrcOrderTypeSaleTransferReturn,
        },
        component: async () => await import('@/pages/saleManagement/transferSales/ReturnProductsDetail.vue'),
      },
      {
        path: 'returnProductsEdit',
        name: 'ReturnProductsEdit',
        title: '编辑退货单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑退货单'],
          title: '编辑退货单',
        },
        component: async () => await import('@/pages/saleManagement/transferSales/ReturnProductsEdit.vue'),
      },
      {
        path: 'transferSalesEdit',
        name: 'TransferSalesEdit',
        title: '编辑调货销售单',
        hideType: true,
        meta: {
          navName: ['销售管理', '编辑调货销售单'],
          title: '编辑调货销售单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/saleManagement/transferSales/edit.vue'),
      },
      {
        path: 'transferSalesDetail',
        name: 'TransferSalesDetail',
        title: '调货销售单详情',
        hideType: true,
        meta: {
          navName: ['销售管理', '调货销售单详情'],
          title: '调货销售单详情',
          orderType: OrderTypeEnum.OrderTypeSaleTransfer,
          srcOrderType: SrcOrderType.SrcOrderTypeSaleTransfer,
        },
        component: async () => await import('@/pages/saleManagement/transferSales/detail.vue'),
      },
      {
        path: 'salesReport',
        name: 'SalesReport',
        title: '销售报表',
        hideType: true,
        meta: {
          navName: ['销售管理', '销售报表'],
          title: '销售报表',
        },
        component: async () => await import('@/pages/saleManagement/salesReport/index.vue'),
      },
    ],
  },
  {
    path: '/dyeingManagement',
    name: 'DyeingManagement',
    component: LayOut,
    icon: ArrowDown,
    redirect: '/dyeingNotice',
    meta: {
      navName: ['染整管理'],
      title: '染整管理',
    },
    children: [
      {
        path: 'rawStockTable',
        name: 'RawStockTable',
        title: '原料库存表',
        meta: {
          navName: ['染整管理', '原料库存表'],
          title: '原料库存表',
        },
        component: async () => await import('@/pages/dyeingManagement/rawStockTable/index.vue'),
      },
      {
        path: 'dyeingNotice',
        name: 'DyeingNotice',
        title: '染整通知单',
        meta: {
          navName: ['染整管理', '染整通知单列表'],
          title: '染整通知单列表',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingNotice/index.vue'),
      },
      {
        path: 'dyeingNoticeAdd',
        name: 'DyeingNoticeAdd',
        title: '新建染整通知单',
        hideType: true,
        meta: {
          navName: ['染整管理', '新建染整通知单'],
          title: '新建染整通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingNotice/add.vue'),
      },
      {
        path: 'dyeingNoticeEdit',
        name: 'DyeingNoticeEdit',
        title: '编辑染整通知单',
        hideType: true,
        meta: {
          navName: ['染整管理', '编辑染整通知单'],
          title: '编辑染整通知单',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingNotice/edit.vue'),
      },
      {
        path: 'dyeingNoticeDetail',
        name: 'DyeingNoticeDetail',
        title: '染整通知单详情',
        hideType: true,
        meta: {
          navName: ['染整管理', '染整通知单详情'],
          title: '染整通知单详情',
          keepAlive: true,
          sourceWarehouseType: SourceWarehouseTypeEnum.DyeNotify,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingNotice/detail.vue'),
      },
      {
        path: 'rawdyeingNotice',
        name: 'RawDyeingNotice',
        title: '原料染整通知单',
        meta: {
          navName: ['染整管理', '原料染整通知单列表'],
          title: '原料染整通知单列表',
        },
        component: async () => await import('@/pages/dyeingManagement/rawdyeingNotice/index.vue'),
      },
      {
        path: 'rawdyeingNoticeAdd',
        name: 'RawDyeingNoticeAdd',
        title: '新建原料染整通知单',
        meta: {
          navName: ['染整管理', '新建原料染整通知单'],
          title: '新建原料染整通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/rawdyeingNotice/add.vue'),
      },
      {
        path: 'rawdyeingNoticeEdit',
        name: 'RawDyeingNoticeEdit',
        title: '编辑原料染整通知单',
        meta: {
          navName: ['染整管理', '编辑原料染整通知单'],
          title: '编辑原料染整通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/rawdyeingNotice/edit.vue'),
      },
      {
        path: 'rawdyeingNoticeDetail',
        name: 'RawDyeingNoticeDetail',
        title: '原料染整通知单详情',
        meta: {
          navName: ['染整管理', '原料染整通知单详情'],
          title: '原料染整通知单详情',
        },
        component: async () => await import('@/pages/dyeingManagement/rawdyeingNotice/detail.vue'),
      },
      {
        path: 'rawDyeingSchedule',
        name: 'RawDyeingSchedule',
        title: '原料染整进度表',
        meta: {
          navName: ['染整管理', '原料染整进度表'],
          title: '原料染整进度表',
        },
        component: async () => await import('@/pages/dyeingManagement/rawDyeingSchedule/index.vue'),
      },
      {
        path: 'dyeingSchedule',
        name: 'DyeingSchedule',
        title: '染整进度情况',
        meta: {
          navName: ['染整管理', '染整进度情况'],
          title: '染整进度情况',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingSchedule/index.vue'),
      },
      {
        path: 'colorNotice',
        name: 'ColorNotice',
        title: '复色通知单',
        meta: {
          navName: ['染整管理', '复色通知单列表'],
          title: '复色通知单列表',
        },
        component: async () => await import('@/pages/dyeingManagement/colorNotice/index.vue'),
      },
      {
        path: 'colorNoticeAdd',
        name: 'ColorNoticeAdd',
        title: '新建复色通知单',
        meta: {
          navName: ['染整管理', '新建复色通知单'],
          title: '新建复色通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/colorNotice/add.vue'),
      },
      {
        path: 'colorNoticeEdit',
        name: 'ColorNoticeEdit',
        title: '编辑复色通知单',
        meta: {
          navName: ['染整管理', '编辑复色通知单'],
          title: '编辑复色通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/colorNotice/edit.vue'),
      },
      {
        path: 'colorNoticeDetail',
        name: 'ColorNoticeDetail',
        title: '复色通知单详情',
        meta: {
          navName: ['染整管理', '复色通知单详情'],
          title: '复色通知单详情',
        },
        component: async () => await import('@/pages/dyeingManagement/colorNotice/detail.vue'),
      },
      {
        path: 'adjustmentNotice',
        name: 'AdjustmentNotice',
        title: '后整通知单',
        meta: {
          navName: ['染整管理', '后整通知单列表'],
          title: '后整通知单列表',
        },
        component: async () => await import('@/pages/dyeingManagement/adjustmentNotice/index.vue'),
      },
      {
        path: 'adjustmentNoticeAdd',
        name: 'AdjustmentNoticeAdd',
        title: '新建后整通知单',
        meta: {
          navName: ['染整管理', '新建后整通知单'],
          title: '新建后整通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/adjustmentNotice/add.vue'),
      },
      {
        path: 'adjustmentNoticeEdit',
        name: 'AdjustmentNoticeEdit',
        title: '编辑后整通知单',
        meta: {
          navName: ['染整管理', '编辑后整通知单'],
          title: '编辑后整通知单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/adjustmentNotice/edit.vue'),
      },
      {
        path: 'adjustmentNoticeDetail',
        name: 'AdjustmentNoticeDetail',
        title: '后整通知单详情',
        meta: {
          navName: ['染整管理', '后整通知单详情'],
          title: '后整通知单详情',
        },
        component: async () => await import('@/pages/dyeingManagement/adjustmentNotice/detail.vue'),
      },
      {
        path: 'dyeingChange',
        name: 'DyeingChange',
        title: '染整变更单',
        meta: {
          navName: ['染整管理', '染整变更单'],
          title: '染整变更单',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/index.vue'),
      },
      {
        path: 'dyeingChangeAdd',
        name: 'DyeingChangeAdd',
        title: '新建染整变更单',
        meta: {
          navName: ['染整管理', '新建染整变更单'],
          title: '新建染整变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/dyeingChangeAdd.vue'),
      },
      {
        path: 'dyeingChangeEdit',
        name: 'DyeingChangeEdit',
        title: '编辑染整变更单',
        meta: {
          navName: ['染整管理', '编辑染整变更单'],
          title: '编辑染整变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/dyeingChangeEdit.vue'),
      },
      {
        path: 'dyeingChangeDetail',
        name: 'DyeingChangeDetail',
        title: '染整变更单详情',
        meta: {
          navName: ['染整管理', '染整变更单详情'],
          title: '染整变更单详情',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/dyeingChangeDetail.vue'),
      },
      {
        path: 'processChangeAdd',
        name: 'ProcessChangeAdd',
        title: '新建加工/回修变更单',
        meta: {
          navName: ['染整管理', '新建加工/回修变更单'],
          title: '新建加工/回修变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/processChangeAdd.vue'),
      },
      {
        path: 'processChangeEdit',
        name: 'ProcessChangeEdit',
        title: '编辑加工/回修变更单',
        meta: {
          navName: ['染整管理', '编辑加工/回修变更单'],
          title: '编辑加工/回修变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/processChangeEdit.vue'),
      },
      {
        path: 'processChangeDetail',
        name: 'ProcessChangeDetail',
        title: '加工/回修变更单详情',
        meta: {
          navName: ['染整管理', '加工/回修变更单详情'],
          title: '加工/回修变更单详情',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/processChangeDetail.vue'),
      },
      {
        path: 'colorChangeAdd',
        name: 'ColorChangeAdd',
        title: '新建复色变更单',
        meta: {
          navName: ['染整管理', '新建复色变更单'],
          title: '新建复色变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/colorChangeAdd.vue'),
      },
      {
        path: 'colorChangeEdit',
        name: 'ColorChangeEdit',
        title: '编辑复色变更单',
        meta: {
          navName: ['染整管理', '编辑复色变更单'],
          title: '编辑复色变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/colorChangeEdit.vue'),
      },
      {
        path: 'colorChangeDetail',
        name: 'ColorChangeDetail',
        title: '复色变更单详情',
        meta: {
          navName: ['染整管理', '复色变更单详情'],
          title: '复色变更单详情',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/colorChangeDetail.vue'),
      },
      {
        path: 'adjusmentChangeAdd',
        name: 'AdjusmentChangeAdd',
        title: '新建后整变更单',
        meta: {
          navName: ['染整管理', '新建后整变更单'],
          title: '新建后整变更单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingChange/adjusmentChangeAdd.vue'),
      },
      {
        path: 'dyeingFactoryTable',
        name: 'DyeingFactoryTable',
        title: '染厂库存表',
        meta: {
          navName: ['染整管理', '染厂库存表'],
          title: '染厂库存表',
        },
        component: async () => await import('@/pages/dyeingManagement/dyeingFactoryTable/index.vue'),
      },
    ],
  },
  {
    path: '/meetMange',
    name: 'MeetMange',
    component: LayOut,
    redirect: '/dyeingPay',
    children: [
      {
        path: 'dyeingPay',
        name: 'DyeingPay',
        title: '染整费应付账',
        meta: {
          navName: ['财务管理', '染整费应付账'],
          title: '染整费应付账列表',
          // keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/dyeingPay/index.vue'),
      },
      {
        path: 'dyeingPayEdit',
        name: 'DyeingPayEdit',
        title: '编辑染整费应付账',
        meta: {
          navName: ['财务管理', '染整费应付账', '编辑染整费应付账'],
          title: '编辑染整费应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/dyeingPay/edit.vue'),
      },
      {
        path: 'dyeingPayDetail',
        name: 'DyeingPayDetail',
        title: '染整费应付账详情',
        meta: {
          navName: ['财务管理', '染整费应付账', '染整费应付账详情'],
          title: '染整费应付账详情',
          orderType: OrderTypeEnum.OrderTypeDNF,
        },
        component: async () => await import('@/pages/MeetMange/dyeingPay/detail.vue'),
      },
      {
        path: 'processPay',
        name: 'ProcessPay',
        title: '加工费应付账',
        meta: {
          navName: ['财务管理', '加工费应付账'],
          title: '加工费应付账列表',
        },
        component: async () => await import('@/pages/MeetMange/processPay/index.vue'),
      },
      {
        path: 'processPayEdit',
        name: 'ProcessPayEdit',
        title: '编辑加工费应付账',
        meta: {
          navName: ['财务管理', '加工费应付账', '编辑加工费应付账'],
          title: '编辑加工费应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/processPay/edit.vue'),
      },
      {
        path: 'processPayDetail',
        name: 'ProcessPayDetail',
        title: '加工费应付账详情',
        meta: {
          navName: ['财务管理', '加工费应付账', '加工费应付账详情'],
          title: '加工费应付账详情',
          orderType: OrderTypeEnum.OrderTypeProcessing,
        },
        component: async () => await import('@/pages/MeetMange/processPay/detail.vue'),
      },
      {
        path: 'rawPay',
        name: 'RawPay',
        title: '原料采购应付账',
        meta: {
          navName: ['财务管理', '原料采购应付账'],
          title: '原料采购应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/rawPay/index.vue'),
      },
      {
        path: 'rawPayEdit',
        name: 'RawPayEdit',
        title: '编辑原料采购应付账',
        meta: {
          navName: ['财务管理', '原料采购应付账', '编辑原料采购应付账'],
          title: '编辑原料采购应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/rawPay/edit.vue'),
      },
      {
        path: 'rawPayDetail',
        name: 'RawPayDetail',
        title: '原料采购应付账详情',
        meta: {
          navName: ['财务管理', '原料采购应付账', '原料采购应付账详情'],
          title: '原料采购应付账详情',
          orderType: OrderTypeEnum.OrderTypeRawMatlPur,
        },
        component: async () => await import('@/pages/MeetMange/rawPay/detail.vue'),
      },

      {
        path: 'greyclothPay',
        name: 'GreyclothPay',
        title: '坯布采购应付账',
        meta: {
          navName: ['财务管理', '坯布采购应付账'],
          title: '坯布采购应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/greyclothPay/index.vue'),
      },
      {
        path: 'greyclothPayEdit',
        name: 'GreyclothPayEdit',
        title: '编辑坯布采购应付账',
        meta: {
          navName: ['财务管理', '坯布采购应付账', '编辑坯布采购应付账'],
          title: '编辑坯布采购应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/greyclothPay/edit.vue'),
      },
      {
        path: 'greyclothPayDetail',
        name: 'GreyclothPayDetail',
        title: '坯布采购应付账详情',
        meta: {
          navName: ['财务管理', '坯布采购应付账', '坯布采购应付账详情'],
          title: '坯布采购应付账详情',
          orderType: OrderTypeEnum.OrderTypeGreyFabricPur,
        },
        component: async () => await import('@/pages/MeetMange/greyclothPay/detail.vue'),
      },
      {
        path: 'finishPay',
        name: 'FinishPay',
        title: '成品采购应付账',
        meta: {
          navName: ['财务管理', '成品采购应付账'],
          title: '成品采购应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/finishPay/index.vue'),
      },
      {
        path: 'finishPayEdit',
        name: 'FinishPayEdit',
        title: '编辑成品采购应付账',
        meta: {
          navName: ['财务管理', '成品采购应付账', '编辑成品采购应付账'],
          title: '编辑成品采购应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/finishPay/edit.vue'),
      },
      {
        path: 'finishPayDetail',
        name: 'FinishPayDetail',
        title: '成品采购应付账详情',
        meta: {
          navName: ['财务管理', '成品采购应付账', '成品采购应付账详情'],
          title: '成品采购应付账详情',
          orderType: OrderTypeEnum.OrderTypeProductPur,
        },
        component: async () => await import('@/pages/MeetMange/finishPay/detail.vue'),
      },

      {
        path: 'otherPay',
        name: 'OtherPay',
        title: '其他应付账',
        meta: {
          navName: ['财务管理', '其他应付账'],
          title: '其他应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/otherPay/index.vue'),
      },
      {
        path: 'otherPayAdd',
        name: 'OtherPayAdd',
        title: '新建其他应付账',
        meta: {
          navName: ['财务管理', '其他应付账', '新建其他应付账'],
          title: '新建其他应付账',
        },
        component: async () => await import('@/pages/MeetMange/otherPay/add.vue'),
      },
      {
        path: 'otherPayEdit',
        name: 'OtherPayEdit',
        title: '编辑其他应付账',
        meta: {
          navName: ['财务管理', '其他应付账', '编辑其他应付账'],
          title: '编辑其他应付账',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/otherPay/edit.vue'),
      },
      {
        path: 'otherPayDetail',
        name: 'OtherPayDetail',
        title: '其他应付账详情',
        meta: {
          navName: ['财务管理', '其他应付账', '其他应付账详情'],
          title: '其他应付账详情',
          orderType: OrderTypeEnum.OrderTypeOther,
        },
        component: async () => await import('@/pages/MeetMange/otherPay/detail.vue'),
      },
      {
        path: 'grossProfitAnalysis',
        name: 'GrossProfitAnalysis',
        title: '毛利分析',
        meta: {
          navName: ['财务管理', '毛利分析'],
          title: '毛利分析',
        },
        component: async () => await import('@/pages/MeetMange/grossProfitAnalysis/index.vue'),
      },
      {
        path: 'businessAnalysis',
        name: 'BusinessAnalysis',
        title: '经营分析',
        meta: {
          navName: ['财务管理', '经营分析'],
          title: '经营分析',
        },
        component: async () => await import('@/pages/MeetMange/businessAnalysis/index.vue'),
      },
      {
        path: 'imprestPay',
        name: 'ImprestPay',
        title: '预付款',
        meta: {
          navName: ['财务管理', '预付款'],
          title: '预付款',
        },
        component: async () => await import('@/pages/MeetMange/imprestPay/index.vue'),
      },
      {
        path: 'imprestPayAdd',
        name: 'ImprestPayAdd',
        title: '新建预付款',
        meta: {
          navName: ['财务管理', '预付款', '新建预付款'],
          title: '新建预付款',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/imprestPay/add.vue'),
      },
      {
        path: 'imprestPayEdit',
        name: 'ImprestPayEdit',
        title: '编辑预付款',
        meta: {
          navName: ['财务管理', '预付款', '编辑预付款'],
          title: '编辑预付款',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/imprestPay/edit.vue'),
      },
      {
        path: 'imprestPayDetail',
        name: 'ImprestPayDetail',
        title: '预付款详情',
        meta: {
          navName: ['财务管理', '预付款', '预付款详情'],
          title: '预付款详情',
          orderType: OrderTypeEnum.OrderTypeAdvance,
        },
        component: async () => await import('@/pages/MeetMange/imprestPay/detail.vue'),
      },

      {
        path: 'actualPay',
        name: 'ActualPay',
        title: ' 实付款',
        meta: {
          navName: ['应付管理', '实付款'],
          title: ' 实付款',
        },
        component: async () => await import('@/pages/MeetMange/actualPay/index.vue'),
      },
      {
        path: 'actualPayAdd',
        name: 'ActualPayAdd',
        title: '新建实付款',
        meta: {
          navName: ['财务管理', '实付款', '新建实付款'],
          title: '新建实付款',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/actualPay/add.vue'),
      },
      {
        path: 'actualPayEdit',
        name: 'ActualPayEdit',
        title: '编辑实付款',
        meta: {
          navName: ['财务管理', '实付款', '编辑实付款'],
          title: '编辑实付款',
          keepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/actualPay/edit.vue'),
      },
      {
        path: 'actualPayDetail',
        name: 'ActualPayDetail',
        title: '实付款详情',
        meta: {
          navName: ['财务管理', '实付款', '实付款详情'],
          title: '实付款详情',
          orderType: OrderTypeEnum.OrderTypeActually,
        },
        component: async () => await import('@/pages/MeetMange/actualPay/detail.vue'),
      },
      {
        path: 'supplierBalanceSheet',
        name: 'SupplierBalanceSheet',
        title: '供方欠款表',
        meta: {
          navName: ['应付管理', '供方欠款表'],
          title: '供方欠款表',
        },
        component: async () => await import('@/pages/MeetMange/supplierBalanceSheet/index.vue'),
      },
      {
        path: 'supplierStatement',
        name: 'SupplierStatement',
        title: '供方对账单',
        meta: {
          navName: ['应付管理', '供方对账单'],
          title: '供方对账单',
          KeepAlive: true,
        },
        component: async () => await import('@/pages/MeetMange/supplierStatement/index.vue'),
      },
    ],
  },
  {
    path: '/receivableManagement',
    name: 'ReceivableManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/receivableManagement/rawMaterialSaleReceivableOrder',
    meta: {
      navName: ['应收管理'],
      title: '应收管理',
    },
    children: [
      {
        path: 'rawMaterialSaleReceivableOrder',
        name: 'RawMaterialSaleReceivableOrder',
        title: '原料销售应收单列表',
        hideType: true,
        meta: {
          navName: ['应收管理', '原料销售应收单列表'],
          title: '原料销售应收单列表',
        },
        component: async () => await import('@/pages/receivableManagement/rawMaterialSaleReceivableOrder/index.vue'),
      },
      {
        path: 'rawMaterialSaleReceivableOrderEdit',
        name: 'RawMaterialSaleReceivableOrderEdit',
        title: '原料销售应收单',
        hideType: true,
        meta: {
          navName: ['应收管理', '原料销售应收单', '原料销售应收单编辑'],
          title: '原料销售应收单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/rawMaterialSaleReceivableOrder/edit.vue'),
      },
      {
        path: 'rawMaterialSaleReceivableOrderDetail',
        name: 'RawMaterialSaleReceivableOrderDetail',
        title: '原料销售应收单',
        hideType: true,
        meta: {
          navName: ['应收管理', '原料销售应收单', '原料销售应收单详情'],
          title: '原料销售应收单详情',
          collectType: [CollectTypeEnum.CollectTypeRawMaterial, CollectTypeEnum.CollectTypeRawMaterialReturn],
        },
        component: async () => await import('@/pages/receivableManagement/rawMaterialSaleReceivableOrder/detail.vue'),
      },
      {
        path: 'grayFabricSaleReceivableOrder',
        name: 'GrayFabricSaleReceivableOrder',
        title: '坯布销售应收单列表',
        hideType: true,
        meta: {
          navName: ['应收管理', '坯布销售应收单列表'],
          title: '坯布销售应收单列表',
        },
        component: async () => await import('@/pages/receivableManagement/grayFabricSaleReceivableOrder/index.vue'),
      },
      {
        path: 'grayFabricSaleReceivableOrderEdit',
        name: 'GrayFabricSaleReceivableOrderEdit',
        title: '坯布销售应收单编辑',
        hideType: true,
        meta: {
          navName: ['应收管理', '坯布销售应收单', '坯布销售应收单编辑'],
          title: '坯布销售应收单编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/grayFabricSaleReceivableOrder/edit.vue'),
      },
      {
        path: 'grayFabricSaleReceivableOrderDetail',
        name: 'GrayFabricSaleReceivableOrderDetail',
        title: '坯布销售应收单详情',
        hideType: true,
        meta: {
          navName: ['应收管理', '坯布销售应收单', '坯布销售应收单详情'],
          title: '坯布销售应收单详情',
          collectType: [CollectTypeEnum.CollectTypeGreyFabric, CollectTypeEnum.CollectTypeGreyFabricReturn],
        },
        component: async () => await import('@/pages/receivableManagement/grayFabricSaleReceivableOrder/detail.vue'),
      },
      {
        path: 'otherReceivableOrder',
        name: 'OtherReceivableOrder',
        title: '其他应收账列表',
        hideType: true,
        meta: {
          navName: ['应收管理', '其他应收账列表'],
          title: '其他应收账列表',
        },
        component: async () => await import('@/pages/receivableManagement/otherReceivableOrder/index.vue'),
      },
      {
        path: 'otherReceivableOrderAdd',
        name: 'OtherReceivableOrderAdd',
        title: '其他应收账新建',
        hideType: true,
        meta: {
          navName: ['应收管理', '其他应收账', '其他应收账新建'],
          title: '其他应收账新建',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/otherReceivableOrder/add.vue'),
      },
      {
        path: 'otherReceivableOrderEdit',
        name: 'OtherReceivableOrderEdit',
        title: '其他应收账编辑',
        hideType: true,
        meta: {
          navName: ['应收管理', '其他应收账', '其他应收账编辑'],
          title: '其他应收账编辑',
        },
        component: async () => await import('@/pages/receivableManagement/otherReceivableOrder/edit.vue'),
      },
      {
        path: 'otherReceivableOrderDetail',
        name: 'OtherReceivableOrderDetail',
        title: '其他应收账详情',
        hideType: true,
        meta: {
          navName: ['应收管理', '其他应收账', '其他应收账详情'],
          title: '其他应收账详情',
          collectType: CollectTypeEnum.CollectTypeOther,
        },
        component: async () => await import('@/pages/receivableManagement/otherReceivableOrder/detail.vue'),
      },
      {
        path: 'fundsReceived',
        name: 'FundsReceived',
        title: '实收款列表',
        hideType: true,
        meta: {
          navName: ['应收管理', '实收款列表'],
          title: '实收款列表',
        },
        component: async () => await import('@/pages/receivableManagement/fundsReceived/index.vue'),
      },
      {
        path: 'fundsReceivedAdd',
        name: 'FundsReceivedAdd',
        title: '实收款新建',
        hideType: true,
        meta: {
          navName: ['应收管理', '实收款', '实收款新建'],
          title: '实收款新建',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/fundsReceived/add.vue'),
      },
      {
        path: 'fundsReceivedEdit',
        name: 'FundsReceivedEdit',
        title: '实收款编辑',
        hideType: true,
        meta: {
          navName: ['应收管理', '实收款', '实收款编辑'],
          title: '实收款编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/fundsReceived/edit.vue'),
      },
      {
        path: 'fundsReceivedDetail',
        name: 'FundsReceivedDetail',
        title: '实收款详情',
        hideType: true,
        meta: {
          navName: ['应收管理', '实收款', '实收款详情'],
          title: '实收款详情',
          collectType: CollectTypeEnum.CollectTypeActual,
        },
        component: async () => await import('@/pages/receivableManagement/fundsReceived/detail.vue'),
      },
      {
        path: 'advancesReceived',
        name: 'AdvancesReceived',
        title: '预收款列表',
        hideType: true,
        meta: {
          navName: ['应收管理', '预收款列表'],
          title: '预收款列表',
        },
        component: async () => await import('@/pages/receivableManagement/advancesReceived/index.vue'),
      },
      {
        path: 'advancesReceivedAdd',
        name: 'AdvancesReceivedAdd',
        title: '预收款新建',
        hideType: true,
        meta: {
          navName: ['应收管理', '预收款', '预收款新建'],
          title: '预收款新建',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/advancesReceived/add.vue'),
      },
      {
        path: 'advancesReceivedEdit',
        name: 'AdvancesReceivedEdit',
        title: '预收款编辑',
        hideType: true,
        meta: {
          navName: ['应收管理', '预收款', '预收款编辑'],
          title: '预收款编辑',
          keepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/advancesReceived/edit.vue'),
      },
      {
        path: 'advancesReceivedDetail',
        name: 'AdvancesReceivedDetail',
        title: '预收款详情',
        hideType: true,
        meta: {
          navName: ['应收管理', '预收款', '预收款详情'],
          title: '预收款详情',
          collectType: CollectTypeEnum.CollectTypeAdvance,
        },
        component: async () => await import('@/pages/receivableManagement/advancesReceived/detail.vue'),
      },
      {
        path: 'customerArrearage',
        name: 'CustomerArrearage',
        title: '客户欠款表',
        hideType: true,
        meta: {
          navName: ['应收管理', '客户欠款表'],
          title: '客户欠款表',
        },
        component: async () => await import('@/pages/receivableManagement/customerArrearage/index.vue'),
      },
      {
        path: 'customerReconciliation',
        name: 'CustomerReconciliation',
        title: '客户对账表',
        hideType: true,
        meta: {
          navName: ['应收管理', '客户对账表'],
          title: '客户对账表',
          KeepAlive: true,
        },
        component: async () => await import('@/pages/receivableManagement/customerReconciliation/index.vue'),
      },
    ],
  },
  {
    path: '/qualityCheckManagement',
    name: 'QualityCheckManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/qualityCheckManagement/fpQualityCheck',
    meta: {
      navName: ['质检管理'],
      title: '质检管理',
    },
    children: [
      {
        path: 'fpQualityCheck',
        name: 'FpQualityCheck',
        title: '成品质检',
        meta: {
          navName: ['质检管理', '成品质检'],
          title: '成品质检',
        },
        component: async () => await import('@/pages/qualityCheckManagement/fpQualityCheck/index.vue'),
      },
      {
        path: 'fpQualityReport',
        name: 'FpQualityReport',
        title: '质检报表',
        meta: {
          navName: ['质检管理', '质检报表'],
          title: '质检报表',
        },
        component: async () => await import('@/pages/qualityCheckManagement/fpQualityCheckReport/index.vue'),
      },
      {
        path: 'qualityInspectionReport',
        name: 'QualityInspectionReport',
        title: '质检报告',
        meta: {
          navName: ['库存', '成品'],
          title: '质检报告',
        },
        component: async () => await import('@/pages/qualityCheckManagement/qualityInspectionReport/index.vue'),
      },
      {
        path: 'qualityInspectionReportAdd',
        name: 'QualityInspectionReportAdd',
        title: '新建质检报告',
        hideType: true,
        meta: {
          navName: ['库存', '成品', '质检报告', '新建质检报告'],
          title: '新建质检报告',
        },
        component: async () => await import('@/pages/qualityCheckManagement/qualityInspectionReport/add.vue'),
      },
      {
        path: 'qualityInspectionReportEdit',
        name: 'QualityInspectionReportEdit',
        title: '编辑质检报告',
        hideType: true,
        meta: {
          navName: ['库存', '成品', '质检报告', '编辑质检报告'],
          title: '编辑质检报告',
        },
        component: async () => await import('@/pages/qualityCheckManagement/qualityInspectionReport/edit.vue'),
      },
      {
        path: 'qualityInspectionReportDetail',
        name: 'QualityInspectionReportDetail',
        title: '质检报告详情',
        hideType: true,
        meta: {
          navName: ['库存', '成品', '质检报告', '质检报告详情'],
          title: '质检报告详情',
        },
        component: async () => await import('@/pages/qualityCheckManagement/qualityInspectionReport/detail.vue'),
      },
    ],
  },
  {
    path: '/dyeingPriceManagement',
    name: 'DyeingPriceManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    meta: {
      navName: ['价格管理'],
      title: '价格管理',
    },
    children: [
      {
        path: 'dyeingQuotationIndex',
        name: 'DyeingQuotationIndex',
        title: '染费报价单列表',
        meta: {
          navName: ['价格管理', '染费报价单', '染费报价单列表'],
          title: '染费报价单列表',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/dyeingQuotation/index.vue'),
      },
      {
        path: 'dyeingQuotationAdd',
        name: 'DyeingQuotationAdd',
        title: '新建染费报价单',
        hideType: true,
        meta: {
          navName: ['价格管理', '染费报价单', '新建染费报价单'],
          title: '新建染费报价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/dyeingQuotation/add.vue'),
      },
      {
        path: 'dyeingQuotationEdit',
        name: 'DyeingQuotationEdit',
        title: '编辑染费报价单',
        hideType: true,
        meta: {
          navName: ['价格管理', '染费报价单', '编辑染费报价单'],
          title: '编辑染费报价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/dyeingQuotation/edit.vue'),
      },
      {
        path: 'dyeingQuotationDetail',
        name: 'DyeingQuotationDetail',
        title: '染费报价单详情',
        hideType: true,
        meta: {
          navName: ['价格管理', '染费报价单', '染费报价单详情'],
          title: '染费报价单详情',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/dyeingQuotation/detail.vue'),
      },

      {
        path: 'adjustmentIndex',
        name: 'AdjustmentIndex',
        title: '后整报价单列表',
        meta: {
          navName: ['价格管理', '后整报价单', '后整报价单列表'],
          title: '后整报价单列表',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/adjustmentQuotation/index.vue'),
      },
      {
        path: 'adjustmentAdd',
        name: 'AdjustmentAdd',
        title: '新建后整报价单',
        hideType: true,
        meta: {
          navName: ['价格管理', '后整报价单', '新建后整报价单'],
          title: '新建后整报价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/adjustmentQuotation/add.vue'),
      },
      {
        path: 'adjustmentEdit',
        name: 'AdjustmentEdit',
        title: '编辑后整报价单',
        hideType: true,
        meta: {
          navName: ['价格管理', '后整报价单', '编辑后整报价单'],
          title: '编辑后整报价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/adjustmentQuotation/edit.vue'),
      },
      {
        path: 'adjustmentDetail',
        name: 'AdjustmentDetail',
        title: '后整报价单详情',
        hideType: true,
        meta: {
          navName: ['价格管理', '后整报价单', '后整报价单详情'],
          title: '后整报价单详情',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/adjustmentQuotation/detail.vue'),
      },
      {
        path: 'dyeingPriceIndex',
        name: 'DyeingPriceIndex',
        title: '染费价目表',
        meta: {
          navName: ['价格管理', '染整价格管理', '染费价目表'],
          title: '染费价目表',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/dyeingPrice/index.vue'),
      },
      // {
      //   path: 'dyeingPriceAdd',
      //   name: 'DyeingPriceAdd',
      //   title: '新建染费调价单',
      //   meta: {
      //     navName: ['价格管理', '染费调价单', '新建染费调价单'],
      //     title: '新建染费调价单',
      //     keepAlive: true,
      //   },
      //   component: async () => await import('@/pages/dyeingPriceManagement/dyeingPrice/add.vue'),
      // },
      {
        path: 'dyeingPriceEdit',
        name: 'DyeingPriceEdit',
        title: '编辑染费调价单',
        meta: {
          navName: ['价格管理', '染费调价单', '编辑染费调价单'],
          title: '编辑染费调价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/dyeingPrice/edit.vue'),
      },
      {
        path: 'adjustmentPriceIndex',
        name: 'AdjustmentPriceIndex',
        title: '后整加工价目表',
        meta: {
          navName: ['价格管理', '染整价格管理', '后整加工价目表'],
          title: '后整加工价目表',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/adjustmentPrice/index.vue'),
      },
      // {
      //   path: 'adjustmentPriceAdd',
      //   name: 'AdjustmentPriceAdd',
      //   title: '新建后整加工价目单',
      //   meta: {
      //     navName: ['价格管理', '染费调价单', '新建后整加工价目单'],
      //     title: '新建后整加工价目单',
      //     keepAlive: true,
      //   },
      //   component: async () => await import('@/pages/dyeingPriceManagement/adjustmentPrice/add.vue'),
      // },
      {
        path: 'adjustmentPriceEdit',
        name: 'AdjustmentPriceEdit',
        title: '编辑后整加工价目单',
        meta: {
          navName: ['价格管理', '染费调价单', '编辑后整加工价目单'],
          title: '编辑后整加工价目单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/adjustmentPrice/edit.vue'),
      },
      {
        path: 'rawQuotationIndex',
        name: 'RawQuotationIndex',
        title: '原料染整报价单',
        meta: {
          navName: ['价格管理', '原料染整报价单'],
          keepAlive: true,
          title: '原料染整报价单',
        },
        component: async () => await import('@/pages/dyeingPriceManagement/rawQuotation/index.vue'),
      },
      {
        path: 'rawQuotationAdd',
        name: 'RawQuotationAdd',
        title: '新建染费报价单',
        meta: {
          navName: ['价格管理', '新建染费报价单'],
          title: '新建染费报价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/rawQuotation/add.vue'),
      },
      {
        path: 'rawQuotationEdit',
        name: 'RawQuotationEdit',
        title: '编辑染费报价单',
        meta: {
          navName: ['价格管理', '编辑染费报价单'],
          title: '编辑染费报价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/rawQuotation/edit.vue'),
      },
      {
        path: 'rawQuotationDetail',
        name: 'RawQuotationDetail',
        title: '染费报价单详情',
        meta: {
          navName: ['价格管理', '染费报价单详情'],
          title: '染费报价单详情',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/rawQuotation/detail.vue'),
      },
      {
        path: 'rawQuotationAdjust',
        name: 'RawQuotationAdjust',
        title: '新建染费调价单',
        meta: {
          navName: ['价格管理', '新建染费调价单'],
          title: '新建染费调价单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/dyeingPriceManagement/rawQuotation/adjust.vue'),
      },
    ],
  },
  {
    path: '/financialManagement',
    name: 'FinancialManagement',
    component: LayOut,
    redirect: '/otherReceipts',
    children: [
      {
        path: 'otherReceipts',
        name: 'OtherReceipts',
        title: '其他收入单',
        meta: {
          navName: ['财务管理', '其他收入单'],
          title: '其他收入单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/otherReceipts/index.vue'),
      },
      {
        path: 'otherReceiptsAdd',
        name: 'OtherReceiptsAdd',
        title: '新建其他收入单',
        meta: {
          navName: ['财务管理', '新建其他收入单'],
          title: '新建其他收入单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/otherReceipts/add.vue'),
      },
      {
        path: 'otherReceiptsEdit',
        name: 'OtherReceiptsEdit',
        title: '编辑其他收入单',
        meta: {
          navName: ['财务管理', '编辑其他收入单'],
          title: '编辑其他收入单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/otherReceipts/edit.vue'),
      },
      {
        path: 'otherReceiptsDetail',
        name: 'OtherReceiptsDetail',
        title: '其他收入单详情',
        meta: {
          navName: ['财务管理', '其他收入单详情'],
          title: '其他收入单详情',
        },
        component: async () => await import('@/pages/financialManagement/otherReceipts/detail.vue'),
      },
      {
        path: 'otherPays',
        name: 'OtherPays',
        title: '其他支出单',
        meta: {
          navName: ['财务管理', '其他支出单'],
          title: '其他支出单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/otherPay/index.vue'),
      },
      {
        path: 'finishProductSalesCost',
        name: 'FinishProductSalesCost',
        title: '成品销售成本',
        meta: {
          navName: ['财务管理', '成品销售成本'],
          title: '成品销售成本',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/finishProductSalesCost/index.vue'),
      },
      {
        path: 'otherPaysAdd',
        name: 'OtherPaysAdd',
        title: '新建其他支出单',
        meta: {
          navName: ['财务管理', '新建其他支出单'],
          title: '新建其他支出单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/otherPay/add.vue'),
      },
      {
        path: 'otherPaysEdit',
        name: 'OtherPaysEdit',
        title: '编辑其他支出单',
        meta: {
          navName: ['财务管理', '编辑其他支出单'],
          title: '编辑其他支出单',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/otherPay/edit.vue'),
      },
      {
        path: 'otherPaysDetail',
        name: 'OtherPaysDetail',
        title: '其他支出单详情',
        meta: {
          navName: ['财务管理', '其他支出单详情'],
          title: '其他支出单详情',
        },
        component: async () => await import('@/pages/financialManagement/otherPay/detail.vue'),
      },
      {
        path: 'carryOverMonth',
        name: 'CarryOverMonth',
        title: '月结结转',
        meta: {
          navName: ['档案', '财务档案', '月结结转'],
          title: '月结结转',
        },
        component: async () => await import('@/pages/financialManagement/carryOverMonth/index.vue'),
      },
      {
        path: 'finishProductCostAccounting',
        name: 'FinishProductCostAccounting',
        title: '成品成本核算',
        meta: {
          navName: ['财务管理', '应付', '成品成本核算'],
          title: '成品成本核算',
          keepAlive: true,
        },
        component: async () => await import('@/pages/financialManagement/finishProductCostAccounting/index.vue'),
      },
    ],
  },
  {
    path: '/mainBackend',
    name: 'MainBackend',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/mainBackend/setOfAccountsManagement',
    meta: {
      navName: ['账套管理'],
      title: '账套管理',
    },
    children: [
      {
        path: 'weChatPaymentOrders',
        name: 'WeChatPaymentOrders',
        title: '微信支付订单',
        meta: {
          navName: ['账套管理', '微信支付订单'],
          title: '微信支付订单',
        },
        component: async () => await import('@/pages/mainBackend/weChatPaymentOrders/index.vue'),
      },
      {
        path: 'setOfAccountsManagement',
        name: 'SetOfAccountsManagement',
        title: '账套管理',
        meta: {
          navName: ['账套管理', '账套管理'],
          title: '账套管理',
        },
        component: async () => await import('@/pages/mainBackend/setOfAccountsManagement/index.vue'),
      },
      {
        path: 'setOfAccountsManagementDetail/:id(\\d+)',
        name: 'SetOfAccountsManagementDetail',
        title: '账套详情',
        props: true,
        hideType: true,
        meta: {
          navName: ['账套管理', '账套详情'],
          title: '账套详情',
        },
        component: async () => await import('@/pages/mainBackend/setOfAccountsManagement/detail.vue'),
      },
      {
        path: 'servicePackage',
        name: 'ServicePackage',
        title: '服务套餐',
        meta: {
          navName: ['账套管理', '服务套餐'],
          title: '服务套餐',
        },
        component: async () => await import('@/pages/mainBackend/servicePackage/index.vue'),
      },
      {
        // 新建
        path: 'servicePackage/add',
        name: 'ServicePackageAdd',
        title: '新建服务套餐',
        meta: {
          navName: ['账套管理', '服务套餐', '新建服务套餐'],
          title: '新建服务套餐',
        },
        component: async () => await import('@/pages/mainBackend/servicePackage/addEdit/index.vue'),
      },
      {
        // 新建
        path: 'servicePackage/edit',
        name: 'ServicePackageEdit',
        title: '编辑服务套餐',
        meta: {
          navName: ['账套管理', '服务套餐', '编辑服务套餐'],
          title: '编辑服务套餐',
        },
        component: async () => await import('@/pages/mainBackend/servicePackage/addEdit/index.vue'),
      },
      {
        path: 'palletizingClues',
        name: 'PalletizingClues',
        title: '调货版线索',
        meta: {
          navName: ['账套管理', '调货版线索'],
          title: '调货版线索',
        },
        component: async () => await import('@/pages/mainBackend/palletizingClues/index.vue'),
      },
      {
        path: 'qyWechatApplicationConfiguration',
        name: 'QYWechatApplicationConfiguration',
        title: '企微应用配置',
        meta: {
          navName: ['账套管理', '企微应用配置'],
          title: '企微应用配置',
        },
        component: async () => await import('@/pages/mainBackend/qyWechatApplicationConfiguration/index.vue'),
      },
      {
        path: 'ocrManagement',
        name: 'OcrManagement',
        title: '码单OCR识别',
        meta: {
          navName: ['账套管理', '码单OCR识别'],
          title: '码单OCR识别',
        },
        component: async () => await import('@/pages/mainBackend/ocrManagement/index.vue'),
      },
    ],
  },
  {
    path: '/reporForms',
    name: 'ReporForms',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/reporForms/checkOrderItemReport',
    meta: {
      navName: ['盘点信息'],
      title: '盘点信息',
    },
    children: [
      {
        path: 'checkOrderItemReport',
        name: 'CheckOrderItemReport',
        title: '进出仓信息盘点',
        meta: {
          navName: ['盘点信息', '进出仓信息盘点'],
          title: '进出仓信息盘点',
        },
        component: async () => await import('@/pages/reportForms/checkOrderItemReport/index.vue'),
      },
      {
        path: 'productInReport',
        name: 'ProductInReport',
        title: '成品进仓报表',
        meta: {
          navName: ['盘点信息', '成品进仓报表'],
          title: '成品进仓报表',
        },
        component: async () => await import('@/pages/reportForms/productInReport/index.vue'),
      },
      {
        path: 'productOutReport',
        name: 'ProductOutReport',
        title: '成品出仓报表',
        meta: {
          navName: ['盘点信息', '成品出仓报表'],
          title: '成品出仓报表',
        },
        component: async () => await import('@/pages/reportForms/productOutReport/index.vue'),
      },
    ],
  },
  {
    path: '/colorCardManagement',
    name: 'ColorCardManagement',
    component: LayOut,
    kindNameLong: '',
    kindNameShort: '',
    icon: ArrowDown,
    redirect: '/colorCardManagement/colorCardService',
    meta: {
      navName: ['电子色卡管理'],
      title: '电子色卡管理',
    },
    children: [
      {
        path: 'colorCardService',
        name: 'ColorCardService',
        title: '电子色卡服务',
        meta: {
          navName: ['电子色卡管理', '电子色卡服务'],
          title: '电子色卡服务',
        },
        component: async () => await import('@/pages/colorCardManagement/service/index.vue'),
      },
      {
        path: 'colorCardShop',
        name: 'ColorCardShop',
        title: '电子色卡服务',
        meta: {
          navName: ['电子色卡管理', '商家信息'],
          title: '商家信息',
        },
        component: async () => await import('@/pages/colorCardManagement/shop/index.vue'),
      },
      {
        path: 'colorCardBanner',
        name: 'ColorCardBanner',
        title: '轮播图',
        meta: {
          navName: ['电子色卡管理', '轮播图'],
          title: '轮播图',
        },
        component: async () => await import('@/pages/colorCardManagement/banner/index.vue'),
      },
      {
        path: 'colorCardBannerAdd',
        name: 'ColorCardBannerAdd',
        title: '新建轮播图',
        meta: {
          navName: ['电子色卡管理', '新建轮播图'],
          title: '新建轮播图',
        },
        component: async () => await import('@/pages/colorCardManagement/banner/add.vue'),
      },
      {
        path: 'colorCardBannerEdit/:id(\\d+)',
        name: 'ColorCardBannerEdit',
        title: '轮播图编辑',
        meta: {
          navName: ['电子色卡管理', '编辑轮播图'],
          title: '编辑轮播图',
        },
        component: async () => await import('@/pages/colorCardManagement/banner/edit.vue'),
      },
      {
        path: 'colorCardBannerDetail/:id(\\d+)',
        name: 'ColorCardBannerDetail',
        title: '轮播图详情',
        meta: {
          navName: ['电子色卡', '轮播图详情'],
          title: '轮播图详情',
        },
        component: async () => await import('@/pages/colorCardManagement/banner/detail.vue'),
      },
    ],
  },
]

function checkDuplicateNames(routes: any, names = new Set(), duplicates = new Set()) {
  for (const route of routes) {
    if (route.name) {
      if (names.has(route.name)) {
        duplicates.add(route.name)
        console.error(`发现重复的路由name: "${route.name}"`, route)
      }
      else {
        names.add(route.name)
      }
    }
    if (route.children)
      checkDuplicateNames(route.children, names, duplicates)
  }
  return duplicates.size > 0 // 返回是否有重复
}

if (checkDuplicateNames(routes))
  console.error('项目中存在重复的路由name，请排查去除')

export default routes
