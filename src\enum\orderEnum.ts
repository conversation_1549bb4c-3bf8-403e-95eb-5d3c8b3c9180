// 客户对账表--单据类型
export enum CollectTypeEnum {
  CollectTypeProductSale = 1, // 销售送货单
  CollectTypeProductReturn = 2, // 销售退货单
  CollectTypeRawMaterial = 3, // 原料销售应收单
  CollectTypeRawMaterialReturn = 4, // 原料销售退货应收单
  CollectTypeGreyFabric = 5, // 坯布销售应收单
  CollectTypeGreyFabricReturn = 6, // 坯布销售退货应收单
  CollectTypeOther = 7, // 其他应收单
  CollectTypeActual = 8, // 实收单
  CollectTypeAdvance = 9, // 预收单
}

export enum SrcOrderType {
  SrcOrderTypeArrange = 1, // 配布单
  SrcOrderTypeProductSale = 2, // 成品销售单
  SrcOrderTypeProductSaleReturn = 3, // 成品退货进仓单
  SrcOrderTypeRawSale = 4, // 原料销售单
  SrcOrderTypeRawSaleReturn = 5, // 原料销售退货单
  SrcOrderTypeFabricSale = 6, // 坯布销售单
  SrcOrderTypeFabricSaleReturn = 7, // 坯布销售退货单
  SrcOrderTypeSaleTransfer = 8, // 调货销售单
  SrcOrderTypeSaleTransferReturn = 9, // 调货销售退货单
  SrcOrderTypeSelf = 10, // 自建单
  SrcOrderTypeProductSaleOut = 11, // 成品销售出仓单
}
// 供方对账单--单据类型
export enum OrderTypeEnum {
  OrderTypeDNF = 1, // 染整费应付账
  OrderTypeProcessing = 2, // 加工
  OrderTypeRawMatlPur = 3, // 原料采购
  OrderTypeGreyFabricPur = 4, // 坯布采购
  OrderTypeProductPur = 5, // 成品采购
  OrderTypeOther = 6, // 其他
  OrderTypeRawDNF = 7, // 原料染整应付账
  OrderTypeRawProcessing = 8, // 原料加工
  OrderTypeActually = 9, // 实付
  OrderTypeAdvance = 10, // 预付
  OrderTypeSaleTransfer = 11, // 调货销售单
}

// 单据审核状态
export enum OrderAuditStatusEnum {
  Pending = 1, // 待审核
  Audited, // 已审核
  TurnDown, // 驳回
  Cancellation, // 作废
}

// 订单类型
export enum SaleModeEnum {
  Bulk = 1, // 大货
  Plate = 2, // 剪板
  CustomerBulk = 3, // 客订-大货
  CustomerPlate = 4, // 客订-剪板
}

// 月结结转订单类型
export enum MonthTransferOrderEnum {
  FpInteriorAllotWarehouseEntryOrder = 1, // 内部调拨进仓单
  FpSaleAllotEntryGodownOrder = 2, // 成品销售调拨进仓单
  FpSaleEntryOrder = 3, // 成品销售退货进仓单
  FinishPurchaseWarehouseEntry = 4, // 成品采购进仓单
  FpProcessingEntryOrder = 5, // 成品加工进仓单
  FpOtherEntryOrder = 6, // 成品其他进仓单
  FpProcessingReturnEntryOrder = 7, // 成品加工退料进仓单
  FpInteriorAllotDeliverFromGodownOrder = 8, // 成品内部调拨出仓单
  FpSaleAllotReturnDeliverGodownOrder = 9, // 成品销售调拨出仓单
  FpSaleDeliverFromGodownOrder = 10, // 成品销售出仓单
  FpPurchaseReturnDeliverGodown = 11, // 成品采购退货出仓单
  FpProcessingDeliverFromGodownOrder = 12, // 成品加工发料出仓单
  FpOtherDeliverFromGodownOrder = 13, // 成品其他出仓单
  FpWithholdDeliverFromGodownOrder = 14, // 成品扣款出仓单
  OrderTypeOutTypeRepair = 15, // 回修出仓单 --
  FpStockCheckOrder = 16, // 成品库存盘点单
  FpStockAdjustOrder = 17, // 成品库存调整单
  // ==== 以下先不处理
  CashCommodityClothOrder = 18, // 现货配布单
  ProductSale = 19, // 成品销售单
  FpSubscribeWarehouseOrder = 20, // 预约单
  TransferSales = 21, // 调货单
}

// 占用库存单据的类型
export enum BookOrderTypeEnum {
  ReservationPass = 1, // 成品预约出仓单审核
  ReservationWait = 2, // 成品预约出仓单消审
  ArrangeChangePass = 3, // 成品配布变更单审核
  ArrangeChangeWait = 4, // 成品配布变更单消审
  PurchaseReturnPass = 5, // 成品采购退货单审核
  PurchaseReturnWait = 6, // 成品采购退货单消审
  PMCPush = 7, // 物料计划单下推销售单自动审核
  ProductSalePass = 8, // 成品销售单审核
  ProductSaleWait = 9, // 成品销售单消审
  ProductSaleOutPass = 10, // 成品销售出仓单审核
  ProductSaleOutWait = 11, // 成品销售出仓单消审
  ProductIntAlloOutPass = 12, // 成品内部调拨出仓单审核
  ProductIntAlloOutWait = 13, // 成品内部调拨出仓单消审
  ProductPrtOutPass = 14, // 成品采购退货出仓单审核
  ProductPrtOutWait = 15, // 成品采购退货出仓单消审
  ProductOtherOutPass = 16, // 成品其他出仓单审核
  ProductOtherOutWait = 17, // 成品其他出仓单消审
  ProductDeductionOutPass = 18, // 成品扣款出仓单审核
  ProductDeductionOutWait = 19, // 成品扣款出仓单消审
  ProductCheckOutPass = 20, // 成品盘点出仓单审核
  ProductCheckOutWait = 21, // 成品盘点出仓单消审
  ProductProcessOutPass = 22, // 成品加工出仓单审核
  ProductProcessOutWait = 23, // 成品加工出仓单消审
  ProductSaleAlloOutPass = 24, // 成品销售调拨出仓单审核
  ProductSaleAlloOutWait = 25, // 成品销售调拨出仓单消审
  ProductAdjustOutPass = 26, // 成品调整出仓单审核
  ProductAdjustOutWait = 27, // 成品调整出仓单消审
  ProductSaleAlloInPass = 28, // 成品销售调拨进仓单审核
  ProductSaleAlloInWait = 29, // 成品销售调拨进仓单消审
}
