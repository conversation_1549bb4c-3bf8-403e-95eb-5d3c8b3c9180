<script setup lang="ts" name="SheetOfProductionPlanAdd">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import AddFabricDialog from '../components/AddFabricDialog.vue'
import { addSheetOfProductionPlan } from '@/api/sheetOfProductionPlan'
import { BusinessUnitIdEnum, DictionaryType, EmployeeType } from '@/common/enum'
import { formatDate, formatTwoDecimalsMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { PHONE_REGEXP } from '@/common/rule'
import { getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const router = useRouter()

const componentRemoteSearch = reactive({
  unit_name: '',
})

const state = reactive<any>({
  isInformation: true,
  form: {
    sale_system_id: '',
    biz_unit_id: '',
    biz_unit_order_follower_id: '',
    biz_unit_order_follower_phone: '',
    receipt_grey_fabric_date: new Date(),
    receipt_grey_fabric_address: '',
    remark: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    biz_unit_order_follower_phone: {
      pattern: PHONE_REGEXP,
      message: '手机号格式不对',
      trigger: 'blur',
    },
  },
  tableData: [],
  filterData: {
    status: '',
    name: '',
  },
  multipleSelection: [],
  showAdd: false,
})
let id = 0
//   日期选择器
function disabledDate(time: Date) {
  return time.getTime() + 24 * 60 * 60 * 1000 < Date.now()
}

const tableConfig = ref({
  showSlotNums: false,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const columnList = ref([
  {
    field: 'sale_plan_order_no',
    title: '销售计划单号',
    soltName: 'sale_plan_order_no',
    minWidth: 100,
  },
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
    required: true,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
    required: true,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    soltName: 'customer_id',
    minWidth: 140,
    required: true,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 180,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 180,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 180,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 180,
  },
  {
    field: 'weight_of_fabric',
    soltName: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_id',
    soltName: 'grey_fabric_color_id',
    title: '织坯颜色',
    minWidth: 140,
  },
  {
    field: 'process_price',
    soltName: 'process_price',
    title: '加工单价',
    minWidth: 100,
  },
  {
    field: 'production_plan_type',
    soltName: 'production_plan_type',
    title: '计划类型',
    minWidth: 150,
    required: true,
  },
  {
    field: 'plan_roll',
    soltName: 'plan_roll',
    title: '计划匹数',
    minWidth: 100,
    required: true,
  },
  {
    field: 'plan_weight',
    soltName: 'plan_weight',
    title: '计划数量',
    minWidth: 100,
  },
  {
    field: 'use_stock_roll',
    soltName: 'use_stock_roll',
    title: '调库存匹数',
    minWidth: 100,
  },
  {
    field: 'remark',
    soltName: 'remark',
    title: '备注',
    minWidth: 100,
  },
])
// 计算计划数量
function setPlanWeight(row: any) {
  row.plan_weight = Number(row.plan_roll) * Number(row.weight_of_fabric)
}

function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 1)
        return '汇总'

      if (['plan_roll'].includes(column.field))
        return sumNum(data, 'plan_roll', '匹')

      if (['use_stock_roll'].includes(column.field))
        return sumNum(data, 'use_stock_roll', '匹')

      if (['plan_weight'].includes(column.field))
        return sumNum(data, 'plan_weight', '')

      return null
    }),
  ]
  return footerData
}
// 选择织厂名称需要带出跟单员&供应商地址
function selectFactoryName(item: any) {
  // 跟单员id
  state.form.biz_unit_order_follower_id = item?.order_follower_id
  // 供应商地址
  state.form.receipt_grey_fabric_address = item?.address
}

function setFollowerPhone(item: any) {
  // 跟单员电话
  state.form.biz_unit_order_follower_phone = item?.phone
}

// 删除
function handDell(row: any, rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}
// 全选中
// 将回选的数据也丢回出去
function handAllSelect({ records }: any) {
  state.multipleSelection = records
}
// 选中
function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

//   从销售计划单中添加--功能--未完成
const AddFabricDialogRef = ref()
function handSaleAdd(_val: number) {
  AddFabricDialogRef.value.state.showModal = true
}
function handSale(list: any) {
  // 带出form交坯日期、收坯地址
  state.form.receipt_grey_fabric_date = formatDate(list[0].receipt_time)
  state.form.receipt_grey_fabric_address = list[0].receipt_address
  const arr = list.map((item: any) => {
    return {
      ...item,
      sale_plan_order_item_no: item.detail_order_no,
      uuid: ++id,
      grey_fabric_id: item.grey_fabric_id,
      sale_plan_order_no: item.order_no,
      sale_plan_order_gf_detail_id: item.id,
      sale_plan_order_id: item.sale_product_plan_order_id,
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      finish_product_width: item.product_width,
      finish_product_gram_weight: item.product_gram_weight,
      weight_of_fabric: formatWeightDiv(item.weight_of_fabric),
      process_price: 0,
      production_plan_type: [1],
      use_stock_roll: 0,
      plan_weight: 0,
      plan_roll: (item.roll - item.planed_roll).toFixed(2),
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      customer_id: item.customer_id,
      grey_fabric_color_id: item?.grey_fabric_color_id || null,
      grey_fabric_width_unit_name: item.grey_fabric_width_unit_name,
      grey_fabric_gram_weight_unit_name: item.grey_fabric_gram_weight_unit_name,
      finish_product_width_unit_name: item.finish_product_width_unit_name,
      finish_product_gram_weight_unit_name: item.finish_product_gram_weight_unit_name,
    }
  })
  state.tableData = [...state.tableData, ...arr]
  AddFabricDialogRef.value.state.showModal = false
}
//   从资料中添加--功能
function handFabricAdd() {
  state.showAdd = true
}
function onSubmit(list: any) {
  const arr = list.map((item: any) => {
    return {
      ...item,
      uuid: ++id,
      grey_fabric_id: item.id,
      sale_plan_order_no: '',
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      finish_product_width: item.finish_product_width,
      finish_product_gram_weight: item.finish_product_gram_weight,
      weight_of_fabric: formatWeightDiv(item.weight_of_fabric),
      grey_fabric_color_id: item?.gray_fabric_color_id || null,
      process_price: item.process_price ? formatUnitPriceDiv(Number(item.process_price)) : 0,
      production_plan_type: [1],
      use_stock_roll: 0,
      plan_weight: 0,
      plan_roll: 0,
      grey_fabric_code: item.code,
      grey_fabric_name: item.name,
      grey_fabric_width_unit_name: item.grey_fabric_width_unit_name,
      grey_fabric_gram_weight_unit_name: item.grey_fabric_gram_weight_unit_name,
      finish_product_width_unit_name: item.finish_product_width_unit_name,
      finish_product_gram_weight_unit_name: item.finish_product_gram_weight_unit_name,
    }
  })

  state.tableData = [...state.tableData, ...arr]
  state.showAdd = false
}
const routerList = useRouterList()
const { fetchData: submitFetch, data: successData, success: submitSuccess, msg: submitMsg } = addSheetOfProductionPlan()
// 提交所有数据
const ruleFormRef = ref()
async function handleSubmit(formEl: FormInstance) {
  if (state.tableData.length < 1)
    return ElMessage.error('请至少添加一种坯布')

  await formEl.validate(async (valid: boolean) => {
    if (!valid)
      return
    // 需要校验表格必填数据
    for (let i = 0; i < state.tableData.length; i++) {
      if (!state.tableData[i].grey_fabric_code)
        return ElMessage.error('坯布编号为必填项')

      if (!state.tableData[i].grey_fabric_name)
        return ElMessage.error('坯布名称为必填项')

      if (!state.tableData[i].customer_id)
        return ElMessage.error('所属客户为必填项')

      if (!state.tableData[i].production_plan_type?.length)
        return ElMessage.error('计划类型为必填项')

      if (!Number(state.tableData[i].plan_roll))
        return ElMessage.error('计划匹数为必填项')
    }
    // 整理请求参数
    const query = {
      ...state.form,
      biz_unit_id: state.form.biz_unit_id || 0,
      biz_unit_order_follower_id: state.form.biz_unit_order_follower_id || 0,
      detail: state.tableData.map((item: any) => {
        return {
          ...item,
          uuid: undefined,
          biz_unit_id: Number(item.biz_unit_id),
          biz_unit_order_follower_id: Number(item.biz_unit_order_follower_id),
          use_stock_roll: formatTwoDecimalsMul(Number(item.use_stock_roll)),
          plan_roll: formatTwoDecimalsMul(Number(item.plan_roll)),
          plan_weight: formatWeightMul(Number(item.plan_weight)),
          weight_of_fabric: formatWeightMul(Number(item.weight_of_fabric)),
          process_price: formatUnitPriceMul(Number(item.process_price)),
          production_plan_type: item.production_plan_type.join(','),
        }
      }),
    }
    query.receipt_grey_fabric_date = formatDate(query.receipt_grey_fabric_date)

    await submitFetch(query)
    if (submitSuccess.value) {
      ElMessage.success('成功')
      routerList.push({
        name: 'SheetOfProductionPlanDetail',
        query: {
          id: successData.value.id,
        },
      })
    }
    else {
      ElMessage.error(submitMsg.value)
    }
  })
}

// 更新表格底部数据
const greyTableRef = ref<any>()
watch(
  [() => state.tableData],
  () => {
    greyTableRef.value.tableRef.updateFooter()
  },
  { deep: true },
)

// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    component: 'input',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重(g/单位)',
    component: 'input',
    type: 'text',
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    component: 'input',
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重(g/单位)',
    component: 'input',
    type: 'text',
  },
  {
    field: 'weight_of_fabric',
    title: '布匹定重(kg/单位)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'grey_fabric_color_id',
    field_name: 'name',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  {
    field: 'process_price',
    title: '加工单价(元/g/单位)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'production_plan_type',
    field_name: 'name',
    title: '计划类型',
    component: 'select',
    api: 'PlanTypeDropdownList',
    multiple: true,
  },
  {
    field: 'plan_roll',
    title: '计划匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'use_stock_roll',
    title: '调库存匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  const ids = state.multipleSelection.map((item: any) => item.uuid)

  state.tableData.map((item: any) => {
    if (ids.includes(item.uuid))
      item[row.field] = value[row.field]
  })
  ElMessage.success('设置成功')
  handBulkClose()
}
const bulkShow = ref(false)
function handEdit() {
  bulkShow.value = true
}
function handBulkClose() {
  bulkShow.value = false
}

function clearCustomer() {
  state.form.biz_unit_id = 0
  state.tableData.forEach((item: any) => {
    item.customer_id = ''
  })
}
// 跳转到销售计划单详情页面
function jumpPageSaleDetail(row: any) {
  router.push({
    name: 'ProductSalePlanDetail',
    query: {
      id: row.sale_plan_order_id,
    },
  })
}

// 选择客户
function handleChangeCustomer(val: any, row: any) {
  row.customer_name = val.name
  row.customer_code = val.code
}

onMounted(() => {
  const res = getDefaultSaleSystem()

  state.form.sale_system_id = res.default_sale_system_id
})
</script>

<template>
  <FildCard :tool-bar="false" title="基础信息">
    <template #right-top>
      <el-button type="primary" @click="handleSubmit(ruleFormRef)">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable @change-value="clearCustomer" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <el-form-item prop="biz_unit_id">
              <SelectDialog
                v-model="state.form.biz_unit_id"
                :query="{ sale_system_id: state.form.sale_system_id, unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.unit_name }"
                api="BusinessUnitSupplierEnumlist"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-value="selectFactoryName"
                @change-input="val => (componentRemoteSearch.unit_name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂跟单:">
          <template #content>
            <el-form-item prop="biz_unit_order_follower_id">
              <SelectComponents
                v-model="state.form.biz_unit_order_follower_id"
                watch-status
                default-status
                :query="{
                  duty: EmployeeType.follower,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
                @change-value="setFollowerPhone"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跟单电话:">
          <template #content>
            <el-form-item prop="biz_unit_order_follower_phone">
              <el-input v-model="state.form.biz_unit_order_follower_phone" placeholder="跟单电话" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="交坯日期:">
          <template #content>
            <el-form-item prop="receipt_grey_fabric_date">
              <el-date-picker v-model="state.form.receipt_grey_fabric_date" style="width: 100%" :disabled-date="disabledDate" type="date" placeholder="交坯日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收坯地址:" :copies="2">
          <template #content>
            <el-form-item prop="receipt_grey_fabric_address">
              <el-input v-model="state.form.receipt_grey_fabric_address" placeholder="收坯地址" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" :copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button :disabled="!state.multipleSelection.length" type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" @click="handFabricAdd">
        从资料中添加
      </el-button>
      <el-button :disabled="!state.form.sale_system_id" style="margin-left: 10px" type="primary" @click="handSaleAdd">
        从销售计划单中添加
      </el-button>
    </template>
    <Table ref="greyTableRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #sale_plan_order_no="{ row }">
        <el-link type="primary" @click="jumpPageSaleDetail(row)">
          {{ row.sale_plan_order_no }}
        </el-link>
      </template>
      <template #customer_id="{ row }">
        <SelectCustomerDialog
          v-model="row.customer_id"
          field="name"
          :query="{ sale_system_id: state.form.sale_system_id }"
          :default-value="{
            id: row.customer_id,
            name: row.customer_name,
            code: row.customer_code,
          }"
          @change-value="handleChangeCustomer($event, row)"
        />
      </template>
      <template #grey_fabric_width="{ row }">
        <!-- <vxe-input size="mini" v-model="row.grey_fabric_width" maxlength="200">
          <template #suffix>{{ row.grey_fabric_width_unit_name }}</template>
        </vxe-input> -->
        <el-input v-model="row.grey_fabric_width" clearable maxlength="200" class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        <!-- <vxe-input type="text" v-model="row.grey_fabric_gram_weight">
          <template #suffix>{{ row.grey_fabric_gram_weight_unit_name }}</template>
        </vxe-input> -->
        <el-input v-model="row.grey_fabric_gram_weight" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #finish_product_width="{ row }">
        <!-- <vxe-input size="mini" v-model="row.finish_product_width" maxlength="200">
          <template #suffix>{{ row.finish_product_width_unit_name }}</template>
        </vxe-input> -->
        <el-input v-model="row.finish_product_width" clearable maxlength="200" class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.finish_product_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #finish_product_gram_weight="{ row }">
        <!-- <vxe-input type="text" v-model="row.finish_product_gram_weight">
          <template #suffix>{{ row.finish_product_gram_weight_unit_name }}</template>
        </vxe-input> -->
        <el-input v-model="row.finish_product_gram_weight" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.finish_product_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #weight_of_fabric="{ row }">
        <vxe-input v-model="row.weight_of_fabric" type="float" @change="setPlanWeight(row)">
          <template #suffix>
            {{ row.unit_name }}
          </template>
        </vxe-input>
      </template>
      <template #grey_fabric_color_id="{ row }">
        <SelectComponents v-model="row.grey_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable />
      </template>
      <template #process_price="{ row }">
        <vxe-input v-model="row.process_price" type="float">
          <template #suffix>
            <span v-if="row.unit_name !== ''">元/{{ row.unit_name }}</span>
            <span v-else />
          </template>
        </vxe-input>
      </template>
      <template #production_plan_type="{ row }">
        <SelectComponents v-model="row.production_plan_type" size="small" multiple api="PlanTypeDropdownList" label-field="name" value-field="id" />
      </template>
      <template #plan_roll="{ row }">
        <vxe-input v-model="row.plan_roll" type="float" size="mini" maxlength="200" @change="setPlanWeight(row)" />
      </template>
      <template #plan_weight="{ row }">
        {{ Number(row.plan_weight).toFixed(2) }}{{ row.unit_name }}
      </template>
      <template #use_stock_roll="{ row }">
        <vxe-input v-model="row.use_stock_roll" type="float" size="mini" maxlength="200" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" size="mini" maxlength="200" />
      </template>
      <template #status="{ row }">
        <div class="flex items-center">
          <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
          <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </div>
        </div>
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handDell(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <AddFabricDialog ref="AddFabricDialogRef" type="sheetOfProductionPlan" :sale_system_id="state.form.sale_system_id" @handle-sure="handSale" />
  <SelectRawMaterial v-model="state.showAdd" @submit="onSubmit" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
    <template #address="{ row }">
      <el-form :model="bulkSetting[row.field]" :rules="row.rule" label-width="120px" label-position="left">
        <el-form-item prop="receipt_unit_id" label="收货单位">
          <SelectComponents
            v-model="bulkSetting[row.field].receipt_unit_id"
            api="GetBusinessUnitListApi"
            label-field="name"
            value-field="id"
            @select="val => (bulkSetting[row.field].receipt_unit_name = val.name)"
          />
        </el-form-item>
        <el-form-item prop="receipt_person" label="收货联系人">
          <el-input v-model="bulkSetting[row.field].receipt_person" />
        </el-form-item>
        <el-form-item prop="receipt_phone" label="收货电话">
          <el-input v-model="bulkSetting[row.field].receipt_phone" />
        </el-form-item>
        <el-form-item prop="receipt_address" label="收货地址">
          <el-input v-model="bulkSetting[row.field].receipt_address" />
        </el-form-item>
      </el-form>
    </template>
  </BulkSetting>
</template>

<style scoped>
.el-link {
  color: #0e7eff;
}
</style>
