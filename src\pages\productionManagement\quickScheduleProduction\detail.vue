<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { Back, Edit } from '@element-plus/icons-vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import { getQuickScheduleProductionDetail } from '@/api/quickScheduleProduction'
import { formatDate } from '@/common/format'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const detailData = ref<any>({})

// 状态映射
const statusMap: Record<number, { label: string, type: string }> = {
  1: { label: '待排产', type: 'warning' },
  2: { label: '已排产', type: 'primary' },
  3: { label: '生产中', type: 'success' },
  4: { label: '已完成', type: 'success' },
  5: { label: '已取消', type: 'danger' },
}

// 优先级映射
const priorityMap: Record<number, { label: string, type: string }> = {
  1: { label: '低', type: 'info' },
  2: { label: '中', type: 'primary' },
  3: { label: '高', type: 'warning' },
  4: { label: '紧急', type: 'danger' },
}

// 获取详情API
const { fetchData: detailFetch } = getQuickScheduleProductionDetail()

// 获取详情
async function getDetail() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少ID参数')
    router.back()
    return
  }

  loading.value = true
  try {
    const res = await detailFetch({ id: Number(id) })
    if (res)
      detailData.value = res
  }
  catch (error) {
    ElMessage.error('获取详情失败')
  }
  finally {
    loading.value = false
  }
}

// 编辑
function handleEdit() {
  router.push({
    name: 'QuickScheduleProductionEdit',
    query: { id: route.query.id },
  })
}

// 返回
function handleBack() {
  router.back()
}

onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="quick-schedule-production-detail">
    <FildCard title="快速排产单详情" :loading="loading">
      <template #header-right>
        <el-button :icon="Back" @click="handleBack">
          返回
        </el-button>
        <el-button
          v-if="detailData.status === 1"
          type="primary"
          :icon="Edit"
          @click="handleEdit"
        >
          编辑
        </el-button>
      </template>

      <div class="detail-content">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <span class="card-title">基本信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <DescriptionsFormItem label="排产单号">
                {{ detailData.schedule_no }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="状态">
                <el-tag
                  v-if="statusMap[detailData.status]"
                  :type="statusMap[detailData.status].type"
                >
                  {{ statusMap[detailData.status].label }}
                </el-tag>
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="优先级">
                <el-tag
                  v-if="priorityMap[detailData.priority]"
                  :type="priorityMap[detailData.priority].type"
                >
                  {{ priorityMap[detailData.priority].label }}
                </el-tag>
              </DescriptionsFormItem>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <DescriptionsFormItem label="创建时间">
                {{ formatDate(detailData.create_time) }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="更新时间">
                {{ formatDate(detailData.update_time) }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
        </el-card>

        <!-- 生产通知单信息 -->
        <el-card v-if="detailData.production_notice" class="info-card">
          <template #header>
            <span class="card-title">生产通知单信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <DescriptionsFormItem label="通知单号">
                {{ detailData.production_notice.order_no }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="坯布名称">
                {{ detailData.production_notice.grey_fabric_name }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="坯布编码">
                {{ detailData.production_notice.grey_fabric_code }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <DescriptionsFormItem label="客户名称">
                {{ detailData.production_notice.customer_name }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="计划产量">
                {{ detailData.production_notice.plan_quantity }} {{ detailData.production_notice.unit_name }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
        </el-card>

        <!-- 排产信息 -->
        <el-card class="info-card">
          <template #header>
            <span class="card-title">排产信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <DescriptionsFormItem label="机台">
                {{ detailData.machine?.name || '未分配' }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="机台编码">
                {{ detailData.machine?.code || '-' }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="机台类型">
                {{ detailData.machine?.type_name || '-' }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <DescriptionsFormItem label="织工">
                {{ detailData.weaver?.name || '未分配' }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="织工编码">
                {{ detailData.weaver?.code || '-' }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="8">
              <DescriptionsFormItem label="技能等级">
                {{ detailData.weaver?.skill_level || '-' }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
        </el-card>

        <!-- 时间信息 -->
        <el-card class="info-card">
          <template #header>
            <span class="card-title">时间信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <DescriptionsFormItem label="计划开始时间">
                {{ formatDate(detailData.plan_start_time) || '未设置' }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="12">
              <DescriptionsFormItem label="计划结束时间">
                {{ formatDate(detailData.plan_end_time) || '未设置' }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <DescriptionsFormItem label="实际开始时间">
                {{ formatDate(detailData.actual_start_time) || '未开始' }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="12">
              <DescriptionsFormItem label="实际结束时间">
                {{ formatDate(detailData.actual_end_time) || '未完成' }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
        </el-card>

        <!-- 产量信息 -->
        <el-card class="info-card">
          <template #header>
            <span class="card-title">产量信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <DescriptionsFormItem label="计划产量">
                {{ detailData.plan_quantity || 0 }}
              </DescriptionsFormItem>
            </el-col>
            <el-col :span="12">
              <DescriptionsFormItem label="实际产量">
                {{ detailData.actual_quantity || 0 }}
              </DescriptionsFormItem>
            </el-col>
          </el-row>
        </el-card>

        <!-- 备注信息 -->
        <el-card v-if="detailData.remark" class="info-card">
          <template #header>
            <span class="card-title">备注信息</span>
          </template>
          <div class="remark-content">
            {{ detailData.remark }}
          </div>
        </el-card>
      </div>
    </FildCard>
  </div>
</template>

<style lang="scss" scoped>
.quick-schedule-production-detail {
  .detail-content {
    .info-card {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .card-title {
        font-weight: bold;
        color: #303133;
      }
    }

    .remark-content {
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      line-height: 1.6;
      color: #606266;
    }
  }
}
</style>
