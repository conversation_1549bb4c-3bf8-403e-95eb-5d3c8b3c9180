<script lang="ts" setup name="RawDyeingNoticeEdit">
import { ElMessage } from 'element-plus'
import { onActivated, reactive, ref, toRaw, watch } from 'vue'
import { useRoute } from 'vue-router'
import AddSalePlanDialog from '../components/AddSalePlanDialog.vue'
import RawTable from './components/RawTable.vue'
import SelectInformation from './components/SelectInformation.vue'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import FildCard from '@/components/FildCard.vue'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'
import { deepClone, deleteToast } from '@/common/util'
import { formatDate, formatPriceDiv, formatPriceMul, formatWeightDiv, formatWeightMul } from '@/common/format'
import { raw_process_orderoutput, raw_process_orderoutputdetail } from '@/api/rawdyeingNotice'
import SelectDialog from '@/components/SelectDialog/index.vue'

function checkPhone(rule: any, value: any, callback: any) {
  const reg = /^(?:(?:\+|00)86)?1\d{10}$/
  if (reg.test(value) || value === '')
    callback()
  else
    callback(new Error('请输入正确的手机号码'))
}

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
  biz_unit_name: '',
})

function handleInput(val: any) {
  componentRemoteSearch.biz_unit_name = val
}

const router = useRouterList()

const state = reactive<any>({
  typeList: [
    {
      id: 1,
      checked: true,
      name: '坯纱染整',
    },
    {
      id: 2,
      checked: false,
      name: '加工',
    },
    {
      id: 3,
      checked: false,
      name: '回修',
    },
  ],
  tableList: [],
  form: {
    sale_system_id: '', // 营销体系
    factory_id: '',
    biz_unit_id: '',
    dye_factory_name: '', // 染厂名称
    date: '', // 染整日期
    factory_follow: '', // 染厂跟单
    remark: '',
    lights: '',
    packing: '',
    finishing: '',
    sale_system_name: '',
    order_follower_name: '',
    phone: '',
    address: '',
    signing_addr: '',
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    factory_id: [{ required: true, message: '请选择染纱厂名称', trigger: 'blur' }],
    date: [{ required: true, message: '请选择染整日期', trigger: 'blur' }],
    biz_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'blur' }],
    phone: [{ trigger: 'blur', validator: checkPhone }],
  },
  multipleSelection: [],
})

const route = useRoute()

const { fetchData, data } = raw_process_orderoutputdetail()

onActivated(async () => {
  await fetchData({ id: route.query.id })
})

watch(
  () => data.value,
  (newValue) => {
    if (data.value) {
      state.form.sale_system_id = newValue.sale_system_id
      state.form.factory_id = newValue.dye_unit_id
      state.form.dye_factory_name = newValue.dye_unit_name
      state.form.date = formatDate(newValue.dye_date)
      state.form.factory_follow = newValue.dye_unit_follower_id
      state.form.phone = newValue.dye_unit_follower_phone
      state.form.remark = newValue.remark
      state.form.address = newValue.receive_address
      state.form.biz_unit_id = !newValue.receive_unit_id ? '' : newValue.receive_unit_id
      state.form.finishing = newValue.dye_requirement
      state.typeList.map((item: any) => {
        item.checked = item.id === newValue.dye_type
      })

      for (let i = 0; i < newValue?.item_data?.length; i++) {
        const row = newValue?.item_data[i]
        const temp = {
          weight: formatWeightDiv(row?.weight),
          delivery_date: formatDate(row?.delivery_date),
          dyeing_loss: formatPriceDiv(row?.dyeing_loss),
          code: row?.raw_material_code,
          name: row?.raw_material_name,
          color_code: row?.raw_material_color_code,
          color_name: row?.raw_material_color_name,
          type_name: row?.raw_material_color_type_name,
          fc_code: row?.dye_unit_color_code,
          fc_name: row?.dye_unit_color_name,
          fc_type_name: row?.dye_unit_color_type_name,
          fc_dyelot: row?.dyelot_number,
          unit_name: row?.measurement_unit_name,
          level_code: row?.raw_material_level_code,
        }
        state.tableList[i] = { ...row, ...temp }
        for (let q = 0; q < row.item_data?.length; q++) {
          const r = row.item_data[q]
          const tp = {
            yarn_loss: Number(formatPriceDiv(r.yarn_loss)),
            yarn_ratio: Number(formatPriceDiv(r.yarn_ratio)),
            use_yarn_quantity: Number(formatWeightDiv(r.use_yarn_quantity)),
            raw_remark: r.stock_remark,
            color_code: r.raw_material_color_code,
            color_name: r.raw_material_color_name,
            level_name: r.raw_material_level_name,
            whole_count: formatPriceDiv(r.whole_count),
            bulk_count: formatPriceDiv(r.whole_count),
          }
          state.tableList[i].item_data[q] = { ...r, ...tp }
        }
      }
    }
  },
)

function factoryChange(val: any) {
  state.form.dye_factory_name = val.name
  state.form.factory_follow = val.order_follower_id
  state.form.phone = val?.order_follower_phone
  state.form.address = val.address
  if (state.tableList.length) {
    // 把里面的色号id全部置空
    state.tableList.map((item: any) => {
      item.raw_material_color_id = ''
      item.color_name = ''
      item.color_code = ''
      item.type_name = ''
      item.raw_material_level_id = ''
      item.level_code = ''
      item.fc_code = ''
      item.fc_name = ''
      item.fc_type_name = ''
      item.fc_dyelot = ''
      item.dye_unit_color_id = ''
      item.item_data = []
    })
  }
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    title: '客户名称',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  {
    field: 'dyeing_loss',
    title: '染损',
    component: 'input',
    type: 'float',
  },
  {
    field: 'weight',
    title: '数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'delivery_date',
    title: '交期',
    component: 'selectDate',
    type: 'date',
  },
  {
    field: 'raw_material_level_id',
    title: '原料等级',
    component: 'select',
    api: 'GetInfoBaseRawMaterialLevelEnumList',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
function changSystem() {
  state.tableList.map((item: any) => {
    item.customer_id = ''
    return item
  })
  bulkList[0].query = { sale_system_id: state.form.sale_system_id }
}

async function onChange(index: number) {
  const res = await deleteToast('切换染整类型，平台将不会保存你所做的更改')
  if (res) {
    state.typeList.map((it: any, inx: number) => {
      if (inx === index)
        it.checked = true
      else
        it.checked = false
    })
    // if (index === 0) {
    //   state.isDyeing = true
    // } else {
    //   state.isDyeing = false
    // }
    state.tableList = []
  }
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = raw_process_orderoutput()

async function handleSure() {
  let isValid
  await ruleFormRef.value.validate(async (valid: any) => {
    isValid = valid
  })
  // 如果校验不通过，不提交
  if (!isValid)
    return

  if (!state.tableList.length)
    return ElMessage.error('至少添加一条原料信息')

  const list = toRaw(state.tableList)
  for (let i = 0; i < list.length; i++) {
    if (list[i].customer_id === '')
      return ElMessage.error('请选择所属客户')

    if (list[i].raw_material_color_id === '')
      return ElMessage.error('请选择色号')

    if (list[i].weight === '')
      return ElMessage.error('数量不可为空')

    // 因为要对原始数据进行修改，所以要先做完无影响的判断
    if (!list[i].item_data?.length)
      return ElMessage.error('至少添加一条用坯信息')

    for (let q = 0; q < list[i].item_data?.length; q++) {
      // 先处理后面的数据可能会导致
      list[i].item_data[q].use_yarn_quantity = Number(formatWeightMul(list[i].item_data[q].use_yarn_quantity))
      if (list[i].item_data[q].use_yarn_quantity === 0)
        return ElMessage.error('用纱量不可为0')

      list[i].item_data[q].yarn_loss = Number(formatPriceMul(list[i].item_data[q].yarn_loss))
      list[i].item_data[q].yarn_ratio = Number(formatPriceMul(list[i].item_data[q].yarn_ratio))
      list[i].item_data[q].whole_count = Number(formatPriceMul(list[i].item_data[q].whole_count))
      list[i].item_data[q].bulk_count = Number(formatPriceMul(list[i].item_data[q].bulk_count))
      list[i].item_data[q].dye_unit_color_id = list[i].dye_unit_color_id
    }

    list[i].weight = Number(formatWeightMul(list[i].weight))
    list[i].dyeing_loss = Number(formatPriceMul(list[i].dyeing_loss))
    list[i].delivery_date = formatDate(list[i].delivery_date)
  }

  const nums = state.typeList.filter((item: any) => {
    return item.checked
  })

  const query = {
    ...state.form,
    dye_date: formatDate(state.form.date),
    dye_requirement: state.form.finishing,
    dye_type: nums[0].id,
    dye_unit_follower_id: Number(state.form.factory_follow),
    dye_unit_follower_phone: state.form.phone.toString(),
    dye_unit_id: state.form.factory_id,
    item_data: list,
    receive_address: state.form.address,
    receive_unit_id: Number(state.form.biz_unit_id),
    remark: state.form.remark,
    sale_system_id: state.form.sale_system_id,
    id: Number(route.query.id),
  }

  await addPost(query)
  if (addSuccess.value) {
    ElMessage.success('成功')
    router.push({ name: 'RawDyeingNoticeDetail', query: { id: addData.value.id } })
  }
  else {
    ElMessage.error(addMsg.value)
  }
}

const showAdd = ref()

function handInfomation() {
  showAdd.value = true
}

const AddSalePlanDialogRef = ref()

function handSalesAdd() {
  AddSalePlanDialogRef.value.state.showModal = true
  AddSalePlanDialogRef.value.state.filterData.plan_type_list = 3
  AddSalePlanDialogRef.value.state.dye_factory_id = state.form.factory_id
}

function handSalePlanAdd(list: any) {
  list.forEach((item: any) => {
    const newItem = deepClone(item)
    delete newItem.id
    state.tableList.push({
      ...newItem,
      sale_plan_order_item_no: item.detail_order_no,
      code: item.raw_material_code,
      name: item.raw_material_name,
      craft: item?.product_craft,
      hand_feeling: item?.touch_style,
      // sale_plan_order_no: item.order_code,
      sale_plan_type_name: '',
      customer_id: item.customer_id,
      customer_name: item?.customer_name,
      dyelot: item.dyelot_number,
      level: item.product_level_name,
      level_code: item?.product_level_code,
      level_id: item.product_level_id,
      tape_specs: item?.tape_specs,
      increase_weight: 0,
      width: item.product_width,
      gram_weight: item.product_gram_weight,
      dnf_loss: formatPriceDiv(item?.dyeing_loss),
      dyeing_loss: formatPriceDiv(item?.dyeing_loss),
      piece_count: formatPriceDiv(item.roll),
      weight: formatWeightDiv(item.weight),
      delivery_date: '',
      remark: '',
      use_fabric: [],
      grey_fabric_code: item.product_grey_fabric_code,
      grey_fabric_id: item.product_grey_fabric_id,
      grey_fabric_name: item.product_grey_fabric_name,
      color_id: item.raw_material_color_id,
      color_no: item.product_color_code,
      color_code: item.raw_material_color_code,
      color_name: item.raw_material_color_name,
      fc_dyelot: item.raw_material_dye_lot_number,
      df_color_no: item.dye_factory_color_code,
      paper_tube_specs: item.paper_tube_specs,
      dnf_craft: item.dyeing_craft,
      finish_product_id: item.product_id,
      unit: item.measurement_unit_name,
      style_no: item.customer_account_num,
      sale_plan_item_id: item.id,
      sale_plan_order_no: item.order_no,
      unit_id: item.measurement_unit_id,
      plastics_bag_ids: item?.tape_specs_ids,
      paper_tube_ids: item?.paper_tube_specs_ids,
      dnf_craft_ids: item?.dye_craft_ids,

      fc_code: item?.raw_material_dye_factory_color_code,
      fc_name: item?.raw_material_dye_factory_color_name,
      fc_type_name: item?.raw_material_dye_factory_color_kind_name,

      width_unit_id: item.finish_product_gram_weight_unit_id,
      gram_weight_unit_id: item.finish_product_width_unit_id,
    })
  })

  AddSalePlanDialogRef.value.state.showModal = false
}

function onSubmit(list: any) {
  showAdd.value = false
  list.forEach((item: any) => {
    state.tableList.push({
      code: item.code,
      name: item.name,
      raw_material_id: item.id,
      unit_name: item.unit_name,
      remark: '',
      customer_id: '',
      delivery_date: '',
      dye_unit_color_id: '',
      dyeing_loss: '',
      raw_material_color_id: '',
      raw_material_level_id: '',
      weight: '',
      item_data: [],
    })
  })
}

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

const bulkSetting = ref<any>({})

async function bulkSubmit({ row, value }: any, val: any, field: string) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })

  if (field === 'raw_material_level_id') {
    state.tableList?.map((item: any) => {
      if (item?.selected) {
        item.level_code = val.code
        return item
      }
    })
  }
  bulkShow.value = false
  ElMessage.success('设置成功')
}
function handBulkClose() {
  bulkShow.value = false
}
</script>

<template>
  <FildCard title="染整类型" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-check-tag v-for="(item, index) in state.typeList" :key="index" class="mr-[10px]" :checked="item.checked" @change="onChange(index)">
      {{ item.name }}
    </el-check-tag>
  </FildCard>
  <FildCard title="基础信息" class="mt-[5px]" :tool-bar="false">
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:" required>
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable @select="changSystem" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂名称:" required>
          <template #content>
            <el-form-item prop="factory_id">
              <SelectDialog
                v-model="state.form.factory_id"
                :label-name="data.dye_unit_name"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill, category: 1, name: componentRemoteSearch.name }"
                api="GetBusinessUnitListApi"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-value="factoryChange"
                @change-input="val => (componentRemoteSearch.name = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂跟单:">
          <template #content>
            <SelectComponents
              v-model="state.form.factory_follow"
              :query="{ duty: EmployeeType.follower }"
              api="Adminemployeelist"
              label-field="name"
              value-field="id"
              clearable
              @change-value="val => ((state.form.order_follower_name = val.name), (state.form.phone = val.phone))"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跟单电话:">
          <template #content>
            <el-form-item prop="phone">
              <el-input v-model="state.form.phone" type="text" maxlength="11" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整日期:" required>
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="染整日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货单位:" required>
          <template #content>
            <el-form-item prop="biz_unit_id">
              <!--              <SelectComponents -->
              <!--                @changeValue="val => (state.form.address = val.address)" -->
              <!--                :query="{ unit_type_id: `${BusinessUnitIdEnum.knittingFactory},${BusinessUnitIdEnum.dyeingMill}`, category: 1 }" -->
              <!--                style="width: 200px" -->
              <!--                api="business_unitlist" -->
              <!--                label-field="name" -->
              <!--                value-field="id" -->
              <!--                v-model="state.form.biz_unit_id" -->
              <!--                clearable -->
              <!--              /> -->
              <SelectDialog
                v-model="state.form.biz_unit_id"
                api="business_unitlist"
                :query="{ unit_type_id: `${BusinessUnitIdEnum.knittingFactory},${BusinessUnitIdEnum.dyeingMill}`, category: 1, name: componentRemoteSearch.biz_unit_name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @on-input="handleInput"
                @change-value="val => (state.form.address = val.address)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址:">
          <template #content>
            <el-input v-model="state.form.address" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="签约地:">
          <template #content>
            <el-form-item prop="signing_addr">
              <el-input v-model="state.form.signing_addr" placeholder="签约地" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-form-item prop="remark">
              <el-input v-model="state.form.remark" placeholder="单据备注" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="原料信息" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" :disabled="state.multipleSelection.length <= 0" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" @click="handInfomation">
        从资料中添加
      </el-button>
      <el-button
        :disabled="
          state.form.sale_system_id === '' || state.form.factory_id === ''
        "
        type="primary"
        @click="handSalesAdd"
      >
        从销售计划单中添加
      </el-button>
    </template>
    <RawTable v-model:select-list="state.multipleSelection" v-model="state.tableList" :info="state.form" />
  </FildCard>
  <FildCard title="其他要求" :tool-bar="false" class="mt-[5px]">
    <div class="flex flex-row">
      <div class="flex flex-1">
        <div>染整要求：</div>
        <div class="w-[300px]">
          <el-input v-model="state.form.finishing" maxlength="2000" show-word-limit :autosize="{ minRows: 5, maxRows: 15 }" type="textarea" />
        </div>
      </div>
    </div>
  </FildCard>
  <SelectInformation v-model="showAdd" @submit="onSubmit" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
  <AddSalePlanDialog
    ref="AddSalePlanDialogRef"
    @handle-sure="handSalePlanAdd"
  />
</template>

<style></style>
