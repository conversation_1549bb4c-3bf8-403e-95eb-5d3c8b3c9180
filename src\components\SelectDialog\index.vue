<script setup lang="ts">
import { Check } from '@element-plus/icons-vue'
import type { Ref } from 'vue'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

import type { VxeGridInstance } from 'vxe-table'
import { VxeGrid } from 'vxe-table'
import { watchOnce } from '@vueuse/core'
import { isEmpty, isEqual } from 'lodash-es'
import BottomBar from '../SelectComponents/SelectBottomBar.vue'
import ComponentSelectDialog from './ComponentSelectDialog.vue'
import QywxPopover from './QywxPopover.vue'
import selectApi from '@/api/selectInit'
import { debounce, deepClone, deleteToast, getFilterData } from '@/common/util'
import { useComponentsRequestStore } from '@/stores/requestCaching'
import { generateReqKey } from '@/util/commonFuns'
import type { RequestOptions } from '@/use/useRequest'
import type { TableColumn, TableColumnType } from '@/components/Table/type'
import { useUserStore } from '@/stores'

interface Props {
  modelValue: string | number
  api: keyof typeof selectApi | ''
  placeholder: string
  query?: Record<string, any>
  needRefresh?: boolean // 是否需要新鲜数据
  columnList?: TableColumn[]
  tableList?: any[] // 可外部传递数据给内部使用，避免表格内有多个 SelectDialog 时因为初始化多次而重复请求

  modal_name?: string // 弹框名
  valueField?: string
  labelField?: string
  disabled?: boolean
  tableColumn?: TableColumn[]
  multiple: boolean // 多选传入数组
  dialogType?: string // 用来判断是否用于客户名称
  labelName?: string
  editable?: boolean // 是否可编辑
  validConfig?: any // 校验配置
  queryConfig?: RequestOptions
  toastMsg?: string
  showDialog?: boolean
  tableConfig?: TableColumnType
  isPushDefaultData?: boolean // 是否需要追加默认数据-分页获取不到的
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  api: '',
  query: () => ({}),
  showDialog: true,
  placeholder: '点击选择内容',
  tableConfig: () => ({}),
  columnList: () => [
    {
      field: 'code',
      title: '客户编号',
      minWidth: 100,
      soltName: 'code',
      isEdit: true,
    },
    {
      field: 'name',
      title: '客户名称',
      minWidth: 100,
      soltName: 'name',
      required: true,
      isEdit: true,
    },
    {
      field: 'phone',
      title: '电话',
      minWidth: 100,
      soltName: 'phone',
      isEdit: true,
    },
    {
      field: 'seller_name',
      title: '销售员',
      minWidth: 100,
      soltName: 'seller_name',
      isEdit: true,
    },
    // {
    //   field: 'order_follower_name',
    //   title: '跟单员',
    //   minWidth: 100,
    // },
  ],
  validConfig: {
    name: [
      { required: true, message: '请输入名称' },
      {
        validator({ cellValue }) {
          // 模拟服务端校验
          return new Promise((resolve, reject) => {
            if (cellValue === '')
              reject(new Error('客户名称不可为空'))
            else resolve('')
          })
        },
      },
    ],
  },
  modal_name: '请选择',
  valueField: 'id',
  labelField: 'name',
  disabled: false,
  tableColumn: () => [
    {
      field: 'name',
      title: '名称',
      defaultData: {}, // 默认数据
    },
  ],
  multiple: false,
  dialogType: '',
  labelName: '',
  needRefresh: false,
  toastMsg: '客户不存在，是否新建',
  isPushDefaultData: true,
})
const emits = defineEmits<{
  (e: 'update:modelValue', value: Props['modelValue']): void
  (e: 'changeValue', value: any): void
  (e: 'changeInput', value: any): void
  (e: 'addData'): void
  (e: 'onInput', value: string): void
}>()
const teleportElement = ref()
const pulldownRef = ref()
const clearFlag = ref(false)
const query = ref<Props['query']>(props.query)
const item = ref()
const dropdown4Ref = ref<HTMLElement>()
const clearIconRef = ref<HTMLElement>()
// 页面点击事件
function handleClick(e: any) {
  if (
    !(
      pulldownRef.value.$el?.contains(e.target)
      || dropdown4Ref.value?.contains(e.target) || clearIconRef.value?.contains(e.target)
    )
  )
    clearFlag.value = false
}
onMounted(() => {
  document.addEventListener('click', handleClick)
  getTeleportElement()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClick)
})

/**
 * 表格弹窗Teleport挂载元素
 * 不能挂载在表格单元格里面，会出现show-overflow错误，
 * 不能挂载在主体内容内,会导致切换页面tabs仍显示弹窗
 * 现挂载在当前页面的第一个元素
 */
function getTeleportElement() {
  nextTick(() => {
    // const mainWrapperElement = document.querySelector('#pageContainer')
    // teleportElement.value = mainWrapperElement || 'body'
    const mainWrapperElement = document.querySelector('.main-wrapper')
    teleportElement.value = mainWrapperElement?.firstElementChild || 'body'
  })
}

const inputLabel = ref('')
const ComponentSelectDialogRef = ref()

// 显示弹框
function handleShowDialog() {
  if (props.disabled)
    return
  pulldownRef.value.hidePanel()

  ComponentSelectDialogRef.value.showDialog(
    props.columnList,
    props.editable,
    props.validConfig,
  )
}

const girdRef = ref<VxeGridInstance>()

// 只有弹框选择确认才会触发
async function selectRow(arr: any) {
  if (!props.multiple) {
    // 单选
    inputLabel.value = arr[0] ? arr[0][props.labelField] : ''
    item.value = arr[0]

    emits('changeValue', arr[0] || {})
    // emits('onInput', inputLabel.value)
    emits('update:modelValue', arr[0] ? arr[0][props.valueField] : undefined)
  }
  else {
    const nameArr: any = []
    const idArr: any = []

    arr?.forEach((item: any) => {
      nameArr.push(item[props.labelField as any])
      idArr.push(item[props.valueField as any])
    })

    inputLabel.value = nameArr?.join?.('、')
    emits('changeValue', arr || [])
    emits('update:modelValue', idArr)
  }
}
const key = ref()
// 存放在store的key名
function setStoreKey() {
  const config = {
    data: {},
    url: props.api,
    method: 'get',
    params: query.value,
  }
  key.value = generateReqKey(config)
}

const options = ref<Props['tableList']>([])

const componentsRequestStore = useComponentsRequestStore()

const { fetchData, data, total, page, size } = selectApi[props.api]?.(
  props.queryConfig,
)
const tableData = ref<any>([])
// 判断 query 是否改变
let isQueryChanged = false
// 获取数据
async function getData(fn = setData) {
  setStoreKey()
  const filterQuery = getFilterData(query.value)
  if (!componentsRequestStore.list[key.value] || isQueryChanged) {
    componentsRequestStore.addCount(key.value)

    if (props.tableList && isEmpty(filterQuery)) {
      options.value = props.tableList
    }
    else {
      await fetchData(filterQuery)
      options.value = data.value?.list || data.value?.sub_department
    }

    isQueryChanged = false

    componentsRequestStore.setRequestData(key.value, {
      data: options.value,
      total: total.value,
      page: page.value,
      size: size.value,
    })
  }
  else {
    const {
      data,
      size: cacheSize,
      page: cachePage,
      total: cacheTotal,
    } = componentsRequestStore.list[key.value]
    options.value = data
    total.value = cacheTotal
    page.value = cachePage
    size.value = cacheSize
  }

  tableData.value = options.value
  // TODO:勾选多选回选数据
  if (props.multiple) {
    const multipleArr: any = props.modelValue === '' ? [] : props.modelValue
    tableData.value.map((item: any) => {
      item.isCheck = false
      return item
    })
    tableData.value.map((item: any) => {
      multipleArr?.forEach((it: any) => {
        if (item?.[props.valueField as any] === it)
          item.isCheck = true

        return item
      })
    })
  }
  fn()
}
// 请求参数变化
watch(
  () => props.query,
  debounce((newQuery) => {
    if (isEqual(newQuery, query.value))
      return
    isQueryChanged = true
    query.value = newQuery
    getData(() => {
      // 高亮第一行
      girdRef.value?.setCurrentRow(girdRef.value?.getData(0))
    })
  }, 400),
  {
    deep: true,
  },
)

const fieldNotFond = ref(false)

// getData回调  拿到对应value的全部数据
function setData() {
  if (options.value && options.value.length) {
    const index = options.value.findIndex(
      (item: any) => item[props.valueField] === props.modelValue,
    )

    if (index > -1) {
      const current = options.value[index]
      inputLabel.value = current ? current[props.labelField] : ''

      item.value = current
      girdRef.value?.scrollToRow(current)
      girdRef.value?.setCurrentRow(current)
      // 请求完成后，不应该触发 changeValue 事件，应该是用户改变value时才触发 changeValue
      // emits('changeValue', current)
      emits('update:modelValue', current ? current[props.valueField] : '')
    }
    else {
      // fieldNotFond.value = true
      setDefaultField()
      girdRef.value?.setCurrentRow(girdRef.value?.getData(0))
    }
  }
}
// 设置默认值
function setDefaultField() {
  //   因为分页 搜索不到
  if (props.tableColumn[0].defaultData) {
    const defaultData = props.tableColumn[0].defaultData
    // 多选-需要有默认值才重置
    if (props.multiple && defaultData[props.labelField])
      inputLabel.value = defaultData[props.labelField]
    else if (!props.multiple)
      inputLabel.value = defaultData[props.labelField]
    item.value = defaultData
    if (props.isPushDefaultData)
      options.value.push(defaultData)
  }
}

watchOnce(fieldNotFond, (isNotFont) => {
  if (isNotFont)
    setDefaultField()
})

const loading = ref(false)
// 获取焦点事件
async function focusEvent() {
  loading.value = true

  if (props.needRefresh) {
    await fetchData(getFilterData(query.value))
    tableData.value = data.value.list
    options.value = data.value.list
    loading.value = false
    const $pulldown = pulldownRef.value
    if ($pulldown)
      $pulldown.showPanel()

    girdRef.value?.setCurrentRow(girdRef.value?.getData(0))
  }
  else {
    getData(() => {
      const $pulldown = pulldownRef.value
      if ($pulldown)
        $pulldown.showPanel()

      loading.value = false
      nextTick(() => {
        setData()
      })
    })
  }
}
// 处理回车键是默认第一个选项
function handleInputKeyup($event: KeyboardEvent) {
  if (options.value?.length) {
    const currentRow
      = girdRef.value?.getCurrentRecord() ?? girdRef.value?.getData(0)
    const currentIndex = girdRef.value?.getRowIndex(currentRow)

    switch ($event.keyCode) {
      case 38: {
        // 上
        const prev = girdRef.value?.getData(currentIndex - 1)
        if (prev) {
          girdRef.value?.setCurrentRow(prev)
          girdRef.value?.scrollToRow(prev)
        }
        break
      }
      case 40:
        {
          // 下
          const next = girdRef.value?.getData(currentIndex + 1)
          if (next) {
            girdRef.value?.setCurrentRow(next)
            girdRef.value?.scrollToRow(next)
          }
        }
        break
      case 13:
        // 回车
        // 模拟点击第一行
        cellClickEvent({ row: girdRef.value?.getData(currentIndex) })
        girdRef.value?.scrollToRow(currentRow)
        break
      default:
        break
    }
  }
}

// 键盘输入事件
// const keyupEvent = () => {
//   loading.value = true
//   setTimeout(() => {
//     if (inputLabel.value) {
//       tableData.value = options.value?.filter(item => item[props.labelField]?.includes(inputLabel.value))
//     } else {
//       tableData.value = options.value
//     }
//     loading.value = false
//   }, 100)
// }
// 选中事件
function cellClickEvent({ row }: any) {
  selectRow([row])
  pulldownRef.value.hidePanel()
}
const isRefresh = ref(false) // 是否正在刷新数据
// 失焦事件
async function blurEvent() {
  // 失焦先隐藏
  setTimeout(() => {
    // 点击刷新按钮的不隐藏面板
    if (isRefresh.value)
      isRefresh.value = false
    else
      pulldownRef.value.hidePanel()
  }, 250)

  const index = options.value?.findIndex(
    (item: any) => item[props.valueField] === props.modelValue,
  )
  if (index == null)
    return
  if (index !== -1) {
    inputLabel.value = options.value[index]
      ? options.value[index][props.labelField]
      : ''
    item.value = options.value[index]
  }
  const filterList = options.value?.filter(item =>
    item[props.labelField]?.includes(inputLabel.value),
  )

  if (props.needRefresh && !filterList.length && inputLabel.value !== '') {
    const res = await deleteToast(props.toastMsg)
    if (res)
      emits('addData')
  }
}
// 输入事件
function inputEvent(value: any) {
  inputLabel.value = value.trim()
  emits('changeInput', value)
  emits('onInput', value)
}
// 清空事件
const vxeInputRef = ref<any>()

function resetCheckStatus() {
  tableData.value?.map((item: any) => {
    item.isCheck = false
    return item
  })
}

function clearEvent() {
  tableData.value = options.value
  item.value = null
  inputLabel.value = ''
  // 读取传入的query
  query.value = deepClone(props.query)
  resetCheckStatus()
  emits('changeValue', props.multiple ? [] : {})
  emits('update:modelValue', '')
  emits('changeInput', '')
  emits('onInput', '')
}

// async function getInputLabel() {
//   await getData(() => {
//     const index = options.value?.findIndex(
//       (item: any) => item[props.valueField] === props.modelValue,
//     )
//     if (index > -1) {
//       girdRef.value?.setCurrentRow(girdRef.value?.getData(index))
//       inputLabel.value = options.value[index]
//         ? options.value[index][props.labelField]
//         : ''
//       item.value = options.value[index]
//     }
//   })
// }

watch(
  () => props.modelValue,
  async (newValue) => {
    if (!props.multiple) {
      if (newValue) {
        getData()
      }
      else {
        if (props.labelName === '') {
          // TODO: 防止进来有数据修改的时候直接清空
          inputLabel.value = ''
        }
        item.value = {}
      }
    }
    else {
      // 回显多选的数据
      const seletNameArr: any = []
      await getData()

      tableData.value?.forEach((item: any) => {
        if (item?.isCheck)
          seletNameArr.push(item[props.labelField as any])
      })

      inputLabel.value = seletNameArr?.join?.('、')
    }
  },
  {
    immediate: true,
  },
)

watch(
  () => inputLabel.value,
  (newValue) => {
    if (newValue === '') {
      clearEvent()
      getData(() => {})
    }
  },
)

watch(
  () => props.labelName,
  () => {
    if (props.labelName !== '') {
      // 加定时器是防止被双向绑定的值置空数据
      setTimeout(() => {
        inputLabel.value = props.labelName as string
      }, 700)
    }
  },
  { immediate: true },
)

function handSelectMore(it: any) {
  tableData.value.map((item: any) => {
    if (item.id === it.id)
      item.isCheck = !item.isCheck

    return item
  })
  const arr = tableData.value.filter((item: any) => {
    return item.isCheck
  })
  const nameArr: any = []
  const idArr: any = []

  arr?.forEach((item: any) => {
    nameArr.push(item[props.labelField as any])
    idArr.push(item[props.valueField as any])
  })
  inputLabel.value = nameArr?.join?.('、')
  emits('changeValue', arr)
  emits('update:modelValue', idArr)
}

export interface SelectDialogRef {
  getData: (fn: Common.Function) => void
  inputLabel: Ref<string>
  item: any
  getTableData: () => void
}

const userStore = useUserStore()

defineExpose<SelectDialogRef>({
  getData,
  inputLabel,
  item,
  getTableData: () => ComponentSelectDialogRef.value?.getData(),
})
// api 包含在内则显示企业微信图标
const showQywxPopover = computed(() => [
  'BusinessUnitSupplierEnumlist',
  'business_unitlist',
  'GetBusinessUnitListApi',
  'GetCustomerEnumList',
].includes(props.api) && userStore.user?.button_codes?.includes('QYWechatFloatButton'))
const isMouseover = ref(false)
function handleMouseover() {
  isMouseover.value = true
}

// 刷新数据-不关闭面板
async function refreshData() {
  isQueryChanged = true
  isRefresh.value = true
  getData()
}
</script>

<template>
  <vxe-pulldown v-bind="$attrs" ref="pulldownRef" transfer class="w-full" @click="clearFlag = true">
    <template #default>
      <el-input
        ref="vxeInputRef"
        v-model="inputLabel"
        class="custom-input-class"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        @focus="focusEvent"
        @keyup="handleInputKeyup"
        @blur="blurEvent"
        @input="inputEvent"
        @mouseover="handleMouseover"
        @mouseleave="isMouseover = false"
      >
        <template #suffix>
          <div class="flex items-center">
            <i
              v-if="inputLabel && clearFlag"
              ref="clearIconRef"
              class="vxe-icon-close vxe_icon"
              @click="clearEvent($event)"
            />
            <QywxPopover v-if="showQywxPopover && isMouseover" class="mr-2" />
            <i
              v-if="showDialog"
              class="vxe-icon-table vxe_icon"
              @click="handleShowDialog"
            />
          </div>
        </template>
      </el-input>
    </template>
    <template #dropdown>
      <div v-if="!props?.multiple" ref="dropdown4Ref" class="my-dropdown4">
        <VxeGrid
          ref="girdRef"
          :show-header="false"
          border
          auto-resize
          :loading="loading"
          :scroll-y="{
            enabled: true,
            gt: 100,
          }"
          height="300px"
          :row-config="{ isHover: true, isCurrent: true }"
          :data="tableData"
          :columns="props.tableColumn"
          @cell-click="cellClickEvent"
        />
      </div>
      <div v-else class="shadow-md">
        <el-scrollbar max-height="200">
          <div
            v-for="(item, index) in tableData"
            :key="index"
            class="flex items-center min-h-[40px] justify-between cursor-pointer hover:bg-[#f5f7fa]"
            @click="handSelectMore(item)"
          >
            <div
              class="ml-[10px]"
              :style="{ color: item.isCheck ? '#47a2ff' : '#333' }"
            >
              {{ item[props.labelField] }}
            </div>
            <el-icon
              style="margin-right: 10px"
              :color="item.isCheck ? '#47a2ff' : 'transparent'"
            >
              <Check />
            </el-icon>
          </div>
        </el-scrollbar>
        <BottomBar :api="props.api" custom-class="p-2 border-[#e5e7eb] border-t" :="$attrs" @refresh="refreshData" />
      </div>
    </template>
    <template #footer>
      <BottomBar v-if="!props?.multiple" :api="props.api" custom-class="p-2 border-[#e5e7eb] border-x border-b" :="$attrs" @refresh="refreshData" />
    </template>
  </vxe-pulldown>
  <!--  避免在VxeTable中使用时遇到show-overflow属性，鼠标移上去出现tooltip -->
  <Teleport v-if="teleportElement" :to="teleportElement">
    <ComponentSelectDialog
      ref="ComponentSelectDialogRef"
      :ids="props.modelValue"
      :multiple="props.multiple"
      :api="props.api"
      :query="query"
      :query-config="props.queryConfig"
      :modal-name="props.modal_name"
      :table-config="props.tableConfig"
      :value-field="props.valueField"
      :label-field="props.labelField"
      :dialog-type="props.dialogType"
      :need-refresh="props.needRefresh"
      @handle-sure="selectRow"
    >
      <template #radioHeader>
        <slot name="radioHeader" />
      </template>
    </ComponentSelectDialog>
  </Teleport>
</template>

<style lang="scss">
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0 !important;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  cursor: pointer;
}

.vxe_icon {
  margin-right: 6px;
  font-size: 14px;
  cursor: pointer;
}
</style>

<style lang="scss" scoped>
::v-deep(.vxe-input--suffix) {
  width: auto;
}

::v-deep(.vxe-input.is--suffix .vxe-input--inner) {
  --vxe-ui-input-disabled-background-color: #f5f7fa;
  --vxe-ui-font-disabled-color:#a8abb2;
  // padding-right: 46px;
}

 .custom-input-class {
   width: auto;
 }
</style>
