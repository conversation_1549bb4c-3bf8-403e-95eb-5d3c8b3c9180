<script setup lang="ts" name="FpOtherEntryOrder">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onActivated, onMounted, reactive, ref, watch } from 'vue'
import FineSizeEnteringDetail from '../components/FineSizeEnteringDetail.vue'
import {
  getFpmOtherInOrder,
  getFpmOtherInOrderList,
  updateFpmOtherInOrderStatusPass,
  updateFpmOtherInOrderStatusWait,
} from '@/api/fpOtherEntryOrder'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { sumNum } from '@/util/tableFooterCount'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { usePageQuery } from '@/use/usePageQuery'
import { BusinessUnitIdEnum, WarehouseTypeIdEnum } from '@/common/enum'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const { formatFilterObj, formatDateRange } = usePageQuery()

const mainOptionsTablesRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  voucher_number: '',
  biz_unit_id: '',
  warehouse_id: '',
  audit_status: [],
}))

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    height: '100%',
    operateWidth: '150',
    showOperate: true,
    showSlotNums: false,
    scrollY: { enabled: true, gt: 100 },
    showSpanHeader: true,
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['total_weight'].includes(column.field))
          return sumNum(data, 'total_weight', '')

        if (['paper_tube_weight'].includes(column.field))
          return sumNum(data, 'paper_tube_weight', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['in_length'].includes(column.field))
          return sumNum(data, 'in_length', '')

        if (['quote_length'].includes(column.field))
          return sumNum(data, 'quote_length', '')

        if (['total_price'].includes(column.field))
          return sumNum(data, 'total_price', '￥')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '成品信息',
      childrenList: [
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'dye_factory_color_code',
          title: '染厂色号',
          minWidth: 100,
        },
        {
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          soltName: 'product_width',
          field: 'product_width',
          title: '成品幅宽',
          minWidth: '5%',
        },
        {
          sortable: true,
          soltName: 'product_gram_weight',
          field: 'product_gram_weight',
          title: '成品克重',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'in_roll', // '5%'
          title: '进仓匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '结算单位',
      field: 'F',
      childrenList: [
        {
          field: 'auxiliary_unit_name',
          title: '结算单位',
          width: '5%',
        },
      ],
    },
    {
      title: '数量单价',
      childrenList: [
        {
          sortable: true,
          field: 'total_weight', // 数量
          title: '进仓数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'weight_error', // 数量
          title: '空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'paper_tube_weight', // 数量
          title: '纸筒',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_weight', // 数量
          title: '结算数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_price', // price
          soltName: 'unit_price', // price
          title: '单价',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '辅助数量单价',
      childrenList: [
        {
          sortable: true,
          field: 'in_length', // '5%'
          title: '进仓辅助数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'length_unit_price', // price
          soltName: 'length_unit_price', // price
          title: '单价',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '金额信息',
      childrenList: [
        {
          sortable: true,
          field: 'other_price', // '5%'
          soltName: 'other_price', // '5%'
          title: '其他金额',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'total_price', // '5%'
          soltName: 'total_price', // '5%'
          title: '进仓金额',
          minWidth: '5%',
        },
      ],
    },
    {
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
})
const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeStock,
  dataType: PrintDataType.Product,
})

const warehousing_date = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmOtherInOrderStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmOtherInOrderStatusWait()
// 驳回
// const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = updateFpmOtherInOrderStatusReject()

const {
  fetchData: fetchDataList,
  data: mainDataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = getFpmOtherInOrderList()
// 获取列表数据
async function getData() {
  const query = {
    ...filterData,
  }

  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (warehousing_date?.value?.length) {
    query.in_time_begin = formatDate(warehousing_date.value[0])
    query.in_time_end = formatDate(warehousing_date.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainDataList.value?.list)
    showFinishProductionDetail(mainDataList.value.list[0])
}

const printList = ref<any>([])

function handlePrintXiMa(row?: any) {
  printList.value = []
  if (!row) {
    finishProductionOptions.datalist.forEach((item: any) => {
      item.item_fc_data.forEach((it: any) => {
        printList.value.push({
          finish_product_craft: it?.finish_product_craft,
          density: it?.density,
          product_kind_name: it?.product_kind_name,
          bleach_name: it?.bleach_name,
          finish_product_width_and_unit_name: it.finish_product_width_and_unit_name, // 幅宽
          finish_product_gram_weight_and_unit_name: it.finish_product_gram_weight_and_unit_name, // 克重
          weaving_organization_name: it?.weaving_organization_name,
          product_name: item?.product_name,
          product_code: item?.product_code,
          yarn_count: it?.yarn_count,
          finish_product_ingredient: item?.product_ingredient,
          dyelot_number: it?.dye_factory_dyelot_number,
          weight: it.base_unit_weight || 0,
          measurement_unit_name: it?.measurement_unit_name,
          product_color_code: item?.product_color_code,
          product_color_name: item?.product_color_name,
          qr_code: it.qr_code, // 二维码
          bar_code: it.bar_code, // 条形码
          volume_number: it.volume_number, // 匹号
          print_date: it.print_date, // 打印时间
        })
      })
    })
  }
  else {
    row.item_fc_data.forEach((it: any) => {
      printList.value.push({
        finish_product_craft: it?.finish_product_craft,
        density: it?.density,
        product_kind_name: it?.product_kind_name,
        bleach_name: it?.bleach_name,
        finish_product_width_and_unit_name: it.finish_product_width_and_unit_name, // 幅宽
        finish_product_gram_weight_and_unit_name: it.finish_product_gram_weight_and_unit_name, // 克重
        weaving_organization_name: it?.weaving_organization_name,
        product_name: row?.product_name,
        product_code: row?.product_code,
        yarn_count: it?.yarn_count,
        finish_product_ingredient: row?.product_ingredient,
        weight: it.base_unit_weight,
        dyelot_number: it?.dye_factory_dyelot_number,
        product_color_code: row?.product_color_code,
        measurement_unit_name: it?.measurement_unit_name,
        product_color_name: row?.product_color_name,
        qr_code: it?.qr_code, // 二维码
        bar_code: it?.bar_code, // 条形码
        volume_number: it?.volume_number, // 匹号
        print_date: it?.print_date, // 打印时间
      })
    })
  }
}

const tableConfig = computed(() => ({
  showSlotNums: true,
  loading: loadingList.value,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList.value,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  height: '100%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
}))

const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '150',
    height: '400',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'voucher_number',
      title: '凭证号',
      width: 100,
    },
    {
      sortable: true,
      field: 'biz_unit_name',
      title: '发货单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_mode_name',
      title: '订单类型',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_in_time',
      title: '进仓日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'store_keeper_name',
      title: '仓管员',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计', // 100
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计', // 数量
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位', // no
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计', // 100
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_price',
      soltName: 'total_price',
      title: '单据金额', // 100
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  //   导出
  handleExport: async () => {
    // if (mainOptions.multipleSelection.length < 1) return ElMessage.warning('请勾选要导出的数据')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSheetOfProductionPlanList()
    // mainOptions.exportOptions.loadingExcel = true
    // exportExcel()
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //
    // ElMessage({
    //   type: 'success',
    //   message: '成功',
    // })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

watch(
  () => mainDataList.value,
  () => {
    mainOptions.mainList
          = mainDataList.value?.list?.map((item: any) => {
        const item_data
            = item.item_data?.map((v: any) => {
              return {
                ...v,
                in_roll: formatTwoDecimalsDiv(item.in_roll), // 进仓匹数
                quote_roll: formatTwoDecimalsDiv(item.quote_roll), // 采购数量
                quote_weight: formatWeightDiv(item.quote_weight), // 采购基本单位数量
                total_weight: formatWeightDiv(item.total_weight), // 进仓数量
                weight_error: formatWeightDiv(item.weight_error), // 空差
                paper_tube_weight: formatWeightDiv(item.paper_tube_weight), // 纸筒总重
                settle_weight: formatWeightDiv(item.settle_weight), // 结算数量
                unit_price: formatUnitPriceDiv(item.unit_price), // 单价
                in_length: formatLengthDiv(item.in_length), // 进仓辅助数量
                quote_length: formatLengthDiv(item.quote_length), // 采购辅助数量
                length_unit_price: formatUnitPriceDiv(item.length_unit_price), // 辅助数量单价
                other_price: formatTwoDecimalsDiv(item.other_price), // 其他金额
                total_price: formatTwoDecimalsDiv(item.total_price), // 进仓金额
              }
            }) || []
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(item.total_roll), // 匹数总计
          total_weight: formatWeightDiv(item.total_weight), // 数量总计
          total_length: formatLengthDiv(item.total_length), // 辅助数量总计
          total_price: formatTwoDecimalsDiv(item.total_price), // 单据金额
          item_data,
        }
      }) || []
  },
  { deep: true },
)
// 表格选中事件
function handAllSelect({ records }: any) {
  mainOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  mainOptions.multipleSelection = records
}
function changeDate() {
  // warehousing_date.value = [row.date_min, row.date_max]
  getData()
}

// 导出勾选的数据
// const exportExcel = () => {
//   mainOptionsTablesRef.value.tableRef.exportData({
//     filename: `成品采购进仓单列表${formatTime(new Date())}`,
//     // isFooter: true,
//     data: mainOptions.mainList,
//     columns: mainOptions.columnList.map((item: any) => {
//       return {
//         ...item,
//         field: item.field,
//       }
//     }),
//   })
// }

onMounted(() => {
  getData()
})
onActivated(getData)

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
// 表格操作列功能
// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
// 驳回
// const handReject = async (row: any) => {
//   const res = await deleteToast('确认驳回嘛？')
//   if (res) {
//     await rejectFetch({ audit_status: 1, id: row.id.toString() })
//     if (rejectSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(rejectMsg.value)
//     }
//   }
// }

function handDetail(row: any) {
  router.push({
    name: 'FpOtherEntryOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpOtherEntryOrderEdit',
    query: {
      id: row.id,
    },
  })
}
function handAdd() {
  router.push({
    name: 'FpOtherEntryOrderAdd',
  })
}

// 获取成品信息
function showFinishProductionDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

const { fetchData: DetailFetch, data: finishProData } = getFpmOtherInOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        in_roll: formatTwoDecimalsDiv(Number(item.in_roll)), // 100
        quote_roll: formatTwoDecimalsDiv(Number(item.quote_roll)), // 100
        quote_total_weight: formatWeightDiv(Number(item.quote_total_weight)), // weight
        total_weight: formatWeightDiv(Number(item.total_weight)), // weight
        weight_error: formatWeightDiv(Number(item.weight_error)), // weight
        settle_weight: formatWeightDiv(Number(item.settle_weight)), // weight
        paper_tube_weight: formatWeightDiv(Number(item.paper_tube_weight)), // weight
        unit_price: formatUnitPriceDiv(Number(item.unit_price)), // price
        quote_length: formatLengthDiv(Number(item.quote_length)), // 100
        in_length: formatLengthDiv(Number(item.in_length)), // 100
        length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)), // price
        other_price: formatTwoDecimalsDiv(Number(item.other_price)), // 100
        total_price: formatTwoDecimalsDiv(Number(item.total_price)), // 100
      }
    },
  )
}
const FineSizeEnteringDetailRef = ref()
function showDialog(row: any) {
  FineSizeEnteringDetailRef.value.showDialog(row)
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input
              v-model="filterData.order_no"
              style="width: 100%"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:">
          <template #content>
            <vxe-input
              v-model="filterData.voucher_number"
              style="width: 100%"
              placeholder="凭证号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="发货单位:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.biz_unit_id"
              :query="{
                unit_type_id: `${BusinessUnitIdEnum.customer},${BusinessUnitIdEnum.finishedProduct}`,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 100%"
              api="GetPhysicalWarehouseDropdownList"
              :query="{ warehouse_type_id: WarehouseTypeIdEnum.finishProduction }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进仓日期:" width="310">
          <template #content>
            <SelectDate
              v-model="warehousing_date"
              style="width: 100%"
              @change-date="changeDate"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              multiple
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard :tool-bar="true" class="table-card-full">
      <template #right-top>
        <!-- <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel> -->
        <el-button
          v-has="'FpOtherEntryOrder_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        ref="mainOptionsTablesRef"
        :config="tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showFinishProductionDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpOtherEntryOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpOtherEntryOrder_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpOtherEntryOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpOtherEntryOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      :tool-bar="false"
      title=""
      class="table-card-bottom"
    >
      <template #right-top>
        <PrintPopoverBtn
          print-btn-text="打印标签细码"
          :options="options"
          :list="printList"
          @on-print="handlePrintXiMa(row)"
        />
      <!--      <el-popover placement="left" title="选择打印" :width="180" trigger="hover"> -->
      <!--        <template #reference> -->
      <!--          <el-button type="primary" plain :icon="Printer">打印标签细码</el-button> -->
      <!--        </template> -->
      <!--        <PrintBtn type="productionXiMa" btnText="打印标签1" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签一']" @onPrint="handlePrintXiMa(row)" :list="printList" /> -->
      <!--        <PrintBtn @onPrint="handlePrintXiMa(row)" class="!ml-0" btnText="打印标签2" :list="printList" type="productionXimaLabelCode" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签二']" /> -->
      <!--        <PrintBtn btnText="打印标签3" class="!ml-0" @onPrint="handlePrintXiMa(row)" :list="printList" type="productionXiMa" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签三']" /> -->
      <!--      </el-popover> -->
      <!--   成品布细码标签   -->
      <!--      <PrintBtn type="productionXiMa" btnType="primary" plain btnText="细码标签打印" :tid="1679373308416256" @onPrint="handlePrintXiMa" :list="printList" /> -->
      </template>
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #unit_price="{ row }">
          ￥{{ row.unit_price }}
        </template>
        <template #length_unit_price="{ row }">
          ￥{{ row.length_unit_price }}
        </template>
        <template #other_price="{ row }">
          ￥{{ row.other_price }}
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #xima="{ row }">
          <el-link @click="showDialog(row)">
            查看
          </el-link>
        </template>
        <template #product_width="{ row }">
          {{ row.product_width }}
          {{ row.finish_product_width_unit_name }}
        </template>
        <template #product_gram_weight="{ row }">
          {{ row.product_gram_weight }}
          {{ row.finish_product_gram_weight_unit_name }}
        </template>
        <template #operate="{ row }">
          <PrintPopoverBtn
            :options="options"
            :list="printList"
            @on-print="handlePrintXiMa(row)"
          />
        <!--        <el-popover placement="left" title="选择打印" :width="180" trigger="hover"> -->
        <!--          <template #reference> -->
        <!--            <el-button type="text">打印</el-button> -->
        <!--          </template> -->
        <!--          &lt;!&ndash;   成品布细码标签   &ndash;&gt; -->
        <!--          <PrintBtn type="productionXiMa" size="small" btnText="打印标签1" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签一']" @onPrint="handlePrintXiMa(row)" :list="printList" /> -->
        <!--          &lt;!&ndash;  成品布细码标签（二维码条形码）    &ndash;&gt; -->
        <!--          <PrintBtn @onPrint="handlePrintXiMa(row)" size="small" class="!ml-0" btnText="打印标签2" :list="printList" type="productionXimaLabelCode" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签二']" /> -->
        <!--          <PrintBtn btnText="打印标签3" class="!ml-0" size="small" @onPrint="handlePrintXiMa(row)" :list="printList" type="productionXiMa" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签三']" /> -->
        <!--        </el-popover> -->
        <!--   成品布细码标签   -->
        <!--        <PrintBtn type="productionXiMa" size="small" plain btnText="打印细码" :tid="1679373308416256" @onPrint="handlePrintXiMa(row)" :list="printList" /> -->
        </template>
      </Table>
    </FildCard>
  </div>
  <FineSizeEnteringDetail ref="FineSizeEnteringDetailRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
