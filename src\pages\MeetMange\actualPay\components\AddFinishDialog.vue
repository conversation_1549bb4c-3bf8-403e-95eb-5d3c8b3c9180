<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
import { list_enum } from '@/api/actualPay'
import { debounce, getFilterData, resetData } from '@/common/util'
import SelectDate from '@/components/SelectDate/index.vue'
import { formatDate } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const emits = defineEmits(['handleSure'])

const state: any = reactive({
  filterData: {
    voucher_num: '',
    order_no: '',
    date: [],
  },
  showModal: false,
  rowIndex: -1,
  multipleSelection: [],
  supplier_id: '',
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = list_enum()

const getData = debounce(async () => {
  const query = {
    pay_start_date: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[0]) : '',
    pay_end_date: state.filterData.date && state.filterData.date !== '' && state.filterData.date.length ? formatDate(state.filterData.date[1]) : '',
    supplier_id: state.supplier_id,
    ...state.filterData,
  }
  await fetchData(getFilterData(query))
}, 400)
watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    field: 'src_order_no',
    title: '来源单号',
    minWidth: 120,
  },
  {
    field: 'order_no',
    title: '应付单号',
    minWidth: 100,
  },
  {
    field: 'voucher_num',
    title: '凭证单号',
    minWidth: 100,
  },
  {
    field: 'order_type_name',
    title: '单据类型',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供方名称',
    minWidth: 100,
  },
  {
    field: 'pay_date',
    title: '应付日期',
    minWidth: 100,
  },
  {
    field: 'total_price',
    title: '应付',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'paid_price',
    title: '已付',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'unpaid_price',
    title: '未付',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'handler_name',
    title: '经手人',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    minWidth: 150,
    isDate: true,
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('至少选择一条数据')

  emits('handleSure', state.multipleSelection)
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="添加成品" width="1200" height="800" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="预付款单号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.filterData.voucher_num" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="采购日期:">
          <template #content>
            <SelectDate v-model="state.filterData.date" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
