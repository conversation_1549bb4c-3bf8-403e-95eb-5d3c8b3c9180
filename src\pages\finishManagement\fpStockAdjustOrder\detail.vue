<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import FineSizeAdjustDetail from '../components/FineSizeAdjustDetail.vue'
import {
  getProductAdjustOrder,
  updateProductAdjustOrderAuditStatusCancel,
  updateProductAdjustOrderAuditStatusPass,
  updateProductAdjustOrderAuditStatusReject,
  updateProductAdjustOrderAuditStatusWait,
} from '@/api/fpStockAdjustOrder'
import { formatDate, formatLengthDiv, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { orderStatusConfirmBox } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'

const form_options = [
  {
    text: '营销体系名称',
    key: 'sale_system_name',
  },
  {
    text: '仓库名称',
    key: 'warehouse_name',
  },
  {
    text: '调整日期',
    key: 'adjust_time',
  },
  {
    text: '仓管员',
    key: 'warehouse_manager_name',
  },
  {
    text: '备注',
    key: 'remark',
    copies: '2',
  },
]
const route = useRoute()

const state = reactive<any>({
  baseData: {
    order_no: '',
    audit_status_name: '',
  },
})
const { fetchData, data: detailData } = getProductAdjustOrder()
const currentFinishedProductAuditStatus = ref<any>(null) // 当前选中的成品数据
onMounted(() => {
  getData()
})

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    showSlotNums: false,
    showSpanHeader: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['roll', 'weight', 'length', 'adjust_roll', 'adjust_weight', 'adjust_length'].includes(column.property))
          return `${sumNum(data, column.property)}`

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'base_info',
      childrenList: [
        {
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      title: '调整前',
      field: 'adjust_before',
      childrenList: [
        {
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'roll',
          title: '库存匹数',
          minWidth: 100,
        },
        {
          field: 'weight',
          title: '库存数量',
          minWidth: 100,
        },
        {
          field: 'measurement_unit_name',
          title: '单位',
          minWidth: 100,
        },
        {
          field: 'length',
          title: '库存辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '调整后',
      field: 'adjust_after',
      fixed: 'right',
      childrenList: [
        {
          field: 'adjust_customer_name',
          soltName: 'adjust_customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          field: 'adjust_product_code',
          soltName: 'adjust_product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          field: 'adjust_product_name',
          soltName: 'adjust_product_name',
          title: '成品名称',
          minWidth: 100,
        },
        {
          field: 'adjust_product_color_code',
          soltName: 'adjust_product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          field: 'adjust_product_color_name',
          soltName: 'adjust_product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          field: 'adjust_product_level_name',
          soltName: 'adjust_product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          field: 'adjust_product_remark',
          soltName: 'adjust_product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          field: 'adjust_dyelot_number',
          soltName: 'adjust_dyelot_number',
          title: '染厂缸号',
          minWidth: 100,
        },
        {
          field: 'adjust_roll',
          soltName: 'adjust_roll',
          title: '调整匹数',
          minWidth: 100,
        },
        {
          field: 'adjust_weight',
          soltName: 'adjust_weight',
          title: '调整数量',
          minWidth: 100,
        },
        {
          field: 'adjust_length',
          soltName: 'adjust_length',
          title: '调整辅助数量',
          minWidth: 100,
        },
      ],
    },
    {
      title: '',
      fixed: 'right',
      field: 'operation',
      childrenList: [
        {
          field: '',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
})

async function getData() {
  // finishProductionOptions.datalist = [{ item_data: [] }]
  await fetchData({ id: route.query.id })
  state.baseData = {
    ...detailData.value,
    adjust_time: formatDate(detailData.value.adjust_time),
    warehouse_manager_name: detailData.value.warehouse_manager_name || '',
  }
  //

  currentFinishedProductAuditStatus.value = detailData.value // 设置当前选中的成品数据
  finishProductionOptions.datalist = detailData.value?.item_data?.map((item: any) => {
    return {
      ...item,
      finishProductionColorId: item.product_color_id,
      in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
      quote_roll: formatTwoDecimalsDiv(Number(item.quote_roll)),
      quote_total_weight: formatWeightDiv(Number(item.quote_total_weight)), // weight
      total_weight: formatWeightDiv(Number(item.total_weight)), // weight
      weight_error: formatWeightDiv(Number(item.weight_error)), // weight
      settle_weight: formatWeightDiv(Number(item.settle_weight)), // weight
      paper_tube_weight: formatWeightDiv(Number(item.paper_tube_weight)), // weight
      unit_price: formatUnitPriceDiv(Number(item.unit_price)), // price
      quote_length: formatLengthDiv(Number(item.quote_length)),
      in_length: formatLengthDiv(Number(item.in_length)),
      length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)), // price
      other_price: formatTwoDecimalsDiv(Number(item.other_price)),
      total_price: formatTwoDecimalsDiv(Number(item.total_price)),
      adjust_roll: formatTwoDecimalsDiv(item.adjust_roll), // 调整匹数
      adjust_weight: formatWeightDiv(item.adjust_weight), // 调整数量
      adjust_length: formatLengthDiv(item.adjust_length), // 调整辅助数量
      roll: formatTwoDecimalsDiv(item.roll), // 库存匹数
      length: formatLengthDiv(item.length), // 库存匹数
      weight: formatWeightDiv(item.weight), // 调整数量
    }
  })
}

// 审核
async function updateStatus(audit_status: number) {
  const id: any = Number(route.query.id)
  const options: Record<number, any> = {
    1: {
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateProductAdjustOrderAuditStatusWait,
    },
    2: {
      message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' },
      api: updateProductAdjustOrderAuditStatusPass,
    },
    3: {
      message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' },
      api: updateProductAdjustOrderAuditStatusReject,
    },
    4: {
      message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' },
      api: updateProductAdjustOrderAuditStatusCancel,
    },
  }
  const { message, api } = options[audit_status]
  await orderStatusConfirmBox({ id, audit_status, message, api })
  getData()
}
function adjustKeys(row: any, pre_key: string, val_key: string) {
  return row[pre_key] === row[val_key]
}

const FineSizeAdjustDetailRef = ref()
function showDialog(row: any) {
  FineSizeAdjustDetailRef.value.showDialog(row)
}

const allPrintData = computed(() => {
  if (currentFinishedProductAuditStatus.value) {
    //
    const printList: any[] = []
    currentFinishedProductAuditStatus.value.item_data.forEach((item: any) => {
      item.item_data.forEach((it: any) => {
        printList.push({
          fatherKey: item.id,
          product_name: item?.product_name, // 名称
          finish_product_craft: it?.finish_product_craft,
          density: it?.density,
          product_kind_name: it?.product_kind_name,
          bleach_name: it?.bleach_name,
          finish_product_width: it?.finish_product_width_and_unit_name,
          finish_product_gram_weight: it?.finish_product_gram_weight_and_unit_name,
          weaving_organization_name: it?.weaving_organization_name,
          product_code: item?.product_code,
          yarn_count: it?.yarn_count,
          finish_product_ingredient: item?.product_ingredient,
          measurement_unit_name: it?.measurement_unit_name,
          dyelot_number: item?.adjust_dyelot_number,
          weight: it.adjust_weight || 0,
          product_color_code: item?.adjust_product_color_code,
          product_color_name: item?.adjust_product_color_name,
          qr_code: it?.qr_code, // 二维码
          bar_code: it?.bar_code, // 条形码
          volume_number: it?.adjust_volume_number, // 匹号
          print_date: it?.print_date, // 打印时间
        })
      })
    })
    return printList
  }

  else {
    return []
  }
})
// 需要打印到的数据
function filterPrintList(id: number | undefined) {
  const printList: any[] = allPrintData.value.filter((item: any) => {
    if (item.fatherKey === id)
      return item
  })
  return printList
}
</script>

<template>
  <div class="flex flex-col h-full">
    <StatusColumn
      :order_no="state.baseData.order_no"
      :order_id="state.baseData.id"
      :status="state.baseData.audit_status"
      :status_name="state.baseData.audit_status_name"
      permission_print_key=""
      permission_wait_key="FpStockAdjustOrder_wait"
      permission_reject_key="FpStockAdjustOrder_reject"
      permission_pass_key="FpStockAdjustOrder_pass"
      permission_cancel_key="FpStockAdjustOrder_cancel"
      permission_edit_key="FpStockAdjustOrder_edit"
      edit_router_name="FpStockAdjustOrderEdit"
      @eliminate="updateStatus"
      @reject="updateStatus"
      @cancel="updateStatus"
      @audit="updateStatus"
    />
    <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
      <div class="descriptions_row">
        <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :copies="item.copies || 1" :label="`${item.text}:`">
          <template #content>
            {{ state.baseData[item.key] }}
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard :tool-bar="false" title="成品信息" class="mt-[5px] flex-1 flex flex-col overflow-y-hidden">
      <template #right-top>
        <PrintPopoverBtn
          v-if="finishProductionOptions.datalist.length && currentFinishedProductAuditStatus?.audit_status === 2"
          :print-type="PrintType.PrintTemplateTypeStock"
          :data-type="PrintDataType.Product"
          :list="allPrintData"
        />
      </template>
      <div class="flex-1 overflow-y-hidden">
        <Table :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
          <template #unit_price="{ row }">
            ￥{{ row.unit_price }}
          </template>
          <template #length_unit_price="{ row }">
            ￥{{ row.length_unit_price }}
          </template>
          <template #other_price="{ row }">
            ￥{{ row.other_price }}
          </template>
          <template #total_price="{ row }">
            ￥{{ row.total_price }}
          </template>
          <template #adjust_customer_name="{ row }">
            <span v-if="!adjustKeys(row, 'customer_name', 'adjust_customer_name')" class="adjust_key">{{ row.adjust_customer_name }}</span>
            <span v-else>{{ row.adjust_customer_name }}</span>
          </template>
          <!-- S 成品信息 -->
          <template #adjust_product_code="{ row }">
            <span :class="{ adjust_key: !adjustKeys(row, 'product_id', 'adjust_product_id') }">{{ row.adjust_product_code }}</span>
          </template>
          <template #adjust_product_name="{ row }">
            <span :class="{ adjust_key: !adjustKeys(row, 'product_id', 'adjust_product_id') }">{{ row.adjust_product_name }}</span>
          </template>
          <!-- E 成品信息 -->
          <template #adjust_product_color_code="{ row }">
            <span :class="{ adjust_key: !adjustKeys(row, 'product_color_id', 'adjust_product_color_id') }">{{ row.adjust_product_color_code }}</span>
          </template>
          <template #adjust_product_color_name="{ row }">
            <span :class="{ adjust_key: !adjustKeys(row, 'product_color_id', 'adjust_product_color_id') }">{{ row.adjust_product_color_name }}</span>
          </template>
          <template #adjust_product_level_name="{ row }">
            <span v-if="!adjustKeys(row, 'product_level_name', 'adjust_product_level_name')" class="adjust_key">{{ row.adjust_product_level_name }}</span>
            <span v-else>{{ row.adjust_product_level_name }}</span>
          </template>
          <template #adjust_product_remark="{ row }">
            <span v-if="!adjustKeys(row, 'product_remark', 'adjust_product_remark')" class="adjust_key">{{ row.adjust_product_remark }}</span>
            <span v-else>{{ row.adjust_product_remark }}</span>
          </template>
          <template #adjust_dyelot_number="{ row }">
            <span :class="{ adjust_key: !adjustKeys(row, 'dyelot_number', 'adjust_dyelot_number') }">{{ row.adjust_dyelot_number }}</span>
          </template>
          <template #adjust_roll="{ row }">
            <span v-if="!adjustKeys(row, 'roll', 'adjust_roll')" class="adjust_key">{{ row.adjust_roll }}</span>
            <span v-else>{{ row.adjust_roll }}</span>
          </template>
          <template #adjust_weight="{ row }">
            <span v-if="!adjustKeys(row, 'weight', 'adjust_weight')" class="adjust_key">{{ row.adjust_weight }}</span>
            <span v-else>{{ row.adjust_weight }}</span>
          </template>
          <template #adjust_length="{ row }">
            <span v-if="!adjustKeys(row, 'length', 'adjust_length')" class="adjust_key">{{ row.adjust_length }}</span>
            <span v-else>{{ row.adjust_length }}</span>
          </template>
          <template #xima="{ row }">
            <el-link @click="showDialog(row)">
              查看
            </el-link>
            <PrintPopoverBtn
              v-if="finishProductionOptions.datalist.length && currentFinishedProductAuditStatus?.audit_status === 2"
              :print-btn-text-style="{ fontSize: '12px', color: '#0e7eff' }"
              print-btn-type="text"
              :print-type="PrintType.PrintTemplateTypeStock"
              :data-type="PrintDataType.Product"
              :list="filterPrintList(row.id)"
            />
          </template>
        </Table>
      </div>
    </FildCard>
  </div>
  <FineSizeAdjustDetail ref="FineSizeAdjustDetailRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.el-link {
  color: #0e7eff;
}

.adjust_key {
  color: red;
}
</style>
