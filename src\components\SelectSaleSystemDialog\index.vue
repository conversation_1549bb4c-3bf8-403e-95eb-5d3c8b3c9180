<script lang="ts" setup>
import { onActivated, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { onKeyStroke } from '@vueuse/core'
import { debounce } from 'xe-utils'
import { handleCustomerMarketingList } from '../SelectCustomerDialog/common'
import { GetSaleSystemDropdownListApi } from '@/api/marketingSystem'
import { Business_unitcustomerDetail } from '@/api/customerMange'

defineOptions({
  name: 'SelectSaleSystemDialog',
})

const props = withDefaults(defineProps<Props>(), {
  list: () => [],
  customerId: 0,
})
const emits = defineEmits(['handleSure'])
interface Props {
  list?: any[]// 客户所属的营销体系列表
  customerId?: number// 客户id
}
const showModal = defineModel({
  default: false,
})
const router = useRouter()
const saleSystemList = ref<any[]>([])
const saleSystemIndex = ref<string | number>(0) // 选择的营销体系索引

watch(() => showModal.value, () => {
  if (showModal.value) {
    saleSystemIndex.value = 0 // 默认选中第一个
    saleSystemList.value = props.list || []
    // 如果客户营销体系列表为空，则获取账套营销体系列表
    if (!saleSystemList.value.length)
      getSaleSystemList()
  }
  else { saleSystemIndex.value = '' }
})
// 获取客户信息
const { fetchData: ApiGetCustomer } = Business_unitcustomerDetail()
const getCustomerInfo = debounce(async () => {
  if (!props.customerId)
    return
  const res = await ApiGetCustomer({ id: props.customerId })
  const customerInfo = handleCustomerMarketingList(res.data)
  saleSystemList.value = customerInfo.sale_system_list || []
  if (!saleSystemList.value.length) {
    // 如果客户没有营销体系，则获取营销体系列表
    getSaleSystemList()
  }
}, 500)
// 获取营销体系
const { fetchData: ApiGetSaleSystemList, data, loading, success, msg } = GetSaleSystemDropdownListApi()
async function getSaleSystemList() {
  await ApiGetSaleSystemList()
  saleSystemList.value = data.value?.list || []
  if (!success.value)
    ElMessage.error(msg.value)
}

onActivated(() => {
  // 切换页面重新获取客户信息
  getCustomerInfo()
})
function handCancel() {
  showModal.value = false
}

// 确认选择营销体系
async function handleSure() {
  if (!saleSystemIndex.value && Number(saleSystemIndex.value) !== 0)
    return ElMessage.error('请选择营销体系')
  emits('handleSure', saleSystemList.value[Number(saleSystemIndex.value)])
  handCancel()
}

// 跳转到新增营销体系
function handleSaleSystem() {
  router.push({ name: 'MarketingSystemAdd' })
}
// 跳转到客户编辑页
function handleCustomerEdit() {
  router.push({
    name: 'CustomerEdit',
    query: {
      id: props.customerId,
    },
  })
}

// 回车键确认
onKeyStroke('Enter', () => {
  handleSure()
})
// 上下箭头切换选择
onKeyStroke('ArrowUp', () => {
  if (saleSystemIndex.value === 0) {
    const allLength = saleSystemList.value?.length || 0
    if (allLength === 0)
      return
    return saleSystemIndex.value = allLength - 1
  }
  saleSystemIndex.value = Number(saleSystemIndex.value) - 1
})
onKeyStroke('ArrowDown', () => {
  if (saleSystemIndex.value === saleSystemList.value?.length - 1)
    return saleSystemIndex.value = 0
  saleSystemIndex.value = Number(saleSystemIndex.value) + 1
})
</script>

<template>
  <vxe-modal
    v-model="showModal" lock-view show-footer title="选择营销体系" width="500" height="60vh" :mask="false"
    :esc-closable="true" resize
  >
    <el-form v-if="saleSystemList?.length" v-loading="loading" class="p-8" label-width="auto">
      <el-radio-group v-model="saleSystemIndex" class="w-full radio-group">
        <el-radio
          v-for="(e, i) in (saleSystemList || [])" :key="e.id" :value="i" size="large" border
          class="w-full mb-3 mr-0"
        >
          {{ e.name }}
        </el-radio>
      </el-radio-group>
    </el-form>
    <!-- 无营销体系 -->
    <div v-else class="w-full h-full flex">
      <div class="border-[#ccc] border flex items-center justify-center flex-1 m-5">
        <div class="flex items-center text-[#999] text-[12px]">
          无营销体系，
          <el-link type="primary" underline @click="handleSaleSystem">
            前往维护>
          </el-link>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex items-center justify-between">
        <div>
          <div v-if="saleSystemList?.length" class="flex items-center text-[#999] text-[12px]">
            添加更多营销体系，
            <el-link type="primary" underline @click="handleCustomerEdit">
              前往>
            </el-link>
          </div>
        </div>
        <div>
          <el-button @click="handCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleSure">
            确认
          </el-button>
        </div>
      </div>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
::v-deep .radio-group {

  .el-radio__input {
    display: none;
  }

  .el-radio__label {
    padding-left: 0;
  }
}
</style>
