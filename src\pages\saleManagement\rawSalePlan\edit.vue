<script lang="ts" setup name="RawSalePlanEdit">
import { Decimal } from 'decimal.js'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import SelectRawInfomation from '../components/SelectRawInfomation.vue'
import { getSaleProductPlanOrder, updateSaleProductPlanOrder } from '@/api/productSalePlan'
import { EmployeeType } from '@/common/enum'
import { formatDate, formatPriceDiv, formatPriceMul, formatUnitPriceDiv, formatUnitPriceMul, formatWeightDiv, formatWeightMul, sumNum } from '@/common/format'
import { deepClone } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import useRouterList from '@/use/useRouterList'
import { formValidatePass } from '@/common/rule'
import useTableEnterAutoFocus from '@/use/useTableEnterAutoFocus'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import SelectSettleTypeDialog from '@/components/SelectSettleTypeDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import AddressCard from '@/components/AddressCard/index.vue'

const routerList = useRouterList()
const addressCardRef = ref()

const state = reactive<any>({
  form: {
    sale_system_id: '',
    customer_id: '',
    customer_phone: '',
    sale_user_id: '',
    voucher_number: '',
    date: '',
    delivery_date: '',
    deposit: '',
    sale_tax_rate: '',
    is_with_tax_rate: true,
    receipt_address: '',
    customer_name: '',
    settle_method_id: '', // 结算方式
    // delivery_type_id: '', // 发货方式
  },
  formRules: {
    sale_system_id: [{ required: true, validator: formValidatePass.sale_system_id(), message: '请选择营销体系', trigger: 'blur' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    date: [{ required: true, message: '请选择订单日期', trigger: 'blur' }],
    sale_user_id: [{ required: true, message: '请选择销售人员', trigger: 'blur' }],
    delivery_date: [{ required: true, message: '请选择交货日期', trigger: 'blur' }],
  },
  fileList: [],
  multipleSelection: [],
  use_yarnList: [],
})

const TableList = ref<any[]>([])

const { fetchData: getFetch, data: fabricList } = getSaleProductPlanOrder()

const route = useRoute()

onMounted(() => {
  getInfomation()
})

async function getInfomation() {
  await getFetch({ id: route.query.id })
}

watch(
  () => fabricList.value,
  () => {
    state.form.sale_system_id = fabricList.value.sale_system_id
    state.form.voucher_number = fabricList.value.voucher_number
    state.form.date = fabricList.value.order_time
    state.form.delivery_date = fabricList.value.receipt_time
    state.form.customer_id = fabricList.value.customer_id
    state.form.customer_phone = fabricList.value.customer_phone
    state.form.customer_name = fabricList.value.customer_name
    state.form.customer_code = fabricList.value.customer_code
    state.form.sale_user_id = fabricList.value.sale_user_id
    state.form.receipt_address = fabricList.value.receipt_address
    state.form.internal_remark = fabricList.value.internal_remark
    state.form.sale_tax_rate = formatPriceDiv(fabricList.value.sale_tax_rate)
    state.form.is_with_tax_rate = fabricList.value.is_with_tax_rate
    state.form.deposit = fabricList.value.deposit
    state.form.settle_method_id = fabricList.value.settle_method_id || ''
    // state.form.delivery_type_id = fabricList.value.delivery_type_id || ''

    state.fileList = fabricList.value.texture_url === '' ? [] : fabricList.value.texture_url.split(',')

    TableList.value = fabricList.value?.item_data

    for (let i = 0; i < TableList.value?.length; i++) {
      const idNum = Math.floor(Math.random() * 10000)
      TableList.value[i].record_id = idNum

      TableList.value[i].weight = Number(formatWeightDiv(TableList.value[i].weight))
      TableList.value[i].other_price = formatPriceDiv(TableList.value[i].other_price)
      TableList.value[i].upper_limit = formatPriceDiv(TableList.value[i].upper_limit)
      TableList.value[i].lower_limit = formatPriceDiv(TableList.value[i].lower_limit)
      TableList.value[i].unit_price = formatUnitPriceDiv(TableList.value[i].unit_price)
      TableList.value[i].total_price = formatPriceDiv(TableList.value[i].total_price)
      TableList.value[i].piece_count = formatPriceDiv(TableList.value[i].piece_count)

      TableList.value[i].code = TableList.value[i].raw_material_code
      TableList.value[i].name = TableList.value[i].raw_material_name

      TableList.value[i].unit_id = TableList.value[i].measurement_unit_id
      TableList.value[i].unit_name = TableList.value[i].measurement_unit_name

      for (let j = 0; j < TableList.value[i].material_ratio.length; j++) {
        TableList.value[i].material_ratio[j].yarn_ratio = formatPriceDiv(TableList.value[i].material_ratio[j].yarn_ratio)
        TableList.value[i].material_ratio[j].yarn_loss = formatPriceDiv(TableList.value[i].material_ratio[j].yarn_loss)
        TableList.value[i].material_ratio[j].use_yarn_quantity = formatWeightDiv(TableList.value[i].material_ratio[j].use_yarn_quantity)

        TableList.value[i].material_ratio[j].code = TableList.value[i].material_ratio[j].raw_material_code
        TableList.value[i].material_ratio[j].name = TableList.value[i].material_ratio[j].raw_material_name
        TableList.value[i].material_ratio[j].unit_name = TableList.value[i].material_ratio[j].measurement_unit_name
      }
    }
  },
)
// 计算属性处理地址数据
const addressDataProps = computed(() => {
  if (!fabricList.value && !fabricList.value.id)
    return null
  return {
    id: 0,
    location: fabricList.value.location ? fabricList.value.location.split(',') : [], // 省市区地址
    address: fabricList.value.receipt_address, // 详细地址
    biz_uint_id: fabricList.value.customer_id, // 客户id
    name: fabricList.value.process_factory, // 加工厂名称
    is_default: fabricList.value.is_default, // 是否默认地址 (没有默认选择)
    logistics_company: fabricList.value.logistics_company, // 物流公司
    logistics_area: fabricList.value.logistics_area, // 物流区域
    contact_name: fabricList.value.contact_name, // 联系人名称
    phone: fabricList.value.customer_phone, // 手机号
    print_tag: fabricList.value.print_tag, // 打印打印标签
  }
})

const tableConfig = ref({
  fieldApiKey: 'RawSalePlanEdit',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '100',
  filterStatus: false,
  //   footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => cellClick(val),
  footerMethod: (val: any) => FooterMethod(val),
})

const tableConfig_ls = ref({
  fieldApiKey: 'RawSalePlanEdit_b',
  showSlotNums: true,
  filterStatus: false,
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')}`

      if (['piece_count'].includes(column.property))
        return `${sumNum(data, 'piece_count')}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['total_price'].includes(column.property))
        return `￥${sumNum(data, 'total_price')}`

      if (['yarn_ratio'].includes(column.property))
        return `${sumNum(data, 'yarn_ratio')}`

      if (['use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'use_yarn_quantity')}`

      return null
    }),
  ]
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const tableRef = ref()
watch(
  () => TableList,
  () => {
    TableList.value.map((item: any) => {
      const total = new Decimal(Number(item.weight)).times(new Decimal(Number(item.unit_price))).plus(new Decimal(Number(item.other_price)))
      item.total_price = total.toFixed(2)

      return item
    })
    nextTick(() => {
      tableRef.value.tableRef?.updateFooter()
    })
  },
  { deep: true },
)

const tableRefOne = ref()

function blur(row: any) {
  // 顺带触发一下当前行的总金额计算
  computeRowTotalPrice(row)

  state.use_yarnList.map((item: any) => {
    item.weight = row.weight
    item.record_id = row.record_id
    const tax = 1 + formatPriceDiv(item.yarn_loss)
    const total: any = new Decimal(Number(item.weight)).times(new Decimal(formatPriceDiv(item.yarn_ratio))).times(new Decimal(tax))

    item.use_yarn_quantity = Number.isNaN(total) ? 0 : total.toFixed(2)

    return item
  })
  nextTick(() => {
    tableRefOne.value.tableRef?.updateFooter()
  })
  // TODO: 每编辑一次数据，都要将他实时保存起来
  TableList.value.map((item: any) => {
    if (item.record_id === row.record_id) {
      item.material_ratio = state.use_yarnList
      return item
    }
  })
}

function handBlur(row: any) {
  TableList.value.map((item: any) => {
    if (item.record_id === row.record_id) {
      item.material_ratio = state.use_yarnList
      return item
    }
  })
  nextTick(() => {
    tableRefOne.value.tableRef?.updateFooter()
  })
}

function handDelete(index: number) {
  TableList.value.splice(index, 1)
}

let appendRecordId = 0
function handCopy(index: number) {
  const copyData = deepClone(TableList.value[index])
  copyData.id = 0
  copyData.record_id = appendRecordId++
  delete copyData._X_ROW_KEY_TABLE
  TableList.value.push(copyData)
}

const SelectRawInfomationRef = ref()

function handAdd() {
  SelectRawInfomationRef.value.state.showModal = true
}

function handSureAdd(list: any) {
  list.forEach((item: any) => {
    const idNum = Math.floor(Math.random() * 10000)
    item?.yarn?.map((it: any) => {
      it.yarn_loss = formatPriceDiv(it.wastage)
      it.yarn_ratio = formatPriceDiv(it.ratio)
      it.raw_material_color_id = it.colorId
      it.raw_material_color_name = it.color
      it.raw_material_id = it.src_raw_matl_id
      return it
    })
    TableList.value.push({
      ...item,
      record_id: idNum,
      raw_material_color_code: '',
      raw_material_color_name: '',
      weight: '',
      piece_count: '',
      upper_limit: '',
      lower_limit: '',
      unit_price: '',
      other_price: '',
      total_price: '',
      internal_remark: '',
      material_ratio: item?.yarn || [],
      raw_material_id: item.id,
      selected: false,
      remark: '',
      id: null,
    })
  })
  SelectRawInfomationRef.value.state.showModal = false
}

function cellClick(val: any) {
  state.use_yarnList = val.row?.material_ratio?.map((item: any) => {
    // item.yarn_loss = item.count === 0 ? formatPriceDiv(item.material_loss) : item.yarn_loss
    // item.yarn_ratio = item.count === 0 ? formatPriceDiv(item.material_ratio) : item.yarn_ratio
    // item.total_yarn = 0
    item.weight = Number(val.row?.weight)
    item.record_id = val.row.record_id || appendRecordId++
    item.unit_name = val.row.unit_name
    item.craft = val.row.craft
    return item
  })
}
const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = updateSaleProductPlanOrder()

async function handleSure() {
  const { addressData } = await addressCardRef.value?.getFormData()
  if (!TableList.value.length)
    return ElMessage.error('至少添加一条原料信息')

  const list = deepClone(TableList.value)

  for (let i = 0; i < list.length; i++) {
    if (list[i].weight === '')
      return ElMessage.error('数量不可为空')

    if (list[i].unit_price === '')
      return ElMessage.error('单价不可为空')

    if (Number(sumNum(list[i]?.material_ratio, 'yarn_ratio')) !== 100 && list[i]?.material_ratio?.length)
      return ElMessage.error('用纱比例的和必须为100')

    for (let j = 0; j < list[i].material_ratio.length; j++) {
      if (list[i].material_ratio[j].yarn_ratio === '')
        return ElMessage.error('请填写用纱比例')

      list[i].material_ratio[j].yarn_ratio = formatPriceMul(list[i].material_ratio[j].yarn_ratio)
      list[i].material_ratio[j].yarn_loss = formatPriceMul(list[i].material_ratio[j].yarn_loss)
      list[i].material_ratio[j].use_yarn_quantity = formatWeightMul(list[i].material_ratio[j].use_yarn_quantity)
    }

    if (list[i].raw_material_color_id === '')
      list[i].raw_material_color_id = 0

    list[i].weight = formatWeightMul(list[i].weight)
    list[i].upper_limit = formatPriceMul(list[i].upper_limit)
    list[i].lower_limit = formatPriceMul(list[i].lower_limit)
    list[i].unit_price = formatUnitPriceMul(list[i].unit_price)
    list[i].total_price = formatPriceMul(list[i].total_price)
    list[i].other_price = formatPriceMul(list[i].other_price)
    list[i].piece_count = formatPriceMul(list[i].piece_count)
  }

  const query = {
    customer_id: state.form.customer_id,
    sale_system_id: state.form.sale_system_id,
    sale_user_id: state.form.sale_user_id,
    voucher_number: state.form.voucher_number,
    order_time: formatDate(state.form.date),
    receipt_time: formatDate(state.form.delivery_date),
    deposit: state.form.deposit,
    plan_type: 3,
    sale_tax_rate: formatPriceMul(state.form.sale_tax_rate),
    is_with_tax_rate: state.form.is_with_tax_rate,
    internal_remark: state.form.internal_remark,
    item_data: list,
    id: Number(route.query.id),
    settle_method_id: Number(state.form.settle_method_id) || 0,
    // delivery_type_id: Number(state.form.delivery_type_id) || 0,
    texture_url: state.fileList ? state.fileList.join(',') : '',
    // 以下是地址卡的收货方式数据 addressInfos
    location: addressData.location.join(' '), // 收货地址(省市区)
    receipt_address: addressData.address, // 收货地址(详情地址)
    contact_name: addressData.contact_name, // 联系人
    customer_phone: addressData.phone, // 联系电话
    logistics_area: addressData.logistics_area, // 物流区域
    is_default: addressData.is_default, // 是否默认地址
    logistics_company: addressData.logistics_company, // 物流公司名称
    process_factory: addressData.name, // 加工厂昵称
    print_tag: state.form.print_tag || addressData.print_tag, // 出货标签
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'RawSalePlanDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const bulkShow = ref(false)

const bulkSetting = ref<any>({})

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  TableList.value?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
}

const bulkList = reactive<any>([
  {
    field: 'raw_material_color_code',
    title: '颜色编号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'raw_material_color_name',
    title: '颜色名称',
    component: 'input',
    type: 'text',
  },
  {
    field: 'piece_count',
    title: '件数',
    component: 'input',
    type: 'integer',
  },
  {
    field: 'weight',
    title: '数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'upper_limit',
    title: '上限',
    component: 'input',
    type: 'float',
  },
  {
    field: 'lower_limit',
    title: '下限',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '单价',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

const columnList = ref([
  {
    field: 'code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'type_name',
    title: '原料类型',
    minWidth: 100,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    minWidth: 100,
  },
  {
    field: 'craft',
    title: '原料工艺',
    minWidth: 100,
  },
  {
    field: 'count',
    title: '原料支数',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'raw_material_color_code',
    title: '颜色编号',
    minWidth: 100,
    soltName: 'raw_material_color_code',
  },
  {
    field: 'raw_material_color_name',
    title: '颜色名称',
    minWidth: 100,
    soltName: 'raw_material_color_name',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    soltName: 'weight',
    required: true,
  },
  {
    field: 'piece_count',
    title: '件数',
    minWidth: 100,
    soltName: 'piece_count',
  },
  {
    field: 'upper_limit',
    title: '上限',
    minWidth: 100,
    soltName: 'upper_limit',
  },
  {
    field: 'lower_limit',
    title: '下限',
    minWidth: 100,
    soltName: 'lower_limit',
  },
  {
    field: 'unit_price',
    title: '单价',
    minWidth: 100,
    soltName: 'unit_price',
    required: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    soltName: 'other_price',
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
])

const columnList_ls = ref([
  {
    field: 'code',
    title: '原料编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '原料名称',
    minWidth: 100,
  },
  {
    field: 'type_name',
    title: '原料类型',
    minWidth: 100,
  },
  {
    field: 'ingredient',
    title: '原料成分',
    minWidth: 100,
  },
  {
    field: 'craft',
    title: '原料工艺',
    minWidth: 100,
  },
  {
    field: 'count',
    title: '原料支数',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 100,
    soltName: 'yarn_loss',
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 100,
    soltName: 'yarn_ratio',
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
    soltName: 'use_yarn_quantity',
  },
])
// 选择客户
async function customerChange(val: any) {
  state.form.customer_name = val?.name || ''
  state.form.customer_code = val?.code || ''
  state.form.sale_user_id = val?.seller_id || ''
  state.form.sale_system_id = val?.select_sale_system_id || ''
}

/** 颜色选择 --- */
function handleChangeColor(value: any, row: any) {
  row.raw_material_color_id = value?.id || 0
  row.raw_material_color_name = value.name
  row.raw_material_color_code = value.code
  row.tempColorCode = value.code
  row.tempColorName = value.name
}

function computeRowTotalPrice(row: any) {
  row.total_price = currency(row.weight).multiply(row.unit_price).add(row.other_price).value
}

const { 聚焦下一行, 聚焦下一行_表尾跳转 } = useTableEnterAutoFocus(
  tableRef,
  TableList,
)
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户名称:" required>
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                is-merge
                field="name"
                :default-value="{
                  id: state.form.customer_id,
                  name: state.form.customer_name,
                  code: state.form.customer_code,
                }"
                show-choice-system
                @change-value="customerChange"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable placeholder="请选择" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="订单日期:">
          <template #content>
            <el-form-item prop="date">
              <el-date-picker v-model="state.form.date" type="date" placeholder="订单日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="交货日期:">
          <template #content>
            <el-form-item prop="delivery_date">
              <el-date-picker v-model="state.form.delivery_date" type="date" placeholder="交货日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="销售员:">
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents v-model="state.form.sale_user_id" :query="{ duty: EmployeeType.salesman }" api="Adminemployeelist" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="合同编号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" placeholder="请输入" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="合同定金:">
          <template #content>
            <vxe-input v-model="state.form.deposit" type="float" clearable placeholder="请输入">
              <template #suffix>
                元
              </template>
            </vxe-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算方式:">
          <template #content>
            <SelectSettleTypeDialog
                v-model="state.form.settle_method_id"
                field="name"
            />
          </template>
        </DescriptionsFormItem>

        <div style="display: flex; align-items: center;">
          <div style="min-width: 96px;text-align: right;margin-right: 15px;">
            <el-checkbox v-model="state.form.is_with_tax_rate" label="是否含税" />
          </div>
          <vxe-input
            v-model="state.form.sale_tax_rate" placeholder="税率" :min="0" type="float" clearable :disabled="!state.form.is_with_tax_rate" style="width: 170px;"
            :controls="false"
          >
            <template #suffix>
              %
            </template>
          </vxe-input>
        </div>
        <DescriptionsFormItem label="单据备注:">
          <template #content>
            <el-input v-model="state.form.internal_remark" placeholder="请输入" :maxlength="200" />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
    <div class="m-[10px]">
      <AddressCard
        v-if="fabricList.id"
        ref="addressCardRef"
        type="Edit"
        :is-show-delivery="false"
        :customer-ids="state.form.customer_id"
        :customer-name="state.form.customer_name"
        :address-data="addressDataProps"
      />
    </div>
  </FildCard>
  <FildCard title="原料信息" class="mt-[5px]">
    <template #right-top>
      <el-button @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" @click="handAdd">
        从资料中添加
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="TableList" :column-list="columnList">
      <template #raw_material_color_code="{ row }">
        <SelectDialog
          v-model="row.raw_material_color_id"
          api="GetRawMaterialColor"
          label-field="code"
          :query="{ raw_matl_id: row.raw_material_id, code: row.tempColorCode }"
          :table-column="[
            {
              field: 'code',
              title: '颜色编号',
              defaultData: {
                id: row.raw_material_color_id,
                name: row.raw_material_color_name,
                code: row.raw_material_color_code,
              },
            },
          ]"
          @change-value="handleChangeColor($event, row)"
          @change-input="(val) => { row.tempColorCode = val }"
        />
      </template>
      <template #raw_material_color_name="{ row }">
        <SelectDialog
          v-model="row.raw_material_color_id"
          api="GetRawMaterialColor"
          label-field="name"
          :query="{ raw_matl_id: row.raw_material_id, name: row.tempColorName }"
          :table-column="[
            {
              field: 'name',
              title: '颜色名称',
              defaultData: {
                id: row.raw_material_color_id,
                name: row.raw_material_color_name,
                code: row.raw_material_color_code,
              },
            },
          ]"
          @change-value="handleChangeColor($event, row)"
          @change-input="(val) => { row.tempColorName = val }"
        />
      </template>
      <template #weight="{ row, rowIndex }">
        <vxe-input
          :id="`weight-${rowIndex}`"
          v-model="row.weight"
          type="float"
          clearable
          @keydown="聚焦下一行_表尾跳转(rowIndex, $event, 'weight', 'piece_count')"
          @blur="blur(row)"
        />
      </template>
      <template #piece_count="{ row, rowIndex }">
        <vxe-input
          :id="`piece_count-${rowIndex}`"
          v-model="row.piece_count" type="integer"
          clearable
          @keydown="聚焦下一行(rowIndex, $event, 'piece_count')"
        />
      </template>
      <template #upper_limit="{ row, rowIndex }">
        <vxe-input
          :id="`upper_limit-${rowIndex}`"
          v-model="row.upper_limit"
          type="float"
          clearable
          @keydown="聚焦下一行(rowIndex, $event, 'upper_limit')"
        >
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #lower_limit="{ row, rowIndex }">
        <vxe-input
          :id="`lower_limit-${rowIndex}`"
          v-model="row.lower_limit"
          type="float"
          clearable
          @keydown="聚焦下一行(rowIndex, $event, 'lower_limit')"
        >
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #unit_price="{ row, rowIndex }">
        <vxe-input
          :id="`unit_price-${rowIndex}`" v-model="row.unit_price" type="float" clearable
          @change="computeRowTotalPrice(row)"
          @keydown="聚焦下一行(rowIndex, $event, 'unit_price')"
        />
      </template>
      <template #other_price="{ row, rowIndex }">
        <vxe-input
          :id="`other_price-${rowIndex}`" v-model="row.other_price" type="float" clearable
          @change="computeRowTotalPrice(row)"
          @keydown="聚焦下一行(rowIndex, $event, 'other_price')"
        />
      </template>
      <template #remark="{ row, rowIndex }">
        <vxe-input
          :id="`remark-${rowIndex}`" v-model="row.remark"
          clearable
          @keydown="聚焦下一行(rowIndex, $event, 'remark')"
        />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text link type="primary" @click="handCopy(rowIndex)">
          复制
        </el-button>
        <el-button text link type="danger" @click="handDelete(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱信息" class="mt-[5px]">
    <Table ref="tableRefOne" :config="tableConfig_ls" :table-list="state.use_yarnList" :column-list="columnList_ls">
      <template #yarn_loss="{ row }">
        <vxe-input v-model="row.yarn_loss" type="float" clearable @blur="blur(row)">
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #yarn_ratio="{ row }">
        <vxe-input v-model="row.yarn_ratio" type="float" clearable @blur="blur(row)">
          <template #suffix>
            %
          </template>
        </vxe-input>
      </template>
      <template #use_yarn_quantity="{ row }">
        <vxe-input v-model="row.use_yarn_quantity" type="float" clearable @blur="handBlur(row)" />
      </template>
    </Table>
  </FildCard>
  <FildCard title="凭证信息" class="mt-[5px]" :tool-bar="false">
    <TextureMapWall v-model:image-list="state.fileList" paste text="" />
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
  <SelectRawInfomation ref="SelectRawInfomationRef" @handle-sure="handSureAdd" />
</template>

<style></style>
