<!--
  @Description: 选择库存细码详情
  涉及单据：
  1. 现货配布单
  2. 成品其他出仓单
  3. 成品采购退货出仓单
  4. 成品内部调拨出仓单
  5. 销售出仓单
  6. 销售送货单
 -->
<script setup lang="ts">
import { reactive, watch } from 'vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { formatHashTag } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import { arrayToString } from '@/common/util'
import { processDataOut } from '@/common/handBinary'

const form_options = [
  {
    text: '成品名称',
    key: 'product_code',
    formatter: (row: any) => {
      return formatHashTag(row.product_code, row.product_name)
    },
  },
  {
    text: '色号颜色',
    key: 'product_color_name',
    formatter: (row: any) => {
      return formatHashTag(row.product_color_code, row.product_color_name)
    },
  },
  {
    text: '幅宽/克重',
    key: 'finish_product_width_and_unit_name',
    formatter: (row: any) => {
      return arrayToString([row.finish_product_width_and_unit_name, row.finish_product_gram_weight_and_unit_name], '*')
    },
  },
  {
    text: '面料单位',
    key: 'unit_name',
  },
  {
    text: '配布备注',
    key: 'remark',
  },
]

const state = reactive<any>({
  showModal: false,
  modalName: '库存细码查看',
  form: {
    sale_system_name: 111,
  },
  tableConfig: {
    height: '100%',
    fieldApiKey: 'FineSizeRepertoryAddxxoxoioxioi',
    showSlotNums: true,
    footerMethod: (val: any) => FooterMethod(val),
  },
  isCloth: false, // 是否为配布单
  datalist: [],
  columnList: [
    {
      field: 'roll',
      title: '匹数',
      minWidth: 60,
    },
    {
      field: 'volume_number',
      title: '卷号',
      minWidth: 60,
    },
    {
      field: 'warehouse_bin_name',
      title: '仓位',
      minWidth: 60,
    },
    {
      field: 'dye_factory_dyelot_number',
      title: '染厂缸号',
      minWidth: 100,
    },
    {
      field: 'base_unit_weight',
      title: '数量',
      minWidth: 60,
    },
    {
      field: 'weight_error',
      title: '码单空差',
      minWidth: 60,
    },
    {
      field: 'settle_error_weight',
      title: '结算空差',
      minWidth: 60,
    },
    {
      field: 'settle_weight',
      title: '结算数量',
      minWidth: 60,
    },
    {
      field: 'length',
      title: '辅助数量',
      minWidth: 100,
    },
  ],
})

function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['weight_error', 'settle_error_weight', 'settle_weight', 'roll', 'base_unit_weight', 'length'].includes(column.field))
        return sumNum(data, column.field, '')

      return null
    }),
  ]
  return footerData
}

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
  { deep: true },
)

function getData() {}
/**
 * 细码处理
 * @param row
 * @param flag 是否已经退位处理
 */
function showDialog(row: any, flag = false) {
  state.form = row
  if (flag)
    state.datalist = row?.item_fc_data

  else
    state.datalist = processDataOut(row?.item_fc_data)

  state.showModal = true
}

defineExpose({
  state,
  showDialog,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" :title="state.modalName" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col overflow-hidden h-full">
      <div class="descriptions_row vertical_min">
        <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :label="`${item.text}`" vertical-block>
          <template #content>
            {{ item?.formatter ? item.formatter(state.form) : state.form[item.key] }}
          </template>
        </DescriptionsFormItem>
      </div>
      <div v-if="state.isCloth" class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="同色同缸:">
          <template #content>
            {{ state.form.same_color_same_dye_lot ? '是' : '否' }}
          </template>
        </DescriptionsFormItem>
      </div>
      <FildCard title="" class="mt-[10px] flex flex-1 flex-col h-full overflow-hidden" :tool-bar="true" no-shadow>
        <Table ref="tablesRef" :config="state.tableConfig" :table-list="state.datalist" :column-list="state.columnList" />
      </FildCard>
    </div>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
    }
  }
}
</style>
