# 宜腾电子 ST76/96系列计数器通讯协议

ST76/96系列仪表可以后配数字通讯接口，其接口为 RS485，可以多达 32 块仪表并联在一起接到上位机对应的通讯口上，通过数字通讯口与上位计算机连接。在上位机上运行适当的通讯程序可检查和修改仪表中的各种参数，数字通讯线通过仪表后部端子连接到计算机。

仪表与上位机通讯为被动方式，即仪表不会主动向上位机发数据，必须由上位机向仪表发出读写命令仪表才会作相应的响应。对不同的仪表通讯时由上位机软件发出不同的表号进行区别。

仪表正常通电并进入正常显示状态；在二级参数设定模式下，将仪表设置为有效的地址及波特、数字结构。依次按“SET”键可得到并循环显示下列参数：

- **Adrset**：仪表地址号: 默认为01.（范围01-32）
- **BAUSET**：通讯速率: 默认为9600.（范围9600-4800）

| 代码 | 数据位 | 停止位 | 校验位 |
|------|--------|--------|--------|
| 0    | 8      | 1      | 1      |
| 1    | 8      | 1      | 无     |
| 2    | 8      | 1      | 奇     |
| 3    | 8      | 1      | 偶     |

**UArt**：数字结构由下面的代码表示不同的结构组合：出厂默认值1.

## 一、通讯类型和帧格式

本系列仪表使用异步串行通讯接口，符合标准 RS485 接口电平规范。通讯信号兼容标准 MODBUS RTU 协议，默认数据帧格式为 1 位起始位、8 位数据位、无校验、1 位停止位。通讯传输数据的波特率可设置为 4800 或 9600 Bit/S。（必要时在仪表后部端子并接一只 100Ω左右的电阻）

### 表1-1 MODBUS数帧默认设置格式

| 波特率 | 起始位 | 数据位 | 校验位 | 停止位 |
|--------|--------|--------|--------|--------|
| 9600   | 1      | 8      | None   | 1      |

## 二、通讯信息的发送格式

仪表端为从机, 接收仪表数据一端为主机, 控制指令首先由主机发送到指定地址的从机，指定地址的从机接收到了命令，如果接收数据 CRC 校验和指令格式正确，从机会执行相应的操作，然后从机把执行结果返回到主机。

1. **仪表地址码**（1 个字节）
   - 地址范围 1-32，主机发送的指令包含了仪表地址, 被指定的仪表收到指令后执行操作并返回执行结果。（同一总线内的仪表地址码不能重复，仪表地址码需先在仪表菜单 ADRSET 中设定好）

2. **操作指令码**（1个字节）
   - 指令代码由主机发送给从机，从机收指令代码执行相应操作。如果从机执行操作正常原样返回收到的指令代码，如果执行异常从机将接收到的功能码最高位置 1 后再返回。

3. **数据码**（不同指令代码数据所发送的数据码长度不同）
   - 需要发送给仪表的数据（例如报警值、设定值等）。

### 通信发送和接收信息格式

以下通过四组示例说明 ST76 系列仪表 MODBUS 数据的发送:

- **示例一**: 读取仪表当前报警状态
  - 由 PLC 或上位机向仪表发 `01 03 00 00 00 01 84 0A` 数据即可, 发送各字节数据含义见表1

- **示例二**: 读取仪表上排显示数值
  - 由 PLC 或上位机向仪表发 `01 03 00 21 00 02 94 01` 数据即可, 发送各字节数据含义见表1

- **示例三**: 读取仪表下排显示数值
  - 由 PLC 或上位机向仪表发 `01 03 00 23 00 02 35 C1` 数据即可, 发送各字节数据含义见表1

- **示例四**: 控制仪表上排数值清零
  - 由 PLC 或上位机向仪表发 `01 06 00 00 00 02 08 0B` 数据即可, 发送各字节数据含义见表2

- **示例五**: 设置 AL1 和 AL2 报警值分别设置为 10 和 20 (AL1 和 AL2 必须同时修改)
  - 由 PLC 或上位机向仪表发 `01 10 00 0F 00 04 08 00 00 00 10 00 00 00 20 62 75` 数据即可

### 表1 ST76仪表MODBUS常用多读(03)操作指令

| 序号 | 功能                     | 仪表地址 | 多读指令码 | 起始地址 | 读取数量 | CRC16校验码 |
|------|------------------------|----------|------------|----------|----------|--------------|
| 1    | 读取仪表当前报警状态     | 0x01     | 0x03       | 0x0000   | 0x0001   | 0x840A       |
| 2    | 读取仪表上排显示数值     | 0x01     | 0x03       | 0x0021   | 0x0002   | 0x9401       |
| 3    | 读取仪表下排显示数值     | 0x01     | 0x03       | 0x0023   | 0x0002   | 0x35C1       |

### 表2 ST76仪表MODBUS常用单写(06)操作指令

| 序号 | 功能                     | 设备地址 | 单写指令码 | 写入地址 | 写入数据 | CRC16校验码 |
|------|------------------------|----------|------------|----------|----------|--------------|
| 1    | 仪表上排显示数值复位     | 0x01     | 0x06       | 0x0000   | 0x0002   | 0x080B       |
| 2    | 仪表下排显示数值复位     | 0x01     | 0x06       | 0x0000   | 0x0001   | 0x480A       |
| 3    | 仪表暂停运行             | 0x01     | 0x06       | 0x0000   | 0x0004   | 0x8809       |
| 4    | 仪表恢复运行             | 0x01     | 0x06       | 0x0000   | 0x0008   | 0x880C       |

### 返回数据格式

| 序号 | 功能                     | 设备地址 | 指令码 | 返回数据的字节数 | 返回的数据 | CRC16校验码 |
|------|------------------------|----------|--------|------------------|------------|--------------|
| 1    | 返回上排显示数据         | 0x01     | 0x03   | 0x02             | 0x0006     | 0x3846       |
| 2    | 返回下排显示数据-2      | 0x01     | 0x03   | 0x02             | 0xFFFE     | 0x7834       |
| 3    | 返回仪表AL1报警中状态    | 0x01     | 0x03   | 0x02             | 0x0001     | 0x7984       |
| 4    | 返回仪表AL2报警中状态    | 0x01     | 0x06   | 0x02             | 0x0002     | 0x3985       |

### 通讯错误处理

如果在指令序列和 CRC 校码均正确的情况下，出现了操作错误，仪表将向主机返回错误信息, 从机将此次接收到的【操作指令码】高位置 1 后连同【仪表地址码】、【错误码】一起作为错误信息返回。

### 表4 从机返回错误代码格式

| 序号 | 功能                     | 设备地址 | 0x80+指令码 | 错误代码 | CRC16校验码 |
|------|------------------------|----------|--------------|----------|--------------|
| 1    | 不支持的操作指令         | 0x01     | 0x83         | 0x08     | 0x40F6       |
| 2    | 无效的寄存器地址         | 0x01     | 0x83         | 0x09     | 0x8136       |
| 3    | 无效的波特率数据         | 0x01     | 0x83         | 0x10     | 0x40FC       |

### 表5 ST系列仪表错误代码总表

| 错误代码 | 错误含义                     | 备注       |
|----------|------------------------------|------------|
| 0x08     | 不支持的操作指令             | 所有模式   |
| 0x09     | 无效的寄存器地址             | 所有模式   |
| 0x10     | 无效的波特率数据             | 所有模式   |
| 0x11     | 无效的起始值1                | 所有模式   |
| 0x12     | 无效的起始值2                | 所有模式   |
| 0x13     | 无效的报警值1                | 所有模式   |
| 0x14     | 无效的报警值2                | 所有模式   |
| 0x15     | 无效的输出模式1              | 所有模式   |
| 0x16     | 无效的输出模式2              | 所有模式   |
| 0x17     | 无效的输出时间1              | 所有模式   |
| 0x18     | 无效的输出时间2              | 所有模式   |
| 0x19     | 无效的计时器模式1            | 计时器专用 |
| 0x1A     | 无效的计时器模式2            | 计时器专用 |
| 0x1B     | 无效的计时组合模式           | 计时器专用 |
| 0x1C     | 无效的计时器模式             | 计时计数器专用 |
| 0x1D     | 无效的计数器模式             | 计时计数器专用 |
| 0x20     | 无效的小数点                 | 计米器/总量批次专用 |
| 0x21     | 无效的计数组合模式           | 计米器专用 |
| 0x22     | 无效的计数频率               | 计米器/总量批次专用 |
| 0x23     | 无效的编码器设置             | 总量批次专用 |
| 0x24     | 无效的锁键设置               | 总量批次专用 |

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).
