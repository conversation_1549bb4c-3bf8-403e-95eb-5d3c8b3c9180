# 布飞取消对话框组件

## 组件说明

`FabricFlyCancelDialog.vue` 是一个可复用的布飞取消对话框组件，支持弹窗形式的布飞取消操作。

## 功能特性

- ✅ 弹窗形式展示，不影响当前页面
- ✅ 条码输入和自动查询布飞信息
- ✅ 实时显示布飞详细信息
- ✅ 支持键盘快捷键操作（F12保存、F1清空、ESC取消）
- ✅ 完整的表单验证和错误处理
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 支持API调用失败时的模拟数据展示

## 使用方法

### 1. 导入组件

```vue
<script setup lang="ts">
import FabricFlyCancelDialog from '@/pages/grayFabricMange/greyClothTicketCancel/components/FabricFlyCancelDialog.vue'
import { ref } from 'vue'

// 对话框显示状态
const fabricFlyCancelDialogVisible = ref(false)
</script>
```

### 2. 在模板中使用

```vue
<template>
  <!-- 触发按钮 -->
  <el-button type="primary" @click="fabricFlyCancelDialogVisible = true">
    布飞取消
  </el-button>

  <!-- 布飞取消对话框 -->
  <FabricFlyCancelDialog
    v-model="fabricFlyCancelDialogVisible"
    title="布飞取消"
    @success="handleFabricFlyCancelSuccess"
  />
</template>
```

### 3. 处理成功事件

```vue
<script setup lang="ts">
// 处理布飞取消成功
function handleFabricFlyCancelSuccess(data: any) {
  console.log('布飞取消成功:', data)
  ElMessage.success(`条码 ${data.barcode} 布飞取消成功`)
  // 可以在这里刷新列表数据或执行其他操作
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | false | 对话框显示状态 |
| title | string | '布飞取消' | 对话框标题 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value: boolean | 更新对话框显示状态 |
| success | data: { barcode: string } | 布飞取消成功时触发 |

## 键盘快捷键

| 快捷键 | 功能 |
|--------|------|
| F12 | 保存（取消布飞） |
| F1 | 清空表单 |
| ESC | 关闭对话框 |
| Enter | 在条码输入框中按Enter可查询布飞信息 |

## 界面展示

### 1. 初始状态
- 显示红色提示信息："等待输入条形码资料!"
- 条码输入框获得焦点
- 保存按钮处于禁用状态

### 2. 输入条码后
- 自动查询布飞信息
- 显示布飞详细信息卡片
- 保存按钮变为可用状态

### 3. 布飞信息展示
- 生产通知单、排产单
- 坯布名称、卷号
- 机台、条码
- 纱名（支持长文本显示）

## API接口

组件使用以下API接口：

- `getFabricFlyDetail`: 根据条码获取布飞详情
- `fabricFlyCancelByBarcode`: 根据条码取消布飞

## 错误处理

- 条码为空时显示警告提示
- API调用失败时使用模拟数据进行演示
- 取消操作失败时显示错误信息

## 样式特点

- 使用vxe-modal组件作为对话框容器
- 响应式布局，支持不同屏幕尺寸
- 清晰的信息层级和视觉分组
- 符合系统整体设计风格

## 注意事项

1. 确保项目中已正确配置vxe-modal组件
2. 确保相关API接口已正确实现
3. 组件会自动处理键盘事件的添加和移除
4. 对话框关闭时会自动清空表单数据

## 完整示例

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import FabricFlyCancelDialog from '@/pages/grayFabricMange/greyClothTicketCancel/components/FabricFlyCancelDialog.vue'

const fabricFlyCancelDialogVisible = ref(false)

function handleFabricFlyCancel() {
  fabricFlyCancelDialogVisible.value = true
}

function handleFabricFlyCancelSuccess(data: any) {
  ElMessage.success(`条码 ${data.barcode} 布飞取消成功`)
  // 刷新数据或其他操作
}
</script>

<template>
  <div>
    <el-button type="primary" @click="handleFabricFlyCancel">
      布飞取消
    </el-button>

    <FabricFlyCancelDialog
      v-model="fabricFlyCancelDialogVisible"
      title="布飞取消"
      @success="handleFabricFlyCancelSuccess"
    />
  </div>
</template>
```

这样就可以在任何页面中轻松集成布飞取消功能了！
