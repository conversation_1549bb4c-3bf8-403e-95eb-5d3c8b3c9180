import { cloneDeep, isArray, isNumber, isObject } from 'lodash-es'
import currency from 'currency.js'
import dayjs from 'dayjs'
import { baseDateDict, baseDict } from '@/common/handBinary/coverDict'

/**
 * 处理对象
 * @param obj 对象
 * @param isIn 是否进位
 */
function processObject(
  obj: Record<string, any>,
  isIn: boolean,
): Record<string, any> {
  // 使用Object.entries来遍历对象的键值对
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => {
      // 如果值是数组，使用map方法来处理每个元素
      if (isArray(value))
        return [key, value.map(item => isNumber(item) ? item : processObject(item, isIn))]

      // 如果值是对象，递归处理嵌套的对象
      if (isObject(value))
        return [key, processObject(value, isIn)]

      // 如果键在字典中，则进去处理
      if (Object.prototype.hasOwnProperty.call(baseDict, key))
        return handleCover(isIn, baseDict, key, value)

      // 日期格式化-如果键在字典中，则进去处理
      if (Object.prototype.hasOwnProperty.call(baseDateDict, key))
        return [key, formatDate(value, baseDateDict[key])]

      // 自动cover ID
      if (key.endsWith('_id')) {
        if (baseDict?.[key] === 1)
          console.error('ID字段已经自动处理过了，无需手动添加toNumber')
        // 如果是退位、key包含id、value的值为0，则转为空字符串
        if (!isIn && value === 0)
          return [key, '']
        // 相反，如果key包含id，并且value是空字符串，则转为0
        if (isIn && value === '')
          return [key, 0]
      }

      // 其他情况，直接返回键值对
      return [key, value]
    }),
  )
}

/**
 * 处理进退位
 * @param isIn 是否进位
 * @param baseDict 字典
 * @param key 键
 * @param value 值
 */
function handleCover(isIn: boolean, baseDict: Record<string, number>, key: string, value: string | number) {
  const base = baseDict[key] || 1 // 默认为1
  const processedValue = isIn ? currency(value).multiply(base).value : currency(value).divide(base).value
  return [key, processedValue]
}

/**
 *  格式化时间
 * @param {string} val 时间字符串，为空返回空
 * @param {string} formatTemp 时间格式，对照dayjs
 * @returns 格式后的日期
 */
function formatDate(val = '', formatTemp = 'YYYY-MM-DD') {
  return val === '' || val == null ? '' : dayjs(val).format(formatTemp)
}

function execute(
  data: Record<string, any> | Array<any>,
  isIn: boolean,
): Record<string, any> {
  if (isArray(data))
    return data.map(item => execute(item, isIn))

  return processObject(data, isIn)
}

/**
 * 进位，发送数据到后端就调这个
 * @param data
 */
export function processDataIn(data: Record<string, any>): Record<string, any> {
  return execute(cloneDeep(data), true)
}

/**
 * 退位，从后端接收数据就调这个
 * @param data
 */
export function processDataOut(data: Record<string, any>): Record<string, any> {
  return execute(cloneDeep(data), false)
}
