import { useRequest } from '@/use/useRequest'

// 获取列表
export const getProductAdjustOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/getProductAdjustOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加
export const addProductAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/addProductAdjustOrder',
    method: 'post',
  })
}

// 获取详情
export const getProductAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/getProductAdjustOrder',
    method: 'get',
  })
}

// 作废
export const updateProductAdjustOrderAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/updateProductAdjustOrderAuditStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateProductAdjustOrderAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/updateProductAdjustOrderAuditStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateProductAdjustOrderAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/updateProductAdjustOrderAuditStatusReject',
    method: 'put',
  })
}
// 消审
export const updateProductAdjustOrderAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/updateProductAdjustOrderAuditStatusWait',
    method: 'put',
  })
}

// 更新
export const updateProductAdjustOrder = () => {
  return useRequest({
    url: '/admin/v1/product/productAdjustOrder/updateProductAdjustOrder',
    method: 'put',
  })
}

// 获取仓管员
export const GetEmployeeListEnum = () => {
  return useRequest({
    url: '/admin/v1/employee/list_enum',
    method: 'get',
  })
}
