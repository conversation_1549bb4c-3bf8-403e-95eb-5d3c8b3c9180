<script setup lang="ts" name="Setting">
import { computed } from 'vue'
import FildCard from '@/components/FildCard.vue'
import { useScanerStore } from '@/stores/scaner'
import { useMeasureMeterService } from '@/use/useMeasurMeterService'
import { useMeasureMeterStore } from '@/stores/measureMeter'

// import { printTemplate } from '@/stores/printTemplate'

const scanerStore = useScanerStore()
// const pt = printTemplate()
const scanerList = computed(() => scanerStore.list)
const defaultScanerindex = computed(() => (scanerStore.defaultScaner ? scanerStore.defaultScaner.index : null))
// 选择默认扫描器
function handleScanerChange(val: any) {
  const _defaultScaner = scanerList.value.filter(v => v.index === val)[0]
  scanerStore.setDefaultScaner(_defaultScaner)
}
const measureMeterStore = useMeasureMeterStore()
const Log = computed(() => {
  return measureMeterStore.measureMeterState.Log
})
const isConnected = computed(() => {
  return measureMeterStore.measureMeterState.isConnected
})
function handleConnectToSerialPort() {
  if (!measureMeterStore.measureMeterState.isConnected) {
    const measureMeterService = useMeasureMeterService()
    try {
      measureMeterService.connectToSerialPort()
      measureMeterStore.setMeasureMeterState(measureMeterService)
    }
    catch (error) {
      console.error('串口连接错误:', error)
    }
  }
}
function handleDisConnectToSerialPort() {
  handleClearLog()
  measureMeterStore.measureMeterState.disconnectPort()
}
function handleClearLog() {
  measureMeterStore.measureMeterState.clearLogMessages()
  measureMeterStore.clearLogList()
}
// const matchCloth = ref(1) // 配布单的打印机
// const saleValue = ref(2) // 销售单的打印机
// const printerList = ref([ // 获取到的打印机列表
//   { label: '129.0.0.1:9100', value: 1 },
//   { label: '129.0.0.2:8100', value: 2 },
//   { label: '129.0.0.3:7100', value: 3 },
//   { label: '*********:6100', value: 4 },
// ])
// 获取打印机列表的按钮
// function handleGetPrinter() {
//   pt.initPrint()
// }
// 比如点击这个按钮开始执行自动打印
// async function handleGetPrinterTem() {
// const clientId = 'AlBaUCNs3AIMFPLZAAAh'
// const client = socketStore.clientInfo[clientId]
// const printer = socketStore.printerList[0]
// if (socketStore.socketRef)
//   socketStore.socketRef.send({ testPdf, client, printer, type: 'pdf' })
// }
</script>

<template>
  <FildCard :tool-bar="false">
    <el-descriptions title="设备设置" :column="1" direction="vertical">
      <el-descriptions-item label="扫码器">
        <el-select v-model="defaultScanerindex" placeholder="选择默认扫描器" size="small" class="w-[200px]" @change="handleScanerChange">
          <el-option v-for="item in scanerList" :key="item.index" :label="item.path" :value="item.index" />
        </el-select>
      </el-descriptions-item>
      <!--      TODO：这里需要把 useMeasureMeterService 单例化才能使用 不然会连接两次相同的串口，因为成品质检就已经使用过一次 useMeasureMeterService 了 -->
      <!--      <el-descriptions-item label="电子计数器"> -->
      <!--        连接状态：<span :class="`${isConnected ? 'text-green-500' : 'text-red-500'} font-bold`"> -->
      <!--          {{ isConnected ? '已连接' : '未连接' }} -->
      <!--        </span> -->
      <!--        <el-button class="ml-2" plain type="primary" size="small" @click="handleConnectToSerialPort"> -->
      <!--          点击连接码表 -->
      <!--        </el-button> -->
      <!--        <el-button class="ml-2" plain type="primary" size="small" @click="handleDisConnectToSerialPort"> -->
      <!--          断开连接 -->
      <!--        </el-button> -->
      <!--        <div> -->
      <!--          打印日志： -->
      <!--          <el-button class="ml-2" plain type="primary" size="small" @click="handleClearLog"> -->
      <!--            清除日志 -->
      <!--          </el-button> -->
      <!--        </div> -->
      <!--        <Log style="width: 400px; height: 100px;" /> -->
      <!--      </el-descriptions-item> -->
    </el-descriptions>
  </FildCard>
</template>

<style lang="scss" scoped>
.auto_print_title{
  font-size: 20px;
  font-weight: 600;
  margin: 15px 0;
  color: #333333;
}
.auto_print_msg{
  margin: 15px 0;
  font-size: 16px;
  color: #333333;
}
</style>
