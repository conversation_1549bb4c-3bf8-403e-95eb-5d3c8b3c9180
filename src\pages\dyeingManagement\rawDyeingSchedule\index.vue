<script lang="ts" setup name="RawDyeingSchedule">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { rmmDyeOrderItemSituationlist, rmmDyeOrderItemSituationput, use_yarn_listby_src_id } from '@/api/rawDyeingSchedule'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { debounce, deleteToast, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'

const state = reactive<any>({
  filterData: {
    src_order_no: '',
    dye_unit_id: '',
    raw_material_color_id: '',
    dye_unit_color_id: '',
    dyelot_number: '',
    situ_id: '',
    devierDate: '',
    finish_status: '',
  },
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const options = ref([
  {
    label: '已完成',
    value: 1,
  },
  {
    label: '未完成',
    value: 2,
  },
])

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = rmmDyeOrderItemSituationlist()

// 获取数据
const getData = debounce(async () => {
  const query: any = {
    dye_date_start: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[0]) : '',
    dye_date_end: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[1]) : '',
    ...state.filterData,
  }
  delete query.devierDate
  await ApiCustomerList(getFilterData(query))
  // 处理数据，如果未返数量<=0,则默认勾选
  // data.value.list.forEach((item: any) => {
  //   if (formatPriceDiv(item.wait_back_weight) <= 0)
  //     item.is_finish = true
  // })
  if (data.value?.list)
    cellClick({ row: data.value.list[0] })
  // 处理绑定完成状态的boolean值
  const newList = data.value.list.map((e: any) => {
    return {
      ...e,
      finish_boolean: e.finish_status === 1,
    }
  })
  data.value.list = newList
  state.multipleSelection = []
}, 400)

onMounted(() => {
  getData()
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  fieldApiKey: 'RawDyeingSchedule',
  footerMethod: (val: any) => FooterMethod(val),
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: false,
  operateWidth: '140',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => cellClick(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

const tableConfig_return = ref({
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  height: '100%',
  fieldApiKey: 'RawDyeingScheduleReturn',
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['wait_back_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'other_price') as any)}`

      if (['back_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'back_weight') as any)}`

      if (['use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'use_yarn_quantity') as any)}`

      if (['back_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'back_yarn_quantity') as any)}`

      return null
    }),
  ]
}

const detailDataList = ref()

const { fetchData: detailFetch, data: detailData } = use_yarn_listby_src_id()
const detailShow = ref(false)
async function cellClick(val: any) {
  await detailFetch({ src_id: val.row.id })
  detailDataList.value = detailData.value || []
  detailShow.value = true
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const { fetchData: putFetch, msg: putMsg, success: putSuccess } = rmmDyeOrderItemSituationput()

async function handEdit() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请选择需要编辑进度的数据')

  await putFetch({ list_param: state.multipleSelection })
  if (putSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(putMsg.value)
  }
}

// 编辑完成状态:1-已完成
async function changeFinishStatus(e: boolean, row: any) {
  row.finish_boolean = !e // 先不修改选中
  const res = await deleteToast('确认修改状态吗？')
  if (res)
    row.finish_boolean = e

  row.finish_status = row.finish_boolean ? 1 : 2
}

const columnList = ref([
  {
    title: '通知单号',
    field: 'src_order_no',
    soltName: 'src_order_no',
    width: 150,
  },
  {
    title: '单据类型',
    field: 'src_order_type_name',
    width: 150,
  },
  {
    title: '染纱厂名称',
    field: 'dye_unit_name',
    width: 100,
  },
  {
    title: '染整日期',
    field: 'dye_date',
    width: 100,
    is_date: true,
  },
  {
    title: '原料编号',
    field: 'raw_material_code',
    width: 100,
  },
  {
    title: '原料名称',
    field: 'raw_material_name',
    width: 100,
  },
  {
    title: '色号',
    field: 'raw_material_color_code',
    width: 100,
  },
  {
    title: '颜色',
    field: 'raw_material_color_name',
    width: 100,
  },
  {
    title: '颜色类别',
    field: 'raw_material_color_type_name',
    width: '6%',
  },
  {
    title: '染纱厂色号',
    field: 'dye_unit_color_code',
    width: 100,
  },
  {
    title: '染纱厂颜色',
    field: 'dye_unit_color_name',
    width: 100,
  },
  {
    title: '染纱厂颜色类别',
    field: 'dye_unit_color_type_name',
    width: 100,
  },
  {
    title: '缸号',
    field: 'dyelot_number',
    width: 100,
    soltName: 'dyelot_number',
  },
  {
    title: '单位',
    field: 'measurement_unit_name',
    width: 100,
  },
  {
    title: '数量',
    field: 'weight',
    width: 100,
    isWeight: true,
  },
  {
    title: '已返数量',
    field: 'back_weight',
    width: 150,
    isWeight: true,
  },
  {
    title: '未返数量',
    field: 'wait_back_weight',
    width: 150,
    isWeight: true,
  },
  {
    title: '进度',
    field: 'situ_name',
    width: 150,
    soltName: 'situ_name',
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    width: 150,
    isDate: true,
  },
  {
    title: '完成状态',
    field: 'finish_status_name',
    width: 100,
    soltName: 'status',
  },
  {
    title: '完成人',
    field: 'finisher_name',
    width: 100,
  },
  {
    title: '完成时间',
    field: 'finish_time',
    width: 150,
    isDate: true,
  },
  {
    title: '出货备注',
    field: 'out_remark',
    width: 150,
  },
  {
    title: '染整交期',
    field: 'delivery_date',
    width: 100,
    is_date: true,
  },
  {
    title: '延期天数',
    field: 'delay_day',
    width: 100,
  },
  {
    title: '备注',
    field: 'remark',
    width: 150,
    soltName: 'remark',
  },
  {
    title: '原料等级',
    field: 'raw_material_level_name',
    width: 150,
  },
  {
    title: '等级编号',
    field: 'raw_material_level_code',
    width: 150,
  },
  {
    title: '供应商',
    field: 'supplier_names',
    width: 150,
  },
  {
    title: '原料品牌',
    field: 'brands',
    width: 150,
  },
  {
    title: '原料批号',
    field: 'batch_num',
    width: 150,
  },
])

const columnList_return = ref([
  {
    sortable: true,
    field: 'back_date',
    title: '返纱日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'back_order_no',
    title: '返纱单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'delivery_order_no',
    title: '送货单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receive_unit_name',
    title: '收货单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'use_yarn_measurement_unit_name',
    title: '用纱单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'use_yarn_quantity',
    title: '用纱数量',
    minWidth: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'back_yarn_measurement_unit_name',
    title: '返纱单位',
    minWidth: '5%',
  },
  {
    sortable: true,
    field: 'back_yarn_quantity',
    title: '返纱数量',
    minWidth: '5%',
    isWeight: true,
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="通知单号">
          <template #content>
            <el-input v-model="state.filterData.src_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂名称">
          <template #content>
            <SelectDialog
              v-model="state.filterData.dye_unit_id"
              :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill }"
              api="GetBusinessUnitListApi"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.unit_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料编号:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              api="rawmaterialMenu"
              label-field="code"
              :query="{ code: componentRemoteSearch.raw_code }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '原料编号',
                  minWidth: 100,
                },
              ]"
              @change-input="val => (componentRemoteSearch.raw_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              api="rawmaterialMenu"
              label-field="name"
              :query="{ name: componentRemoteSearch.raw_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="val => (componentRemoteSearch.raw_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料色号:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_color_id"
              api="GetRawMaterialColor"
              label-field="code"
              :query="{ raw_matl_id: state.filterData.raw_material_id, code: componentRemoteSearch.color_code }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'code',
                  title: '原料色号',
                  minWidth: 100,
                },
              ]"
              @change-input="val => (componentRemoteSearch.color_code = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂色号">
          <template #content>
            <SelectComponents v-model="state.filterData.dye_unit_color_id" api="GetlistFactoryEnum" label-field="code" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号">
          <template #content>
            <el-input v-model="state.filterData.dyelot_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整日期:" width="350">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="完成状态:">
          <template #content>
            <el-select v-model="state.filterData.finish_status" clearable class="m-2">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进度:">
          <template #content>
            <SelectComponents v-model="state.filterData.situ_id" api="GetInfoDyeingFinishingProgressEnumList" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button v-has="'RawDyeingSchedule_put'" v-btnAntiShake="handEdit" type="primary">
          确认编辑进度
        </el-button>
      </template>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #src_order_no="{ row }">
          <el-link type="primary" :underline="false">
            {{
              row.src_order_no
            }}
          </el-link>
        </template>
        <template #dyelot_number="{ row }">
          <vxe-input v-model="row.dyelot_number" :disabled="!row.selected" clearable />
        </template>
        <template #situ_name="{ row }">
          <SelectComponents v-model="row.situ_id" :disabled="!row.selected" api="GetInfoDyeingFinishingProgressEnumList" label-field="name" value-field="id" clearable />
        </template>
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" :disabled="!row.selected" clearable />
        </template>
        <template #status="{ row }">
          <el-checkbox :key="row.id" v-model="row.finish_boolean" :disabled="formatPriceDiv(row.wait_back_weight) <= 0" @change="changeFinishStatus($event, row)" />
        <!-- </div> -->
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table :config="tableConfig_return" :table-list="detailDataList" :column-list="columnList_return" />
    </FildCard>
  </div>
</template>

<style></style>
