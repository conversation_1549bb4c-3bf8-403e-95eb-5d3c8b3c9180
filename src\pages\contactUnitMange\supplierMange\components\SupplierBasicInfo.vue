<script lang="ts" setup>
import type { Contact } from '../../components/SelectWechatFriendLink.vue'
import SelectWechatFriendLink from '../../components/SelectWechatFriendLink.vue'
import SelectQyGroupLink from '../../components/SelectQyGroupLink.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { useSupplierCodeSearch } from '../composables/useSupplierForm'

interface Props {
  modelValue: any
  wechatContacts: Contact[]
  boundQyGroups: Api.CustomerAdd.SystemQYWXGroupChat[]
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'update:wechatContacts', value: Contact[]): void
  (e: 'update:boundQyGroups', value: Api.CustomerAdd.SystemQYWXGroupChat[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 供应商编号搜索逻辑
const { filterDatas, getSearchedCodes, selectConfirm } = useSupplierCodeSearch()

// 处理表单数据更新
const updateForm = (field: string, value: any) => {
  emit('update:modelValue', { ...props.modelValue, [field]: value })
}

// 处理供应商编号选择确认
const handleSelectConfirm = (e: any) => {
  const value = selectConfirm(e)
  if (value) {
    updateForm('supplerNums', value)
  }
}

// 处理微信联系人更新
const updateWechatContacts = (value: Contact[]) => {
  emit('update:wechatContacts', value)
}

// 处理企微群更新
const updateBoundQyGroups = (value: Api.CustomerAdd.SystemQYWXGroupChat[]) => {
  emit('update:boundQyGroups', value)
}
</script>

<template>
  <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
    <DescriptionsFormItem required label="供应商名称:">
      <template #content>
        <el-form-item prop="supplerName">
          <el-input :model-value="modelValue.supplerName" placeholder="供应商名称：" @input="updateForm('supplerName', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem label="供应商全称:">
      <template #content>
        <el-form-item prop="supplerAllName">
          <el-input :model-value="modelValue.supplerAllName" placeholder="供应商全称：" @input="updateForm('supplerAllName', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem label="供应商编号:">
      <template #content>
        <el-form-item prop="supplerNums">
          <el-select 
            :model-value="modelValue.supplerNums" 
            filterable 
            :filter-method="getSearchedCodes" 
            placeholder="供应商编号" 
            allow-create 
            default-first-option 
            class="w-full" 
            @blur="handleSelectConfirm"
            @update:model-value="updateForm('supplerNums', $event)"
          >
            <el-option v-for="item in filterDatas" :key="item?.id" :label="item?.code" :value="item?.code" disabled>
              <span style="float: left">{{ item?.code }}</span>
              <span style="float: right; color: var(--el-text-color-secondary); font-size: 10px">已存在</span>
            </el-option>
          </el-select>
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem required label="供应商类型:">
      <template #content>
        <el-form-item prop="supplerType">
          <SelectComponents 
            :model-value="modelValue.supplerType" 
            multiple 
            style="width: 300px" 
            api="AdminuenumsupplierType" 
            label-field="name" 
            value-field="id" 
            clearable 
            @update:model-value="updateForm('supplerType', $event)"
          />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem required label="所属营销体系:">
      <template #content>
        <el-form-item prop="marketingSystem">
          <SelectComponents 
            :model-value="modelValue.marketingSystem" 
            style="width: 300px" 
            api="AdminsaleSystemgetSaleSystemDropdownList" 
            multiple 
            label-field="name" 
            value-field="id" 
            clearable 
            @update:model-value="updateForm('marketingSystem', $event)"
          />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem required label="联系电话:">
      <template #content>
        <el-form-item prop="userPhone">
          <el-input :model-value="modelValue.userPhone" placeholder="联系电话：" @input="updateForm('userPhone', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem label="传真号:">
      <template #content>
        <el-form-item prop="fax_number">
          <el-input :model-value="modelValue.fax_number" clearable placeholder="请输入传真号" @input="updateForm('fax_number', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem required label="联系人:">
      <template #content>
        <el-form-item prop="contact_name">
          <el-input :model-value="modelValue.contact_name" placeholder="联系人：" @input="updateForm('contact_name', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem required label="地址:">
      <template #content>
        <el-form-item prop="address">
          <el-input :model-value="modelValue.address" placeholder="地址：" @input="updateForm('address', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem label="邮箱:">
      <template #content>
        <el-form-item prop="userEmail">
          <el-input :model-value="modelValue.userEmail" placeholder="邮箱：" @input="updateForm('userEmail', $event)" />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem label="备注:" copies="2">
      <template #content>
        <el-form-item prop="remark">
          <el-input 
            :model-value="modelValue.remark" 
            show-word-limit 
            maxlength="100" 
            type="textarea" 
            placeholder="请输入备注：" 
            @input="updateForm('remark', $event)"
          />
        </el-form-item>
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem v-has="'QYWechatFloatButtond'" label="企微客户绑定:" :copies="2">
      <template #content>
        <SelectWechatFriendLink :model-value="wechatContacts" @update:model-value="updateWechatContacts" />
      </template>
    </DescriptionsFormItem>
    
    <DescriptionsFormItem v-has="'QYWechatFloatButtond'" label="企微群绑定:">
      <template #content>
        <SelectQyGroupLink :model-value="boundQyGroups" :clickable="wechatContacts.length" @update:model-value="updateBoundQyGroups" />
      </template>
    </DescriptionsFormItem>
  </div>
</template>

<style lang="scss" scoped>
</style>
