import { useRequest } from '@/use/useRequest'

// 获取来源类型
export const GetWarehouseGoodInTypeEnum = () => {
  return useRequest({
    url: '/admin/v1/product/enum/getWarehouseGoodInTypeEnum',
    method: 'get',
  })
}

// 获取列表
export const getFpmOutReservationOrderList = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/getFpmOutReservationOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取详情
export const getFpmOutReservationOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/getFpmOutReservationOrder',
    method: 'get',
  })
}

// 新建
export const addFpmOutReservationOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/addFpmOutReservationOrder',
    method: 'post',
  })
}

// 编辑
export const updateFpmOutReservationOrder = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/updateFpmOutReservationOrder',
    method: 'put',
  })
}

// 作废
export const updateFpmOutReservationOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/updateFpmOutReservationOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateFpmOutReservationOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/updateFpmOutReservationOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateFpmOutReservationOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/updateFpmOutReservationOrderStatusReject',
    method: 'put',
  })
}
// 消审
export const updateFpmOutReservationOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/product/fpmOutReservationOrder/updateFpmOutReservationOrderStatusWait',
    method: 'put',
  })
}

// 库存汇总
export const getStockProductDropdownList = () => {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getStockProductDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
