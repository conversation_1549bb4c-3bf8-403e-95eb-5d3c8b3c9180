<script setup lang="ts">
import { computed, ref, useSlots, watch } from 'vue'
import BulkItem from '@/components/BulkSetting/item.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'

const props = withDefaults(defineProps<Props>(), {
  show: false,
})
const emit = defineEmits([
  'update:modelValue',
  'submit',
  'close',
  'update:otherValue',
])
const slots = useSlots()
interface ItemProps {
  field_name?: string // 选择时保存枚举名称字段
  hideTitle: boolean // 是否隐藏自动生成的标题
  field: string // 需要绑定的字段名
  title: string // 标题名称
  rule?: object // 字段验证
  component?: 'input' | 'select' | 'selectDate' | 'checkbox' | 'selectOneDate' // 显示的组件
  api?: any // 当component为select时需要传入请求api
  type: any // 组件类型
  digits: number // 当type为float时，保留的小数位
  valueField?: string // 选择框值
  labelField?: string // 选择框展示字段
  query?: object // 可传请求参数
  quickInput?: boolean
}

interface Props {
  modelValue: any // 初始化数据
  columnList: ItemProps[]
  show: boolean
  otherValue?: any
}

const show = computed(() => {
  return props.show
})

const newColumnList = ref()

const textarea = ref('')

const bulkField = ref<ItemProps>()

watch(
  () => props.columnList,
  (newVal) => {
    newColumnList.value = newVal?.map((item) => {
      if (item.component === 'input') {
        item.quickInput
          = typeof item.quickInput === 'boolean' ? item.quickInput : true
        if (item.field === bulkField.value?.field)
          bulkField.value.quickInput = item.quickInput
      }
      return item
    })
  },
  { deep: true, immediate: true },
)

const bulkSetting = ref<any>({})
const selectValue = ref<string>()

const selectRes = ref()
const fieldStr = ref()

watch(
  () => props.modelValue,
  () => {
    bulkSetting.value = props.modelValue || {}
  },
  {
    deep: true,
    immediate: true,
  },
)
function bulkHandSave() {
  const fieldConfig = bulkField.value
  const quickInputResult: string[] = []

  if (
    textarea.value
    && fieldConfig?.quickInput
    && fieldConfig?.component === 'input'
  )
    quickInputResult.push(...textarea.value.split('\n'))

  emit('update:modelValue', { ...bulkSetting.value })
  // TODO:selectRes.value, fieldStr.value 是方便联动选择框判断数据源而改变数据
  emit(
    'submit',
    {
      row: bulkField.value,
      value: bulkSetting.value,
      selectData: selectRes.value,
      quickInputResult,
    },
    selectRes.value,
    fieldStr.value,
  )
}

function onClose() {
  emit('close', false)
}

function getName(row: any) {
  if (bulkField.value?.field_name) {
    bulkSetting.value[bulkField.value?.field_name]
      = row[bulkField.value?.labelField || 'name']
  }
}

function changeField(val: ItemProps) {
  bulkField.value = val
  selectValue.value = val.title
}

function changeSelectValue(val: any, field: string) {
  selectRes.value = val
  fieldStr.value = field
  getName(val)
  emit('update:otherValue', val?.id)
}
</script>

<template>
  <vxe-modal
    v-model="show"
    title="批量设置"
    show-footer
    width="600px"
    @close="onClose"
  >
    <div class="pt-[10px] pr-[30px]">
      <BulkItem title="请选择字段">
        <el-select
          v-model="selectValue"
          placeholder="请选择字段"
          style="width: 100%"
          filterable
          @change="changeField"
        >
          <template v-for="item in props.columnList" :key="item?.field">
            <el-option :label="item?.title" :value="item" />
          </template>
        </el-select>
      </BulkItem>
      <template v-for="(item, index) in newColumnList">
        <template v-if="slots[item?.field]">
          <BulkItem
            v-if="!item?.hideTitle && item?.field === bulkField?.field"
            :key="item.field"
            :title="item?.title"
          >
            <slot
              :label="item.title"
              :name="item?.field"
              :row="item"
              :index="index"
            />
          </BulkItem>
          <slot
            v-if="item?.field === bulkField?.field && item?.hideTitle"
            :label="item.title"
            :name="item?.field"
            :row="item"
            :index="index"
          />
        </template>
        <template v-else>
          <BulkItem
            v-if="item?.field === bulkField?.field"
            :key="item.field"
            :title="item?.title"
          >
            <vxe-input
              v-if="item.component === 'input'"
              v-model="bulkSetting[item?.field]"
              :maxlength="item?.maxlength || 200"
              clearable
              :digits="item?.digits || 2"
              :type="item?.type"
              style="width: 100%"
            />
            <SelectComponents
              v-if="item.component === 'select'"
              v-model="bulkSetting[item?.field]"
              :api="item?.api"
              :query="item?.query"
              :label-field="item.labelField ? item.labelField : 'name'"
              :value-field="item.valueField ? item.valueField : 'id'"
              style="width: 100%"
              :multiple="item?.multiple || false"
              @change-value="(val) => changeSelectValue(val, item.field)"
            />
            <!--          <SelectDialog -->
            <!--            v-if="item.component === 'select'" -->
            <!--            :label-field="item.labelField ? item.labelField : 'name'" -->
            <!--            :multiple="item?.multiple || false" -->
            <!--            style="width: 250px" -->
            <!--            :value-field="item.valueField ? item.valueField : 'id'" -->
            <!--            v-model="bulkSetting[item?.field]" -->
            <!--            :query="item?.query" -->
            <!--            @changeValue="val => changeSelectValue(val, item.field)" -->
            <!--            :api="item?.api" -->
            <!--          /> -->
            <el-checkbox
              v-if="item.component === 'checkbox'"
              v-model="bulkSetting[item?.field]"
              style="width: 250px"
            />
            <SelectDate
              v-if="item.component === 'selectDate'"
              v-model="bulkSetting[item?.field]"
              :type="item?.type"
              style="width: 100%"
            />
            <vxe-input
              v-if="item.component === 'selectOneDate'"
              v-model="bulkSetting[item?.field]"
              type="date"
              transfer
            />
          </BulkItem>
        </template>
        <template v-if="item?.quickInput">
          <BulkItem
            v-if="item?.field === bulkField?.field && item.component === 'input'"
            :key="item.field"
            title="快速录入"
          >
            <el-input
              v-model="textarea"
              :rows="10"
              type="textarea"
              placeholder="文本使用回车隔开，可粘贴自 Excel 列数据"
            />
          </BulkItem>
        </template>
      </template>
    </div>

    <template #footer>
      <el-button size="small" @click="onClose">
        取消
      </el-button>
      <el-button size="small" type="primary" @click="bulkHandSave">
        确定
      </el-button>
    </template>
  </vxe-modal>
</template>
