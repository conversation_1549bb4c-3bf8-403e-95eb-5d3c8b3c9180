<template>
  <vxe-modal show-footer v-model="state.showModal" :title="state.modalName" width="80vw" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem v-if="props.api !== 1" label="坯布采购单号:">
          <template v-slot:content>
            <el-input v-model="state.filterData.order_code"></el-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布编号:">
          <template v-slot:content>
            <el-input v-model="state.filterData.code"></el-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="坯布名称:">
          <template v-slot:content>
            <el-input v-model="state.filterData.name"></el-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="props.api === 1" label="坯布幅宽:">
          <template v-slot:content>
            <el-input v-model="state.filterData.grey_fabric_width"></el-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem v-if="props.api === 1" label="坯布克重:">
          <template v-slot:content>
            <el-input v-model="state.filterData.grey_fabric_gram_weight"></el-input>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex flex-col h-full overflow-hidden">
        <Table :config="tableConfig" :tableList="props.api === 1 ? data1?.list : data2?.list" :column-list="props.api === 1 ? columnList_infomation : columnList">
          <template #finish_product_width="{ row }">{{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}</template>
          <template #finish_product_gram_weight="{ row }">
            {{ row.finish_product_gram_weight }}
            {{ row.finish_product_gram_weight_unit_name }}
          </template>
          <template #grey_fabric_width="{ row }">{{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}</template>
          <template #grey_fabric_gram_weight="{ row }">
            {{ row.grey_fabric_gram_weight }}
            {{ row.grey_fabric_gram_weight_unit_name }}
          </template>
        </Table>
      </div>
    </div>

    <template #footer>
      <el-button @click="handCancel">取消</el-button>
      <el-button type="primary" @click="handleSure">确认</el-button>
    </template>
  </vxe-modal>
</template>

<script setup lang="ts">
import { reactive, ref, toRaw, watch } from 'vue'
import Table from '@/components/Table.vue'
import { GetGreyFabricInfoListUseByOthers, GetPurchaseGreyFabricItemList } from '@/api/greyFabricPurchase'
import { getFilterData } from '@/common/util'
import { ElMessage } from 'element-plus'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

export interface Props {
  api: number
}

const emits = defineEmits(['handleSure'])

const props = withDefaults(defineProps<Props>(), {
  api: -1,
})

const state = reactive({
  filterData: {
    code: '',
    name: '',
    order_code: '',
    grey_fabric_width: '',
    grey_fabric_gram_weight: '',
  },
  showModal: false,
  modalName: '添加坯布',
  multipleSelection: [],
  apiString: '',
  queryInfo: {
    recipien_entity_id: '',
    supplier_id: '',
  },
  // rowIndex: -1,
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = GetGreyFabricInfoListUseByOthers()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  size: size2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = GetPurchaseGreyFabricItemList()

watch(
  () => state.showModal,
  () => {
    if (state.showModal) {
      getData()
    }
  }
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  }
)

const tableConfig = reactive<any>({
  loading: props.api === 1 ? loading1 : loading2,
  showPagition: true,
  showSlotNums: true,
  height: '100%',
  page: props.api === 1 ? page1 : page2,
  size: props.api === 1 ? size1 : size2,
  total: props.api === 1 ? total1 : total2,
  showCheckBox: true,
  showSort: false,
  handleSizeChange: (val: number) => (props.api === 1 ? handleSizeChange1(val) : handleSizeChange2(val)),
  handleCurrentChange: (val: number) => (props.api === 1 ? handleCurrentChange1(val) : handleCurrentChange2(val)),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const getData = () => {
  const query = {
    ...state.filterData,
    ...state.queryInfo,
  }

  props.api === 1 ? fetchData1(getFilterData(state.filterData)) : fetchData2(getFilterData(query))
  tableConfig.total = props.api === 1 ? total1 : total2
  tableConfig.page = props.api === 1 ? page1 : page2
  tableConfig.size = props.api === 1 ? size1 : size2
}

const columnList = ref([
  {
    field: 'order_code',
    title: '坯布采购单号',
    minWidth: 140,
  },
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    minWidth: 150,
  },
  {
    field: 'code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'number',
    title: '匹数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'total_weight',
    title: '数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'total_wait_collect_weight',
    title: '待收总数量',
    isWeight: true,
    minWidth: 100,
  },
  // {
  //   field: '',
  //   title: '备注',
  // },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 140,
  },
])

const columnList_infomation = ref([
  {
    field: 'code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'full_name',
    title: '坯布全称',
    minWidth: 100,
  },
  {
    field: 'type_grey_fabric_name',
    title: '布种类型',
    minWidth: 100,
  },
  {
    field: 'type_grey_fabric_order_name',
    title: '坯布订单类型',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_composition',
    title: '坯布成分',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    // isWeight: true,
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'total_needle_size',
    title: '总针数',
    minWidth: 100,
  },
  {
    field: 'loom_model_name',
    title: '坯织机型',
    minWidth: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '坯织颜色',
    minWidth: 100,
  },
  // {
  //   field: '',
  //   title: '坯织规格',
  // },
  {
    field: 'weaving_loss',
    title: '织造损耗',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'yarn_length',
    title: '纱长',
    minWidth: 100,
  },
  // {
  //   field: 'remark',
  //   title: '备注',
  //   minWidth: 100,
  // },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 100,
  },
])

const handAllSelect = ({ records }: any) => {
  state.multipleSelection = records
}

const handleSelectionChange = ({ records }: any) => {
  state.multipleSelection = records
}

const handCancel = () => {
  state.showModal = false
}

const handleSure = () => {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择一条数据')
  } else {
    emits('handleSure', toRaw(state.multipleSelection))
  }
}

defineExpose({
  state,
})
</script>

<style></style>
