import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 获取列表
export function price_list() {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/price_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export function price_listExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/dnf_quote/finishing/price_list',
    method: 'get',
    nameFile,
  })
}

// 获取items
export function detail_items() {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/detail_items',
    method: 'get',
  })
}

// 获取items
export function priceBy_items() {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/price_by_items',
    method: 'get',
  })
}

// 列表枚举
export function price_listenum() {
  return useRequest({
    url: '/admin/v1/dnf_quote/finishing/price_list_enum',
    method: 'get',
  })
}
