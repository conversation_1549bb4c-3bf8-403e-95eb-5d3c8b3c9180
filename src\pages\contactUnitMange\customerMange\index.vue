<script lang="ts" setup name="CustomerMange">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  Business_unitcustomerDelete,
  Business_unitcustomerDisable,
  Business_unitcustomerEnable,
  Business_unitcustomerlist,
} from '@/api/customerMange'
import { debounce, deleteToast, deleteToastWithRiskWarning2, getFilterData, resetData } from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import CascaderAddress from '@/components/CascaderAddress/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    code: '',
    name: '',
    status: '',
    sale_system_id: '',
    location: [],
    unit_type_id: '',
    phone: '',
    remark: '',
  },
  multipleSelection: [],
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = Business_unitcustomerlist()

const tableConfig = ref({
  height: '100%',
  loading,
  showPagition: true,
  page,
  size,
  total,
  fieldApiKey: 'CustomerManage',
  showSlotNums: true,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

const getData = debounce(() => {
  const query = {
    ...state.filterData,
    location: state.filterData.location ? state.filterData.location.join(',') : '',
  }
  ApiCustomerList(getFilterData(query))
  state.multipleSelection = []
}, 400)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const columnList = reactive<any>([
  {
    sortable: true,
    field: 'code',
    title: '编号',
    minWidth: 100,
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'name',
    title: '客户名称',
    minWidth: 100,
    fixed: 'left',
  },
  {
    sortable: true,
    field: 'full_name',
    title: '客户全称',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'full_name',
    title: '企微客户',
    minWidth: 150,
    soltName: 'qywxCustomer',
  },
  {
    sortable: true,
    field: 'full_name',
    title: '企微群',
    minWidth: 150,
    soltName: 'qywxGroupChat',
  },
  {
    sortable: true,
    field: 'contact_name',
    title: '联系人',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'location',
    title: '地区',
    minWidth: 250,
    soltName: 'location',
  },
  {
    sortable: true,
    field: 'address',
    title: '地址',
    minWidth: 250,
  },
  {
    sortable: true,
    field: 'sale_system_names',
    title: '营销体系',
    width: 120,
  },
  {
    sortable: true,
    field: 'main_unit_type_name',
    title: '主要客户类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'unit_type_name',
    title: '客户类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'phone',
    title: '联系电话',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'credit_code',
    title: '社会统一信用代码',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    minWidth: 130,
    isDate: true,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    showStatus: true,
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const router = useRouter()

function handleAdd() {
  router.push({ name: 'CustomerAdd' })
}

function handDetail(row: any) {
  router.push({ name: 'CustomerDetail', query: { id: row?.id } })
}

function handEdit(row: any) {
  router.push({ name: 'CustomerEdit', query: { id: row?.id } })
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

// 删除数据
const {
  fetchData: deleteFetch,
  success: deleteSuccess,
  msg: deleteMsg,
} = Business_unitcustomerDelete()

async function handDelete(row: any) {
  const res = await deleteToastWithRiskWarning2(row.name)
  if (res) {
    await deleteFetch({ id: row.id.toString() })
    if (deleteSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteMsg.value)
    }
  }
}

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    await deleteFetch({ id: ids.toString() })
    if (deleteSuccess.value) {
      ElMessage.success('成功')
      getData()
      state.multipleSelection = []
    }
    else {
      ElMessage.error(deleteMsg.value)
    }
  }
}

// 启用、禁用数据

const {
  fetchData: openFetch,
  msg: openMsg,
  success: openSuccess,
} = Business_unitcustomerEnable()

const {
  fetchData: closeFetch,
  msg: closeMsg,
  success: closeSuccess,
} = Business_unitcustomerDisable()

async function handStatus(row: any) {
  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    row.status === 1
      ? await closeFetch({ id: row.id.toString() })
      : await openFetch({ id: row.id.toString() })
    if (row.status === 1 ? closeSuccess.value : openSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(row.status === 1 ? closeMsg.value : openMsg.value)
    }
  }
}

// 批量修改状态

async function handAllEnabled(val: number) {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    val === 2
      ? await closeFetch({ id: ids.toString() })
      : await openFetch({ id: ids.toString() })
    if (val === 2 ? closeSuccess.value : openSuccess.value) {
      ElMessage.success('成功')
      getData()
      state.multipleSelection = []
    }
    else {
      ElMessage.error(val === 2 ? closeMsg.value : openMsg.value)
    }
  }
}

const tablesRef = ref()
// 导出
const loadingExcel = ref(false)
async function handleExport() {
  tablesRef.value.exportSelectEvent()
  // if (!data?.value.list || data?.value.list.length <= 0) return ElMessage.warning('当前无数据可导出')
  // const name_str = '客户管理'
  // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = Business_unitcustomerExport({ nameFile: name_str })
  // loadingExcel.value = true
  // await getFetch({
  //   ...getFilterData(state.filterData),
  //   download: 1,
  // })
  // if (getSuccess.value) {
  //   ElMessage({
  //     type: 'success',
  //     message: '成功',
  //   })
  // } else {
  //   ElMessage({
  //     type: 'error',
  //     message: getMsg.value,
  //   })
  // }
  // loadingExcel.value = false
}

onActivated(() => {
  getData()
})
onMounted(getData)
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <el-input
              v-model="state.filterData.name"
              clearable
              placeholder="请输入"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="地区:" width="280">
          <template #content>
            <CascaderAddress
              v-model="state.filterData.location" :cascader-props="{
                checkStrictly: true,
              }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户类型:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.unit_type_id"
              api="AdminuenumsupplierType"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              style="width: 300px"
              api="StatusListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="手机号:">
          <template #content>
            <vxe-input v-model="state.filterData.phone" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:">
          <template #content>
            <vxe-textarea
              v-model="state.filterData.remark"
              clearable
              resize="both"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <FildCard title="" class="flex flex-col flex-1 table-card-full">
      <template #right-top>
        <el-button
          v-has="'CustomerMange_add'"
          style="margin-right: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button class="mr-[10px]">批量操作</el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handAllDelete">
                批量删除
              </el-dropdown-item>
              <el-dropdown-item @click="handAllEnabled(1)">
                批量启用
              </el-dropdown-item>
              <el-dropdown-item @click="handAllEnabled(2)">
                批量禁用
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <BottonExcel
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <div class="flex-1 overflow-hidden">
        <Table
          ref="tablesRef"
          :config="tableConfig"
          :table-list="data?.list"
          :column-list="columnList"
        >
          <template #location="{ row }">
            {{ row.location ? row.location.split(",").join(" ") : "" }}
          </template>
          <template #qywxCustomer="{ row }">
            <el-tag v-for="item in row.qywx_customers" :key="item.id" type="success">
              {{ item.name }}
            </el-tag>
          </template>
          <template #qywxGroupChat="{ row }">
            <el-tag v-for="item in row.qywx_groups" :key="item.id" type="success">
              {{ item.name }}
            </el-tag>
          </template>
          <template #operate="{ row }">
            <el-space size="10">
              <el-link v-has="'CustomerMange_detail'" :underline="false" type="primary" @click="handDetail(row)">
                查看
              </el-link>
              <el-link v-has="'CustomerMange_edit'" :underline="false" type="primary" @click="handEdit(row)">
                编辑
              </el-link>
              <el-link v-has="'CustomerMange_del'" :underline="false" type="primary" @click.stop="handDelete(row)">
                删除
              </el-link>
              <el-link v-if="row.status === 2" v-has="'CustomerMange_status'" :underline="false" type="primary" @click="handStatus(row)">
                启用
              </el-link>

              <el-link v-else v-has="'CustomerMange_status'" :underline="false" type="primary" @click="handStatus(row)">
                禁用
              </el-link>
            </el-space>
          </template>
        </Table>
      </div>
    </FildCard>
  </div>
</template>

<style></style>
