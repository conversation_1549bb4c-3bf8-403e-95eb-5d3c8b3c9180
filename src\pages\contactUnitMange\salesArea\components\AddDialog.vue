<script setup lang="ts">
import { reactive, ref } from 'vue'

const emits = defineEmits(['handleSure'])

const state = reactive({
  showModal: false,
  modalName: '',
  form: {
    saleAreaCode: '',
    saleAreaName: '',
    parentId: 0,
    id: 0,
  },
  fromRules: {
    saleAreaName: [{ required: true, message: '请填写区域名称', trigger: 'blur' }],
  },
})

function handCancel() {
  state.showModal = false
}

const ruleFormRef = ref()
async function handleSure() {
  if (!ruleFormRef.value)
    return
  await ruleFormRef.value.validate((valid: any) => {
    if (valid)
      emits('handleSure', state.form)
  })
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="400" height="210" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form ref="ruleFormRef" size="default" :model="state.form" label-width="140px" label-position="left" :rules="state.fromRules">
      <el-form-item label="区域编号" prop="saleAreaCode">
        <el-input v-model="state.form.saleAreaCode" clearable placeholder="若不输入则自动生成" />
      </el-form-item>
      <el-form-item label="区域名称" prop="saleAreaName">
        <el-input v-model="state.form.saleAreaName" clearable placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
