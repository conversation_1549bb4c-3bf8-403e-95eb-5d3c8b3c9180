/*
 * @Description: 地址转换处理
 */
export interface Region {
  ad_code: string
  name: string
  children?: Region[]
}

export interface AddressInfo {
  province: Region | null
  city: Region | null
  district: Region | null
  detail: string
}

export interface ParsedAddressResult {
  name: string
  phone: string
  address: AddressInfo
}

// 定义一个函数用于将一维数组转换为嵌套结构，剔除parent_id==0的数据
function convertToTree(data: any[]) {
  // 过滤掉 parent_id 为 0 的数据
  const filteredData = data.filter(item => item.parent_id !== 0)
  const map: { [key: number]: any } = {}
  const roots: any[] = []

  // 首先将所有节点存储到 map 中
  filteredData.forEach((item) => {
    map[item.id] = { ...item, children: [] }
  })

  // 遍历节点，构建父子关系
  filteredData.forEach((item) => {
    const parent = map[item.parent_id]
    if (parent) {
      // 将节点添加到父节点的 children 中
      parent.children.push(map[item.id])
    }
    else {
      // 如果找不到父节点，说明该节点是根节点
      roots.push(map[item.id])
    }
  })

  return roots
}

/**
 * 增强版地址解析方法
 * @param text 输入的字符串，支持多种格式
 * @param regionData 省市区数据传入一位数组
 * @returns 解析结果对象
 */
export function parseAddress(
  text: string,
  regionDataFlat: any[],
): ParsedAddressResult {
  const regionData = convertToTree(regionDataFlat)
  const result: ParsedAddressResult = {
    name: '',
    phone: '',
    address: {
      province: null,
      city: null,
      district: null,
      detail: '',
    },
  }

  if (!text.trim())
    return result

  // 预处理：统一替换中文标点为英文标点，去除多余空格
  const processedText = text
    .replace(/，/g, ',')
    .replace(/：/g, ':')
    .replace(/　/g, ' ')
    .replace(/\s+/g, ' ')
    .trim()

  // 1. 提取手机号（支持多种格式）
  extractPhone(processedText, result)

  // 2. 提取姓名（支持多种格式）
  extractName(processedText, result)

  // 3. 提取地址部分（去除已识别的姓名和手机号）
  const addressPart = cleanTextForAddress(processedText, result)

  // 4. 解析省市区信息
  parseRegionInfo(addressPart, regionData, result.address)

  // 5. 提取详细地址（去除已匹配的省市区）
  extractDetailAddress(addressPart, result.address)

  return result
}

/**
 * 提取手机号（支持多种格式）
 */
function extractPhone(text: string, result: ParsedAddressResult) {
  // 支持格式：13800138000、138-0013-8000、138 0013 8000
  const phoneRegex = /(1[3-9]\d[\s-]?\d{4}[\s-]?\d{4})/
  const phoneMatch = text.match(phoneRegex)
  if (phoneMatch)
    result.phone = phoneMatch[1].replace(/[\s-]/g, '')
}

/**
 * 提取姓名（支持多种格式）
 */
function extractName(text: string, result: ParsedAddressResult) {
  // 格式1："姓名:李四" 或 "姓名：李四"
  const nameLabelRegex = /(?:姓名\s*[:：]\s*)([\u4E00-\u9FA5]{2,4})/
  const nameLabelMatch = text.match(nameLabelRegex)
  if (nameLabelMatch) {
    result.name = nameLabelMatch[1]
    return
  }

  // 格式2："name:李四" 或 "name：李四"
  const nameEnglishRegex = /(?:name\s*[:：]\s*)([\u4E00-\u9FA5]{2,4})/i
  const nameEnglishMatch = text.match(nameEnglishRegex)
  if (nameEnglishMatch) {
    result.name = nameEnglishMatch[1]
    return
  }

  // 格式3：开头的中文名（2-4个字）
  const nameStartRegex = /^([\u4E00-\u9FA5]{2,4})/
  const nameStartMatch = text.match(nameStartRegex)
  if (nameStartMatch) {
    result.name = nameStartMatch[1]
    return
  }

  // 格式4：手机号前后的中文名
  if (result.phone) {
    const aroundPhoneRegex = new RegExp(
      `([\u4E00-\u9FA5]{2,4})\\s*${result.phone}|${result.phone}\\s*([\u4E00-\u9FA5]{2,4})`,
    )
    const aroundPhoneMatch = text.match(aroundPhoneRegex)
    if (aroundPhoneMatch)
      result.name = aroundPhoneMatch[1] || aroundPhoneMatch[2]
  }
}

/**
 * 清理文本用于地址提取（增强版，确保去除所有标签）
 */
function cleanTextForAddress(text: string, result: ParsedAddressResult): string {
  // 创建要移除的模式数组
  const patternsToRemove = []

  // 添加姓名相关模式
  if (result.name) {
    patternsToRemove.push(result.name)
    patternsToRemove.push(`姓名\\s*[:：]\\s*${result.name}`)
    patternsToRemove.push(`name\\s*[:：]\\s*${result.name}`)
  }

  // 添加手机号相关模式
  if (result.phone) {
    patternsToRemove.push(result.phone)
    const formattedPhone = result.phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1[- ]$2[- ]$3')
    patternsToRemove.push(formattedPhone)
    patternsToRemove.push(`手机\\s*[:：]\\s*${formattedPhone}`)
    patternsToRemove.push(`电话\\s*[:：]\\s*${formattedPhone}`)
    patternsToRemove.push(`手机号\\s*[:：]\\s*${formattedPhone}`)
    patternsToRemove.push(`电话号\\s*[:：]\\s*${formattedPhone}`)
    patternsToRemove.push(`手机\\s*[:：]`)
    patternsToRemove.push(`电话\\s*[:：]`)
  }

  // 添加地址标签模式（不包含具体地址内容）
  patternsToRemove.push(`地址\\s*[:：]`)
  patternsToRemove.push(`收货地址\\s*[:：]`)
  patternsToRemove.push(`详细地址\\s*[:：]`)
  patternsToRemove.push(`address\\s*[:：]`)

  // 构建正则表达式
  const cleanRegex = new RegExp(
    `(${patternsToRemove.join('|')}|[,\\s:：]+)`,
    'gi', // 不区分大小写
  )

  return text
    .replace(cleanRegex, ' ')
    .replace(/\s+/g, ' ')
    .trim()
}

/**
 * 解析省市区信息
 */
function parseRegionInfo(
  addressStr: string,
  regionData: Region[],
  addressInfo: AddressInfo,
) {
  // 尝试匹配省份
  for (const province of regionData) {
    if (addressStr.includes(province.name)) {
      addressInfo.province = province

      // 尝试匹配城市
      if (province.children) {
        for (const city of province.children) {
          if (addressStr.includes(city.name)) {
            addressInfo.city = city

            // 尝试匹配区县
            if (city.children) {
              for (const district of city.children) {
                if (addressStr.includes(district.name)) {
                  addressInfo.district = district
                  break
                }
              }
            }
            break
          }
        }
      }
      break
    }
  }
}

/**
 * 提取详细地址（增强版，确保去除所有残留标签）
 */
function extractDetailAddress(addressStr: string, addressInfo: AddressInfo) {
  let detail = addressStr

  const removeMatched = (name: string | undefined) => {
    if (name) {
      // 处理可能包含"市"、"区"等后缀的情况
      detail = detail.replace(name, '')
      if (name.endsWith('市') || name.endsWith('区') || name.endsWith('县')) {
        const shortName = name.slice(0, -1)
        detail = detail.replace(new RegExp(shortName, 'g'), '')
      }
    }
  }

  removeMatched(addressInfo.province?.name)
  removeMatched(addressInfo.city?.name)
  removeMatched(addressInfo.district?.name)

  // 清理多余空格和标点
  addressInfo.detail = detail
    .replace(/^[,\s:：]+/, '')
    .replace(/[,\s:：]+$/, '')
    .replace(/\s+/g, ' ')
    .trim()
}
