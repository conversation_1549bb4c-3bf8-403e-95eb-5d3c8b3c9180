<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import YarnInformationDialog from '../../components/YarnInformationDialog.vue'
import { autoSelectYarn } from '../autoSelectUtils'
import Table from '@/components/Table.vue'
import { deepClone } from '@/common/util'
import { list_enum } from '@/api/greyFabricPurchaseReturn'
import { formatHashTag, formatWeightDiv } from '@/common/format'

const emit = defineEmits<{
  (e: 'handleSure', data: any[]): void
  (e: 'handleChange', params: { val: any, row: any, type: number }): void
  (e: 'handleDelete', params: { row: any, rowIndex: number }): void
  (e: 'handleYarnInfoSure', params: { row: any, rowIndex: number }): void
}>()

const state = reactive({
  showModal: false,
  data: {},
  isEdit: true,
  grayId: 0,
  grayIndex: 0,
  unitId: 0,
  rowIndex: 0,
  itemIndex: 0,
  tableData: [],
})
const YarnInformationDialogRef = ref()

// 表格配置
const tableConfig = computed(() => ({
  showSlotNums: true,
  showOperate: state.isEdit,
  operateWidth: '80',
  height: '100%',
  footerMethod,
}))

// 列定义
const columnList = ref([
  {
    field: 'unit_name',
    title: '织厂名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  // {
  //   field: 'customer_name',
  //   title: '所属客户',
  //   minWidth: 150,
  // },
  {
    field: 'brand',
    title: '纱牌',
    minWidth: 100,
  },
  {
    field: 'batch_num',
    title: '纱批',
    minWidth: 100,
  },
  // {
  //   field: 'raw_material_code',
  //   title: '原料编号',
  //   minWidth: 100,
  // },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 140,
    soltName: 'raw_material_name',
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 100,
  },
  // {
  //   field: 'measurement_unit_name',
  //   title: '单位',
  //   minWidth: 150,
  // },
  // {
  //   field: 'level_name',
  //   title: '原料等级',
  //   minWidth: 150,
  // },
  // {
  //   field: 'raw_remark',
  //   title: '原料备注',
  //   minWidth: 150,
  // },
  // {
  //   field: 'production_date',
  //   title: '生产日期',
  //   minWidth: 150,
  //   is_date: true,
  // },
  // {
  //   field: 'spinning_type',
  //   title: '纺纱类型',
  //   minWidth: 150,
  // },
  // {
  //   field: 'cotton_origin',
  //   title: '棉花产地',
  //   minWidth: 150,
  // },
  // {
  //   field: 'carton_num',
  //   title: '装箱单号',
  //   minWidth: 150,
  // },
  // {
  //   field: 'fapiao_num',
  //   title: '发票号',
  //   minWidth: 150,
  // },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 100,
    soltName: 'use_yarn_quantity',
  },
  // {
  //   field: 'remark',
  //   title: '备注',
  //   minWidth: 100,
  //   soltName: 'remark',
  // },
])

// 表格底部汇总方法
function footerMethod({ columns, data }) {
  return [
    columns.map((column, _columnIndex) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${sumNum(data, 'use_yarn_quantity')}`

      return null
    }),
  ]
}

// 辅助函数：计算数值之和
function sumNum(data, field) {
  if (!data || data.length === 0)
    return 0
  return data.reduce((sum, item) => {
    const value = Number(item[field] || 0)
    return sum + value
  }, 0).toFixed(3)
}

// 处理输入变化
// function handChange(val, row, type) {
//   emit('handleChange', { val, row, type })
// }

// 处理删除行
function handDeleteTotalItems(row, rowIndex) {
  state.tableData.splice(rowIndex, 1)
  emit('handleDelete', { row, rowIndex })
}

// 取消按钮
function handCancel() {
  state.showModal = false
}

// 确认按钮
function handleSure() {
  emit('handleSure', state.tableData)
  state.showModal = false
}

// 暴露方法给父组件
defineExpose({
  state,
})
function handleSelect() {
  const arr = deepClone(state.data.use_yarn_item_data)
  YarnInformationDialogRef.value.state.grayIndex = state.grayIndex
  YarnInformationDialogRef.value.state.grayId = state.grayId
  YarnInformationDialogRef.value.state.showModal = true
  YarnInformationDialogRef.value.state.filterData.raw_material_id = Number(state.data.raw_material_id)
  YarnInformationDialogRef.value.state.filterData.unit_id = state.unitId
  // YarnInformationDialogRef.value.state.filterData.brand = row.raw_material_brand
  // YarnInformationDialogRef.value.state.filterData.batch_num = row.raw_material_batch_number
  // YarnInformationDialogRef.value.state.filterData.color_scheme = row.color_scheme
  YarnInformationDialogRef.value.state.list = arr
  YarnInformationDialogRef.value.state.parent_id = state.data.id
  YarnInformationDialogRef.value.state.rowIndex = state.rowIndex
  YarnInformationDialogRef.value.state.use_weight = state.data.use_yarn_quantity
}
const { fetchData, success, msg, data } = list_enum()

// 自动选择
async function handleAutoSelect() {
  let originList = []
  await fetchData({ raw_material_id: Number(state.data.raw_material_id), unit_id: state.unitId, is_stock_type_param: true, is_has_stock: true })

  if (!success.value)
    return ElMessage.error(msg.value)
  originList = data.value.list
  // 使用抽离的工具函数
  const selectList = await autoSelectYarn({
    originList,
    totalWeight: state.data.use_yarn_quantity,
    parentId: state.parent_id,
    grayId: state.grayId,
  })
  state.tableData = selectList
  emit('handleYarnInfoSure', {
    grayId: state.grayId,
    rowIndex: state.rowIndex,
    itemIndex: state.itemIndex,
    list: selectList,
  })
}
function handAddYarn(info: any) {
  YarnInformationDialogRef.value.state.showModal = false
  emit('handleYarnInfoSure', info)
  state.tableData = info.list
}
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="用纱情况选择" width="80vw" height="70vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div v-if="state.isEdit" class="flex items-center">
        请选择用纱（{{ state.data.use_yarn_quantity }} {{ state.data.unit_name }}）
        <el-space>
          <el-link type="primary" :underline="false" @click="handleSelect">
            选择
          </el-link>
          <el-link type="primary" :underline="false" @click="handleAutoSelect">
            自动选择
          </el-link>
        </el-space>
      </div>
      <div class="flex flex-col h-full overflow-hidden">
        <Table :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
          <template #raw_material_name="{ row }">
            {{ formatHashTag(row.raw_material_code, row.raw_material_name) }}
          </template>
          <template #use_yarn_quantity="{ row }">
            {{ row.use_yarn_quantity }} {{ row.measurement_unit_name }}
            <!-- <VxeInput v-model="row.use_yarn_quantity" type="float" min="0" placeholder="请输入" @blur="val => handChange(val, row, 1)" /> -->
          </template>
          <!-- <template #remark="{ row }">
            <VxeInput v-model="row.remark" placeholder="请输入" @blur="val => handChange(val, row, 2)" />
          </template> -->
          <template #operate="{ row, rowIndex }">
            <el-button text type="danger" @click="handDeleteTotalItems(row, rowIndex)">
              删除
            </el-button>
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
  <YarnInformationDialog ref="YarnInformationDialogRef" @handle-sure="handAddYarn" />
</template>

<style scoped>

</style>
