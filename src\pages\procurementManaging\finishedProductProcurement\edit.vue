<script lang="ts" setup name="FinishedProductProcurementEdit">
import { Plus } from '@element-plus/icons-vue'
import Big from 'big.js'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import SelectRawMaterial from './components/SelectRawMaterial/index.vue'
import { FinishProductDetail, FinishProductEdit } from '@/api/finishedProductProcurement'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  formatLengthDiv,
  formatLengthMul,
  formatPriceDiv,
  formatPriceMul,
  formatTwoDecimalsDiv,
  formatTwoDecimalsMul,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { deepClone, getFilterData } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
const router = useRoute()
const state = reactive({
  form: {
    sale_system_id: '',
    supplier_id: '',
    receipt_unit_id: '',
    purchase_date: dayjs().format('YYYY-MM-DD'),
    receipt_date: '',
    fapiao_title: '',
    remark: '',
    sale_system_name: '',
    supplier_name: '',
    receipt_unit_name: '',
    receipt_address: '',
    receipt_phone: '',
    items: [] as any[],
    fapiao_id: '',
    tax_rate: '', // 税率
    sale_mode: '', // 订单类型
  },
  tableData: [] as any[],
  fromRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
    receipt_unit_id: [{ required: true, message: '请选择收货单位', trigger: 'change' }],
    purchase_date: [{ required: true, message: '请选择采购日期', trigger: 'change' }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
    // receipt_phone: [{ validator: formValidatePass.phone('收货电话'), trigger: 'change' }],
  },
})
const checkbox = ref(true)

const { fetchData: detailFetch, data: detalData, success, msg } = FinishProductDetail()

onMounted(async () => {
  getData()
})

async function getData() {
  await detailFetch({
    id: router.params.id,
  })
  if (!success.value)
    return ElMessage.error(msg.value)

  // supplierIdRef.value.inputLabel = detalData.value.supplier_name
  state.form.sale_system_id = detalData.value.sale_system_id
  state.form.supplier_id = detalData.value.supplier_id
  state.form.receipt_unit_id = detalData.value.receipt_unit_id
  state.form.purchase_date = detalData.value.purchase_date
  state.form.receipt_date = detalData.value.receipt_date
  state.form.fapiao_title = detalData.value.fapiao_title
  state.form.remark = detalData.value.remark
  state.form.sale_system_name = detalData.value.sale_system_name
  state.form.supplier_name = detalData.value.supplier_name
  state.form.receipt_unit_name = detalData.value.receipt_unit_name
  state.form.receipt_address = detalData.value.receipt_Address
  state.form.receipt_phone = detalData.value.receipt_Phone
  state.form.fapiao_id = detalData.value.fapiao_id || null
  state.form.tax_rate = formatPriceDiv(detalData.value.tax_rate) || 0
  checkbox.value = detalData.value.include_tax
  state.form.order_type = detalData.value.order_type
  state.form.sale_mode = detalData.value.sale_mode
  state.tableData = detalData.value?.items?.map((item: any, index: number) => {
    const temp = {
      ...item,
      uniqueId: `${index}${Date.now() + Math.ceil(Math.random())}`, // 为复制项生成新的唯一id
      piece_weight: formatWeightDiv(item.piece_weight),
      unit_price: formatUnitPriceDiv(item.unit_price),
      upper_limit: formatPriceDiv(item.upper_limit),
      lower_limit: formatPriceDiv(item.lower_limit),
      length: formatLengthDiv(item.length),
      length_unit_price: formatUnitPriceDiv(item.length_unit_price),
      paper_tube_weight: formatWeightDiv(item.paper_tube_weight),
      piece_count: formatTwoDecimalsDiv(item.piece_count),
      total_weight: formatWeightDiv(item.total_weight),
      total_price: formatPriceDiv(item.total_price),
      color_id: item.color_id,
      finish_product_level_id: item.finish_product_level_id || '',
    }
    return conductUnitPrice(temp, true)
  })
}

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})
const multipleSelection = ref<any[]>([])
const bulkShow = ref(false)

const showAdd = ref(false)

function handAdd() {
  showAdd.value = true
}
function handEdit() {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请选择批量修改的数据')
  bulkShow.value = true
}

const columnList = ref([
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    fixed: 'left',
    width: 150,
  },
  {
    field: 'finish_product_code',
    title: '成品编号',
    fixed: 'left',
    width: 110,
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    fixed: 'left',
    width: 100,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    width: 130,
    soltName: 'customer_id',
    required: true,
  },
  {
    field: 'color_code',
    title: '颜色编号',
    width: 130,
    soltName: 'color_code',
    required: true,
  },
  {
    field: 'color_type_name',
    title: '颜色类别',
    width: 100,
  },
  {
    field: 'color_Name',
    title: '颜色名称',
    width: 100,
    required: true,
  },

  {
    field: 'fabric_type_name',
    title: '布种类型',
    width: 100,
  },

  {
    field: 'dye_factory_color',
    title: '染厂颜色',
    width: 100,
    soltName: 'dye_factory_color',
  },
  {
    field: 'dye_factory_color_num',
    title: '染厂色号',
    width: 100,
    soltName: 'dye_factory_color_num',
  },
  {
    field: 'dye_factory_vat_code',
    title: '染厂缸号',
    width: 100,
    isPrice: true,
    soltName: 'dye_factory_vat_code',
  },
  {
    field: 'finish_product_level',
    title: '成品等级',
    width: 130,
    soltName: 'finish_product_level',
  },
  // {
  //   field: 'dye_craft',
  //   title: '染整工艺',
  //   width: 100,
  // },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    width: 100,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    width: 100,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒重量',
    width: 100,
    soltName: 'paper_tube_weight',
  },
  {
    field: 'finish_product_ingredient',
    title: '成品成分',
    width: 100,
  },
  {
    field: 'finish_product_craft',
    title: '成品工艺',
    width: 100,
  },
  {
    field: 'remark',
    title: '备注',
    width: 200,
    soltName: 'remark',
  },
  {
    field: 'piece_count',
    title: '匹数',
    fixed: 'right',
    width: 90,
    soltName: 'piece_count',
    required: true,
  },
  {
    field: 'piece_weight',
    title: '均重',
    fixed: 'right',
    width: 90,
    soltName: 'piece_weight',
  },
  // {
  //   field: 'unit',
  //   title: '单位',
  //   fixed: 'right',
  //   width: 70,
  // },
  {
    field: 'auxiliary_unit_id',
    title: '结算单位',
    fixed: 'right',
    width: 100,
    soltName: 'auxiliary_unit_id',
  },
  {
    field: 'unit_price',
    title: '采购单价',
    fixed: 'right',
    width: 90,
    soltName: 'unit_price',
    required: true,
  },
  {
    field: 'upper_limit',
    title: '上限',
    width: 70,
    soltName: 'upper_limit',
    fixed: 'right',
  },
  {
    field: 'lower_limit',
    width: 70,
    title: '下限',
    soltName: 'lower_limit',
    fixed: 'right',
  },
  {
    field: 'total_weight',
    title: '采购数量',
    fixed: 'right',
    width: 90,
    soltName: 'total_weight',
    required: true,
  },
  {
    field: 'length',
    title: '辅助数量',
    fixed: 'right',
    width: 90,
    soltName: 'length',
  },
  {
    field: 'total_price',
    title: '采购金额',
    fixed: 'right',
    width: 90,
    soltName: 'total_price',
  },
])

const tablesRef = ref()

function footerMethod({ columns, data }: { columns: any, data: any }) {
  const footerData = [
    columns.map((column: any, _columnIndex: number) => {
      if (['code'].includes(column.field))
        return '合计'

      if (
        [
          'number',
          'whole_piece_count',
          'whole_piece_weight',
          'bulk_piece_count',
          'count_weight',
          'bulk_piece_weight',
          'bulk_count_weight',
          'bulk_weight',
          'count_price',
          'piece_count',
          'piece_weight',
          'total_weight',
          'length',
          'total_price',
        ].includes(column.field)
      )
        return sumNum(data, column.field)
    }),
  ]
  return footerData
}
const saleSaleSystemInfo = ref()

function onSubmit(row: any) {
  showAdd.value = false
  const val = row?.map((item: any, index: number) => {
    const temp = {
      ...item,
      uniqueId: `${index}${state.tableData.length + 1}${Date.now() + Math.ceil(Math.random())}`, // 为复制项生成新的唯一id
      id: 0,
      selected: false,
      paper_tube_weight: formatWeightDiv(item.paper_tube_weight),
      unit: item.measurement_unit_name,
      fabric_type_name: item.type_grey_fabric_name,
      customer_id: saleSaleSystemInfo.value?.default_customer_id || '',
      customer_name: saleSaleSystemInfo.value?.default_customer_name || '',
      finish_product_id: Number.parseInt(item.id),
      piece_weight: formatWeightDiv(item.standard_weight || 0),
      finish_product_level_id: item.finish_product_level_id || '',
    }
    return conductUnitPrice(temp, true)
  })
  state.tableData = [...state.tableData, ...val]
}

const changeRow = ref<any>()
const changeField = ref<string>()
function changeData(row: any, field = '') {
  changeRow.value = row
  changeField.value = field
}

function getColorInfo(row: any, val: any) {
  row.color_code = val.product_color_code
  row.color_type_name = val.type_finished_product_kind_name
  row.color_type_id = val.type_finished_product_kind_id
  row.color_Name = val.product_color_name
}

watch(
  () => [changeRow.value?.piece_count, changeRow.value?.piece_weight, changeRow.value?.unit_price, changeRow.value?.total_weight, changeRow.value?.length, changeRow.value?.length_unit_price],
  async () => {
    computedData(changeRow.value)
  },
  {
    deep: true,
  },
)

const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = FinishProductEdit()
const ruleFormRef = ref()

async function handSubmit() {
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      if (validateList())
        return false
      const tableData = fomatData()
      state.form.items = tableData
      await fetchDataAdd({
        ...getFilterData(
          cloneDeep({
            ...state.form,
            tax_rate: formatPriceMul(state.form.tax_rate),
            sale_mode: state.form.sale_mode,
            purchase_date: formatDate(state.form.purchase_date),
            receipt_date: formatDate(state.form.receipt_date),
            id: Number.parseInt(router.params.id as string),
          }),
        ),
        include_tax: checkbox.value,
      })
      if (successAdd.value) {
        ElMessage.success('编辑成功')
        getData()
        routerList.push({
          name: 'FinishedProductProcurementDetail',
          params: { id: router.params.id },
        })
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

// 整理坯布信息
function fomatData() {
  return state.tableData?.map((item: any) => {
    return {
      ...item,
      finish_product_level_id: item.finish_product_level_id || 0,
      piece_weight: formatWeightMul(item.piece_weight),
      unit_price: formatUnitPriceMul(item.unit_price),
      upper_limit: formatPriceMul(item.upper_limit),
      lower_limit: formatPriceMul(item.lower_limit),
      length: formatLengthMul(item.length),
      length_unit_price: formatUnitPriceMul(item.length_unit_price),
      paper_tube_weight: formatWeightMul(item.paper_tube_weight),
      piece_count: formatTwoDecimalsMul(item.piece_count || 0),
      total_weight: formatWeightMul(item.total_weight),
      total_price: formatPriceMul(item.total_price),
    }
  })
}

// 验证坯布信息字段
function validateList() {
  let msg = ''
  if (!state.tableData || state.tableData.length === 0) {
    msg = '成品信息不能为空'
  }
  else {
    state.tableData?.some((item: any) => {
      if (!item.customer_id) {
        msg = `成品编号为${item.finish_product_code}的数据,所属客户不能为空`
        return true
      }
      if (!item.finish_product_code) {
        msg = `成品编号不能为空`
        return true
      }
      if (!item.color_code) {
        msg = `成品编号为${item.finish_product_code}的数据,颜色编号不能为空`
        return true
      }
      if (!item.color_Name) {
        msg = `成品编号为${item.finish_product_code}的数据,颜色名称不能为空`
        return true
      }
      if (item.piece_count === '') {
        msg = `成品编号为${item.finish_product_code}的数据,匹数不能为空`
        return true
      }
      if (!item.auxiliary_unit_id) {
        msg = `成品编号为${item.finish_product_code}的数据,结算单位不能为空`
        return true
      }
      if (!item.total_weight) {
        msg = `成品编号为${item.finish_product_code}的数据,采购数量不能为空`
        return true
      }
      if (item.unit_price === '') {
        msg = `成品编号为${item.finish_product_code}的数据,采购单价不能为空`
        return true
      }
      // if (!item.length && !isMainUnit(item)) {
      //   msg = `成品编号为${item.finish_product_code}的数据,辅助数量不能为空`
      //   return true
      // }
      // if (item.length_unit_price === '' && !isMainUnit(item)) {
      //   msg = `成品编号为${item.finish_product_code}的数据,辅助数量单价不能为空`
      //   return true
      // }
    })
  }

  msg && ElMessage.error(msg)
  return msg
}

// 复制
function handleCopy(row: any) {
  // 复制选中的一行数据
  const tempItem = deepClone({
    ...row,
    uniqueId: `${state.tableData.length + 1}${Date.now() + Math.ceil(Math.random())}`, // 为复制项生成新的唯一id
  })
  state.tableData.push(tempItem)
  //
}

// 删除
function handDel(row: any) {
  //

  const idx = state.tableData.findIndex((item: any) => item.uniqueId === row.uniqueId)
  if (idx !== -1) {
    state.tableData.splice(idx, 1)
    ElMessage.success('删除成功')
  }
  else {
    ElMessage.error('未找到目标行')
  }

  //

  // state.tableData.splice(rowIndex, 1)
  // const index = state.tableData.findIndex(item => item.uniqueId === row.uniqueId)
  // if (index !== -1) {
  //   state.tableData.splice(index, 1)
  //   ElMessage.success('删除成功')
  // }
  // else {
  //   ElMessage.error('未找到目标行')
  // }
}

function getSaleSystem(row: any) {
  state.form.sale_system_name = row?.name
  saleSaleSystemInfo.value = row
  clearData(row)
}

const bulkSetting = ref<any>({
  address: {
    receipt_unit_name: '',
    receipt_unit_id: '',
    receipt_person: '',
    receipt_address: '',
  },
})
function handBulkClose() {
  bulkShow.value = false
}

const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
    query: { sale_system_id: state.form.sale_system_id },
  },
  // {
  //   field: 'color_code',
  //   title: '颜色编号',
  //   component: 'select',
  //   api: 'GetFinishProductColorDropdownList',
  //   labelField: 'product_color_code',
  // },
  {
    field: 'dye_factory_color',
    title: '染厂颜色',
    component: 'input',
    type: 'text',
  },
  {
    field: 'dye_factory_color_num',
    title: '染厂色号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'dye_factory_vat_code',
    title: '染厂缸号',
    component: 'input',
    type: 'text',
  },
  {
    field: 'finish_product_level',
    title: '染厂缸号',
    component: 'select',
    api: 'GetInfoBaseFinishedProductLevelEnumList',
  },
  {
    field: 'piece_count',
    title: '匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'piece_weight',
    title: '均重',
    component: 'input',
    type: 'float',
  },
  {
    field: 'total_weight',
    title: '采购数量',
    component: 'input',
    type: 'float',
  },
  {
    field: 'unit_price',
    title: '采购单价',
    component: 'input',
    type: 'float',
    digits: 4,
  },
  {
    field: 'length',
    title: '辅助数量',
    component: 'input',
    type: 'float',
  },
  // {
  //   field: 'length_unit_price',
  //   title: '辅助数量单价',
  //   component: 'input',
  //   type: 'float',
  //   digits: 4,
  // },
])

async function bulkSubmit({ row, value, selectData }: any) {
  if (multipleSelection.value?.length <= 0)
    return ElMessage.error('请填写批量修改的数据')

  if (!value[row.field])
    return ElMessage.error('请输入参数')

  multipleSelection.value?.map((item: any) => {
    if (row.field === 'color_code') {
      item.color_code = selectData?.product_color_code
      item.color_type_name = selectData?.type_finished_product_kind_name
      item.color_type_id = selectData?.type_finished_product_kind_id
      item.color_Name = selectData?.product_color_name
      item.color_id = selectData?.id
    }
    // else if (row.field === 'unit_price') {
    //   if (isMainUnit(item))
    //     item.unit_price = value[row.field]
    //   else
    //     item.length_unit_price = value[row.field]
    // }
    else {
      item[row.field] = value[row.field]
    }
    computedData(item)
  })
  ElMessage.success('设置成功')
}

/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(item: any, isInit = false) {
  // 初始化结算单位
  if (isInit && !item.auxiliary_unit_id) {
    item.auxiliary_unit_id = item.measurement_unit_id
    item.auxiliary_unit_name = item.unit
  }
  return item
}

async function computedData(changeRow: any) {
  // 计算总数量
  if (changeRow?.piece_weight !== '' && (changeField.value === 'piece_count' || changeField.value === 'piece_weight')) {
    changeRow.total_weight = Number.parseFloat(
      Big(changeRow.piece_count || 0)
        .times(changeRow?.piece_weight || 0)
        .toFixed(2),
    )
  }

  // 计算金额
  const price_weight = Big(changeRow.unit_price || 0).times(changeRow?.total_weight || 0)
  const price_length = Big(changeRow.length || 0).times(changeRow?.length_unit_price || 0)
  changeRow.total_price = Number.parseFloat(price_weight.plus(price_length).toFixed(2))

  await tablesRef.value.tableRef.updateFooter()
}

function handAllSelect({ records }: any) {
  multipleSelection.value = records
}

function handleSelectionChange({ records }: any) {
  multipleSelection.value = records
}

let oneStatus = true
function clearData(row: any) {
  state.tableData?.map((item) => {
    if (!oneStatus) {
      item.customer_id = saleSaleSystemInfo.value?.default_customer_id || ''
      item.customer_name = saleSaleSystemInfo.value?.default_customer_name || ''
    }
  })
  oneStatus = false
  bulkList[0].query = { sale_system_id: row.id }
  bulkSetting.value.customer_id = ''
}

const tableConfig = ref({
  showCheckBox: true,
  showOperate: true,
  fieldApiKey: 'FinishedProductProcurementEdit',
  operateWidth: 100,
  filterStatus: false,
  footerMethod,
  handAllSelect,
  handleSelectionChange,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <div class="line" />
    <template #right-top>
      <el-button v-btnAntiShake="handSubmit" type="primary">
        提交
      </el-button>
    </template>
    <slot>
      <el-form ref="ruleFormRef" :model="state.form" label-width="60px" label-position="top" :rules="state.fromRules">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem required label="营销体系名称:">
            <template #content>
              <el-form-item prop="sale_system_id">
                <SelectComponents
                  v-model="state.form.sale_system_id"
                  :default-status="true"
                  api="GetSaleSystemDropdownListApi"
                  label-field="name"
                  value-field="id"
                  @select="getSaleSystem"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="供应商名称:">
            <template #content>
              <el-form-item prop="supplier_id">
                <!-- <SelectComponents
                  @select="val => (state.form.supplier_name = val?.name)"
                  :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct }"
                  v-model="state.form.supplier_id"
                  api="BusinessUnitSupplierEnumlist"
                  label-field="name"
                  value-field="id"
                ></SelectComponents> -->
                <SelectDialog
                  v-model="state.form.supplier_id"
                  :label-name="detalData.supplier_name"
                  :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct, name: componentRemoteSearch.name }"
                  api="BusinessUnitSupplierEnumlist"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '供应商编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '供应商编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-value="val => (state.form.supplier_name = val?.name)"
                  @change-input="val => (componentRemoteSearch.name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="收货单位名称:">
            <template #content>
              <el-form-item prop="receipt_unit_id">
                <SelectDialog
                  v-model="state.form.receipt_unit_id"
                  :label-name="detalData.receipt_unit_name"
                  :query="{ or_unit_type_id: `${BusinessUnitIdEnum.customer},${BusinessUnitIdEnum.dyeFactory}`, name: componentRemoteSearch.recipien_entity_name }"
                  api="GetBusinessUnitListApi"
                  :column-list="[
                    {
                      field: 'name',
                      title: '名称',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'name',
                          isEdit: true,
                          title: '名称',
                          minWidth: 100,
                        },
                      ],
                    },
                    {
                      field: 'code',
                      title: '编号',
                      minWidth: 100,
                      isEdit: true,
                      colGroupHeader: true,
                      childrenList: [
                        {
                          field: 'code',
                          isEdit: true,
                          title: '编号',
                          minWidth: 100,
                        },
                      ],
                    },
                  ]"
                  @change-value="getUnitInfo"
                  @change-input="val => (componentRemoteSearch.recipien_entity_name = val)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem required label="采购日期:">
            <template #content>
              <el-form-item prop="purchase_date">
                <SelectDate v-model="state.form.purchase_date" type="date" placeholder="请选择采购日期" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货日期:">
            <template #content>
              <el-form-item prop="receipt_date">
                <SelectDate v-model="state.form.receipt_date" type="date" placeholder="请选择收货日期" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货地址:">
            <template #content>
              <el-form-item prop="receipt_address">
                <el-input v-model="state.form.receipt_address" placeholder="请输入收货地址" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="收货电话:">
            <template #content>
              <el-form-item prop="receipt_phone">
                <el-input v-model="state.form.receipt_phone" placeholder="请输入收货电话" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="发票抬头:">
            <template #content>
              <el-form-item prop="fapiao_title">
                <SelectComponents
                  v-model="state.form.fapiao_id"
                  api="GetInfoPurchaseInvoiceHeaderListUseByOther"
                  label-field="name"
                  value-field="id"
                  @change-value="row => (state.form.fapiao_title = row.name)"
                />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="税率">
            <template #content>
              <el-input v-model="state.form.tax_rate" label="税率" :disabled="!checkbox" placeholder="请输入税率" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="">
            <template #content>
              <el-checkbox v-model="checkbox" label="是否含税" size="large" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="订单类型:">
            <template #content>
              <SelectSaleMode v-model="state.form.sale_mode" :show-customer-book="false" />
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="备注:" copies="2">
            <template #content>
              <el-form-item prop="remark">
                <el-input v-model="state.form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </template>
          </DescriptionsFormItem>
        </div>
      </el-form>
    </slot>
  </FildCard>
  <FildCard title="成品信息" :tool-bar="true" class="mt-[5px]">
    <template #right-top>
      <el-button style="margin-left: 10px" type="primary" @click="handEdit">
        批量操作
      </el-button>
      <el-button style="margin-left: 10px" type="primary" :icon="Plus" @click="handAdd">
        根据资料添加
      </el-button>
    </template>
    <Table ref="tablesRef" :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #customer_id="{ row }">
        <SelectDialog
          :key="row.uniqueId"
          v-model="row.customer_id"
          :label-name="row.customer_name"
          :query="{ sale_system_id: state.form.sale_system_id, name: componentRemoteSearch.customer_name }"
          api="GetCustomerEnumList"
          :column-list="[
            {
              title: '客户编号',
              minWidth: 100,
              required: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '客户编号',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '客户名称',
              minWidth: 100,
              colGroupHeader: true,
              required: true,
              childrenList: [
                {
                  isEdit: true,
                  field: 'name',
                  title: '客户名称',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '电话',
              colGroupHeader: true,
              minWidth: 100,
              childrenList: [
                {
                  field: 'phone',
                  isEdit: true,
                  title: '电话',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '销售员',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'seller_name',
                  title: '销售员',
                  soltName: 'seller_name',
                  isEdit: true,
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="val => (componentRemoteSearch.customer_name = val)"
        />
      </template>
      <template #color_code="{ row }">
        <SelectDialog
          :key="row.uniqueId"
          v-model="row.color_id"
          :query="{
            finish_product_id: row.finish_product_id,
            product_color_code: row.color_code,
          }"
          :column-list="[
            {
              field: 'product_color_code',
              title: '色号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'product_color_code',
                  isEdit: true,
                  title: '色号',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'product_color_name',
              title: '颜色',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'product_color_name',
                  isEdit: true,
                  title: '颜色',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :table-column="[
            {
              field: 'product_color_code',
              title: '颜色编号',
            },
          ]"
          api="GetFinishProductColorDropdownList"
          label-field="product_color_code"
          @on-input="val => (row.color_code = val)"
          @change-value="val => getColorInfo(row, val)"
        />
      </template>
      <template #dye_factory_color="{ row }">
        <vxe-input v-model="row.dye_factory_color" size="mini" maxlength="200" />
      </template>
      <template #dye_factory_color_num="{ row }">
        <vxe-input v-model="row.dye_factory_color_num" size="mini" maxlength="200" />
      </template>
      <template #dye_factory_vat_code="{ row }">
        <vxe-input v-model="row.dye_factory_vat_code" size="mini" maxlength="200" />
      </template>
      <template #finish_product_level="{ row }">
        <SelectComponents
          v-model="row.finish_product_level_id"
          size="small"
          api="GetInfoBaseFinishedProductLevelEnumList"
          label-field="name"
          value-field="id"
          @select="val => (row.finish_product_level = val.name)"
        />
      </template>
      <template #finish_product_width="{ row }">
        {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
        <!-- <vxe-input size="mini" v-model="row.finish_product_width" maxlength="200"></vxe-input> -->
      </template>
      <template #finish_product_gram_weight="{ row }">
        {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
        <!-- <vxe-input size="mini" v-model="row.finish_product_gram_weight" maxlength="200"></vxe-input> -->
      </template>
      <template #paper_tube_weight="{ row }">
        {{ `${row.paper_tube_weight}kg` }}
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" size="mini" maxlength="200" />
      </template>
      <template #piece_count="{ row }">
        <vxe-input v-model="row.piece_count" size="mini" type="float" :min="0" @input="changeData(row, 'piece_count')" />
      </template>
      <template #piece_weight="{ row }">
        <vxe-input v-model="row.piece_weight" size="mini" type="float" :min="0" @input="changeData(row, 'piece_weight')" />
      </template>
      <!--  结算单位 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" clearable @select="conductUnitPrice(row)" />
      </template>
      <!-- 数量 -->
      <template #total_weight="{ row }">
        <vxe-input v-model="row.total_weight" size="mini" type="float" :min="0" @input="changeData(row, 'total_weight')" />
      </template>
      <!-- 数量单价 -->
      <template #unit_price="{ row }">
        <vxe-input v-model="row.unit_price" size="mini" type="float" :min="0" :digits="2" @input="changeData(row)" />
      </template>
      <template #upper_limit="{ row }">
        <vxe-input v-model="row.upper_limit" size="mini" type="float" :min="0" :digits="2" @input="changeData(row)" />
      </template>
      <template #lower_limit="{ row }">
        <vxe-input v-model="row.lower_limit" size="mini" type="float" :min="0" :digits="2" @input="changeData(row)" />
      </template>
      <!-- 辅助数量 -->
      <template #length="{ row }">
        <vxe-input v-model="row.length" size="mini" type="float" :min="0" @input="changeData(row)" />
      </template>
      <template #total_price="{ row }">
        {{ row.total_price }}
      </template>
      <template #operate="{ row }">
        <el-space :size="10">
          <el-link type="primary" :underline="false" @click="handleCopy(row)">
            复制
          </el-link>
          <el-link type="danger" :underline="false" @click="handDel(row)">
            删除
          </el-link>
        </el-space>
      </template>
    </Table>
  </FildCard>
  <SelectRawMaterial v-model="showAdd" @submit="onSubmit" />
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
</template>

<style lang="scss" scoped></style>
