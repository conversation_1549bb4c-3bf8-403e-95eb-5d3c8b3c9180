import type { CSSProperties } from 'vue'
import { h, onActivated, onMounted, onUnmounted, ref } from 'vue'

export interface DataDelimiterConfig {
  /** 数据头，用于标识数据包开始 */
  dataHeader?: number[]
  /** 数据尾，用于标识数据包结束 */
  dataFooter?: number[]
  /** 是否启用数据头尾分割模式，默认使用 \r\n */
  useCustomDelimiter?: boolean
}

export interface StabilityConfig {
  /** 是否启用稳定值检测 */
  enabled?: boolean
  /** 稳定值：连续相同重量值的次数 */
  stableCount?: number
  /** 重量值精度，用于比较重量是否相同 */
  precision?: number
}

export type MeasureWeightServiceReturnType = ReturnType<typeof useMeasureWeightService>

export function useMeasureWeightService(delimiterConfig?: DataDelimiterConfig, stabilityConfig?: StabilityConfig) {
  const isSerialSupported = ref(false)
  const isConnected = ref(false)
  const port = ref<any | null>(null)
  const reader = ref<ReadableStreamDefaultReader<Uint8Array> | null>(null)
  const writer = ref<WritableStreamDefaultWriter<Uint8Array> | null>(null)
  const logMessages = ref<string[]>([])
  const statusMessage = ref('')
  const isError = ref(false)
  const currentFrameData = ref(0) // 当前帧数据
  const currentWeight = ref('0.00') // 当前重量值
  const weightUnit = ref('kg') // 重量单位
  const isStable = ref(false) // 称重是否稳定
  const isGross = ref(true) // 是否为毛重
  const isPositive = ref(true) // 是否为正值

  // 数据分割配置
  const config = ref<DataDelimiterConfig>({
    dataHeader: [],
    dataFooter: [13, 10], // 默认使用 \r\n
    useCustomDelimiter: false,
    ...delimiterConfig,
  })

  // 稳定性配置
  const stabilitySettings = ref<StabilityConfig>({
    enabled: false,
    stableCount: 3, // 默认3次相同重量视为稳定
    precision: 2, // 默认精度到小数点后2位
    ...stabilityConfig,
  })

  // 重量历史记录，用于稳定性检测
  const weightHistory = ref<number[]>([])

  // 数据缓冲区
  const dataBuffer: number[] = []
  // 添加日志
  function addMessage(message: string) {
    logMessages.value.push(message)
    if (logMessages.value.length > 100)
      logMessages.value.shift()
  }
  // 抽象重连逻辑为独立函数
  async function autoReconnect() {
    if (!isSerialSupported.value)
      return

    try {
      const ports = await (navigator as any).serial.getPorts()
      if (ports && ports.length > 0) {
        port.value = ports[0]

        // 检查端口是否已经完全打开并可用
        if (port.value.readable && port.value.writable) {
          addMessage('✅检测到端口已经打开，恢复连接状态')
          isConnected.value = true
          statusMessage.value = '已连接到串口设备'

          // 先清理现有的 reader 和 writer
          if (reader.value) {
            try {
              reader.value.cancel()
              reader.value.releaseLock()
            }
            catch (e) {
              console.error('释放现有 reader 错误:', e)
            }
            reader.value = null
          }

          if (writer.value) {
            try {
              writer.value.close()
              writer.value.releaseLock()
            }
            catch (e) {
              console.error('释放现有 writer 错误:', e)
            }
            writer.value = null
          }

          // 检查 stream 是否已被锁定
          try {
            reader.value = port.value.readable.getReader()
            writer.value = port.value.writable.getWriter()
            readSerialData()
          }
          catch (e) {
            console.error('获取 reader/writer 错误:', e)
            addMessage('⚠️ 端口已被其他进程占用，跳过自动重连')
          }
          return
        }

        // 检查端口是否正在打开或处于未知状态
        if (port.value.readable !== null || port.value.writable !== null) {
          addMessage('⚠️ 检测到端口可能正在打开中，跳过自动重连')
          return
        }

        // 只有当端口完全关闭时才尝试打开
        await port.value.open({ baudRate: 9600 })

        isConnected.value = true
        statusMessage.value = '已重新连接到设备'
        reader.value = port.value.readable.getReader()
        writer.value = port.value.writable.getWriter()
        readSerialData()
        addMessage('✅已重新连接到设备')
      }
    }
    catch (error: any) {
      console.error('重新连接错误:', error)

      // 特殊处理正在打开的错误
      if (error.message && error.message.includes('A call to open() is already in progress')) {
        addMessage('⚠️ 端口正在打开中，请稍后再试')
        return
      }

      addMessage(`❌重新连接错误: ${error}`)
      // 重置状态
      isConnected.value = false
      port.value = null
      reader.value = null
      writer.value = null
    }
  }
  onMounted(() => {
    isSerialSupported.value = 'serial' in navigator
    if (isSerialSupported.value) {
      // 监听设备连接
      (navigator as any).serial.addEventListener('connect', onConnect);
      // 监听设备断开
      (navigator as any).serial.addEventListener('disconnect', onDisconnect)
      // 延迟执行自动重连，避免页面刷新时的竞态条件
      setTimeout(() => {
        autoReconnect()
      }, 500)
    }
    // 添加页面刷新前的事件监听
    window.addEventListener('beforeunload', disconnectPort)
  })
  // 组件被激活时尝试重连
  onActivated(() => {
    if (isSerialSupported.value && !isConnected.value) {
      addMessage('组件激活，尝试重新连接...')
      autoReconnect()
    }
  })

  function onConnect() {
    // 设备重新连接时的处理
    addMessage('onConnect')
    isConnected.value = true
    statusMessage.value = '设备已重新连接'
  }
  function onDisconnect() {
    // 设备断开时的处理
    addMessage('onDisconnect')
    isConnected.value = false
    statusMessage.value = '设备已断开连接'
    reader.value = null
    writer.value = null
    port.value = null
  }
  function clearLogMessages() {
    logMessages.value = []
  }
  async function connectToSerialPort() {
    try {
      if (!('serial' in navigator)) {
        addMessage('❌您的浏览器不支持串口连接')
        throw new Error('❌您的浏览器不支持串口连接')
      }

      // 如果没有端口，请求选择端口
      if (!port.value)
        port.value = await (navigator as any).serial.requestPort()

      // 检查端口是否已经打开
      if (port.value && port.value.readable) {
        addMessage('⚠️ 检测到串口已打开，先关闭现有连接')
        // 先断开现有连接
        await disconnectPort()
        // 等待一小段时间确保断开完成
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      // 重新获取端口（如果之前被关闭了）
      if (!port.value || port.value.readable === undefined)
        port.value = await (navigator as any).serial.requestPort()

      await port.value.open({ baudRate: 9600 }) // 根据您的设备调整波特率

      isConnected.value = true
      statusMessage.value = '已连接到串口设备'
      clearLogMessages()
      addMessage('✅已连接到串口设备')
      isError.value = false

      reader.value = port.value.readable.getReader()
      writer.value = port.value.writable.getWriter()
      readSerialData()
    }
    catch (error: any) {
      if (error.message.includes('Failed to execute \'open\' on \'SerialPort\': Failed to open serial port.'))
        addMessage('❌请检查当前串口是否已被连接')
      else if (error.message.includes('The port is already open'))
        addMessage('❌串口已被占用，请先断开现有连接')
      else if (error.message.includes('A call to open() is already in progress'))
        addMessage('❌串口正在打开中，请稍后再试或先断开现有连接')
      else
        addMessage(`❌串口连接错误:${error}`)
      console.error('串口连接错误:', error)
      statusMessage.value = `串口连接错误: ${error.message}`
      isError.value = true
      isConnected.value = false
    }
  }
  // 切换连接
  async function toggleConnection() {
    if (isConnected.value)
      await disconnectPort()
    else
      await connectToSerialPort()
  }
  async function readSerialData() {
    try {
      // 检查 reader 是否存在
      if (!reader.value) {
        console.error('Reader 不存在，无法读取数据')
        return
      }

      while (true) {
        // 再次检查 reader 是否仍然存在（可能在循环中被清理）
        if (!reader.value)

          break

        const { value, done } = await reader.value.read()

        if (done) {
          // 如果 stream 结束，可能是页面刷新导致的状态问题
          addMessage('⚠️ 数据流已结束，可能是页面刷新导致的状态问题')

          // 清理当前的 reader 和 writer
          try {
            if (reader.value) {
              reader.value.releaseLock()
              reader.value = null
            }
            if (writer.value) {
              writer.value.releaseLock()
              writer.value = null
            }
          }
          catch (e) {
            console.error('清理资源错误:', e)
          }

          // 调用 autoReconnect 来处理重连逻辑
          if (port.value && isConnected.value) {
            addMessage('🔄 调用自动重连...')
            setTimeout(() => {
              autoReconnect()
            }, 100)
          }

          break
        }

        // 将接收到的数据添加到缓冲区
        dataBuffer.push(...value)
        // 尝试解析缓冲区中的数据
        parseBufferedData()
      }
    }
    catch (error: any) {
      console.error('读取数据错误:', error)
      // 如果读取出错，可能是连接已断开
      if (error?.message && error.message.includes('Cannot read properties of null')) {
        addMessage('❌ 连接已断开，停止读取数据')
        isConnected.value = false
      }
    }
  }
  function parseBufferedData() {
    if (dataBuffer.length === 0)
      return

    // 使用循环处理所有完整的数据包
    while (dataBuffer.length > 0) {
      let packetStartIndex = 0
      let packetEndIndex = -1

      if (config.value.useCustomDelimiter) {
        // 使用自定义数据头尾分割
        const { dataHeader, dataFooter } = config.value

        // 查找数据头（如果配置了）
        if (dataHeader && dataHeader.length > 0) {
          const headerIndex = findSequenceInBuffer(dataBuffer, dataHeader)
          if (headerIndex === -1) {
            // 没有找到数据头，等待更多数据
            break
          }
          packetStartIndex = headerIndex + dataHeader.length
        }

        // 查找数据尾
        if (dataFooter && dataFooter.length > 0) {
          const footerIndex = findSequenceInBuffer(dataBuffer, dataFooter, packetStartIndex)
          if (footerIndex === -1) {
            // 没有找到数据尾，等待更多数据
            break
          }
          packetEndIndex = footerIndex
        }
        else {
          // 如果没有配置数据尾，使用整个缓冲区
          packetEndIndex = dataBuffer.length
        }
      }
      else {
        // 南计台秤默认使用 \r\n 分割
        const crlfIndex = dataBuffer.findIndex((byte, index) =>
          byte === 13 && dataBuffer[index + 1] === 10, // CR LF
        )

        if (crlfIndex === -1) {
          // 没有找到完整的数据包，等待更多数据
          break
        }
        packetEndIndex = crlfIndex
      }

      // 提取完整的数据包
      const packetBytes = dataBuffer.slice(packetStartIndex, packetEndIndex)

      const packetString = String.fromCharCode(...packetBytes)

      // 解析重量数据
      parseWeightData(packetString)

      // 移除已处理的数据
      const removeLength = config.value.useCustomDelimiter
        ? packetEndIndex + (config.value.dataFooter?.length || 0)
        : packetEndIndex + 2 // 包括\r\n
      dataBuffer.splice(0, removeLength)
    }
  }

  // 在缓冲区中查找字节序列
  function findSequenceInBuffer(buffer: number[], sequence: number[], startIndex = 0): number {
    if (sequence.length === 0)
      return -1

    for (let i = startIndex; i <= buffer.length - sequence.length; i++) {
      let found = true
      for (let j = 0; j < sequence.length; j++) {
        if (buffer[i + j] !== sequence[j]) {
          found = false
          break
        }
      }
      if (found)
        return i
    }
    return -1
  }

  // 检查重量值是否稳定
  function checkWeightStability(newWeight: number): boolean {
    if (!stabilitySettings.value.enabled)
      return true // 如果未启用稳定性检测，直接返回稳定

    const { stableCount, precision } = stabilitySettings.value

    // 将重量值按精度四舍五入
    const roundedWeight = Number(newWeight.toFixed(precision))

    // 添加到历史记录
    weightHistory.value.push(roundedWeight)

    // 保持历史记录长度不超过稳定次数
    if (weightHistory.value.length > stableCount!)
      weightHistory.value.shift()

    // 检查是否有足够的历史记录
    if (weightHistory.value.length < stableCount!)
      return false

    // 检查最近的重量值是否都相同
    const firstWeight = weightHistory.value[0]
    const allSame = weightHistory.value.every(weight => weight === firstWeight)

    if (allSame) {
      addMessage(`🎯 重量稳定: ${firstWeight}${weightUnit.value} (连续${stableCount}次相同)`)
      return true
    }

    return false
  }

  // 更新稳定性配置
  function updateStabilityConfig(newConfig: Partial<StabilityConfig>) {
    stabilitySettings.value = { ...stabilitySettings.value, ...newConfig }
    // 清空历史记录，重新开始检测
    weightHistory.value = []
    addMessage(`🔧 稳定性配置已更新: ${JSON.stringify(stabilitySettings.value)}`)
  }

  // 获取稳定性配置
  function getStabilityConfig() {
    return { ...stabilitySettings.value }
  }

  // 解析重量数据
  function parseWeightData(dataString: string) {
    // 检查是否有状态字（第一个字符为%、$、=等）
    const hasStatusByte = /^[%$=]/.test(dataString)
    let statusByte = 0
    let cleanData = dataString

    if (hasStatusByte) {
      // 获取状态字的ASCII码
      statusByte = dataString.charCodeAt(0)
      // 移除状态字符
      cleanData = dataString.substring(1)

      // 解析状态字（根据README.md协议）
      parseStatusByte(statusByte)
    }

    // 匹配重量数据格式：数字.数字+单位
    const weightMatch = cleanData.match(/^(\d*\.?\d+)(kg|g)?/i)

    if (weightMatch) {
      let weightValue = weightMatch[1]
      const unit = weightMatch[2] || 'kg'

      // 根据状态字判断正负（如果有状态字的话）
      if (hasStatusByte) {
        // 状态字位0：1=负，0=正
        const isNegative = (statusByte & 0x01) === 1
        if (isNegative && !weightValue.startsWith('-'))
          weightValue = `-${weightValue}`

        isPositive.value = !isNegative
      }
      else {
        // 没有状态字，检查是否有负号
        isPositive.value = !weightValue.startsWith('-')
      }

      // 更新重量数据
      currentWeight.value = weightValue
      weightUnit.value = unit.toLowerCase()

      // 获取数值重量
      const numericWeight = Number.parseFloat(weightValue)

      // 检查重量稳定性
      const isWeightStable = checkWeightStability(Math.abs(numericWeight))

      // 如果启用了稳定性检测，只有稳定的重量才更新 currentFrameData
      if (stabilitySettings.value.enabled) {
        if (isWeightStable) {
          currentFrameData.value = numericWeight
          isStable.value = true
          addMessage(`✅ 稳定重量已更新: ${numericWeight}${unit}`)
        }
        else {
          isStable.value = false
          addMessage(`⏳ 重量检测中: ${numericWeight}${unit} (${weightHistory.value.length}/${stabilitySettings.value.stableCount})`)
        }
      }
      else {
        // 未启用稳定性检测，直接更新
        currentFrameData.value = numericWeight
      }

      // 添加日志消息
      const sign = isPositive.value ? '' : '-'
      const stabilityIcon = stabilitySettings.value.enabled ? (isStable.value ? '🎯' : '⏳') : '📊'
      addMessage(`${stabilityIcon} 重量: ${sign}${Math.abs(numericWeight)}${unit}`)
    }
    else {
      addMessage(`⚠️ 无法解析数据: ${dataString}`)
    }
  }

  // 解析状态字节（根据README.md协议）
  function parseStatusByte(statusByte: number) {
    // 位0：正负 (1=负，0=正)
    const isNegative = (statusByte & 0x01) === 1
    // 位1：重量类型 (1=净重，0=毛重)
    const isNetWeight = (statusByte & 0x02) === 2
    // 位2：称重稳定性 (1=稳定，0=不稳定)
    const hardwareStable = (statusByte & 0x04) === 4

    // 更新状态
    isPositive.value = !isNegative
    isGross.value = !isNetWeight

    // 如果启用了用户自定义稳定值检测，则不使用硬件稳定状态
    // 如果启用了自定义稳定检测，isStable.value 将在 parseWeightData 中设置
    // 稳定状态将在 parseWeightData 中根据重量历史记录来判断
    if (!stabilitySettings.value.enabled)
      isStable.value = hardwareStable
  }

  // 更新数据分割配置
  function updateDelimiterConfig(newConfig: Partial<DataDelimiterConfig>) {
    config.value = { ...config.value, ...newConfig }
    addMessage(`🔧 数据分割配置已更新: ${JSON.stringify(config.value)}`)
  }

  // 设置数据头
  function setDataHeader(header: number[] | string) {
    const headerBytes = typeof header === 'string'
      ? Array.from(header).map(char => char.charCodeAt(0))
      : header
    config.value.dataHeader = headerBytes
    addMessage(`🔧 数据头已设置: [${headerBytes.join(', ')}] (${String.fromCharCode(...headerBytes)})`)
  }

  // 设置数据尾
  function setDataFooter(footer: number[] | string) {
    const footerBytes = typeof footer === 'string'
      ? Array.from(footer).map(char => char.charCodeAt(0))
      : footer
    config.value.dataFooter = footerBytes
    addMessage(`🔧 数据尾已设置: [${footerBytes.join(', ')}] (${String.fromCharCode(...footerBytes)})`)
  }

  // 启用/禁用自定义分割模式
  function toggleCustomDelimiter(enabled: boolean) {
    config.value.useCustomDelimiter = enabled
    const mode = enabled ? '自定义数据头尾' : '默认\\r\\n'
    addMessage(`🔧 数据分割模式已切换为: ${mode}`)
  }

  // 获取当前配置
  function getDelimiterConfig() {
    return { ...config.value }
  }

  // 测试解析函数（用于调试）
  function testParseData() {
    // 测试新的负数数据：37, 48, 48, 48, 48, 46, 49, 55, 107, 103
    const testData1 = [37, 48, 48, 48, 48, 46, 49, 55, 107, 103, 13, 10]

    // 测试之前的正数数据
    const testData2 = [48, 46, 48, 48, 107, 103, 13, 10, 36, 48, 48, 48, 48, 46, 48, 48, 107, 103, 13, 10]

    // 清空缓冲区并添加测试数据
    dataBuffer.length = 0

    addMessage('🧪 测试负数数据: %0000.17kg')
    dataBuffer.push(...testData1)
    parseBufferedData()

    addMessage('🧪 测试正数数据: 0.00kg$0000.00kg')
    dataBuffer.push(...testData2)
    parseBufferedData()

    addMessage('🧪 所有测试数据解析完成')
  }

  // 断开连接
  async function disconnectPort() {
    try {
      // 标记为断开连接中
      isConnected.value = false
      statusMessage.value = '正在断开连接...'

      // 异步关闭读取器
      if (reader.value) {
        try {
          await reader.value.cancel()
          reader.value.releaseLock()
        }
        catch (e) {
          console.error('关闭读取器错误:', e)
        }
        reader.value = null
      }

      // 异步关闭写入器
      if (writer.value) {
        try {
          await writer.value.close()
          writer.value.releaseLock()
        }
        catch (e) {
          console.error('关闭写入器错误:', e)
        }
        writer.value = null
      }

      // 异步关闭端口
      if (port.value) {
        try {
          // 尝试异步关闭端口
          await port.value.close()
        }
        catch (e) {
          console.error('关闭端口错误:', e)
        }
        port.value = null
      }

      statusMessage.value = '已断开连接'
      isError.value = false

      // 添加日志
      addMessage('✅ 已断开串口连接')

      return true
    }
    catch (error) {
      console.error('断开连接时出错:', error)
      addMessage(`❌ 断开连接时出错: ${error}`)
      return false
    }
  }
  onUnmounted(() => {
    // 监听设备连接
    (navigator as any).serial?.removeEventListener('connect', onConnect);
    // 监听设备断开
    (navigator as any).serial?.removeEventListener('disconnect', onDisconnect)
    // 移除页面刷新前的事件监听
    window.removeEventListener('beforeunload', disconnectPort)
    disconnectPort()
  })
  function Log(style: CSSProperties) {
    return h(
      'textarea',
      {
        style: {
          width: '300px',
          height: '50px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          fontSize: '12px',
          resize: 'both', // 允许用户拖拽调整大小
          overflow: 'auto', // 允许滚动
          ...style,
        },
        readonly: true, // 设置为只读，防止用户编辑
      },
      logMessages.value.join('\n'), // 将消息以换行符连接
    )
  }
  return {
    isSerialSupported,
    isConnected,
    statusMessage,
    isError,
    logMessages,
    currentFrameData,
    currentWeight,
    weightUnit,
    isStable,
    isGross,
    isPositive,
    toggleConnection,
    connectToSerialPort,
    clearLogMessages,
    disconnectPort,
    testParseData,
    Log,
    port,
    reader,
    writer,
    // 数据分割配置相关方法
    updateDelimiterConfig,
    setDataHeader,
    setDataFooter,
    toggleCustomDelimiter,
    getDelimiterConfig,
    // 稳定性配置相关方法
    updateStabilityConfig,
    getStabilityConfig,
    checkWeightStability,
  }
}
