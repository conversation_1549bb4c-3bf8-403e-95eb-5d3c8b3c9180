<script setup lang="ts" name="FundsReceivedEdit">
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import AdvancesReceivedAdd from './components/advancesReceivedAdd.vue'
import FundsReceivedAdd from './components/fundsReceivedAdd.vue'
import { getActuallyCollectOrder, updateActuallyCollectOrder } from '@/api/fundsReceived'
import { EmployeeType } from '@/common/enum'
import { formatDate, formatPriceDiv, formatPriceMul, formatTwoDecimalsDiv, formatTwoDecimalsMul, formatUnitPriceDiv, formatWeightDiv, formatWeightMul, sumNum as sumNumCopy } from '@/common/format'
import { deleteToast } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import useRouterList from '@/use/useRouterList'
import SelectSettleAccountDialog from '@/components/SelectSettleAccountDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

const route = useRoute()

const routerList = useRouterList()
const formRef = ref()
const state = reactive<any>({
  form: {
    collect_price: 0,
    use_advance_price: 0,
    actually_collect_price: 0,
    write_off_price: 0,
  },
  formRules: {
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'change' }],
    settle_type_id: [{ required: true, message: '请选择收款账户', trigger: 'change' }],
    customer_id: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
    receive_collect_date: [{ required: true, message: '请选择收款日期', trigger: 'change' }],
  },
})

let uuid = 0
const AdvancesReceivedAddRef = ref()
const advancesReceivedOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showOperate: false,
    showSlotNums: true,
    showCheckBox: false,
    footerMethod: (val: any) => advancesReceivedOptions.footerMethod(val),
    handAllSelect: (val: any) => advancesReceivedOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => advancesReceivedOptions.handleSelectionChange(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['total_advance_price'].includes(column.field))
          return sumNum(data, 'total_advance_price', '') as any

        if (['total_remain_price'].includes(column.field))
          return sumNum(data, 'total_remain_price', '') as any

        if (['use_price'].includes(column.field))
          return sumNum(data, 'use_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'advance_collect_order_no',
      title: '预收款单号',
      minWidth: 150,
    },
    {
      field: 'advance_collect_date',
      title: '单据时间',
      minWidth: 100,
      is_date: true,
    },
    {
      field: 'total_advance_price',
      title: '预收金额',
      minWidth: 100,
      // isPrice: true,
    },
    {
      field: 'total_remain_price',
      title: '可用金额',
      minWidth: 100,
      // isPrice: true,
    },
    {
      field: 'use_price',
      soltName: 'use_price',
      title: '本次使用金额',
      minWidth: 100,
      required: true,
    },
    {
      field: 'remark',
      soltName: 'remark',
      title: '备注',
      minWidth: 100,
    },
    // {
    //   field: '',
    //   soltName: 'operate',
    //   title: '操作',
    //   width: 100,
    // },
  ],
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该预收信息')
    if (!res)
      return
    const index = advancesReceivedOptions.list.findIndex((item: any) => item.uuid === uuid)
    advancesReceivedOptions.list.splice(index, 1)
  },
  handAllSelect: ({ records }: any) => {
    advancesReceivedOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    advancesReceivedOptions.multipleSelection = records
  },
  handleSure: (list: any) => {
    const lists = list.map((item: any) => {
      return {
        uuid: ++uuid,
        ...item,
        advance_collect_order_id: item.id,
        advance_collect_order_no: item.order_no,
        advance_collect_date: item.receive_collect_date,
        total_advance_price: item.total_advance_price,
        total_remain_price: item.total_remain_price,
        use_price: 0,
      }
    })
    advancesReceivedOptions.list = [...advancesReceivedOptions.list, ...lists]
    AdvancesReceivedAddRef.value.state.showModal = false
  },
  showAdvancesReceived: () => {
    AdvancesReceivedAddRef.value.state.showModal = true
  },
})

const FundsReceivedAddRef = ref()
const fundsReceivedOptions = reactive<any>({
  multipleSelection: [],
  rowIndex: 0,
  tableConfig: {
    showSlotNums: false,
    showCheckBox: true,
    footerMethod: (val: any) => fundsReceivedOptions.footerMethod(val),
    handAllSelect: (val: any) => fundsReceivedOptions.handAllSelect(val),
    handleSelectionChange: (val: any) => fundsReceivedOptions.handleSelectionChange(val),
  },
  footerMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        if (['settle_roll'].includes(column.field))
          return sumNum(data, 'settle_roll', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['settle_length'].includes(column.field))
          return sumNum(data, 'settle_length', '')

        if (['settle_price'].includes(column.field))
          return sumNum(data, 'settle_price', '')

        if (['collected_price'].includes(column.field))
          return sumNum(data, 'collected_price', '')

        if (['uncollect_price'].includes(column.field))
          return sumNum(data, 'uncollect_price', '')

        if (['actually_collect_price'].includes(column.field))
          return sumNum(data, 'actually_collect_price', '')

        return null
      }),
    ]
  },
  list: [],
  columnList: [
    {
      field: 'src_order_no',
      fixed: 'left',
      title: '单据编号',
      minWidth: '8%',
    },
    {
      field: 'src_order_type_name',
      fixed: 'left',
      title: '单据类型',
      minWidth: 100,
    },
    {
      field: 'src_order_date',
      fixed: 'left',
      title: '单据日期',
      minWidth: 100,
      is_date: true,
    },
    {
      field: 'settle_roll',
      title: '结算条数/件数',
      minWidth: 100,
    },
    {
      field: 'settle_weight',
      title: '结算数量',
      minWidth: 100,
    },
    {
      field: 'settle_length',
      title: '结算辅助数量',
      minWidth: 100,
    },
    // {
    //   field: 'length_unit_price',
    //   title: '辅助数量单价',
    //   minWidth: 100,
    // },
    // {
    //   field: 'weight_unit_price',
    //   title: '数量单价',
    //   minWidth: 100,
    // },
    {
      field: 'settle_price',
      title: '结算金额',
      minWidth: 100,
    },
    {
      field: 'offset_price',
      soltName: 'offset_price',
      title: '优惠金额',
      minWidth: 100,
    },
    {
      field: 'discount_price',
      soltName: 'discount_price',
      title: '折扣金额',
      minWidth: 100,
    },
    {
      field: 'deduction_price',
      soltName: 'deduction_price',
      title: '扣款金额',
      minWidth: 100,
    },
    {
      field: 'uncollect_price',
      title: '未收金额',
      minWidth: 100,
    },
    // {
    //   field: 'should_collect_price',
    //   soltName: 'should_collect_price',
    //   title: '应收金额',
    //   minWidth: 100,
    // },
    {
      field: 'actually_collect_price',
      soltName: 'actually_collect_price',
      title: '本次收款金额',
      minWidth: 100,
      required: true,
    },
    // {
    //   field: 'write_off_price',
    //   title: '核销金额',
    //   minWidth: 100,
    // },
    {
      field: 'remark',
      soltName: 'remark',
      title: '备注',
      minWidth: 100,
    },
    {
      field: 'collected_price',
      title: '已收金额',
      minWidth: 100,
    },
    // {
    //   field: '',
    //   soltName: 'operate',
    //   title: '操作',
    //   width: 100,
    // },
  ],
  //   删除
  handleRowDel: async ({ uuid }: any) => {
    const res = await deleteToast('是否确认删除该实收信息')
    if (!res)
      return
    const index = fundsReceivedOptions.list.findIndex((item: any) => item.uuid === uuid)
    fundsReceivedOptions.list.splice(index, 1)
  },
  handAllSelect: ({ records }: any) => {
    fundsReceivedOptions.multipleSelection = records
  },
  handleSelectionChange: ({ records }: any) => {
    fundsReceivedOptions.multipleSelection = records
  },
  handleSure: (list: any) => {
    let actually_collect_price = 0
    const lists = list.map((item: any) => {
      actually_collect_price += Number(item.total_uncollect_money)
      // 如果是退货需要定义成负数
      const settle_roll = item.roll
      const settle_weight = item.weight
      const settle_length = formatTwoDecimalsDiv(item.length)
      const settle_price = item.total_settle_money
      const collected_price = item.total_collected_money
      const uncollect_price = item.total_uncollect_money
      // if (item.collect_type_name.includes('退货')) {
      //   settle_roll = 0 - settle_roll
      //   settle_weight = 0 - settle_weight
      //   settle_length = 0 - settle_length
      //   settle_price = 0 - settle_price
      //   collected_price = 0 - collected_price
      //   uncollect_price = 0 - uncollect_price
      // }
      return {
        uuid: ++uuid,
        ...item,
        src_order_no: item.order_no,
        src_order_id: item.id,
        src_order_type_name: item.collect_type_name,
        src_order_type: item.collect_type,
        src_order_date: item.order_time,
        settle_roll,
        settle_weight,
        settle_length,
        length_unit_price: formatUnitPriceDiv(item.length_sale_price),
        weight_unit_price: formatUnitPriceDiv(item.weight_sale_price),
        settle_price,
        collected_price,
        uncollect_price,
        offset_price: 0,
        discount_price: 0,
        deduction_price: 0,
        actually_collect_price: 0,
        write_off_price: 0,
        should_collect_price: item.total_uncollect_money,
        selected: false,
      }
    })
    fundsReceivedOptions.list = [...fundsReceivedOptions.list, ...lists]
    state.form.collect_price = actually_collect_price
    state.form.write_off_price = actually_collect_price
    FundsReceivedAddRef.value.state.showModal = false
  },
  showFundsReceived: () => {
    FundsReceivedAddRef.value.state.showModal = true
  },
})

// 新增提交
const { fetchData: addFetch, data: successData, success: addSuccess, msg: addMsg } = updateActuallyCollectOrder()
// 提交所有数据
function submitAddAllData() {
  // 表单验证
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!fundsReceivedOptions.list.length)
        return ElMessage.error('请添加实收信息')

      // 必填项
      for (let i = 0; i < advancesReceivedOptions.list.length; i++) {
        if (advancesReceivedOptions.list[i].use_price === '')
          return ElMessage.error('使用金额为必填项')
      }
      for (let i = 0; i < fundsReceivedOptions.list.length; i++) {
        if (!Number(fundsReceivedOptions.list[i].actually_collect_price))
          return ElMessage.error('收款金额为必填项')
      }
      // if (Number(state.form.actually_collect_price) > Number(state.form.use_advance_price) + Number(state.form.collect_price)) {
      //   return ElMessage.error('实收金额不能大于使用预收款与收款金额之和')
      // }

      const filterList = advancesReceivedOptions.list.filter((item: unknown) => {
        return item.use_price !== '' && Number(item.use_price) > 0
      })

      // 整理参数
      const query = {
        id: Number(route.query.id),
        ...state.form,
        seller_id: state.form.seller_id || 0,
        receive_collect_date: formatDate(state.form.receive_collect_date),
        collect_price: formatTwoDecimalsMul(Number(state.form.collect_price)),
        actually_collect_price: formatTwoDecimalsMul(Number(state.form.actually_collect_price)),
        // 预收款
        adv_item_data: filterList.map((item: any) => {
          return {
            ...item,
            advance_collect_date: formatDate(item.advance_collect_date),
            total_advance_price: formatPriceMul(Number(item.total_advance_price)),
            total_remain_price: formatPriceMul(Number(item.total_remain_price)),
            use_price: formatPriceMul(Number(item.use_price)),
          }
        }),
        act_item_data: fundsReceivedOptions.list.map((item: any) => {
          return {
            ...item,
            src_order_date: formatDate(item.src_order_date),
            settle_roll: item.src_order_type_name.includes('原料') ? item.settle_roll : formatTwoDecimalsMul(item.settle_roll),
            settle_weight: formatWeightMul(item.settle_weight),
            settle_length: formatTwoDecimalsMul(item.settle_length),
            settle_price: formatTwoDecimalsMul(item.settle_price),
            collected_price: formatTwoDecimalsMul(item.collected_price),
            uncollect_price: formatTwoDecimalsMul(item.uncollect_price),
            offset_price: formatTwoDecimalsMul(item.offset_price),
            discount_price: formatTwoDecimalsMul(item.discount_price),
            deduction_price: formatTwoDecimalsMul(item.deduction_price),
            should_collect_price: formatTwoDecimalsMul(item.should_collect_price),
            actually_collect_price: formatTwoDecimalsMul(item.actually_collect_price),
            write_off_price: formatTwoDecimalsMul(item.write_off_price),
          }
        }),
      }
      await addFetch(query)
      if (addSuccess.value) {
        ElMessage.success('提交成功')
        // 跳转到列表页
        // router.push({
        //   name: 'FundsReceived',
        // })
        getData()
        routerList.push({
          name: 'FundsReceivedDetail',
          query: {
            id: successData.value.id,
          },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}
const tablesRef = ref()
const tablesRef1 = ref()
watch(
  () => advancesReceivedOptions.list,
  () => {
    tablesRef.value.tableRef.updateFooter()
  },
  { deep: true },
)
watch(
  () => fundsReceivedOptions.list,
  () => {
    tablesRef1.value.tableRef.updateFooter()
  },
  { deep: true },
)

function computedShouldCollectPrice(row: any) {
  Number((Number(row.uncollect_price) - Number(row.offset_price) - Number(row.discount_price) - Number(row.deduction_price)).toFixed(2))
  Number((Number(row.uncollect_price) + Number(row.offset_price) + Number(row.discount_price) + Number(row.deduction_price)).toFixed(2))
  const should_collect_price = [2, 4, 6].includes(row.src_order_type) ? '' : ''
  row.should_collect_price = should_collect_price
  row.actually_collect_price = should_collect_price
}
function computedWriteOffPrice(row: any) {
  const write_off_price = Number((Number(row.actually_collect_price) + Number(row.offset_price) + Number(row.discount_price) + Number(row.deduction_price)).toFixed(2))
  row.write_off_price = row.src_order_type_name.includes('退货') ? -write_off_price : write_off_price
}
function computedPrice(row: any) {
  computedShouldCollectPrice(row)
  computedWriteOffPrice(row)
}

watch(
  () => advancesReceivedOptions.list,
  () => {
    state.form.use_advance_price = advancesReceivedOptions.list.reduce((pre: number, val: any) => pre + Number(val.use_price), 0)
  },
  { deep: true },
)
watch(
  () => fundsReceivedOptions.list,
  () => {
    let actually_collect_price = 0
    let write_off_price = 0
    fundsReceivedOptions.list.forEach((item: any) => {
      actually_collect_price += Number(item.actually_collect_price)
      write_off_price += Number(item.write_off_price)
    })
    state.form.collect_price = Number(actually_collect_price.toFixed(2))
    state.form.write_off_price = Number(write_off_price.toFixed(2))
  },
  { deep: true },
)

const { fetchData: DetailFetch, data: detailData } = getActuallyCollectOrder()
onMounted(() => {
  getData()
})
const customerRef = ref()
// 获取数据
async function getData() {
  await DetailFetch({ id: route.query.id })
  state.form = {
    ...detailData.value,
    collect_price: formatTwoDecimalsDiv(detailData.value.collect_price),
    actually_collect_price: formatTwoDecimalsDiv(detailData.value.actually_collect_price),
  }
  customerRef.value.inputLabel = detailData.value.customer_name
  fundsReceivedOptions.list
    = detailData.value?.act_item_data?.map((item: any) => {
      const settle_roll = formatTwoDecimalsDiv(item.settle_roll)
      const settle_weight = formatWeightDiv(item.settle_weight)
      const settle_length = formatTwoDecimalsDiv(item.settle_length)
      const settle_price = formatTwoDecimalsDiv(item.settle_price)
      const collected_price = formatTwoDecimalsDiv(item.collected_price)
      const uncollect_price = formatTwoDecimalsDiv(item.uncollect_price)
      // if (item.src_order_type_name.includes('退货')) {
      //   settle_roll = 0 - settle_roll
      //   settle_weight = 0 - settle_weight
      //   settle_length = 0 - settle_length
      //   settle_price = 0 - settle_price
      //   collected_price = 0 - collected_price
      //   uncollect_price = 0 - uncollect_price
      // }
      return {
        ...item,
        uuid: ++uuid,
        src_order_date: formatDate(item.src_order_date),
        settle_roll,
        settle_weight,
        settle_length,
        settle_price,
        collected_price,
        uncollect_price,
        offset_price: formatTwoDecimalsDiv(item.offset_price),
        discount_price: formatTwoDecimalsDiv(item.discount_price),
        deduction_price: formatTwoDecimalsDiv(item.deduction_price),
        should_collect_price: formatTwoDecimalsDiv(item.should_collect_price),
        actually_collect_price: formatTwoDecimalsDiv(item.actually_collect_price),
        write_off_price: formatTwoDecimalsDiv(item.write_off_price),
      }
    }) || []
  advancesReceivedOptions.list
    = detailData.value?.adv_item_data?.map((item: any) => {
      return {
        uuid: ++uuid,
        ...item,
        total_remain_price: formatPriceDiv(item.total_remain_price),
        total_advance_price: formatPriceDiv(item.total_advance_price),
        total_used_price: formatPriceDiv(item.total_used_price),
        use_price: formatPriceDiv(item.use_price),
      }
    }) || []
}

const percentage = computed(() => {
  const yushouMoney = Number(sumNumCopy(advancesReceivedOptions.list, 'use_price') as any)

  const shoukuanMoney = Number(state.form.actually_collect_price)

  const hexiaoMoney = Number(sumNumCopy(fundsReceivedOptions?.list, 'actually_collect_price')) || 0

  if ((hexiaoMoney / (shoukuanMoney + shoukuanMoney)) * 100 > 100) {
    return 100
  }
  else {
    if (Number.isNaN(hexiaoMoney / (yushouMoney + shoukuanMoney)))
      return 0
    else
      return Number((hexiaoMoney / (yushouMoney + shoukuanMoney)) * 100).toFixed(2)
  }
})

async function handAlldelet() {
  if (fundsReceivedOptions.multipleSelection.length <= 0) {
    return ElMessage.error('请选择一条数据')
  }
  else {
    const res = await deleteToast('是否删除已选订单')
    if (res)
      fundsReceivedOptions.list = compareAndRemoveDuplicates(fundsReceivedOptions.list, fundsReceivedOptions.multipleSelection)
  }
}

function compareAndRemoveDuplicates(originalArray: unknown, newArray: unknown) {
  const result = []

  for (const item of originalArray) {
    const found = newArray.find(newItem => newItem.uuid === item.uuid)
    if (!found)
      result.push(item)
  }

  return result
}

function changeCustomer(item: any) {
  // state.form.settle_type_id = item?.settle_type
  state.form.seller_id = item?.seller_id
  state.form.sale_system_id = item?.select_sale_system_id
}
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" :disabled="fundsReceivedOptions.list.length ? false : true" @click="submitAddAllData">
        提交
      </el-button>
    </template>
    <el-form ref="formRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem :required="true" label="客户名称:">
          <template #content>
            <el-form-item prop="customer_id">
              <SelectCustomerDialog
                v-model="state.form.customer_id"
                is-merge
                field="name"
                :default-value="{
                  id: state.form.customer_id,
                  name: state.form.customer_name,
                  code: state.form.customer_code,
                }"
                show-choice-system
                @change-value="changeCustomer"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系名称:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <!--        <DescriptionsFormItem :required="true" label="结算类型:"> -->
        <!--          <template v-slot:content> -->
        <!--            <el-form-item prop="settle_type_id"> -->
        <!--              <SelectComponents api="GetTypeSettleAccountsEnumList" label-field="name" value-field="id" v-model="state.form.settle_type_id" clearable /> -->
        <!--            </el-form-item> -->
        <!--          </template> -->
        <!--        </DescriptionsFormItem> -->
        <DescriptionsFormItem :required="true" label="收款账户:">
          <template #content>
            <el-form-item prop="settle_type_id">
              <SelectSettleAccountDialog
                v-model="state.form.settle_type_id"
                field="name"
              />
              <!--              <SelectComponents v-model="state.form.settle_type_id" api="GetTypeSettleAccountsEnumList" label-field="name" value-field="id" clearable /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="收款日期:">
          <template #content>
            <el-form-item prop="receive_collect_date">
              <el-date-picker v-model="state.form.receive_collect_date" type="date" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.form.seller_id"
                :query="{
                  duty: EmployeeType.salesman,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款凭证:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.form.voucher_number" maxlength="50" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款金额:">
          <template #content>
            <el-form-item>
              <vxe-input v-model="state.form.actually_collect_price" type="float" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款备注:" copies="2">
          <template #content>
            <el-form-item>
              <vxe-textarea v-model="state.form.remark" style="width: 100%" maxlength="500" show-word-count />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="核销信息" class="mt-[5px]" :tool-bar="false">
    <div class="verification-box">
      <div>
        <div class="top-font">
          本次核销情况
        </div>
        <div class="flex-box">
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ state.form.actually_collect_price }}
            </div>
            <div class="font">
              收款金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ state.form.use_advance_price || 0 }}
            </div>
            <div class="font">
              预收金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ fundsReceivedOptions?.list.length ? sumNumCopy(fundsReceivedOptions?.list, 'actually_collect_price') : 0 }}
            </div>
            <div class="font">
              已用金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ fundsReceivedOptions?.list.length ? sumNumCopy(fundsReceivedOptions?.list, 'offset_price') : 0 }}
            </div>
            <div class="font">
              优惠金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ fundsReceivedOptions?.list.length ? sumNumCopy(fundsReceivedOptions?.list, 'discount_price') : 0 }}
            </div>
            <div class="font">
              折扣金额
            </div>
          </div>
          <div class="mony-box">
            <div class="mb-[10px]">
              ¥ {{ fundsReceivedOptions?.list.length ? sumNumCopy(fundsReceivedOptions?.list, 'deduction_price') : 0 }}
            </div>
            <div class="font">
              扣款金额
            </div>
          </div>
        </div>
      </div>
      <div class="bg-box">
        <el-progress class="custom-progress" type="dashboard" :percentage="percentage" color="#5599f1" />
      </div>
      <div class="shu" />
      <div>
        <div class="use-font mb-[10px]">
          使用预收款
        </div>
        <div v-show="advancesReceivedOptions.list.length">
          <Table ref="tablesRef" :config="advancesReceivedOptions.tableConfig" :table-list="advancesReceivedOptions.list" :column-list="advancesReceivedOptions.columnList">
            <!-- 使用金额 -->
            <template #use_price="{ row }">
              <vxe-input v-model="row.use_price" :max="row.total_remain_price" type="float" />
            </template>
            <!-- 备注 -->
            <template #remark="{ row }">
              <vxe-input v-model="row.remark" maxlength="200" type="text" />
            </template>
          </Table>
        </div>
        <div v-if="advancesReceivedOptions.list.length === 0" class="no_data">
          <el-icon :size="80">
            <MessageBox />
          </el-icon>
          <div class="text">
            该客户暂无预收款
          </div>
        </div>
      </div>
    </div>
  </FildCard>
  <FildCard title="本次核销订单明细" class="mt-[5px]" :tool-bar="false">
    <template #right-top>
      <el-button :disabled="state.form.sale_system_id && state.form.customer_id ? false : true" type="primary" @click="fundsReceivedOptions.showFundsReceived">
        根据应收单添加
      </el-button>
      <el-button class="my-btn" type="danger" @click="handAlldelet">
        删除
      </el-button>
    </template>
    <div v-show="fundsReceivedOptions.list.length">
      <Table ref="tablesRef1" :config="fundsReceivedOptions.tableConfig" :table-list="fundsReceivedOptions.list" :column-list="fundsReceivedOptions.columnList">
        <!-- 优惠金额 -->
        <template #offset_price="{ row }">
          <vxe-input v-if="[2, 4, 6].includes(row.src_order_type)" v-model="row.offset_price" :min="row.uncollect_price" type="float" @change="computedPrice(row)" />
          <vxe-input v-else v-model="row.offset_price" :max="row.uncollect_price" type="float" @change="computedPrice(row)" />
        </template>
        <!-- 折扣金额 -->
        <template #discount_price="{ row }">
          <vxe-input v-if="[2, 4, 6].includes(row.src_order_type)" v-model="row.discount_price" :min="row.uncollect_price" type="float" @change="computedPrice(row)" />
          <vxe-input v-else v-model="row.discount_price" :max="row.uncollect_price" type="float" @change="computedPrice(row)" />
        </template>
        <!-- 扣款金额 -->
        <template #deduction_price="{ row }">
          <vxe-input v-if="[2, 4, 6].includes(row.src_order_type)" v-model="row.deduction_price" :min="row.uncollect_price" type="float" @change="computedPrice(row)" />
          <vxe-input v-else v-model="row.deduction_price" :max="row.uncollect_price" type="float" @change="computedPrice(row)" />
        </template>
        <template #should_collect_price="{ row }">
          <!--          <span v-if="[2, 4, 6].includes(row.src_order_type)">{{ 0 - row.should_collect_price }}</span> -->
          <span v-if="[2, 4, 6].includes(row.src_order_type)">{{ row.should_collect_price }}</span>
          <span v-else>{{ row.should_collect_price }}</span>
        </template>
        <!-- 实收金额 -->
        <template #actually_collect_price="{ row }">
          <vxe-input v-model="row.actually_collect_price" type="float" @change="computedWriteOffPrice(row)" />
        </template>
        <!-- 备注 -->
        <template #remark="{ row }">
          <vxe-input v-model="row.remark" maxlength="200" type="text" />
        </template>
      </Table>
    </div>
    <div v-if="fundsReceivedOptions.list.length === 0" class="no_data">
      <el-icon :size="80">
        <MessageBox />
      </el-icon>
      <div class="text">
        请添加实收信息
      </div>
    </div>
  </FildCard>
  <AdvancesReceivedAdd
    ref="AdvancesReceivedAddRef"
    :query="{
      sale_system_id: state.form.sale_system_id,
      customer_id: state.form.customer_id,
    }"
    @handle-sure="advancesReceivedOptions.handleSure"
  />
  <FundsReceivedAdd
    ref="FundsReceivedAddRef"
    :query="{
      sale_system_id: state.form.sale_system_id,
      customer_id: state.form.customer_id,
      audit_status: 2,
    }"
    @handle-sure="fundsReceivedOptions.handleSure"
  />
</template>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
}

.no_data {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40px 0;
  user-select: none;
  color: #999;
}

.flex_button {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
.verification-box {
  display: flex;
  .top-font {
    font-weight: 500;
    font-size: 14px;
    margin-left: 30px;
  }
  .flex-box {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    width: 360px;
    height: 200px;
    .mony-box {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      font-weight: 500;
      font-size: 12px;
      margin-left: 50px;
    }
  }
  .bg-box {
    width: 150px;
    height: 150px;
    background: linear-gradient(to bottom, #4992f0, #ffffff);
    margin-top: 60px;
    margin-left: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
  }
  .shu {
    width: 2px;
    height: 200px;
    background: #d7d7d7;
    margin-left: 30px;
    margin-top: 30px;
    margin-right: 20px;
  }
  .use-font {
    font-weight: 500;
    font-size: 14px;
  }

  .no_data {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 40px 0;
    user-select: none;
    color: #999;
  }
}

::v-deep(.custom-progress) {
  .el-progress__text {
    color: #3e77e1;
    font-weight: 500;
  }
}
.my-btn {
  background-color: #fff;
  border-color: red;
  color: red;
}
</style>
