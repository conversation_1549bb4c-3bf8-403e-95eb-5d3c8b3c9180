declare namespace Api.UpdateProductionScheduleOrder {
  /**
   * produce.UpdateProductionScheduleOrderParam
   */
  export interface Request {
  /**
   * 款号
   */
    account_num?: string
    /**
     * 获取往来单位织造配置
     */
    bf_prefix?: string
    /**
     * 布飞卷号规则
     */
    bf_sequence_number_rule?: number
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 布种后整
     */
    fabric_finishing?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布id
     */
    grey_fabric_id?: number
    /**
     * 排产单ID
     */
    id?: number
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 停机日期
     */
    machine_stop_date?: string
    /**
     * 停机备注
     */
    machine_stop_remark?: string
    /**
     * 停机状态
     */
    machine_stop_status?: boolean
    materialRatioList?: ProduceAddProductionScheduleOrderMaterialRatioParam[]
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 生产通知单id
     */
    production_notify_order_id?: number
    /**
     * 生产通知单号
     */
    production_notify_order_no?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 排产日期
     */
    schedule_date?: string
    /**
     * 排产条数
     */
    schedule_roll?: number
    /**
     * 总针数
     */
    total_needle_size?: string
    /**
     * 织厂id
     */
    weave_factory_id?: number
    /**
     * 纱批
     */
    yarn_batch?: string
    [property: string]: any
  }

  /**
   * produce.AddProductionScheduleOrderMaterialRatioParam
   */
  export interface ProduceAddProductionScheduleOrderMaterialRatioParam {
  /**
   * 原料损耗
   */
    material_loss?: number
    /**
     * 原料比例
     */
    material_ratio: number
    /**
     * 生产通知单详情id
     */
    production_notify_order_detail_id: number
    /**
     * 原料批号
     */
    raw_material_batch_number?: string
    /**
     * 原料品牌
     */
    raw_material_brand?: string
    /**
     * 原料颜色id
     */
    raw_material_color_id: number
    /**
     * 原料颜色名称
     */
    raw_material_color_name: string
    /**
     * 原料缸号
     */
    raw_material_dyelot_number?: string
    /**
     * 原料id
     */
    raw_material_id: number
    /**
     * 原料名称
     */
    raw_material_name: string
    /**
     * 供应商id
     */
    supplier_id?: number
    /**
     * 织造类别id
     */
    weaving_category_id?: number
    [property: string]: any
  }
  /**
   * produce.UpdateProductionScheduleOrderData
   */
  export interface Response {
    id?: number
    [property: string]: any
  }
}
