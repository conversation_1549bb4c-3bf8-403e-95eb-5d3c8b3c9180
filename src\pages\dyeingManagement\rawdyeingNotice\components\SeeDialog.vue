<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import Table from '@/components/Table.vue'
import { raw_materialdetail } from '@/api/rawInformationColor'
import { debounce } from '@/common/util'

const state = reactive<any>({
  showModal: false,
  stock_item_id: '',
})

const { fetchData, data, total, loading, page, size } = raw_materialdetail()

const getData = debounce(async () => {
  const query = {
    id: state.stock_item_id,
  }
  await fetchData(query)
}, 400)

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)
const tableConfig = ref({
  loading,
  showPagition: false,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: false,
  showSort: false,
})

defineExpose({
  state,
})

const columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 100,
    fixed: 'left',
  },
  {
    field: 'unit_name',
    title: '染纱厂名称',
    minWidth: 100,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'color_code',
    title: '原料色号',
    minWidth: 100,
  },
  {
    field: 'color_name',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    field: 'level_name',
    title: '原料等级',
    minWidth: 100,
  },
  {
    field: 'remark',
    title: '原料备注',
    minWidth: 100,
  },
  {
    field: 'production_date',
    title: '生产日期',
    minWidth: 100,
    is_date: true,
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    minWidth: 100,
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    minWidth: 100,
  },
  {
    field: 'yarn_origin',
    title: '棉纱产地',
    minWidth: 100,
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    minWidth: 100,
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    minWidth: 100,
  },
  {
    field: 'weave_factory_remark',
    title: '织厂/染纱厂备注',
    minWidth: 100,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
    fixed: 'right',
  },
  {
    field: 'whole_piece_count',
    title: '整件件数',
    minWidth: 100,
    isPrice: true,
    fixed: 'right',
  },
  {
    field: 'bulk_piece_count',
    title: '散件件数',
    isPrice: true,
    minWidth: 100,
    fixed: 'right',
  },
  {
    field: 'total_weight',
    title: '数量',
    minWidth: 100,
    fixed: 'right',
    isWeight: true,
  },
])
</script>

<template>
  <vxe-modal v-model="state.showModal" z-index="99999" title="查看库存数" width="1200" height="200" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full">
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList" />
    </div>
  </vxe-modal>
</template>

<style></style>
