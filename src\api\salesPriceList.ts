import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function GetSalePriceColorKindList() {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceColorKind/getSalePriceColorKindList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取列表--导出
export function GetSalePriceColorKindListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/sale_price/salePriceColorKind/exportSalePriceColorKindList',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}

// 变更状态
export function UpdateSalePriceColorKindStatus() {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceColorKind/updateSalePriceColorKindStatus',
    method: 'put',
  })
}

// 删除
export function DeleteSalePriceColorKind() {
  return useRequest({
    url: '/admin/v1/sale_price/salePriceColorKind/deleteSalePriceColorKind',
    method: 'put',
  })
}
