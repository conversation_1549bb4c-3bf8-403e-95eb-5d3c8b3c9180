<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  SaleOrderCancel,
  SaleOrderDetail,
  SaleOrderList,
  SaleOrderListExport,
  SaleOrderPass,
} from '@/api/rawMaterIalSaleOrder'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate } from '@/common/format'
import { debounce, getFilterData, orderStatusConfirmBox } from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'

const filterData = reactive({
  order_no: '',
  customer_id: '',
  sale_date: '',
  sale_unit_id: '',
  status: '',
  sale_start_date: '',
  sale_end_date: '',
})

onMounted(() => {
  getData()
})
const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeSaleOrder,
  dataType: PrintDataType.Raw,
})
const {
  fetchData: fetchDataList,
  data: dataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = SaleOrderList()

// 列表请求参数
function getQuery() {
  const status = ((filterData.status as unknown as []) || []).join(',')
  return getFilterData({ ...filterData, status }, ['purchase_time'])
}

async function getData() {
  await fetchDataList(getQuery())
  if (dataList.value?.list)
    showDetail(dataList.value?.list?.[0])
}

onActivated(getData)

const selectRow = ref()
const detailShow = ref()
const {
  fetchData: fetchDataDetail,
  data: dataDetail,
  loading: loadingDetail,
} = SaleOrderDetail()
async function getDataDetail() {
  await fetchDataDetail({ id: selectRow.value.id })
}

watch(
  () => filterData.sale_date,
  (value: any) => {
    filterData.sale_start_date = formatDate(value?.[0]) || ''
    filterData.sale_end_date = formatDate(value?.[1]) || ''
  },
)

watch(
  () => filterData,
  debounce(() => {
    getData()
  }, 400),
  { deep: true },
)

function showDetail(row: any) {
  selectRow.value = row
  detailShow.value = true
  getDataDetail()
}

function handDetail(row: any) {
  router.push({ name: 'RawMaterialSalesOrderDetail', params: { id: row?.id } })
}
async function handPass(id: number) {
  const msg = { title: '是否审核该订单', desc: '点击审核后订单将审核通过' }
  await orderStatusConfirmBox({ id, message: msg, api: SaleOrderPass })
  getData()
}
async function handCancel(id: number) {
  const msg = {
    title: '是否消审该订单',
    desc: '点击消审后订单将变回待审核状态',
  }
  await orderStatusConfirmBox({ id, message: msg, api: SaleOrderCancel })
  getData()
}

function handEdit(row: any) {
  router.push({
    name: 'RawMaterialSalesOrderEdit',
    params: { id: row?.id },
  })
}

function handAdd() {
  router.push({
    name: 'RawMaterialSalesOrderAdd',
  })
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '订单编号',
    soltName: 'order_no',
    width: '8%',
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'sale_unit_name',
    title: '出货单位',
    width: 80,
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系名称',
    width: 100,
  },
  {
    sortable: true,
    field: 'sale_date',
    title: '销售时间',
    width: 100,
  },
  {
    sortable: true,
    field: 'total_weight',
    title: '销售数量',
    isWeight: true,
    width: 100,
  },
  {
    sortable: true,
    field: 'seller_name',
    title: '销售员',
    width: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    width: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'update_user_name',
    title: '修改人',
    width: 100,
  },
  {
    sortable: true,
    field: 'update_time',
    title: '修改时间',
    isDate: true,
    width: 150,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    width: 100,
  },
  {
    sortable: true,
    field: 'audit_time',
    title: '审核时间',
    isDate: true,
    width: 140,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    showOrder_status: true,
    soltName: 'status',
    fixed: 'right',
    width: '5%',
  },
])

const cColumnList = ref([

  {
    sortable: true,
    field: 'raw_material_code',
    title: '原料编号',
    fixed: 'left',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'raw_material_name',
    title: '原料名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'craft',
    title: '原料工艺',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'color_scheme',
    title: '原料色系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '供应商',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'level_name',
    title: '原料等级',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'raw_material_remark',
    title: '原料备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'production_date',
    title: '生产日期',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'spinning_type',
    title: '纺纱类型',
    minWidth: 100,
  },

  {
    sortable: true,
    field: 'cotton_origin',
    title: '棉花产地',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'yarn_origin',
    title: '棉纱产地',
    minWidth: 100,
  },
])

const tableConfig = ref({
  fieldApiKey: 'RawMaterialSalesOrder',
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  height: '100%',
  showOperate: true,
  operateWidth: '11%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,

  // handAllSelect,
  // handleSelectionChange,
})

const CTableConfig = ref({
  fieldApiKey: 'RawMaterialSalesOrder_B',
  loadingDetail,
  height: '100%',
  operateWidth: '200',
  showSort: false,
  showSpanHeader: true,
  // handAllSelect,
  // handleSelectionChange,
})

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!dataList?.value.list || dataList?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '原料销售单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = SaleOrderListExport({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getQuery(),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false" class="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              placeholder="单据编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="filterData.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货单位:">
          <template #content>
            <SelectBusinessDialog
              v-model="filterData.sale_unit_id"
              api="GetBusinessUnitListApi"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory }"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售日期:" width="330">
          <template #content>
            <SelectDate v-model="filterData.sale_date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.status"
              :multiple="true"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full">
      <template #right-top>
        <el-button
          v-has="`RawMaterialSalesOrderAdd`"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="`RawMaterialSalesOrder_export`"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="dataList?.list"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="showDetail(row)">
            {{
              row?.order_no
            }}
          </el-link>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="`RawMaterialSalesOrderDetail`"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="`RawMaterialSalesOrderEdit`"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="`RawMaterialSalesOrder_pass`"
              type="primary"
              :underline="false"
              @click="handPass(row.id)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="`RawMaterialSalesOrder_cancel`"
              type="primary"
              :underline="false"
              @click="handCancel(row.id)"
            >
              消审
            </el-link>
            <PrintPopoverBtn
              :id="row.id"
              style="width: auto"
              print-btn-text="打印"
              print-btn-type="text"
              :options="options"
              api="SaleOrderDetail"
            />
          </el-space>
        <!--        <PrintBtn
          btnType="text"
          type="saleDeliverRaw"
          :tid="1671455114998016"
          api="SaleOrderDetail"
          :id="row.id"
        /> -->
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom">
      <Table
        :config="CTableConfig"
        :table-list="dataDetail.items"
        :column-list="cColumnList"
      />
    </FildCard>
  </div>
</template>
