// PrintType 结合 PrintDataType 二者确定可打印的模板
export enum PrintType {
  PrintTemplateTypePurOrder = 1, // 采购单
  PrintTemplateTypePurReturnOrder = 2, // 采购退货单
  PrintTemplateTypeProduceNotify = 3, // 生产通知单
  PrintTemplateTypeDNFNotify = 4, // 染整通知单
  PrintTemplateTypeSaleOrder = 5, // 销售单
  PrintTemplateTypeShouldCollectOrder = 6, // 销售送货单
  PrintTemplateTypeSaleTransferOrder = 14, // 调货销售单
  PrintTemplateTypeSaleTransferOrderRtn = 15, // 调货退货单
  PrintTemplateQuarantine = 16, // 质检
  PrintTemplateTypeStock = 50, // 库存打码
  PrintTemplateTypeWarehouseBin = 51, // 仓位打印
  PrintTemplateTypeLabel = 52, // 标签打印
  PrintTemplateTypeColorLabel = 53, // 颜色标签打印
  PrintTemplateTypePurchaserReconciliation = 17, // 客户对账表
  PrintTemplateTypeSupplierReconciliation = 18, // 供应商对账单
}
export enum PrintDataType {
  Raw = 1, // 原料
  Grey, // 坯布
  Product, // 成品
}
