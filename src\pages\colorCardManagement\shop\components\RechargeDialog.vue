<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { cloneDeep } from 'lodash-es'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import { Recharge } from '@/api'

const props = withDefaults(defineProps<Props>(), {
  row: {},
})
const emits = defineEmits(['handleSure'])
interface Props {
  row: any
}
const showModal = defineModel({
  default: false,
})
const formData = ref({
  remark: '',
  deadline: '',
  fileList: [],
})
function disabledDate(time: Date) {
  return time.getTime() < Date.now()
}
const rules = reactive({
  deadline: [
    {
      type: 'date',
      required: true,
      message: '请选择截至有效期',
      trigger: 'change',
    },
  ],
})

function handCancel() {
  showModal.value = false
}

const formRef = ref()
watch(showModal, (val) => {
  if (val)
    formRef.value?.resetFields()
})
const {
  fetchData,
  success,
  msg,
  loading,
} = Recharge()
async function handleSure() {
  await formRef.value.validate(async (valid: any) => {
    if (valid) {
      const query: any = cloneDeep(formData.value)
      query.id = props.row?.id
      query.voucher = query.fileList.join(',')
      query.deadline = dayjs(`${query.deadline} 23:59:59`)
      delete query.fileList
      await fetchData(query)
      if (success.value) {
        emits('handleSure')
        ElMessage.success('充值成功')
        showModal.value = false
      }
      else { ElMessage.error(msg.value) }
    }
  })
}
</script>

<template>
  <vxe-modal v-model="showModal" lock-view show-footer title="充值" width="900" height="700" :mask="false" :esc-closable="true" resize>
    <el-form
      ref="formRef"
      class="p-8"
      label-width="auto"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="截至有效期" prop="deadline">
        <el-date-picker v-model="formData.deadline" type="date" value-format="YYYY-MM-DD" :disabled-date="disabledDate" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" />
      </el-form-item>
      <el-form-item label="凭证" prop="fileList">
        <TextureMapWall :key="formData.fileList.length" v-model:image-list="formData.fileList" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" :loading="loading" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
