<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { GetTenantPackageList, ManualAddPayRecord } from '@/api'
import { formatPriceMul } from '@/common/format'
import UploadFile from '@/components/UploadFile/index.vue'

const emits = defineEmits(['handleSubmit'])

const { fetchData: getTenantPackageList } = GetTenantPackageList()

const { fetchData: handAddFun, success: addSuccess, msg: addMessage, loading: addLoading } = ManualAddPayRecord()

onMounted(async () => {
  await getTenantPackageList()
})
const ruleFormRef = ref<FormInstance>()
const state = reactive({
  showModal: false,
  modalName: '添加购买记录',
  form: {
    remark: '',
    deadline: '',
    pay_voucher_url: '',
  },
})
defineExpose({
  state,
})

// 上传之前处理file
async function handUpload(url: any) {
  state.form.pay_voucher_url = url[0]
}
const rules = reactive<FormRules<RuleForm>>({
  deadline: [
    { required: true, message: 'Please input Activity name', trigger: 'blur' },
    { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
  ],
  remark: [
    {
      required: true,
      message: 'Please select Activity zone',
      trigger: 'change',
    },
  ],
  pay_voucher_url: [
    {
      required: true,
      message: 'Please select Activity count',
      trigger: 'change',
    },
  ],
})

async function handleSubmit() {
  await ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const query = {
        tenant_management_id: Number(state.form.tenant_management_id),
        trade_no: state.form.trade_no,
        pay_record_order_no: state.form.pay_record_order_no,
        payer_name: state.form.payer_name,
        pay_time: state.form.pay_time,
        pay_price: formatPriceMul(Number(state.form.pay_price)),
        tenant_package_id: state.form.tenant_package_id,
        start_time: state.form.start_time,
        pay_voucher_url: state.form.pay_voucher_url,
      }

      await handAddFun(query)

      if (!addSuccess.value) {
        ElMessage.error(addMessage.value)
        return
      }

      emits('handleSubmit')

      state.showModal = false
      state.form = {
        remark: '',
        deadline: '',
        pay_voucher_url: '',
      }
    }
  })
}
</script>

<template>
  <vxe-modal v-model="state.showModal" :title="state.modalName" width="1000" height="500" :mask="false" :lock-view="false" show-footer :esc-closable="true" resize>
    <el-form ref="ruleFormRef" :rules="rules" :model="state.form" label-width="auto">
      <el-form-item label="截至有效期" prop="deadline">
        <el-date-picker v-model="state.form.deadline" format="YYYY/MM/DD" value-format="YYYY-MM-DD" type="date" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="state.form.remark" clearable type="textarea" placeholder="输入备注" />
      </el-form-item>
      <el-form-item label="凭证" prop="pay_voucher_url">
        <UploadFile multiple :show-submit-btn="false" @on-upload-success="handUpload" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="addLoading" @click="handleSubmit">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
