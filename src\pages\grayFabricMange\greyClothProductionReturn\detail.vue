<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import {
  getGfmProduceReturnOrder,
  updateGfmProduceReturnOrderStatusCancel,
  updateGfmProduceReturnOrderStatusPass,
  updateGfmProduceReturnOrderStatusReject,
  updateGfmProduceReturnOrderStatusWait,
} from '@/api/greyClothProductionReturn'
import { formatPriceDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import { deepClone, orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { processDataOut } from '@/common/handBinary'

const state = reactive<any>({
  yarnRatioList: [],
  yarnRatioList_total: [],
})

const rourte = useRoute()

const { fetchData, data } = getGfmProduceReturnOrder()

onMounted(() => {
  fetchData({ id: rourte.query.id })
})

const columnList_fabic_config = ref({
  fieldApiKey: 'GreyClothProductionDetail_A',
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val, 1),
})

function cellDBLClickEvent(val: any, nums: number) {
  if (nums === 1) {
    state.yarnRatioList = val.row?.use_yarn_data
    state.yarnRatioList_total = flattenUseYarnItemData(val.row.use_yarn_data)
  }
}

// TODO:将数据源摊平
function flattenUseYarnItemData(arr: any) {
  const result: any = []

  arr.forEach((item: any) => {
    const useYarnItemData = item.use_yarn_item_data?.map((itemData: any) => Object.entries(itemData).reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}))
    result.push(...useYarnItemData)
  })

  return result
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['total_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'total_weight') as any)}`

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['total_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'total_price') as any)}`

      if (['roll_return'].includes(column.property))
        return `${sumNum(data, 'roll_return')}`

      return null
    }),
  ]
}

const columnList_fabic = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 150,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 100,
  },
  {
    field: 'produce_notice_order_no',
    title: '生产通知单号',
    minWidth: 100,
    fixed: 'right',
  },
  {
    field: 'roll',
    title: '退货匹数',
    minWidth: 100,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'xima',
    title: '细码',
    minWidth: 100,
    soltName: 'xima',
    fixed: 'right',
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 100,
    fixed: 'right',
    isWeight: true,
  },
  {
    field: 'avg_weight',
    title: '平均数量',
    minWidth: 100,
    fixed: 'right',
    isWeight: true,
  },
  {
    field: 'process_single_unit_price',
    title: '加工单价',
    minWidth: 100,
    fixed: 'right',
    isUnitPrice: true,
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 100,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'total_price',
    title: '金额',
    minWidth: 100,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    fixed: 'right',
  },
])

const AddXimaDialogRef = ref()

function handSeeXima(row: any) {
  AddXimaDialogRef.value.state.showModal = true
  let listData = deepClone(row?.item_fc_data || [])
  listData = processDataOut(listData)
  AddXimaDialogRef.value.state.tableData = listData || []
  AddXimaDialogRef.value.state.canEnter = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.horsepower = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.isDisabled = true
}

const closeLoading = ref(false)
const cancelLoading = ref(false)
const rejectLoading = ref(false)
const auditLoading = ref(false)
const eliminateLoading = ref(false)
async function updateStatus(audit_status: number) {
  const id: any = rourte.query.id?.toString()
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: updateGfmProduceReturnOrderStatusCancel, loadingRef: cancelLoading })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: updateGfmProduceReturnOrderStatusReject, loadingRef: rejectLoading })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: updateGfmProduceReturnOrderStatusPass, loadingRef: auditLoading })

  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateGfmProduceReturnOrderStatusWait,
      loadingRef: eliminateLoading,
    })
  }
  fetchData({ id: rourte.query.id })
}

const tableConfig_other = ref({
  fieldApiKey: 'GreyClothProductionDetail_B',
  showSlotNums: true,
  height: 600,
  footerMethod: (val: any) => FooterMethodOnce(val),
})

const tableConfig_other_two = ref({
  fieldApiKey: 'GreyClothProductionDetail_C',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  height: 600,
  footerMethod: (val: any) => FooterMethodOther(val),
})

function FooterMethodOnce({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'use_yarn_quantity') as any)}`

      if (['actually_use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_use_yarn_quantity') as any)}`

      return null
    }),
  ]
}

function FooterMethodOther({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'use_yarn_quantity') as any)}`

      return null
    }),
  ]
}

const yarnRatio_columnList = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 150,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 150,
  },
  {
    field: 'raw_material_brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'raw_material_batch_number',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 150,
  },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 150,
    isWeight: true,
  },
  {
    field: 'actually_use_yarn_quantity',
    title: '实际用纱量',
    minWidth: 150,
    isWeight: true,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 150,
  },
])

const yarnRatio_total = ref([
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 150,
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 150,
  },
  {
    field: 'unit_name',
    title: '织厂名称',
    minWidth: 150,
  },
  {
    field: 'supplier_name',
    title: '供应商',
    minWidth: 150,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 150,
  },
  {
    field: 'brand',
    title: '原料品牌',
    minWidth: 150,
  },
  {
    field: 'batch_num',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 150,
  },
  {
    field: 'level_name',
    title: '原料等级',
    minWidth: 150,
  },
  {
    field: 'raw_remark',
    title: '原料备注',
    minWidth: 150,
  },
  {
    field: 'measurement_unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'production_date',
    title: '生产日期',
    minWidth: 150,
  },
  {
    field: 'spinning_type',
    title: '纺纱类型',
    minWidth: 150,
  },
  {
    field: 'cotton_origin',
    title: '棉花产地',
    minWidth: 150,
  },
  {
    field: 'carton_num',
    title: '装箱单号',
    minWidth: 150,
  },
  {
    field: 'fapiao_num',
    title: '发票号',
    minWidth: 150,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])
</script>

<template>
  <StatusColumn
    :close-loading="closeLoading"
    :cancel-loading="cancelLoading"
    :reject-loading="rejectLoading"
    :audit-loading="auditLoading"
    :eliminate-loading="eliminateLoading"
    :order_no="data.order_no"
    :order_id="data.id"
    :status="data.audit_status"
    :status_name="data.audit_status_name"
    permission_wait_key="GreyClothProductionReturn_wait"
    permission_reject_key="GreyClothProductionReturn_reject"
    permission_pass_key="GreyClothProductionReturn_pass"
    permission_cancel_key="GreyClothProductionReturn_cancel"
    permission_edit_key="GreyClothProductionReturn_edit"
    edit_router_name="GreyClothProductionReturnEdit"
    @eliminate="updateStatus"
    @reject="updateStatus"
    @cancel="updateStatus"
    @audit="updateStatus"
  />
  <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="营销体系:">
        <template #content>
          {{ data?.sale_system_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="货源单位:">
        <template #content>
          {{ data?.return_unit_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="接收单位:">
        <template #content>
          {{ data?.supplier_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="退货日期:">
        <template #content>
          {{ data?.return_time }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="单据备注:" copies="2">
        <template #content>
          {{ data?.remark }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard title="坯布信息" class="mt-[5px]">
    <Table :config="columnList_fabic_config" :table-list="data?.item_data" :column-list="columnList_fabic">
      <template #xima="{ row }">
        <el-button type="primary" text link @click="handSeeXima(row)">
          查看
        </el-button>
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱比例" class="mt-[5px]">
    <Table :config="tableConfig_other" :table-list="state.yarnRatioList" :column-list="yarnRatio_columnList">
      <template #actually_use_yarn_quantity="{ row }">
        {{ formatWeightDiv(sumTotal(row?.use_yarn_item_data, 'use_yarn_quantity')) }}
      </template>
    </Table>
  </FildCard>
  <FildCard title="用纱信息汇总" class="mt-[5px]">
    <Table :config="tableConfig_other_two" :table-list="state.yarnRatioList_total" :column-list="yarnRatio_total" />
  </FildCard>
  <AddXimaDialog ref="AddXimaDialogRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
