<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { getStockProductDetailDropdownList } from '@/api/fpQualityCheck'

// import { formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import { debounce, getFilterData, resetData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import FildCard from '@/components/FildCard.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    quality_check_status: 1,
    finish_product_id: '',
    dye_factory_id: '',
    order_type: '',
    order_no: '',
  },
  showModal: false,
  modalName: '从库存中查询',
  multipleSelection: [],
  list: [],
})

const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
  product_color_name: '',
  product_color_code: '',
})

const { fetchData, data: datalist, total, loading, page, size, handleSizeChange, handleCurrentChange } = getStockProductDetailDropdownList()

const tableConfig = reactive<any>({
  loading,
  showPagition: true,
  showSlotNums: false,
  page,
  size,
  fieldApiKey: fieldApiKeyList.QualityCheckManagementStockDialog,
  total,
  height: '100%',
  showSort: false,
  showRadio: true,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  radioChangeEvent: handleSelectionChange,
  // handAllSelect: (val: any) => handAllSelect(val),
  // handleSelectionChange: (val: any) => handleSelectionChange(val),
})

async function getData() {
  await fetchData(getFilterData(state.filterData))
  tableConfig.total = total
  tableConfig.page = page
  tableConfig.size = size
}

watch(
  () => datalist.value,
  () => {
    state.list = datalist.value?.list?.map((item: any) => {
      return {
        ...item,
      }
    })
  },
  { deep: true },
)

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const columnList = ref([
  {
    field: 'warehouse_in_order_no',
    minWidth: 170,
    title: '来源单号',
  },
  {
    field: 'warehouse_in_type_name',
    minWidth: 100,
    title: '来源单据类型',
  },
  {
    field: 'warehouse_name',
    minWidth: 100,
    title: '仓库',
  },
  {
    field: 'product_code',
    minWidth: 100,
    title: '成品编号',
  },
  {
    field: 'product_name',
    minWidth: 100,
    title: '成品名称',
  },
  {
    field: 'product_color_code',
    minWidth: 100,
    title: '色号',
  },
  {
    field: 'product_color_name',
    minWidth: 100,
    title: '颜色',
  },
  {
    field: 'dyelot_number',
    minWidth: 100,
    title: '缸号',
  },
  {
    field: 'volume_number',
    minWidth: 100,
    title: '卷号',
  },
  {
    field: 'roll',
    minWidth: 100,
    title: '条数',
    isPrice: true,
  },
  {
    field: 'weight',
    minWidth: 100,
    title: '数量',
    isWeight: true,
  },
  {
    field: 'warehouse_in_time',
    minWidth: 150,
    title: '入库时间',
    isDate: true,
  },
])

// function handAllSelect({ records }: any) {
//   // 单选
//   if (records.length > 1) {
//     records.forEach((item: any, index: number) => {
//       item.selected = index === 0
//     })
//     state.multipleSelection = records.filter((v: any) => v.selected)
//     return ElMessage.error('只能选中一种')
//   }
//   state.multipleSelection = records
// }

function handleSelectionChange({ row }: any) {
  // 单选
  // if (records.length > 1) {
  //   const index = records.findIndex((item: any) => item.id === row.id)
  //   records.forEach((item: any, i: number) => {
  //     item.selected = index === i
  //   })
  state.multipleSelection = [row]
  // return ElMessage.error('只能选中一种')
  // }
  // state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection?.length) {
    return ElMessage.error('请选择一条数据')
  }
  else {
    // return

    emits('handleSure', state.multipleSelection, state.rowIndex)
    state.showModal = false
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}

function changePro(data: any) {
  componentRemoteSearch.finish_product_code = data.finish_product_code
  componentRemoteSearch.finish_product_name = data.finish_product_name
}

const customerRef1 = ref()
const customerRef2 = ref()
function changeColor(stringRef: string) {
  if (stringRef === 'customerRef1')
    customerRef2.value.inputLabel = customerRef1.value.item?.product_color_name
  else
    customerRef1.value.inputLabel = customerRef2.value.item?.product_color_code
}
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="来源单号:">
          <template #content>
            <vxe-input v-model="state.filterData.warehouse_in_order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="来源单据类型:">
          <template #content>
            <SelectComponents v-model="state.filterData.warehouse_in_type" api="GetWarehouseGoodInTypeEnum" label-field="name" value-field="id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @change-input="val => (componentRemoteSearch.finish_product_code = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef1" -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :query="{ -->
            <!--                finish_product_code: componentRemoteSearch.finish_product_code, -->
            <!--              }" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @change-input="val => (componentRemoteSearch.finish_product_code = val)" -->
            <!--              @change-value="changePro('proRef1')" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @change-input="val => (componentRemoteSearch.finish_product_name = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef2" -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_name" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  colGroupHeader: true, -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  colGroupHeader: true, -->
            <!--                  minWidth: 100, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :query="{ -->
            <!--                finish_product_name: componentRemoteSearch.finish_product_name, -->
            <!--              }" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @change-value="changePro('proRef2')" -->
            <!--              @change-input="val => (componentRemoteSearch.finish_product_name = val)" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号:">
          <template #content>
            <SelectDialog
              ref="customerRef1"
              key="color2"
              v-model="state.filterData.product_color_id"
              :disabled="!state.filterData.product_id"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      title: '颜色编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      title: '颜色名称',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :query="{
                finish_product_id: state.filterData.product_id,
                product_color_name: componentRemoteSearch.product_color_code,
              }"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '色号',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="val => (componentRemoteSearch.product_color_code = val)"
              @change-value="changeColor('customerRef1')"
            />
          <!-- <SelectComponents v-model="state.filterData.product_color_id" api="GetFinishProductColorDropdownList" label-field="product_color_code" value-field="id"></SelectComponents> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色:">
          <template #content>
            <SelectDialog
              key="color1"
              ref="customerRef2"
              v-model="state.filterData.product_color_id"
              :disabled="!state.filterData.product_id"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      title: '颜色编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      title: '颜色名称',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :query="{
                finish_product_id: state.filterData.product_id,
                product_color_name: componentRemoteSearch.product_color_name,
              }"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '颜色',
                },
              ]"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_name"
              @change-input="val => (componentRemoteSearch.product_color_name = val)"
              @change-value="changeColor('customerRef2')"
            />
          <!-- <SelectComponents v-model="state.filterData.product_color_id" api="GetFinishProductColorDropdownList" label-field="product_color_name" value-field="id"></SelectComponents> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:">
          <template #content>
            <el-input v-model="state.filterData.dyelot_number" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="卷号:">
          <template #content>
            <vxe-input v-model="state.filterData.volume_number" type="number" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-radio-group v-model="state.filterData.quality_check_status" class="ml-4">
              <el-radio :label="1" size="large">
                仅看未质检的
              </el-radio>
              <el-radio :label="2" size="large">
                仅看已质检的
              </el-radio>
            </el-radio-group>
          </template>
        </DescriptionsFormItem>
      </div>
      <FildCard no-shadow tool-bar class="flex-auto flex flex-col ">
        <template #right-top>
          <el-button type="primary" @click="handReset">
            重置
          </el-button>
        </template>
        <Table :config="tableConfig" :table-list="state.list" :column-list="columnList">
          <template #is_finish="{ row }">
            <el-checkbox v-model="row.is_finish" disabled />
          </template>
          <template #weight_error="{ row }">
            {{ row.weight_error }}kg
          </template>
          <template #paper_tube_weight="{ row }">
            {{ row.paper_tube_weight }}kg
          </template>
        </Table>
      </FildCard>
    </div>

    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex_end {
  display: flex;
  justify-content: flex-end;
}

.el-link {
  color: #409eff;
}
</style>
