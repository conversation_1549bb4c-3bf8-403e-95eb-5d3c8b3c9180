<script setup lang="ts" name="BusinessAnalysis">
import { onActivated, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { debounce, getFilterData, resetData } from '@/common/util'
import { GetBusinessAnalysis } from '@/api/businessAnalysis'
import Table from '@/components/Table.vue'
import { formatDate, formatPriceDiv } from '@/common/format'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'

const filterData = ref({
  create_time: [],
  order_date_start: '',
  order_date_end: '',
  sale_system_id: '',
})
function handReset() {
  filterData.value = resetData(filterData.value)
}
const {
  fetchData: getBusinessAnalysisApi,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
  success,
  msg,
} = GetBusinessAnalysis()

// 获取数据
const getData = debounce(async () => {
  await getBusinessAnalysisApi(
    getFilterData(filterData.value, ['create_time']),
  )
  if (!success.value) {
    ElMessage({
      type: 'error',
      message: msg.value,
    })
  }
}, 400)

watch(filterData, getData, { deep: true })

watch(
  () => filterData.value.create_time,
  (newDate) => {
    if (newDate) {
      const [startTime, endTime] = newDate
      filterData.value.order_date_start = formatDate(startTime)
      filterData.value.order_date_end = formatDate(endTime)
    }
    else {
      filterData.value.order_date_start = ''
      filterData.value.order_date_end = ''
    }
  },
)

onMounted(() => {
  getData()
})
onActivated(getData)

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  fieldApiKey: fieldApiKeyList.BusinessAnalysisList,
  page,
  size,
  total,
  height: '100%',
  showOperate: false,
  operateWidth: '220',
  showSort: false,
  showSpanHeader: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})

const columnList = ref([
  {
    sortable: true,
    field: 'roll',
    title: '匹数',
    minWidth: 150,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'weight',
    title: '数量',
    minWidth: 150,
    isWeight: true,
  },
  {
    title: '单位',
    field: 'measurement_unit_name',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'avg_price',
    title: '均价',
    minWidth: 150,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'total_price',
    title: '金额',
    minWidth: 150,
    isPrice: true,
  },
])
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="时间范围:" width="330">
          <template #content>
            <SelectDate v-model="filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="filterData.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard :tool-bar="false" title="营业收支" class="">
      <el-row>
        <el-col :span="4">
          <el-statistic
            title="主营业务收支"
            :value="formatPriceDiv(data.main_business_income)"
          />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="主营业务成本"
            :value="formatPriceDiv(data.main_business_cost)"
          />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="其他收入"
            :value="formatPriceDiv(data.other_income)"
          />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="其他支出"
            :value="formatPriceDiv(data.other_cost)"
          />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="净利"
            :value="formatPriceDiv(data.net_profit)"
          />
        </el-col>
      </el-row>
    </FildCard>
    <FildCard :tool-bar="true" title="营业收支" class="table-card-full">
      <Table
        :config="tableConfig"
        :table-list="data?.item_data"
        :column-list="columnList"
      />
    </FildCard>
  </div>
</template>

<style scoped>
.el-col {
  text-align: center;
}
</style>
