<!--
  @Description: 自定义后端请求地址
 -->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import { getBaseUrl, isDevRun } from '@/common/constant'

const showModal = defineModel({ default: false })
const isDev = ref(isDevRun())
const envArr = [
  {
    env: 'test',
    domain: 'https://hcscmtest.zzfzyc.com/hcscm',
  },
  {
    env: 'cjt',
    domain: 'http://************:50002/hcscm',
  },
  {
    env: 'xmh',
    domain: 'http://************:50001/hcscm',
  },
  {
    env: 'pre',
    domain: 'https://hcscmpre.zzfzyc.com/hcscm',
  },
  {
    env: 'kdyb',
    domain: 'https://www.cottonyb.com/server',
  },
  {
    env: '自定义',
    domain: '',
  },
]

const form = reactive({
  protocol: '',
  ip: '',
  port: '',
  suffix: '',
  env: '',
})

onMounted(() => {
  if (!isDev.value)
    return

  const requestConfig = JSON.parse(localStorage.getItem('requestUrl') || '{}')
  if (requestConfig.url) {
    form.env = requestConfig.env
    form.ip = requestConfig.ip
    form.protocol = requestConfig.protocol
    form.port = requestConfig.port
    form.suffix = requestConfig.suffix
  }
  else {
    getRequestData()
  }
})

function getRequestData() {
  const curEnv = form.env
  if (!curEnv)
    return
  const curDomain = envArr.find(e => e.env === curEnv)
  const info = extractHttpInfo(curDomain?.domain)
  form.ip = info?.ip || ' '
  form.protocol = info?.protocol || ''
  form.port = info?.port || ''
  form.suffix = info?.suffix || ''
}

const requestUrl = computed(() => `${form.protocol}://${form.ip}${form.port ? `:${form.port}` : ''}/${form.suffix}`)

function extractHttpInfo(url) {
  const regex = /^(?<protocol>[^:]+):\/\/(?<ip>[^:\/]+)(?::(?<port>\d+))?(?:\/(?<suffix>.+))?/
  const match = url.match(regex)
  if (match) {
    return {
      protocol: match.groups.protocol,
      ip: match.groups.ip,
      port: match.groups.port || '',
      suffix: match.groups.suffix || '',
    }
  }
  else {
    return {}
  }
}

// 确定
function handleConfirm() {
  showModal.value = false
  localStorage.setItem(
    'requestUrl',
    JSON.stringify({
      env: form.env,
      ip: form.ip,
      protocol: form.protocol,
      port: form.port,
      suffix: form.suffix,
      url: requestUrl.value,
    }),
  )
  // 重新登录
  location.reload()
}
// 获取git分支
function getGitBranch() {
  return CURRENT_VERSION
}
</script>

<template>
  <div v-if="isDev">
    <div class="urlset fixed bottom-10 right-10 z-50">
      <el-affix target=".urlset" position="bottom">
        <el-button type="primary" size="default" :icon="Edit" circle @click="showModal = true" />
      </el-affix>
    </div>
    <el-dialog v-model="showModal" title="开发地址切换" width="600">
      <el-form :model="form" label-width="120px">
        <el-form-item label="目前请求地址:">
          <span class="text-red-500 font-bold"> {{ getBaseUrl() }}</span>
        </el-form-item>
        <el-form-item label="开发环境">
          <el-select v-model="form.env" placeholder="选择开发环境" clearable @change="getRequestData()">
            <el-option v-for="item in envArr" :key="item.env" :label="item.env" :value="item.env" />
          </el-select>
        </el-form-item>
        <el-form-item label="请求地址">
          <el-col :span="5">
            <el-input v-model="form.protocol" :disabled="form.env !== '自定义'" />
          </el-col>
          <span class="px-1">: //</span>
          <el-col :span="10">
            <el-input v-model="form.ip" :disabled="form.env !== '自定义'" />
          </el-col>
          <span class="px-1">:</span>
          <el-col :span="5">
            <el-input v-model="form.port" :disabled="form.env !== '自定义'" />
          </el-col>
        </el-form-item>
        <el-form-item label="api前缀">
          <el-input v-model="form.suffix" :disabled="form.env !== '自定义'" />
        </el-form-item>
        <el-form-item label="请求地址：">
          {{ requestUrl }}
        </el-form-item>
        <el-form-item label="">
          <span class="text-red-500"> {{ getGitBranch() }}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showModal = false">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
