kind: pipeline
type: docker
name: 测试环境

clone:
  depth: 10
  disable: false # 启用代码拉取

# 挂载 host 缓存目录
volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache

steps:
  - name: 企业微信开始通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}}
        {{- if eq .Build.Branch "dev"}}
        {{- $env = "前端测试环境"}}
        {{-  else if eq .Build.Branch "main"}}
        {{- $env = "前端正式环境"}}
        {{- else if eq .Build.Branch "pre"}}
        {{- $env = "前端预发布环境"}}
        {{- end}}
        {{- if ne .Build.Tag ""}}
        {{- $env = "前端生产环境"}}
        {{- end}}
        {{- if eq .Build.Tag ""}}
        构建人: {{- .Build.AuthorName }}({{- .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        {{- end -}}
        > 正在构建: {{- .Repo.Namespace }}/{{ .Repo.Name }}
        执行编号: {{- .Build.Number }}
        其他吊毛注意别推代码
        <font color="comment">{{- .Build.Message -}}</font>
    when:
      status:
        - started

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: build
    image: node:alpine
    pull: if-not-exists
    commands:
      - export NODE_OPTIONS=--max_old_space_size=7000
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - npm config set registry https://registry.npmmirror.com
      - npm install -g pnpm
      - pnpm config set registry https://registry.npmmirror.com
      - pnpm i
      - pnpm buildtest

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: TEST_TARGET_PATH
      source: dist/*
      overwrite: true

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}}
        {{- if eq .Build.Branch "dev"}}
        {{- $env = "前端测试环境"}}
        {{-  else if eq .Build.Branch "main"}}
        {{- $env = "前端正式环境"}}
        {{- else if eq .Build.Branch "pre"}}
        {{- $env = "前端预发布环境"}}
        {{- end}}
        {{- if ne .Build.Tag ""}}
        {{- $env = "前端生产环境"}}
        {{- end}}
        {{- if eq .Build.Status "success"}}
        {{- $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{- $env }} - <font color="red">**[失败]**</font>
        {{- end}}
        仓库:`{{- .Repo.Namespace }}/{{ .Repo.Name }}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        标签: [{{- .Build.Tag }}-{{- .Build.ShortCommit }}]({{- .Build.Link}})
        {{- if eq .Build.Tag ""}}
        构建人: {{- .Build.AuthorName }}({{- .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        {{- end -}}
        执行编号: {{- .Build.Number }}

        > <font color="comment">{{- .Build.Message -}}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - dev
  event:
    - push

---
kind: pipeline
type: docker
name: 致盛正式环境

clone:
  depth: 10
  disable: false # 启用代码拉取

# 挂载 host 缓存目录
volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache


steps:
  - name: 企业微信开始通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}}
        {{- if eq .Build.Branch "dev"}}
        {{- $env = "前端测试环境"}}
        {{-  else if eq .Build.Branch "main"}}
        {{- $env = "前端致盛正式环境"}}
        {{- else if eq .Build.Branch "zhisheng"}}
        {{- $env = "前端预发布环境"}}
        {{- end}}
        {{- if ne .Build.Tag ""}}
        {{- $env = "前端生产环境"}}
        {{- end}}
        {{- if eq .Build.Tag ""}}
        构建人: {{- .Build.AuthorName }}({{- .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        {{- end -}}
        > 正在构建: {{- .Repo.Namespace }}/{{ .Repo.Name }}
        执行编号: {{- .Build.Number }}
        其他吊毛注意别推代码
    when:
      status:
        - started

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: build
    image: node:alpine
    pull: if-not-exists
    commands:
      - export NODE_OPTIONS=--max_old_space_size=6144
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - npm config set registry https://registry.npmmirror.com
      - npm install -g pnpm
      - pnpm config set registry https://registry.npmmirror.com
      - pnpm i
      - pnpm buildpre

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: PRE_TARGET_PATH
      source: dist/*
      overwrite: true

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}}
        {{- if eq .Build.Branch "dev"}}
        {{- $env = "前端测试环境"}}
        {{-  else if eq .Build.Branch "main"}}
        {{- $env = "前端致盛正式环境"}}
        {{- else if eq .Build.Branch "zhisheng"}}
        {{- $env = "前端预发布环境"}}
        {{- end}}
        {{- if ne .Build.Tag ""}}
        {{- $env = "前端生产环境"}}
        {{- end}}
        {{- if eq .Build.Status "success"}}
        {{- $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{- $env }} - <font color="red">**[失败]**</font>
        {{- end}}
        仓库:`{{- .Repo.Namespace }}/{{ .Repo.Name }}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        标签: [{{- .Build.Tag }}-{{- .Build.ShortCommit }}]({{- .Build.Link}})
        {{- if eq .Build.Tag ""}}
        构建人: {{- .Build.AuthorName }}({{- .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        {{- end -}}
        执行编号: {{- .Build.Number }}

        > <font color="comment">{{- .Build.Message -}}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - zhisheng
  event:
    - push

---
kind: pipeline
type: docker
name: 前端科顿易布环境

clone:
  depth: 10
  disable: false # 启用代码拉取

# 挂载 host 缓存目录
volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache

steps:
  - name: 企业微信开始通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}}
        {{- if eq .Build.Branch "dev"}}
        {{- $env = "前端测试环境"}}
        {{-  else if eq .Build.Branch "main"}}
        {{- $env = "前端正式环境"}}
        {{- else if eq .Build.Branch "pre"}}
        {{- $env = "前端预发布环境"}}
        {{- else if eq .Build.Branch "kdyb"}}
        {{- $env = "前端科顿易布环境"}}
        {{- end}}
        {{- if ne .Build.Tag ""}}
        {{- $env = "前端生产环境"}}
        {{- end}}
        {{- if eq .Build.Tag ""}}
        构建人: {{- .Build.AuthorName }}({{- .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        {{- end -}}
        > 正在构建: {{- .Repo.Namespace }}/{{ .Repo.Name }}
        执行编号: {{- .Build.Number }}
        其他吊毛注意别推代码
    when:
      status:
        - started

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: build
    image: node:alpine
    pull: if-not-exists
    commands:
      - export NODE_OPTIONS=--max_old_space_size=6144
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - npm config set registry https://registry.npmmirror.com
      - npm install -g pnpm
      - pnpm config set registry https://registry.npmmirror.com
      - pnpm i
      - pnpm buildkdyb

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: KDYB_HOST
      username:
        from_secret: KDYB_USERNAME
      password:
        from_secret: KDYB_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: KDYB_TARGET_PATH
      source: dist/*
      overwrite: true

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}}
        {{- if eq .Build.Branch "dev"}}
        {{- $env = "前端测试环境"}}
        {{-  else if eq .Build.Branch "main"}}
        {{- $env = "前端正式环境"}}
        {{- else if eq .Build.Branch "pre"}}
        {{- $env = "前端预发布环境"}}
        {{- else if eq .Build.Branch "kdyb"}}
        {{- $env = "前端科顿易布环境"}}
        {{- end}}
        {{- if ne .Build.Tag ""}}
        {{- $env = "前端生产环境"}}
        {{- end}}
        {{- if eq .Build.Status "success"}}
        {{- $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{- $env }} - <font color="red">**[失败]**</font>
        {{- end}}
        仓库:`{{- .Repo.Namespace }}/{{ .Repo.Name }}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        标签: [{{- .Build.Tag }}-{{- .Build.ShortCommit }}]({{- .Build.Link}})
        {{- if eq .Build.Tag ""}}
        构建人: {{- .Build.AuthorName }}({{- .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        {{- end -}}
        执行编号: {{- .Build.Number }}

        > <font color="comment">{{- .Build.Message -}}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - kdyb
  event:
    - push


---
kind: pipeline
type: docker
name: 正式环境TAG

clone:
  depth: 10
  disable: false # 启用代码拉取

volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache

steps:
  - name: 企业微信开始通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env = "前端生产环境" -}}
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        > 正在构建: {{- .Repo.Namespace }}/{{ .Repo.Name }}
        执行编号: {{- .Build.Number }}
        标签版本: {{- .Build.Tag }}
        其他吊毛注意别推代码
    when:
      status:
        - started

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: build
    image: node:alpine
    pull: if-not-exists
    commands:
      - export NODE_OPTIONS=--max_old_space_size=8192  # 生产环境给更大内存
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - npm config set registry https://registry.npmmirror.com
      - npm install -g pnpm
      - pnpm config set registry https://registry.npmmirror.com
      - pnpm i
      - pnpm buildpre  # 使用生产环境构建命令

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules

  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: TEST_HOST  # 使用生产环境的主机
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: PROD_TARGET_PATH  # 使用生产环境的目标路径
      source: dist/*
      overwrite: true

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env = "前端生产环境" -}}
        {{- if eq .Build.Status "success"}}
        {{- $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{- $env }} - <font color="red">**[失败]**</font>
        {{- end}}
        仓库:`{{- .Repo.Namespace }}/{{ .Repo.Name }}`
        标签: [{{- .Build.Tag }}-{{- .Build.ShortCommit }}]({{- .Build.Link}})
        构建人: {{- .Build.Author }}({{- .Build.AuthorEmail }})
        执行编号: {{- .Build.Number }}

        > <font color="comment">{{- .Build.Message -}}</font>
    when:
      status:
        - success
        - failure

trigger:
  event:
    - tag
