<script setup lang="ts">
import { ref } from 'vue'
import MachineSchedulingDialog from './components/MachineSchedulingDialog.vue'

const dialogVisible = ref(false)
const editDialogVisible = ref(false)

const openDialog = () => {
  dialogVisible.value = true
}

const openEditDialog = () => {
  editDialogVisible.value = true
}

const handleSave = (data: any) => {
  console.log('保存数据:', data)
  dialogVisible.value = false
}

const handleEditSave = (data: any) => {
  console.log('保存编辑数据:', data)
  editDialogVisible.value = false
}

// 模拟编辑数据
const mockEditData = {
  unit_id: '普德',
  unit_name: '普德',
  customer_id: 1,
  grey_fabric_id: 1,
  production_number: 'JZS25041801',
  machine_number: '20#',
  schedule_count: 100,
  yarn_batch: 'FL+DG',
  contract_number: '01',
  material_batch: 'xxxxx+xaaa',
  contract_remark: '生产通知单单备注，支持修改',
  yarn_brand: '菲力+大嘴',
  grey_fabric_number: '红布编号',
  grey_fabric_name: '40S高密101上下牙 38*18G',
  needle_count: '38寸18针',
  grey_fabric_color: '',
  customer_name: '测试客户',
  gray_fabric_color_id: 1,
  customer_code: 'TEST001',
  style_no: 'STYLE001',
  yarn_name: '40S高密棉菲力xxxx白色+30D氨纶大嘴xaaa',
  production_count: 240,
  scheduled_count: 17,
  unscheduled_count: 223,
}
</script>

<template>
  <div class="test-page">
    <div class="page-header">
      <h1>机台排产弹窗测试页面</h1>
      <el-button type="primary" @click="openDialog">
        打开机台排产弹窗（添加模式）
      </el-button>
      <el-button type="success" style="margin-left: 10px;" @click="openEditDialog">
        打开机台排产弹窗（编辑模式）
      </el-button>
    </div>

    <div class="instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>点击"打开机台排产弹窗（添加模式）"按钮打开添加弹窗</li>
        <li>点击"打开机台排产弹窗（编辑模式）"按钮打开编辑弹窗</li>
        <li>在添加模式下，点击"生产单号"输入框会弹出选择生产通知单的弹窗</li>
        <li>在编辑模式下，生产单号、机台号、合同序号字段不能修改</li>
        <li>在选择弹窗中可以搜索和选择生产通知单</li>
        <li>选择后会自动填充相关信息到表单中</li>
        <li>表单使用了el-row和el-col布局，符合设计要求</li>
      </ul>
    </div>

    <!-- 机台排产弹窗（添加模式） -->
    <MachineSchedulingDialog
      v-model="dialogVisible"
      mode="add"
      @save="handleSave"
    />

    <!-- 机台排产弹窗（编辑模式） -->
    <MachineSchedulingDialog
      v-model="editDialogVisible"
      mode="edit"
      :production-data="mockEditData"
      @save="handleEditSave"
    />
  </div>
</template>

<style lang="scss" scoped>
.test-page {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;

    h1 {
      margin: 0;
      color: #303133;
    }
  }

  .instructions {
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;

    h3 {
      margin-top: 0;
      color: #606266;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
      }
    }
  }
}
</style>
