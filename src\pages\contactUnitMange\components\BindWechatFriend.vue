<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { Contact } from './SelectWechatFriendLink.vue'
import Table from '@/components/Table.vue'
import type { TableColumn, TableConfig } from '@/components/Table/type'
import { DEFAULT_TABLE_CONFIG } from '@/components/Table/constant'
import { GetQywxCustomers } from '@/api/qywx'
import { getFilterData } from '@/common/util'

const emits = defineEmits(['handleSure'])
const showModal = ref(false)
const searchForm = reactive({
  keyword: '',
  bindOther: false,
})

// 选中的数据
const selectedRow = ref<Contact[]>([] as Contact[])
const { data, fetchData, loading, success, msg, page, size, total, handleCurrentChange, handleSizeChange } = GetQywxCustomers()
const tablesRef = ref()
// 表格配置
const tableConfig = computed<TableConfig>(() => ({
  ...DEFAULT_TABLE_CONFIG,
  editConfig: { enabled: false },
  loading: loading.value,
  showCheckBox: true, // 启用多选
  height: '100%',
  page: page.value,
  size: size.value,
  total: total.value,
  showPagition: true,
  show_footer: false,
  handleSizeChange: async (arg) => {
    await handleSizeChange(arg)
    data.value.list?.forEach((item) => {
      item.selected = selectedRow.value.some(it => it.id === item.id)
    })
  },
  handleCurrentChange: async (arg) => {
    await handleCurrentChange(arg)
    data.value.list?.forEach((item) => {
      item.selected = selectedRow.value.some(it => it.id === item.id)
    })
  },
  checkboxConfig: {
    checkField: 'selected',
    checkMethod: ({ row }) => {
      return !row.bound
    },
  },
  handAllSelect: () => {
    selectedRow.value = tablesRef.value.tableRef.getCheckboxRecords()
  },
  handleSelectionChange: () => {
    selectedRow.value = tablesRef.value.tableRef.getCheckboxRecords()
  },
}))
async function getData() {
  await fetchData(getFilterData({
    name: searchForm.keyword,
    all_customers: searchForm.bindOther,
  }))
  if (!success.value)
    return ElMessage.error(msg.value)
  data.value.list?.forEach((item) => {
    item.selected = selectedRow.value.some(it => it.id === item.id)
  })
}

watch(showModal, (val) => {
  if (val)
    getData()
})

// 修改 handleSearch 方法
function handleSearch() {
  getData()
}
// 表格列定义
const columns: TableColumn[] = [
  {
    title: '头像',
    field: 'avatar',
    width: 80,
    isEdit: false,
    align: 'center',
    soltName: 'avatar',
  },
  {
    title: '微信名称',
    field: 'wxName',
    isEdit: false,
    minWidth: 120,
    align: 'center',
    soltName: 'wxName',
  },
]

// 来源类型常量
const SOURCE_TYPES = {
  wx: {
    text: '微信',
    color: '#70b603',
  },
  qywx: {
    text: '企业微信',
    color: '#b67903',
  },
} as const

// 确认选择
function handleSubmit() {
  if (!selectedRow.value) {
    ElMessage.warning('请选择要绑定的微信好友')
    return
  }
  // TODO: 提交选中的好友
  showModal.value = false
  emits('handleSure', selectedRow.value)
}

// 暴露方法给父组件调用
defineExpose({
  showModal,
  selectedRow,
})
</script>

<template>
  <vxe-modal
    v-model="showModal"
    title="绑定微信好友"
    width="800"
    height="800"
    :mask="false"
    :lock-view="false"
    show-footer
    :esc-closable="true"
    resize
  >
    <div class="flex flex-col h-full">
      <!-- 搜索框 -->
      <el-form inline class="form-inline" :model="searchForm" @submit.prevent>
        <el-form-item label="名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="输入用户名/备注名搜索"
            clearable
            @input="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-has="'bindOtherWxFriends'" class="ml-2">
          <el-checkbox v-model="searchForm.bindOther" @change="handleSearch">
            绑定其他微信好友
          </el-checkbox>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <Table
        ref="tablesRef"
        :config="tableConfig"
        :column-list="columns"
        :table-list="data?.list"
        class="flex-1 mt-2"
      >
        <template #avatar="{ row }">
          <div class="flex items-center justify-center">
            <img
              :src="row.avatar"
              class="w-8 h-8 rounded-full"
              alt="avatar"
            >
          </div>
        </template>
        <template #wxName="{ row }">
          <div class="flex flex-col justify-center">
            <div class="flex items-center gap-1">
              <span>{{ row.name }}</span>
              <span
                class="text-sm"
                :style="{ color: SOURCE_TYPES[row.type as keyof typeof SOURCE_TYPES]?.color }"
              >
                @{{ SOURCE_TYPES[row.type as keyof typeof SOURCE_TYPES]?.text }} {{ row.corp_full_name ? `(${row.corp_full_name})` : '' }}
              </span>
            </div>
          </div>
        </template>
      </Table>
    </div>

    <template #footer>
      <el-button type="primary" :loading="loading" :disabled="!selectedRow?.length" @click="handleSubmit">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style scoped>
:deep(.vxe-modal--body) {
  padding: 0;
}
.form-inline{
  display: flex;
}
.form-inline .el-form-item{
  display: flex !important;
}

 .el-input {
  --el-input-width: 220px;
}
</style>
