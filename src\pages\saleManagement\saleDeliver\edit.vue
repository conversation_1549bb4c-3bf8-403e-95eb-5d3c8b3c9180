<script lang="ts" setup name="SaleDeliverEdit">
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import Big from 'big.js'
import { cloneDeep } from 'lodash-es'
import CancelMargeDialog from '../components/CancelMargeDialog.vue'
import MargeSaleDialog from '../components/MargeSaleDialog.vue'
import {
  breakUpItems,
  getWeightItemList,
  mergeItems,
  updateget,
  updateput,
} from '@/api/saleDeliver'
import { EmployeeType } from '@/common/enum'
import {
  formatCalculate,
  formatDate,
  formatLengthDiv,
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import { deepClone, deleteToast, getCurrentDate, isMainUnit } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import useRouterList from '@/use/useRouterList'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'

// todo: 后续改全局常量，
const CODEGAPMIN = -92233720 // 码单空差最小负数
const SETTLEMENTGAP = -92233720 // 结算空差最小负数

const productTableRef = ref() // 成品表格实例
const tableRef = ref() // 细码表的实例

const routerList = useRouterList()
const data = ref<any>()

const state = reactive<any>({
  form: {
    voucher_number: '', // 凭证单号
    sale_user_id: '', // 销售员
    order_time: '', // 送货日期
    settle_type: '', // 结算类型
    sale_follower_id: '', // 销售跟单
    sale_tax_rate: '',
    order_remark: '', // 单据备注
    sale_mode: '',
  },
  formRules: {
    order_time: [
      { required: true, message: '请选择送货日期', trigger: 'blur' },
    ],
    sale_user_id: [
      { required: true, message: '请选择销售员', trigger: 'blur' },
    ],
    sale_follower_id: [
      { required: true, message: '请选择销售跟单', trigger: 'blur' },
    ],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
  tableList: [],
  fabricList: [],
  multipleSelection: [],
  isShow: false,
})
// 成品信息的所有人配置
const tableConfig = ref({
  showSlotNums: true,
  fieldApiKey: 'SaleDeliverEdit',
  //   showOperate: true,
  //   operateWidth: '80',
  footerMethod: (val: any) => ProductFooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  showSpanHeader: true,
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val),
})

// 细码的配置
const tableConfig_fabric = ref({
  showSlotNums: true,
  footerMethod: (val: any) => FooterMethod(val),
})

const { fetchData: getFetch, data: fabricList, success, msg } = updateget() // 获取成品数据

const { fetchData: getXima, data: ximaData } = getWeightItemList() // 获取细码数据

const activeRowId = ref<any>(0)
const activeXiMaList = ref<any[]>([])
// 成品信息的某个回调 暂时还不知道用于启动谁的
async function cellDBLClickEvent(val: any) {
  activeRowId.value = val.row.id
  // 已存在细码
  if (val.row.fc_data_list?.length) {
    activeXiMaList.value = val.row.fc_data_list
  }
  else {
    await getXima({ detail_id: val.row.id })
    // 弄一个新的数组存数据，不对原始数据进行修改
    activeXiMaList.value = cloneDeep(ximaData.value.list)
    // 处理一下需要输入的地方的进位
    activeXiMaList.value.map((item: any) => {
      item.weight_error = formatWeightDiv(item?.weight_error || 0)
      item.settle_error_weight = formatWeightDiv(item?.settle_error_weight || 0)
      return item
    })
  }
  state.isShow = true
}

// 细码的结算空差和码单空差输入发生变化的时候
function updateXimaInput(row: any) {
  // 把两个输入框的值同步到成品列表中
  state.tableList?.map((item: any) => {
    if (item.id === activeRowId.value) {
      // 判断有没有fc_data_list，没有的话就给他赋一个空数组
      if (!item.fc_data_list) {
        item.fc_data_list = []
        item.fc_data_list.push(row)
      }
      else {
        // 有的话就更新对应的fc_list和weight_error和settle_error_weight
        let isAdd = false
        item.fc_data_list.forEach((fc: any) => {
          if (fc.id === row.id) {
            fc = row
            isAdd = true
          }
        })
        // 如果不存在，就添加到数组中
        if (!isAdd)
          item.fc_data_list.push(row)
      }
      // 添加weight_error和settle_error_weight到对应的成品中
      let allWeightError = 0
      let allSettleErrorWeight = 0
      activeXiMaList.value.forEach((row: any) => {
        allWeightError += Number(row.weight_error)
        allSettleErrorWeight += Number(row.settle_error_weight)
      })

      item.weight_error = formatWeightMul(allWeightError) //  更新成品的码单空差
      item.settle_error_weight = formatWeightMul(allSettleErrorWeight) // 更新成品的结算空差

      item.actually_weight = item.weight - formatWeightMul(allWeightError) //  更新子数组的码单数量
      item.settle_weight = item.weight - formatWeightMul(allSettleErrorWeight) - formatWeightMul(allWeightError) // 更新子数组的结算数量
    }
    return item
  })

  // 计算当前行的码单数量，结算数量
  row.actually_weight = row.base_unit_weight - formatWeightMul(row.weight_error) //   这段没生效
  row.settle_weight = row.actually_weight - formatWeightMul(row.settle_error_weight) // 这段也没生效
  nextTick(() => {
    // 两个表格的footer都要更新
    productTableRef.value.tableRef?.updateFooter()
    tableRef.value.tableRef?.updateFooter()
  })
}

const route = useRoute()

onMounted(() => {
  getInfomation()
})

const checkbox = ref(true)

// 请求信息回来的时候执行
async function getInfomation() {
  await getFetch({ id: route.query.id })
  if (!success.value)
    return ElMessage.error(msg.value)

  if (fabricList.value) {
    data.value = fabricList.value
    state.form.order_remark = data.value.order_remark
    state.form.sale_tax_rate = formatPriceDiv(data.value.tax_rate)
    checkbox.value = data.value.is_with_tax_rate
    state.form.voucher_number = fabricList.value.voucher_number
    if (fabricList.value.order_time === '')
      state.form.order_time = getCurrentDate()
    else state.form.order_time = fabricList.value.order_time

    state.form.settle_type = fabricList.value.settle_type
    state.form.sale_user_id = fabricList.value.sale_user_id
    state.form.sale_follower_id = fabricList.value.sale_follower_id
    state.form.sale_mode = fabricList.value.sale_mode

    state.tableList = fabricList.value.items
    state.tableList?.map((item: any) => {
      item.sale_tax_rate = formatPriceDiv(item.sale_tax_rate)
      item.other_price = formatPriceDiv(item.other_price)
      item.offset_sale_price = formatUnitPriceDiv(item.offset_sale_price)
      item.sale_price = formatUnitPriceDiv(item.sale_price)
      item.offset_length_cut_sale_price = formatUnitPriceDiv(item.offset_length_cut_sale_price)
      item.standard_sale_price = formatUnitPriceDiv(item.standard_sale_price)
      item.length_cut_sale_price = formatUnitPriceDiv(item.length_cut_sale_price)
      item.settle_price = formatPriceDiv(item.settle_price)
      // 如果结算单位（辅助单位）初始为0或者空，先绑定出仓单位
      item.auxiliary_unit_id = item.auxiliary_unit_id || item.measurement_unit_id
      item.auxiliary_unit_name = item.auxiliary_unit_name || item.measurement_unit_name

      item = conductUnitPrice(item)
      return item
    })
  }
}
function handleBlur(
  row: any,
  type:
    | 'offset_sale_price'
    | 'sale_price'
    | 'offset_length_cut_sale_price'
    | 'length_cut_sale_price',
) {
  switch (type) {
    // 优惠单价 数量单位
    case 'offset_sale_price':
      row.sale_price = currency(row.standard_sale_price).subtract(
        row.offset_sale_price,
      )
      if (row.offset_length_cut_sale_price)
        row.offset_length_cut_sale_price = 0

      handleBlur(row, 'sale_price')
      break
    // 优惠单价 辅助数量单位
    case 'offset_length_cut_sale_price':
      row.length_cut_sale_price = currency(
        row.standard_length_cut_sale_price,
      ).subtract(row.offset_length_cut_sale_price)
      if (row.offset_sale_price)
        row.offset_sale_price = 0

      handleBlur(row, 'length_cut_sale_price')
      break
    //   销售单价 数量单位
    case 'sale_price':
      if (row.length_cut_sale_price)
        row.length_cut_sale_price = 0

      break
    //   销售单价 辅助数量单位
    case 'length_cut_sale_price':
      if (row.sale_price)
        row.sale_price = 0

      break
  }
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['weight_error'].includes(column.property)) {
        // return `${formatWeightDiv(sumNum(data, 'weight_error') as any)}`
        // 接收的时候已经处理过了，这两个字段就不用再处理了
        return `${sumNum(data, 'weight_error') as any}`
      }
      if (['actually_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_weight') as any)}`

      if (['settle_error_weight'].includes(column.property)) {
        // return `${formatWeightDiv(sumNum(data, 'settle_error_weight') as any)}`
        return `${sumNum(data, 'settle_error_weight') as any}`
      }
      if (['settle_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_weight') as any)}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length') as any)}`

      if (['settle_price'].includes(column.property))
        return `${sumNum(data, 'settle_price') as any}`

      return null
    }),
  ]
}
function ProductFooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['weight_error'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight_error') as any)}`

      if (['actually_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_weight') as any)}`

      if (['settle_error_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_error_weight') as any)}`

      if (['settle_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'settle_weight') as any)}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length') as any)}`

      if (['settle_price'].includes(column.property))
        return `${sumNum(data, 'settle_price') as any}`

      return null
    }),
  ]
}

const ruleFormRef = ref()

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// const handDelete = (index: number) => {
//   state.tableList.splice(index, 1)
// }

// todo: 佩奇的结算金额
// 结算金额只有两种计算方法: 如果A参数为零，那么就采取下另外一种算法
//                    A                   B
//          1,(出仓数量=>结算数量) * (数量单价=>销售单价) + 其他金额 = 结算金额
//          2,(出仓数量=>结算长度) * (长度单价=>销售单价) + 其他金额 = 结算金额
watch(
  [() => state.tableList],
  () => {
    // 其他金额 : other_price
    // 结算数量 : settle_weight |   数量单价的销售单价 : sale_price
    // 结算长度 : length        |   长度单价的销售单价 : length_cut_sale_price
    if (state.tableList?.length > 0) {
      state.tableList.map((item: any) => {
        let settle_money = 0 // 声明结算金额
        // 主单位结算金额 = 主单位结算单价 + 主单位结算数量
        const measurementMoney = new Big(Number(item.sale_price)).times(formatWeightDiv(item.settle_weight))

        // 辅助单位结算金额 = 辅助单位结算单价 * 辅助单位结算数量
        const auxiliaryMoney = new Big(
          Number(item.length_cut_sale_price),
        ).times(formatLengthDiv(item.length))

        // 结算金额 = 主单位结算金额 + 辅助单位结算金额 + 其他金额
        settle_money = new Big(measurementMoney)
          .plus(auxiliaryMoney)
          .plus((Number(item.other_price)))
          .toNumber()
        item.settle_price = formatCalculate(settle_money)
        return item
      })
      nextTick(() => {
        productTableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const bulkSetting = reactive<any>({})

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
  state.multipleSelection = []
}

const {
  fetchData: addPost,
  data: addData,
  success: addSuccess,
  msg: addMsg,
} = updateput()

async function handleSure() {
  const list = deepClone(state.tableList)
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    if (!item.auxiliary_unit_id)
      return ElMessage.error(`成品编号为${item.product_code}的数据,结算单位不能为空`)

    if (!Number(item.length) && !isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,辅助数量不能为空且不能为0`)

    if (item.sale_price === '' && isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,销售单价不能为空`)

    if (item.length_cut_sale_price === '' && !isMainUnit(item))
      return ElMessage.error(`成品编号为${item.product_code}的数据,销售单价不能为空`)

    list[i].other_price = formatPriceMul(list[i].other_price)
    list[i].offset_sale_price = formatUnitPriceMul(list[i].offset_sale_price)
    list[i].sale_price = formatUnitPriceMul(list[i].sale_price)
    list[i].standard_sale_price = formatUnitPriceMul(list[i].standard_sale_price)
    list[i].offset_length_cut_sale_price = formatUnitPriceMul(
      list[i].offset_length_cut_sale_price,
    )
    list[i].length_cut_sale_price = formatUnitPriceMul(
      list[i].length_cut_sale_price,
    )
    list[i].sale_tax_rate = formatPriceMul(list[i].sale_tax_rate)
    list[i].settle_price = formatPriceMul(list[i].settle_price)
    if (list[i].fc_data_list) {
      list[i].fc_data_list.forEach((item: any) => {
        item.src_detail_fc_id = item.id
        item.id = item?.order_detail_fc_id || 0
        item.src_detail_id = list[i].src_detail_id
        item.order_detail_id = list[i].id
        item.weight_error = formatWeightMul(item.weight_error)
        item.settle_error_weight = formatWeightMul(item.settle_error_weight)
      })
    }
  }
  const query = {
    voucher_number: state.form.voucher_number,
    order_time: formatDate(state.form.order_time),
    sale_user_id: state.form.sale_user_id,
    sale_follower_id: state.form.sale_follower_id,
    sale_mode: state.form.sale_mode,
    settle_type: state.form.settle_type || 0,
    is_with_tax_rate: checkbox.value,
    sale_tax_rate: formatPriceMul(state.form.sale_tax_rate),
    tax_rate: formatPriceMul(state.form.sale_tax_rate),
    order_remark: state.form.order_remark,
    items: list,
    id: Number(route.query.id),
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({
          name: 'SaleDeliverDetail',
          query: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const MargeSaleDialogRef = ref()

const CancelMargeDialogRef = ref()

function handMarge() {
  MargeSaleDialogRef.value.state.showModal = true
  MargeSaleDialogRef.value.state.info = {
    customer_code: fabricList.value?.customer_code,
    customer_name: fabricList.value?.customer_name,
  }
  MargeSaleDialogRef.value.state.should_collect_order_id = Number(
    route.query.id,
  )
  MargeSaleDialogRef.value.state.customer_id = data.value.customer_id
  MargeSaleDialogRef.value.state.sale_system_id = data.value.sale_system_id
  MargeSaleDialogRef.value.state.multipleSelection = []
}

function handRemove() {
  CancelMargeDialogRef.value.state.showModal = true
  CancelMargeDialogRef.value.state.sale_id = Number(route.query.id)
  CancelMargeDialogRef.value.state.multipleSelection = []
}

const {
  fetchData: margeFetch,
  success: margeSuccess,
  msg: margeMsg,
} = mergeItems()

const {
  fetchData: breakFetch,
  success: breakSuccess,
  msg: breakMsg,
} = breakUpItems()

async function handSureMarge(val: any) {
  const res = await deleteToast('确认合并吗？')
  if (res) {
    await margeFetch({ id: Number(route.query.id), ids: val })
    if (margeSuccess.value) {
      ElMessage.success('合并成功')
      MargeSaleDialogRef.value.state.showModal = false
      getInfomation()
    }
    else {
      ElMessage.error(margeMsg.value)
    }
  }
}

async function handSureBreak(val: any) {
  const res = await deleteToast('确认撤销吗？')
  if (res) {
    await breakFetch({ id: Number(route.query.id), ids: val })
    if (breakSuccess.value) {
      ElMessage.success('撤销成功')
      CancelMargeDialogRef.value.state.showModal = false
      getInfomation()
    }
    else {
      ElMessage.error(breakMsg.value)
    }
  }
}

const bulkList = reactive<any>([
  {
    field: 'sale_tax_rate',
    title: '税率',
    component: 'input',
    type: 'float',
  },
  {
    field: 'other_price',
    title: '其他金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])

const columnList = ref([
  {
    title: '成品',
    field: 'A',
    childrenList: [
      {
        field: 'sale_order_no',
        title: '成品销售单号',
        width: 150,
      },
      {
        field: 'fpm_sale_out_order_no',
        title: '成品销售出仓单号',
        width: 150,
      },
      {
        field: 'arrange_order_no',
        title: '配布单号',
        width: 150,
      },
      {
        field: 'warehouse_name',
        title: '仓库名称',
        width: 100,
      },
      {
        field: 'product_code',
        title: '成品编号',
        width: 100,
      },
      {
        field: 'product_name',
        title: '成品名称',
        width: 100,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        width: 100,
      },
      {
        field: 'product_color_code',
        title: '色号',
        width: 100,
      },
      {
        field: 'product_color_name',
        title: '颜色',
        width: 100,
      },
      {
        field: 'dyelot_number',
        title: '染厂缸号',
        width: 100,
      },
      {
        field: 'product_craft',
        title: '成品工艺',
        width: 100,
      },
      {
        field: 'product_level_name',
        title: '成品等级',
        width: 100,
      },
      {
        field: 'product_ingredient',
        title: '成品成分',
        width: 100,
      },
      {
        field: 'product_remark',
        title: '成品备注',
        width: 100,
      },
    ],
  },
  {
    title: '出仓数量',
    field: 'B',
    childrenList: [
      {
        field: 'roll',
        title: '匹数',
        width: 100,
        isPrice: true,
      },
      {
        field: 'weight',
        title: '数量',
        width: 100,
        isWeight: true,
      },
      {
        field: 'weight_error',
        title: '码单空差',
        width: 100,
        isWeight: true,
      },
      {
        field: 'actually_weight',
        title: '码单数量',
        width: 100,
        isWeight: true,
      },
      {
        field: 'settle_error_weight',
        title: '结算空差',
        width: 100,
        isWeight: true,
      },
      {
        field: 'settle_weight',
        title: '结算数量',
        width: 100,
        isWeight: true,
      },
      {
        field: 'length',
        title: '辅助数量',
        width: 100,
        isLength: true,
      },
      {
        field: 'warehouse_out_remark',
        title: '出仓备注',
        width: 100,
      },
      {
        field: 'measurement_unit_name',
        title: '单位',
        width: 100,
      },
    ],
  },
  {
    title: '结算单位',
    field: 'F',
    childrenList: [
      {
        field: 'auxiliary_unit_id',
        title: '结算单位',
        width: 100,
        soltName: 'auxiliary_unit_id',
      },
    ],
  },
  {
    title: '数量单价',
    field: 'C',
    childrenList: [
      {
        field: 'standard_sale_price',
        title: '销售报价',
        width: 100,
      },
      {
        field: 'sale_level_name',
        title: '优惠等级',
        width: 100,
      },
      {
        field: 'offset_sale_price',
        title: '优惠单价',
        width: 100,
      },
      {
        field: 'sale_price',
        title: '销售单价',
        width: 100,
        soltName: 'sale_price',
      },
    ],
  },
  {
    title: '辅助数量单价',
    field: 'D',
    childrenList: [
      {
        field: 'standard_length_cut_sale_price',
        title: '销售报价',
        width: 100,
        isUnitPrice: true,
      },
      {
        field: 'offset_length_cut_sale_price',
        title: '优惠单价',
        width: 100,
      },
      {
        field: 'length_cut_sale_price',
        title: '销售单价',
        width: 100,
        soltName: 'length_cut_sale_price',
      },
    ],
  },
  {
    title: '结算信息',
    field: 'E',
    childrenList: [
      {
        field: 'sale_tax_rate',
        title: '税率',
        width: 100,
        soltName: 'sale_tax_rate',
      },
      {
        field: 'other_price',
        title: '其他金额',
        width: 100,
        soltName: 'other_price',
      },
      {
        field: 'settle_price',
        title: '结算金额',
        width: 100,
        // isPrice: true,
      },
      {
        field: 'remark',
        title: '备注',
        width: 100,
        soltName: 'remark',
      },
    ],
  },
])

// 细码的配置信息
const columnList_fabric = ref([
  {
    field: 'roll',
    title: '匹数',
    width: 150,
    isPrice: true,
  },
  {
    field: 'warehouse_bin_name',
    title: '仓位',
    width: 150,
  },
  {
    field: 'volume_number',
    title: '卷号',
    width: 100,
  },
  {
    field: 'base_unit_weight',
    title: '基本单位数量',
    width: 150,
    isWeight: true,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒重量',
    width: 150,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '码单空差',
    width: 100,
    // isWeight: true,
    soltName: 'weight_error',
  },
  {
    field: 'actually_weight',
    title: '码单数量',
    width: 100,
    isWeight: true,
  },
  {
    field: 'settle_error_weight',
    title: '结算空差',
    width: 100,
    // isWeight: true,
    soltName: 'settle_error_weight',
  },
  {
    field: 'settle_weight',
    title: '结算数量',
    width: 100,
    isWeight: true,
  },
  {
    field: 'length',
    title: '辅助数量',
    width: 100,
    isLength: true,
  },
  {
    field: 'dye_factory_dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    width: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    width: 100,
  },
])

// 根据结算单位是否为主单位显示单价
function conductUnitPrice(item: any) {
  if (isMainUnit(item))
    item.length_cut_sale_price = 0 // 主单位-把辅助单价置0

  else
    item.sale_price = 0 // 辅助单位-把单价置0

  nextTick(() => {
    productTableRef.value.tableRef?.updateFooter()
  })
  return item
}
</script>

<template>
  <!-- 基础信息 -->
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            {{ data?.sale_system_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="送货日期:" required>
          <template #content>
            <el-form-item prop="order_time">
              <el-date-picker
                v-model="state.form.order_time"
                type="date"
                placeholder="送货日期"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货标签:">
          <template #content>
            {{ data?.receive_tag }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户编号:">
          <template #content>
            {{ data?.customer_code }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            {{ data?.customer_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售群体:">
          <template #content>
            {{ data?.sale_group_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算类型:">
          <template #content>
            <el-form-item prop="settle_type">
              <SelectComponents
                v-model="state.form.settle_type"
                api="AdminenumsettleType"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售员:" required>
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectComponents
                v-model="state.form.sale_user_id"
                :query="{ duty: EmployeeType.salesman }"
                api="Adminemployeelist"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售跟单:">
          <template #content>
            <el-form-item prop="sale_follower_id">
              <SelectComponents
                v-model="state.form.sale_follower_id"
                :query="{ duty: EmployeeType.follower }"
                api="Adminemployeelist"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工厂名称:">
          <template #content>
            {{ data?.process_factory_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系人:">
          <template #content>
            {{ data?.contacts }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="联系电话:">
          <template #content>
            {{ data?.receive_phone }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流公司:">
          <template #content>
            {{ data?.logistics_company_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="物流区域:">
          <template #content>
            {{ data?.logistics_area }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="邮费项目:">
          <template #content>
            {{ data?.postage_items_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货地址:">
          <template #content>
            {{ data?.receive_addr }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="内部备注:">
          <template #content>
            {{ data?.internal_remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货备注:">
          <template #content>
            {{ data?.send_product_remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:">
          <template #content>
            <vxe-input
              v-model="state.form.order_remark"
              maxlength="255"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="税率:">
          <template #content>
            <vxe-input
              v-model="state.form.sale_tax_rate"
              type="float"
              clearable
              :disabled="!checkbox"
              placeholder="税率"
              :min="0"
            >
              <template #suffix>
                %
              </template>
            </vxe-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-checkbox v-model="checkbox" label="是否含税" size="large" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:" :copies="2">
          <template #content>
            <SelectSaleMode v-model="state.form.sale_mode" />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <!-- 成品信息 -->
  <FildCard title="成品信息" :tool-bar="true" class="mt-[5px]">
    <template #right-top>
      <!-- <el-checkbox style="margin-right: 10px" v-model="state.isShow">显示细码</el-checkbox> -->
      <el-button type="primary" @click="bulkHand">
        批量操作
      </el-button>
      <el-button type="primary" @click="handRemove">
        撤销合并
      </el-button>
      <el-button type="primary" @click="handMarge">
        合并其他送货单
      </el-button>
    </template>
    <Table
      ref="productTableRef"
      :config="tableConfig"
      :table-list="state.tableList"
      :column-list="columnList"
    >
      <!--       单位 结算单位 -->
      <template #auxiliary_unit_id="{ row }">
        <SelectComponents v-model="row.auxiliary_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice(row)" />
      </template>

      <!--      优惠单价 数量单位 -->
      <template #offset_sale_price="{ row }">
        <vxe-input v-show="isMainUnit(row)" v-model="row.offset_sale_price" type="float" :min="0" clearable @blur="handleBlur(row, 'offset_sale_price')" />
        <span v-show="!isMainUnit(row)">{{ row.offset_sale_price }}</span>
      </template>
      <!--      销售单价 数量单位 -->
      <template #sale_price="{ row }">
        <vxe-input v-show="isMainUnit(row)" v-model="row.sale_price" type="float" :min="0" clearable />
        <span v-show="!isMainUnit(row)">{{ row.sale_price }}</span>
      </template>

      <!--      优惠单价 辅助数量单位 -->
      <template #offset_length_cut_sale_price="{ row }">
        <vxe-input v-show="!isMainUnit(row)" v-model="row.offset_length_cut_sale_price" type="float" :min="0" clearable @blur="handleBlur(row, 'offset_length_cut_sale_price')" />
        <span v-show="isMainUnit(row)">{{ row.offset_length_cut_sale_price }}</span>
      </template>
      <!--      销售单价 辅助数量单位 -->
      <template #length_cut_sale_price="{ row }">
        <vxe-input v-show="!isMainUnit(row)" v-model="row.length_cut_sale_price" type="float" :min="0" clearable />
        <span v-show="isMainUnit(row)">{{ row.length_cut_sale_price }}</span>
      </template>
      <!--      税率 -->
      <template #sale_tax_rate="{ row }">
        <vxe-input
          v-model="row.sale_tax_rate"
          :min="0"
          type="float"
          :disabled="!checkbox"
          clearable
        />
      </template>
      <!--      其他金额 -->
      <template #other_price="{ row }">
        <vxe-input v-model="row.other_price" type="float" clearable />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" clearable />
      </template>
    </Table>
  </FildCard>
  <!-- 细码 -->
  <FildCard v-if="state.isShow" title="细码" :tool-bar="false" class="mt-[5px]">
    <Table
      ref="tableRef"
      :config="tableConfig_fabric"
      :table-list="activeXiMaList"
      :column-list="columnList_fabric"
    >
      <template #weight_error="{ row }">
        <vxe-input
          v-model="row.weight_error"
          :min="CODEGAPMIN"
          type="float"
          clearable
          @change="updateXimaInput(row)"
        />
      </template>
      <template #settle_error_weight="{ row }">
        <vxe-input
          v-model="row.settle_error_weight"
          :min="SETTLEMENTGAP"
          type="float"
          clearable
          @change="updateXimaInput(row)"
        />
      </template>
    </Table>
  </FildCard>
  <MargeSaleDialog ref="MargeSaleDialogRef" @handle-sure="handSureMarge" />
  <CancelMargeDialog ref="CancelMargeDialogRef" @handle-sure="handSureBreak" />
  <BulkSetting
    v-model="bulkSetting"
    :column-list="bulkList"
    :show="bulkShow"
    @submit="bulkSubmit"
    @close="handBulkClose"
  />
</template>

<style></style>
