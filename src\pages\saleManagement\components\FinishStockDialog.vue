<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import OccupyStockDialog from './OccupyStockDialog.vue'
import { getStockProductDyelotNumberDetailList } from '@/api/fpPurchaseReturnDeliverGodown'
import { getStockProductDropdownList } from '@/api/productOutStock'
import { formatHashTag, formatPriceDiv, formatWeightDiv } from '@/common/format'
import { getFilterData, resetData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import FildCard from '@/components/FildCard.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import type { TableColumn } from '@/components/Table/type'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'
import { InformationViewTypeEnum } from '@/enum/productEnum'

export interface Props {
  obj: any
}

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    product_id: '',
    customer_id: '',
    product_color_id: '',
    warehouse_id: '',
    product_level_id: '',
    dyelot_number: '',
    available_only: false,
    radioValue: InformationViewTypeEnum.Stock,
    with_price: true,
    sale_system_id: '',
    sale_customer_id: '',
  },
  customer_name: '',
  showModal: false,
  modalName: '根据库存添加',
  multipleSelection: [],
  list: [],
})

// 当前是否按缸号查询
const isViewDyeltNumber = computed(() => {
  return state.filterData.radioValue === InformationViewTypeEnum.DyeltNumber
})

const componentRemoteSearch = reactive({
  finish_product_code: '',
  finish_product_name: '',
  customer_name: '',
  product_color_name: '',
  product_color_code: '',
  color_name: '',
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  success: success1,
  msg: msg1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getStockProductDyelotNumberDetailList()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  success: success2,
  msg: msg2,
  size: size2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = getStockProductDropdownList()

const tableConfig = reactive<any>({
  loading: isViewDyeltNumber.value ? loading1 : loading2,
  showPagition: true,
  showSlotNums: true,
  page: isViewDyeltNumber.value ? page1 : page2,
  size: isViewDyeltNumber.value ? size1 : size2,
  total: isViewDyeltNumber.value ? total1 : total2,
  showCheckBox: true,
  showSort: false,
  height: 'auto',
  filterStatus: false,
  handleSizeChange: (val: number) => (state.filterData.radioValue === '1' ? handleSizeChange1(val) : handleSizeChange2(val)),
  handleCurrentChange: (val: number) => (state.filterData.radioValue === '1' ? handleCurrentChange1(val) : handleCurrentChange2(val)),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  fieldApiKey: 'FinishStockDrog',
  checkboxConfig: {
    highlight: true,
    reserve: true,
    // checkMethod: ({ row }: any) => {
    //   return row.available_roll !== 0 || row.available_weight !== 0
    // },
  },
})

// function handChange(val: any) {
//   state.filterData.radioValue = val.toString()
// }

async function getData() {
  const query = {
    ...state.filterData,
  }
  if (isViewDyeltNumber.value) {
    await fetchData1(getFilterData(query))
    if (!success1.value)
      return ElMessage.error(msg1.value)
  }
  else {
    await fetchData2(getFilterData(query))
    if (!success2.value)
      return ElMessage.error(msg2.value)
  }

  state.list = isViewDyeltNumber.value ? data1.value?.list : data2.value?.list
  // state.list.map((item: any) => {
  //   return {
  //     ...item,
  //     // 数量单价 辅助数量单价
  //     roll: formatTwoDecimalsDiv(Number(item.roll)), // 100
  //     weight: formatWeightDiv(Number(item.weight)), // weight
  //     length: formatTwoDecimalsDiv(Number(item.length)), // 100
  //     available_roll: formatTwoDecimalsDiv(Number(item.available_roll)), // 100
  //     unit_price: formatPriceDiv(Number(item.unit_price)) || 0,
  //     length_unit_price: formatPriceDiv(Number(item.length_unit_price)) || 0,
  //   }
  // })
  tableConfig.total = isViewDyeltNumber.value ? total1 : total2
  tableConfig.page = isViewDyeltNumber.value ? page1 : page2
  tableConfig.size = isViewDyeltNumber.value ? size1 : size2
}
watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

watch(
  () => data1.value.list,
  () => {
    state.list = data1.value.list
  },
)

watch(
  () => data2.value.list,
  () => {
    state.list = data2.value.list
  },
)

const columnList = ref<TableColumn[]>([
  {
    field: 'product_code',
    minWidth: 150,
    title: '成品名称',
    soltName: 'product_code',
    fixed: 'left',
  },
  {
    field: 'customer_name',
    minWidth: 100,
    title: '所属客户',
  },
  {
    field: 'product_color_code',
    minWidth: 100,
    title: '色号颜色',
    soltName: 'product_color_code',
  },
  {
    field: 'warehouse_name',
    minWidth: 100,
    title: '所属仓库',
  },
  {
    field: 'dyelot_number',
    minWidth: 100,
    title: '染厂缸号',
  },
  {
    field: 'remark',
    minWidth: 100,
    title: '库存备注',
  },
  {
    field: 'finish_product_craft',
    minWidth: 100,
    title: '成品工艺',
    soltName: 'product_craft',
  },
  {
    field: 'product_level_name',
    minWidth: 100,
    title: '成品等级',
  },
  {
    field: 'finish_product_ingredient',
    minWidth: 100,
    title: '成品成分',
    soltName: 'product_ingredient',
  },
  {
    field: 'product_remark',
    minWidth: 100,
    title: '成品备注',
  },
  // {
  //   field: 'unit_price',
  //   minWidth: 100,
  //   title: '数量单价',
  //   isUnitPrice: true,
  // },
  // {
  //   field: 'length_unit_price',
  //   minWidth: 100,
  //   title: '辅助数量单价',
  //   isUnitPrice: true,
  // },
  {
    field: 'stock_roll',
    minWidth: 70,
    title: '库存匹数',
    fixed: 'right',
    soltName: 'stock_roll',
  },
  {
    field: 'weight',
    minWidth: 80,
    title: '数量',
    fixed: 'right',
    soltName: 'weight',
  },
  {
    field: 'length',
    minWidth: 70,
    title: '辅助数量',
    fixed: 'right',
    isLength: true,
  },
  {
    field: 'available_roll',
    soltName: 'available_roll',
    minWidth: 80,
    title: '可用匹数',
    fixed: 'right',
  },
  {
    field: 'available_weight',
    soltName: 'available_weight',
    minWidth: 70,
    title: '可用数量',
    fixed: 'right',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择数据')
  }
  else {
    state.showModal = false
    emits('handleSure', state.multipleSelection)
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
  state.filterData.radioValue = InformationViewTypeEnum.Stock
}

// const proRef1 = ref()
// const proRef2 = ref()
function changePro(data: any) {
  componentRemoteSearch.finish_product_name = data.finish_product_name
  componentRemoteSearch.finish_product_code = data.finish_product_code
  // if (stringRef === 'proRef1')
  //   proRef2.value.inputLabel = proRef1.value.item?.finish_product_name
  // else
  //   proRef1.value.inputLabel = proRef2.value.item?.finish_product_code

  // 清空颜色
  state.filterData.product_color_id = ''
  state.filterData.product_color_name = ''
  state.filterData.product_color_code = ''
}

const customerRef1 = ref()
const customerRef2 = ref()
function changeColor(stringRef: string) {
  if (stringRef === 'customerRef1')
    customerRef2.value.inputLabel = customerRef1.value.item?.product_color_name
  else
    customerRef1.value.inputLabel = customerRef2.value.item?.product_color_code
}
// 选择客户
function selectCustomerValueChange(val: any) {
  state.customer_name = val.name
  state.filterData.customer_id = val.id
}

// 查看占用库存
const occupyStockRef = ref()
function handleShowOccupyStock(row: any) {
  occupyStockRef.value.state.baseData = row
  occupyStockRef.value.state.showModal = true
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="75vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_code"
              field="finish_product_code"
              :query="{
                finish_product_code: componentRemoteSearch.finish_product_code,
              }"
              @on-input="(val) => (componentRemoteSearch.finish_product_code = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef1" -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_code" -->
            <!--              :query="{ finish_product_code: componentRemoteSearch.finish_product_code }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      isEdit: true, -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      isEdit: true, -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @on-input="val => (componentRemoteSearch.finish_product_code = val)" -->
            <!--              @change-value="changePro('proRef1')" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectProductDialog
              v-model="state.filterData.product_id"
              :label-name="componentRemoteSearch.finish_product_name"
              field="finish_product_name"
              :query="{
                finish_product_name: componentRemoteSearch.finish_product_name,
              }"
              @on-input="(val) => (componentRemoteSearch.finish_product_name = val)"
              @change-value="changePro"
            />
            <!--            <SelectDialog -->
            <!--              ref="proRef2" -->
            <!--              v-model="state.filterData.product_id" -->
            <!--              label-field="finish_product_name" -->
            <!--              :query="{ finish_product_name: componentRemoteSearch.finish_product_name }" -->
            <!--              api="GetFinishProductDropdownList" -->
            <!--              :column-list="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                  minWidth: 100, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_name', -->
            <!--                      isEdit: true, -->
            <!--                      title: '成品名称', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--                { -->
            <!--                  field: 'finish_product_code', -->
            <!--                  title: '成品编号', -->
            <!--                  minWidth: 100, -->
            <!--                  colGroupHeader: true, -->
            <!--                  childrenList: [ -->
            <!--                    { -->
            <!--                      field: 'finish_product_code', -->
            <!--                      isEdit: true, -->
            <!--                      title: '成品编号', -->
            <!--                      minWidth: 100, -->
            <!--                    }, -->
            <!--                  ], -->
            <!--                }, -->
            <!--              ]" -->
            <!--              :table-column="[ -->
            <!--                { -->
            <!--                  field: 'finish_product_name', -->
            <!--                  title: '成品名称', -->
            <!--                }, -->
            <!--              ]" -->
            <!--              @change-value="changePro('proRef2')" -->
            <!--              @on-input="val => (componentRemoteSearch.finish_product_name = val)" -->
            <!--            /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号:">
          <template #content>
            <SelectDialog
              ref="customerRef1"
              key="color2"
              v-model="state.filterData.product_color_id"
              :disabled="!state.filterData.product_id"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_code',
                  title: '色号',
                },
              ]"
              :query="{
                finish_product_id: state.filterData.product_id,
                product_color_code: componentRemoteSearch.product_color_code,
              }"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_code"
              @on-input="val => (componentRemoteSearch.product_color_code = val)"
              @change-value="changeColor('customerRef1')"
            />
            <!-- <SelectComponents size="small" v-model="state.filterData.product_color_id" api="GetFinishProductColorDropdownList" label-field="product_color_code" value-field="id"></SelectComponents> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色:">
          <template #content>
            <SelectDialog
              key="color1"
              ref="customerRef2"
              v-model="state.filterData.product_color_id"
              :disabled="!state.filterData.product_id"
              :column-list="[
                {
                  field: 'product_color_code',
                  title: '色号',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_code',
                      isEdit: true,
                      title: '色号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'product_color_name',
                  title: '颜色',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'product_color_name',
                      isEdit: true,
                      title: '颜色',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'product_color_name',
                  title: '颜色',
                },
              ]"
              :query="{
                finish_product_id: state.filterData.product_id,
                product_color_name: componentRemoteSearch.product_color_name,
              }"
              api="GetFinishProductColorDropdownList"
              label-field="product_color_name"
              @change-value="changeColor('customerRef2')"
              @on-input="val => (componentRemoteSearch.product_color_name = val)"
            />
            <!-- <SelectComponents size="small" v-model="state.filterData.product_color_id" api="GetFinishProductColorDropdownList" label-field="product_color_name" value-field="id"></SelectComponents> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户:">
          <template #content>
            <SelectCustomerDialog
              v-model="state.filterData.customer_id"
              :default-value="{
                id: state.filterData.customer_id,
                name: state.customer_name,
              }"
              @change-value="selectCustomerValueChange"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属仓库:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.warehouse_id"
              warehouse_type_id="finishProduction"
              api="GetPhysicalWarehouseDropdownList"
              label-field="name"
              value-field="id"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品等级:">
          <template #content>
            <SelectComponents v-model="state.filterData.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂缸号:">
          <template #content>
            <vxe-input v-model="state.filterData.dyelot_number" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仅显示有可用库存:">
          <template #content>
            <el-checkbox v-model="state.filterData.available_only" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>

      <FildCard title="" :tool-bar="true" class="mt-[5px] flex-1 overflow-hidden flex flex-col h-full">
        <template #title>
          <el-tabs v-model="state.filterData.radioValue" class="demo-tabs">
            <el-tab-pane label="按库存添加" :name="InformationViewTypeEnum.Stock" />
            <el-tab-pane label="按缸号添加" :name="InformationViewTypeEnum.DyeltNumber" />
          </el-tabs>
        </template>
        <Table :config="tableConfig" :table-list="state.list" :column-list="columnList" class="flex-1 overflow-hidden">
          <template #product_craft="{ row }">
            {{ isViewDyeltNumber ? row.finish_product_craft : row.product_craft }}
          </template>
          <template #product_ingredient="{ row }">
            {{ isViewDyeltNumber ? row.finish_product_ingredient : row.product_ingredient }}
          </template>
          <template #product_code="{ row }">
            {{ formatHashTag(row?.product_code, row?.product_name) }}
          </template>
          <template #product_color_code="{ row }">
            {{ formatHashTag(row?.product_color_code, row?.product_color_name) }}
          </template>
          <template #stock_roll="{ row }">
            {{ formatPriceDiv(row?.roll || row?.stock_roll) }}
          </template>
          <template #weight="{ row }">
            {{ formatWeightDiv(row?.weight) }} {{ row.measurement_unit_name }}
          </template>
          <template #available_roll="{ row }">
            <div class="flex items-center justify-between w-full">
              <span :style="{ color: row.available_roll === 0 ? 'red' : '' }">{{ formatPriceDiv(row?.available_roll) }}</span>
              <el-icon v-if="((row.available_roll < (row?.roll || row?.stock_roll)) || (row.available_weight < row?.weight)) && !isViewDyeltNumber" color="var(--el-color-primary)" size="16" class="ml-2 cursor-pointer" @click="handleShowOccupyStock(row)">
                <View />
              </el-icon>
            </div>
          </template>
          <template #available_weight="{ row }">
            <span :style="{ color: row.available_weight === 0 ? 'red' : '' }">{{ formatWeightDiv(row?.available_weight) }}</span>
          </template>
        </Table>
      </FildCard>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
  <OccupyStockDialog ref="occupyStockRef" />
</template>

<style lang="scss" scoped>
.flex_end {
  display: flex;
  justify-content: flex-end;
}

.demo-tabs{
  width: 150%;

  .el-tabs__header {
    margin-bottom: 8px;
  }
}
</style>
