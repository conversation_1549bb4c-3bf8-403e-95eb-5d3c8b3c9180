<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template v-slot:right-top>
      <el-button type="primary" v-btnAntiShake="handleSure">提交</el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:" required>
          <template v-slot:content>
            <el-form-item prop="sale_system_id">
              <SelectComponents api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" v-model="state.form.sale_system_id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂名称" required>
          <template v-slot:content>
            <el-form-item prop="dye_factory_id">
              <SelectComponents
                ref="SelectComponentsRef"
                @change-value="val => changeFactory(val)"
                api="GetBusinessUnitListApi"
                :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill }"
                label-field="name"
                value-field="id"
                v-model="state.form.dye_factory_id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂报价单号:">
          <template v-slot:content>
            <el-input maxlength="50" v-model="state.form.quote_order_no" clearable></el-input>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染纱厂报价日期:">
          <template v-slot:content>
            <el-date-picker v-model="state.form.quote_date" type="date" placeholder="染厂报价日期" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="报价类型:">
          <template v-slot:content>
            {{ '新报价' }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="生效时间:" required>
          <template v-slot:content>
            <el-form-item prop="effect_time">
              <el-date-picker v-model="state.form.effect_time" type="datetime" placeholder="生效时间" format="YYYY/MM/DD HH:mm:ss" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="截止时间:">
          <template v-slot:content>
            <el-date-picker v-model="state.form.deadline" type="datetime" placeholder="截止时间" format="YYYY/MM/DD HH:mm:ss" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:">
          <template v-slot:content>
            <el-input v-model="state.form.remark" clearable></el-input>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard title="原料信息" :tool-bar="false" class="mt-[5px]">
    <template v-slot:right-top>
      <!-- <el-button type="primary" @click="bulkHand">批量操作</el-button> -->
      <el-button type="primary" @click="handAddRow">新增行</el-button>
      <el-button :disabled="state.form.dye_factory_id === ''" type="primary" @click="handAdd">添加原料颜色</el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :tableList="state.tableList" :column-list="columnList">
      <template #raw_code="{ row }">
        <SelectDialog
          @change-value="row.factory_color_id = ''"
          v-model="row.raw_material_id"
          api="rawmaterialMenu"
          labelField="code"
          :query="{ code: componentRemoteSearch.raw_code }"
          @change-input="val => (componentRemoteSearch.raw_code = val)"
          :columnList="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :tableColumn="[
            {
              field: 'code',
              title: '原料编号',
            },
          ]"
        />
      </template>
      <template #raw_name="{ row }">
        <SelectDialog
          v-model="row.raw_material_id"
          api="rawmaterialMenu"
          :query="{ name: componentRemoteSearch.raw_name }"
          @change-input="val => (componentRemoteSearch.raw_name = val)"
          :columnList="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :tableColumn="[
            {
              field: 'name',
              title: '原料名称',
            },
          ]"
        />
      </template>

      <template #factory_color_type="{ row }">
        <SelectComponents api="GetDictionaryDetailEnumListApi" label-field="name" value-field="id" v-model="row.factory_color_type" :query="{ dictionary_id: DictionaryType.colorKind }" clearable />
        <!-- {{ row.factory_color_type }} -->
      </template>

      <template #factory_color_number="{ row }">
        <SelectDialog
          :disabled="row.rowType === 1 && state.form.dye_factory_id === ''"
          @changeValue="val => ((row.factory_color_number = val.code), (row.factory_color_id = ''), (row.factory_color_type = val.type_id))"
          :query="{ raw_matl_id: row.raw_material_id, code: componentRemoteSearch.color_code }"
          v-model="row.ben_factory_color_id"
          @change-input="val => (componentRemoteSearch.color_code = val)"
          api="GetRawMaterialColor"
          labelField="code"
          :columnList="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :tableColumn="[
            {
              field: 'code',
              title: '原料色号',
              minWidth: 100,
            },
          ]"
        ></SelectDialog>
      </template>

      <template #factory_color_name="{ row }">
        <SelectDialog
          :disabled="row.rowType === 1 && state.form.dye_factory_id === ''"
          @changeValue="val => ((row.factory_color_number = val.code), (row.factory_color_id = ''), (row.factory_color_type = val.type_id))"
          :query="{ raw_matl_id: row.raw_material_id, name: componentRemoteSearch.color_name }"
          v-model="row.ben_factory_color_id"
          api="GetRawMaterialColor"
          labelField="name"
          @change-input="val => (componentRemoteSearch.color_name = val)"
          :columnList="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :tableColumn="[
            {
              field: 'name',
              title: '原料颜色',
              minWidth: 100,
            },
          ]"
        ></SelectDialog>
      </template>

      <template #dye_factory_color_type_name="{ row }">
        <SelectComponents
          api="GetDictionaryDetailEnumListApi"
          label-field="name"
          value-field="id"
          v-model="row.dye_factory_color_type_name"
          :query="{ dictionary_id: DictionaryType.colorKind }"
          clearable
        />
        <!-- {{ row.dye_factory_color_type_name }} -->
      </template>

      <template #dye_factory_color_code="{ row }">
        <SelectDialog
          :disabled="row.rowType === 1 && state.form.dye_factory_id === ''"
          :query="{ code: row.factory_color_number, factory_id: state.form.dye_factory_id }"
          v-model="row.factory_color_id"
          api="GetListFactory"
          valueField="fc_id"
          labelField="fc_code"
          :columnList="[
            {
              field: 'fc_name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'fc_name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'fc_code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'fc_code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :tableColumn="[
            {
              field: 'fc_code',
              title: '染纱厂编号',
              minWidth: 100,
            },
          ]"
        ></SelectDialog>
      </template>

      <template #dye_factory_color_name="{ row }">
        <SelectDialog
          :disabled="row.rowType === 1 && state.form.dye_factory_id === ''"
          :query="{ code: row.factory_color_number, factory_id: state.form.dye_factory_id }"
          v-model="row.factory_color_id"
          valueField="fc_id"
          api="GetListFactory"
          labelField="fc_name"
          :columnList="[
            {
              field: 'fc_name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'fc_name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'fc_code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'fc_code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :tableColumn="[
            {
              field: 'fc_name',
              title: '染纱厂颜色',
              minWidth: 100,
            },
          ]"
        ></SelectDialog>
      </template>

      <template #project="{ row }">
        <vxe-input maxlength="50" clearable v-model="row.project"></vxe-input>
      </template>

      <template #quote_unit_id="{ row }">
        <SelectComponents api="getInfoBaseMeasurementUnitEnumList" label-field="name" value-field="id" v-model="row.quote_unit_id" clearable />
      </template>

      <template #unit_price="{ row }">
        <vxe-input type="float" :min="0" clearable v-model="row.unit_price"></vxe-input>
      </template>

      <template #include_tax="{ row }">
        <el-select clearable v-model="row.include_tax" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </template>
      <template #remark="{ row }">
        <vxe-input clearable v-model="row.remark"></vxe-input>
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDelete(rowIndex)">删除</el-button>
      </template>
    </Table>
  </FildCard>
  <!-- <BulkSetting v-model="bulkSetting" :column-list="bulkList" @submit="bulkSubmit" :show="bulkShow" @close="handBulkClose">
    <template #include_tax="{ row }">
      <el-select clearable v-model="row.include_tax" placeholder="请选择">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </template>
  </BulkSetting> -->
  <SelectRawColorDialog ref="SelectRawColorDialogRef" @handleSure="handleSureAdd"></SelectRawColorDialog>
</template>

<script setup lang="ts" name="RawQuotationAdd">
// import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { deepClone, deleteToast, getCurrentDate, getDefaultSaleSystem } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { formatDate, formatTime, formatUnitPriceMul } from '@/common/format'
import { raw_matl_quotedyeing } from '@/api/rawQuotation'
import { BusinessUnitIdEnum, DictionaryType } from '@/common/enum'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectRawColorDialog from '../components/SelectRawColorDialog.vue'
import useRouterList from '@/use/useRouterList'

const state = reactive<any>({
  form: {
    sale_system_id: '',
    dye_factory_id: '',
    quote_order_no: '',
    quote_date: '',
    dye_price_unit: '',
    effect_time: '',
    deadline: '',
    quote_type: 2,
    remark: '',
    quote_unit: '',
  },
  multipleSelection: [],
  tableList: [],
  formRules: {
    effect_time: [{ required: true, message: '请选择生效时间', trigger: 'blur' }],
    sale_system_id: [{ required: true, message: '请选择营销体系', trigger: 'blur' }],
    dye_factory_id: [{ required: true, message: '请选择染纱厂名称', trigger: 'blur' }],
  },
  info: {},
  old_dye_factory_ids: [],
  old_dye_factor_id: '',
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const options = ref([
  {
    label: '含税',
    value: true,
  },
  {
    label: '不含税',
    value: false,
  },
])

const tableConfig = ref({
  showSlotNums: true,
  height: 500,
  showSpanHeader: true,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),

  //   showOperate: true,
  //   operateWidth: '80',
})

const handAllSelect = ({ records }: any) => {
  state.multipleSelection = records
}

const handleSelectionChange = ({ records }: any) => {
  state.multipleSelection = records
}

onMounted(() => {
  const resDes = getDefaultSaleSystem()
  state.form.sale_system_id = resDes?.default_sale_system_id
  state.form.quote_date = getCurrentDate()
  state.form.effect_time = getCurrentDate()
  state.old_dye_factor_id = ''
})

const ruleFormRef = ref()

const router = useRouterList()

const SelectRawColorDialogRef = ref()

const handAdd = () => {
  SelectRawColorDialogRef.value.state.showModal = true
  SelectRawColorDialogRef.value.state.factory_id = state.form.dye_factory_id
}

const handleSureAdd = (list: any) => {
  list.forEach((item: any) => {
    state.tableList.push({
      raw_material_id: item.raw_matl_id,
      factory_color_type: item.type_id,
      dye_factory_color_type_name: item.fc_type_id,
      factory_color_number: item.code,
      factory_color_id: '',
      ben_factory_color_id: '',
      project: '',
      quote_unit_id: '',
      unit_price: '',
      include_tax: '',
      remark: '',
      rowType: 2, // 从弹窗选择添加的数据
    })
  })
}

const handAddRow = () => {
  state.tableList.push({
    raw_material_id: '',
    factory_color_type: '',
    factory_color_id: '',
    ben_factory_color_id: '',
    dye_factory_color_type_name: '',
    project: '',
    quote_unit_id: '',
    unit_price: '',
    include_tax: '',
    remark: '',
    rowType: 1, // 手动添加的数据
  })
}

const { fetchData: addPost, success: addSuccess, msg: addMsg } = raw_matl_quotedyeing()

const handleSure = async () => {
  if (!state.tableList.length) {
    return ElMessage.error('请至少添加一条报价信息')
  }

  const list = deepClone(state.tableList)

  for (let i = 0; i < list.length; i++) {
    if (list[i].unit_price === '') {
      return ElMessage.error('请输入单价')
    }

    if (list[i].ben_factory_color_id === '') {
      return ElMessage.error('请选择本厂颜色编号/名称')
    }

    if (list[i].factory_color_id === '') {
      return ElMessage.error('请选择染纱厂颜色编号/名称')
    }

    list[i].unit_price = formatUnitPriceMul(list[i].unit_price)

    list[i].quote_unit_id = Number(list[i].quote_unit_id)
  }

  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      state.form.deadline = formatTime(state?.form.deadline)
      state.form.effect_time = formatTime(state?.form.effect_time)
      state.form.quote_date = formatDate(state?.form.quote_date)

      const query = {
        ...state.form,
        quote_type: 1,
        items: list,
      }
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        router.push({ name: 'RawQuotationIndex' })
      } else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const handDelete = (index: number) => {
  state.tableList.splice(index, 1)
}

const SelectComponentsRef = ref()

const changeFactory = async (val: any) => {
  if (state.old_dye_factor_id === '') {
    state.old_dye_factor_id = state.form.dye_factory_id
  }

  if (state.old_dye_factor_id !== '' && state.old_dye_factor_id !== state.form.dye_factory_id) {
    const res = await deleteToast('修改染纱厂名称会将已填写的原料信息清空，是否确认修改？')
    if (res) {
      state.tableList = []
      state.old_dye_factor_id = state.form.dye_factory_id
      SelectComponentsRef.value.SelectOptionsRef.selectRef.blur()
    } else {
      state.form.dye_factory_id = state.old_dye_factor_id
      SelectComponentsRef.value.SelectOptionsRef.selectRef.blur()
    }
  }
}

const tableRef = ref()

const columnList = ref([
  {
    title: '',
    childrenList: [
      {
        field: 'raw_code',
        title: '原料编号',
        soltName: 'raw_code',
        minWidth: 100,
      },
      {
        field: 'raw_name',
        title: '原料名称',
        minWidth: 100,
        soltName: 'raw_name',
        required: true,
      },
    ],
  },

  {
    title: '本厂颜色',
    childrenList: [
      {
        field: 'factory_color_type',
        title: '本厂颜色类别',
        minWidth: 100,
        soltName: 'factory_color_type',
      },
      {
        field: 'factory_color_number',
        title: '本厂颜色编号',
        minWidth: 100,
        soltName: 'factory_color_number',
      },
      {
        field: 'factory_color_name',
        title: '本厂颜色名称',
        minWidth: 100,
        soltName: 'factory_color_name',
        required: true,
      },
    ],
  },
  {
    title: '染纱厂颜色',
    childrenList: [
      {
        field: 'dye_factory_color_type_name',
        title: '染纱厂颜色类别',
        soltName: 'dye_factory_color_type_name',
        minWidth: 100,
      },
      {
        field: 'dye_factory_color_code',
        title: '染纱厂颜色编号',
        soltName: 'dye_factory_color_code',
        minWidth: 100,
      },
      {
        field: 'dye_factory_color_name',
        title: '染纱厂颜色名称',
        soltName: 'dye_factory_color_name',
        minWidth: 100,
        required: true,
      },
    ],
  },
  {
    title: '计价信息',
    childrenList: [
      {
        field: 'project',
        title: '染费项目',
        minWidth: 100,
        soltName: 'project',
      },
      {
        field: 'quote_unit_id',
        title: '计价单位',
        minWidth: 100,
        soltName: 'quote_unit_id',
      },
      {
        field: 'unit_price',
        title: '单价',
        minWidth: 100,
        required: true,
        soltName: 'unit_price',
      },
      {
        field: 'include_tax',
        title: '是否含税',
        minWidth: 100,
        soltName: 'include_tax',
      },
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
        soltName: 'remark',
      },
      {
        field: '',
        title: '操作',
        minWidth: 100,
        soltName: 'operate',
      },
    ],
  },
])

// const bulkSetting = ref<any>({})

// const bulkShow = ref(false)

// const bulkHand = () => {
//   bulkShow.value = true
// }

// const handBulkClose = () => {
//   bulkShow.value = false
// }

// const bulkSubmit = async ({ row, value }: any) => {
//   if (state.multipleSelection?.length <= 0) {
//     return ElMessage.error('请先勾选批量修改的数据')
//   }
//   state.tableList?.map((item: any) => {
//     if (item?.selected) {
//       item[row.field] = value[row.field]
//       return item
//     }
//   })

//   if (row.field === 'unit_price_kg') {
//     state.tableList?.map((item: any) => {
//       if (item?.selected) {
//         item.unit_price_dun_new_new = Number(value[row.field]) * 1000
//         item.unit_price = Number(value[row.field])
//         return item
//       }
//     })
//   }

//   if (row.field === 'unit_price_dun_new_new') {
//     state.tableList?.map((item: any) => {
//       if (item?.selected) {
//         item.unit_price = Number(value[row.field]) / 1000
//         item.unit_price_dun_new_new = Number(value[row.field])
//         return item
//       }
//     })
//   }
//   if (row?.field === 'include_tax') {
//     state.tableList?.map((item: any) => {
//       if (item?.selected) {
//         item.include_tax = row.include_tax
//         return item
//       }
//     })
//   }
//   bulkShow.value = false
//   ElMessage.success('设置成功')
//   // state.multipleSelection = []
// }

// const bulkList = reactive<any>([
//   {
//     field: 'unit_price_dun_new_new',
//     title: '新单价(元/吨)',
//     component: 'input',
//     type: 'float',
//     digits: 2,
//   },
//   {
//     field: 'project',
//     title: '染费项目',
//     component: 'input',
//     type: 'text',
//   },
//   {
//     field: 'unit_price_kg',
//     title: '新单价(数量)',
//     component: 'input',
//     type: 'float',
//     digits: 4,
//   },
//   {
//     field: 'unit_price',
//     title: '新单价(长度)',
//     component: 'input',
//     type: 'float',
//     digits: 4,
//   },
//   {
//     field: 'include_tax',
//     title: '是否含税',
//     labelField: 'name',
//     valueField: 'name',
//   },
//   {
//     field: 'remark',
//     title: '备注',
//     component: 'input',
//     type: 'text',
//   },
// ])
</script>

<style></style>
