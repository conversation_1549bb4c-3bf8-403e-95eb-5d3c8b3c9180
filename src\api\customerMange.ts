import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表
export function Business_unitcustomerlist() {
  return useRequest<Api.Customer.Request, ResponseList<Api.Customer.Response>>({
    url: '/admin/v1/business_unit/customer/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 删除数据
export function Business_unitcustomerDelete() {
  return useRequest<{ id: number }, void>({
    url: '/admin/v1/business_unit/customer',
    method: 'delete',
  })
}

// 启用数据
export function Business_unitcustomerEnable() {
  return useRequest<{ id: number }, void>({
    url: '/admin/v1/business_unit/customer/enable',
    method: 'put',
  })
}

// 禁用数据
export function Business_unitcustomerDisable() {
  return useRequest<{ id: number }, void>({
    url: '/admin/v1/business_unit/customer/disable',
    method: 'put',
  })
}

// 新增数据
export function Business_unitcustomerAdd() {
  return useRequest<Api.CustomerAdd.Request, Api.CustomerAdd.Response>({
    url: '/admin/v1/business_unit/customer',
    method: 'post',
  })
}

// 修改数据
export function Business_unitcustomerUpdate() {
  return useRequest<Api.CustomerAdd.Request, Api.CustomerAdd.Response>({
    url: '/admin/v1/business_unit/customer',
    method: 'put',
  })
}

// 详情
export function Business_unitcustomerDetail() {
  return useRequest<Api.CustomerDetail.Request, Api.CustomerDetail.Response>({
    url: '/admin/v1/business_unit/customer/detail',
    method: 'get',
  })
}

// 导出数据
export function Business_unitcustomerExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/business_unit/customer/list',
    method: 'get',
    nameFile,
  })
}

// 更新数据
export function Business_unitcustomerPut() {
  return useRequest<Api.CustomerAdd.Request, Api.CustomerAdd.Response>({
    url: '/admin/v1/business_unit/customer',
    method: 'put',
  })
}

export function business_unitcustomersimple() {
  return useRequest<Api.CustomerAdd.Request, Api.CustomerAdd.Response>({
    url: '/admin/v1/business_unit/customer/simple',
    method: 'put',
  })
}
