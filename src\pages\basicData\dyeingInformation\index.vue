<script setup lang="ts" name="KindDyeingInformation">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useToggle } from '@vueuse/core'
import AddDialog from './components/AddDialog.vue'
import DetailDialog from './components/DetailDialog.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'

// import router from '@/router'
import {
  addFabricDyeProcessInfo,
  deleteFabricDyeProcessInfo,
  getFabricDyeProcessInfo,
  getFabricDyeProcessInfoList,
  updateFabricDyeProcessInfo,
  updateFabricDyeProcessInfoStatus,
} from '@/api/dyeingInformationBasic'
import { formatPriceDiv } from '@/common/format'
import {
  debounce,
  deepClone,
  deleteRemark,
  deleteToast,
  getFilterData,
  resetData,
} from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SideTree from '@/components/SideTree.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import Accordion from '@/components/Accordion/index.vue'

const state = reactive<any>({
  tableData: [],
  filterData: {
    product_info_name: '',
    status: '',
    type_fabric_ids: [],
  },
  multipleSelection: [],
  information: false,
  info: {},
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = getFabricDyeProcessInfoList()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.type_fabric_ids.length)
    obj.type_fabric_ids = obj.type_fabric_ids.join(',')

  const query = {
    ...state.filterData,
    type_fabric_ids: obj.type_fabric_ids,
  }
  await ApiCustomerList(getFilterData(query))
  if (data.value?.list)
    getInfomation(data.value?.list?.[0])

  state.multipleSelection = []
}, 400)
onMounted(() => {
  getData()
})
const tableConfig_twince = ref({
  fieldApiKey: fieldApiKeyList.KindDyeingInformationListTable2,
  showSlotNums: true,
  height: '100%',
})

const tableConfig = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  fieldApiKey: fieldApiKeyList.KindDyeingInformationList,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const AddDialogRef = ref()

const columnList = ref([
  {
    sortable: true,
    field: 'product_code',
    title: '编号',
    soltName: 'link',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'type_fabric_name',
    title: '布种类型',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'product_name',
    title: '成品名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_name',
    title: '染厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    soltName: 'status',
    width: '5%',
  },
])

const columnListg_twince = ref([
  {
    sortable: true,
    field: 'dye_process_data_info_code',
    title: '染整工艺编号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_process_data_info_name',
    title: '染整工艺名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_process_data_info_type',
    title: '染整工艺分类',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'presets',
    title: '预设值',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
])

function handShowSort() {
  tableConfig.value.showSort = true
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handleAdd() {
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '新增布种染整工艺资料'
  AddDialogRef.value.state.tableList = []
  AddDialogRef.value.state.form.dye_factory_id = ''
  AddDialogRef.value.state.form.product_code = ''
  AddDialogRef.value.state.form.product_name = ''
  AddDialogRef.value.state.form.type_fabric_id = ''
  AddDialogRef.value.state.form.remark = ''
  AddDialogRef.value.state.form.id = -1
  AddDialogRef.value.state.form.product_id = ''
}

const { fetchData: getFetchDetail, data: fabricList }
    = getFabricDyeProcessInfo()
async function handEdit(row: any) {
  await getInfomation(row)
  state.information = false
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '编辑布种染整工艺资料'
  AddDialogRef.value.state.form.dye_factory_id = row.dye_factory_id || ''
  AddDialogRef.value.state.form.product_code = row.product_code
  AddDialogRef.value.state.form.product_name = row.product_name
  AddDialogRef.value.state.form.type_fabric_id = row.type_fabric_id || ''
  AddDialogRef.value.state.form.remark = row.remark
  AddDialogRef.value.state.form.id = row.id
  AddDialogRef.value.state.form.product_id = row?.product_id || ''

  const arr = deepClone(fabricList.value?.item_data)
  arr.map((item: any) => {
    item.presets = formatPriceDiv(item.presets)
    return item
  })
  AddDialogRef.value.state.tableList = arr
}

const [detailDialogShow, setDetailDialogShow] = useToggle(false)
const detailDialogRow = ref<any>()
const detailDialogTable = ref<any>()
async function handDetail(row: any) {
  setDetailDialogShow(true)
  detailDialogRow.value = row
  await getInfomation(row)
  const arr = deepClone(fabricList.value?.item_data)
  arr.map((item: any) => {
    item.presets = formatPriceDiv(item.presets)
    return item
  })
  detailDialogTable.value = arr
}

const {
  fetchData: AddFetch,
  msg: AddMsg,
  success: AddSuccess,
} = addFabricDyeProcessInfo()

const {
  fetchData: putFetch,
  msg: putMsg,
  success: putSuccess,
} = updateFabricDyeProcessInfo()

// 新建、编辑
async function handleSure(form: any, list: any) {
  const query = {
    ...form.form,
    product_id: Number(form.form.product_id),
    dye_factory_id: Number(form.form.dye_factory_id),
    type_fabric_id: Number(form.form.type_fabric_id),
    item_data: list,
  }
  form.form.id === -1 ? await AddFetch(query) : await putFetch(query)
  if (form.form.id === -1 ? AddSuccess.value : putSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    getData()
  }
  else {
    ElMessage.error(form.form.id === -1 ? AddMsg.value : putMsg.value)
  }
}

// 删除数据
const {
  fetchData: deleteFetch,
  success: deleteSuccess,
  msg: deleteMsg,
} = deleteFabricDyeProcessInfo()

// async function handDelete(row: any) {
//   const res = await deleteRemark()
//   await deleteFetch({ id: row.id.toString(), delete_remark: res })
//   if (deleteSuccess.value) {
//     ElMessage.success('成功')
//     getData()
//   }
//   else {
//     ElMessage.error(deleteMsg.value)
//   }
// }

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteRemark()
  const ids: any[] = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  await deleteFetch({ id: ids.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

// 编辑状态
const {
  fetchData: statusFetch,
  msg: StatusMsg,
  success: StatusSuccess,
} = updateFabricDyeProcessInfoStatus()

async function handStatus(row: any) {
  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    await statusFetch({
      id: row.id.toString(),
      status: row.status === 1 ? 2 : 1,
    })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      AddDialogRef.value.state.showModal = false
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 批量修改状态
async function handAll(val: number) {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    await statusFetch({ id: ids.toString(), status: val === 1 ? 1 : 2 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 获取坯布信息
async function getInfomation(row: any) {
  await getFetchDetail({ id: row.id })
  state.info = row
  state.information = true
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.product_info_name"
              api="GetFinishProductDropdownList"
              label-field="finish_product_name"
              value-field="finish_product_name"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              style="width: 200px"
              api="StatusListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <div class="flex flex-1 overflow-hidden">
      <Accordion :open-status="true">
        <SideTree
          v-model="state.filterData.type_fabric_ids"
          api="getTreeEnumList"
          type="checkbox"
          check-strictly
          highlight-current
          default-expand-all
          @select="() => (state.filterData.type_fabric_ids = [])"
        />
      </Accordion>
      <div class="list-page flex-1">
        <FildCard title="" class="table-card-full" tool-bar>
          <template #right-top>
            <el-button
              v-has="'KindDyeingInformationAdd'"
              style="margin-left: 10px"
              type="primary"
              :icon="Plus"
              @click="handleAdd"
            >
              新建
            </el-button>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="点击出现排序按钮"
              placement="top-start"
            >
              <el-button style="margin-right: 10px" @click="handShowSort">
                排序
              </el-button>
            </el-tooltip>
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-button>批量操作</el-button>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <div v-has="'KindDyeingInformationDelect'">
                    <el-dropdown-item @click="handAllDelete">
                      批量删除
                    </el-dropdown-item>
                  </div>
                  <div v-has="'KindDyeingInformationStatus'">
                    <el-dropdown-item @click="handAll(1)">
                      批量启用
                    </el-dropdown-item>
                  </div>
                  <div v-has="'KindDyeingInformationStatus'">
                    <el-dropdown-item @click="handAll(2)">
                      批量禁用
                    </el-dropdown-item>
                  </div>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <Table
            :config="tableConfig"
            :table-list="data?.list"
            :column-list="columnList"
          >
            <template #link="{ row }">
              <el-link
                type="primary"
                :underline="false"
                @click="getInfomation(row)"
              >
                {{ row?.product_code }}
              </el-link>
            </template>
            <template #status="{ row }">
              <div class="flex items-center">
                <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
                <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
                  {{ row?.status === 1 ? "启用" : "禁用" }}
                </div>
              </div>
            </template>
            <template #operate="{ row }">
              <el-space :size="10">
                <el-link
                  type="primary" :underline="false"
                  @click="handDetail(row)"
                >
                  查看
                </el-link>
                <el-link
                  v-has="'KindDyeingInformationEdit'"
                  type="primary" :underline="false"
                  @click="handEdit(row)"
                >
                  编辑
                </el-link>
                <!--          <el-link v-has="'KindDyeingInformationDelect'" text type="danger" @click="handDelete(row)">删除</el-link> -->
                <el-link
                  v-has="'KindDyeingInformationStatus'"
                  type="primary" :underline="false"
                  @click="handStatus(row)"
                >
                  {{ row?.status === 1 ? "禁用" : "启用" }}
                </el-link>
              </el-space>
            </template>
          </Table>
        </FildCard>
        <FildCard title="" class="table-card-bottom" tool-bar>
          <div class="flex mb-[20px]">
            <div class="mr-[20px]">
              成品编号：{{ state.info?.product_code }}
            </div>
            <div class="mr-[20px]">
              成品名称：{{ state.info?.product_name }}
            </div>
            <div class="mr-[20px]">
              染厂名称：{{ state.info?.dye_factory_name }}
            </div>
          </div>
          <Table
            :config="tableConfig_twince"
            :table-list="fabricList?.item_data"
            :column-list="columnListg_twince"
          />
        </FildCard>
      </div>
    </div>
  </div>
  <AddDialog ref="AddDialogRef" @handle-sure="handleSure" />
  <DetailDialog v-model="detailDialogShow" :row="detailDialogRow" :table-data="detailDialogTable" />
</template>

<style lang="scss" scoped>
.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
