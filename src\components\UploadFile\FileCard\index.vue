<script lang="ts" setup>
import { Delete, Discount, Download, Select, ZoomIn } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'
import { fileIsPdf, getFileType } from '@/common/uploadImage'
import { formatFileName, getFileName } from '@/common/util'

interface ParamType {
  fileUrl: string
  allUrls?: any
  clearDisabled: boolean
  drownDisabled: boolean
  defaultDisabled: boolean
  defaultStatus: boolean
}
const props = withDefaults(defineProps<ParamType>(), {
  fileUrl: '',
  allUrls: [],
  clearDisabled: false,
  drownDisabled: true,
  defaultDisabled: false,
  defaultStatus: false,
})

// const emits = defineEmits(['download', 'remove', 'preview', 'setDefault'])
// 图片
const imageList = computed(() => {
  return props.allUrls.filter((e: string) => getFileType(e) === 'image')
})
// 获取当前图片下标
function getImageIndex(url: string) {
  return imageList.value.findIndex(e => e === url)
}

// const dialogImageUrl = ref('')
// const dialogVisible = ref(false)
// function handlePictureCardPreview() {
//   dialogImageUrl.value = props.fileUrl
//   dialogVisible.value = true
//   emits('preview', props.fileUrl)
// }
// function handleDownload() {
//   emits('download', props.fileUrl)
// }
// function handleRemove() {
//   emits('remove', props.fileUrl)
// }

// function handleDefault() {
//   emits('setDefault', props.fileUrl)
// }
</script>

<template>
  <div class="fileCard">
    <div v-if="props.defaultStatus" class="defaultStatus">
      <el-icon><Select /></el-icon>
    </div>
    <div v-if="getFileType(props?.fileUrl) !== 'image'" class="bg-slate-100 flex items-center justify-center item-thumbnail">
      <el-link type="primary" :href="formatFileName(props?.fileUrl)" :target="fileIsPdf(props.fileUrl) ? '_blank' : '_self'">
        {{ getFileName(props?.fileUrl) }}
      </el-link>
    </div>
    <el-image v-else class="item-thumbnail w-full h-full rounded-lg" fit="cover" alt="Preview Image" :preview-src-list="[props?.fileUrl]" :src="props?.fileUrl" />

    <!-- <span v-if="getFileType(props?.fileUrl) === 'image'" class="item-actions">
      <span class="item-button" @click="handlePictureCardPreview">
        <el-icon><ZoomIn /></el-icon>
      </span>
      <span v-if="!props.drownDisabled" class="item-button" @click="handleDownload">
        <el-icon><Download /></el-icon>
      </span>
      <span v-if="!props.clearDisabled" class="item-button" @click="handleRemove">
        <el-icon><Delete /></el-icon>
      </span>
      <span v-if="!props.defaultDisabled" class="item-button" @click="handleDefault">
        <el-icon><Discount /></el-icon>
      </span>
    </span> -->
  </div>
  <!-- <vxe-modal v-model="dialogVisible" resize title="图片预览" width="500px">
    <el-image fit="fill" w-full :src="dialogImageUrl" alt="Preview Image">
      <template #error>
        <div class="image-slot">
          <span>加载失败</span>
        </div>
      </template>
    </el-image>
  </vxe-modal> -->
</template>

<style lang="scss" scoped>
.fileCard {
  width: 100px;
  height: 100px;
  position: relative;
  border: 1px solid #dcdfe6;
  margin: 0 5px;
  display: inline-block;
  overflow: hidden;
  border-radius: 5px;
  .item-thumbnail {
    width: 100px;
    height: 100px;
    border-radius: 5px;
  }
  .item-actions {
    opacity: 0;
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    padding: 0 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    &:hover {
      opacity: 1;
    }
  }
  .item-button {
    display: block;
    // padding: 10px;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
  }
  .defaultStatus {
    position: absolute;
    line-height: inherit;
    justify-content: center;
    align-items: center;
    right: -15px;
    top: -6px;
    width: 40px;
    height: 24px;
    background: #67c23a;
    text-align: center;
    transform: rotate(45deg);
    color: #fff;
    font-size: 12px;
    i {
      margin-top: 10px;
      transform: rotate(-45deg);
    }
  }
}
</style>
