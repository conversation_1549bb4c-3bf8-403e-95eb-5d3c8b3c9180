<script setup lang="ts" name="ClothType">
import { Delete, Plus, Setting } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import AddDialog from '../components/AddProductType.vue'
import FildCard from '@/components/FildCard.vue'
// import router from '@/router'
import { deleteTypeIntercourseUnits, getPhysicalWarehouseListTree, updateTypeIntercourseUnitsStatus } from '@/api/clothType'
import { formatDate } from '@/common/format'
import {
  debounce,
  deleteRemark,
  deleteToastWithRiskWarning,
  disabledConfirmBox,
  getFilterData,
  resetData,
} from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    name: '',
    status: '',
    code: '',
  },
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data, loading } = getPhysicalWarehouseListTree()

// 获取数据
const getData = debounce(() => {
  ApiCustomerList(getFilterData(state.filterData))
  state.multipleSelection = []
}, 400)

onMounted(() => {
  getData()
})
watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const showAdd = ref()
const selectRow = ref({})
const dataList = ref()
const showTitle = ref('添加类型')
function addChildren(row: any) {
  showAdd.value = true
  selectRow.value = { ...row, parent_id: row.id, id: 0 }
  dataList.value = data.value.list
  showTitle.value = '添加类型'
}

function editChildren(row: any) {
  showAdd.value = true
  selectRow.value = row
  dataList.value = data.value.list
  showTitle.value = '编辑类型'
}

function handleAdd() {
  showAdd.value = true
  selectRow.value = {}
  dataList.value = data.value.list
  showTitle.value = '添加类型'
}

function onSuccess() {
  showAdd.value = false
  getData()
}

// 删除数据
const { fetchData: deleteFetch, success: deleteSuccess, msg: deleteMsg } = deleteTypeIntercourseUnits()

async function handDelete(row: any) {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    const res = await deleteRemark()
    await deleteFetch({ id: row.id.toString(), delete_remark: res })
    if (deleteSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteMsg.value)
    }
  }
}

function changeStatus(row: any) {
  disabledConfirmBox({ api: updateTypeIntercourseUnitsStatus, row }).then(() => {
    getData()
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="编号或名称:">
          <template #content>
            <el-input v-model="state.filterData.code" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents v-model="state.filterData.status" style="width: 200px" api="StatusListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button v-has="'ClothType_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
      </template>
      <el-table v-loading="loading" :indent="20" :data="data.list" style="width: 100%; margin-bottom: 20px" :tree-props="{ children: 'sub_type_fabric' }" row-key="id">
        <el-table-column prop="code" label="编号" width="250" sortable />
        <el-table-column prop="name" label="名称" width="250" sortable />
        <el-table-column prop="remark" label="备注" sortable />
        <el-table-column prop="is_hide" label="创建时间" sortable width="170">
          <template #default="{ row }">
            {{ formatDate(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="status_name" label="状态" width="100" sortable>
          <template #default="{ row }">
            <span :class="{ green: row.status === 1, red: row.status === 2 }">{{ row.status_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-link v-has="'ClothType_add'" type="primary" :avatar_url="Plus" :underline="false" @click="addChildren(row)">
              添加子类型
            </el-link>
            <el-link v-has="'ClothType_edit'" type="primary" :underline="false" :avatar_url="Setting" @click="editChildren(row)">
              编辑
            </el-link>
            <el-link v-has="'ClothType_status'" :underline="false" type="primary" :class="{ green: row.status === 2, red: row.status === 1 }" :avatar_url="Setting" @click="changeStatus(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-link>
            <el-link v-has="'ClothType_del'" :underline="false" type="danger" :avatar_url="Delete" @click.stop="handDelete(row)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </FildCard>
  </div>
  <AddDialog v-model="showAdd" :title="showTitle" :row="selectRow" :list="dataList" @on-success="onSuccess" />
</template>

<style lang="scss" scoped>
::v-deep(.el-descriptions__cell) {
  width: 300px !important;
}
::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}
.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.green {
  color: #51c41b;
}
.red {
  color: red;
}
.el-link {
  margin-right: 15px;
}
</style>
