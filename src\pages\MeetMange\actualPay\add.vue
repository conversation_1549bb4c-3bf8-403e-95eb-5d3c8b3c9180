<script lang="ts" setup name="ActualPayAdd">
import { ElMessage, ElMessageBox } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import Decimal from 'decimal.js'
import currency from 'currency.js'
import AddFinishDialog from './components/AddFinishDialog.vue'
import UseAdvanceDialog from './components/UseAdvanceDialog.vue'
import { GetAutoWriteOffList, actuallyPayOrderpost } from '@/api/actualPay'
import { formatDate, formatPriceDiv, formatPriceMul, sumNum } from '@/common/format'
import { deepClone, getCurrentDate, getDefaultSaleSystem } from '@/common/util'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import useRouterList from '@/use/useRouterList'
import { formValidatePass } from '@/common/rule'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import SelectSettleAccountDialog from '@/components/SelectSettleAccountDialog/index.vue'

const routerList = useRouterList()

const state = reactive<any>({
  form: {
    src_order_type_name: '',
    src_order_type: '',
    sale_system_name: '',
    voucher_number: '',
    remark: '',
    src_order_no: '',
    pay_date: '',
    sale_system_id: '',
    src_order_id: '',
    supplier_id: '',
    operator_id: '',
    settle_type_id: '',
    actually_pay_price: '',
  },
  tableList: [],
  useList: [],
  formRules: {
    sale_system_id: [{ required: true, validator: formValidatePass.sale_system_id(), message: '请选择营销体系', trigger: 'change' }],
    pay_date: [{ required: true, message: '请选择实付日期', trigger: 'blur' }],
    supplier_id: [{ required: true, message: '请选择供方名称', trigger: 'blur' }],
    settle_type_id: [{ required: true, message: '请选择收款账户', trigger: ['blur'] }],
  },
  multipleSelection: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

onMounted(() => {
  state.form.pay_date = getCurrentDate()
  const resDes = getDefaultSaleSystem()
  state.form.sale_system_id = resDes?.default_sale_system_id
})

const showBox = ref(false)

function handleChangeShowBox(value: boolean) {
  if (state.useList.length) {
    ElMessageBox.confirm('取消使用预付将清空已添加的预收信息，是否确认？', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      state.useList = []
      showBox.value = value
    })
  }
  else {
    showBox.value = value
  }
}
// 自动核销单据

const { fetchData: getAutoWriteOffListApi, success: autoWriteOffSuccess, msg: autoWriteOffMsg, data: autoWriteOffData } = GetAutoWriteOffList()

async function handleAutoSelectWriteOffDocx() {
  if (state.tableList.length) {
    try {
      await ElMessageBox.confirm('重新选择将清空现有数据，是否确认', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        state.tableList = []
      })
    }
    catch (e) {
      return
    }
  }

  await getAutoWriteOffListApi({
    money: formatPriceMul(state.form.actually_pay_price),
    supplier_id: state.form.supplier_id,
  })
  if (!autoWriteOffSuccess.value)
    return ElMessage.error(autoWriteOffMsg.value)

  if (!autoWriteOffData.value.list?.length)
    return ElMessage.info('没有可核销的单据')

  autoWriteOffData.value.list?.forEach((item: any) => {
    const deduction_price = 0
    const discount_price = 0
    const offset_price = 0
    const written_price = currency(formatPriceDiv(item.auto_write_off_money)).add(deduction_price).add(discount_price).add(offset_price).value
    state.tableList.push({
      ...item,
      src_order_data: item.pay_date,
      src_order_type: item.order_type,
      pay_price: formatPriceDiv(item.auto_write_off_money),
      deduction_price,
      discount_price,
      offset_price,
      written_price,
      remark: '',
      selected: false,
      src_order_id: item?.id,
    })
  })
}

const UseAdvanceDialogRef = ref()

const AddFinishDialogRef = ref()

function handAddUse() {
  UseAdvanceDialogRef.value.state.showModal = true
  UseAdvanceDialogRef.value.state.filterData.sale_system_id = state.form.sale_system_id
  UseAdvanceDialogRef.value.state.filterData.supplier_id = state.form.supplier_id
}

function handAddXi() {
  AddFinishDialogRef.value.state.showModal = true
  AddFinishDialogRef.value.state.supplier_id = state.form.supplier_id
}

function handDeleteUse(rowIndex: number) {
  state.useList.splice(rowIndex, 1)
}

function handDeleteXi(rowIndex: number) {
  state.tableList.splice(rowIndex, 1)
}

function handsSureUse(list: any) {
  list.forEach((item: any) => {
    state.useList.push({
      ...item,
      use_price: '',
      advance_pay_order_id: item.id,
    })
  })
  UseAdvanceDialogRef.value.state.showModal = false
}

function handsSureFinish(list: any) {
  list.forEach((item: any) => {
    const deduction_price = 0
    const discount_price = 0
    const offset_price = 0

    const written_price = currency(formatPriceDiv(item.auto_write_off_money))
      .add(deduction_price)
      .add(discount_price)
      .add(offset_price).value

    state.tableList.push({
      ...item,
      src_order_data: item.pay_date,
      src_order_type: item.order_type,
      pay_price: formatPriceDiv(item.auto_write_off_money),
      deduction_price,
      discount_price,
      offset_price,
      written_price: written_price || 0,
      remark: '',
      selected: false,
      src_order_id: item?.id,
    })
  })
  AddFinishDialogRef.value.state.showModal = false
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['paid_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'paid_price') as any)}`

      if (['total_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'total_price') as any)}`

      if (['unpaid_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'unpaid_price') as any)}`

      if (['pay_price'].includes(column.property))
        return `${sumNum(data, 'pay_price') as any}`

      if (['deduction_price'].includes(column.property))
        return `${sumNum(data, 'deduction_price') as any}`

      if (['offset_price'].includes(column.property))
        return `${sumNum(data, 'offset_price') as any}`

      if (['discount_price'].includes(column.property))
        return `${sumNum(data, 'discount_price') as any}`

      if (['written_price'].includes(column.property))
        return `${sumNum(data, 'written_price') as any}`

      return null
    }),
  ]
}

function FooterMethods({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_price'].includes(column.property))
        return `${sumNum(data, 'use_price') as any}`

      return null
    }),
  ]
}

const tableRefs = ref()
const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList.length) {
      state.tableList.map((item: any) => {
        item.written_price = currency(item.deduction_price)
          .add(item?.discount_price)
          .add(item?.offset_price)
          .add(item?.pay_price)
          .value.toFixed(2)
        return item
      })
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

watch(
  () => state.useList,
  () => {
    if (state.useList.length) {
      nextTick(() => {
        tableRefs.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const tableConfig_use = ref({
  showSlotNums: true,
  height: 300,
  fieldApiKey: fieldApiKeyList.MeetMangeActualPayAdd,
  footerMethod: (val: any) => FooterMethods(val),
  showOperate: true,
  operateWidth: '80',
})

const tableConfig = ref({
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  height: 400,
  fieldApiKey: fieldApiKeyList.MeetMangeActualPayAddDataDetail,
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

const ruleFormRef = ref()

const { fetchData: addPost, data: addData, success: addSuccess, msg: addMsg } = actuallyPayOrderpost()

// 提交数据
async function handleSure() {
  if (Number(new Decimal(Number(sumNum(state.useList, 'use_price'))).plus(new Decimal(Number(state.form.actually_pay_price)))) !== Number(sumNum(state.tableList, 'pay_price')))
    return ElMessage.error('实付金额+使用预付款与付款金额不等，请检查')

  const list = deepClone(state.tableList)
  for (let i = 0; i < list.length; i++) {
    if (Number(list[i].use_price) > formatPriceDiv(list[i].total_remain_price))
      return ElMessage.error('预付款的本次使用金额不大于未用金额')

    list[i].deduction_price = formatPriceMul(list[i].deduction_price)
    list[i].discount_price = formatPriceMul(list[i].discount_price)
    list[i].offset_price = formatPriceMul(list[i].offset_price)
    list[i].pay_price = formatPriceMul(list[i].pay_price)
  }
  const listTwo = deepClone(state.useList)
  for (let i = 0; i < listTwo.length; i++) {
    if (Number(listTwo[i].written_price) > formatPriceDiv(Number(listTwo[i].unpaid_price)))
      return ElMessage.error('核销金额不可大于未付金额')

    listTwo[i].use_price = formatPriceMul(listTwo[i].use_price)
  }

  state.form.pay_date = formatDate(state.form.pay_date)
  const query = {
    item_data: list,
    adv_item_data: listTwo,
    actually_pay_data: formatDate(state.form.pay_date),
    actually_pay_price: formatPriceMul(state.form.actually_pay_price),
    operator_id: state.form.operator_id || 0,
    sale_system_id: state.form.sale_system_id || 0,
    settle_type_id: state.form.settle_type_id || 0,
    supplier_id: state.form.supplier_id || 0,
    voucher_number: state.form.voucher_number,
    remark: state.form.remark,
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({ name: 'ActualPayDetail', query: { id: addData.value.id } })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const bulkSetting = ref<any>({})

const bulkShow = ref(false)

function bulkHand() {
  bulkShow.value = true
}

function handBulkClose() {
  bulkShow.value = false
}

async function bulkSubmit({ row, value }: any) {
  if (state.multipleSelection?.length <= 0)
    return ElMessage.error('请先勾选批量修改的数据')

  state.tableList?.map((item: any) => {
    if (item?.selected) {
      item[row.field] = value[row.field]
      return item
    }
  })
  bulkShow.value = false
  ElMessage.success('设置成功')
  state.multipleSelection = []
}

const columnList = ref([
  {
    field: 'order_no',
    title: '单号',
    minWidth: 100,
  },

  {
    field: 'pay_date',
    title: '日期',
    minWidth: 100,
  },
  {
    field: 'order_type_name',
    title: '单据类型',
    minWidth: 100,
  },
  {
    field: 'total_price',
    title: '应付金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'paid_price',
    title: '已付金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'unpaid_price',
    title: '未付金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'pay_price',
    title: '付款金额',
    minWidth: 100,
    soltName: 'pay_price',
  },
  {
    field: 'offset_price',
    title: '优惠金额',
    minWidth: 100,
    soltName: 'offset_price',
  },
  {
    field: 'discount_price',
    title: '折扣金额',
    minWidth: 100,
    soltName: 'discount_price',
  },
  {
    field: 'deduction_price',
    title: '扣款金额',
    minWidth: 100,
    soltName: 'deduction_price',
  },
  {
    field: 'written_price',
    title: '核销金额',
    minWidth: 100,
  },

  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    soltName: 'remark',
  },
])

const columnList_use = ref([
  {
    field: 'order_no',
    title: '单号',
    minWidth: 100,
  },
  {
    field: 'advance_pay_data',
    title: '预付日期',
    minWidth: 100,
    isDate: true,
    formatTime: 'YYYY-MM-DD',
  },
  {
    field: 'total_advance_price',
    title: '预付金额',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'total_used_price',
    title: '已用',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'total_remain_price',
    title: '未用',
    minWidth: 100,
    isPrice: true,
  },
  {
    field: 'use_price',
    title: '本次使用金额',
    minWidth: 100,
    soltName: 'use_price',
    required: true,
  },
])

const bulkList = reactive<any>([
  {
    field: 'pay_price',
    title: '付款金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'offset_price',
    title: '优惠金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'discount_price',
    title: '折扣金额',
    component: 'input',
    type: 'float',
  },
  {
    field: 'deduction_price',
    title: '扣款金额',
    component: 'input',
    type: 'float',
  },
])
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:" required>
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents v-model="state.form.sale_system_id" api="AdminsaleSystemgetSaleSystemDropdownList" label-field="name" value-field="id" clearable />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.form.voucher_number" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供方名称:" required>
          <template #content>
            <el-form-item prop="supplier_id">
              <SelectDialog
                v-model="state.form.supplier_id"
                api="BusinessUnitSupplierEnumlist"
                :query="{ name: componentRemoteSearch.name }"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @change-value="val => (state.form.operator_id = val.order_follower_id)"
                @on-input="val => (componentRemoteSearch.name = val)"
              />
              <!-- <SelectComponents
                @change-value="val => (state.form.operator_id = val.order_follower_id)"
                api="BusinessUnitSupplierEnumlist"
                label-field="name"
                value-field="id"
                v-model="state.form.supplier_id"
                clearable
              /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="实付日期:" required>
          <template #content>
            <el-form-item prop="pay_date">
              <el-date-picker v-model="state.form.pay_date" type="date" placeholder="实付日期" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="实付金额:">
          <template #content>
            <el-input v-model="state.form.actually_pay_price" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="使用预付款:">
          <template #content>
            {{ sumNum(state.useList, 'use_price') }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="付款金额:">
          <template #content>
            {{ sumNum(state.tableList, 'pay_price') }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="核销总金额:">
          <template #content>
            {{ sumNum(state.tableList, 'written_price') }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收款账户:" required>
          <template #content>
            <SelectSettleAccountDialog
              v-model="state.form.settle_type_id"
              field="name"
            />
            <!--            <SelectComponents v-model="state.form.settle_type_id" api="GetTypeSettleAccountsEnumList" label-field="name" value-field="id" clearable /> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="经手人:">
          <template #content>
            <SelectComponents v-model="state.form.operator_id" api="Adminemployeelist" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-input v-model="state.form.remark" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="使用预付:" copies="2">
          <template #content>
            <el-checkbox :checked="showBox" label="" size="large" @change="handleChangeShowBox" />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <FildCard v-show="showBox" title="使用预付款" :tool-bar="true" class="mt-[5px]">
    <template #right-top>
      <el-button plain type="primary" :disabled="state.form.sale_system_id === '' || state.form.supplier_id === ''" @click="handAddUse">
        新增
      </el-button>
    </template>
    <Table ref="tableRefs" :config="tableConfig_use" :table-list="state.useList" :column-list="columnList_use">
      <template #use_price="{ row }">
        <vxe-input v-model="row.use_price" min="0" type="float" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDeleteUse(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <FildCard title="核销明细" :tool-bar="true" class="mt-[5px]">
    <template #right-top>
      <el-button plain type="info" @click="bulkHand">
        批量操作
      </el-button>
      <el-button plain :disabled="!state.form.sale_system_id || !state.form.supplier_id" type="primary" @click="handleAutoSelectWriteOffDocx">
        自动选择核销单据
      </el-button>
      <el-button plain :disabled="!state.form.sale_system_id || !state.form.supplier_id" type="primary" @click="handAddXi">
        新增
      </el-button>
    </template>
    <Table ref="tableRef" :config="tableConfig" :table-list="state.tableList" :column-list="columnList">
      <template #pay_price="{ row }">
        <vxe-input v-model="row.pay_price" type="float" />
      </template>
      <template #offset_price="{ row }">
        <vxe-input v-model="row.offset_price" type="float" />
      </template>
      <template #deduction_price="{ row }">
        <vxe-input v-model="row.deduction_price" type="float" />
      </template>
      <template #discount_price="{ row }">
        <vxe-input v-model="row.discount_price" type="float" />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" />
      </template>
      <template #operate="{ rowIndex }">
        <el-button text type="danger" @click="handDeleteXi(rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose" />
  <UseAdvanceDialog ref="UseAdvanceDialogRef" @handle-sure="handsSureUse" />
  <AddFinishDialog ref="AddFinishDialogRef" @handle-sure="handsSureFinish" />
</template>

<style></style>
