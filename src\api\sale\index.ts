import { useRequest } from '@/use/useRequest'

// 获取列表
export const GetSaleTransferOrderDetailList = () => {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/getSaleTransferOrderDetailList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

export const AddSaleTransferReturnOrder = () => {
  return useRequest({
    url: '/admin/v1/sale/saleTransferOrder/addSaleTransferReturnOrder',
    method: 'post',
  })
}

export const GetBySrcId = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/productSale/getBySrcId',
    method: 'get',
  })
}
