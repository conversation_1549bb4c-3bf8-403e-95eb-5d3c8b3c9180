declare namespace Api.FabricFlyCancel {
  /**
   * 布飞列表请求参数
   */
  export interface ListRequest {
    /** 布飞条码 */
    barcode?: string
    /** 机台号 */
    machine_no?: string
    /** 织工姓名 */
    weaver_name?: string
    /** 生产通知单号 */
    production_notice_no?: string
    /** 状态：1-正常，2-已取消 */
    status?: number
    /** 取消原因 */
    cancel_reason?: number
    /** 创建开始时间 */
    create_time_start?: string
    /** 创建结束时间 */
    create_time_end?: string
  }

  /**
   * 布飞列表响应数据
   */
  export interface ListResponse {
    /** 布飞ID */
    id: number
    /** 布飞条码 */
    barcode: string
    /** 机台号 */
    machine_no: string
    /** 织工姓名 */
    weaver_name: string
    /** 生产通知单号 */
    production_notice_no: string
    /** 重量 */
    weight: number
    /** 状态：1-正常，2-已取消 */
    status: number
    /** 取消原因ID */
    cancel_reason?: number
    /** 取消原因名称 */
    cancel_reason_name?: string
    /** 创建时间 */
    create_time: string
    /** 更新时间 */
    update_time: string
  }

  /**
   * 布飞详情响应数据
   */
  export interface DetailResponse {
    /** 布飞ID */
    id: number
    /** 布飞条码 */
    barcode: string
    /** 机台号 */
    machine_no: string
    /** 织工姓名 */
    weaver_name: string
    /** 生产通知单号 */
    production_notice_no: string
    /** 重量 */
    weight: number
    /** 状态：1-正常，2-已取消 */
    status: number
    /** 取消原因ID */
    cancel_reason?: number
    /** 取消原因名称 */
    cancel_reason_name?: string
    /** 备注 */
    remark?: string
    /** 创建时间 */
    create_time: string
    /** 更新时间 */
    update_time: string

    // 称重信息
    /** 电子称重量 */
    electronic_weight?: number
    /** 实际重量 */
    actual_weight?: number
    /** 称重时间 */
    weigh_time?: string
    /** 称重员姓名 */
    weigher_name?: string
    /** 是否交班织工 */
    is_shift_worker?: boolean
    /** 交班时长 */
    shift_duration?: string

    // 验布信息
    /** 等级 */
    grade?: string
    /** 查布员姓名 */
    inspector_name?: string
    /** 验布时间 */
    inspect_time?: string
    /** 疵点数量 */
    defect_count?: number
    /** 质检分数 */
    quality_score?: number
    /** 是否合格 */
    is_qualified?: boolean

    // 操作记录
    /** 操作记录列表 */
    operation_records?: OperationRecord[]
  }

  /**
   * 操作记录
   */
  export interface OperationRecord {
    /** 操作类型 */
    operation_type: number
    /** 操作类型名称 */
    operation_type_name: string
    /** 操作人姓名 */
    operator_name: string
    /** 备注 */
    remark?: string
    /** 创建时间 */
    create_time: string
  }

  /**
   * 取消请求参数
   */
  export interface CancelRequest {
    /** 布飞ID */
    id: number
    /** 取消原因 */
    cancel_reason: number
    /** 备注 */
    remark?: string
  }

  /**
   * 批量取消请求参数
   */
  export interface BatchCancelRequest {
    /** 布飞ID列表 */
    ids: number[]
    /** 取消原因 */
    cancel_reason: number
    /** 备注 */
    remark?: string
  }

  /**
   * 恢复请求参数
   */
  export interface RestoreRequest {
    /** 布飞ID */
    id: number
  }

  /**
   * 取消原因枚举项
   */
  export interface CancelReasonEnum {
    /** 值 */
    value: number
    /** 标签 */
    label: string
  }

  /**
   * 详情请求参数
   */
  export interface DetailRequest {
    /** 布飞ID */
    id?: number
    /** 布飞条码 */
    barcode?: string
  }

  /**
   * 条码取消请求参数
   */
  export interface BarcodeCancelRequest {
    /** 布飞条码 */
    barcode: string
    /** 取消原因 */
    cancel_reason: number
    /** 备注 */
    remark?: string
  }
}
