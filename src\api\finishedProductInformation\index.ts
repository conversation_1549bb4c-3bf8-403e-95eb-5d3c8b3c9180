import {RequestOptions, useRequest} from '@/use/useRequest'

// 获取列表
export const GetFinishProductList = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/getFinishProductList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取下拉列表
export const GetFinishProductDropdownList = (config: RequestOptions) => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/getFinishProductDropdownList',
    method: 'get',
    pagination: true,
    ...config,
  })
}

// 新增
export const AddFinishProduct = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/addFinishProduct',
    method: 'post',
  })
}

// 编辑
export const UpdateFinishProduct = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/updateFinishProduct',
    method: 'put',
  })
}

// 删除
export const DeleteFinishProduct = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/deleteFinishProduct',
    method: 'delete',
  })
}

// 修改状态
export const UpdateFinishProductStatus = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/updateFinishProductStatus',
    method: 'put',
  })
}

// 详情
export const GetFinishProduct = () => {
  return useRequest({
    url: '/admin/v1/product/finishProduct/getFinishProduct',
    method: 'get',
  })
}

// 仓库下拉列表
export const GetPhysicalWarehouseDropdownList = () => {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/getPhysicalWarehouseDropdownList',
    method: 'get',
  })
}
