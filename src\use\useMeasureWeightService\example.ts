// useMeasureWeightService 数据头尾配置使用示例

import { useMeasureWeightService } from './index'

// 示例1: 使用默认的 \r\n 分割
export function useDefaultDelimiter() {
  const weightService = useMeasureWeightService()

  // 默认使用 \r\n (CR LF) 分割数据包
  return weightService
}

// 示例2: 使用自定义数据头尾
export function useCustomDelimiter() {
  const weightService = useMeasureWeightService({
    dataHeader: [0x02], // STX (Start of Text)
    dataFooter: [0x03], // ETX (End of Text)
    useCustomDelimiter: true,
  })

  return weightService
}

// 示例3: 使用字符串作为数据头尾
export function useStringDelimiter() {
  const weightService = useMeasureWeightService()

  // 动态设置数据头尾
  weightService.setDataHeader('START') // 设置 "START" 作为数据头
  weightService.setDataFooter('END') // 设置 "END" 作为数据尾
  weightService.toggleCustomDelimiter(true) // 启用自定义分割模式

  return weightService
}

// 示例4: 使用十六进制字节作为分割符
export function useHexDelimiter() {
  const weightService = useMeasureWeightService()

  // 设置十六进制字节作为分割符
  weightService.setDataHeader([0xAA, 0xBB]) // 数据头: 0xAABB
  weightService.setDataFooter([0xCC, 0xDD]) // 数据尾: 0xCCDD
  weightService.toggleCustomDelimiter(true)

  return weightService
}

// 示例5: 只使用数据尾分割（无数据头）
export function useFooterOnlyDelimiter() {
  const weightService = useMeasureWeightService({
    dataHeader: [], // 空数据头
    dataFooter: [0x0A], // 只使用 LF 作为结束符
    useCustomDelimiter: true,
  })

  return weightService
}

// 示例6: 动态切换分割模式
export function useDynamicDelimiter() {
  const weightService = useMeasureWeightService()

  // 运行时动态更新配置
  function switchToCustomMode() {
    weightService.updateDelimiterConfig({
      dataHeader: [0x7E], // ~ 字符
      dataFooter: [0x7E], // ~ 字符
      useCustomDelimiter: true,
    })
  }

  function switchToDefaultMode() {
    weightService.toggleCustomDelimiter(false)
  }

  // 获取当前配置
  function getCurrentConfig() {
    return weightService.getDelimiterConfig()
  }

  return {
    ...weightService,
    switchToCustomMode,
    switchToDefaultMode,
    getCurrentConfig,
  }
}

// 常见的数据分割符配置
export const COMMON_DELIMITERS = {
  // 默认 CR LF
  CRLF: {
    dataHeader: [],
    dataFooter: [13, 10], // \r\n
    useCustomDelimiter: false,
  },

  // 只使用 LF
  LF: {
    dataHeader: [],
    dataFooter: [10], // \n
    useCustomDelimiter: true,
  },

  // STX/ETX 控制字符
  STX_ETX: {
    dataHeader: [0x02], // STX
    dataFooter: [0x03], // ETX
    useCustomDelimiter: true,
  },

  // 自定义字符串分割
  CUSTOM_STRING: {
    dataHeader: [83, 84, 65, 82, 84], // "START"
    dataFooter: [69, 78, 68], // "END"
    useCustomDelimiter: true,
  },

  // 十六进制分割符
  HEX_DELIM: {
    dataHeader: [0xAA, 0xBB],
    dataFooter: [0xCC, 0xDD],
    useCustomDelimiter: true,
  },
}
