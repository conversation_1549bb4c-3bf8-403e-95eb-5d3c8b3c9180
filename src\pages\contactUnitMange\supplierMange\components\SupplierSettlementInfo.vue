<script lang="ts" setup>
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { SettleType } from '@/common/enum'

interface Props {
  modelValue: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'settleTypeChange', value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理表单数据更新
function updateForm(field: string, value: any) {
  emit('update:modelValue', { ...props.modelValue, [field]: value })
}

// 处理结算类型变化
function handleSettleTypeChange(val: any) {
  updateForm('settle_type', val)
  emit('settleTypeChange', val)
}
</script>

<template>
  <div>
    <div class="sale">
      结算信息
    </div>
    <div class="line" />
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="默认结算类型:">
        <template #content>
          <el-form-item prop="settle_type">
            <SelectComponents
              :model-value="modelValue.settle_type"
              style="width: 300px"
              api="AdminenumsettleType"
              label-field="name"
              value-field="id"
              clearable
              @select="handleSettleTypeChange"
              @update:model-value="updateForm('settle_type', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem v-if="modelValue.settle_type === SettleType.weekType" label="结算周期:">
        <template #content>
          <el-form-item prop="settle_cycle">
            <SelectComponents
              :model-value="modelValue.settle_cycle"
              api="GetSettleCycleApi"
              label-field="name"
              value-field="id"
              @update:model-value="updateForm('settle_cycle', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem v-if="modelValue.settle_type === SettleType.dayType" label="自定义天数:">
        <template #content>
          <el-form-item prop="settle_cycle">
            <el-input-number
              :model-value="modelValue.settle_cycle"
              :precision="0"
              :min="0"
              style="width: 130px"
              placeholder="请输入默认结算天数"
              controls-position="right"
              @update:model-value="updateForm('settle_cycle', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem v-if="modelValue.settle_type === SettleType.moonType" label="自定义月份:">
        <template #content>
          <el-form-item prop="settle_cycle">
            <el-input-number
              :model-value="modelValue.settle_cycle"
              :precision="0"
              :min="0"
              style="width: 130px"
              placeholder="请输入默认结算天数"
              controls-position="right"
              @update:model-value="updateForm('settle_cycle', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="信用额度:">
        <template #content>
          <el-form-item prop="creditLimit">
            <el-input-number
              :model-value="modelValue.creditLimit"
              :precision="0"
              :min="0"
              style="width: 130px"
              placeholder="信用额度"
              controls-position="right"
              @update:model-value="updateForm('creditLimit', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="信用等级:">
        <template #content>
          <el-form-item prop="credit_level">
            <SelectComponents
              :model-value="modelValue.credit_level"
              style="width: 300px"
              api="CreditLevel"
              label-field="name"
              value-field="id"
              @update:model-value="updateForm('credit_level', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="染整收费方式:">
        <template #content>
          <el-form-item prop="dnf_charging_method">
            <SelectComponents
              :model-value="modelValue.dnf_charging_method"
              style="width: 300px"
              api="DnfChargingMethodEnum"
              label-field="name"
              value-field="id"
              @update:model-value="updateForm('dnf_charging_method', $event)"
            />
          </el-form-item>
        </template>
      </DescriptionsFormItem>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.line {
  background: #efefef;
  width: 100%;
  height: 3px;
  margin-top: 15px;
  margin-bottom: 20px;
}

.sale {
  margin-top: 40px;
  font-weight: 600;
}
</style>
