import { ElMessage } from 'element-plus'
import { getBaseUrl } from './constant'
import api from '@/util/http'

export const baseURL = getBaseUrl()
export async function upload(file: any, url: string) {
  return await new Promise((resolve: any, reject: any) => {
    const data = new FormData()
    data.append('file', file)
    api({
      url: baseURL + url,
      method: 'post',
      data,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
      .then((res) => {
        ElMessage.success('导入成功!')
        resolve(res.data)
      })
      .catch((error) => {
        ElMessage.error('失败!')

        reject(error)
      })
  })
}
