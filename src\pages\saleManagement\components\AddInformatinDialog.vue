<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import Table from '@/components/Table.vue'
// import SelectComponents from '@/components/SelectComponents/index.vue'
import { debounce, getFilterData, resetData } from '@/common/util'
import { getFinishProductDropdownListPage } from '@/api/productSalePlan'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    finish_product_code: '',
    finish_product_name: '',
    finish_product_width: '',
    finish_product_gram_weight: '',
  },
  showModal: false,
  rowIndex: -1,
  multipleSelection: [],
})

const { fetchData, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getFinishProductDropdownListPage()

const getData = debounce(async () => {
  await fetchData(getFilterData(state.filterData))
}, 400)

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
    else
      loading.value = false
  },
)

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// function handReset() {
//   state.filterData = resetData(state.filterData)
// }

const columnList = ref([
  {
    field: 'finish_product_code',
    title: '成品编号',
    width: 100,
    fixed: 'left',
    cellRender: {
      name: 'LinkJump',
      props: {
        routerName: 'FinishedProductInformationDetail',
        params: (row: any) => ({ id: row.id }),
      },
    },
  },
  {
    field: 'finish_product_name',
    title: '成品名称',
    width: 100,
    fixed: 'left',
  },
  {
    field: 'type_grey_fabric_name',
    title: '布种类型',
    width: 70,
  },
  {
    field: 'finish_product_width',
    title: '幅宽',
    width: 100,
    soltName: 'finish_product_width',
  },
  {
    field: 'finish_product_gram_weight',
    title: '克重',
    width: 100,
    soltName: 'finish_product_gram_weight',
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒',
    width: 80,
    isWeight: true,
  },
  {
    field: 'weight_error',
    title: '空差',
    width: 80,
    isWeight: true,
  },
  {
    field: 'standard_weight',
    title: '标准数量',
    width: 80,
    isWeight: true,
  },
  {
    field: 'length_to_weight_rate',
    title: '米转公斤比率',
    width: 100,
    isPrice: true,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    width: 80,
    fixed: 'right',
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
    fixed: 'right',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  if (!state.multipleSelection.length)
    return ElMessage.error('请至少选择一条数据')
  loading.value = true
  // 这里用setTimeout是因为loading.value = true后，表格数据还没加载出来，会导致表格数据丢失，所以用setTimeout延迟一下
  setTimeout(() => {
    emits('handleSure', state.multipleSelection)
  }, 0)
}

const tableConfig = ref({
  showSlotNums: true,
  showCheckBox: true,
  loading,
  showPagition: true,
  page,
  size,
  total,
  height: '100%',
  filterStatus: false,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})
defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer title="从基础资料中添加成品" width="1200" height="75vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full">
      <div class="descriptions_row items-center" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="成品编号:">
          <template #content>
            <el-input v-model="state.filterData.finish_product_code" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称:">
          <template #content>
            <el-input v-model="state.filterData.finish_product_name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品幅宽:">
          <template #content>
            <el-input v-model="state.filterData.finish_product_width" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品克重:">
          <template #content>
            <el-input v-model="state.filterData.finish_product_gram_weight" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex-1 overflow-y-hidden">
        <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
          <template #finish_product_gram_weight="{ row }">
            {{ row?.finish_product_gram_weight }}{{ row?.finish_product_gram_weight_unit_name }}
          </template>
          <template #finish_product_width="{ row }">
            {{ row?.finish_product_width }}{{ row?.finish_product_width_unit_name }}
          </template>
          <template #is_color_card="{ row }">
            {{ row.is_color_card ? '是' : '否' }}
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
