<script setup lang="ts">
import { computed, nextTick, onUnmounted, ref } from 'vue'
import MarkdownIt from 'markdown-it'
import highlightjs from 'markdown-it-highlightjs'
import 'highlight.js/styles/github.css'
import * as echarts from 'echarts'

// 添加防抖函数
function debounce(fn: Function, delay: number) {
  let timer: NodeJS.Timeout | null = null
  return function (...args: any[]) {
    if (timer)
      clearTimeout(timer)
    timer = setTimeout(() => fn(...args), delay)
  }
}

// 管理图表实例
const chartInstances = ref<echarts.ECharts[]>([])

// 清理图表实例
onUnmounted(() => {
  chartInstances.value.forEach(chart => chart.dispose())
  chartInstances.value = []
})

function initCharts(charts: any[], _cleanText: string) {
  const container = document.querySelector('.content')
  if (!container)
    return

  // 清理旧图表实例
  chartInstances.value.forEach(chart => chart.dispose())
  chartInstances.value = []

  // 找到所有图表标记位置
  const paragraphs = container.querySelectorAll('p')
  const placeholders = Array.from(paragraphs).filter(p => p.textContent?.includes('#echart_start#'))

  placeholders.forEach((placeholder, index) => {
    if (charts[index]) {
      const existingContainer = placeholder.nextElementSibling
      let chartContainer: HTMLElement

      if (existingContainer?.classList.contains('chart-container')) {
        // 复用已存在的容器
        chartContainer = existingContainer as HTMLElement
      }
      else {
        // 创建新容器
        chartContainer = document.createElement('div')
        chartContainer.className = 'chart-container'
        chartContainer.style.width = '100%'
        chartContainer.style.height = '400px'
        chartContainer.style.marginBottom = '20px'
        placeholder.parentNode?.insertBefore(chartContainer, placeholder.nextSibling)
      }

      // 初始化图表
      const chart = echarts.init(chartContainer)
      chart.setOption(charts[index])
      chartInstances.value.push(chart)

      // 清除占位符
      placeholder.remove()
    }
  })
}
function parseEchartsConfig(text: string) {
  const charts: any[] = []
  // 修改正则表达式以匹配多行内容
  const regex = /#echart_start#\s*([\s\S]*?)\s*#echart_end#/g
  let match

  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(text)) !== null) {
    try {
      // 清理配置文本中的多余空格和换行
      const configText = match[1].replace(/\n/g, '').trim()

      const config = JSON.parse(configText)
      charts.push(config)
    }
    catch (e) {
      console.error('解析 图表配置失败:', e)
    }
  }
  return charts
}

const md = new MarkdownIt({
  linkify: true,
  breaks: true,
  html: true, // 允许 HTML 标签
  typographer: true, // 启用一些语言中立的替换 + 引号美化
}).use(highlightjs)

const loading = ref(false)
const text = ref('')
const error = ref<Error | null>(null)

// 添加容器引用
const containerRef = ref<HTMLElement | null>(null)

// 滚动到底部方法
function scrollToBottom() {
  if (containerRef.value)
    containerRef.value.scrollTop = containerRef.value.scrollHeight
}
// 更新文本内容
function onUpdate(fullText: string) {
  text.value = fullText
  // 使用 nextTick 确保 DOM 更新后再滚动
  nextTick(() => {
    scrollToBottom()
  })
}

// 完成时的处理
function onFinish(message: string) {
  loading.value = false
  text.value = message
}

// 错误处理
function onError(err: Error) {
  error.value = err
  loading.value = false
}
// 使用防抖处理图表初始化
const debouncedInitCharts = debounce(initCharts, 200)
// 将文本转换为 HTML
const renderText = computed(() => {
  if (error.value)
    return `<div class="error">${error.value.message}</div>`

  if (!text.value)
    return ''

  try {
    const charts = parseEchartsConfig(text.value)
    const cleanText = text.value.replace(/#echart_start#[\s\S]*?#echart_end#/g, '<p>#echart_start#</p>')
    const html = md.render(cleanText)

    // 使用防抖的图表初始化
    debouncedInitCharts(charts, cleanText)

    return html
  }
  catch (err: any) {
    return `<div class="error">Markdown 渲染错误: ${err.message}</div>`
  }
})

// 暴露方法给父组件
defineExpose({
  onUpdate,
  onFinish,
  onError,
})
</script>

<template>
  <div
    ref="containerRef" class="output-container markdown-body"
  >
    <div
      class="content"
      v-html="renderText"
    />
    <div v-if="loading" class="loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
    </div>
    <div v-if="error" class="error-message">
      {{ error.message }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.output-container {
  position: relative;
  padding: 12px;
  min-height: 100px;
  max-height: 500px;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: white;
  color: #333;
  .content {
    :deep(h1){
      font-size: 20px;
      font-weight: bold;
    }
    :deep(h2){
      font-size: 18px;
      font-weight: bold;
    }
    :deep(p) {
      margin: 8px 0;
    }

    :deep(pre) {
      margin: 16px 0;
      padding: 16px;
      background-color: #f6f8fa;
      border-radius: 6px;
      overflow: auto;
    }

    :deep(code) {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      font-size: 14px;
      line-height: 1.5;
    }

    :deep(ul), :deep(ol) {
      padding-left: 20px;
      margin: 8px 0;
    }

    :deep(table) {
      border-collapse: collapse;
      margin: 16px 0;
      width: 100%;
    }

    :deep(th), :deep(td) {
      border: 1px solid #dfe2e5;
      padding: 6px 13px;
    }

    :deep(blockquote) {
      margin: 16px 0;
      padding: 0 16px;
      color: #6a737d;
      border-left: 4px solid #dfe2e5;
    }
  }
}

.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  word-break: break-word;

  :deep(pre) {
    background-color: #f6f8fa;
    border-radius: 6px;
    padding: 16px;
    overflow: auto;
  }

  :deep(code) {
    background-color: rgba(175, 184, 193, 0.2);
    border-radius: 6px;
    padding: 0.2em 0.4em;
    font-size: 85%;
  }
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.error-message {
  color: #f56c6c;
  padding: 8px;
  text-align: center;
}
.chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}
</style>
