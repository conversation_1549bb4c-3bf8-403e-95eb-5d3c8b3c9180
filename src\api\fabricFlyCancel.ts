import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取布飞列表
export function getFabricFlyList() {
  return useRequest<Api.FabricFlyCancel.ListRequest, ResponseList<Api.FabricFlyCancel.ListResponse>>({
    url: '/admin/v1/grey_fabric_manage/fabricFly/getFabricFlyList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出布飞列表
export function getFabricFlyListExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/fabricFly/getFabricFlyList',
    method: 'get',
    nameFile,
  })
}

// 获取布飞详情
export function getFabricFlyDetail() {
  return useRequest<Api.FabricFlyCancel.DetailRequest, Api.FabricFlyCancel.DetailResponse>({
    url: '/admin/v1/grey_fabric_manage/fabricFly/getFabricFlyDetail',
    method: 'get',
  })
}

// 布飞取消/作废
export function fabricFlyCancel() {
  return useRequest<Api.FabricFlyCancel.CancelRequest, void>({
    url: '/admin/v1/grey_fabric_manage/fabricFly/cancel',
    method: 'put',
  })
}

// 布飞批量取消
export function fabricFlyBatchCancel() {
  return useRequest<Api.FabricFlyCancel.BatchCancelRequest, void>({
    url: '/admin/v1/grey_fabric_manage/fabricFly/batchCancel',
    method: 'put',
  })
}

// 布飞恢复
export function fabricFlyRestore() {
  return useRequest<Api.FabricFlyCancel.RestoreRequest, void>({
    url: '/admin/v1/grey_fabric_manage/fabricFly/restore',
    method: 'put',
  })
}

// 通过条码取消布飞
export function fabricFlyCancelByBarcode() {
  return useRequest<Api.FabricFlyCancel.BarcodeCancelRequest, void>({
    url: '/admin/v1/grey_fabric_manage/fabricFly/cancelByBarcode',
    method: 'put',
  })
}

// 获取布飞取消原因枚举
export function getFabricFlyCancelReasonEnum() {
  return useRequest<void, ResponseList<Api.FabricFlyCancel.CancelReasonEnum>>({
    url: '/admin/v1/enum/fabricFlyCancelReason',
    method: 'get',
  })
}
