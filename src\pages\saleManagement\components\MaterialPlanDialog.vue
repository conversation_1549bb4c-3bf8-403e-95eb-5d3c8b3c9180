<script setup lang="ts">
import Big from 'big.js'
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import currency from 'currency.js'
import SelectRawMaterial from './SelectRawMaterial/index.vue'
import { getProductStockDetail } from '@/api/materialPlan'
import { GetFinishProductEnum } from '@/api/enum'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { deleteToast } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SeeStockFabric from '@/pages/saleManagement/components/SeeStockFabric.vue'
import SelectStockFabric from '@/pages/saleManagement/components/SelectStockFabric.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { typeDisabledStrategy } from '@/pages/saleManagement/components/shared'
import { PushType } from '@/api/settlementType'
import { GetGreyFabricInfoListUseByOthersMenu } from '@/api/greyFabricInformation'
import SelectBusinessDialog from '@/components/SelectBusinessDialog/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'

const props = withDefaults(defineProps<{ isEdit: boolean }>(), {
  isEdit: true,
})

const emits = defineEmits(['handleSure'])

const tableConfig = ref({
  footerMethod: (val: any) => FooterMethod(val),
  showSlotNums: true,
  showOperate: true,
  operateWidth: '200',
  fieldApiKey: 'ProductMaterialPlanEditDrog',
})

const state = reactive<any>({
  showModal: false,
  modalName: '计划分配',
  code: '',
  name: '',
  horsepower: 0,
  weight: 0,
  tableData: [],
  canEnter: 0, // 可以录入的匹数
  total_roll: 0, // 录入的全部匹数
  row_id: -1,
  isDisabled: false,
  rowIndex: -1,
  plan_roll: 0,
  plan_weight: 0,
  color_name: '',
  plan_type_name: '',
  finish_product_gram_weight: '',
  finish_product_gram_weight_and_unit_name: '',
  finish_product_width: '',
  finish_product_width_and_unit_name: '',
  need_weight: '',
  remark: '',
  rowInfo: {},
  customer_id: '',
  product_color_code: '',
  product_color_name: '',
  product_id: '',
  product_color_id: '',
  product_level_id: '',
  product_code: '',
  product_name: '',
})
const { fetchData: getFinishProductApi, data: finishProductDetail, success: detailSuccess, msg: datailMsg } = GetFinishProductEnum()

async function getFinishProductDetail() {
  await getFinishProductApi({
    id: state.product_id,
  })
  if (!detailSuccess.value)
    return ElMessage.error(datailMsg.value)
}
const { fetchData: getPushType, data: pushTypeData } = PushType()
const { fetchData: getGreyFabricInfoListUseByOthersMenu, data: getGreyFabricInfoListUseByOthersMenuData } = GetGreyFabricInfoListUseByOthersMenu()

async function getEnum() {
  getPushType()
  getGreyFabricInfoListUseByOthersMenu()
}

watch(
  () => state.showModal,
  () => {
    if (state.showModal) {
      getFinishProductDetail()
      getEnum()
      if (!state.tableData.length) {
        nextTick(() => {
          state.tableData.push({
            grey_fabric_id: state.rowInfo.grey_fabric_id,
            gray_fabric_color_id: state.rowInfo?.grey_fabric_color_id,
            product_supplier_id: '',
            roll: state.horsepower,
            dye_factory_id: '',
            weave_factory_id: '',
            push_type: '',
            weight: state.weight,
            raw_material_supplier_id: '',
            delivery_time: '',
            currentSelectRow: state.rowInfo,
          })
        })
      }
    }
  },
)

const columnList = ref([
  {
    field: 'push_type',
    title: '计划类型',
    minWidth: 140,
    soltName: 'push_type',
    required: true,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 140,
    soltName: 'code',
  },
  {
    field: 'grey_fabric_id',
    title: '坯布名称',
    minWidth: 140,
    soltName: 'name',
  },
  {
    field: 'gray_fabric_color_id',
    title: '织坯颜色',
    minWidth: 140,
    soltName: 'gray_fabric_color_id',
  },

  {
    field: 'roll',
    title: '匹数',
    minWidth: 140,
    soltName: 'horsepower',
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 140,
    soltName: 'weight',
  },
  {
    field: 'product_supplier_id',
    title: '成品供应商',
    minWidth: 140,
    soltName: 'product_supplier_id',
  },
  {
    field: 'grey_fabric_supplier_id',
    title: '坯布供应商',
    minWidth: 140,
    soltName: 'grey_fabric_supplier_id',
  },
  {
    field: 'raw_material_supplier_id',
    title: '原料供应商',
    minWidth: 140,
    soltName: 'raw_material_supplier_id',
  },
  {
    field: 'raw_material_code',
    title: '原料编号',
    minWidth: 140,
    soltName: 'raw_material_code',
  },
  {
    field: 'raw_material_name',
    title: '原料名称',
    minWidth: 140,
  },
  {
    field: 'raw_material_color_code',
    title: '原料色号',
    minWidth: 140,
    soltName: 'raw_material_color_code',
  },
  {
    field: 'raw_material_color_name',
    title: '原料颜色',
    minWidth: 140,
    soltName: 'raw_material_color_name',
  },
  {
    field: 'unit_name',
    title: '原料单位',
    minWidth: 140,
  },
  {
    field: 'raw_material_weight',
    title: '原料数量',
    minWidth: 140,
    soltName: 'raw_material_weight',
  },
  // {
  //   field: 'raw_material_supplier_id',
  //   title: '原料供应商',
  //   minWidth: 140,
  //   soltName: 'raw_material_supplier_id',
  // },
  {
    field: 'dye_raw_material_factory_id',
    title: '染纱厂',
    minWidth: 140,
    soltName: 'dye_raw_material_factory_id',
  },
  {
    field: 'dye_factory_id',
    title: '染厂',
    minWidth: 140,
    soltName: 'dye_factory_id',
  },
  {
    field: 'weave_factory_id',
    title: '织厂',
    minWidth: 140,
    soltName: 'weave_factory_id',
  },
  {
    field: 'delivery_time',
    title: '交期',
    minWidth: 140,
    soltName: 'delivery_time',
  },
])

function handCancel() {
  state.showModal = false
}

async function handleSure() {
  // if (state.total_roll > state.canEnter) {
  //   return ElMessage.error('匹数不可大于原库存匹数')
  // }
  const list = JSON.parse(JSON.stringify(state.tableData))
  for (let i = 0; i < list.length; i++) {
    // push_type的值是4和5的时候，坯布编号和名称必填
    if (list[i].push_type === 4 || list[i].push_type === 5) {
      if (list[i].grey_fabric_id === '' || list[i].grey_fabric_id === undefined || list[i].grey_fabric_id === null)
        return ElMessage.error('"坯布编号"是必填内容')
    }
    if (list[i].roll === '' || list[i].weight === '')
      return ElMessage.error('"匹数"是必填内容')

    if (list[i].push_type === 4 && list[i].grey_fabric_id === '')
      return ElMessage.error('计划单类型为"坯布采购"，必须选择"坯布编号"和"坯布名称"')

    if (!typeDisabledStrategy(list[i].push_type, 'raw_material_code') && !list[i].raw_material_code)
      return ElMessage.error('必须选择"原料编号"')

    list[i].raw_material_weight = list[i].raw_material_weight || 0
  }
  emits('handleSure', { ...state, tableData: list })
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property)) {
        // state.total_roll = sumTotal(data, 'roll')
        state.plan_roll = data.reduce((result: number, curr) => {
          // 非 原料采购和原料加工
          if (!isRawMaterialType(curr))
            result = currency(curr.roll).add(result).value

          return result
        }, 0)
        return `${sumNum(data, 'roll')}`
      }
      if (['weight'].includes(column.property)) {
        // state.plan_weight = sumTotal(data, 'weight')
        state.plan_weight = data.reduce((result: number, curr) => {
          if (isRawMaterialType(curr))
            result = currency(curr.raw_material_weight).add(result).value
          else
            result = currency(curr.weight).add(result).value

          return result
        }, 0)
        return `${sumNum(data, 'weight')}`
      }
      return null
    }),
  ]
}

const tableRef = ref()

watch(
  () => state.tableData,
  () => {
    if (state.tableData.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handAdd() {
  state.tableData.push({
    grey_fabric_id: finishProductDetail.value.grey_fabric_id,
    gray_fabric_color_id: finishProductDetail.value.grey_fabric_color_id,
    product_supplier_id: '',
    roll: state.horsepower || 0,
    dye_factory_id: '',
    weave_factory_id: '',
    push_type: '',
    weight: state.weight || 0,
    raw_material_supplier_id: '',
    delivery_time: '',
    count: 0,
    is_new: true,
    currentSelectRow: {},
  })
  state.tableData = [...state.tableData]
}

const SelectStockFabricRef = ref()

const { fetchData, data } = getProductStockDetail()

async function handSelect(row: any, rowIndex: number) {
  SelectStockFabricRef.value.state.showModal = true
  SelectStockFabricRef.value.state.rowIndex = rowIndex
  SelectStockFabricRef.value.state.info = {
    code: state.code,
    name: state.name,
    color_name: state.color_name,
    horsepower: state.horsepower,
    plan_roll: state.plan_roll,
    product_color_code: state.product_color_code,
    color_code: state.product_color_code,
    customer_name: state.customer_name,
    product_code: state.product_code,
    product_name: state.product_name,
    needWeight: state.weight,
    product_color_id: state.product_color_id,
    // customer_id: state.customer_id,
    finish_product_width: state.finish_product_width,
    finish_product_gram_weight: state.finish_product_gram_weight,
    roll: row.roll,
    weight: row.weight,
  }

  SelectStockFabricRef.value.filterData.product_id = state.product_id
  SelectStockFabricRef.value.filterData.radioValue = '2'

  // 不默认带出客户
  // SelectStockFabricRef.value.state.filterData.customer_id = state.customer_id
  SelectStockFabricRef.value.filterData.product_level_id = state.product_level_id
  SelectStockFabricRef.value.filterData.finish_product_width = state.finish_product_width
  SelectStockFabricRef.value.filterData.finish_product_gram_weight = state.finish_product_gram_weight
  SelectStockFabricRef.value.filterData.product_color_id = state.product_color_id

  if (row.count === 0)
    await fetchData({ id: row.id })

  const arr = row.count > 0 ? row?.stock_message_list : data?.value?.list

  // const arr = deepClone(row?.id && data?.value?.list.length ? data?.value?.list : row?.stock_message_list || [])
  const newArr = arr.map((item: any) => {
    // TODO:因为组件封的时候就是需要id来渲染勾选回去，所以新增个字段来代替后端返回的字段
    // TODO:stock_id也是后端需要的字段所以不能沿用这个字段去反查数据
    item.is_edit_id = !!row?.is_new
    item.edit_id = row?.is_new ? item.id : null
    item.id = item.stock_product_id
    item.use_roll = row.count === 0 ? formatPriceDiv(item.pmc_roll) : item.use_roll
    item.use_weight = row.count === 0 ? formatWeightDiv(item.pmc_weight) : item.use_weight

    return item
  })

  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.stock_message_list = newArr
      item.count += 1
      return item
    }
  })
  SelectStockFabricRef.value.state.multipleSelection = newArr
}

const SeeStockFabricRef = ref()

async function handSeeStock(row: any) {
  await fetchData({ id: row.id })
  SeeStockFabricRef.value.state.showModal = true

  SeeStockFabricRef.value.state.multipleSelection = data?.value?.list
}

const SelectComponentsRef = ref()

// 选择坯布，自动带出织坯颜色、坯布供货商、织厂供应商
function greyFabricChange(row: any, value: any) {
  row.gray_fabric_color_id = value.gray_fabric_color_id || ''
  row.grey_fabric_supplier_id = value.supplier_id?.[0] || ''
  // row.dye_factory_id = value.dye_factory_id || '' // 改成织厂 2023年12月13日11:25:01

  row.weave_factory_id = value.weave_factory_id || ''
}

async function changType(val: any, rowIndex: number, row: any) {
  if (val.id !== 1 && row.old_push_type === 1 && row.push_type !== '') {
    const res = await deleteToast('确认更换计划类型？已选的库存信息将被清空')

    if (res) {
      state.tableData.map((item: any, index: number) => {
        if (index === rowIndex) {
          item.stock_message_list = []
          return item
        }
      })
    }
    else {
      state.tableData.map((item: any, index: number) => {
        if (index === rowIndex) {
          item.push_type = 1
          return item
        }
      })
    }

    SelectComponentsRef.value.SelectOptionsRef.selectRef.blur() // 关闭选择
  }
  // TODO: 记住选择前的值以便重置
  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.old_push_type = val.id
      return item
    }
  })
}

function handSureStock(list: any, rowIndex: number) {
  const returnPlan = list.pop()
  const arr = list.map((item: any) => {
    item.pmc_roll = item.use_roll
    item.pmc_weight = item.use_weight
    return item
  })
  state.tableData.map((item: any, index: number) => {
    if (index === rowIndex) {
      item.stock_message_list = arr
      item.roll = returnPlan.roll
      item.weight = returnPlan.weight
      return item
    }
  })
}

function handDelete(rowIndex: number) {
  state.tableData.splice(rowIndex, 1)
}

const selectRow = ref()
const SelectRawMaterialRef = ref()
function openRaw(row: any, status: boolean) {
  if (status)
    return false
  selectRow.value = row

  SelectRawMaterialRef.value.state.showModal = true
  // SelectRawMaterialRef.value.state.grey_fabric_info_id = row.currentSelectRow.id
  SelectRawMaterialRef.value.state.grey_fabric_info_id = row.grey_fabric_id
}

// 清空原料编号
function clearMaterial() {
  selectRow.value.raw_material_name = ''
  selectRow.value.raw_material_weight = ''
  selectRow.value.raw_material_color_id = ''
}

function onSubmit(row: any) {
  selectRow.value.raw_material_name = row.name
  selectRow.value.raw_material_supplier_id = row.supplier_id?.[0] || selectRow.value.raw_material_supplier_id
  selectRow.value.raw_material_code = row.code
  selectRow.value.unit_name = row.unit_name
  selectRow.value.material_loss = row.material_loss || 0
  selectRow.value.material_ratio = row.material_ratio || 0
  selectRow.value.raw_material_id = row.id
  SelectRawMaterialRef.value.state.showModal = false
  selectRow.value.raw_material_id = row.radioValue === '1' ? row.raw_material_id : row.id

  computedData(selectRow.value)
}

const changeRow = ref<any>()
function changeData(row: any) {
  changeRow.value = row
  computedData(row)
}

function handleRawMaterialWeightBlur(row: any) {
  if (isRawMaterialType(row)) {
    state.plan_weight = state.tableData.reduce((result: number, curr: any) => {
      if (isRawMaterialType(curr))
        result = Number.parseFloat(Big(curr.raw_material_weight).plus(result).toFixed(2))
      else
        result = Number.parseFloat(Big(curr.weight).plus(result).toFixed(2))

      return result
    }, 0)
  }
}
// 原料采购和原料加工
function isRawMaterialType(row: any) {
  return row.push_type === 6 || row.push_type === 7
}

function computedData(row: any) {
  if (isRawMaterialType(row)) {
    row.raw_material_weight = Number.parseFloat(
      Big(row.weight || 0)
        .times(Big(row.material_ratio || 0).div(10000))
        .times(Big(Big(row.material_loss || 0).div(10000)).plus(1))
        .toFixed(4),
    )
  }
  else {
    row.raw_material_weight = ''
  }
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" :show-footer="props.isEdit" :title="state.modalName" width="80vw" height="80vh" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem label="成品编号:">
        <template #content>
          {{ state.code }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品名称:">
        <template #content>
          {{ state.name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色:">
        <template #content>
          {{ state?.color_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="需求匹数:">
        <template #content>
          {{ state?.horsepower }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="需求数量:">
        <template #content>
          {{ state?.weight }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="已计划匹数:">
        <template #content>
          {{ state?.plan_roll }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="已计划数量:">
        <template #content>
          {{ state?.plan_weight }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="订单类型:">
        <template #content>
          {{ state?.plan_type_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="克重:">
        <template #content>
          {{ state?.finish_product_gram_weight_and_unit_name }}
        </template>
      </DescriptionsFormItem>

      <DescriptionsFormItem label="幅宽:">
        <template #content>
          {{ state?.finish_product_width_and_unit_name }}
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="备注:">
        <template #content>
          {{ state?.remark }}
        </template>
      </DescriptionsFormItem>
    </div>
    <FildCard title="" class="mt-[5px]" no-shadow>
      <template #right-top>
        <div v-if="props.isEdit" class="buttom-oper" style="margin-bottom: 20px">
          <el-button type="primary" @click="handAdd">
            新增
          </el-button>
        </div>
      </template>
      <Table ref="tableRef" :config="tableConfig" :column-list="columnList" :table-list="state.tableData">
        <template #code="{ row }">
          <!-- <SelectComponents
            :disabled="!props.isEdit"
            ref="greyFabricInfoListUseByOthersMenuRef"
            v-model:currentItem="row.currentSelectRow"
            api="GetGreyFabricInfoListUseByOthersMenu"
            label-field="code"
            value-field="id"
            v-model="row.grey_fabric_id"
            clearable
          /> -->

          <SelectDialog
            v-model="row.grey_fabric_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'grey_fabric_id')"
            api="GetGreyFabricInfoListUseByOthersMenu"
            :table-list="getGreyFabricInfoListUseByOthersMenuData.list"
            :label-name="row.grey_fabric_code"
            label-field="code"
            :query="{ code: row.code }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
              },
            ]"
            @change-input="val => (row.code = val)"
            @change-value="greyFabricChange(row, $event)"
          />
        </template>
        <template #name="{ row }">
          <SelectDialog
            v-model="row.grey_fabric_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'grey_fabric_id')"
            api="GetGreyFabricInfoListUseByOthersMenu"
            :label-name="row.grey_fabric_name"
            label-field="name"
            :query="{ name: row.name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
              },
            ]"
            @change-input="val => (row.name = val)"
            @change-value="greyFabricChange(row, $event)"
          />
        </template>
        <template #gray_fabric_color_id="{ row }">
          <SelectDialog
            v-model="row.gray_fabric_color_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'grey_fabric_id')"
            api="getInfoProductGrayFabricColorList"
            :label-name="row.gray_fabric_color_name"
            label-field="name"
            :query="{ grey_fabric_id: row.grey_fabric_id, name: row.color_name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
              },
            ]"
            @on-input="val => (row.color_name = val)"
          />
        </template>

        <template #product_supplier_id="{ row }">
          <SelectDialog
            v-model="row.product_supplier_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'product_supplier_id')"
            :label-name="row.product_supplier_name"
            :query="{ unit_type_id: BusinessUnitIdEnum.finishedProduct, name: row.product_supplier_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.product_supplier_name = val)"
          />
        </template>
        <!-- 原料供应商 -->
        <template #raw_material_supplier_id="{ row }">
          <SelectDialog
            v-model="row.raw_material_supplier_id"
            :label-name="row.raw_material_supplier_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_supplier_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.rawMaterial, name: row.raw_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.raw_unit_name = val)"
          />
        </template>
        <!--        坯布供应商 -->
        <template #grey_fabric_supplier_id="{ row }">
          <SelectDialog
            v-model="row.grey_fabric_supplier_id"
            :label-name="row.grey_fabric_supplier_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'grey_fabric_supplier_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.blankFabric, name: row.grey_unit_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.grey_unit_name = val)"
          />
        </template>
        <!-- 原料编号 -->
        <template #raw_material_code="{ row }">
          <el-input
            v-model="row.raw_material_code"
            placeholder="请选择内容"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_code')"
            clearable
            @clear="clearMaterial"
            @click="openRaw(row, !props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_code'))"
          />
        </template>
        <!--        原料色号 -->
        <template #raw_material_color_code="{ row }">
          <!--          <vxe-input :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_color_code')" v-model="row.raw_material_color_code" placeholder=""></vxe-input> -->
          <SelectDialog
            v-model="row.raw_material_color_id"
            api="GetRawMaterialColor"
            label-field="code"
            :query="{ raw_matl_id: row.raw_material_id, code: row.color_code }"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_color_code') || !row.raw_material_code "
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'code',
                title: '原料色号',
                minWidth: 100,
              },
            ]"
            @on-input="val => (row.color_code = val)"
          />
        </template>
        <!--        原料颜色 -->
        <template #raw_material_color_name="{ row }">
          <!--          <vxe-input :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_color_name')" v-model="row.raw_material_color_name" placeholder=""></vxe-input> -->
          <SelectDialog
            v-model="row.raw_material_color_id"
            api="GetRawMaterialColor"
            label-field="name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_color_name') || !row.raw_material_code "
            :query="{ raw_matl_id: row.raw_material_id, code: row.color_name }"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.color_name = val)"
          />
        </template>
        <template #raw_material_weight="{ row }">
          <vxe-input
            v-model="row.raw_material_weight"
            type="float"
            :digits="4"
            :min="0"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'raw_material_weight')"
            placeholder=""
            @blur="handleRawMaterialWeightBlur(row)"
          />
        </template>
        <template #horsepower="{ row }">
          <vxe-input v-model="row.roll" :disabled="!props.isEdit" type="float" placeholder="必填" />
        </template>
        <template #weight="{ row }">
          <vxe-input v-model="row.weight" :disabled="!props.isEdit" type="float" placeholder="必填" @blur="changeData(row)" />
        </template>
        <template #dye_raw_material_factory_id="{ row }">
          <SelectBusinessDialog
            v-model="row.dye_raw_material_factory_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'dye_raw_material_factory_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeingMill }"
            api="BusinessUnitSupplierEnumlist"
            :default-value="{
              id: row.dye_raw_material_factory_id,
              name: row.dye_raw_material_factory_name,
            }"
          />
        </template>
        <template #dye_factory_id="{ row }">
          <SelectDialog
            v-model="row.dye_factory_id"
            :label-name="row.dye_factory_name"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'dye_factory_id')"
            :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory, name: row.dye_factory_name }"
            api="BusinessUnitSupplierEnumlist"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.dye_factory_name = val)"
          />
        </template>
        <template #weave_factory_id="{ row }">
          <SelectDialog
            v-model="row.weave_factory_id"
            :disabled="!props.isEdit || typeDisabledStrategy(row.push_type, 'weave_factory_id')"
            :label-name="row.weave_factory_name"
            :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: row.weave_factory_name }"
            api="GetBusinessUnitListApi"
            :column-list="[
              {
                field: 'name',
                title: '名称',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'name',
                    isEdit: true,
                    title: '名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'code',
                title: '编号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '编号',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (row.weave_factory_name = val)"
          />
        </template>
        <template #delivery_time="{ row }">
          <vxe-input v-model="row.delivery_time" :disabled="!props.isEdit" type="date" placeholder="" transfer />
        </template>
        <!--        计划类型 -->
        <template #push_type="{ row, rowIndex }">
          <SelectComponents
            ref="SelectComponentsRef"
            v-model="row.push_type"
            :disabled="!props.isEdit"
            placeholder="请选择"
            api="PushType"
            label-field="name"
            value-field="id"
            clearable
            @change-value="val => changType(val, rowIndex, row)"
          />
        </template>
        <template #operate="{ row, rowIndex }">
          <el-space :size="10">
            <el-link v-if="props.isEdit && row.push_type === 1" :underline="false" type="primary" @click="handSelect(row, rowIndex)">
              选取库存
            </el-link>
            <el-link v-if="!props.isEdit && row.push_type === 1" :underline="false" type="primary" @click="handSeeStock(row)">
              查看库存
            </el-link>
            <el-link v-if="props.isEdit" :underline="false" type="danger" @click="handDelete(rowIndex)">
              删除
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
  <!--  根据库存添加 -->
  <SelectStockFabric ref="SelectStockFabricRef" @handle-sure="handSureStock" />

  <SeeStockFabric ref="SeeStockFabricRef" />
  <SelectRawMaterial ref="SelectRawMaterialRef" @submit="onSubmit" />
</template>

<style></style>
