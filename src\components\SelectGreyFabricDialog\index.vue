<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getFilterData } from '@/common/util'
import type selectApi from '@/api/selectInit'

defineOptions({
  name: 'SelectProductDialog',
})

const props = withDefaults(defineProps<Props>(), {
  query: {},
  field: 'code',
  disabled: false,
  showAddLink: true,
  api: 'GetGreyFabricInfoListUseByOthers',
  isPushDefaultData: false, // 是否需要追加默认数据-分页获取不到的
})

const emits = defineEmits<{
  // (e: 'update:grey_fabric_id', val: number): void
  (e: 'changeValue', val: number): void
  (e: 'onInput', val: string): void
}>()

const modelValue = defineModel<number>('modelValue', { required: true })
interface Props {
  query?: any
  defaultValue?: {
    id: number
    name?: string
    code?: string
  }
  field?: 'code' | 'name'
  api?: keyof typeof selectApi | ''
  showAddLink?: boolean
  disabled?: boolean
  isPushDefaultData?: boolean
}
const fieldName = {
  name: '坯布名称',
  code: '坯布编号',
}

const productRef = ref()

const componentRemoteSearch = reactive({
  name: '',
  code: '',
})
function handleInputCustomerName(val: string) {
  componentRemoteSearch[props.field] = val.trim()
  emits('onInput', val)
}

function handleChangeValue(val: any) {
  emits('changeValue', val)
  modelValue.value = val.id
  // emits('update:grey_fabric_id', val.id)
}
const tableConfig = reactive({
  ...(props.showAddLink ? { radioWidth: '75' } : {}),
  radioConfig: {
    trigger: 'row',
  },
})

defineExpose({
  productRef,
})
const router = useRouter()
function handleSelectCustomer() {
  router.push({
    name: 'GreyFabricInformationAdd',
  })
}
</script>

<template>
  <SelectDialog
    ref="productRef"
    v-model="modelValue"
    :label-field="field"
    :api="api"
    :disabled="disabled"
    :table-config="tableConfig"
    :query="getFilterData({
      ...query,
      ...componentRemoteSearch,
    })"
    :is-push-default-data="isPushDefaultData"
    :column-list="[
      {
        field: 'code',
        colGroupHeader: true,
        title: '坯布编号',
        minWidth: 100,
        childrenList: [
          {
            field: 'code',
            title: '坯布编号',
            minWidth: 100,
          },
        ],
      },
      {
        field: 'name',
        colGroupHeader: true,
        title: '坯布名称',
        minWidth: 100,
        childrenList: [
          {
            field: 'name',
            title: '坯布名称',
            minWidth: 100,
          },
        ],
      },
    ]"
    :table-column="[
      {
        field,
        title: fieldName[field],
        defaultData: defaultValue ? {
          id: defaultValue?.id,
          name: defaultValue?.name,
          code: defaultValue?.code,
        } : null,
      },
    ]"
    @change-value="handleChangeValue"
    @on-input="handleInputCustomerName"
  >
    <template v-if="showAddLink" #radioHeader>
      <el-link type="primary" :underline="false" @click="handleSelectCustomer">
        快速新增
      </el-link>
    </template>
  </SelectDialog>
</template>

<style scoped></style>
