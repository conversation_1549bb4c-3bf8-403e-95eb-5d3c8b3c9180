import { useRequest } from '@/use/useRequest'

// 获取列表
export const shouldCollectOrderList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/getList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 作废
export const updateAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/updateAuditStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/updateAuditStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/updateAuditStatusReject',
    method: 'put',
  })
}
// 消审
export const updateAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/updateAuditStatusWait',
    method: 'put',
  })
}

// 新建
export const shouldCollectOrderAdd = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/add',
    method: 'post',
  })
}

// 获取详情
export const shouldCollectOrderDetail = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/get',
    method: 'get',
  })
}

// 编辑
export const shouldCollectOrderUpdate = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/shouldCollectOrder/update',
    method: 'put',
  })
}
