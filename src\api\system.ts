import { useRequest } from '@/use/useRequest'

export const GetPackageApi = () => {
  return useRequest({
    url: '/autoCode/package/getList',
    method: 'get',
    pagination: true,
  })
}

export const SetPackageApi = () => {
  return useRequest({
    url: '/autoCode/package/add',
    method: 'post',
  })
}

export const DelPackageApi = () => {
  return useRequest({
    url: '/autoCode/package/del',
    method: 'delete',
  })
}

// 获取数据库
export const GetDBApi = () => {
  return useRequest({
    url: '/autoCode/getDB',
    method: 'get',
  })
}

// 获取对应数据库的表
export const GetTablesApi = () => {
  return useRequest({
    url: '/autoCode/getTables',
    method: 'get',
  })
}

// 获取指定表所有字段信息
export const GetColumnApi = () => {
  return useRequest({
    url: '/autoCode/getColumn',
    method: 'get',
  })
}

//  获取自动创建代码预览
export const CreatePreviewApi = () => {
  return useRequest({
    url: '/autoCode/preview',
    method: 'post',
  })
}

//  创建自动化代码
export const CreateTempApi = () => {
  return useRequest({
    url: '/autoCode/createTemp',
    method: 'post',
  })
}

//  创建字典
export const AddDictionaryApi = () => {
  return useRequest({
    url: '/admin/v1/dictionary/addDictionary',
    method: 'post',
  })
}

//  更新字典
export const UpdateDictionaryApi = () => {
  return useRequest({
    url: '/admin/v1/dictionary/updateDictionary',
    method: 'put',
  })
}

//  删除字典
export const DeleteDictionaryApi = () => {
  return useRequest({
    url: '/admin/v1/dictionary/deleteDictionary',
    method: 'delete',
  })
}

//  获取字典
export const GetDictionaryListApi = () => {
  return useRequest({
    url: '/admin/v1/dictionary/getDictionaryList',
    pagination: true,
    method: 'get',
  })
}

//  获取字典详情
export const GetDictionaryApi = () => {
  return useRequest({
    url: '/admin/v1/dictionary/getDictionary',
    method: 'get',
  })
}

// 字典详情-id获取数据
export const GetDictionaryDetailApi = () => {
  return useRequest({
    url: '/admin/v1/dictionaryDetail/getDictionaryDetail',
    method: 'get',
  })
}

// 字典详情-删除
export const DelDictionaryDetailApi = () => {
  return useRequest({
    url: '/admin/v1/dictionaryDetail/deleteDictionaryDetail',
    method: 'delete',
  })
}

// 字典详情-列表
export const GetDictionaryDetailListApi = () => {
  return useRequest({
    url: '/admin/v1/dictionaryDetail/getDictionaryDetailList',
    pagination: true,
    method: 'get',
  })
}

// 字典详情-更新
export const UpdateDictionaryDetailApi = () => {
  return useRequest({
    url: '/admin/v1/dictionaryDetail/updateDictionaryDetail',
    method: 'put',
  })
}

// 字典详情-创建
export const AddDictionaryDetailApi = () => {
  return useRequest({
    url: '/admin/v1/dictionaryDetail/addDictionaryDetail',
    method: 'post',
  })
}
