<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import AddXimaDialog from '../components/AddXimaDialog.vue'
import UseYarnTotalModal from './components/UseYarnTotalModal.vue'
import {
  getGfmProduceReceiveOrder,
  updateGfmProduceReceiveOrderStatusCancel,
  updateGfmProduceReceiveOrderStatusPass,
  updateGfmProduceReceiveOrderStatusReject,
  updateGfmProduceReceiveOrderStatusWait,
} from '@/api/greyClothProduction'
import {
  formatDate,
  formatHashTag,
  formatPriceDiv,
  formatRollDiv,
  formatWeightDiv,
  sumNum,
  sumTotal,
} from '@/common/format'
import { deepClone, orderStatusConfirmBox } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { processDataOut } from '@/common/handBinary'
import useRouterList from '@/use/useRouterList'

const routerList = useRouterList()
const state = reactive<any>({
  yarnRatioList: [],
  yarnRatioList_total: [],
})

const rourte = useRoute()

const { fetchData, data, success, msg } = getGfmProduceReceiveOrder()
async function getData() {
  await fetchData({ id: rourte.query.id })
  if (!success.value)
    return ElMessage.error(msg.value)
  const processedItems = data.value.item_data.map((item, index: number) => {
    const idNum = Math.floor(Math.random() * 10000)
    item.use_yarn_data = item.use_yarn_data?.map((it: any) => {
      it.grayIndex = index
      it.grayId = idNum
      it.grey_fabric_full_name = formatHashTag(item?.grey_fabric_code, item?.grey_fabric_name)
      it.produce_notice_order_no = item.produce_notice_order_no
      it.roll = formatRollDiv(item.roll)
      return it
    })
    return item
  })
  state.yarnRatioList = processedItems.flatMap(item => item.use_yarn_data)
}
onMounted(() => {
  getData()
})

const columnList_fabic_config = ref({
  fieldApiKey: 'GreyClothProductionDetail',
  footerMethod: (val: any) => FooterMethod(val),
  height: '100%',
  showSlotNums: true,
  cellDBLClickEvent: (val: any) => cellDBLClickEvent(val, 1),
})

function cellDBLClickEvent(val: any, nums: number) {
  if (nums === 1) {
    state.yarnRatioList = val.row?.use_yarn_data
    state.yarnRatioList_total = flattenUseYarnItemData(val.row.use_yarn_data)
  }
}

// TODO:将数据源摊平
function flattenUseYarnItemData(arr: any) {
  const result: any = []

  arr.forEach((item: any) => {
    const useYarnItemData = item.use_yarn_item_data?.map((itemData: any) => Object.entries(itemData).reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}))
    result.push(...useYarnItemData)
  })

  return result
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['total_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'total_weight') as any)}`

      if (['other_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'other_price') as any)}`

      if (['total_price'].includes(column.property))
        return `￥${formatPriceDiv(sumNum(data, 'total_price') as any)}`

      if (['return_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'return_roll') as any)}`

      return null
    }),
  ]
}

const columnList_fabic = ref([
  // {
  //   field: 'sale_plan_order_item_no',
  //   title: '销售计划详情单号',
  //   fixed: 'left',
  //   minWidth: 150,
  // },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
    soltName: 'grey_fabric_code',
  },
  // {
  //   field: 'grey_fabric_name',
  //   title: '坯布名称',
  //   fixed: 'left',
  //   minWidth: 150,
  // },
  {
    field: 'produce_notice_order_no',
    title: '生产通知单号',
    minWidth: 140,
    soltName: 'produce_notice_order_no',
  },
  {
    field: 'customer_name',
    title: '所属客户',
    minWidth: 100,
  },
  {
    field: 'raw_material_yarn_name',
    title: '原料纱名',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    field: 'gray_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  // {
  //   field: 'unit_name',
  //   title: '单位',
  //   minWidth: 150,
  // },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'machine_number',
    title: '机台号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_remark',
    title: '坯布备注',
    minWidth: 100,
  },
  {
    field: 'total_weight',
    title: '总数量',
    minWidth: 80,
    soltName: 'total_weight',
  },
  {
    field: 'avg_weight',
    title: '平均数量',
    minWidth: 100,
    soltName: 'avg_weight',
  },
  {
    field: 'other_price',
    title: '其他金额',
    minWidth: 80,
    isPrice: true,
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
  },
  {
    field: 'roll',
    title: '收货匹数',
    minWidth: 80,
    fixed: 'right',
    isPrice: true,
  },
  {
    field: 'xima',
    title: '细码',
    minWidth: 80,
    soltName: 'xima',
    fixed: 'right',
  },
  {
    field: 'process_single_unit_price',
    title: '加工单价',
    minWidth: 80,
    fixed: 'right',
    isUnitPrice: true,
  },
  {
    field: 'total_price',
    title: '总金额',
    minWidth: 80,
    fixed: 'right',
    isPrice: true,
  },
  // {
  //   field: 'return_roll',
  //   title: '退货匹数',
  //   minWidth: 100,
  //   fixed: 'right',
  //   isPrice: true,
  // },
])
function handleClickLink(row: any) {
  routerList.push({
    name: 'ProductionNoticeDetail',
    query: { id: row?.produce_notice_order_item_id },
  })
}
const AddXimaDialogRef = ref()

function handSeeXima(row: any) {
  AddXimaDialogRef.value.state.showModal = true
  let listData = deepClone(row?.item_fc_data || [])
  listData = processDataOut(listData)
  AddXimaDialogRef.value.state.tableData = listData || []
  AddXimaDialogRef.value.state.canEnter = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.horsepower = formatPriceDiv(row.roll)
  AddXimaDialogRef.value.state.reference_weight = formatWeightDiv(row.reference_weight)
  AddXimaDialogRef.value.state.code = row.grey_fabric_code
  AddXimaDialogRef.value.state.name = row.grey_fabric_name
  AddXimaDialogRef.value.state.isDisabled = true
}

const closeLoading = ref(false)
const cancelLoading = ref(false)
const rejectLoading = ref(false)
const auditLoading = ref(false)
const eliminateLoading = ref(false)
async function updateStatus(audit_status: number) {
  const id: any = rourte.query.id?.toString()
  if (audit_status === 4)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被作废', title: '是否作废该订单？' }, api: updateGfmProduceReceiveOrderStatusCancel, loadingRef: cancelLoading })

  if (audit_status === 3)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击确定后订单将被驳回', title: '是否驳回该订单？' }, api: updateGfmProduceReceiveOrderStatusReject, loadingRef: rejectLoading })

  if (audit_status === 2)
    await orderStatusConfirmBox({ id, audit_status, message: { desc: '点击审核后订单将审核通过', title: '是否审核该订单？' }, api: updateGfmProduceReceiveOrderStatusPass, loadingRef: auditLoading })

  if (audit_status === 1) {
    await orderStatusConfirmBox({
      id,
      audit_status,
      message: { desc: '点击消审后订单将变为待审核状态', title: '是否消审该订单？' },
      api: updateGfmProduceReceiveOrderStatusWait,
      loadingRef: eliminateLoading,
    })
  }
  fetchData({ id: rourte.query.id })
}
const spanFields = [
  'grey_fabric_full_name',
  'produce_notice_order_no',
  'roll',
]
const tableConfig_other = ref({
  fieldApiKey: 'GreyClothProductionDetail_B',
  showSlotNums: true,
  showOperate: true,
  operateWidth: '80',
  height: '100%',
  colspanMethod: ({ row, _rowIndex, column, visibleData }: any) => {
    const cellValue = row[column.field]
    // cellValue为0时，合并单元格
    if (String(cellValue) && spanFields.includes(column.field)) {
      const prevRow = visibleData[_rowIndex - 1]
      let nextRow = visibleData[_rowIndex + 1]
      if (
        prevRow
        && prevRow[column.field] === cellValue
        && prevRow.production_notify_order_id === row.production_notify_order_id
      ) {
        return { rowspan: 0, colspan: 0 }
      }
      else {
        let countRowspan = 1
        while (
          nextRow
          && nextRow[column.field] === cellValue
          && nextRow.production_notify_order_id === row.production_notify_order_id
        )
          nextRow = visibleData[++countRowspan + _rowIndex]

        if (countRowspan > 1)
          return { rowspan: countRowspan, colspan: 1 }
      }
    }
  },
  footerMethod: (val: any) => FooterMethodOnce(val),
})
const useYarnTotalModalRef = ref()
// 打开弹窗的方法
function openYarnTotalModal(row: any, rowIndex: number) {
  useYarnTotalModalRef.value.state.grayIndex = row.grayIndex
  useYarnTotalModalRef.value.state.isEdit = false
  useYarnTotalModalRef.value.state.grayId = row.grayId
  useYarnTotalModalRef.value.state.showModal = true
  useYarnTotalModalRef.value.state.data = row
  useYarnTotalModalRef.value.state.rowIndex = rowIndex
  useYarnTotalModalRef.value.state.itemIndex = row.itemIndex
  useYarnTotalModalRef.value.state.tableData = processDataOut(row?.use_yarn_item_data)
}

function FooterMethodOnce({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'use_yarn_quantity') as any)} kg`

      if (['actually_use_yarn_quantity'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'actually_use_yarn_quantity') as any)} kg`

      return null
    }),
  ]
}

const yarnRatio_columnList = ref([
  {
    field: 'grey_fabric_full_name',
    title: '坯布名称',
    minWidth: 150,
  },
  {
    field: 'produce_notice_order_no',
    title: '生产通知单',
    minWidth: 120,
  },
  {
    field: 'roll',
    title: '收货匹数',
    minWidth: 120,
  },
  {
    field: 'raw_material_code',
    title: '原料名称',
    minWidth: 150,
    soltName: 'raw_material_code',
  },
  // {
  //   field: 'raw_material_name',
  //   title: '原料名称',
  //   minWidth: 150,
  // },
  // {
  //   field: 'raw_material_brand',
  //   title: '原料品牌',
  //   minWidth: 150,
  // },
  // {
  //   field: 'raw_material_batch_number',
  //   title: '原料批号',
  //   minWidth: 150,
  // },
  {
    field: 'color_scheme',
    title: '原料颜色',
    minWidth: 150,
  },
  // {
  //   field: 'unit_name',
  //   title: '单位',
  //   minWidth: 150,
  // },
  {
    field: 'yarn_ratio',
    title: '用纱比例',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'yarn_loss',
    title: '用纱损耗',
    minWidth: 150,
    isPrice: true,
  },
  {
    field: 'use_yarn_quantity',
    title: '用纱量',
    minWidth: 150,
    soltName: 'use_yarn_quantity',
  },
  {
    field: 'actually_use_yarn_quantity',
    title: '实际用纱量',
    minWidth: 150,
    soltName: 'actually_use_yarn_quantity',
  },
])
</script>

<template>
  <div class="list-page">
    <StatusColumn
      :close-loading="closeLoading"
      :cancel-loading="cancelLoading"
      :reject-loading="rejectLoading"
      :audit-loading="auditLoading"
      :eliminate-loading="eliminateLoading"
      :order_no="data.order_no"
      :order_id="data.id"
      :status="data.audit_status"
      :status_name="data.audit_status_name"
      permission_wait_key="GreyClothProduction_wait"
      permission_reject_key="GreyClothProduction_reject"
      permission_pass_key="GreyClothProduction_pass"
      permission_cancel_key="GreyClothProduction_cancel"
      permission_edit_key="GreyClothProduction_edit"
      edit_router_name="GreyClothProductionEdit"
      @eliminate="updateStatus"
      @reject="updateStatus"
      @cancel="updateStatus"
      @audit="updateStatus"
    />
    <FildCard class="mt-[5px]" title="基础信息" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            {{ data?.sale_system_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="接收单位:">
          <template #content>
            {{ data?.receive_unit_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="货源单位:">
          <template #content>
            {{ data?.supplier_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货日期:">
          <template #content>
            {{ formatDate(data?.receive_time) }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:">
          <template #content>
            {{ data?.remark }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂用坯单号:">
          <template #content>
            {{ data?.dye_unit_use_order_no }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:">
          <template #content>
            {{ data?.voucher_number }}
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="坯布信息" class="table-card-full">
      <Table :config="columnList_fabic_config" :table-list="data?.item_data" :column-list="columnList_fabic">
        <template #grey_fabric_code="{ row }">
          {{ formatHashTag(row.grey_fabric_code, row.grey_fabric_name) }}
        </template>
        <template #produce_notice_order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handleClickLink(row)">
            {{ row.produce_notice_order_no }}
          </el-link>
        </template>
        <template #total_weight="{ row }">
          {{ formatWeightDiv(row.total_weight) }} {{ row.unit_name }}
        </template>
        <template #avg_weight="{ row }">
          {{ formatWeightDiv(row.avg_weight) }} {{ row.unit_name }}
        </template>
        <template #xima="{ row }">
          <el-button type="primary" text link @click="handSeeXima(row)">
            查看
          </el-button>
        </template>
        <template #grey_fabric_width="{ row }">
          {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
        </template>
        <template #grey_fabric_gram_weight="{ row }">
          {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
        </template>
      </Table>
    </FildCard>
    <FildCard title="用纱情况" class="table-card-bottom">
      <Table :config="tableConfig_other" :table-list="state.yarnRatioList" :column-list="yarnRatio_columnList">
        <template #raw_material_code="{ row }">
          {{ formatHashTag(row.raw_material_code, row.raw_material_name) }}
        </template>
        <template #use_yarn_quantity="{ row }">
          {{ formatWeightDiv(row.use_yarn_quantity) }} {{ row.unit_name }}
        </template>
        <template #actually_use_yarn_quantity="{ row }">
          {{ formatWeightDiv(sumTotal(row?.use_yarn_item_data, 'use_yarn_quantity')) }} {{ row.unit_name }}
        </template>
        <template #operate="{ row, rowIndex }">
          <el-link type="primary" :underline="false" @click="openYarnTotalModal(row, rowIndex)">
            查看
          </el-link>
        </template>
      </Table>
    </FildCard>
  </div>
  <UseYarnTotalModal
    ref="useYarnTotalModalRef"
  />
  <!-- <FildCard title="用纱信息汇总" class="mt-[5px]">
    <Table :config="tableConfig_other_two" :table-list="state.yarnRatioList_total" :column-list="yarnRatio_total" />
  </FildCard> -->
  <AddXimaDialog ref="AddXimaDialogRef" />
</template>

<style lang="scss" scoped>
.oreder_code {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
