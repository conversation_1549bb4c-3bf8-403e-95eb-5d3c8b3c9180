import { useRequest } from '@/use/useRequest'

// 获取列表
export const greyFabricList = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/getList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 作废
export const updateAuditStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/updateAuditStatusCancel',
    method: 'put',
  })
}
// 审核
export const updateAuditStatusPass = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/updateAuditStatusPass',
    method: 'put',
  })
}
// 驳回
export const updateAuditStatusReject = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/updateAuditStatusReject',
    method: 'put',
  })
}
// 消审
export const updateAuditStatusWait = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/updateAuditStatusWait',
    method: 'put',
  })
}

// 获取详情
export const getGreyFabric = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/get',
    method: 'get',
  })
}

// 编辑
export const greyFabricUpdate = () => {
  return useRequest({
    url: '/admin/v1/should_collect_order/greyFabric/update',
    method: 'put',
  })
}
