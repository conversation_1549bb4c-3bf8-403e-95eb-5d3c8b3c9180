import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'

// 获取列表页数据
export const getGfmAllocateOrderList = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/getGfmAllocateOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 导出数据
export const getGfmAllocateOrderListExport = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/getGfmAllocateOrderList',
    method: 'get',
    nameFile,
  })
}

// 详情
export const getGfmAllocateOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/getGfmAllocateOrder',
    method: 'get',
  })
}

// 新增数据
export const addGfmAllocateOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/addGfmAllocateOrder',
    method: 'post',
  })
}

// 编辑
export const updateGfmAllocateOrder = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/updateGfmAllocateOrder',
    method: 'put',
  })
}

// 审核
export const updateGfmAllocateOrderStatusPass = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/updateGfmAllocateOrderStatusPass',
    method: 'put',
  })
}

// 消审
export const updateGfmAllocateOrderStatusWait = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/updateGfmAllocateOrderStatusWait',
    method: 'put',
  })
}

// 作废
export const updateGfmAllocateOrderStatusCancel = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/updateGfmAllocateOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const updateGfmAllocateOrderStatusReject = () => {
  return useRequest({
    url: '/admin/v1/grey_fabric_manage/gfmAllocateOrder/updateGfmAllocateOrderStatusReject',
    method: 'put',
  })
}
