<script lang="ts" setup name="SalesArea">
import { Delete, Plus } from '@element-plus/icons-vue'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import AddDialog from './components/AddDialog.vue'
import FildCard from '@/components/FildCard.vue'
// import SelectOptions from '@/components/SelectOptions/index.vue'
// import Table from '@/components/Table.vue'
// import { useRouter } from 'vue-router'
import {
  Business_unitsale_areaPost,
  Business_unitsale_areaPut,
  Business_unitsale_areadelete,
  Business_unitsale_arealist,
} from '@/api/saleArea'
import { formatTime } from '@/common/format'
import { debounce, deleteToastWithRiskWarning, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const state = reactive<any>({
  tableData: [],
  filterData: {
    code: '',
    name: '',
    show_children: true,
  },
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = Business_unitsale_arealist()

// 获取数据
const getData = debounce(async () => {
  await ApiCustomerList(getFilterData(state.filterData))
  if (data.value?.list)
    state.tableData = data.value?.list
}, 400)

onMounted(() => {
  getData()
})
watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

// 新建销售区域
const AddDialogRef = ref()

function handleAdd(row: any) {
  AddDialogRef.value.state.modalName = '新建销售区域'
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.form.saleAreaCode = ''
  AddDialogRef.value.state.form.id = 0
  AddDialogRef.value.state.form.parentId = row?.id || 0
  AddDialogRef.value.state.form.saleAreaName = ''
}

const {
  fetchData: postFetch,
  success: postSuccess,
  msg: postMsg,
} = Business_unitsale_areaPost()

const {
  fetchData: putFetch,
  success: putSuccess,
  msg: putMsg,
} = Business_unitsale_areaPut()

async function handleSure(form: any) {
  const query = {
    code: form?.saleAreaCode,
    name: form?.saleAreaName,
    parent_id: form?.parentId,
    id: form?.id,
  }

  if (form.id === 0)
    delete query.id
  else
    delete query.parent_id

  form.id === 0 ? await postFetch(query) : await putFetch(query)
  if (form.id === 0 ? postSuccess.value : putSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    getData()
  }
  else {
    ElMessage.error(form.id === 0 ? postMsg.value : putMsg.value)
  }
}

// 编辑销售区域
function handEdit(row: any) {
  AddDialogRef.value.state.modalName = '编辑销售区域'
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.form.saleAreaCode = row.code
  AddDialogRef.value.state.form.id = row.id
  AddDialogRef.value.state.form.parentId = 0
  AddDialogRef.value.state.form.saleAreaName = row.name
}

// 删除销售区域
const {
  fetchData: statusFetch,
  msg: StatusMsg,
  success: StatusSuccess,
} = Business_unitsale_areadelete()

async function handDelete(row: any) {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    await statusFetch({ id: row.id.toString() })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      AddDialogRef.value.state.showModal = false
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

function handReset() {
  state.filterData = resetData(state.filterData)
}

onActivated(() => {
  getData()
})
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="编号:">
          <template #content>
            <el-input v-model="state.filterData.code" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="区域名称:">
          <template #content>
            <el-input v-model="state.filterData.name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button
          v-has="'SalesArea_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
      </template>
      <el-table
        :data="data?.list"
        row-key="id"
        border
        :header-cell-style="{
          background: '#f8f8f9',
        }"
        lazy
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="code" label="区域编号" sortable />
        <el-table-column prop="name" label="区域名称" sortable />
        <el-table-column prop="update_user_name" label="最后修改人" sortable />
        <el-table-column label="最后修改时间" sortable>
          <template #default="{ row }">
            {{ formatTime(row.update_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button v-has="'SalesArea_addChild'" type="primary" text link @click="handleAdd(scope.row)">
              新增
            </el-button>
            <el-button v-has="'SalesArea_edit'" type="primary" text link @click="handEdit(scope.row)">
              编辑
            </el-button>
            <el-button v-has="'SalesArea_del'" text type="danger" @click.stop="handDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="buttom-oper">
        <el-pagination
          :current-page="page"
          :page-sizes="[10, 50, 100, 200, 300, 400, 600, 1000]"
          :page-size="size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </FildCard>
  </div>
  <AddDialog ref="AddDialogRef" @handle-sure="handleSure" />
</template>

<style></style>
