<script setup lang="ts" name="EmbryoType">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import AddDialog from '../components/AddUnitDialog.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
// import router from '@/router'
import { addTypeIntercourseUnits, deleteTypeIntercourseUnits, getPhysicalWarehouseList, updateTypeIntercourseUnits, updateTypeIntercourseUnitsStatus } from '@/api/embryoType'
import { debounce, deleteRemark, deleteToast, deleteToastWithRiskWarning, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'

const state = reactive({
  tableData: [],
  filterData: {
    name: '',
    status: '',
  },
  multipleSelection: [],
})

const { fetchData: ApiCustomerList, data, total, loading, page, size, handleSizeChange, handleCurrentChange } = getPhysicalWarehouseList()

// 获取数据
const getData = debounce(() => {
  ApiCustomerList(getFilterData(state.filterData))
  state.multipleSelection = []
}, 400)

onMounted(() => {
  getData()
})
const tableConfig = ref({
  fieldApiKey: 'EmbryoType',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showOperate: true,
  operateWidth: '6%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

function handReset() {
  state.filterData = resetData(state.filterData)
}

const AddDialogRef = ref()

const columnList = ref([
  {
    sortable: true,
    field: 'code',
    title: '编号',
  },
  {
    sortable: true,
    field: 'name',
    title: '名称',
  },
  {
    sortable: true,
    field: 'remark',
    title: '备注',
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    isDate: true,
  },
  {
    sortable: true,
    field: 'status',
    title: '状态',
    soltName: 'status',
    width: '5%',
  },
])

function handShowSort() {
  tableConfig.value.showSort = true
}

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handleAdd() {
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '新增坯布订单类型'
  AddDialogRef.value.state.form.number = ''
  AddDialogRef.value.state.form.name = ''
  AddDialogRef.value.state.form.remark = ''
  AddDialogRef.value.state.form.id = -1
}

function handEdit(row: any) {
  AddDialogRef.value.state.showModal = true
  AddDialogRef.value.state.modalName = '编辑坯布订单类型'
  AddDialogRef.value.state.form.number = row.code
  AddDialogRef.value.state.form.name = row.name
  AddDialogRef.value.state.form.remark = row.remark
  AddDialogRef.value.state.form.id = row.id
}

const { fetchData: AddFetch, msg: AddMsg, success: AddSuccess } = addTypeIntercourseUnits()

const { fetchData: putFetch, msg: putMsg, success: putSuccess } = updateTypeIntercourseUnits()

// 新建、编辑往来单位类型
async function handleSure(form: any) {
  const query = {
    id: form.id,
    code: form.number,
    remark: form.remark,
    name: form.name,
    // status: 1,
  }
  form.id === -1 ? await AddFetch(query) : await putFetch(query)
  if (form.id === -1 ? AddSuccess.value : putSuccess.value) {
    ElMessage.success('成功')
    AddDialogRef.value.state.showModal = false
    getData()
  }
  else {
    ElMessage.error(form.id === -1 ? AddMsg.value : putMsg.value)
  }
}

// 删除数据
const { fetchData: deleteFetch, success: deleteSuccess, msg: deleteMsg } = deleteTypeIntercourseUnits()

async function handDelete(row: any) {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    const res = await deleteRemark()
    await deleteFetch({ id: row.id.toString(), delete_remark: res })
    if (deleteSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteMsg.value)
    }
  }
}

// 批量删除
async function handAllDelete() {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteRemark()
  const ids: any[] = []
  state.multipleSelection.forEach((item: any) => {
    ids.push(item.id)
  })
  await deleteFetch({ id: ids.toString(), delete_remark: res })
  if (deleteSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(deleteMsg.value)
  }
}

// 编辑状态
const { fetchData: statusFetch, msg: StatusMsg, success: StatusSuccess } = updateTypeIntercourseUnitsStatus()

async function handStatus(row: any) {
  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    await statusFetch({ id: row.id.toString(), status: row.status === 1 ? 2 : 1 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      AddDialogRef.value.state.showModal = false
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}

// 批量修改状态
async function handAll(val: number) {
  if (!state.multipleSelection.length)
    return ElMessage.warning('请至少选择一条数据！')

  const res = await deleteToast('确认修改状态嘛？')
  if (res) {
    const ids: any[] = []
    state.multipleSelection.forEach((item: any) => {
      ids.push(item.id)
    })
    await statusFetch({ id: ids.toString(), status: val === 1 ? 1 : 2 })
    if (StatusSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(StatusMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="名称:">
          <template #content>
            <el-input v-model="state.filterData.name" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <SelectComponents v-model="state.filterData.status" style="width: 200px" api="StatusListApi" label-field="name" value-field="id" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" tool-bar>
      <template #right-top>
        <el-button v-has="'EmbryoType_add'" style="margin-left: 10px" type="primary" :icon="Plus" @click="handleAdd">
          新建
        </el-button>
        <el-tooltip class="box-item" effect="dark" content="点击出现排序按钮" placement="top-start">
          <el-button style="margin-right: 10px" @click="handShowSort">
            排序
          </el-button>
        </el-tooltip>
        <el-dropdown>
          <span class="el-dropdown-link">
            <el-button>批量操作</el-button>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <div v-has="'EmbryoType_del'">
                <el-dropdown-item @click="handAllDelete">
                  批量删除
                </el-dropdown-item>
              </div>
              <div v-has="'EmbryoType_status'">
                <el-dropdown-item @click="handAll(1)">
                  批量启用
                </el-dropdown-item>
              </div>
              <div v-has="'EmbryoType_status'">
                <el-dropdown-item @click="handAll(2)">
                  批量禁用
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link v-has="'EmbryoType_edit'" type="primary" :underline="false" @click="handEdit(row)">
              编辑
            </el-link>
            <el-link v-has="'EmbryoType_del'" :underline="false" type="primary" @click.stop="handDelete(row)">
              删除
            </el-link>
            <el-link v-has="'EmbryoType_status'" type="primary" :underline="false" @click="handStatus(row)">
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
  </div>
  <AddDialog ref="AddDialogRef" @handle-sure="handleSure" />
</template>

<style lang="scss" scoped>
::v-deep(.el-descriptions__cell) {
  width: 300px !important;
}

::v-deep(.el-dropdown-link) {
  outline: 0 !important;
}

.flex_box {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
