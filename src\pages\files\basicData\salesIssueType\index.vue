<script lang="ts" setup name="SalesIssueType">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { GetSaleSystemDropdownListApi } from '@/api/marketingSystem'
import { GetSendProductTypeReverseIntMap } from '@/api/settlementType'
import { GetDropdownListWithoutDS } from '@/api/warehouseInformation'
import type { ShipmentInfosType } from '@/api/files/basicData/saleslssueType'
import { AddInfoSaleShipmentType, DeleteInfoSaleShipmentType, GetInfoSaleShipmentType, GetInfoSaleShipmentTypeList, UpdateInfoSaleShipmentType } from '@/api/files/basicData/saleslssueType'
import { deleteToastWithRiskWarning } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'

// 添加接口定义
interface RuleForm {
  id?: number
  code: string
  name: string
  out_order_type?: number
  sale_system_id: number[]
  ware_house_in_id?: number
  order_type: number[]
  auto_print: string
}

const modelType = ref<'add' | 'edit'>('add') // 弹出窗类型 新增或者编辑
const showModal = ref(false) // 弹窗状态
const ruleFormRef = ref() // 表单实例
const INITIAL_FORM = {
  id: undefined,
  code: '',
  name: '',
  out_order_type: undefined,
  sale_system_id: [],
  ware_house_in_id: undefined,
  order_type: [],
  auto_print: '',
}
const ruleForm = reactive<RuleForm>({ ...INITIAL_FORM }) // 弹出的表单数据
// 将验证规则提取为常量
const FORM_RULES = {
  code: [
    { required: true, message: '发货类型编号不能为空', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '发货类型名称不能为空', trigger: 'blur' },
  ],
  out_order_type: [
    { required: true, message: '出仓类型是必选项', trigger: 'change' },
  ],
  sale_system_id: [
    { required: true, message: '使用营销体系最少选择一个', trigger: 'change' },
  ],
  ware_house_in_id: [
    {
      required: true,
      validator: (rule: any, value: any, callback: Function) => {
        if (ruleForm.out_order_type === 2 && !value)
          callback(new Error('调至仓库是必选项'))
        else
          callback()
      },
      trigger: 'change',
    },
  ],
  order_type: [
    {
      validator: (rule: any, value: any, callback: Function) => {
        if (value.length === 0)
          callback(new Error('订单类型最少选择一个'))
        else
          callback()
      },
      trigger: 'change',
    },
  ],
}

const rules = reactive(FORM_RULES)
const tableData = ref<ShipmentInfosType[]>([]) // 表格数据
const saleSystemList = ref<any>([]) // 营销体系
const sendProductType = ref<{ id: number, name: string }[]>([]) // 出仓类型
const wareHouse = ref<any>([]) // 仓库

// 表格列配置
const filesBasicDataSalesIssueTypeColumns = ref<any>([
  { field: 'code', align: 'center', title: '发货类型编号' },
  { field: 'name', align: 'center', title: '发货类型名称' },
  { field: 'out_order_type_name', align: 'center', title: '出仓类型' },
  { field: 'sale_system_name', align: 'center', title: '使用营销体系' },
  { field: 'ware_house_in_name', align: 'center', title: '调至仓库' },
  { field: 'order_type_name', align: 'center', title: '订单类型' },
  { field: 'level_name', align: 'center', title: '自动打印' },
  { field: 'operation', align: 'center', title: '操作', soltName: 'operation' },
])

// API 请求方法
const { fetchData: getSaleSystemList } = GetSaleSystemDropdownListApi() // 获取营销体系列表 （初始化用）
const { fetchData: getSendProductType } = GetSendProductTypeReverseIntMap() // 获取出仓类型 （初始化用）
const { fetchData: getWareHouse } = GetDropdownListWithoutDS() // 获取仓库 （初始化用）
const { fetchData: getList, total, page, size, handleSizeChange, handleCurrentChange } = GetInfoSaleShipmentTypeList() // 获取列表数据 （初始化用）
const { fetchData: addPost } = AddInfoSaleShipmentType() // 新增销售发货类型
const { fetchData: deletePost } = DeleteInfoSaleShipmentType() // 删除销售发货类型
const { fetchData: getDetail } = GetInfoSaleShipmentType() // 获取销售发货类型单条的详情
const { fetchData: updatePost } = UpdateInfoSaleShipmentType() // 编辑销售发货类型

// 表格配置
const TableConfig = computed(() => ({
  fieldApiKey: 'filesBasicDataSalesIssueType',
  loading: false,
  showPagition: true,
  page: page.value,
  size: size.value,
  total: total.value,
  height: '100%',
  operateWidth: '120',
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
}))

// 获取数据
async function getData() {
  try {
    TableConfig.value.loading = true
    const result = await getList()
    if (result.success) {
      tableData.value = result.data.list || []
    }
    else {
      ElMessage.error(result.msg || '获取数据失败')
      tableData.value = []
    }
  }
  catch (error) {
    ElMessage.error('获取数据失败')
    tableData.value = []
  }
  finally {
    TableConfig.value.loading = false
  }
}

// 获取下拉数据 (营销体系)
async function getSaleSystemListData() {
  const res = await getSaleSystemList()
  if (res.success)
    saleSystemList.value = res.data.list
}
// 获取下拉数据 (出仓类型)
async function getSendProductTypeData() {
  const res = await getSendProductType()
  if (res.success)
    sendProductType.value = res.data.list
}
// 获取下拉数据 (仓库)
async function getWareHouseData() {
  const res = await getWareHouse()
  if (res.success)
    wareHouse.value = res.data.list
}

// 点击新增
function handleFunctionAdd() {
  if (ruleFormRef.value)
    ruleFormRef.value.resetFields() // 先初始化表单
  modelType.value = 'add' // 将弹窗类型设置为新增
  resetFormData() // 重置表单数据
  showModal.value = true // 显示弹窗
}

// 点击编辑
async function handEdit(row: any) {
  try {
    const result = await getDetail({ id: row.id })
    if (result.success && result.data) {
      const {
        id,
        code,
        name,
        out_order_type,
        sale_system_id,
        ware_house_in_id,
        order_type,
        auto_print,
      } = result.data

      // 确保 ruleForm 存在且有效
      if (!ruleForm) {
        ElMessage.error('表单初始化失败')
        return
      }

      // 处理数据转换
      const newFormData = {
        id,
        code,
        name,
        out_order_type,
        // 处理 sale_system_id：如果已经是数组就直接使用，否则尝试转换
        sale_system_id: Array.isArray(sale_system_id)
          ? sale_system_id
          : (typeof sale_system_id === 'string' ? sale_system_id.split(',').map(Number) : []),
        // 处理仓库 ID
        ware_house_in_id: out_order_type === 1 ? undefined : (ware_house_in_id || undefined),
        // 处理订单类型：如果已经是数组就直接使用，否则尝试转换
        order_type: Array.isArray(order_type)
          ? order_type
          : (typeof order_type === 'string' ? order_type.split(',').map(Number) : []),
        auto_print: auto_print || '',
      }

      // 更新表单数据
      Object.assign(ruleForm, newFormData)

      modelType.value = 'edit'
      showModal.value = true
    }
    else {
      ElMessage.error(result.msg || '获取详情失败')
      resetForm(ruleFormRef.value)
    }
  }
  catch (error) {
    console.error('编辑错误:', error)
    ElMessage.error('获取详情失败')
    resetForm(ruleFormRef.value)
  }
}

// 添加 watch 来处理出仓类型变化
watch(
  () => ruleForm.out_order_type,
  (newVal) => {
    // 当出仓类型为1(出货)时，清空仓库值
    if (newVal === 1)
      ruleForm.ware_house_in_id = undefined
  },
)

// 点击删除
async function handleDelete(row: any) {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    const result = await deletePost({ id: row.id.toString() }) // 删除
    if (result.success) { // 删除成功
      ElMessage.success('删除成功')
      await getData() // 重新获取数据
    }
    else {
      ElMessage.error('删除失败') // 删除失败
    }
  }
}

// 提交表单
async function submitForm(formEl: any) {
  if (!formEl)
    return

  try {
    await formEl.validate()

    // 检查订单类型是否已选择
    if (!ruleForm.order_type || ruleForm.order_type.length === 0) {
      ElMessage.warning('订单类型最少选择一个')
      return
    }

    if (ruleForm.out_order_type === 2 && !ruleForm.ware_house_in_id) {
      ElMessage.warning('出仓类型为销调的情况下需要选择调至仓库')
      return
    }

    const req = {
      ...ruleForm,
      sale_system_id: ruleForm.sale_system_id.join(','),
      // 将订单类型数组转换为逗号分隔的字符串
      order_type: ruleForm.order_type.join(','),
    }

    TableConfig.value.loading = true
    const result = modelType.value === 'add'
      ? await addPost(req)
      : await updatePost(req)

    if (result.success) {
      ElMessage.success(`${modelType.value === 'add' ? '新增' : '更新'}成功`)
      showModal.value = false
      await getData()
      resetForm(formEl)
    }
    else {
      ElMessage.error(result.msg || `${modelType.value === 'add' ? '新增' : '更新'}失败`)
    }
  }
  catch (error) {
    ElMessage.error('操作失败')
  }
  finally {
    TableConfig.value.loading = false
  }
}

// 使用函数重置表单
function resetFormData() {
  Object.assign(ruleForm, INITIAL_FORM)
}

// 修改重置表单的方法
function resetForm(formEl: any) {
  formEl?.resetFields()
  resetFormData()
  showModal.value = false
}

const wareHousePlaceholder = computed(() => {
  if (!ruleForm.out_order_type)
    return '请先选择出仓类型'
  return ruleForm.out_order_type === 1
    ? '当前出仓类型无需调至仓库'
    : '请选择调至仓库'
})

// 进入页面执行
onMounted(async () => {
  try {
    await Promise.all([
      getData(),
      getSaleSystemListData(),
      getSendProductTypeData(),
      getWareHouseData(),
    ])
  }
  catch (error) {
    ElMessage.error('初始化数据失败')
  }
})
</script>

<template>
  <FildCard class="list-page">
    <div class="mb-4">
      <el-button v-has="'SalesIssueTypeAdd'" type="primary" @click="handleFunctionAdd">
        新增
      </el-button>
    </div>
    <div class="flex flex-col h-full">
      <Table :config="TableConfig" :table-list="tableData" :column-list="filesBasicDataSalesIssueTypeColumns">
        <template #operation="{ row }">
          <el-button v-has="'SalesIssueTypeEdit'" type="primary" text="primary" link @click="handEdit(row)">
            编辑
          </el-button>
          <!--        <el-popconfirm title="确定删除?" @confirm="handleDelete(row)"> -->
          <!--          <template #reference> -->
          <!--            <el-button type="danger" text="danger" link> -->
          <!--              删除 -->
          <!--            </el-button> -->
          <!--          </template> -->
          <!--        </el-popconfirm> -->
          <el-button v-has="'SalesIssueTypeDel'" type="danger" link @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </Table>
    </div>
    <vxe-modal
      v-model="showModal"
      resize
      :title="modelType === 'add' ? '新增' : '编辑'"
      :width="700"
      :height="468"
      :mask="false"
      :lock-view="true"
    >
      <el-form ref="ruleFormRef" :model="ruleForm" label-width="auto" style="max-width: 700px" :rules="rules" hide-required-asterisk>
        <el-form-item label="发货类型编号" prop="code">
          <el-input v-model="ruleForm.code" clearable />
        </el-form-item>
        <el-form-item label="发货类型名称" prop="name">
          <el-input v-model="ruleForm.name" clearable />
        </el-form-item>
        <el-form-item label="出仓类型" prop="out_order_type">
          <el-select v-model="ruleForm.out_order_type" placeholder="请选择出仓类型" clearable>
            <el-option v-for="item in sendProductType" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用营销体系" prop="sale_system_id">
          <el-select v-model="ruleForm.sale_system_id" placeholder="请选择营销体系" multiple clearable>
            <el-option v-for="item in saleSystemList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="调至仓库" prop="ware_house_in_id">
          <SelectComponents
            v-model="ruleForm.ware_house_in_id"
            :placeholder="wareHousePlaceholder"
            :disabled="!ruleForm.out_order_type || ruleForm.out_order_type === 1"
            api="GetDropdownListWithoutDS"
            label-field="name"
            value-field="id"
            warehouse_type_id="finishProduction"
            clearable
          />
        </el-form-item>
        <el-form-item label="订单类型" prop="order_type">
          <el-checkbox-group v-model="ruleForm.order_type">
            <el-checkbox :value="1" name="大货订单">
              大货订单
            </el-checkbox>
            <el-checkbox :value="2" name="剪板发货">
              剪板发货
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="自动打印" prop="auto_print">
          <el-radio-group v-model="ruleForm.auto_print">
            <el-radio value="Sponsor">
              配布单
            </el-radio>
            <el-radio value="Venue">
              客户标签
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-row justify="end" style="width: 100%;">
            <el-button @click="resetForm(ruleFormRef)">
              取消
            </el-button>
            <el-button type="primary" @click="submitForm(ruleFormRef)">
              {{ modelType === 'add' ? '确定' : '更新' }}
            </el-button>
          </el-row>
        </el-form-item>
      </el-form>
    </vxe-modal>
  </FildCard>
</template>

<style scoped lang="scss"></style>
