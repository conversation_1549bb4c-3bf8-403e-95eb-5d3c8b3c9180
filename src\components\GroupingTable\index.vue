<script lang="ts" setup>
import { mode, number } from 'mathjs'
import { computed, ref, watch } from 'vue'
import type { VxeGridInstance, VxeGridPropTypes, VxeTableEvents } from 'vxe-table'

// 接收父组件的配置
const props = defineProps({
  columns: {
    type: Array as () => VxeGridPropTypes.Columns,
    required: true, // 必须传入列配置
  },
  data: {
    type: Array,
    required: true, // 必须传入表格数据
  },
  config: { // 表格总体配置
    type: Object,
    required: false,
  },
  pagination: { // 分页配置
    type: Object,
    required: false,
  },
})

// 当前页和每页条数的默认值
const defaultPageSizes = [10, 50, 100, 200, 300, 400, 500] // 默认每页条数选择

// const currentPage = ref(props.pagination?.currentPage || 1) // 当前页
// const currentSize = ref(props.pagination?.currentSize || 50) // 每页条数
// const total = ref(props.pagination?.total || 0) // 总条数
// const pageSizes = ref(props.pagination?.pageSizes.length ? props.pagination?.pageSizes : defaultPageSizes) // 每页条数选择

const currentPage = computed(() => {
  return props.pagination?.currentPage || 1
}) // 当前页
const currentSize = computed(() => props.pagination?.currentSize || 50) // 每页条数
const pageSizes = computed(() => props.pagination?.pageSizes || defaultPageSizes) // 每页条数选择
const total = computed(() => props.pagination?.total || 0) // 总条数
// 表格实例引用
const gridRef = ref<VxeGridInstance>()

// 单选行选中的数据
const selectRow = ref<any | null>()

// 多选行选中的数据
const selectedRows = ref<any[]>([])

// 判断是否启用单选功能
const isRadioEnabled = computed(() =>
  props.columns.some(col => col.type === 'radio'), // 检测是否有单选列
)

// 判断是否启用多选功能
const isCheckboxEnabled = computed(() =>
  props.columns.some(col => col.type === 'checkbox'), // 检测是否有多选列
)

// 分页后的数据
const tableData = computed(() => {
  const start = (currentPage.value - 1) * currentSize.value
  const end = start + currentSize.value
  return props.data.slice(start, end)
})

// 监听数据变化以更新总条数
// watch(() => props.data, (newValue: string | any[]) => {
//   total.value = newValue.length
// }, { immediate: true })

// 表格配置项
const gridOptions = computed<any>(() => ({
  loading: false, // 是否显示加载中状态
  border: true, // 是否显示边框
  stripe: true, // 是否显示斑马纹
  rowConfig: {
    useKey: true, // 每行必须有唯一主键
    keyField: 'id', // 唯一主键字段
    isHover: true, // 鼠标经过是否高亮当前行
  },
  radioConfig: isRadioEnabled.value ? { highlight: true } : null, // 动态启用单选
  checkboxConfig: isCheckboxEnabled.value ? { highlight: true } : null, // 动态启用多选
  columnConfig: {
    useKey: true, // 每列必须有唯一主键
    resizable: true, // 是否可以拖拽调整列宽
  },
  // toolbarConfig: { // 动态启用工具栏
  //   custom: true,
  // },
  columns: props.columns || [], // 列配置
  data: tableData.value, // 表格数据
  // 自定义配置
  ...props.config,
}))

/**
 * 单击单元格事件处理
 * @param val - 单击事件的参数对象
 */
const cellClickEvent: VxeTableEvents.CellClick = (val: any) => {
  const { seq } = val // 获取当前行的序号
  const $grid = gridRef.value
  if ($grid) {
    // 如果启用了单选功能，选中当前行
    if (isRadioEnabled.value)
      $grid.setRadioRow(gridOptions.value?.data?.[seq - 1])

    // 如果启用了多选功能，切换当前行的选中状态
    if (isCheckboxEnabled.value) {
      const clickedRow = gridOptions.value?.data?.[seq - 1]
      if (clickedRow) {
        const isSelected = $grid.isCheckedByCheckboxRow(clickedRow) // 检查当前行是否选中
        $grid.setCheckboxRow(clickedRow, !isSelected) // 切换选中状态
      }
    }
  }
}

/** 单选功能相关方法 */
/**
 * 获取当前选中的单选行
 * @returns 选中的行数据或 null
 */
function getRadioEventInfo() {
  const $grid = gridRef.value
  return $grid ? $grid.getRadioRecord() || null : null
}

/**
 * 清除单选行的选中状态
 */
function clearRadioRowEvent() {
  const $grid = gridRef.value
  if ($grid) {
    selectRow.value = null
    $grid.clearRadioRow()
  }
}

/**
 * 手动设置单选选中的行
 * @param row - 要选中的行数据
 */
function setSelectRow(row: any) {
  const $grid = gridRef.value
  if ($grid) {
    $grid.setRadioRow(row)
    selectRow.value = row
  }
}

/** 多选功能相关方法 */
/**
 * 获取所有选中的多选行
 * @returns 所有选中的行数据
 */
function getSelectedRows() {
  const $grid = gridRef.value
  return $grid ? $grid.getCheckboxRecords() || [] : []
}

/**
 * 清除所有多选行的选中状态
 */
function clearSelectedRows() {
  const $grid = gridRef.value
  if ($grid) {
    selectedRows.value = []
    $grid.clearCheckboxRow()
  }
}

/**
 * 手动设置多选选中的行
 * @param rows - 要选中的行数据数组
 */
function setSelectedRows(rows: any[]) {
  const $grid = gridRef.value
  if ($grid) {
    $grid.setCheckboxRow(rows, true)
    selectedRows.value = rows
  }
}
/**
 * 分页尺寸变化时的事件
 */
function handleSizeChange(val: number) {
  // console.log('每页条数变更触发：', val)
  // currentSize.value = val
  // 如果父组件提供了分页配置，调用父组件传递的回调
  props.pagination?.onSizeChange?.(val)
}

/**
 * 当前页变化时的事件
 */
function handleCurrentChange(val: number) {
  // console.log('页码更新的时候触发：', val)
  // currentPage.value = val
  // 如果父组件提供了分页配置，调用父组件传递的回调
  props.pagination?.onCurrentChange?.(val)
}

// 暴露方法给父组件使用
defineExpose({
  getRadioEventInfo, // 获取单选选中的行
  clearRadioRowEvent, // 清除单选行
  setSelectRow, // 手动设置单选行
  getSelectedRows, // 获取多选选中的行
  clearSelectedRows, // 清除多选行
  setSelectedRows, // 手动设置多选行

  currentPage, // 当前页
  currentSize, // 每页条数
  total, // 总条数
  pageSizes, // 每页条数选择
  handleSizeChange, // 每页条数变更时的处理方法
  handleCurrentChange, // 页码变更时的处理方法
})
</script>

<template>
  <div>
    <!-- 渲染表格 -->
    <vxe-grid
      ref="gridRef"
      v-bind="gridOptions"
      @cell-click="cellClickEvent"
    />
    <div style="display: flex;justify-content: end;margin: 15px 0;">
      <el-pagination
        :current-page="currentPage"
        :page-size="currentSize"
        size="large"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :pager-count="5"
        :page-sizes="defaultPageSizes"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
