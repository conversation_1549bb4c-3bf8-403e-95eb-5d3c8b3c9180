import { ref } from 'vue'
import type { AddressTypes } from '@/api/addressCard'

export function useAddressState() {
  const addressList = ref<AddressTypes[]>([])
  const currentAddress = ref<AddressTypes>({
    id: 0,
    address: '',
    biz_uint_id: 0,
    contact_name: '',
    is_default: false,
    location: [],
    logistics_area: '',
    logistics_company: '',
    name: '',
    phone: '',
    print_tag: '',
  })
  const editData = ref<Partial<AddressTypes>>({})

  return {
    addressList,
    currentAddress,
    editData,
  }
}
