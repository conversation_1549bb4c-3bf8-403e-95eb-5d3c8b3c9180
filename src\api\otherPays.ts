import { useRequest } from '@/use/useRequest'

// 获取列表
export const getActuallyPayOrderListForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/getActuallyPayOrderListForOther',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 新增
export const addActuallyPayOrderForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/addActuallyPayOrderForOther',
    method: 'post',
  })
}

// 编辑
export const updateActuallyPayOrderForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/updateActuallyPayOrderForOther',
    method: 'put',
  })
}

// 详情
export const getActuallyPayOrderForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/getActuallyPayOrderForOther',
    method: 'get',
  })
}

// 审核
export const updateActuallyPayOrderStatusPassForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/updateActuallyPayOrderStatusPassForOther',
    method: 'put',
  })
}

// 消审
export const updateActuallyPayOrderStatusWaitForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/updateActuallyPayOrderStatusWaitForOther',
    method: 'put',
  })
}

// 作废
export const updateActuallyPayOrderStatusCancelForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/updateActuallyPayOrderStatusCancelForOther',
    method: 'put',
  })
}

// 驳回
export const updateActuallyPayOrderStatusRejectForOther = () => {
  return useRequest({
    url: '/admin/v1/payable/otherPayOrder/updateActuallyPayOrderStatusRejectForOther',
    method: 'put',
  })
}
