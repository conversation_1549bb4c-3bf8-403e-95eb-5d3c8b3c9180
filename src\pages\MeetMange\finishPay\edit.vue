<script lang="ts" setup name="FinishPayEdit">
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import Big from 'big.js'
import FildCard from '@/components/FildCard.vue'
import { product_purdetail, product_purput } from '@/api/finishPay'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import {
  formatDate,
  formatHashTag,
  formatLengthDiv,
  formatPriceDiv,
  formatPriceMul,
  formatUnitPriceDiv,
  formatUnitPriceMul,
  formatWeightDiv,
  formatWeightMul,
  sumNum,
} from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { deepClone } from '@/common/util'
import useRouterList from '@/use/useRouterList'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import SelectSaleMode from '@/components/SelectSaleMode/index.vue'

const routerList = useRouterList()
const rourte = useRoute()

const state = reactive<any>({
  form: {
    src_order_type_name: '',
    src_order_type: '',
    sale_system_name: '',
    voucher_num: '',
    remark: '',
    src_order_no: '',
    pay_date: '',
    sale_system_id: '',
    src_order_id: '',
    supplier_id: '',
    handler_id: '',
    sale_mode: '',
  },
  tableList: [],
  formRules: {
    pay_date: [{ required: true, message: '请选择应付日期', trigger: 'blur' }],
    sale_mode: [{ required: true, message: '请选择订单类型', trigger: 'change' }],
  },
})

const { fetchData, data } = product_purdetail()

onMounted(async () => {
  await fetchData({ id: rourte.query.id })
  if (data.value) {
    //
    state.form.src_order_type_name = data.value?.src_order_type_name
    state.form.src_order_type = data.value?.src_order_type
    state.form.sale_system_name = data.value?.sale_system_name
    state.form.voucher_num = data.value?.voucher_num
    state.form.remark = data.value?.remark
    state.form.src_order_no = data.value?.src_order_no
    // if (data.value?.pay_date === '')
    state.form.pay_date = formatDate(data.value?.pay_date)
    // else
    //   state.form.pay_date = getCurrentDate()

    state.form.sale_system_id = data.value?.sale_system_id || 0
    state.form.supplier_id = data.value?.supplier_id || 0
    state.form.handler_id = data.value?.handler_id || 0
    state.form.src_order_id = data.value?.src_order_id || 0
    state.form.sale_mode = data.value?.sale_mode || ''

    const dataArr = data.value.items

    dataArr.map((item: any) => {
      item.length_unit_price = formatUnitPriceDiv(item?.length_unit_price)
      item.weight_unit_price = formatUnitPriceDiv(item?.weight_unit_price)
      item.other_price = formatPriceDiv(item?.other_price)
      item.price = formatPriceDiv(item?.price)
      item.should_pay_weight = formatWeightDiv(item?.should_pay_weight)
      item.receive_weight = formatWeightDiv(item?.receive_weight)
      item.pt_weight_and_weight_error = formatWeightDiv(item?.pt_weight_and_weight_error)
      // item = conductUnitPrice(item, true)
      return item
    })

    state.tableList = dataArr // 表格数据
  }
})

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'roll') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)}`

      if (['length'].includes(column.property))
        return `${formatLengthDiv(sumNum(data, 'length') as any)}`

      if (['other_price'].includes(column.property))
        return `￥${sumNum(data, 'other_price')}`

      return null
    }),
  ]
}

const tableRef = ref()

watch(
  () => state.tableList,
  () => {
    if (state.tableList.length) {
      state.tableList.map((item: any) => {
        let newPrice = 0

        // 主单位结算金额 = 总数量 * 数量单价 + 其他应付
        const newWeightPrice = new Big(formatWeightMul(item.should_pay_weight))
          .times(Number(item.weight_unit_price))
          .toNumber()

        // 辅助单位结算金额 = 辅助数量 * 辅助单价 + 其他应付
        // const newLengthPrice = new Big(item.length)
        //   .times(Number(item.length_unit_price))
        //   .toNumber()

        // 结算金额 = 主单位结算金额 + 辅助单位结算金额 + 其他应付
        newPrice = new Big(newWeightPrice)
          // .plus(newLengthPrice)
          .plus(formatUnitPriceMul(item.other_price))
          .toNumber()

        item.price = formatUnitPriceDiv(newPrice)

        return item
      })
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

const tableConfig = ref({
  showSlotNums: true,
  fieldApiKey: fieldApiKeyList.MeetMangeFinishPayEdit,
  height: 600,
  rowColor: {
    B: 'blue',
    D: 'blue',
  },
  footerMethod: (val: any) => FooterMethod(val),
})

const ruleFormRef = ref()
function handleBlur(row: any) {
  row.should_pay_weight = currency(row.receive_weight).subtract(row.pt_weight_and_weight_error).value
  row.total_price = currency(row.unit_price).multiply(row.should_pay_weight).add(row.other_price).value
}
const {
  fetchData: addPost,
  data: addData,
  success: addSuccess,
  msg: addMsg,
} = product_purput()

// 提交数据
async function handleSure() {
  const list = deepClone(state.tableList)
  let msg = ''
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    if (!item.measurement_unit_id)
      msg = `成品编号为${item.product_code}的数据,结算单位不能为空`

    else if (item.weight_unit_price === '')
      msg = `成品编号为${item.product_code}的数据,数量单价不能为空`

    // else if (item.length_unit_price === '' && !isMainUnit(item))
    //   msg = `成品编号为${item.product_code}的数据,辅助数量单价不能为空`

    if (msg)
      return ElMessage.error(msg)

    list[i].length_unit_price = formatUnitPriceMul(list[i].length_unit_price)
    list[i].receive_weight = formatWeightMul(list[i].receive_weight)
    list[i].pt_weight_and_weight_error = formatWeightMul(list[i].pt_weight_and_weight_error)
    list[i].should_pay_weight = formatWeightMul(list[i].should_pay_weight)
    list[i].other_price = formatPriceMul(list[i].other_price)
    list[i].weight_unit_price = formatUnitPriceMul(list[i].weight_unit_price)
  }
  state.form.pay_date = formatDate(state.form.pay_date)
  const query = {
    ...state.form,
    items: list,
    id: Number(rourte.query.id),
  }
  ruleFormRef.value.validate(async (valid: any) => {
    if (valid) {
      await addPost(query)
      if (addSuccess.value) {
        ElMessage.success('成功')
        routerList.push({
          name: 'FinishPayDetail',
          query: { id: addData.value.id },
        })
      }
      else {
        ElMessage.error(addMsg.value)
      }
    }
  })
}

const columnList = ref([
  {
    field: 'A',
    title: '采购信息',
    align: 'center',
    childrenList: [
      {
        field: 'product_code',
        title: '成品名称',
        minWidth: 100,
        soltName: 'product_code',
      },
      {
        field: 'color',
        title: '色号颜色',
        soltName: 'color',
        minWidth: 100,
      },
      {
        field: 'customer_name',
        title: '所属客户',
        minWidth: 100,
      },
      {
        field: 'dye_factory_dyelot_number',
        title: '缸号',
        minWidth: 100,
      },
      {
        field: 'product_width',
        soltName: 'product_width',
        title: '成品幅宽',
        minWidth: 100,
      },
      {
        field: 'product_gram_weight',
        soltName: 'product_gram_weight',
        title: '成品克重',
        minWidth: 100,
      },
      {
        field: 'finish_product_level_name',
        title: '成品等级',
        minWidth: 100,
      },
      {
        field: 'product_remark',
        title: '成品备注',
        minWidth: 100,
      },
    ],
  },
  {
    field: 'B',
    title: '采购信息',
    align: 'center',
    childrenList: [
      {
        field: 'unit_name',
        title: '结算单位',
        minWidth: 100,
        soltName: 'unit_name',
      },
      {
        field: 'receive_weight',
        soltName: 'receive_weight',
        title: '本次采购数量',
        minWidth: 100,
      },
      {
        field: 'pt_weight_and_weight_error',
        title: '供方纸筒空差',
        soltName: 'pt_weight_and_weight_error',
        minWidth: 100,
      },
      {
        field: 'should_pay_weight',
        soltName: 'should_pay_weight',
        title: '应付数量',
        minWidth: 100,
      },
      {
        field: 'weight_unit_price',
        title: '应付单价',
        minWidth: 100,
        soltName: 'weight_unit_price',
      },
      {
        field: 'other_price',
        title: '其他应付',
        minWidth: 100,
        soltName: 'other_price',
      },
      {
        field: 'price',
        title: '应付金额',
        minWidth: 100,
      },
      {
        field: 'remark',
        title: '备注',
        minWidth: 100,
        soltName: 'remark',
      },
    ],
  },
  {
    field: 'C',
    title: '采购信息',
    align: 'center',
    childrenList: [
      {
        field: 'weight',
        isWeight: true,
        minWidth: 100,
        title: '主数量（不含空差）',
      },
      {
        field: 'weight_error',
        isWeight: true,
        minWidth: 100,
        title: '进仓空差',
      },
      {
        field: 'paper_tube_weight',
        isWeight: true,
        minWidth: 100,
        title: '进仓纸筒',
      },
      {
        field: 'length',
        title: '辅助数量',
        minWidth: 100,
        isLength: true,
      },
    ],
  },
])
/**
 * 根据结算单位是否为主单位显示单价
 * @param item 成品数据
 * @param isInit 是否需要初始化单位
 */
function conductUnitPrice(event, item: any) {
  // 初始化结算单位
  item.measurement_unit_id = event.id
  item.measurement_unit_name = event.name
  return item
}
</script>

<template>
  <!-- 基础信息 -->
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary">
        提交
      </el-button>
    </template>
    <el-form ref="ruleFormRef" :model="state.form" :rules="state.formRules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="源单类型:">
          <template #content>
            {{ data?.src_order_type_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="源单单号:">
          <template #content>
            {{ data?.src_order_no }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            {{ data?.sale_system_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证单号:">
          <template #content>
            <el-input v-model="state.form.voucher_num" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供方名称:">
          <template #content>
            {{ data?.supplier_name }}
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="应付日期:" required>
          <template #content>
            <el-form-item prop="pay_date">
              <el-date-picker
                v-model="state.form.pay_date"
                type="date"
                placeholder="应付日期"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="经手人:">
          <template #content>
            <SelectComponents
              v-model="state.form.handler_id"
              api="Adminemployeelist"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单类型:">
          <template #content>
            <SelectSaleMode v-model="state.form.sale_mode" :show-customer-book="false" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据备注:" copies="2">
          <template #content>
            <el-input v-model="state.form.remark" clearable />
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <!-- 成品信息 -->
  <FildCard title="成品信息" :tool-bar="true" class="mt-[5px]">
    <Table
      ref="tableRef"
      :config="tableConfig"
      :table-list="state.tableList"
      :column-list="columnList"
    >
      <template #product_code="{ row }">
        {{ formatHashTag(row.product_code, row.product_name) }}
      </template>
      <template #color="{ row }">
        {{ formatHashTag(row.color_code, row.color) }}
      </template>
      <!--       单位 结算单位（辅助单位 -->
      <template #unit_name="{ row }">
        <SelectComponents v-model="row.measurement_unit_id" size="small" style="width: 100%;" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" @select="conductUnitPrice($event, row)" />
      </template>
      <!-- 单价 -->
      <template #weight_unit_price="{ row }">
        <vxe-input v-model="row.weight_unit_price" type="float" :min="0" clearable />
      </template>
      <!-- 本次采购数量 -->
      <template #receive_weight="{ row }">
        <vxe-input v-model="row.receive_weight" type="float" :min="0" clearable @blur="handleBlur(row)" />
      </template>
      <template #should_pay_weight="{ row }">
        {{ row.should_pay_weight }}
      </template>
      <!-- 供方纸筒空差 -->
      <template #pt_weight_and_weight_error="{ row }">
        <vxe-input v-model="row.pt_weight_and_weight_error" type="float" :min="0" clearable @blur="handleBlur(row)" />
      </template>
      <template #other_price="{ row }">
        <vxe-input
          v-model="row.other_price"
          :min="data.src_order_type !== 11 ? 0 : ''"
          type="float"
        />
      </template>
      <template #remark="{ row }">
        <vxe-input v-model="row.remark" />
      </template>
      <template #product_width="{ row }">
        {{ row.product_width }} {{ row.finish_product_width_unit_name }}
      </template>
      <template #product_gram_weight="{ row }">
        {{ row.product_gram_weight }}
        {{ row.finish_product_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
</template>

<style scoped>
:deep(.blue){
  background: #afd9ff
}
</style>
