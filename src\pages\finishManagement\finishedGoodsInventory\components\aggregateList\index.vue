<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import DetailModel from '../detailModel/index.vue'
import OccupyStockDialog from '../../../../saleManagement/components/OccupyStockDialog.vue'
import { GetStockProductList } from '@/api/finishedGoodsInventory'
import { formatHashTag, formatPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { debounce, getFilterData } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import Table from '@/components/Table.vue'
import BottonExcel from '@/components/BottonExcel/index.vue'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'

export interface Props {
  type: 'aggregate' | 'cylinderNumber' | 'fineCode'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'aggregate',
})

const customerRef1 = ref()
const customerRef2 = ref()
const mainOptionsTablesRef = ref()
const filterData = ref({
  warehouse_id: '',
  customer_id: '',
  product_id: '',
  product_color_id: '',
  product_code: '',
  product_name: '',
  product_level_id: '',
  stock_show_type: 2,
})

const { fetchData: fetchDataList, data: mainDataList, total: totalList, page: pageList, size: sizeList, loading: loadingList, handleSizeChange, handleCurrentChange }: any = GetStockProductList()
// 获取列表数据
async function getData() {
  await fetchDataList(getFilterData(filterData.value))
}

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  finish_product_code: '',
  finish_product_name: '',
  color_name: '',
  color_code: '',
})

function changePro(data: any) {
  componentRemoteSearch.finish_product_code = data.finish_product_code
  componentRemoteSearch.finish_product_name = data.finish_product_name

  filterData.value.product_color_id = null
}

async function handleExport() {
  mainOptionsTablesRef.value.exportSelectEvent()
}

watch(
  () => props.type,
  (val) => {
    if (val === 'aggregate')
      getData()
  },
  { immediate: true },
)

const mainOptions = reactive<any>({
  columnList: [
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'customer_name',
      title: '所属客户',
      minWidth: 100,
    },
    {
      sortable: true,
      title: '成品名称',
      field: 'product_name',
      minWidth: 120,
      soltName: 'product_code',
    },
    {
      sortable: true,
      field: 'product_color_code',
      title: '色号颜色',
      minWidth: 100,
      soltName: 'product_color_code',
    },
    // {
    //   sortable: true,
    //   field: 'product_color_name',
    //   title: '颜色',
    //   minWidth: 100,
    // },
    {
      sortable: true,
      field: 'product_level_name',
      title: '成品等级',
      minWidth: 100,
    },
    // 【ID1001835】【erp】erp优化点三  3.成品汇总库存、成品缸号库存、成品细码库存增加 成分 纱支 密度 组织 ====》可导出excel
    {
      sortable: true,
      field: 'finish_product_ingredient',
      title: '成分',
      width: 100,
    },
    {
      sortable: true,
      field: 'yarn_count',
      title: '纱支',
      width: 100,
    },
    {
      sortable: true,
      field: 'density',
      title: '密度',
      width: 100,
    },
    {
      sortable: true,
      field: 'weaving_organization_name',
      title: '组织',
      width: 100,
    },
    {
      sortable: true,
      field: 'product_remark',
      title: '成品备注', // 100
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'stock_roll',
      title: '匹数',
      width: 70,
      isPrice: true,
    },
    {
      sortable: true,
      field: 'book_roll',
      title: '预约匹数',
      width: 80,
      isPrice: true,
    },
    {
      sortable: true,
      field: 'available_roll',
      title: '可用匹数',
      width: 80,
      soltName: 'available_roll',
    },
    {
      sortable: true,
      field: 'available_weight',
      title: '可用数量',
      width: 80,
      isWeight: true,
    },
    {
      sortable: true,
      field: 'weight',
      title: '基本单位数量',
      width: 100,
      isWeight: true,
    },
    {
      sortable: true,
      field: 'measurement_unit_name',
      title: '单位',
      width: 80,
    },
    {
      sortable: true,
      field: 'length',
      title: '辅助数量',
      width: 80,
      isLength: true,
    },
    {
      sortable: true,
      field: 'remark',
      title: '库存备注',
      width: 100,
    },
  ],
})

// 表格选中事件
function handAllSelect({ records }: any) {
  mainOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  mainOptions.multipleSelection = records
}

watch(
  () => filterData.value,
  debounce(() => getData(), 400),
  {
    deep: true,
  },
)
const detailRef = ref()
const defaultId = ref(0)
const showModel = ref(false)
function handDetail(row: any) {
  defaultId.value = row.id
  showModel.value = true
}

function footerMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([1].includes(_columnIndex))
        return '总计'

      if (['weight', 'available_weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property) as unknown as number)}`

      if (['stock_roll', 'book_roll', 'available_roll'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, column.property) as unknown as number)}`

      return null
    }),
  ]
}

const tableConfig = ref({
  fieldApiKey: 'FinishedGoodsInventory_A',
  footerMethod,
  showSlotNums: true,
  loading: loadingList,
  showPagition: true,
  page: pageList,
  size: sizeList,
  total: totalList,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '7%',
  height: '100%',
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function changeColor(myref: string) {
  if (myref === 'customerRef2')
    customerRef1.value.inputLabel = customerRef2.value.item?.product_color_code
  else
    customerRef2.value.inputLabel = customerRef1.value.item?.product_color_name
}
// 查看占用库存
const occupyStockRef = ref()
function handleShowOccupyStock(row: any) {
  occupyStockRef.value.state.baseData = row
  occupyStockRef.value.state.showModal = true
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="!p-0">
      <el-descriptions title="" :column="4" border size="small" class="">
        <el-descriptions-item label="仓库名称">
          <SelectComponents v-model="filterData.warehouse_id" api="GetPhysicalWarehouseDropdownList" warehouse_type_id="finishProduction" label-field="name" value-field="id" clearable />
        </el-descriptions-item>
        <el-descriptions-item label="所属客户">
          <SelectDialog
            v-model="filterData.customer_id"
            :query="{ name: componentRemoteSearch.customer_name }"
            api="GetCustomerEnumList"
            :column-list="[
              {
                title: '客户编号',
                minWidth: 100,
                required: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'code',
                    isEdit: true,
                    title: '客户编号',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '客户名称',
                minWidth: 100,
                colGroupHeader: true,
                required: true,
                childrenList: [
                  {
                    isEdit: true,
                    field: 'name',
                    title: '客户名称',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '电话',
                colGroupHeader: true,
                minWidth: 100,
                childrenList: [
                  {
                    field: 'phone',
                    isEdit: true,
                    title: '电话',
                    minWidth: 100,
                  },
                ],
              },
              {
                title: '销售员',
                minWidth: 100,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'seller_name',
                    title: '销售员',
                    soltName: 'seller_name',
                    isEdit: true,
                    minWidth: 100,
                  },
                ],
              },
            ]"
            @change-input="val => (componentRemoteSearch.customer_name = val)"
          />
          <!-- <SelectComponents
          api="GetCustomerEnumList"
          label-field="name"
          value-field="id"
          v-model="filterData.customer_id"
           clearable /> -->
        </el-descriptions-item>
        <el-descriptions-item label="成品编号">
          <SelectProductDialog
            v-model="filterData.product_id"
            :label-name="componentRemoteSearch.finish_product_code"
            field="finish_product_code"
            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }"
            @on-input="val => (componentRemoteSearch.finish_product_code = val)"
            @change-value="changePro"
          />
          <!--          <SelectDialog -->
          <!--            ref="proRef1" -->
          <!--            v-model="filterData.product_id" -->
          <!--            label-field="finish_product_code" -->
          <!--            :query="{ finish_product_code: componentRemoteSearch.finish_product_code }" -->
          <!--            api="GetFinishProductDropdownList" -->
          <!--            :column-list="[ -->
          <!--              { -->
          <!--                field: 'finish_product_name', -->
          <!--                colGroupHeader: true, -->
          <!--                title: '成品名称', -->
          <!--                minWidth: 100, -->
          <!--                childrenList: [ -->
          <!--                  { -->
          <!--                    field: 'finish_product_name', -->
          <!--                    title: '成品名称', -->
          <!--                    minWidth: 100, -->
          <!--                  }, -->
          <!--                ], -->
          <!--              }, -->
          <!--              { -->
          <!--                field: 'finish_product_code', -->
          <!--                colGroupHeader: true, -->
          <!--                title: '成品编号', -->
          <!--                minWidth: 100, -->
          <!--                childrenList: [ -->
          <!--                  { -->
          <!--                    field: 'finish_product_code', -->
          <!--                    title: '成品编号', -->
          <!--                    minWidth: 100, -->
          <!--                  }, -->
          <!--                ], -->
          <!--              }, -->
          <!--            ]" -->
          <!--            :table-column="[ -->
          <!--              { -->
          <!--                field: 'finish_product_code', -->
          <!--                title: '成品编号', -->
          <!--              }, -->
          <!--            ]" -->
          <!--            @on-input="val => (componentRemoteSearch.finish_product_code = val)" -->
          <!--            @change-value="changePro('proRef1')" -->
          <!--          /> -->
        </el-descriptions-item>
        <el-descriptions-item label="成品名称">
          <!--          <el-input v-model="filterData.product_name" /> -->
          <SelectProductDialog
            v-model="filterData.product_id"
            :label-name="componentRemoteSearch.finish_product_name"
            field="finish_product_name"
            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }"
            @change-value="changePro"
            @on-input="val => (componentRemoteSearch.finish_product_name = val)"
          />
          <!--          <SelectDialog -->
          <!--            ref="proRef2" -->
          <!--            v-model="filterData.product_id" -->
          <!--            label-field="finish_product_name" -->
          <!--            :query="{ finish_product_name: componentRemoteSearch.finish_product_name }" -->
          <!--            api="GetFinishProductDropdownList" -->
          <!--            :column-list="[ -->
          <!--              { -->
          <!--                field: 'finish_product_name', -->
          <!--                colGroupHeader: true, -->
          <!--                title: '成品名称', -->
          <!--                minWidth: 100, -->
          <!--                childrenList: [ -->
          <!--                  { -->
          <!--                    field: 'finish_product_name', -->
          <!--                    title: '成品名称', -->
          <!--                    minWidth: 100, -->
          <!--                  }, -->
          <!--                ], -->
          <!--              }, -->
          <!--              { -->
          <!--                field: 'finish_product_code', -->
          <!--                colGroupHeader: true, -->
          <!--                title: '成品编号', -->
          <!--                minWidth: 100, -->
          <!--                childrenList: [ -->
          <!--                  { -->
          <!--                    field: 'finish_product_code', -->
          <!--                    title: '成品编号', -->
          <!--                    minWidth: 100, -->
          <!--                  }, -->
          <!--                ], -->
          <!--              }, -->
          <!--            ]" -->
          <!--            :table-column="[ -->
          <!--              { -->
          <!--                field: 'finish_product_name', -->
          <!--                title: '成品名称', -->
          <!--              }, -->
          <!--            ]" -->
          <!--            @change-value="changePro('proRef2')" -->
          <!--            @on-input="val => (componentRemoteSearch.finish_product_name = val)" -->
          <!--          /> -->
        </el-descriptions-item>
        <el-descriptions-item label="色号">
          <SelectDialog
            ref="customerRef1"
            key="color2"
            v-model="filterData.product_color_id"
            :disabled="!filterData.product_id"
            :query="{ finish_product_id: filterData.product_id, product_color_code: componentRemoteSearch.color_code }"
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_code',
                title: '色号',
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_code"
            @on-input="val => (componentRemoteSearch.color_code = val)"
            @change-value="changeColor('customerRef1')"
          />
          <!-- <SelectComponents api="GetFinishProductColorDropdownList" label-field="product_color_code" value-field="id" v-model="filterData.product_color_id" clearable /> -->
        </el-descriptions-item>
        <el-descriptions-item label="颜色">
          <SelectDialog
            key="color1"
            ref="customerRef2"
            v-model="filterData.product_color_id"
            :disabled="!filterData.product_id"
            :query="{ finish_product_id: filterData.product_id, product_color_name: componentRemoteSearch.color_name }"
            :column-list="[
              {
                field: 'product_color_code',
                title: '色号',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_code',
                    isEdit: true,
                    title: '色号',
                    minWidth: 100,
                  },
                ],
              },
              {
                field: 'product_color_name',
                title: '颜色',
                minWidth: 100,
                isEdit: true,
                colGroupHeader: true,
                childrenList: [
                  {
                    field: 'product_color_name',
                    isEdit: true,
                    title: '颜色',
                    minWidth: 100,
                  },
                ],
              },
            ]"
            :table-column="[
              {
                field: 'product_color_name',
                title: '色号',
              },
            ]"
            api="GetFinishProductColorDropdownList"
            label-field="product_color_name"
            @on-input="val => (componentRemoteSearch.color_name = val)"
            @change-value="changeColor('customerRef2')"
          />
          <!-- <SelectComponents api="GetFinishProductColorDropdownList" label-field="product_color_name" value-field="id" v-model="filterData.product_color_id" clearable /> -->
        </el-descriptions-item>
        <el-descriptions-item label="成品等级">
          <SelectComponents v-model="filterData.product_level_id" api="GetInfoBaseFinishedProductLevelEnumList" label-field="name" value-field="id" clearable />
        </el-descriptions-item>
        <el-descriptions-item label="显示方式">
          <SelectComponents v-model="filterData.stock_show_type" api="GetStockShowType" label-field="name" value-field="id" clearable />
        </el-descriptions-item>
      </el-descriptions>
    </FildCard>
    <FildCard class="table-card-full !p-0" :tool-bar="true">
      <template #right-top>
        <BottonExcel plain title="导出文件" @on-click-excel="handleExport" />

      <!--      <BottonExcel plain class="!ml-2" :loading="loadingExcel" @onClickExcel="handleAllExport" title="全部导出"></BottonExcel> -->
      </template>
      <Table ref="mainOptionsTablesRef" :config="tableConfig" :table-list="mainDataList.list" :column-list="mainOptions.columnList">
        <template #product_code="{ row }">
          {{ formatHashTag(row?.product_code, row?.product_name) }}
        </template>
        <template #product_color_code="{ row }">
          {{ formatHashTag(row?.product_color_code, row?.product_color_name) }}
        </template>
        <template #available_roll="{ row }">
          <div class="flex items-center justify-between w-full">
            {{ formatPriceDiv(row?.available_roll) }}
            <el-icon v-if="row.available_roll < row?.stock_roll || row.available_weight < row?.weight" color="var(--el-color-primary)" size="16" class="ml-2 cursor-pointer" @click="handleShowOccupyStock(row)">
              <View />
            </el-icon>
          </div>
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #operate="{ row }">
          <el-button type="primary" text link @click="handDetail(row)">
            查看进出仓情况
          </el-button>
        </template>
      </Table>
    </FildCard>
  </div>
  <DetailModel :id="defaultId" ref="detailRef" v-model="showModel" :status="1" />
  <OccupyStockDialog ref="occupyStockRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
