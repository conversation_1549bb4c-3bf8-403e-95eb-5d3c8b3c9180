<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { getStockProductDyelotNumberDetailList } from '@/api/fpPurchaseReturnDeliverGodown'
import { getStockProductDropdownList } from '@/api/fpSubscribeWarehouseOrder'
import {
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, getFilterData, resetData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import FildCard from '@/components/FildCard.vue'
import { fieldApiKeyList } from '@/common/fieldApiKeyList'
import SelectProductDialog from '@/components/SelectProductDialog/index.vue'
import SelectProductColorDialog from '@/components/SelectProductColorDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'

export interface Props {
  // eslint-disable-next-line vue/prop-name-casing
  warehouse_id: number | ''
}

const props = withDefaults(defineProps<Props>(), {
  warehouse_id: '',
})

const emits = defineEmits(['handleSure'])

const componentRemoteSearch = reactive({
  product_code: '',
  product_name: '',
  product_color_code: '',
  product_color_name: '',
})

const state = reactive<any>({
  filterData: {
    product_id: '',
    customer_id: '',
    product_color_id: '',
    warehouse_id: '',
    product_level_id: '',
    dyelot_number: '',
    available_only: true,
    radioValue: '2',
  },
  showModal: false,
  modalName: '根据库存添加',
  multipleSelection: [],
  list: [],
})

const {
  fetchData: fetchData1,
  data: data1,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getStockProductDyelotNumberDetailList()

const {
  fetchData: fetchData2,
  data: data2,
  total: total2,
  loading: loading2,
  page: page2,
  size: size2,
  handleSizeChange: handleSizeChange2,
  handleCurrentChange: handleCurrentChange2,
} = getStockProductDropdownList()

const tableConfig = reactive<any>({
  loading: state.filterData.radioValue === '1' ? loading1 : loading2,
  showPagition: true,
  showSlotNums: true,
  page: state.filterData.radioValue === '1' ? page1 : page2,
  size: state.filterData.radioValue === '1' ? size1 : size2,
  total: state.filterData.radioValue === '1' ? total1 : total2,
  showCheckBox: true,
  showSort: false,
  height: '400',
  filterStatus: false,
  fieldApiKey: fieldApiKeyList.AccordingSubscribeStockAdd,
  handleSizeChange: (val: number) =>
    state.filterData.radioValue === '1'
      ? handleSizeChange1(val)
      : handleSizeChange2(val),
  handleCurrentChange: (val: number) =>
    state.filterData.radioValue === '1'
      ? handleCurrentChange1(val)
      : handleCurrentChange2(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  checkboxConfig: {
    highlight: true,
    reserve: true,
    checkMethod: ({ row }: any) => {
      return row.available_roll !== 0 || row.available_weight !== 0
    },
  },
})

// 选择成品
function changeProductSelect(val: any) {
  state.filterData.product_id = val?.id || ''
  componentRemoteSearch.product_code = val?.finish_product_code || ''
  componentRemoteSearch.product_name = val?.finish_product_name || ''

  // 清空色号
  state.filterData.product_color_id = ''
  componentRemoteSearch.product_color_code = ''
  componentRemoteSearch.product_color_name = ''
}
// 选择色号
function changeProductColorSelect(val: any) {
  state.filterData.product_color_id = val?.id || ''
  componentRemoteSearch.product_color_code = val?.product_color_code || ''
  componentRemoteSearch.product_color_name = val?.product_color_name || ''
}

function handChange(val: any) {
  state.filterData.radioValue = val.toString()
}

async function getData() {
  state.filterData.radioValue === '1'
    ? await fetchData1(getFilterData(state.filterData))
    : await fetchData2(getFilterData(state.filterData))
}

watch(
  [() => data1.value, () => data2.value],
  () => {
    const list
      = state.filterData.radioValue === '1'
        ? data1.value?.list
        : data2.value?.list
    state.list
      = list?.map((item: any) => {
        return {
          ...item,
          unit_price: formatUnitPriceDiv(item.unit_price),
          length_unit_price: formatUnitPriceDiv(item.length_unit_price),
          roll: formatTwoDecimalsDiv(item.roll),
          stock_roll: formatTwoDecimalsDiv(item.stock_roll),
          available_roll: formatTwoDecimalsDiv(item.available_roll),
          available_weight: formatWeightDiv(item.available_weight),
          length: formatLengthDiv(item.length),
          weight: formatWeightDiv(item.weight),
        }
      }) || []
    tableConfig.total = state.filterData.radioValue === '1' ? total1 : total2
    tableConfig.page = state.filterData.radioValue === '1' ? page1 : page2
    tableConfig.size = state.filterData.radioValue === '1' ? size1 : size2
  },
  { deep: true },
)

watch(
  () => state.showModal,
  () => {
    if (state.showModal) {
      state.filterData.warehouse_id = props.warehouse_id || ''
      state.multipleSelection = [] // 清空选择内容
      getData()
    }
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const columnList = ref([
  {
    field: 'product_code',
    minWidth: 100,
    title: '成品编号',
    fixed: 'left',
  },
  {
    field: 'product_name',
    minWidth: 100,
    title: '成品名称',
    fixed: 'left',
  },
  {
    field: 'customer_name',
    minWidth: 100,
    title: '所属客户',
  },
  {
    field: 'product_color_code',
    minWidth: 100,
    title: '色号',
  },
  {
    field: 'product_color_name',
    minWidth: 100,
    title: '颜色',
  },
  {
    field: 'warehouse_name',
    minWidth: 100,
    title: '所属仓库',
  },
  {
    field: 'dyelot_number',
    minWidth: 100,
    title: '染厂缸号',
  },
  {
    field: 'product_craft',
    minWidth: 100,
    title: '成品工艺',
    soltName: 'product_craft',
  },
  {
    field: 'product_level_name',
    minWidth: 100,
    title: '成品等级',
  },
  {
    field: 'product_ingredient',
    minWidth: 100,
    title: '成品成分',
    soltName: 'product_ingredient',
  },
  {
    field: 'product_remark',
    width: 100,
    title: '成品备注',
  },
  // {
  //   field: 'unit_price',
  //   minWidth: 100,
  //   title: '数量单价',
  // },
  // {
  //   field: 'length_unit_price',
  //   minWidth: 100,
  //   title: '辅助数量单价',
  // },
  {
    field: 'stock_roll',
    minWidth: 70,
    title: '库存匹数',
    fixed: 'right',
  },
  {
    field: 'weight',
    minWidth: 70,
    title: '数量总计',
    fixed: 'right',
  },
  {
    field: 'measurement_unit_name',
    minWidth: 60,
    title: '单位',
    fixed: 'right',
  },
  {
    field: 'length',
    minWidth: 100,
    title: '辅助数量总计',
    fixed: 'right',
  },
  {
    field: 'available_roll',
    soltName: 'available_roll',
    width: 70,
    title: '可用匹数',
    fixed: 'right',
  },
  {
    field: 'available_weight',
    soltName: 'available_weight',
    width: 70,
    title: '可用数量',
    fixed: 'right',
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择数据')
  }
  else {
    state.showModal = false
    emits('handleSure', state.multipleSelection)
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
  state.filterData.radioValue = '1'
}

// const productNameRef = ref()
// const productCodeRef = ref()
// function changePro(stringRef: string) {
//   if (stringRef === 'productNameRef') {
//     productCodeRef.value.inputLabel
//       = productNameRef.value.item?.finish_product_code
//   }
//   else {
//     productNameRef.value.inputLabel
//       = productCodeRef.value.item?.finish_product_name
//   }
// }

// const colorRef1 = ref()
// const colorRef2 = ref()
// function changeColor(stringRef: string) {
//   if (stringRef === 'colorRef2')
//     colorRef1.value.inputLabel = colorRef2.value.item?.product_color_code
//   else
//     colorRef2.value.inputLabel = colorRef1.value.item?.product_color_name
// }

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal
    v-model="state.showModal"
    show-footer
    :title="state.modalName"
    width="80vw"
    height="80vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
  >
    <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
      <DescriptionsFormItem label="成品编号:">
        <template #content>
          <SelectProductDialog
            v-model="state.filterData.product_id"
            field="finish_product_code"
            :label-name="componentRemoteSearch.product_code"
            :default-value="{
              id: state.filterData.product_id,
              finish_product_name: componentRemoteSearch.product_name,
              finish_product_code: componentRemoteSearch.product_code,
            }"
            @change-value="(val: any) => changeProductSelect(val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品名称:">
        <template #content>
          <SelectProductDialog
            v-model="state.filterData.product_id"
            :label-name="componentRemoteSearch.product_code"
            :default-value="{
              id: state.filterData.product_id,
              finish_product_name: componentRemoteSearch.product_name,
              finish_product_code: componentRemoteSearch.product_code,
            }"
            @change-value="(val: any) => changeProductSelect(val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="所属客户:">
        <template #content>
          <SelectCustomerDialog v-model="state.filterData.customer_id" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="色号:">
        <template #content>
          <SelectProductColorDialog
            v-model="state.filterData.product_color_id"
            field="product_color_code"
            :query="{
              finish_product_id: state.filterData.product_id,
            }"
            :disabled="!state.filterData.product_id"
            :default-value="{
              id: state.filterData.product_color_id,
              product_color_name: componentRemoteSearch.product_color_name,
              product_color_code: componentRemoteSearch.product_color_code,
            }"
            @change-value="(val: any) => changeProductColorSelect(val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="颜色:">
        <template #content>
          <SelectProductColorDialog
            v-model="state.filterData.product_color_id"
            :query="{
              finish_product_id: state.filterData.product_id,
            }"
            :disabled="!state.filterData.product_id"
            :default-value="{
              id: state.filterData.product_color_id,
              product_color_name: componentRemoteSearch.product_color_name,
              product_color_code: componentRemoteSearch.product_color_code,
            }"
            @change-value="(val: any) => changeProductColorSelect(val)"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="所属仓库:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.warehouse_id"
            :disabled="state.filterData.warehouse_id"
            api="GetPhysicalWarehouseDropdownList"
            label-field="name"
            value-field="id"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="成品等级:">
        <template #content>
          <SelectComponents
            v-model="state.filterData.product_level_id"
            api="GetInfoBaseFinishedProductLevelEnumList"
            label-field="name"
            value-field="id"
          />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="染厂缸号:">
        <template #content>
          <vxe-input v-model="state.filterData.dyelot_number" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="仅显示有可用库存:">
        <template #content>
          <el-checkbox v-model="state.filterData.available_only" />
        </template>
      </DescriptionsFormItem>
      <DescriptionsFormItem label="">
        <template #content>
          <el-radio-group
            v-model="state.filterData.radioValue"
            class="ml-4"
            @change="(val:any) => handChange(val)"
          >
            <el-radio label="1" size="large">
              按缸号库存添加
            </el-radio>
            <el-radio label="2" size="large">
              按汇总库存添加
            </el-radio>
          </el-radio-group>
        </template>
      </DescriptionsFormItem>
    </div>
    <FildCard title="" :tool-bar="true" no-shadow>
      <template #right-top>
        <el-button type="primary" @click="handReset">
          重置
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="state.list"
        :column-list="columnList"
      >
        <template #product_craft="{ row }">
          {{ state.filterData.radioValue === '1' ? row.finish_product_craft : row.product_craft }}
        </template>
        <template #product_ingredient="{ row }">
          {{ state.filterData.radioValue === '1' ? row.finish_product_ingredient : row.product_ingredient }}
        </template>
        <template #available_roll="{ row }">
          <span v-if="row.available_roll === 0" style="color: red">{{
            row.available_roll
          }}</span>
          <span v-else>{{ row.available_roll }}</span>
        </template>
        <template #available_weight="{ row }">
          <span v-if="row.available_weight === 0" style="color: red">{{
            row.available_weight
          }}</span>
          <span v-else>{{ row.available_weight }}</span>
        </template>
      </Table>
    </FildCard>

    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex_end {
  display: flex;
  justify-content: flex-end;
}
</style>
