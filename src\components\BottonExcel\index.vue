<script lang="ts" setup name="BottonExcel">
import { Document as DocumentIcon } from '@element-plus/icons-vue'

interface Props {
  loading?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '导出Excel',
})

const emit = defineEmits<{
  (e: 'onClickExcel'): void
}>()

function outExcel() {
  emit('onClickExcel')
}
</script>

<template>
  <el-button v-btnAntiShake="outExcel" type="info" text link :icon="DocumentIcon" :loading="loading">
    {{ props.loading ? '正在导出...' : props.title }}
  </el-button>
</template>

<style scoped>

</style>
