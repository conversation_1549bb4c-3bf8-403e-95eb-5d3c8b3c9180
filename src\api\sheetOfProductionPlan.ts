import { useRequest } from '@/use/useRequest'

// 获取生产计划单
export const getSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/getProductionPlanOrder',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取生产计划单列表
export const getSheetOfProductionPlanList = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/getProductionPlanOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 添加生产计划单
export const addSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/addProductionPlanOrder',
    method: 'post',
  })
}
// 删除生产计划单
export const deleteSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/deleteProductionPlanOrder',
    method: 'delete',
  })
}
// 根据id获取生产计划单列表坯布信息
export const byIdSheetOfProductionPlanGreyInfo = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/getProductionPlanOrderListGreyFabricItems',
    method: 'get',
  })
}
// 更新生产计划单
export const putSheetOfProductionPlanInfo = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/updateProductionPlanOrder',
    method: 'put',
  })
}

// 更新生产计划单状态
export const putSheetOfProductionPlanStatus = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/updateProductionPlanOrderBusinessClose',
    method: 'put',
  })
}

// 审核生产计划单
export const checkSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/updateProductionPlanOrderStatusAudit',
    method: 'put',
  })
}

// 消审生产计划单
export const cancelApprovedSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/updateProductionPlanOrderStatusWait',
    method: 'put',
  })
}

// 获取计划类型下拉列表
export const PlanTypeDropdownList = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanType',
    method: 'get',
  })
}

// 作废
export const deleteCancelSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/updateProductionPlanOrderStatusCancel',
    method: 'put',
  })
}

// 驳回
export const rejectSheetOfProductionPlan = () => {
  return useRequest({
    url: '/admin/v1/produce/productionPlanOrder/updateProductionPlanOrderStatusReject',
    method: 'put',
  })
}

// 获取销售计划单列表
export const getSaleProductPlanOrderGfDropdownList = () => {
  return useRequest({
    url: '/admin/v1/sale/saleProductPlanOrder/getSaleProductPlanOrderGfDropdownList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
