<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, watch } from 'vue'
import { type UseFabricFlyCancelOptions, useFabricFlyCancel } from '../composables/useFabricFlyCancel'

// Props 定义
interface Props {
  mode?: 'page' | 'dialog' // 模式：页面模式或弹框模式
  showHeader?: boolean // 是否显示头部
  showFooter?: boolean // 是否显示底部按钮
  title?: string // 标题
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'dialog',
  showHeader: true,
  showFooter: true,
  title: '布飞取消',
})

// Emits 定义
const emit = defineEmits<{
  success: [data: { barcode: string }]
  cancel: []
}>()

// 使用布飞取消逻辑
const options: UseFabricFlyCancelOptions = {
  onSuccess: data => emit('success', data),
  onCancel: () => emit('cancel'),
}

const {
  formData,
  fabricDetail,
  loading,
  showFabricInfo,
  barcodeInputRef,
  getFabricInfo,
  handleSave,
  handleClear,
  handleCancel,
  handleKeydown,
} = useFabricFlyCancel(options)

// 生命周期管理
onMounted(() => {
  nextTick(() => {
    barcodeInputRef.value?.focus()
  })
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 暴露方法给父组件
defineExpose({
  handleSave,
  handleClear,
})
</script>

<template>
  <div class="fabric-cancel-content" :class="{ 'page-mode': mode === 'page' }">
    <!-- 头部 -->
    <div v-if="showHeader && mode === 'page'" class="content-header">
      <span class="content-title">{{ title }}</span>
    </div>

    <!-- 主要内容 -->
    <div class="content-body">
      <!-- 提示信息 -->
      <div class="alert-info">
        <span class="alert-text">提示：等待输入条形码资料!</span>
      </div>

      <!-- 条码输入 -->
      <div class="barcode-section">
        <label class="barcode-label">条码：</label>
        <el-input
          ref="barcodeInputRef"
          v-model="formData.barcode"
          :size="mode === 'page' ? 'large' : 'default'"
          class="barcode-input"
          placeholder="请输入条码"
          clearable
          @keyup.enter="getFabricInfo(formData.barcode)"
        />
      </div>

      <!-- 布飞信息展示 -->
      <div v-if="showFabricInfo" v-loading="loading" class="fabric-info">
        <el-row :gutter="20" class="info-row">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">生产通知单</span>
              <span class="info-value">{{ fabricDetail.production_notice_no }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">排产单</span>
              <span class="info-value">{{ fabricDetail.production_order_no }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="info-row">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">坯布名称</span>
              <span class="info-value">{{ fabricDetail.fabric_name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">卷号</span>
              <span class="info-value">{{ fabricDetail.roll_no }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="info-row">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">机台</span>
              <span class="info-value">{{ fabricDetail.machine_no }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">条码</span>
              <span class="info-value">{{ fabricDetail.barcode }}</span>
            </div>
          </el-col>
        </el-row>

        <!-- 纱名 - 单独一行，因为内容较长 -->
        <div class="yarn-section">
          <span class="yarn-label">纱名</span>
          <div class="yarn-content">
            {{ fabricDetail.yarn_name }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div v-if="showFooter" class="content-footer" :class="{ 'page-footer': mode === 'page' }">
      <el-button :size="mode === 'page' ? 'large' : 'default'" @click="handleCancel">
        取消
      </el-button>
      <el-button
        :size="mode === 'page' ? 'large' : 'default'"
        type="primary"
        :disabled="!showFabricInfo"
        :loading="loading"
        @click="handleSave"
      >
        保存 (F12)
      </el-button>
      <el-button :size="mode === 'page' ? 'large' : 'default'" @click="handleClear">
        清空 (F1)
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.fabric-cancel-content {
  width: 100%;
}

/* 页面模式样式 */
.page-mode {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* 头部 */
.content-header {
  background: #409eff;
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-title {
  font-size: 16px;
  font-weight: 500;
}

/* 主要内容 */
.content-body {
  padding: 20px;
  min-height: 400px;
}

/* 提示信息 */
.alert-info {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-left: 4px solid #f56c6c;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.alert-text {
  color: #f56c6c;
  font-size: 14px;
}

/* 条码输入区域 */
.barcode-section {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  gap: 12px;
}

.barcode-label {
  font-size: 14px;
  color: #606266;
  min-width: 50px;
}

.barcode-input {
  flex: 1;
  max-width: 400px;
}

/* 布飞信息展示 */
.fabric-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.info-row {
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  min-width: 80px;
  margin-right: 12px;
}

.info-value {
  font-size: 14px;
  color: #303133;
  flex: 1;
  word-break: break-all;
}

/* 纱名区域 */
.yarn-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.yarn-label {
  font-size: 14px;
  color: #606266;
  display: block;
  margin-bottom: 8px;
}

.yarn-content {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  word-break: break-all;
  min-height: 80px;
}

/* 底部按钮 */
.content-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.page-footer {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-mode {
    margin: 10px;
    max-width: none;
  }

  .content-body {
    padding: 16px;
  }

  .barcode-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .barcode-input {
    width: 100%;
    max-width: none;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    margin-bottom: 4px;
  }
}
</style>
