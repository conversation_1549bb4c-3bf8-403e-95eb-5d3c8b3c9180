<script setup lang="ts" name="CashCommodityClothOrder">
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { cloneDeep } from 'lodash-es'
import { Delete } from '@element-plus/icons-vue'
import FineSizeSelectStockDetail from '../components/FineSizeSelectStockDetail.vue'
import {
  getFpmArrangeOrder,
  getFpmArrangeOrderList,
  outFpmArrangeOrder,
  updateFpmArrangeOrderStatusPass,
  updateFpmArrangeOrderStatusWait,
} from '@/api/cashCommodityClothOrder'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import router from '@/router'
import { sumNum } from '@/util/tableFooterCount'
import { deliverFromGoDownJumpPage } from '@/pages/finishManagement/cashCommodityClothOrder/util'
import { usePageQuery } from '@/use/usePageQuery'
import { WarehouseGoodOutTypeEnum } from '@/enum'

const { formatFilterObj, formatDateRange } = usePageQuery()

const FineSizeCashCommodityClothDetailRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  warehouse_id: '',
  out_order_type: '',
  audit_status: [],
  business_status_ids: [],
}))
const arrange_time = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmArrangeOrderStatusPass()
// 消审
// const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = updateFpmArrangeOrderStatusWait()
const {
  fetchData: fetchDataList,
  data: mainList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
} = getFpmArrangeOrderList()
const mainOptions = reactive<any>({
  tableConfig: {
    fieldApiKey: 'CashCommodityClothOrder',
    showSlotNums: true,
    loading: loadingList,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '10%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'src_order_no',
      title: '预约/销售单号',
      soltName: 'src_order_no',
      minWidth: 150,
    },
    {
      sortable: true,
      field: 'out_order_type_name',
      title: '出货类型',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'arrange_to_warehouse_name',
      title: '调入仓库',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'biz_unit_name',
      title: '收货单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_mode_name',
      title: '订单类型',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'arrange_time',
      title: '配布日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'store_keeper_name',
      title: '仓管员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
    {
      field: 'business_status_name',
      soltName: 'business_status_name',
      title: '业务状态',
      fixed: 'right',
      width: 80,
    },
  ],
  showToolBar: true,
  //   导出
  handleExport: async () => {
    // if (mainOptions.mainList.length < 1) return ElMessage.warning('当前无数据可导出')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getFpmArrangeOrderList()
    // mainOptions.exportOptions.loadingExcel.value = true
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //   exportExcel()
    //   ElMessage({
    //     type: 'success',
    //     message: '成功',
    //   })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel.value = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})

async function getData() {
  const query = cloneDeep({
    ...filterData,
  })
  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (arrange_time?.value?.length) {
    query.arrange_time_begin = formatDate(arrange_time.value[0])
    query.arrange_time_end = formatDate(arrange_time.value[1])
  }
  // 如果business_status_ids有值，需要将值转换成用逗号隔开的字符串
  if (query.business_status_ids.length)
    query.business_status_ids = query.business_status_ids.join(',')

  await fetchDataList(getFilterData({ ...query }))
  if (mainList.value?.list)
    showDetail(mainList.value.list[0])
}

onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)

watch(
  () => mainList.value,
  () => {
    mainOptions.mainList
      = mainList?.value?.list?.map((item: any) => {
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(Number(item.total_roll)), // 100
          total_weight: formatWeightDiv(Number(item.total_weight)), // weight
          total_length: formatLengthDiv(Number(item.total_length)), // 100
          total_price: formatTwoDecimalsDiv(Number(item.total_price)), // 100
        }
      }) || []
  },
  { deep: true },
)

// 审核
async function handAudit(row: any) {
  if (
    [1, 2].includes(row.last_arrange_order_business_status)
    && row.business_status === 3
  ) {
    const res = await deleteToast('配布单业务状态已变为已配布，请重新编辑')
    if (res) {
      if (auditSuccess.value) {
        router.push({
          name: 'CashCommodityClothOrderEdit',
          query: {
            id: row.id,
          },
        })
      }
      else {
        ElMessage.error(auditMsg.value)
      }
    }
  }
  else {
    const res = await deleteToast('确认提交审核嘛？')
    if (res) {
      await auditFetch({ audit_status: 2, id: row.id.toString() })
      if (auditSuccess.value) {
        ElMessage.success('成功')
        getData()
      }
      else {
        ElMessage.error(auditMsg.value)
      }
    }
  }
}
// // 消审
// const handApproved = async (row: any) => {
//   const res = await deleteToast('确认取消审核嘛？')
//   if (res) {
//     await cancelFetch({ audit_status: 1, id: row.id.toString() })
//     if (cancelSuccess.value) {
//       ElMessage.success('成功')
//       getData()
//     } else {
//       ElMessage.error(cancelMsg.value)
//     }
//   }
// }

function handDetail(row: any) {
  router.push({
    name: 'CashCommodityClothOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'CashCommodityClothOrderEdit',
    query: {
      id: row.id,
    },
  })
}

// 成品信息
const finishProductionOptions = reactive({
  detailShow: false,
  tableConfig: {
    fieldApiKey: 'CashCommodityClothOrder_A',
    showSlotNums: false,
    showSpanHeader: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 出仓匹数
        if (['arrange_roll'].includes(column.field))
          return sumNum(data, 'arrange_roll', '')
        // 库存匹数
        if (['sum_stock_roll'].includes(column.field))
          return sumNum(data, 'sum_stock_roll', '')
        // 库存数量
        if (['sum_stock_weight'].includes(column.field))
          return sumNum(data, 'sum_stock_weight', '')
        // 库存辅助数量
        if (['sum_stock_length'].includes(column.field))
          return sumNum(data, 'sum_stock_length', '')
        // 出仓数量
        if (['arrange_weight'].includes(column.field))
          return sumNum(data, 'arrange_weight', '')
        // 码单空差
        if (['weight_error'].includes(column.field))
          return sumNum(data, 'weight_error', '')
        // 结算空差
        if (['settle_error_weight'].includes(column.field))
          return sumNum(data, 'settle_error_weight', '')
        // 结算数量
        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')
        // 码单数量
        if (['actually_weight'].includes(column.field))
          return sumNum(data, 'actually_weight', '')
        // 辅助数量
        if (['arrange_length'].includes(column.field))
          return sumNum(data, 'arrange_length', '')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },

        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'dye_factory_dyelot_number',
          title: '染厂缸号',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'arrange_roll',
          title: '出仓匹数',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'C',
      title: '库存信息',
      childrenList: [
        {
          sortable: true,
          field: 'sum_stock_roll',
          title: '库存匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'sum_stock_weight',
          title: '库存数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'sum_stock_length',
          title: '库存辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'D',
      title: '出仓数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'arrange_weight',
          title: '出仓数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'weight_error',
          title: '码单空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'actually_weight',
          title: '码单数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_error_weight',
          title: '结算空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_weight',
          title: '结算数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'E',
      title: '出仓辅助数量信息',
      childrenList: [
        {
          sortable: true,
          field: 'arrange_length',
          title: '辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'F',
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'G',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
  showXima: (row: any) => {
    FineSizeCashCommodityClothDetailRef.value.state.isCloth = true
    FineSizeCashCommodityClothDetailRef.value.showDialog({
      ...row,
      same_color_same_dye_lot: finishProData.value.same_color_same_dye_lot,
    })
  },
})

// 获取成品信息
function showDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}
function handleSrcDetail(row: any) {
  if (row.src_type === 1) {
    router.push({
      name: 'FpSubscribeWarehouseOrderDetail',
      query: {
        id: row.src_id,
      },
    })
  }
  else if (row.src_type === 2) {
    router.push({
      name: 'ProductSaleDetail',
      query: {
        id: row.src_id,
      },
    })
  }
  // routeToDetail('srcOrderType', row.src_type, row.src_id)
}
const { fetchData: DetailFetch, data: finishProData } = getFpmArrangeOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        arrange_roll: formatTwoDecimalsDiv(Number(item.arrange_roll)), // 100
        sum_stock_roll: formatTwoDecimalsDiv(Number(item.sum_stock_roll)), // 100
        sum_stock_weight: formatWeightDiv(Number(item.sum_stock_weight)), // weight
        sum_stock_length: formatLengthDiv(Number(item.sum_stock_length)), // 100
        arrange_weight: formatWeightDiv(Number(item.arrange_weight)), // weight
        actually_weight: formatWeightDiv(Number(item.actually_weight)), // weight
        weight_error: formatWeightDiv(Number(item.weight_error)), // weight
        settle_error_weight: formatWeightDiv(Number(item.settle_error_weight)), // weight
        settle_weight: formatWeightDiv(Number(item.settle_weight)), // weight
        unit_price: formatUnitPriceDiv(Number(item.unit_price)), // price
        arrange_length: formatLengthDiv(Number(item.arrange_length)), // 100
        length_unit_price: formatUnitPriceDiv(Number(item.length_unit_price)), // price
        other_price: formatTwoDecimalsDiv(Number(item.other_price)), // 100
        total_price: formatTwoDecimalsDiv(Number(item.total_price)), // 100
        item_fc_data: item.item_fc_data || [],
      }
    },
  )
}

function changeDate() {
  // arrange_time.value = [row.date_min, row.date_max]
  getData()
}
// 变更需要生成变更单
async function handChange(row: any) {
  router.push({
    name: 'SaleCashCommodityClothChangeOrderAdd',
    query: {
      id: row.id,
    },
  })
}
// 确认出仓
const {
  fetchData: outFetch,
  data,
  success: outSuccess,
  msg: outMsg,
} = outFpmArrangeOrder()

async function handDeliverFromGodown(row: any) {
  await outFetch({
    id: Number(row.id),
  })
  if (outSuccess.value) {
    ElMessage.success('成功')
    getData()
    // 齐单提货&销售出仓单不跳转
    if (row.pick_up_goods_in_order && row.out_order_type === WarehouseGoodOutTypeEnum.Sale)
      return

    // 根据出货类型需要跳转到对应的详情页面
    deliverFromGoDownJumpPage(row, data.value)
  }
  else {
    ElMessage.error(outMsg.value)
  }
}

// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmArrangeOrderStatusWait()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input
              v-model="filterData.order_no"
              clearable
              placeholder="单据编号"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 200px"
              api="GetPhysicalWarehouseDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="出货类型:">
          <template #content>
            <SelectComponents
              v-model="filterData.out_order_type"
              style="width: 200px"
              api="GetWarehouseGoodOutTypeEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="配布日期:" width="310px">
          <template #content>
            <SelectDate v-model="arrange_time" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="业务状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.business_status_ids"
              api="GetBusinessStatusEnumDropdownList"
              label-field="name"
              value-field="id"
              multiple
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="mainOptions.showToolBar">
      <!-- <template v-slot:right-top>
      <BottonExcel :loading="mainOptions.exportOptions.loadingExport" @onClickExcel="mainOptions.exportOptions.handleExport" title="导出文件"></BottonExcel>
    </template> -->
      <Table
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="showDetail(row)">
            {{
              row?.order_no
            }}
          </el-link>
        </template>
        <template #src_order_no="{ row }">
          <el-link type="primary" :underline="false" @click="handleSrcDetail(row)">
            {{
              row?.src_order_no
            }}
          </el-link>
        </template>
        <template #business_status_name="{ row }">
          <!--        <el-tag type="danger" round v-if="row?.business_status_name === '待配布'">待配布</el-tag> -->
          <!--        <el-tag type="warning" round v-if="row?.business_status_name === '已配布'">已配布</el-tag> -->
          <!--        <el-tag round v-if="row?.business_status_name === '待出仓'">待出仓</el-tag> -->
          <!--        <el-tag type="success" round v-if="row?.business_status_name === '已出仓'">已出仓</el-tag> -->
          <div
            v-if="row?.business_status_name === '待配布'"
            class="w-[60px] h-[20px] bg-[#fef0f0] rounded-[20px] text-[#f56c6c] text-center"
          >
            待配布
          </div>
          <div
            v-else-if="row?.business_status_name === '配布中'"
            class="w-[60px] h-[20px] bg-[#fef0f0] rounded-[20px] text-[#f56c6c] text-center"
          >
            配布中
          </div>
          <div
            v-else-if="row?.business_status_name === '已配布'"
            class="w-[60px] h-[20px] bg-[#faecd8] rounded-[20px] text-[#e6a23c] text-center"
          >
            已配布
          </div>
          <div
            v-else-if="row?.business_status_name === '待出仓'"
            class="w-[60px] h-[20px] bg-[#ecf5ff] rounded-[20px] text-[#409eff] text-center"
          >
            待出仓
          </div>
          <div
            v-else-if="row?.business_status_name === '已出仓'"
            class="w-[60px] h-[20px] bg-[#f0f9eb] rounded-[20px] text-[#67c23a] text-center"
          >
            已出仓
          </div>
          <div
            v-else-if="row?.business_status_name"
            class="w-[60px] h-[20px] bg-[#fef0f0] rounded-[20px] text-[#f56c6c] text-center"
          >
            {{ row.business_status_name }}
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'CashCommodityClothOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'CashCommodityClothOrder_edit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'CashCommodityClothOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'CashCommodityClothOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'CashCommodityClothOrder_change'"
              type="primary"
              :underline="false"
              @click="handChange(row)"
            >
              变更
            </el-link>
            <el-link
              v-if="
                (row.audit_status === 2 && row.business_status === 3)
                  || row.arranged_roll === 0
              "
              v-has="'CashCommodityClothOrder_deliver'"
              type="primary"
              :underline="false"
              @click="handDeliverFromGodown(row)"
            >
              确认出仓
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      title=""
      class="table-card-bottom"
    >
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #xima="{ row }">
          <el-link @click="finishProductionOptions.showXima(row)">
            查看
          </el-link>
        </template>
      </Table>
    </FildCard>
  </div>
  <FineSizeSelectStockDetail ref="FineSizeCashCommodityClothDetailRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
