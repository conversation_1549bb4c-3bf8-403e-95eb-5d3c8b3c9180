import { useRequest } from '@/use/useRequest'

export const getTreeEnumList = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/getTreeEnumList',
    method: 'get',
  })
}

const getTypeRawMaterialListEnum = () => {
  return useRequest({
    url: '/admin/v1/basic_data/typeRawMaterial/getTypeRawMaterialListEnum',
    method: 'get',
  })
}

// 供应商枚举
export const BusinessUnitSupplierEnumlist = () => {
  return useRequest({
    url: '/admin/v1/business_unit/supplier/enum_list',
    // query: { unit_type_id: 12 },
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 往来单位枚举
export const GetBusinessUnitListApi = () => {
  return useRequest({
    url: '/admin/v1/business_unit/list',
    method: 'get',
  })
}

export default {
  getTreeEnumList,
  getTypeRawMaterialListEnum,
  BusinessUnitSupplierEnumlist,
  GetBusinessUnitListApi,
}
