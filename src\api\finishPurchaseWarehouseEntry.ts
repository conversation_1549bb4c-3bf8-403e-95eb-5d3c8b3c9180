import { useRequest } from '@/use/useRequest'

// 获取列表
export function getFpmPrcInOrderList() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/getFpmPrcInOrderList',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 成品色号/颜色
export function GetFinishProductColorDropdownList() {
  return useRequest({
    url: '/admin/v1/product/finishProductColor/getFinishProductColorDropdownList',
    method: 'get',
    pagination: true,
  })
}

// 作废
export function updateFpmPrcInOrderStatusCancel() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusCancel',
    method: 'put',
  })
}
// 审核
export function updateFpmPrcInOrderStatusPass() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusPass',
    method: 'put',
  })
}
// 驳回
export function updateFpmPrcInOrderStatusReject() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusReject',
    method: 'put',
  })
}
// 消审
export function updateFpmPrcInOrderStatusWait() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusWait',
    method: 'put',
  })
}

// 整单关闭 PUT admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderBusinessClose
export function updateFpmPrcInOrderBusinessClose() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderBusinessClose',
    method: 'put',
  })
}

// 成品等级
export function GetInfoBaseFinishedProductLevelEnumList() {
  return useRequest({
    url: '/admin/v1/info_basic_data/infoBaseFinishedProductLevel/getInfoBaseFinishedProductLevelEnumList',
    method: 'get',
  })
}

// 仓库
export function GetPhysicalWarehouseDropdownList() {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouse/getPhysicalWarehouseDropdownList',
    method: 'get',
  })
}

// 成品资料
export function GetFinishProductDropdownList() {
  return useRequest({
    url: '/admin/v1/product/finishProduct/getFinishProductDropdownList',
    method: 'get',
  })
}
// 布种类型
export function GetTypeFabricEnumList() {
  return useRequest({
    url: '/admin/v1/basic_data/typeFabric/getTypeFabricEnumList',
    method: 'get',
  })
}

// 仓位枚举
export function GetPhysicalWarehouseBinListEnum() {
  return useRequest({
    url: '/admin/v1/warehouse/physicalWarehouseBin/getPhysicalWarehouseBinListEnum',
    method: 'get',
  })
}

// 新建
export function addFpmPrcInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/addFpmPrcInOrder',
    method: 'post',
  })
}

// 获取详情
export function getFpmPrcInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/getFpmPrcInOrder',
    method: 'get',
  })
}

// 编辑
export function updateFpmPrcInOrder() {
  return useRequest({
    url: '/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrder',
    method: 'put',
  })
}
// 获取成品采购
export function getPurchaseOrderList() {
  return useRequest({
    url: '/admin/v1/purchase_order/finish_product/item_list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 获取最大卷号
export function getMaxVolumeNumber() {
  return useRequest({
    url: '/admin/v1/product/stockProduct/getMaxVolumeNumber',
    method: 'get',
  })
}
