import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 导出数据
export const PurchaseReceiveOrderListDownLoad = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/raw_material/purchase_return_order/list',
    method: 'get',
    nameFile,
  })
}

// 原料采购退货单列表
export const PurchaseReturnOrderList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}
// 原料采购退货单添加
export const PurchaseReturnOrderaDdd = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order',
    method: 'post',
  })
}
// 原料采购退货单添加
export const PurchaseReturnOrderaEdit = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order',
    method: 'put',
  })
}

// 原料采购退货单详情
export const PurchaseReturnOrderDetail = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order/detail',
    method: 'get',
  })
}

// 原料采购退货单审核
export const PurchaseReturnOrderPass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order/pass',
    method: 'put',
  })
}

// 原料采购退货单驳回
export const PurchaseReturnOrderReject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order/reject',
    method: 'put',
  })
}

// 原料采购退货单作废
export const PurchaseReturnOrderVoid = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order/void',
    method: 'put',
  })
}

// 原料采购退货单消审
export const PurchaseReturnOrderCancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/purchase_return_order/cancel',
    method: 'put',
  })
}
