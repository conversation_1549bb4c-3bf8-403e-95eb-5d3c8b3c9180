<script lang="ts" setup name="DyeingFactoryTable">
import { ref } from 'vue'
import Summarize from './components/Summarize.vue'
import Details from './components/Details.vue'
import CylinderNumber from './components/CylinderNumber.vue'
// import FineCode from './components/FineCode.vue'

import FildCard from '@/components/FildCard.vue'

const activeName = ref('SummarizeRef') // 当前选中的tab

const SummarizeRef = ref() // 汇总
const DetailsRef = ref() // 明细
const CylinderNumberRef = ref() // 缸号
// const FineCodeRef = ref() // 细码
</script>

<template>
  <FildCard title="" :tool-bar="false" class="h-full flex flex-col">
    <el-tabs v-model="activeName" default-active="SummarizeRef" class="demo-tabs flex-1 flex flex-col overflow-y-hidden">
      <!-- 成品布种汇总 -->
      <el-tab-pane label="成品布种汇总" name="SummarizeRef">
        <Summarize v-if="activeName === 'SummarizeRef'" ref="SummarizeRef" />
      </el-tab-pane>
      <!-- 成品颜色汇总 -->
      <el-tab-pane label="成品颜色汇总" name="DetailsRef">
        <Details v-if="activeName === 'DetailsRef'" ref="DetailsRef" />
      </el-tab-pane>
      <!-- 成品缸号汇总 -->
      <el-tab-pane label="成品缸号汇总" name="CylinderNumberRef">
        <CylinderNumber v-if="activeName === 'CylinderNumberRef'" ref="CylinderNumberRef" />
      </el-tab-pane>
      <!-- 成品细码报表 -->
      <!-- <el-tab-pane label="成品细码报表" name="FineCodeRef">
        <FineCode v-if="activeName === 'FineCodeRef'" ref="FineCodeRef" />
      </el-tab-pane> -->
    </el-tabs>
  </FildCard>
</template>

<style lang="scss">
.demo-tabs {
  .el-tabs__header {
    margin-bottom: 0;
  }

  .el-tabs__content {
    flex:1;
    overflow-y: hidden
  }

  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
</style>
