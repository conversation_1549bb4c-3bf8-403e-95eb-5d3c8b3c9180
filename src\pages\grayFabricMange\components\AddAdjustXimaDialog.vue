<!-- 细码调整 坯布库存调整单 -->
<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { getGfmWarehouseList } from '@/api/greyFabricPurchaseReturn'
import { deepClone, filterDataList, getFilterData } from '@/common/util'
import { formatPriceDiv, formatWeightDiv, sumNum, sumTotal } from '@/common/format'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { DictionaryType, WarehouseTypeIdEnum } from '@/common/enum'

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true,
  },
})
const emits = defineEmits(['handleSure'])
const state = reactive<any>({
  filterData: {},
  info: {},
  showModal: false,
  modalName: '细码录入',
  multipleSelection: [],
  ximaList: [],
  canEnter: 0,
  rowIndex: -1,
  total_roll: 0,
  gfm_sale_delivery_item_id: '',
})

const { fetchData, data, total, loading, page, size } = getGfmWarehouseList()

watch(
  () => state.showModal,
  () => {
    if (state.showModal)
      getData()
  },
)

onMounted(() => {
  getData()
})

// watch(
//   () => state.filterData,
//   () => {
//     getData()
//   },
//   {
//     deep: true,
//   }
// )

const tableConfig = reactive<any>({
  showSlotNums: true,
  page,
  size,
  loading,
  total,
  showCheckBox: props.isEdit,
  showSort: false,
  showOperate: props.isEdit,
  height: '100%',
  backSelection: true,
  cellDBLClickEvent: (val: any) => handleSelectionChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  footerMethod: (val: any) => FooterMethod1(val),
})

const tableConfig_xima = reactive<any>({
  showSlotNums: true,
  showOperate: props.isEdit,
  operateWidth: 80,
  height: '100%',
  footerMethod: (val: any) => FooterMethod(val),
})

function FooterMethod1({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['num'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, 'num') as any)}`

      if (['weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, 'weight') as any)} kg`

      return null
    }),
  ]
}

function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'

      if (['roll'].includes(column.property)) {
        state.total_roll = sumTotal(data, 'roll')
        return `${sumNum(data, 'roll')}`
      }
      if (['weight'].includes(column.property))
        return `${sumNum(data, 'weight')} kg`

      return null
    }),
  ]
}

async function getData() {
  await fetchData(getFilterData(state.filterData))
  data.value.list?.map((item: any) => {
    item.selected = state.ximaList?.some((citem: { id: any }) => citem.id === item.id) ?? false
  })
}

const columnList = ref([
  // {
  //   field: 'grey_fabric_code',
  //   title: '坯布编号',
  // },
  // {
  //   field: 'grey_fabric_name',
  //   title: '来源类型',
  // },
  // {
  //   field: 'source_code',
  //   title: '来源单号',
  // },
  {
    field: 'num',
    title: '匹数',
    isPrice: true,
  },
  props.isEdit
    ? {
        field: 'has_selected',
        title: '已调整匹数',
      }
    : null,
  {
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
  },
  {
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    // isWeight: true,
  },
  {
    field: 'volume_number',
    title: '卷号',
  },
  {
    field: 'warehouse_bin_Name',
    title: '仓位',
  },
  {
    field: 'weight',
    title: '数量',
    isWeight: true,
  },
  {
    field: 'fabric_piece_code',
    title: '条码',
  },
])

const columnList_xima = reactive([
  {
    field: 'roll',
    title: '匹数',
    minWidth: 100,
    soltName: props.isEdit ? 'horsepower' : '',
  },
  {
    field: 'position',
    title: '仓位',
    minWidth: 100,
    soltName: props.isEdit ? 'position' : '',
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    soltName: 'grey_fabric_width',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重',
    soltName: 'grey_fabric_gram_weight',
    minWidth: 100,
  },
  {
    field: 'weight',
    title: '数量',
    minWidth: 100,
    soltName: 'weight',
  },
  {
    field: 'fabric_piece_code',
    title: '条码',
    minWidth: 100,
  },
])

function handAllSelect({ checked, selectCheck }: any) {
  data.value.list?.map((item: any) => {
    if (!checked) {
      item.selected = false
      item.has_selected = ''
      return item
    }
    else {
      item.has_selected = formatPriceDiv(item.num)
      item.selected = true
      return item
    }
  })
  state.ximaList = deepClone(selectCheck)
  for (let i = 0; i < state.ximaList.length; i++) {
    state.ximaList[i].weight = Number(formatWeightDiv(state.ximaList[i].weight))
    state.ximaList[i].roll = Number(formatPriceDiv(state.ximaList[i].num))
    state.ximaList[i].grey_fabric_width_unit_id = data.value.list[i].grey_fabric_width_unit_id
    state.ximaList[i].grey_fabric_gram_weight_unit_id = data.value.list[i].grey_fabric_gram_weight_unit_id
  }
}

function handleSelectionChange({ checked, row }: any) {
  data.value.list?.map((item: any) => {
    if (item.id === row.id) {
      if (!checked) {
        item.selected = false
        item.has_selected = ''
      }
      else {
        item.has_selected = formatPriceDiv(item.num)
        item.selected = true
      }
      return item
    }
  })

  let filterList = []
  filterList = state.ximaList.filter((item: any) => {
    return item.id === row.id
  })

  if (!filterList.length) {
    state.ximaList.push({
      id: row.id,
      position: row?.warehouse_bin_Name,
      warehouse_bin_id: row?.warehouse_bin_id,
      volume_number: row?.volume_number,
      roll: formatPriceDiv(row?.num),
      weight: formatWeightDiv(row?.weight),
      grey_fabric_width: row.grey_fabric_width,
      grey_fabric_gram_weight: row.grey_fabric_gram_weight,
      grey_fabric_gram_weight_unit_name: row.grey_fabric_gram_weight_unit_name,
      grey_fabric_width_unit_name: row.grey_fabric_width_unit_name,
      grey_fabric_width_unit_id: row.grey_fabric_width_unit_id,
      grey_fabric_gram_weight_unit_id: row.grey_fabric_gram_weight_unit_id,
      fabric_piece_code: row.fabric_piece_code,
    })
  }
  else {
    state.ximaList = state.ximaList.filter((item: any) => {
      return item.id !== row.id
    })
  }

  //   state.ximaList = deepClone(selectCheck)

  //   for (let i = 0; i < state.ximaList.length; i++) {
  //     state.ximaList[i].weight = Number(formatWeightDiv(state.ximaList[i].weight))
  //     state.ximaList[i].roll = state.ximaList[i].num
  //   }
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (state.total_roll > state.canEnter)
    return ElMessage.error('匹数总和不可超过当前分录行填写的匹数')

  // TODO:判断已录入的匹数有没有超过上面表格可录入的匹数

  // TODO:首先将细码列表的数据去重得到对应存在的ids

  const idList: any = Array.from(new Set(state.ximaList.map((item: any) => item.id))).map(id => ({ id }))

  // TODO:根据id取出来获取录入数据汇总的匹数和数量
  for (let i = 0; i < idList?.length; i++) {
    let filterArr = []
    filterArr = state.ximaList.filter((item: any) => {
      return item.id === idList[i].id
    })
    idList[i].total_roll = sumTotal(filterArr, 'roll')
    idList[i].total_weight = sumTotal(filterArr, 'weight')
  }

  // TODO:判断录入的匹数是否大于表格最大的匹数
  for (let i = 0; i < data.value.list.length; i++) {
    const matchingObject = idList.find((obj: any) => obj.id === data.value.list[i].id)
    if (matchingObject && matchingObject.total_roll > Number(formatPriceDiv(data.value.list[i].num)))
      return ElMessage.error('不可大于录入大于坯布信息的匹数')

    if (matchingObject && matchingObject.total_weight > Number(formatWeightDiv(data.value.list[i]?.weight)))
      return ElMessage.error('不可大于录入大于坯布信息的总数量')
  }

  emits('handleSure', state)
}

const tableRef = ref()

watch(
  () => state.ximaList,
  () => {
    if (state.ximaList.length > 0) {
      nextTick(() => {
        tableRef.value.tableRef?.updateFooter()
      })
    }
  },
  { deep: true },
)

function handDelete(row: any, rowIndex: number) {
  state.ximaList.splice(rowIndex, 1)
  //  TODO:找出当前数据对应上面表格的索引
  const idIndex = data.value.list?.findIndex((item: any) => {
    return item.id === row.id
  })
  // TODO:将已选的数据减掉
  data.value.list[idIndex].has_selected = Number(data.value.list[idIndex].has_selected) - Number(row.roll)
}

const countNums = ref(0)

// 点击分开录入
function handEntry(row: any) {
  countNums.value = 0
  data.value.list?.map((item: any) => {
    if (row.id === item.id) {
      item.visible = true
      return item
    }
    else {
      item.visible = false
    }
  })
}

function handCancelWrite(row: any) {
  data.value.list?.map((item: any) => {
    if (row.id === item.id) {
      item.visible = false
      return item
    }
  })
}
function handSureWrite(row: any) {
  if (countNums.value === 0 || countNums.value === '')
    return ElMessage.error('调整匹数不可为0或者为空')

  if (Number(countNums.value) > Number(formatPriceDiv(row.num))) {
    //  TODO:首先判断录入的数字是否超过当前总的匹数
    return ElMessage.error('不允许超过总匹数')
  }
  // TODO:判断当前数据是否已选择进去
  if (row.selected) {
    // TODO:将删除原本是否存在的id数据
    state.ximaList = filterDataList('id', row.id, state?.ximaList)
  }
  for (let i = 0; i < countNums.value; i++) {
    state.ximaList.push({
      id: row.id,
      position: row?.warehouse_bin_Name,
      warehouse_bin_id: row?.warehouse_bin_id,
      volume_number: row?.volume_number,
      fabric_piece_code: row.fabric_piece_code,
      roll: '1.00',
      weight: 0,
      grey_fabric_width: row.grey_fabric_width,
      grey_fabric_gram_weight: row.grey_fabric_gram_weight,
    })
  }
  data.value.list?.map((item: any) => {
    if (item.id === row.id) {
      item.has_selected = Number(countNums.value)
      item.visible = false
      // item.visible = false
      return item
    }
  })
}

watch(
  () => state.ximaList,
  () => {
    data.value.list?.map((item: any) => {
      item.selected = state.ximaList?.some((citem: { id: any }) => citem.id === item.id) ?? false
      return item
    })

    if (!state.ximaList.length) {
      data.value.list?.map((item: any) => {
        item.has_selected = ''
        return item
      })
    }
    else {
      const idList: any = Array.from(new Set(state.ximaList.map((item: any) => item.id))).map(id => ({ id }))

      // TODO:根据id取出来获取录入数据汇总的匹数和数量
      for (let i = 0; i < idList?.length; i++) {
        let filterArr = []
        filterArr = state.ximaList.filter((item: any) => {
          return item.id === idList[i].id
        })
        idList[i].total_roll = sumTotal(filterArr, 'roll')
        idList[i].total_weight = sumTotal(filterArr, 'weight')
      }

      // TODO:判断录入的匹数是否大于表格最大的匹数
      for (let i = 0; i < data.value.list.length; i++) {
        const matchingObject = idList.find((obj: any) => obj.id === data?.value?.list[i].id)
        if (matchingObject)
          data.value.list[i].has_selected = matchingObject.total_roll
      }
    }
  },
  {
    deep: true,
  },
)

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" :show-footer="props.isEdit" :title="state.modalName" width="1500" height="900" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-row justify-evenly h-full">
      <div class="w-[50%] flex flex-col">
        <div class="my-2">
          坯布库存明细<span class="text-slate-300 text-xs">（双击可快速选择）</span>
        </div>

        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="坯布编号:">
            <template #content>
              {{ state.info.grey_fabric_code }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="坯布名称:">
            <template #content>
              {{ state.info.grey_fabric_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="供方名称:">
            <template #content>
              {{ state.info.supplier_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="机台号:">
            <template #content>
              {{ state.info.machine_number }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="所属客户:">
            <template #content>
              {{ state.info.customer_name }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纱批:">
            <template #content>
              {{ state.info.yarn_batch }}
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="织坯颜色:">
            <template #content>
              {{ state.info.gray_fabric_color_name }}
            </template>
          </DescriptionsFormItem>
        </div>
        <div class="flex-1">
          <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
            <template #operate="{ row, rowIndex }">
              <el-popover :visible="row.visible" placement="top" :width="250">
                <div class="flex items-center">
                  <p>选择</p>
                  <vxe-input v-model="countNums" style="width: 100px; margin-left: 10px; margin-right: 10px" clearable type="float" :min="0" />
                  <p>分开调整</p>
                </div>
                <div style="text-align: right; margin: 0">
                  <el-button size="small" text @click="handCancelWrite(row)">
                    取消
                  </el-button>
                  <el-button size="small" type="primary" @click="handSureWrite(row)">
                    确定
                  </el-button>
                </div>
                <template #reference>
                  <el-button type="primary" text link @click="handEntry(row, rowIndex)">
                    分开调整
                  </el-button>
                </template>
              </el-popover>
            </template>
          </Table>
        </div>
      </div>
      <el-divider direction="vertical" class="h-full" />
      <div class="w-[50%] flex flex-col">
        <div class="my-2">
          已选（{{ state?.ximaList.length || 0 }}）
        </div>
        <div class="flex-1">
          <Table ref="tableRef" :config="tableConfig_xima" :table-list="state?.ximaList" :column-list="columnList_xima">
            <template #horsepower="{ row }">
              <template v-if="props.isEdit">
                <vxe-input v-model="row.roll" type="float" placeholder="必填" />
              </template>
              <template v-else>
                {{ row.roll }}
              </template>
            </template>
            <template #weight="{ row }">
              <template v-if="props.isEdit">
                <vxe-input v-model="row.weight" :min="0" type="float" placeholder="必填">
                  <template #suffix>
                    Kg
                  </template>
                </vxe-input>
              </template>
              <template v-else>
                {{ row.weight }} Kg
              </template>
            </template>

            <template #grey_fabric_width="{ row }">
              <template v-if="props.isEdit">
                <el-input v-model="row.grey_fabric_width" clearable placeholder="坯布幅宽">
                  <template #append>
                    <SelectComponents
                      v-model="row.grey_fabric_width_unit_id"
                      placeholder="单位"
                      style="width: 80px"
                      :query="{ dictionary_id: DictionaryType.width_unit }"
                      api="GetDictionaryDetailEnumListApi"
                      label-field="name"
                      value-field="id"
                      clearable
                    />
                  </template>
                </el-input>
              </template>
              <template v-else>
                {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
              </template>
            </template>

            <template #grey_fabric_gram_weight="{ row }">
              <template v-if="props.isEdit">
                <el-input v-model="row.grey_fabric_gram_weight" clearable placeholder="坯布克重">
                  <template #append>
                    <SelectComponents
                      v-model="row.grey_fabric_gram_weight_unit_id"
                      placeholder="单位"
                      style="width: 80px"
                      :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
                      api="GetDictionaryDetailEnumListApi"
                      label-field="name"
                      value-field="id"
                      clearable
                    />
                  </template>
                </el-input>
              </template>
              <template v-else>
                {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
              </template>
            </template>

            <template #position="{ row }">
              <SelectComponents
                v-model="row.warehouse_bin_id"
                api="GetDictionaryDetailEnumListApi"
                label-field="name"
                value-field="id"
                :query="{ dictionary_id: WarehouseTypeIdEnum.commonWarehouseBin }"
                clearable
              />
            </template>

            <template #operate="{ row, rowIndex }">
              <el-button text type="danger" @click="handDelete(row, rowIndex)">
                删除
              </el-button>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style></style>
