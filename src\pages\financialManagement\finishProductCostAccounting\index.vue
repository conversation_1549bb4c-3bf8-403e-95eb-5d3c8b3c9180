<script setup lang="ts" name="FinishProductCostAccounting">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { formatDate, formatHashTag, formatPriceDiv, formatUnitPriceDiv, formatWeightDiv, sumNum } from '@/common/format'
import { debounce, deepClone, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import {
  getCostCalculationDetail,
  getCostCalculationFpmList,
  getCostCalculationFpmListExport,
} from '@/api/inventory/finishedProduct/finishedProductReport'
import BottonExcel from '@/components/BottonExcel/index.vue'
import { useListExport } from '@/hooks/useListExport'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import StatusTag from '@/components/StatusTag/index.vue'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'

const defaultFilterData = {
  devierDate: '',
  product_id: '',
  product_name: '',
  color_name: '',
  color_id: '',
  status: '',
  dyelot_number: '',
  payable_order_type: [],
  cost_price: [],
  customer_id: '',
}
const state = reactive({
  filterData: JSON.parse(JSON.stringify(defaultFilterData)),
})

const { handleExport, loadingExcel } = useListExport()
function getQuery() {
  const obj = deepClone(state.filterData)

  const query: any = {
    start_time: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[0]) : '',
    end_time: state.filterData.devierDate && state.filterData.devierDate !== '' && state.filterData.devierDate.length ? formatDate(state.filterData.devierDate[1]) : '',
    ...state.filterData,
    status: obj.status,
    payable_order_type: obj.payable_order_type.join(','),
    cost_price: obj.cost_price.length === 0 ? 0 : obj.cost_price[0],
  }
  delete query.devierDate
  return getFilterData(query)
}
function handleChangeCostPrice(value) {
  state.filterData.cost_price = [value[value.length - 1]]
}
const { fetchData, data, success, msg, loading, page, size, total, handleSizeChange, handleCurrentChange } = getCostCalculationFpmList()
const { fetchData: getDetail, data: detailList, success: detailSuccess, msg: detailMsg } = getCostCalculationDetail()
// 获取数据
async function getData() {
  await fetchData(getQuery())
  if (!success.value)
    return ElMessage.error(msg.value)
}
const route = useRoute()

onMounted(async () => {
  state.filterData = JSON.parse(JSON.stringify(defaultFilterData))
  // 接收路由参数并应用到筛选条件
  if (route.query.product_id) {
    state.filterData.product_id = route.query.product_id as string
    state.filterData.product_name = route.query.product_name as string
  }

  if (route.query.color_id) {
    state.filterData.color_id = route.query.color_id as string
    state.filterData.color_name = route.query.color_name as string
  }

  if (route.query.dyelot_number)
    state.filterData.dyelot_number = route.query.dyelot_number as string

  await getData()
  cellClickEvent({ row: data.value.list[0] })
})
function FooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'
      if (['grey_fabric_roll', 'roll', 'paper_tube_total_price', 'purchase_roll', 'grey_fabric_total_price', 'daf_total_price', 'other_price', 'price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, column.property) as any)}`

      if (['purchase_weight', 'grey_fabric_weight', 'weight'].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property) as any)}`
      return null
    }),
  ]
}
const isFinishProduct = ref(false)
// const showDrawer = ref(false)
const firstList = ref([])
const secondList = ref([])
async function cellClickEvent({ row }: any) {
  await getDetail({
    payable_item_id: row.id,
  })
  if (!detailSuccess.value)
    return ElMessage.error(detailMsg.value)

  // showDrawer.value = true
  isFinishProduct.value = row.payable_order_type === 5
  if (isFinishProduct.value) {
    firstList.value = detailList.value.fpm_details
  }
  else {
    firstList.value = detailList.value.gfm_details
    secondList.value = detailList.value.rm_details
  }
}
const tableConfig = ref({
  loading,
  scrollY: {
    enabled: true,
  },
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  rowColor: {
    purchase: 'purchase',
    grey_fabric: 'grey_fabric',
    dyeing: 'dyeing',
    tube: 'tube',
    tape: 'tape',
  },
  height: '100%',
  showSort: false,
  fieldApiKey: 'finishProductCostAccounting',
  cellClick: (val: any) => cellClickEvent(val),
  footerMethod: (val: any) => FooterMethod(val),
})

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

// function handReset() {
//   state.filterData = resetData(state.filterData)
// }

const columnList = ref([
  {
    sortable: true,
    field: 'customer_name',
    title: '所属客户',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'product',
    title: '成品名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'color',
    title: '色号颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'src_order_type_name',
    title: '单据类型',
    soltName: 'src_order_type_name',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'payable_order_no',
    soltName: 'payable_order_no',
    title: '单据单号',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'pay_date',
    title: '单据日期',
    minWidth: 120,
    is_date: true,
  },
  {
    sortable: true,
    field: 'dye_factory_color_code',
    title: '染厂色号',
    width: 100,
  },
  {
    sortable: true,
    field: 'craft',
    title: '加工项目',
    minWidth: 140,
  },
  {
    field: 'paper_tube_weight',
    title: '纸筒',
    width: 100,
    sortable: true,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'weight_error',
    title: '空差',
    minWidth: 140,
    isWeight: true,
  },
  {
    field: 'purchase',
    title: '采购',
    width: 100,
    sortable: true,
    childrenList: [
      {
        sortable: true,
        field: 'purchase_price',
        title: '采购单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        sortable: true,
        field: 'purchase_roll',
        title: '结算匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'purchase_weight',
        title: '结算数量',
        isWeight: true,
        minWidth: 100,
      },
    ],
  },
  {
    sortable: true,
    field: 'grey_fabric',
    title: '坯布',
    minWidth: 140,
    childrenList: [
      {
        sortable: true,
        field: 'grey_fabric_roll',
        title: '匹数',
        isPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_weight',
        isWeight: true,
        title: '数量',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'grey_fabric_total_price',
        title: '坯布成本',
        isPrice: true,
        minWidth: 100,
      },
    ],
  },
  {
    sortable: true,
    field: 'dyeing',
    title: '染整',
    childrenList: [
      {
        sortable: true,
        field: 'daf_price',
        title: '加工单价',
        minWidth: 100,
        isUnitPrice: true,
      },
      {
        sortable: true,
        field: 'daf_weight',
        title: '计产数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'daf_total_price',
        title: '加工成本',
        isPrice: true,
        minWidth: 100,
      },
    ],
  },
  {
    sortable: true,
    field: 'tube',
    title: '纸筒',
    childrenList: [
      {
        sortable: true,
        field: 'paper_tube_price',
        title: '单价',
        isUnitPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'paper_tube_total_price',
        title: '金额',
        isPrice: true,
        minWidth: 100,
      },
    ],
  },
  {
    sortable: true,
    field: 'tape',
    title: '胶带',
    childrenList: [
      {
        sortable: true,
        field: 'plastic_bag_unit_price',
        title: '单价',
        isUnitPrice: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'plastic_bag_total_price',
        title: '金额',
        isPrice: true,
        minWidth: 100,
      },
    ],
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '其他',
    childrenList: [
      {
        sortable: true,
        field: 'remark',
        title: '备注',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'other_price',
        title: '其他金额',
        isPrice: true,
        minWidth: 100,
      },
    ],
  },
  {
    sortable: true,
    field: 'audit_status',
    title: '成品',
    fixed: 'right',
    childrenList: [
      {
        sortable: true,
        field: 'roll',
        title: '成品匹数',
        minWidth: 100,
        isPrice: true,
      },
      {
        sortable: true,
        field: 'weight',
        title: '成品数量',
        minWidth: 100,
        isWeight: true,
      },
      {
        sortable: true,
        field: 'price',
        isPrice: true,
        title: '成品成本',
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'buoyant_weight_price',
        title: '毛重单价',
        isWeight: true,
        minWidth: 100,
      },
      {
        sortable: true,
        field: 'net_weight_price',
        title: '净重单价',
        isWeight: true,
        minWidth: 100,
      },
      {
        sortable: true,
        showOrder_status: true,
        field: 'status',
        soltName: 'status',
        title: '状态',
        minWidth: 100,
      },
    ],
  },
])

const router = useRouter()

function selectCustomerValueChange(val: any) {
  state.customer_name = val.name
  state.filterData.customer_id = val.id
}
function handDetail(row: any) {
  if (row.payable_order_type === 1) {
    router.push({
      name: 'DyeingPayDetail',
      query: { id: row.payable_order_id },
    })
  }
  else {
    router.push({
      name: 'FinishPayDetail',
      query: { id: row.payable_order_id },
    })
  }
}

function secondFooterMethod({ columns, data }: any) {
  return [
    columns.map((column: any, _columnIndex: number) => {
      if ([0].includes(_columnIndex))
        return '汇总'
      if (['gfm_roll', 'gfm_other_price', 'gfm_price', 'fpm_roll', 'fpm_other_price', 'fpm_price', 'fpm_buoyant_weight_price', 'rm_buoyant_weight_price'].includes(column.property))
        return `${formatPriceDiv(sumNum(data, column.property) as any)}`

      if ([
        'gfm_weight',
        'fpm_weight',
        'rm_weight',
        'gfm_produce_weight',
        'gfm_buoyant_weight_price',
      ].includes(column.property))
        return `${formatWeightDiv(sumNum(data, column.property) as any)}`
      return null
    }),
  ]
}
const tableConfig_twince = ref({
  height: '100%',
  footerMethod: (val: any) => secondFooterMethod(val),
})
const greyFabricColumnList = ref([
  {
    sortable: true,
    field: 'daf_order_no',
    soltName: 'daf_order_no',
    title: '染整单号',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'src_order_no',
    title: '出坯单号',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'src_order_time',
    title: '出坯日期',
    is_date: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'produce_order_no',
    soltName: 'produce_order_no',
    title: '生产单号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gfm_product',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gfm_color',
    title: '颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'supplier_name',
    title: '织厂',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'raw_material_yarn_name',
    title: '纱名',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'machine_number',
    title: '机台号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_level_name',
    title: '等级',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gfm_remark',
    title: '出坯备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gfm_roll',
    isPrice: true,
    title: '匹数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gfm_weight',
    isWeight: true,
    title: '数量',
    minWidth: 100,
  },
  // {
  //   sortable: true,
  //   field: 'process_single_unit_price',
  //   isUnitPrice: true,
  //   title: '加工单价',
  //   minWidth: 100,
  // },
  // {
  //   sortable: true,
  //   field: 'gfm_unit_price',
  //   soltName: 'gfm_unit_price',
  //   title: '采购单价',
  //   isUnitPrice: true,
  //   minWidth: 100,
  // },
  // {
  //   sortable: true,
  //   field: 'gfm_other_price',
  //   soltName: 'gfm_other_price',
  //   isPrice: true,
  //   title: '其他金额',
  //   minWidth: 100,
  // },
  {
    sortable: true,
    field: 'gfm_buoyant_weight_price',
    title: '毛重单价',
    isWeight: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'gfm_price',
    isPrice: true,
    title: '成本金额',
    minWidth: 100,
  },
])
const finishProductColumnList = ref([
  {
    sortable: true,
    field: 'fpm_purchase_order_no',
    soltName: 'fpm_purchase_order_no',
    title: '成品采购单',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'fpm_product',
    title: '成品名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_color',
    title: '色号颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dyelot_number',
    title: '缸号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_color_code',
    title: '染厂色号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'product_level_name',
    title: '成品等级',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_width_and_weight',
    title: '幅宽克重',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_paper_tube_weight',
    isWeight: true,
    title: '纸筒',
    minWidth: 100,
  },
  {
    sortable: true,
    isWeight: true,
    field: 'fpm_weight_error',
    title: '空差',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_roll',
    isPrice: true,
    title: '匹数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_weight',
    isWeight: true,
    title: '数量',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_unit_price',
    soltName: 'fpm_unit_price',
    isUnitPrice: true,
    title: '采购单价',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_other_price',
    soltName: 'fpm_other_price',
    isPrice: true,
    title: '其他金额',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_price',
    soltName: 'fpm_price',
    isPrice: true,
    title: '金额',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fpm_buoyant_weight_price',
    isWeight: true,
    title: '毛重单价',
    minWidth: 100,
  },
])
const rawMaterialColumnList = ref([
  {
    sortable: true,
    field: 'rm_product',
    title: '原料名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'raw_material_batch_brand',
    title: '原料品牌',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'raw_material_batch_num',
    title: '原料批号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'rm_color',
    title: '原料颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'material_ratio',
    isPrice: true,
    soltName: 'material_ratio',
    title: '用料比例',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'material_loss',
    soltName: 'material_loss',
    isPrice: true,
    title: '用料损耗',
    minWidth: 100,
  },
  // {
  //   sortable: true,
  //   field: 'rm_unit_price',
  //   soltName: 'rm_unit_price',
  //   title: '原料单价',
  //   minWidth: 100,
  // },
  // {
  //   sortable: true,
  //   field: 'rm_weight',
  //   isWeight: true,
  //   title: '用料数量',
  //   minWidth: 100,
  // },
  // {
  //   sortable: true,
  //   field: 'gfm_produce_order_no',
  //   title: '出坯单号',
  //   minWidth: 140,
  // },
  // {
  //   sortable: true,
  //   field: 'gfm_produce_weight',
  //   isWeight: true,
  //   title: '坯布数量',
  //   minWidth: 100,
  // },
  // {
  //   sortable: true,
  //   field: 'process_unit_price',
  //   isUnitPrice: true,
  //   title: '加工单价',
  //   minWidth: 100,
  // },
  {
    sortable: true,
    field: 'rm_buoyant_weight_price',
    isWeight: true,
    title: '毛重单价',
    minWidth: 100,
  },
])

const exportTableName = computed(() => {
  if (!state.filterData.devierDate)
    return '成本核算'

  return `${dayjs(state.filterData.devierDate[0]).format('YYYYMMDD')}-${dayjs(state.filterData.devierDate[1]).format('YYYYMMDD')}_成本核算`
})
function handleClickExcel() {
  if (!state.filterData.devierDate)
    return ElMessage.error('请先选择时间')
  handleExport({
    tableList: data.value?.list,
    apiRequest: getCostCalculationFpmListExport,
    query: getQuery(),
    tableName: exportTableName.value,
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="应付日期">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="成品名称" width="330">
          <template #content>
            <SelectMergeComponent
              v-model="state.filterData.product_id"
              :query="{
                finish_product_name: state.filterData.product_name,
              }"
              :custom-label="(row:any) => `${formatHashTag(row.finish_product_code, row.finish_product_name)}`"
              :multiple="false"
              api-name="GetFinishProductDropdownList"
              remote
              remote-key="finish_product_code_or_name"
              remote-show-suffix
              placeholder="成品编号、成品名称"
              value-field="id"
              label-field="name"
              :default-label="{
                label: state.filterData.product_name,
                finish_product_code: '',
                finish_product_name: state.filterData.product_name,
                value: state.filterData.product_id,
              }"
              @clear="state.filterData.product_name = ''"
              @change="state.filterData.color_id = ''"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色号颜色">
          <template #content>
            <SelectMergeComponent
              v-model="state.filterData.color_id"
              :custom-label="(row:any) => `${formatHashTag(row.product_color_code, row.product_color_name)}`"
              :multiple="false"
              :disabled="!state.filterData.product_id"
              :default-label="{
                label: state.filterData.color_name,
                value: state.filterData.color_id,
                product_color_code: '',
                product_color_name: state.filterData.color_name,
              }"
              :query="{
                finish_product_id: state.filterData.product_id,
              }"
              api-name="GetFinishProductColorDropdownList"
              remote
              remote-key="product_color_code_or_name"
              remote-show-suffix
              placeholder="成品颜色、色号"
              value-field="id"
              label-field="name"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="所属客户">
          <template #content>
            <SelectCustomerDialog
              v-model="state.filterData.customer_id"
              @change-value="selectCustomerValueChange"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="订单状态">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              :exclude="[4]"
              :multiple="false"
              style="width: 200px"
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸号:">
          <template #content>
            <vxe-input v-model="state.filterData.dyelot_number" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="" copies="2">
          <template #content>
            <el-space :size="30">
              <el-checkbox-group v-model="state.filterData.payable_order_type">
                <el-checkbox :value="5" border>
                  成品采购
                </el-checkbox>
                <el-checkbox :value="1" border>
                  染整加工
                </el-checkbox>
              </el-checkbox-group>
              <el-checkbox-group :model-value="state.filterData.cost_price" @change="handleChangeCostPrice">
                <el-checkbox :value="1" border>
                  成本单价为0
                </el-checkbox>
                <el-checkbox :value="2" border>
                  成本单价>0
                </el-checkbox>
              </el-checkbox-group>
            </el-space>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <BottonExcel
              :loading="loadingExcel"
              title="导出文件"
              @on-click-excel="handleClickExcel"
            />
            <!--            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info"> -->
            <!--              清除条件 -->
            <!--            </el-button> -->
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <Table :config="tableConfig" :table-list="data?.list" :column-list="columnList">
        <template #payable_order_no="{ row }">
          <el-link type="primary" :underline="false" @click.stop="handDetail(row)">
            {{ row.payable_order_no }}
          </el-link>
        </template>
        <template #src_order_type_name="{ row }">
          <div class="flex items-center">
            <div class="bg-[#3399ff] text-white p-1 rounded mr-2 text-xs" :class="row.payable_order_type === 1 ? '!bg-gray-100 !text-[#333]' : null">
              {{ row.payable_order_type === 1 ? '染' : row.payable_order_type === 5 ? '采' : '' }}
            </div>
            <div>{{ row.src_order_type_name }}</div>
          </div>
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
      </Table>
    </FildCard>
    <div
      class="table-card-bottom"
    >
      <el-row justify="space-evenly" class="h-full">
        <el-col :span="isFinishProduct ? 24 : 12" class="h-full">
          <FildCard :title="isFinishProduct ? '成品采购' : '坯布成品'" class="flex flex-col h-full" :tool-bar="false">
            <Table
              :config="tableConfig_twince"
              :table-list="firstList"
              :column-list="isFinishProduct ? finishProductColumnList : greyFabricColumnList"
            >
              <template #fpm_unit_price="{ row }">
                ￥{{ formatUnitPriceDiv(row.fpm_unit_price) }}
              </template>
              <template #fpm_other_price="{ row }">
                ￥{{ formatPriceDiv(row.fpm_other_price) }}
              </template>
              <template #daf_order_no="{ row }">
                <el-link
                  type="primary" :underline="false" @click="router.push({
                    name: 'DyeingNoticeDetail',
                    query: { id: row.daf_order_id },
                  })"
                >
                  {{ row.daf_order_no }}
                </el-link>
              </template>
              <template #fpm_purchase_order_no="{ row }">
                <el-link
                  type="primary" :underline="false" @click="router.push({
                    name: 'FinishPurchaseWarehouseEntryDetail',
                    query: {
                      id: row.fpm_purchase_order_id,
                    },
                  })"
                >
                  {{ row.fpm_purchase_order_no }}
                </el-link>
              </template>
              <template #produce_order_no="{ row }">
                <el-link
                  type="primary" :underline="false" @click="router.push({
                    name: 'ProductionNoticeDetail',
                    query: { id: row.produce_order_id },
                  })"
                >
                  {{ row.produce_order_no }}
                </el-link>
              </template>
              <template #fpm_price="{ row }">
                ￥{{ formatPriceDiv(row.fpm_price) }}
              </template>
              <template #gfm_unit_price="{ row }">
                ￥{{ formatUnitPriceDiv(row.gfm_unit_price) }}
              </template>
              <template #gfm_other_price="{ row }">
                ￥{{ formatPriceDiv(row.gfm_other_price) }}
              </template>
            </Table>
          </FildCard>
        </el-col>
        <el-col v-if="!isFinishProduct" :span="12" class="h-full">
          <FildCard title="原料" class="flex flex-col h-full" :tool-bar="false">
            <Table
              :config="tableConfig_twince"
              :table-list="secondList"
              :column-list="rawMaterialColumnList"
            >
              <template #material_ratio="{ row }">
                {{ formatPriceDiv(row.material_ratio) }}%
              </template>
              <template #material_loss="{ row }">
                {{ formatPriceDiv(row.material_loss) }}%
              </template>
              <template #rm_unit_price="{ row }">
                ￥{{ formatUnitPriceDiv(row.rm_unit_price) }}
              </template>
            </Table>
          </FildCard>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.purchase){
  background: #efdcfd;
}
:deep(.grey_fabric){
  background: #a5daf1;
}
:deep(.dyeing){
  background: #b7d4e0;
}
:deep(.tube){
  background: #a4e699;
}
:deep(.tape){
  background: #f1cbe7
}
</style>
