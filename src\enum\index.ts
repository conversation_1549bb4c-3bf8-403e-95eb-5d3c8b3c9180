/**
 * 出仓单类型枚举
 */
export enum WarehouseGoodOutTypeEnum {
  // 内部调拨出仓单
  InternalAllocate = 1,
  // 销售调拨出仓单
  SaleAllocate = 2,
  // 销售出仓单
  Sale = 3,
  // 采购退货出仓单
  PurchaseReturn = 4,
  // 加工出仓单
  Process = 5,
  // 其他出仓单
  Other = 6,
  // 扣款出仓单
  Deduction = 7,
  // 回修出仓单
  TypeRepair = 8,
  // 盘点出仓单
  Check = 9,
  // 调整出仓单
  Adjust = 10,
}

// 销售类型
export enum SaleTypeEnum {
  // 成品销售
  FinishProduct = 1,
  // 坯布销售
  GreyFabric,
  // 原料销售
  RawMaterial,
}

// 启用状态
export enum ActiveStatusEnum {
  Enable = 1, // 启用
  Disable = 2, // 禁用
  NoActive = 3, // 未激活
}

// 设置habit的type（常用功能）
export enum HabitKeyEnum {
  yibuCommon = 3, // 易布-常用功能
  PcCommon = 4, // pc-常用功能
}

// 调货销售单用到的：大货还是散剪
export enum SplitTypeEnum {
  Bulk = 1, // 大货
  Plate = 2, // 散剪
}

// 出仓类型
export enum OutTypeEnum {
  Shipment = 1, // 出货
  SaleTransfer = 2, // 销调
}
export enum DnfType {
  GreyDnf = 1, // 坯布dnf
  ProductProcessing = 2, // 成品加工
  ProductRepair = 3, // 成品回修
}
// 染整收费方式
export enum DnfChargingMethod {
  DnfWeight = 1, // 坯布数量
  Length = 2, // 长度
  finishProductWeight = 3, // 成品数量
}
