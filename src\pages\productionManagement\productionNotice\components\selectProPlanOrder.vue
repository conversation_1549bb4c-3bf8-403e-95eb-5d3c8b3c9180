<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Table from '@/components/Table.vue'
import { getProductionPlanOrder } from '@/api/productionNotice'
import { formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  showModal: false,
  modalName: '选择生产计划单',
  multipleSelection: [],
  tableData: [],
})
const selectList = ref<any[]>()

const {
  fetchData: fetchData1,
  data: tableData,
  total: total1,
  loading: loading1,
  page: page1,
  size: size1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getProductionPlanOrder()

watch(
  () => state.showModal,
  () => {
    if (state.showModal) {
      selectList.value = []
      getData()
    }
  },
)

const tableConfig = reactive<any>({
  loading: loading1,
  showPagition: true,
  showSlotNums: false,
  page: page1,
  size: size1,
  total: total1,
  showCheckBox: true,
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange1(val),
  handleCurrentChange: (val: number) => handleCurrentChange1(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})

async function getData() {
  await fetchData1()
  tableConfig.total = total1
  tableConfig.page = page1
  tableConfig.size = size1
}

watch(
  () => tableData.value,
  () => {
    state.tableData
      = tableData.value?.list?.map((item: any) => {
        return {
          ...item,
          plan_roll: formatTwoDecimalsDiv(item.plan_roll),
          plan_weight: formatWeightDiv(item.plan_weight),
          weight_of_fabric: formatWeightDiv(Number(item.weight_of_fabric)),
          process_price: formatUnitPriceDiv(Number(item.process_price)),
        }
      }) || []
  },
  { deep: true },
)

const columnList = ref([
  {
    field: 'order_no',
    width: 150,
    title: '生产计划单号',
  },
  {
    field: 'weaving_mill_name',
    width: 100,
    title: '织厂名称',
  },
  {
    field: 'grey_fabric_code',
    width: 100,
    title: '坯布编号',
  },
  {
    field: 'grey_fabric_name',
    width: 100,
    title: '坯布名称',
  },
  {
    field: 'customer_name',
    width: 100,
    title: '所属客户',
  },
  {
    field: 'grey_fabric_width',
    width: 100,
    title: '坯布幅宽',
  },
  {
    field: 'grey_fabric_gram_weight',
    width: 100,
    title: '坯布克重',
  },
  {
    field: 'grey_fabric_color_name',
    width: 100,
    title: '织坯颜色',
  },
  {
    field: 'plan_roll',
    width: 100,
    title: '计划匹数',
  },
  {
    field: 'plan_weight',
    width: 100,
    title: '计划数量',
  },
  {
    field: 'remark',
    width: 100,
    title: '备注',
  },
  {
    field: 'create_time',
    width: 150,
    title: '创建时间',
    isDate: true,
  },
])

function handAllSelect({ records }: any) {
  // 单选
  if (records.length > 1) {
    records.forEach((item: any, index: number) => {
      item.selected = index === 0
    })
    selectList.value = records.filter((v: any) => v.selected)
    return ElMessage.error('只能选中一种')
  }
  selectList.value = records
}

function handleSelectionChange({ records, row }: any) {
  // 单选
  if (records.length > 1) {
    const index = records.findIndex((item: any) => item.id === row.id)
    records.forEach((item: any, i: number) => {
      item.selected = index === i
    })
    selectList.value = records.filter((v: any) => v.selected)
    return ElMessage.error('只能选中一种')
  }
  selectList.value = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  emits('handleSure', selectList.value.length ? selectList.value[0] : undefined)
  //   if (!selectList.value?.length) {
  //     return ElMessage.error('请选择一条数据')
  //   } else {
  //     // return

  //   }
  handCancel()
}

defineExpose({
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="600" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <Table :config="tableConfig" :table-list="state.tableData" :column-list="columnList">
      <template #grey_fabric_gram_weight="{ row }">
        <div>{{ row.grey_fabric_gram_weight }}g</div>
      </template>
    </Table>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>
