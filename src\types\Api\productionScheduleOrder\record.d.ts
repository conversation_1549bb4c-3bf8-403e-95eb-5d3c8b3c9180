declare namespace Api.GetFineCodePrintRecordList {
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * page
     */
    page?: number
    /**
     * 打印结束时间
     */
    print_end_time?: string
    /**
     * 打印开始时间
     */
    print_start_time?: string
    /**
     * 打印人ID
     */
    printer_id?: number
    /**
     * 打印人姓名
     */
    printer_name?: string
    /**
     * 生产排产单细码ID
     */
    production_schedule_order_fine_code_id?: number
    /**
     * size
     */
    size?: number
    [property: string]: any
  }
  /**
   * system.ListResponse
   */
  export interface Response {
    /**
     * 响应码
     */
    code?: number
    data?: Data
    /**
     * 响应信息
     */
    msg?: string
    /**
     * 版本号
     */
    version?: string
    list?: ProduceGetProductionScheduleOrderFineCodePrintRecordListData[]
    [property: string]: any
  }

  export interface Data {
    list?: any
    summary?: any
    total?: number
    [property: string]: any
  }

  /**
   * produce.GetProductionScheduleOrderFineCodePrintRecordListData
   */
  export interface ProduceGetProductionScheduleOrderFineCodePrintRecordListData {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 打印记录ID
     */
    id?: number
    /**
     * 打印数量
     */
    print_count?: number
    /**
     * 打印时间
     */
    print_time?: string
    /**
     * 打印人ID
     */
    printer_id?: number
    /**
     * 打印人姓名
     */
    printer_name?: string
    /**
     * 生产排产单细码ID
     */
    production_schedule_order_fine_code_id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
