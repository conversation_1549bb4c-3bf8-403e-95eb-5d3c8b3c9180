<script lang="ts" setup name="AdjustmentNotice">
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { formatCraft } from '../common/utils'
import OrderMore from './components/OrderMore.vue'
import {
  redye_orderlcancel,
  redye_orderldetail,
  redye_orderlist,
  redye_orderlistExoprt,
  redye_orderlpass,
} from '@/api/dyeingManagement'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceDiv, formatWeightDiv } from '@/common/format'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  getRecentDay_Date,
  resetData,
} from '@/common/util'
import BottonExcel from '@/components/BottonExcel/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import type { TableColumn } from '@/components/Table/type'

const state = reactive<any>({
  filterData: {
    order_no: '',
    status: '',
    dnf_type: '',
    sale_system_id: '',
    dye_factory_id: '',
    creator_id: '',
    auditor_id: '',
    create_time: '',
    audit_time: '',
    src_dye_factory_id: '',
    is_charge: '',
  },
  multipleSelection: [],
  information: false,
  isDyeing: true,
  moreItems: [],
})

const options = ref([
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
])

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = redye_orderlist()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.status.length)
    obj.status = obj.status.join(',')

  const query: any = {
    dnf_start_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[0])
        : '',
    dnf_end_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[1])
        : '',
    create_start_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    create_end_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    audit_start_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    audit_end_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
    status: obj.status,
  }
  delete query.audit_time
  delete query.create_time
  delete query.devierDate
  await ApiCustomerList(query)
  if (data.value?.list)
    getInfomation(data.value.list[0])
}, 400)
onMounted(() => {
  getData()
  state.filterData.create_time = getRecentDay_Date(1)
})

onActivated(() => {
  getData()
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  fieldApiKey: 'AdjustmentNotice',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '8%',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  //   handAllSelect: (val: any) => handAllSelect(val),
  //   handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref<TableColumn[]>([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    width: '9%',
    soltName: 'link',
  },
  // {
  //   field: 'dnf_type_name',
  //   title: '通知单号',
  //   minWidth: 200,
  //   soltName: 'link',
  // },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'src_dye_factory_name',
    title: '来源染厂',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_name',
    title: '染厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_follower_name',
    title: '染厂跟单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_follower_phone',
    title: '跟单电话',
    minWidth: 100,
  },
  { sortable: true, field: 'dnf_date', title: '染整日期', minWidth: 100 },
  { sortable: true, field: 'return_address', title: '回货地址', minWidth: 100 },
  {
    sortable: true,
    field: 'is_charge',
    title: '是否收费',
    minWidth: 100,
    soltName: 'is_charge',
  },
  { sortable: true, field: 'remark', title: '单据备注', minWidth: 120 },
  {
    sortable: true,
    field: 'piece_count',
    title: '匹数',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'weight',
    title: '总数量',
    minWidth: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'change_roll',
    title: '变更匹数',
    minWidth: '5%',
    soltName: 'change_roll',
  },
  {
    sortable: true,
    field: 'change_weight',
    title: '变更数量',
    minWidth: '5%',
    soltName: 'change_weight',
  },
  {
    sortable: true,
    field: 'rtn_piece_count',
    title: '已返匹数',
    minWidth: '5%',
    isPrice: true,
  },
  {
    sortable: true,
    field: 'rtn_weight',
    title: '已返数量',
    minWidth: '5%',
    isWeight: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 120,
    isDate: true,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    sortable: true,
    minWidth: 120,
    isDate: true,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: '',
    title: '单据状态',
    fixed: 'right',
    soltName: 'status',
    showOrder_status: true,
    width: '5%',
  },
])

const router = useRouter()

function handleAdd() {
  router.push({
    name: 'AdjustmentNoticeAdd',
  })
}

function handDetail(row: any) {
  router.push({
    name: 'AdjustmentNoticeDetail',
    query: { id: row.id },
  })
}

function handEdit(row: any) {
  router.push({
    name: 'AdjustmentNoticeEdit',
    query: { id: row.id },
  })
}

const { fetchData: getFetchDetail, data: fabricList } = redye_orderldetail()

const OrderMoreRef = ref()

// 获取坯布信息
async function getInfomation(row: any) {
  state.information = true
  await getFetchDetail({ id: row.id })

  nextTick(() => {
    if (fabricList.value) {
      fabricList.value.craft_requirement = formatCraft(fabricList.value.craft_requirement)

      const obj = deepClone(fabricList.value)

      state.moreItems = deepClone(fabricList.value.items)

      // 遍历数据数组
      state.moreItems?.forEach((item: any) => {
        if (item.final_src_type === 1) {
          // 判断final_src_type是否为1
          // 遍历use_fabric数组
          item.use_fabric.map((subItem: any, index: number) => {
            // 将对象摊平
            subItem.total_weight = subItem.weight
            subItem.stock_weight = subItem.gf_stock_info.weight
            Object.assign(subItem, subItem.gf_stock_info)
            // 删除原来的对象
            delete subItem.gf_stock_info
            // 更新数据数组
            item.use_fabric[index] = subItem
          })
        }
        else {
          // final_src_type不为1
          // 遍历use_fabric数组
          item.use_fabric.map((subItem: any, index: number) => {
            // 将对象摊平
            subItem.total_weight = subItem.weight
            subItem.out_weight = subItem.f_out_info.weight
            Object.assign(subItem, subItem.f_out_info)

            // 删除原来的对象
            delete subItem.f_out_info
            // 更新数据数组
            item.use_fabric[index] = subItem
          })
        }
      })

      OrderMoreRef.value.state.detail = obj
    }
  })
}

// 导出
const loadingExcel = ref(false)
async function handleExport() {
  if (!data?.value.list || data?.value.list.length <= 0)
    return ElMessage.warning('当前无数据可导出')
  const name_str = '染整通知单'
  const {
    fetchData: getFetch,
    success: getSuccess,
    msg: getMsg,
  } = redye_orderlistExoprt({ nameFile: name_str })
  loadingExcel.value = true
  await getFetch({
    ...getFilterData(state.filterData),
    download: 1,
  })
  if (getSuccess.value) {
    ElMessage({
      type: 'success',
      message: '成功',
    })
  }
  else {
    ElMessage({
      type: 'error',
      message: getMsg.value,
    })
  }
  loadingExcel.value = false
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = redye_orderlpass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = redye_orderlcancel()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <!-- <DescriptionsFormItem label="染整类型:">
        <template v-slot:content>
          <el-select clearable v-model="state.filterData.rework_type" class="m-2">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </template>
      </DescriptionsFormItem> -->
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="来源染厂:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.src_dye_factory_id"
              api="BusinessUnitSupplierEnumAll"
              :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂名称:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.dye_factory_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整日期:">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="是否收费:">
          <template #content>
            <el-select v-model="state.filterData.is_charge" clearable class="m-2">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.creator_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.auditor_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核时间:">
          <template #content>
            <SelectDate v-model="state.filterData.audit_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <template #right-top>
        <el-button
          v-has="'AdjustmentNoticeAdd'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新建
        </el-button>
        <BottonExcel
          v-has="'AdjustmentNoticeExport'"
          :loading="loadingExcel"
          title="导出文件"
          @on-click-excel="handleExport"
        />
      </template>
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="getInfomation(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #change_roll="{ row }">
          <div
            v-if="formatPriceDiv(row.change_piece_count) > 0"
            class="text-[#3fc191]"
          >
            +{{ formatPriceDiv(row.change_piece_count) }}
          </div>
          <div
            v-else-if="formatPriceDiv(row.change_piece_count) < 0"
            class="text-[#ed90bd]"
          >
            -{{ formatPriceDiv(row.change_piece_count) }}
          </div>
          <div v-else-if="row.change_piece_count === 0">
            {{ row.change_piece_count }}
          </div>
        </template>
        <template #change_weight="{ row }">
          <div
            v-if="formatWeightDiv(row.change_weight) > 0"
            class="text-[#3fc191]"
          >
            +{{ formatWeightDiv(row.change_weight) }}
          </div>
          <div
            v-else-if="formatWeightDiv(row.change_weight) < 0"
            class="text-[#ed90bd]"
          >
            -{{ formatWeightDiv(row.change_weight) }}
          </div>
          <div v-else-if="row.change_weight === 0">
            {{ row.change_weight }}
          </div>
        </template>
        <template #is_charge="{ row }">
          {{ row.is_charge ? "是" : "否" }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'AdjustmentNoticeDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="'AdjustmentNoticeEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="'AdjustmentNoticeAudit'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="'AdjustmentNoticeCancel'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard class="table-card-bottom" :tool-bar="false">
      <OrderMore
        ref="OrderMoreRef"
        v-model:table-list="state.moreItems"
        class="h-full"
        table-height="100%"
      />
    </FildCard>
  </div>
</template>

<style></style>
