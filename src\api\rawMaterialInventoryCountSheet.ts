import { useDownLoad } from '@/use/useDownLoad'
import { useRequest } from '@/use/useRequest'

// 原料库存盘点单列表
export const StockCheckOrderList = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
  })
}

// 原料库存盘点单详情
export const StockCheckOrderDetail = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order/detail',
    method: 'get',
  })
}

// 原料库存盘点单添加
export const StockCheckOrderAdd = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order',
    method: 'post',
  })
}

// 原料库存盘点单修改
export const StockCheckOrderEdit = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order',
    method: 'put',
  })
}

// 原料库存盘点单审核
export const StockCheckOrderPass = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order/pass',
    method: 'put',
  })
}

// 原料库存盘点单驳回
export const StockCheckOrderReject = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order/reject',
    method: 'put',
  })
}
// 原料库存盘点单作废
export const StockCheckOrderVoid = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order/void',
    method: 'put',
  })
}
// 原料库存盘点单消审
export const StockCheckOrderCancel = () => {
  return useRequest({
    url: '/admin/v1/raw_material/stock_check_order/cancel',
    method: 'put',
  })
}

// 导出数据
export const StockCheckOrderListDownLoad = ({ nameFile }: any) => {
  return useDownLoad({
    url: '/admin/v1/raw_material/stock_check_order/list',
    method: 'get',
    nameFile,
  })
}
