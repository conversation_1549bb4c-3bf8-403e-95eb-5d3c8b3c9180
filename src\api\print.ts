import { useRequest } from "@/use/useRequest";

// 获取列表
export const GetPrintTemplateList = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/getPrintTemplateList",
    method: "get",
    pagination: true,
    pageSize: 50,
  });
};

// 打印类型枚举
export const PrintTemplateType = () => {
  return useRequest({
    url: "/admin/v1/enum/printTemplateType",
    method: "get",
  });
};

// 添加打印列表
export const AddPrintTemplate = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/addPrintTemplate",
    method: "post",
  });
};

// 修改打印
export const UpdatePrintTemplate = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/updatePrintTemplate",
    method: "put",
  });
};

// 删除打印模板
export const DeletePrintTemplate = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/deletePrintTemplate",
    method: "delete",
  });
};

// 修改模板状态
export const UpdatePrintTemplateStatus = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/updatePrintTemplateStatus",
    method: "put",
  });
};

// 根据id获取模板信息
export const GetPrintTemplate = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/getPrintTemplate",
    method: "get",
  });
};

// 根据id获取模板字段
export const GetPrintTemplateColumn = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/getPrintTemplateColumn",
    method: "get",
  });
};

// 根据id复制模板
export const CopyPrintTemplate = () => {
  return useRequest({
    url: "/admin/v1/printTemplate/copyPrintTemplate",
    method: "post",
  });
};
// 获取数据类型枚举列表(原料坯布成品)
export const GetDataType = () => {
  return useRequest({
    url: "/admin/v1/enum/dataType",
    method: "get",
  });
};
