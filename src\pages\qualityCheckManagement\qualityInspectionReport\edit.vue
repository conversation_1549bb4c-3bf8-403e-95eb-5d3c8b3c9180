<script setup lang="ts" name="QualityInspectionReportEdit">
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import currency from 'currency.js'
import { ColumnList, ModalColumnList } from './pullData'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import InputSelect from '@/components/InputSelect/index.vue'
import { DictionaryType, EmployeeType } from '@/common/enum'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { GetListByDyelotNumber, GetqualityInspectionReport, PutFpmQualityCheckoutReport } from '@/api/qualityInspectionReport'
import { processDataIn, processDataOut } from '@/common/handBinary'
import TextureMapWall from '@/components/TextureMapWall/index.vue'
import { vueEffect } from '@/use/vueEffect'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import { formatHashTag } from '@/common/format'

const route = useRoute() // 实例化路由
const router = useRouter() // 实例化路由
const { fetchData: fetchDataList, loading: loadingList } = GetqualityInspectionReport()

// 弹窗获取信息的请求参数
const temPayload = ref<any>({
  dyelot_number: '', // 缸号
  supplier_id: '', // 来源供应商id
  product_id: '', // 成品id
  product_color_id: '', // 颜色id
  type: 1, // 质检状态
})
// 弹窗的成品喝颜色临时数据
const componentRemoteSearch = ref({
  finish_product_code: '',
  finish_product_name: '',
  color_name: '',
  color_code: '',
})
// 提交的所有信息
const payloadData = ref<any>({
  dyelot_number: '', // 缸号
  supplier_id: '', // 来源供应商id
  supplier_name: '', // 来源供应商名称
  qc_technical_director_id: '', // QC/技术主管
  quality_check_date: new Date(), // 质检日期
  yarn_count: '', // 纱支
  needle_size: '', // 针寸数
  density: '', // 密度
  customer_name: '', // 客户名称
  customer_id: '', // 客户id
  dye_factory_name: '', // 染厂
  weaving_mill_name: '', // 织厂
  yarn_batch_number: '', // 纱批号
  finish_product_code: '', // 成品编号
  finish_product_name: '', // 成品名称
  product_color_code: '', // 色号
  product_color_name: '', // 颜色
  gram_weight: '', // 克重
  warehouse_entry_order_no: '', // 进仓单号
  dyelot_weight: 0, // 缸数量
  edge_width: '', // 包边门幅
  sample_weight: 0, // 抽检数量
  sample_proportion: '', // 抽检比例
  item_list: [], // 成品信息
  hand_feel_name: '', // 手感
  hand_feel_id: 0, // 手感 id
  shrinkage_rate_id: 0, // 缩水率id
  shrinkage_rate: '', // 缩水率名称
  product_level_id: '', // 产品等级
  color_fastness: '', // 色牢度
  smell: '', //  是否有异味
  strength_horizontal: '', // 强力横
  strength_vertical: '', // 强力竖
  hair_effect_id: 0, // 抓毛效果id
  hair_effect_name: '', // 抓毛效果 名称
  fabric_tilt_id: 0, // 布纹斜度 id
  fabric_tilt_name: '', // 布纹斜度名称
  color_light_id: 0, // 对色光源id colorLight
  color_light_name: '', // 对色光源名称
  positive_and_negative_id: 0, // 阴阳id positiveAndNegative
  positive_and_negative_name: '', // 阴阳name positiveAndNegative
  water_absorption: '', // 吸水性能
  batch_difference_id: 0, // 疋差 id
  batch_difference_name: '', // 疋差 名称
  hair_head_id: 0, // 布面毛头id
  hair_head_name: '', // 布面毛头名称
  scouring_effect_id: 0, // 磨毛效果id`
  scouring_effect_name: '', // 磨毛效果name
  qc_conclusion: '', // QC结论
  qc_opinion: '', // QC/技术主管意见
  sales_opinion: '', // 销售意见
  remark: '', // 备注
  attachments_url: [], // 上传的图片

})

// 获取页面所有信息
async function getDatas() {
  const result: any = await fetchDataList({ id: route.query.id })
  const { shrinkage_rate, ...data } = result.data
  const newData = processDataOut(data)
  payloadData.value = {
    ...newData,
    shrinkage_rate: shrinkage_rate || '',
    // 将fileList 这个字符串转换成数组
    attachments_url: newData.attachments_url && newData.attachments_url.length ? newData.attachments_url.split(',') : [],
  }
  temPayload.value.dyelot_number = newData.dyelot_number // 绑定缸号
  temPayload.value.supplier_id = newData.supplier_id // 绑定来源供应商
  temPayload.value.product_id = newData.product_id // 绑定成品id
  temPayload.value.product_color_id = newData.product_color_id // 绑定颜色id

  componentRemoteSearch.value.finish_product_code = newData.product_code
  componentRemoteSearch.value.finish_product_name = newData.product_name
  componentRemoteSearch.value.color_code = newData.product_color_code
  componentRemoteSearch.value.color_name = newData.product_color_name
}

onMounted(async () => {
  await getDatas()
})

// 监听页面
watch(() => route.path, async (newPath, _) => {
  if (newPath === '/qualityCheckManagement/qualityInspectionReportEdit')
    await getDatas()
}, { deep: true })

// table配置
const tableConfig = computed(() => ({
  fieldApiKey: 'qualityInspectionReportEdit',
  loading: false,
  showPagition: true,
  total: payloadData.value?.item_data?.length || 0,
  showOperate: false,
  operateWidth: '220',
  showSort: false,
  filterStatus: false,
}))

const showModal = ref(false) // #region 是否展示详细筛选

const { fetchData: getDyelotNumber, data: dyelotNumberData, loading: gBNloading, total: gbTotal, page: gbPage, size: gbSize, handleSizeChange, handleCurrentChange } = GetListByDyelotNumber()

const modalTableConfig = ref<any>({
  loading: gBNloading,
  showPagition: true,
  showRadio: true,
  showSlotNums: false,
  page: gbPage,
  size: gbSize,
  total: gbTotal,
  showSort: false,
  height: '100%',
  filterStatus: false,
  handleSizeChange,
  handleCurrentChange,
  radioChangeEvent: handleSelectionChange,
})

// 获取缸号和来源供应商的信息
async function getDyelotNumberData() {
  await getDyelotNumber({ page: 1, size: 50, ...temPayload.value })
}

// 点击缸号或者来源供应商弹出窗体
async function showSelectProPlanOrder() {
  showModal.value = true
  await getDyelotNumberData()
}
// 关闭弹出窗体
function handleCancelModal() {
  showModal.value = false
}

const temData = ref<any>() // 选中的临时数据
// 关闭弹出窗体
function handleSureModal() {
  payloadData.value = { ...payloadData.value, ...temData.value }
  tableConfig.value.total = payloadData.value.item_list.length
  showModal.value = false
  temPayload.value.dyelot_number = payloadData.value.dyelot_number
  temPayload.value.supplier_id = payloadData.value.supplier_id
}

vueEffect(async () => {
  if (showModal.value)
    await getDyelotNumberData()
}, [temPayload, showModal], 350, true, 'debounce')

const gbList: any = computed(() => processDataOut(dyelotNumberData.value?.list || []))

// 弹窗选择数据
function handleSelectionChange({ row }: { row: any }) {
  temData.value = { ...payloadData.value, ...row }
  // payloadData.value = { ...payloadData.value, ...row }
  // tableConfig.value.total = payloadData.value.item_data.length
}

const { fetchData: setData } = PutFpmQualityCheckoutReport()
async function handleSure() {
  if (!payloadData.value.dyelot_number) {
    ElMessage.error('缸号不能为空！')
  }
  else if (!payloadData.value.quality_check_date) {
    ElMessage.error('质检日期不能为空！')
  }
  else {
    const query = {
      ...payloadData.value,
      strength_horizontal: payloadData.value.strength_horizontal,
      strength_vertical: payloadData.value.strength_vertical,
      attachments_url: payloadData.value.attachments_url.join(','), // 将图片转换成字符串提交
    }
    const result: any = await setData({
      ...processDataIn(query),
      shrinkage_rate: payloadData.value.shrinkage_rate,
      item_data: processDataIn(query).item_list,
      quality_check_date: dayjs(payloadData.value.quality_check_date).format('YYYY-MM-DD'),
    })
    if (result.success) {
      ElMessage.success('修改成功')
      setTimeout(() => {
        router.push({
          name: 'QualityInspectionReportDetail',
          query: { id: result.data.id },
        })
      }, 300)
    }

    else {
      ElMessage.error(result.msg || '新增失败')
    }
  }
}
// 选中成品编号或者成品名称
function changeProductSelect(val: any) {
  temPayload.value.product_id = val?.id || ''
  temPayload.value.product_color_id = '' // 清空颜色
  componentRemoteSearch.value.finish_product_code = val?.finish_product_code || ''
  componentRemoteSearch.value.finish_product_name = val?.finish_product_name || ''
  componentRemoteSearch.value.color_code = ''
  componentRemoteSearch.value.color_name = ''
}
// 选择颜色
function changeColor(val: any) {
  temPayload.value.product_color_id = val?.id || ''
  componentRemoteSearch.value.color_code = val?.product_color_code || ''
  componentRemoteSearch.value.color_name = val?.product_color_name || ''
}
// 选择是否已质检
function onChangeType(val: any) {
  if (Number(val) === 1) { // 已质检
    temPayload.value.stock_show_type = ''
  }
}
</script>

<template>
  <FildCard :tool-bar="false" title="基础信息" class="mb-[5px]">
    <template #right-top>
      <el-button v-btnAntiShake="handleSure" type="primary" :loading="loadingList">
        提交
      </el-button>
    </template>
    <slot>
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="缸号:" width="310" required>
          <template #content>
            <div @click="showSelectProPlanOrder">
              <vxe-input
                v-model="payloadData.dyelot_number"
                readonly
                placeholder="点击选择缸号"
              />
            </div>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="来源供应商:" width="310">
          <template #content>
            <div @click="showSelectProPlanOrder">
              <vxe-input
                v-model="payloadData.supplier_name"
                readonly
                placeholder="点击选择来源供应商"
              />
            </div>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="QC/质检主管:" width="310">
          <template #content>
            <SelectComponents
              v-model="payloadData.qc_technical_director_id"
              style="width: 300px" api="GetUserDropdownList"
              :query="{ duty: EmployeeType.qualityInspectionSupervisor }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="质检日期:" width="310" required>
          <template #content>
            <el-date-picker v-model="payloadData.quality_check_date" type="date" placeholder="请选择质检日期" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="纱支:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.yarn_count" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="针寸数:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.needle_size" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="密度:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.density" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.customer_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="染厂:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.dye_factory_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.weave_factory_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="纱批号:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.yarn_batch_number" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="成品名称:" width="310">
          <template #content>
            <vxe-input :model-value="formatHashTag(payloadData.product_code, payloadData.product_name)" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="颜色编号:" width="310">
          <template #content>
            <vxe-input :model-value="formatHashTag(payloadData.product_color_code, payloadData.product_color_name)" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.warehouse_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="克重:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.finish_product_gram_weight_and_unit_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进仓单号:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.warehouse_in_order_no" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缸匹数:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.total_roll" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="包边门幅:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.finish_product_width_and_unit_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="抽检匹数:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.inspect_total_roll" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="抽检比例:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.inspect_ratio" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="制单人:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.creator_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="制单时间:" width="310">
          <template #content>
            <!-- <vxe-input disabled v-model="payloadData.create_time" style="width: 100%" placeholder="" /> -->
            <el-date-picker v-model="payloadData.create_time" disabled type="date" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="修改人:" width="310">
          <template #content>
            <vxe-input v-model="payloadData.update_user_name" disabled style="width: 100%" placeholder="" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="修改时间:" width="310">
          <template #content>
            <el-date-picker v-model="payloadData.update_time" disabled type="date" />
          </template>
        </DescriptionsFormItem>
      </div>
    </slot>
  </FildCard>
  <FildCard :tool-bar="false" title="质检结果" class="mb-[5px]">
    <slot>
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="手感:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.hand_feel_id"
              v-model:key-value="payloadData.hand_feel_name"
              :query="{ dictionary_id: DictionaryType.handFeel }"
              placeholder="请选择或者输入手感"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="缩水率:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.shrinkage_rate_id"
              v-model:key-value="payloadData.shrinkage_rate"
              :query="{ dictionary_id: DictionaryType.shrink }"
              placeholder="请选择或者输入缩水率"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="产品等级:" width="310">
          <template #content>
            <SelectComponents
              v-model="payloadData.product_level_id"
              placeholder="请选择"
              api="GetInfoBaseFinishedProductLevelEnumList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="色牢度:" width="310">
          <template #content>
            <el-input v-model="payloadData.color_fastness" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="是否有异味:" width="310">
          <template #content>
            <!-- <el-input v-model="payloadData.smell" clearable /> -->
            <el-select
              v-model="payloadData.smell"
              placeholder=""
            >
              <el-option
                v-for="item in [{ value: '是', label: '是' }, { value: '否', label: '否' }]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="弹力横/直:" width="310">
          <template #content>
            <div style="display: flex;align-items: center;gap: 5px;">
              <el-input v-model="payloadData.strength_horizontal" clearable />
              <el-input v-model="payloadData.strength_vertical" clearable />
            </div>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="抓毛效果:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.hair_effect_id"
              v-model:key-value="payloadData.hair_effect_name"
              :query="{ dictionary_id: DictionaryType.hairEffect }"
              placeholder="请选择或者输入抓毛效果"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布纹斜度:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.fabric_tilt_id"
              v-model:key-value="payloadData.fabric_tilt_name"
              :query="{ dictionary_id: DictionaryType.fabricTilt }"
              placeholder="请选择或者输入布纹斜度"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="对色光源:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.color_light_id"
              v-model:key-value="payloadData.color_light_name"
              :query="{ dictionary_id: DictionaryType.colorLight }"
              placeholder="请选择或者输入对色光源"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="阴阳:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.positive_and_negative_id"
              v-model:key-value="payloadData.positive_and_negative_name"
              :query="{ dictionary_id: DictionaryType.positiveAndNegative }"
              placeholder="请选择或者输入阴阳"
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="吸水性能:" width="310">
          <template #content>
            <el-input v-model="payloadData.water_absorption" clearable />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="疋差:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.batch_difference_id"
              v-model:key-value="payloadData.batch_difference_name"
              :query="{ dictionary_id: DictionaryType.batchDifference }"
              placeholder="请选择或者输入疋差"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布面毛头:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.hair_head_id"
              v-model:key-value="payloadData.hair_head_name"
              :query="{ dictionary_id: DictionaryType.hairHead }"
              placeholder="请选择或者输入布面毛头"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="磨毛效果:" width="310">
          <template #content>
            <InputSelect
              v-model:keys="payloadData.scouring_effect_id"
              v-model:key-value="payloadData.scouring_effect_name"
              :query="{ dictionary_id: DictionaryType.scouringEffect }"
              :default-value="payloadData.scouring_effect_name"
              placeholder="请选择或者输入磨毛效果"
            />
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem label="QC结论:" copies="2">
          <template #content>
            <el-input
              v-model="payloadData.qc_conclusion"
              :autosize="{ minRows: 2, maxRows: 5 }"
              resize="none"
              maxlength="200"
              show-word-limit
              type="textarea" clearable
              placeholder="QC结论"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="QC/技术主管意见:" copies="2">
          <template #content>
            <el-input
              v-model="payloadData.qc_opinion"
              :autosize="{ minRows: 2, maxRows: 5 }"
              resize="none"
              maxlength="200"
              show-word-limit
              type="textarea" clearable
              placeholder="QC/技术主管意见"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="销售意见:" copies="2">
          <template #content>
            <el-input
              v-model="payloadData.sales_opinion"
              :autosize="{ minRows: 2, maxRows: 5 }"
              resize="none"
              maxlength="200"
              show-word-limit
              type="textarea" clearable
              placeholder="销售意见"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" copies="2">
          <template #content>
            <el-input
              v-model="payloadData.remark"
              :autosize="{ minRows: 2, maxRows: 5 }"
              resize="none"
              maxlength="200"
              show-word-limit
              type="textarea" clearable
              placeholder="备注"
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </slot>
  </FildCard>
  <FildCard :tool-bar="false" title="成品信息" class="mb-[5px]">
    <Table
      :config="tableConfig"
      :table-list="payloadData.item_data"
      :column-list="ColumnList"
    >
      <template #is_pass="{ row }">
        <span v-if="row.is_pass === false">不合格</span>
        <span v-else-if="row.is_pass === true">合格</span>
        <span v-else>-</span>
      </template>
    </Table>
  </FildCard>
  <FildCard title="附件" :tool-bar="false">
    <TextureMapWall v-model:image-list="payloadData.attachments_url" text="" />
  </FildCard>
  <vxe-modal
    v-model="showModal"
    destroy-on-close
    show-zoom
    resize
    show-footer
    title="从质检报表中查询"
    width="75vw"
    height="75vh"
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
  >
    <div class="h-full flex flex-col">
      <div style="display: flex;flex-wrap: wrap; gap: 15px;margin-bottom: 15px;">
        <!-- 缸号 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>缸号:</div>
          <div style="width: 200px;">
            <el-input v-model="temPayload.dyelot_number" clearable />
          </div>
        </div>
        <!-- 来源供应商 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>来源供应商:</div>
          <div style="width: 200px;">
            <SelectComponents
              v-model="temPayload.supplier_id"
              api="BusinessUnitSupplierEnumAll"
              :query="{ unit_type_id: '11,12,15,16,17,18' }"
              label-field="name"
              value-field="id"
              clearable
              @select="(val:any) => payloadData.supplier_name = val?.name"
            />
          </div>
        </div>
        <!-- 成品编号名称 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>成品名称:</div>
          <div style="width: 200px;">
            <SelectMergeComponent
              v-model="temPayload.product_id"
              :custom-label="(row:any) => `${formatHashTag(row.finish_product_code, row.finish_product_name)}`"
              :multiple="false"
              api-name="GetFinishProductDropdownList"
              remote
              remote-key="finish_product_code_or_name"
              remote-show-suffix
              placeholder="成品编号、成品名称"
              value-field="id"
              :default-value="formatHashTag(componentRemoteSearch.finish_product_code, componentRemoteSearch.finish_product_name)"
              @change="changeProductSelect"
            />
          </div>
        </div>
        <!-- 色号颜色 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>颜色编号:</div>
          <div style="width: 200px;">
            <SelectMergeComponent
              v-model="temPayload.product_color_id"
              :custom-label="(row:any) => `${formatHashTag(row.product_color_code, row.product_color_name)}`"
              :multiple="false"
              :query="{
                finish_product_id: temPayload.product_id,
              }"
              :disabled="!temPayload.product_id"
              api-name="GetFinishProductColorDropdownList"
              remote
              remote-key="product_color_code_or_name"
              remote-show-suffix
              placeholder="成品颜色、色号"
              value-field="id"
              :default-value="formatHashTag(componentRemoteSearch.color_code, componentRemoteSearch.color_name)"
              @change="changeColor"
            />
          </div>
        </div>
        <!-- 仓库 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>仓库名称:</div>
          <div style="width: 200px;">
            <SelectComponents v-model="temPayload.warehouse_id" api="GetPhysicalWarehouseDropdownList" warehouse_type_id="finishProduction" label-field="name" value-field="id" clearable />
          </div>
        </div>
        <!-- 显示方式 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <div>显示方式:</div>
          <div style="width: 200px;">
            <SelectComponents v-model="temPayload.stock_show_type" api="GetStockShowType" label-field="name" value-field="id" clearable :disabled="Number(temPayload.type) === 1" />
          </div>
        </div>

        <!-- 是否质检过 -->
        <div style="display: flex;align-items: center;gap: 10px;">
          <el-radio-group v-model="temPayload.type" class="ml-4" @change="onChangeType">
            <el-radio :label="1">
              仅看已质检的
            </el-radio>
            <el-radio :label="2">
              仅看未质检的
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="flex-1 overflow-hidden">
        <Table :config="modalTableConfig" :table-list="gbList" :column-list="ModalColumnList">
          <template #product_name="{ row }">
            {{ formatHashTag(row.product_code, row.product_name) }}
          </template>
          <template #product_color_code="{ row }">
            {{ formatHashTag(row.product_color_code, row.product_color_name) }}
          </template>
          <template #inspect_ratio="{ row }">
            <span>{{ row.inspect_ratio ? `${currency(row.inspect_ratio).multiply(100).value}%` : '' }}</span>
          </template>
        </Table>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleCancelModal">
        取消
      </el-button>
      <el-button type="primary" @click="handleSureModal">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style scoped lang="scss">
:deep(.vxe-input--inner[disabled]){
color: #cccccc;
}
</style>
