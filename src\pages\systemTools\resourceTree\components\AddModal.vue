<script setup lang="ts">
import { Minus, Plus } from '@element-plus/icons-vue'
import type { CascaderProps, FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { getFilterData } from '@/common/util'
import { AddResourceTreeApi, UpdateResourceTreeApi } from '@/api/resourceTree'

export interface Row {
  button_code: string
  type: number // 1目录， 2资源， 3按钮
  sub_resource_tree?: Row[]
  api_module: { method: string, url: string }[]
  resource_name: string
  parent_id: number
  sort: number
  status: number
  id: number
  router_name: string
  router_url: string
}
export interface Props {
  title: string
  modelValue: boolean
  type: string
  row: Row
  menuList: Row[]
}
const props = withDefaults(defineProps<Props>(), {
  title: '添加菜单',
  modelValue: false,
  type: 'add',
})
const emit = defineEmits(['update:modelValue', 'addSuccess', 'editSuccess'])
const state = reactive<{ form: Row }>({
  form: {
    sort: 0,
    status: 1,
    parent_id: 0,
    type: 1, // 1目录， 2资源， 3按钮
    id: 0,
    router_name: '',
    router_url: '',
    resource_name: '',
    api_module: [],
    button_code: '',
  },
})
const apiList = ref([{ method: 'GET', url: '' }])

const resourceList = computed(() => {
  return [{ id: 0, resource_name: '根目录' }, ...(props.menuList || [])]
})
const ruleFormRef = ref<FormInstance>()

const showModal = ref<boolean>(false)
watch(
  () => [props.modelValue],
  () => {
    showModal.value = props.modelValue
    state.form.parent_id = props.row.parent_id || 0

    if (props.modelValue) {
      if (props.type === 'edit') {
        state.form.sort = props.row.sort || 0
        state.form.status = props.row.status
        state.form.parent_id = props.row.parent_id || 0
        state.form.type = props.row.type || 1
        state.form.resource_name = props.row.resource_name || ''
        state.form.router_name = props.row.router_name || ''
        state.form.router_url = props.row.router_url || ''
        state.form.api_module = props.row.api_module || []
        state.form.id = props.row.id || 0
        state.form.button_code = props.row.button_code || ''
        apiList.value = props.row.api_module || []
        if (apiList.value.length <= 0)
          apiList.value = [{ method: 'GET', url: '' }]
      }
      else {
        state.form.type = props.row.type || 1
        if (props.row.type !== 1 && props.row.type)
          state.form.type = 3
      }
    }
    else {
      state.form.resource_name = ''
      state.form.sort = 0
      state.form.status = 1
      state.form.parent_id = 0
      state.form.router_name = ''
      state.form.router_url = ''
      state.form.api_module = []
      state.form.button_code = ''
      state.form.type = 0
      apiList.value = [{ method: 'GET', url: '' }]
    }
  },
)

const { fetchData: fetchDataAdd, success: successAdd, msg: msgAdd } = AddResourceTreeApi()
async function addData() {
  let parent_id = state.form.parent_id
  if (typeof state.form.parent_id === 'object')
    parent_id = state.form.parent_id[0]

  formatApi()
  await fetchDataAdd(getFilterData({ ...state.form, api_module: state.form.api_module.join(','), parent_id }))
  if (successAdd.value) {
    ElMessage.success('添加成功')
    emit('addSuccess')
    emit('update:modelValue', false)
  }
  else {
    ElMessage.error(msgAdd.value)
  }
}

// 更新菜单
const { fetchData: fetchDataUpdate, success: successUpdate, msg: msgUpdate } = UpdateResourceTreeApi()
async function updateData() {
  let parent_id = state.form.parent_id
  if (typeof state.form.parent_id === 'object')
    parent_id = state.form.parent_id[0]

  formatApi()
  await fetchDataUpdate(getFilterData({ ...state.form, parent_id, api_module: state.form.api_module.join(',') }))
  if (successUpdate.value) {
    ElMessage.success('编辑成功成功')
    emit('editSuccess')
    emit('update:modelValue', false)
  }
  else {
    ElMessage.error(msgUpdate.value)
  }
}

function onClose() {
  emit('update:modelValue', false)
}

function handCancel() {
  emit('update:modelValue', false)
}
const cascader = ref()
function handleSure() {
  ruleFormRef.value?.validate(async (valid: any) => {
    if (valid) {
      if (props.type === 'edit') {
        // 当是资源还有下级按钮时，不能修改为目录
        updateData()
      }
      else {
        addData()
      }
      // emit('update:modelValue', false)
    }
  })
}

const router = useRouter()

function validateRouterName(rule: any, value: any, callback: any) {
  if (state.form.type === 3 && !value)
    return callback()
  if (!router.hasRoute(value))
    callback(new Error('请填写正确的前端路由名称'))
  else
    callback()
}
const fromRulesResources = ref({
  resource_name: { rules: [{ required: true, message: '请输入资源名称', trigger: 'blur' }], type: [1, 2, 3] },
  router_name: { rules: [{ validator: validateRouterName, trigger: 'blur' }], type: [2, 3] },
  button_code: { rules: [{ required: true, message: '请输入按钮编号', trigger: 'blur' }], type: [3] },
})

const fromRules = computed(() => {
  ruleFormRef.value?.clearValidate()
  const rules: any = {}
  for (const [k, v] of Object.entries(fromRulesResources.value)) {
    if (v.type.includes(state.form.type))
      rules[k] = v.rules
  }
  return rules
})

const options = ref([
  { label: '禁用', value: 0 },
  { label: '启用', value: 1 },
])

const typeOptionsStatus = computed(() => {
  if (props.type === 'add') {
    if (props.row.type === 1) {
      if (!props.row.id) {
        return [{ label: '目录', value: 1 }]
      }
      else {
        return [
          { label: '目录', value: 1 },
          { label: '资源', value: 2 },
        ]
      }
    }
    else {
      return [{ label: '按钮', value: 3 }]
    }
  }
  else {
    if (props.row.type === 1 || props.row.type === 2) {
      // 当是目录下面已有资源不能更改为资源
      if (props.row.type === 1 && props.row.sub_resource_tree && props.row.sub_resource_tree[0].type === 2)
        return [{ label: '目录', value: 2 }]

      // 当是资源下面已有按钮不能更改为目录
      if (props.row.type === 2 && props.row.sub_resource_tree)
        return [{ label: '资源', value: 2 }]

      return [
        { label: '目录', value: 1 },
        { label: '资源', value: 2 },
      ]
    }
    else {
      return [{ label: '按钮', value: 3 }]
    }
  }
})

const methodType = ref([
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
])

function addKey(index: number) {
  apiList.value.splice(index + 1, 0, { method: 'GET', url: '' })
}
function delKey(index: number) {
  apiList.value.splice(index, 1)
}

function formatApi() {
  const list: any[] = []
  if (state.form.type === 1) {
    state.form.api_module = []
    return
  }
  apiList.value.map((item) => {
    if (item.url) {
      const url_str = item.url[0] === '/' ? item.url.slice(1).split('/') : item.url.split('/')
      url_str.push(item.method)
      list.push(url_str.join('.'))
    }
  })
  state.form.api_module = list
}

const cascaderProps: CascaderProps = {
  value: 'id',
  label: 'resource_name',
  checkStrictly: true,
  children: 'sub_resource_tree',
  // lazy: true,
  // lazyLoad(node, resolve) {
  //   const { data } = node
  //   const sub_resource_tree: any = data?.sub_resource_tree || []
  //   const children: any = sub_resource_tree?.length > 0 ? data?.sub_resource_tree : null
  //   if (children && children[0].type !== 3) {
  //     let data = children
  //     if (props.type === 'edit') {
  //       data = children.filter((item: Row) => item.id !== props.row.id)
  //     }
  //     resolve(data)
  //   } else {
  //     resolve()
  //   }
  // },
}
</script>

<template>
  <vxe-modal v-model="showModal" show-footer :title="props.title" width="800" height="auto" :mask="false" :lock-view="false" :esc-closable="true" resize @close="onClose">
    <el-form ref="ruleFormRef" size="default" :model="state.form" label-width="140px" label-position="left" :rules="fromRules">
      <el-form-item v-if="props.row.type !== 3" label="父级" prop="parent_id">
        <el-cascader ref="cascader" v-model="state.form.parent_id" :disabled="true" style="width: 300px" :props="cascaderProps" :options="resourceList">
          <template #default="{ data }">
            <span>{{ data.resource_name }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item v-if="props.row.type !== 3" label="类型" prop="type">
        <el-select v-model="state.form.type" style="width: 300px" placeholder="Select">
          <el-option v-for="item in typeOptionsStatus" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="资源名称" prop="resource_name">
        <el-input v-model.trim="state.form.resource_name" clearable placeholder="请输入资源名称" />
      </el-form-item>
      <el-form-item v-if="state.form.type !== 1" label="路由名称" prop="router_name">
        <el-input v-model.trim="state.form.router_name" clearable placeholder="请输入路由名称" />
      </el-form-item>
      <el-form-item v-if="state.form.type !== 1" label="路由路径" prop="router_url">
        <el-input v-model.trim="state.form.router_url" clearable placeholder="请输入路由路径" />
      </el-form-item>
      <el-form-item v-if="state.form.type === 3" label="按钮编号" prop="button_code">
        <el-input v-model.trim="state.form.button_code" clearable placeholder="请输入按钮编号" />
      </el-form-item>
      <template v-if="state.form.type !== 1">
        <el-form-item v-for="(citem, index) in apiList" :key="index" label="后端key" prop="api_module">
          <div class="flex">
            <el-select v-model="citem.method" placeholder="Select" style="width: 200px">
              <el-option v-for="item in methodType" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-input v-model="citem.url" size="small" placeholder="请输入后端key" />
            <el-button type="primary" :icon="Plus" @click="addKey(index)">
              添加
            </el-button>
            <el-button v-if="apiList.length > 1" type="warning" :icon="Minus" @click="delKey(index)">
              删除
            </el-button>
          </div>
        </el-form-item>
      </template>

      <el-form-item label="序号" prop="sort">
        <el-input v-model.number="state.form.sort" clearable placeholder="请输入序号" />
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <el-select v-model="state.form.status" style="width: 300px" placeholder="Select" clearable>
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
