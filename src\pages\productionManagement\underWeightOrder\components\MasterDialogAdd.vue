<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import { getPurchaseReceiveOrderList } from '@/api/underWeightOrder'
import { formatDate, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import { debounce, getFilterData, resetData } from '@/common/util'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import { BusinessUnitIdEnum } from '@/common/enum'
import SelectDialog from '@/components/SelectDialog/index.vue'

const emits = defineEmits(['handleSure'])

const state = reactive<any>({
  filterData: {
    order_num: '',
    supplier_id: '',
    raw_material_id: '',
  },
  query: {},
  showModal: false,
  modalName: '添加原料',
  multipleSelection: [],
  list: [],
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_code: '',
  raw_name: '',
  color_code: '',
  color_name: '',
})

const { fetchData, data: datalist, total, loading, page, size, handleSizeChange, handleCurrentChange } = getPurchaseReceiveOrderList()

const tableConfig = reactive<any>({
  loading,
  showPagition: true,
  showSlotNums: false,
  page,
  size,
  total,
  height: '100%',
  showCheckBox: true,
  showSort: false,
  handleSizeChange,
  handleCurrentChange,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
})
const receive_date = ref<any>([new Date().getTime() - 30 * 24 * 60 * 60 * 1000, new Date()])

async function getData() {
  const query = {
    ...getFilterData(state.filterData),
    ...state.query,
  }
  if (receive_date?.value?.length) {
    query.rec_start_date = formatDate(receive_date.value[0])
    query.rec_end_date = formatDate(receive_date.value[1])
  }
  await fetchData(query)

  tableConfig.total = total
  tableConfig.page = page
  tableConfig.size = size
}
watch(
  () => datalist.value,
  () => {
    state.list = datalist.value?.list?.map((item: any) => {
      return {
        ...item,
        whole_piece_count: formatTwoDecimalsDiv(item.whole_piece_count),
        whole_piece_weight: formatWeightDiv(item.whole_piece_weight),
      }
    })
  },
)

watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

const columnList = ref([
  {
    field: 'order_num',
    minWidth: 150,
    title: '原料采购收货单号',
  },
  {
    field: 'voucher_num',
    minWidth: 100,
    title: '凭证单号',
  },
  {
    field: 'supplier_name',
    minWidth: 100,
    title: '供应商名称',
  },
  {
    field: 'receipt_unit_name',
    minWidth: 100,
    title: '收货单位名称',
  },
  {
    field: 'receipt_date',
    minWidth: 100,
    title: '收货日期',
    is_date: true,
  },
  {
    field: 'raw_material_code',
    minWidth: 100,
    title: '原料编号',
  },
  {
    field: 'raw_material_name',
    minWidth: 100,
    title: '原料名称',
  },
  {
    field: 'brand',
    minWidth: 100,
    title: '品牌',
  },
  {
    field: 'batch_num',
    minWidth: 100,
    title: '批号',
  },
  {
    field: 'color_scheme',
    minWidth: 100,
    title: '颜色',
  },
  {
    field: 'raw_matl_remark',
    minWidth: 100,
    title: '原料备注',
  },
  {
    field: 'whole_piece_count',
    minWidth: 100,
    title: '件数',
  },
  {
    field: 'whole_piece_weight',
    minWidth: 100,
    title: '件重',
  },
  {
    field: 'remark',
    minWidth: 100,
    title: '备注',
  },
  {
    field: 'create_time',
    width: 150,
    title: '创建时间',
    isDate: true,
  },
])

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

function handCancel() {
  state.showModal = false
}

function handleSure() {
  if (!state.multipleSelection.length) {
    return ElMessage.error('请选择数据')
  }
  else {
    state.showModal = false
    emits('handleSure', state.multipleSelection)
  }
}

// 重置
function handReset() {
  state.filterData = resetData(state.filterData)
}

function changeDate() {
  // receive_date.value = [row.date_min, row.date_max]
  getData()
}

function showModal(query: any) {
  state.query = query
  getData()
  state.showModal = true
}

defineExpose({
  state,
  showModal,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="1200" height="700" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full overflow-hidden">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '120px' }">
        <DescriptionsFormItem label="原料采购收货单号:" :width="350">
          <template #content>
            <el-input v-model="state.filterData.order_num" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="供应商名称:" :width="350">
          <template #content>
            <!-- <SelectComponents
              :query="{
                unit_type_id: BusinessUnitIdEnum.rawMaterial,
              }"
              v-model="state.filterData.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              label-field="name"
              value-field="id"
            ></SelectComponents>  -->
            <SelectDialog
              v-model="state.filterData.supplier_id"
              :query="{
                unit_type_id: BusinessUnitIdEnum.rawMaterial,
                name: componentRemoteSearch.name,
              }"
              api="BusinessUnitSupplierEnumlist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :valid-config="{
                name: [
                  { required: true, message: '请输入名称' },
                  {
                    validator({ cellValue }) {
                      if (cellValue === '') {
                        new Error('供应商名称')
                      }
                    },
                  },
                ],
              }"
              :editable="true"
              @on-input="val => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="原料名称:" :width="350">
          <template #content>
            <SelectDialog
              v-model="state.filterData.raw_material_id"
              api="rawmaterialMenu"
              :query="{ name: componentRemoteSearch.raw_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (componentRemoteSearch.raw_name = val)"
            />
            <!-- <SelectComponents v-model="state.filterData.raw_material_id" api="rawmaterialMenu" label-field="name" value-field="id"></SelectComponents> -->
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收货日期:" :width="350">
          <template #content>
            <SelectDate v-model="receive_date" style="width: 100%" @change-date="changeDate" />
          </template>
        </DescriptionsFormItem>
      </div>
      <div class="flex_end mb-[20px]">
        <el-button type="primary" @click="handReset">
          重置
        </el-button>
      </div>
      <div class="flex flex-1 overflow-hidden h-full">
        <Table :config="tableConfig" :table-list="state.list" :column-list="columnList" />
      </div>
    </div>
    <template #footer>
      <el-button @click="handCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSure">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style>
.flex_end {
  display: flex;
  justify-content: end;
}
</style>
