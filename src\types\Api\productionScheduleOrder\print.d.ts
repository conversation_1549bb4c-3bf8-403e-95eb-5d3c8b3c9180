declare namespace Api.PrintFineCode {
  /**
   * produce.PrintProductionScheduleOrderFineCodeParam
   */
  export interface Request {
    /**
     * 生产通知单号
     * 机台号
     */
    ids?: string
    /**
     * 备注
     */
    remark?: string
    [property: string]: any
  }
  /**
   * produce.PrintProductionScheduleOrderFineCodeResponse
   */
  export interface Response {
    /**
     * 细码详情列表
     */
    fine_code_list?: ProduceProductionScheduleOrderFineCodePrintItem[]
    /**
     * 打印数量
     */
    print_count?: number
    /**
     * 打印记录ID
     */
    print_record_id?: number
    [property: string]: any
  }

  /**
   * produce.ProductionScheduleOrderFineCodePrintItem
   */
  export interface ProduceProductionScheduleOrderFineCodePrintItem {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 条形码
     */
    fabric_piece_code?: string
    /**
     * 坯布等级ID
     */
    grey_fabric_level_id?: number
    /**
     * 坯布等级名称
     */
    grey_fabric_level_name?: string
    /**
     * 细码ID
     */
    id?: number
    /**
     * 进仓条数
     */
    in_warehouse_roll?: number
    /**
     * 进仓数量
     */
    in_warehouse_weight?: number
    /**
     * 验布条数
     */
    inspection_roll?: number
    /**
     * 查布时间
     */
    inspection_time?: string
    /**
     * 验布数量
     */
    inspection_weight?: number
    /**
     * 查布人ID
     */
    inspector_id?: number
    /**
     * 查布人姓名
     */
    inspector_name?: string
    /**
     * 机台号
     */
    machine_number?: string
    /**
     * 出仓条数
     */
    out_warehouse_roll?: number
    /**
     * 出仓数量
     */
    out_warehouse_weight?: number
    /**
     * 打印时间
     */
    print_time?: string
    /**
     * 打印人ID
     */
    printer_id?: number
    /**
     * 打印人姓名
     */
    printer_name?: string
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 排产单ID
     */
    production_schedule_order_id?: number
    /**
     * 质量备注
     */
    quality_remark?: string
    /**
     * 库存条数
     */
    stock_roll?: number
    /**
     * 库存数量
     */
    stock_weight?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 卷号
     */
    volume_number?: number
    /**
     * 称重条数
     */
    weighing_roll?: number
    /**
     * 称重数量
     */
    weighing_weight?: number
    [property: string]: any
  }
}
