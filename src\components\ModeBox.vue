<script setup lang="ts">
import { ref } from 'vue'

const showModal = ref(false) // #region 是否展示详细筛选

// 点击取消关闭模态框
function handCancel() {
  showModal.value = false
}
// 点击确定关闭模态框
function handleSure() {
  showModal.value = false
}
</script>

<template>
  <Teleport to="body">
    <vxe-modal
      v-model="showModal"
      destroy-on-close
      show-zoom
      resize
      show-footer
      title="从质检报表中查询"
      width="1000"
      :height="300"
      :mask="false"
      :lock-view="false"
      :esc-closable="true"
    >
      <template #footer>
        <el-button @click="handCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleSure">
          确认
        </el-button>
      </template>
    </vxe-modal>
  </Teleport>
</template>

<style scoped lang="scss">
.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 6px 0 !important;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  cursor: pointer;
}

.vxe_icon {
  margin-right: 6px;
  font-size: 14px;
  cursor: pointer;
}
</style>

<style lang="scss" scoped>
::v-deep(.vxe-input--suffix) {
  width: auto;
}

::v-deep(.vxe-input.is--suffix .vxe-input--inner) {
  padding-right: 46px;
}
</style>
