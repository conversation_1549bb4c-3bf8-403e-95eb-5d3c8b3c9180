<script lang="ts" setup name="DepartmentManagement">
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { DeleteDepartmentApi, EditDepartmentApi, GetDepartmentApi, PostDepartmentApi } from '@/api/department'
import type { SystemGetDepartmentData } from '@/api/department/rule'
import { formatDate } from '@/common/format'
import { deleteToastWithRiskWarning, getFilterData } from '@/common/util'
import SelectComponents from '@/components/SelectComponents/index.vue'

interface formType {
  name: string
  sale_system_id: number | ''
  parent_id: number
  id?: number
}
const dialogFormVisibleChild = ref(false)
const titleChild = ref('新增子部门')
const ruleFormChild = reactive<formType>({
  name: '',
  sale_system_id: '',
  parent_id: 0,
  id: 0,
})
const treeProps = reactive({
  label: 'name',
  children: 'sub_department',
  class: 'customNodeClass',
})

onMounted(() => {
  getList()
})

const departmentList = ref<SystemGetDepartmentData[]>()
const { fetchData: fetchDataList, data: dataList } = GetDepartmentApi()
async function getList() {
  await fetchDataList()
  departmentList.value = dataList.value.list || []
}

const dialogFormVisible = ref(false)

function openForm() {
  ruleFormChild.parent_id = 0
  ruleFormChild.id = 0
  ruleFormChild.name = ''
  titleChild.value = '新增部门'
  dialogFormVisible.value = true
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<formType>({
  name: '',
  sale_system_id: '',
  parent_id: 0,
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '名称必须添加', trigger: 'blur' },
    { min: 1, max: 15, message: '长度在 3 到 5 个字符', trigger: 'blur' },
  ],
})

const { fetchData: fetchAdd, success: addSuccess, msg: msgAdd } = PostDepartmentApi()
async function onSubmit() {
  ruleFormRef.value!.validate(async (valid) => {
    if (valid) {
      await fetchAdd(ruleForm)
      if (addSuccess.value) {
        ElMessage.success('添加成功')
        dialogFormVisible.value = false
        getList()
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

function openChildForm(data: formType, type: 'add' | 'edit') {
  dialogFormVisibleChild.value = true
  ruleFormChild.sale_system_id = data.sale_system_id
  ruleFormChild.parent_id = data.id!

  if (type === 'edit') {
    ruleFormChild.name = data.name
    ruleFormChild.id = data.id
    titleChild.value = '编辑部门'
  }
  else {
    ruleFormChild.id = 0
    ruleFormChild.name = ''
    titleChild.value = '新增部门'
  }
}

const rulesChild = reactive<FormRules>({
  name: [
    { required: true, message: '名称必须添加', trigger: 'blur' },
    { min: 1, max: 15, message: '长度在 1 到 5 个字符', trigger: 'blur' },
  ],
})

function onChild() {
  ruleFormChild.id ? onEditChild() : onAddChild()
}
async function onAddChild() {
  ruleFormRef.value!.validate(async (valid) => {
    if (valid) {
      await fetchAdd(getFilterData(ruleFormChild))
      if (addSuccess.value) {
        ElMessage.success('添加成功')
        dialogFormVisibleChild.value = false
        getList()
      }
      else {
        ElMessage.error(msgAdd.value)
      }
    }
  })
}

const { fetchData: fetchEdit, success: editSuccess, msg: msgEdit } = EditDepartmentApi()
async function onEditChild() {
  ruleFormRef.value!.validate(async (valid) => {
    if (valid) {
      await fetchEdit(getFilterData(ruleFormChild))
      if (editSuccess.value) {
        ElMessage.success('编辑成功')
        dialogFormVisibleChild.value = false
        getList()
      }
      else {
        ElMessage.error(msgEdit.value)
      }
    }
  })
}

const { fetchData: fetchDataDel, success: successDel, msg: msgDel } = DeleteDepartmentApi()
async function onDelData(data: formType) {
  const res = await deleteToastWithRiskWarning(data.name)
  if (res) {
    await fetchDataDel({ id: data.id })
    if (successDel.value) {
      ElMessage.success('删除成功')
      getList()
    }
    else {
      ElMessage.error(msgDel.value)
    }
  }
}
</script>

<template>
  <div class="list-page block">
    <div class="p-2 flex justify-end">
      <el-button v-has="'DepartmentManagement_add'" type="primary" class="mr-2" icon="Plus" @click="openForm">
        新增
      </el-button>
    </div>

    <el-table :data="departmentList" style="width: 100%; margin-bottom: 20px" :tree-props="treeProps" row-key="id">
      <el-table-column prop="name" label="部门名称" />
      <el-table-column prop="sale_system_name" label="所属营销体系" width="200px" />
      <el-table-column prop="update_user_name" label="最后修改人" width="200px" />
      <el-table-column prop="update_time" label="最后修改时间" width="200px">
        <template #default="{ row }">
          {{ formatDate(row.update_time) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="390">
        <template #default="{ row }">
          <el-button v-if="row.parent_id !== -1" v-has="'DepartmentManagement_add'" type="primary" link class="mr-2" @click="openChildForm(row, 'add')">
            新增
          </el-button>
          <el-button v-if="row.parent_id !== -1" v-has="'DepartmentManagement_edit'" type="primary" link class="mr-2" @click="openChildForm(row, 'edit')">
            编辑
          </el-button>
          <el-button v-if="row.parent_id !== -1" v-has="'DepartmentManagement_delete'" type="danger" link @click.stop="onDelData(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogFormVisible" title="新增一级部门">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px">
        <el-form-item label="营销体系">
          <SelectComponents v-model="ruleForm.sale_system_id" api="GetSaleSystemDropdownListApi" label-field="name" value-field="id" clearable />
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model.trim="ruleForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogFormVisibleChild" :title="titleChild" width="600px">
      <el-form ref="ruleFormRef" :model="ruleFormChild" :rules="rulesChild" label-width="120px">
        <el-form-item label="营销体系">
          <SelectComponents v-model="ruleFormChild.sale_system_id" api="GetSaleSystemDropdownListApi" :disabled="true" label-field="name" value-field="id" clearable />
        </el-form-item>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model.trim="ruleFormChild.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogFormVisibleChild = false">取消</el-button>
          <el-button type="primary" @click="onChild">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.customNodeClass {
  padding: 3px 0;
}
</style>
