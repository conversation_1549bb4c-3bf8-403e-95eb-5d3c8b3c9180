declare namespace Api.GetAISaleProductOrder {
  export interface Request {
    /**
     * 下单指令
     */
    content: string
    [property: string]: any
  }
  /**
   * ai.AiGetSaleProductOrderDetailResponse
   */
  export interface Response {
    /**
     * 响应码
     */
    code?: number
    data?: {
      customer_code: string
      customer_id: number
      customer_name: string
      lost_colors: number
      sale_follower_id: number
      sale_follower_name: string
      sale_group_id: number
      sale_group_name: string
      sale_mode: number
      sale_mode_name: string
      sale_system_id: number
      sale_system_name: string
      sale_user_id: number
      sale_user_name: string
      send_product_type: number
      send_product_type_name: string
      settle_type: number
      settle_type_name: string
      list?: AiAiSaleProductOrderDetail[]
    }
    /**
     * 响应信息
     */
    msg?: string
    /**
     * 版本号
     */
    version?: string
    [property: string]: any
  }

  /**
   * ai.AiSaleProductOrderDetail
   */
  export interface AiAiSaleProductOrderDetail {
    /**
     * 调整空差 /0.1g
     */
    adjust_weight_error?: number
    /**
     * 辅助单位id
     */
    auxiliary_unit_id?: number
    /**
     * 预约匹数
     */
    book_roll?: number
    /**
     * 客户款号
     */
    customer_account_num?: string
    /**
     * 所属客户id
     */
    customer_id?: number
    /**
     * 缸号
     */
    dyelot_number?: string
    /**
     * 是否显示单价
     */
    is_display_price?: boolean
    /**
     * 长度
     */
    length?: number
    /**
     * 剪版销售单价(剪板销售价格-剪版优惠单价)
     */
    length_cut_sale_price?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string
    /**
     * 剪版优惠单价
     */
    offset_length_cut_sale_price?: number
    /**
     * 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
     */
    offset_sale_price?: number
    /**
     * 优惠空差 /0.1g
     */
    offset_weight_error?: number
    /**
     * 其他金额
     */
    other_price?: number
    /**
     * 纸筒数量（公斤）(成品用)
     */
    paper_tube_weight?: number
    /**
     * 销售计划单详细id
     */
    plan_detail_id?: number
    /**
     * StockLength                int    `json:"stock_length"`                   // 库存可用长度
     */
    pmc_grey_plan_order_summary_detail_id?: number
    /**
     * 成品编码
     */
    product_code?: string
    /**
     * 颜色id
     */
    product_color_id?: number
    /**
     * 颜色类别id(关联type_finished_product_kind_id)
     */
    product_color_kind_id?: number
    /**
     * 颜色类别名称
     */
    product_color_kind_name?: string
    /**
     * 颜色名称
     */
    product_color_name?: string
    /**
     * 成品id
     */
    product_id?: number
    /**
     * 布种类型id(关联type_grey_fabric_id)
     */
    product_kind_id?: number
    /**
     * 成品等级id
     */
    product_level_id?: number
    /**
     * 成品名称
     */
    product_name?: string
    /**
     * 成品备注
     */
    product_remark?: string
    /**
     * 采购长度
     */
    purchase_length?: number
    /**
     * 采购匹数
     */
    purchase_roll?: number
    /**
     * 采购数量
     */
    purchase_weight?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 匹数
     */
    roll?: number
    /**
     * 销售等级ID
     */
    sale_level_id?: number
    /**
     * 销售单价(销售报价-优惠单价)(大货 散剪)
     */
    sale_price?: number
    /**
     * 成品销售单id
     */
    sale_product_order_id?: number
    /**
     * 结算空差 /0.1g
     */
    settle_weight_error?: number
    /**
     * 欠货长度
     */
    shortage_length?: number
    /**
     * 欠货匹数
     */
    shortage_roll?: number
    /**
     * 欠货数量
     */
    shortage_weight?: number
    /**
     * 剪板销售价格
     */
    standard_length_cut_sale_price?: number
    /**
     * 标准销售报价(大货 散剪)
     */
    standard_sale_price?: number
    /**
     * 库存汇总id
     */
    stock_product_id?: number
    /**
     * 库存可用匹数(可用库存)
     */
    stock_roll?: number
    /**
     * 可用数量
     */
    stock_weight?: number
    /**
     * 出货仓库id
     */
    warehouse_id?: number
    /**
     * 出货仓库名称
     */
    warehouse_name?: string
    /**
     * 数量
     */
    weight?: number
    /**
     * 标准空差 /0.1g
     */
    weight_error?: number
    [property: string]: any
  }
}
declare namespace Api.Enum {
  /**
   * system.BaseData
   */
  export interface Response {
    code?: string
    default?: boolean
    id?: number
    name?: string
    [property: string]: any
  }
}
