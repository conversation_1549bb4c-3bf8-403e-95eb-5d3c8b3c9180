import { useRequest } from '@/use/useRequest'

// 获取资源列表
export const GetResourceTreeListApi = () => {
  return useRequest({
    url: '/admin/v1/resourceTree/getResourceTreeList',
    method: 'get',
  })
}

// 新增资源列表
export const AddResourceTreeApi = () => {
  return useRequest({
    url: '/admin/v1/resourceTree/addResourceTree',
    method: 'post',
  })
}

// 删除资源列表
export const DeleteResourceTreeApi = () => {
  return useRequest({
    url: '/admin/v1/resourceTree/deleteResourceTree',
    method: 'delete',
  })
}

// 更新资源列表
export const UpdateResourceTreeApi = () => {
  return useRequest({
    url: '/admin/v1/resourceTree/updateResourceTree',
    method: 'put',
  })
}

// 更新资源状态
export const UpdateResourceTreeStatusApi = () => {
  return useRequest({
    url: '/admin/v1/resourceTree/updateResourceTreeStatus',
    method: 'put',
  })
}
