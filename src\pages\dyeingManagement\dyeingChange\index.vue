<script lang="ts" setup name="DyeingChange">
import { ElMessage } from 'element-plus'
import { nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Delete } from '@element-plus/icons-vue'
import OrderMore from './components/OrderMore.vue'
import {
  change_ordercancel,
  change_orderdetail,
  change_orderlist,
  change_orderpass,
} from '@/api/dyeingChange'
import { BusinessUnitIdEnum } from '@/common/enum'
import { formatDate, formatPriceDiv, formatWeightDiv } from '@/common/format'
import {
  debounce,
  deepClone,
  deleteToast,
  getFilterData,
  getRecentDay_Date,
  resetData,
} from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'

const state = reactive<any>({
  filterData: {
    name: '',
    status: '',
  },
  multipleSelection: [],
  information: false,
  isDyeing: true,
  moreItems: [],
  dyeingType: 1,
})

const {
  fetchData: ApiCustomerList,
  data,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = change_orderlist()

// 获取数据
const getData = debounce(async () => {
  const obj = deepClone(state.filterData)
  if (state.filterData.status.length)
    obj.status = obj.status.join(',')

  const query: any = {
    dnf_start_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[0])
        : '',
    dnf_end_date:
      state.filterData.devierDate
      && state.filterData.devierDate !== ''
      && state.filterData.devierDate.length
        ? formatDate(state.filterData.devierDate[1])
        : '',
    create_start_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[0])
        : '',
    create_end_time:
      state.filterData.create_time
      && state.filterData.create_time !== ''
      && state.filterData.create_time.length
        ? formatDate(state.filterData.create_time[1])
        : '',
    audit_start_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[0])
        : '',
    audit_end_time:
      state.filterData.audit_time
      && state.filterData.audit_time !== ''
      && state.filterData.audit_time.length
        ? formatDate(state.filterData.audit_time[1])
        : '',
    ...state.filterData,
    status: obj.status,
  }
  delete query.audit_time
  delete query.create_time
  delete query.devierDate
  await ApiCustomerList(getFilterData(query))
  if (data.value?.list)
    getInfomation(data.value.list[0])
}, 400)
onMounted(() => {
  getData()
  state.filterData.create_time = getRecentDay_Date(1)
})

onActivated(() => {
  getData()
})

watch(
  () => state.filterData,
  () => {
    getData()
  },
  {
    deep: true,
  },
)

const tableConfig = ref({
  fieldApiKey: 'DyeingChange',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '10%',
  height: '100%',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  //   handAllSelect: (val: any) => handAllSelect(val),
  //   handleSelectionChange: (val: any) => handleSelectionChange(val),
})

function handReset() {
  state.filterData = resetData(state.filterData)
}

const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '单据编号',
    soltName: 'order_no',
    width: '8%',
  },
  {
    sortable: true,
    field: 'src_order_no',
    title: '通知单号',
    width: '8%',
    soltName: 'link',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销体系',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dye_factory_name',
    title: '染厂名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_follower_name',
    title: '染厂跟单',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'order_follower_phone',
    title: '跟单电话',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'dnf_date',
    title: '染整日期',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'return_address',
    title: '回货地址',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '单据备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'piece_count',
    title: '原匹数',
    minWidth: 120,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'weight',
    title: '原总数量',
    minWidth: 120,
    isWeight: true,
  },
  // {
  //   field: 'receive_time',
  //   title: '变更匹数',
  //   minWidth: 120,
  // },
  // {
  //   field: 'receive_time',
  //   title: '变更数量',
  //   minWidth: 120,
  // },
  // {
  //   field: 'rtn_piece_count',
  //   title: '最终匹数',
  //   minWidth: 120,
  //   isPrice: true,
  // },
  // {
  //   field: 'rtn_weight',
  //   title: '最终总数量',
  //   minWidth: 120,
  //   isWeight: true,
  // },
  {
    field: 'create_time',
    title: '创建时间',
    sortable: true,
    minWidth: 120,
    isDate: true,
  },
  {
    field: 'audit_time',
    title: '审核时间',
    sortable: true,
    minWidth: 120,
    isDate: true,
  },
  {
    field: 'update_time',
    title: '最后修改时间',
    sortable: true,
    isDate: true,
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'status',
    title: '单据状态',
    fixed: 'right',
    soltName: 'status',
    showOrder_status: true,
    width: '5%',
  },
])

const router = useRouter()

function handDetail(row: any) {
  if (row.src_order_type === 1 && row.src_dnf_type === 1) {
    router.push({
      name: 'DyeingChangeDetail',
      query: { id: row.id },
    })
  }
  else if (row.src_order_type === 1 && row.src_dnf_type !== 1) {
    router.push({
      name: 'ProcessChangeDetail',
      query: { id: row.id },
    })
  }
  else if (row.src_order_type === 2) {
    router.push({
      name: 'ColorChangeDetail',
      query: { id: row.id },
    })
  }
  else if (row.src_order_type === 3) {
    router.push({
      name: 'AdjusmentChangeDetail',
      query: { id: row.id },
    })
  }
}

function handEdit(row: any) {
  if (row.src_order_type === 1 && row.src_dnf_type === 1) {
    router.push({
      name: 'DyeingChangeEdit',
      query: { id: row.id },
    })
  }
  else if (row.src_order_type === 1 && row.src_dnf_type !== 1) {
    router.push({
      name: 'ProcessChangeEdit',
      query: { id: row.id },
    })
  }
  else if (row.src_order_type === 2) {
    router.push({
      name: 'ColorChangeEdit',
      query: { id: row.id },
    })
  }
  else if (row.src_order_type === 3) {
    router.push({
      name: 'AdjusmentChangeEdit',
      query: { id: row.id },
    })
  }
}

const { fetchData: getFetchDetail, data: fabricList } = change_orderdetail()

const OrderMoreRef = ref()
function handleSrcDetail(row: any) {
  router.push({
    name: 'DyeingNoticeDetail',
    query: { id: row.src_order_id },
  })
}
// 获取坯布信息
async function getInfomation(row: any) {
  state.information = true
  await getFetchDetail({ id: row.id })

  if (
    fabricList.value.src_order_type === 1
    && fabricList.value.src_dnf_type === 1
  )
    state.dyeingType = 1
  else if (
    fabricList.value.src_order_type === 1
    && fabricList.value.src_dnf_type !== 1
  )
    state.dyeingType = 4
  else if (fabricList.value.src_order_type === 2)
    state.dyeingType = 2
  else if (fabricList.value.src_order_type === 3)
    state.dyeingType = 3

  nextTick(() => {
    if (fabricList.value) {
      try {
        fabricList.value.craft_requirement = JSON.parse(
          fabricList.value?.craft_requirement,
        )
        fabricList.value.craft_requirement.selectList
          = fabricList.value.craft_requirement?.selectList?.map((item: any) => {
            if (item?.show_type === 2) {
              // 把以逗号分隔的字符串转换成数组，这里只是展示，所以直接在这里把开头的-去掉
              item.selectValue = item.selectValue
                .split(',')
                .map((value: string) =>
                  value.startsWith('-') ? value.substring(1) : Number(value),
                )
            }
            return item
          })
      }
      catch (error) {
        // 处理空值情况
      }

      const obj = deepClone(fabricList.value)

      state.moreItems = deepClone(fabricList.value.items)

      // 遍历数据数组
      state.moreItems?.forEach((item: any) => {
        if (
          (row.src_order_type === 1 && row.src_dnf_type === 1)
          || row.src_order_type === 2
          || row.src_order_type === 3
        ) {
          item.use_fabric.map((subItem: any, index: number) => {
            // 将对象摊平
            subItem.total_weight = subItem.weight
            subItem.stock_weight = subItem.gf_stock_info.weight
            Object.assign(subItem, subItem.gf_stock_info)
            // 删除原来的对象
            delete subItem.gf_stock_info
            // 更新数据数组
            item.use_fabric[index] = subItem
          })
        }
        else {
          item.use_fabric.map((subItem: any, index: number) => {
            // 将对象摊平
            subItem.total_weight = subItem.weight
            subItem.out_weight = subItem.f_out_info.weight
            Object.assign(subItem, subItem.f_out_info)

            // 删除原来的对象
            delete subItem.f_out_info
            // 更新数据数组
            item.use_fabric[index] = subItem
          })
        }
      })
      OrderMoreRef.value.state.detail = obj
    }
  })
}

const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = change_orderpass()

// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = change_ordercancel()

async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销体系:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.sale_system_id"
              api="AdminsaleSystemgetSaleSystemDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染厂名称:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.dye_factory_id"
              api="BusinessUnitSupplierEnumAll"
              :query="{ unit_type_id: BusinessUnitIdEnum.dyeFactory }"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="染整日期:">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.creator_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="创建时间:">
          <template #content>
            <SelectDate v-model="state.filterData.create_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核人:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.auditor_id"
              api="GetUserDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="审核时间:">
          <template #content>
            <SelectDate v-model="state.filterData.audit_time" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              api="GetAuditStatusEnum"
              multiple
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="">
          <template #content>
            <el-button v-btnAntiShake="handReset" :icon="Delete" text link type="info">
              清除条件
            </el-button>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full">
      <Table
        :config="tableConfig"
        :table-list="data?.list"
        :column-list="columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" :underline="false" @click="getInfomation(row)">
            {{ row.order_no }}
          </el-link>
        </template>
        <template #link="{ row }">
          <el-link type="primary" :underline="false" @click="handleSrcDetail(row)">
            {{ row.src_order_no }}
          </el-link>
        </template>
        <template #change_roll="{ row }">
          <div
            v-if="formatPriceDiv(row.change_piece_count) > 0"
            class="text-[#3fc191]"
          >
            +{{ formatPriceDiv(row.change_piece_count) }}
          </div>
          <div
            v-else-if="formatPriceDiv(row.change_piece_count) < 0"
            class="text-[#ed90bd]"
          >
            -{{ formatPriceDiv(row.change_piece_count) }}
          </div>
          <div v-else-if="row.change_piece_count === 0">
            {{ row.change_piece_count }}
          </div>
        </template>
        <template #change_weight="{ row }">
          <div
            v-if="formatWeightDiv(row.change_weight) > 0"
            class="text-[#3fc191]"
          >
            +{{ formatWeightDiv(row.change_weight) }}
          </div>
          <div
            v-else-if="formatWeightDiv(row.change_weight) < 0"
            class="text-[#ed90bd]"
          >
            -{{ formatWeightDiv(row.change_weight) }}
          </div>
          <div v-else-if="row.change_weight === 0">
            {{ row.change_weight }}
          </div>
        </template>

        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'DyeingChangeDetail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.status === 1 || row.status === 3"
              v-has="'DyeingChangeEdit'"
              type="primary"
              :underline="false"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.status === 1"
              v-has="'DyeingChangePass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.status === 2"
              v-has="'DyeingChangeCancel'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard class="table-card-bottom" :tool-bar="false">
      <OrderMore
        ref="OrderMoreRef"
        v-model:table-list="state.moreItems"
        class="h-full"
        table-height="100%"
        :dyeing-type="state.dyeingType"
      />
    </FildCard>
  </div>
</template>

<style></style>
