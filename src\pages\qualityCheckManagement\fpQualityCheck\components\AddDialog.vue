<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useToggle } from '@vueuse/core'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { formatLengthMul } from '@/common/format'

const emits = defineEmits(['handleSure', 'onHide'])
const rules = reactive<any>({
  defect_count: [{ required: true, message: '请输入疵点个数', trigger: 'blur' }],
})

const state = reactive({
  showModal: false,
  modalName: '验布疵点定义',
  form: {
    name: '',
    defect_count: 1, // 疵点个数
    defect_position: 0, // 疵点位置 进位 10000
    score: '1', // 分数
  },
})

const [isOther, setIsOther] = useToggle(false)

function showAddDialog(row: any, isOther: boolean, isAdd: boolean) {
  state.showModal = true
  setIsOther(isOther)
  if (isAdd) {
    state.form = {
      ...row,
      id: void 0,
      defect_count: 1,
      defect_position: 0,
      score: '1',
      defect_id: row?.id || 0,
      defect_name: row?.name || '',
    }
    return
  }
  state.form = {
    ...row,
  }
}

const formRef = ref()
function handleSubmit() {
  formRef.value.validate((valid: boolean) => {
    if (!valid)
      return

    emits('handleSure', {
      ...state.form,
      measurement_unit_id: state.form.measurement_unit_id || 0,
      defect_id: state.form.defect_id || 0,
      defect_count: Number(state.form.defect_count),
      defect_position: formatLengthMul(state.form.defect_position),
      score: Number(state.form.score),
    })
    state.showModal = false
  })
}

function setData(formData: any) {
  state.showModal = true
  state.form = formData
}

function unitSelectChange(val: any) {
  state.form.measurement_unit_name = val.name
}
function handleHide() {
  emits('onHide')
}
defineExpose({
  showAddDialog,
  setData,
  state,
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="300" :mask="false" :lock-view="false" :esc-closable="true" resize @hide="handleHide">
    <el-form ref="formRef" :model="state.form" :rules="rules">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="疵点名称:">
          <template #content>
            <div v-if="isOther">
              <el-input v-model="state.form.defect_name" placeholder="请输入疵点名称" />
            </div>
            <span v-else>
              {{ state.form.name || state.form?.defect_name }}
            </span>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单位名称:">
          <template #content>
            <div v-if="isOther">
              <SelectComponents v-model="state.form.measurement_unit_id" style="width: 200px" api="getInfoBaseMeasurementUnitList" label-field="name" value-field="id" clearable @change-value="unitSelectChange" />
            </div>
            <span v-else>
              {{ state.form.measurement_unit_name }}
            </span>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="疵点位置:">
          <template #content>
            第
            <vxe-input v-model="state.form.defect_position" type="number" style="width: 120px !important" />
            米
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="疵点数量:" required>
          <template #content>
            <el-form-item prop="defect_count">
              <vxe-input v-model="state.form.defect_count" type="number" />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="分数:" required>
          <template #content>
            <el-form-item>
              <el-radio-group v-model="state.form.score">
                <el-radio-button label="1" value="1" />
                <el-radio-button label="2" value="2" />
                <el-radio-button label="3" value="3" />
                <el-radio-button label="4" value="4" />
              </el-radio-group>
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </vxe-modal>
</template>
