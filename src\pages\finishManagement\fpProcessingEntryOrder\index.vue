<script setup lang="ts" name="FpProcessingEntryOrder">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import FineSizeEnteringDetail from '../components/FineSizeEnteringDetail.vue'
import {
  getFpmProcessInOrder,
  getFpmProcessInOrderList,
  updateFpmProcessInOrderStatusPass,
  updateFpmProcessInOrderStatusWait,
} from '@/api/fpProcessingEntryOrder'
import { BusinessUnitIdEnum } from '@/common/enum'
import {
  formatDate,
  formatLengthDiv,
  formatTwoDecimalsDiv,
  formatUnitPriceDiv,
  formatWeightDiv,
} from '@/common/format'
import { debounce, deleteToast, getFilterData } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { usePageQuery } from '@/use/usePageQuery'

const { formatFilterObj, formatDateRange } = usePageQuery()

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeStock,
  dataType: PrintDataType.Product,
})

const router = useRouter()

const mainOptionsTablesRef = ref()
const filterData = reactive<any>(formatFilterObj({
  order_no: '',
  voucher_number: '',
  biz_unit_id: '',
  warehouse_id: '',
  audit_status: [],
}))

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  recipien_entity_name: '',
})

const warehousing_date = ref<any>(formatDateRange())
// 审核
const {
  fetchData: auditFetch,
  success: auditSuccess,
  msg: auditMsg,
} = updateFpmProcessInOrderStatusPass()
// 消审
const {
  fetchData: cancelFetch,
  success: cancelSuccess,
  msg: cancelMsg,
} = updateFpmProcessInOrderStatusWait()
// 驳回
// const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = updateFpmProcessInOrderStatusReject()

const {
  fetchData: fetchDataList,
  data: mainDataList,
  total: totalList,
  page: pageList,
  size: sizeList,
  loading: loadingList,
  handleSizeChange,
  handleCurrentChange,
}: any = getFpmProcessInOrderList()
// 获取列表数据
async function getData() {
  const query = {
    ...filterData,
  }

  if (query.audit_status.length)
    query.audit_status = query.audit_status.join(',')

  if (warehousing_date?.value?.length) {
    query.in_time_begin = formatDate(warehousing_date.value[0])
    query.in_time_end = formatDate(warehousing_date.value[1])
  }
  await fetchDataList(getFilterData({ ...query }))
  if (mainDataList.value?.list)
    showFinishProductionDetail(mainDataList.value.list[0])
}

const printList = ref<any>([])

// 成品信息
const finishProductionOptions = reactive<any>({
  detailShow: false,
  tableConfig: {
    operateWidth: '150',
    showOperate: true,
    fieldApiKey: 'FpProcessingEntryOrder_B',
    showSlotNums: false,
    showSpanHeader: true,
    height: '100%',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
  },
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return '总计'

        // 总数量 纸筒总重（kg） 结算数量 进仓辅助数量 采购辅助数量 进仓金额
        if (['use_gf_weight'].includes(column.field))
          return sumNum(data, 'use_gf_weight', '')

        if (['in_weight'].includes(column.field))
          return sumNum(data, 'in_weight', '')

        if (['settle_weight'].includes(column.field))
          return sumNum(data, 'settle_weight', '')

        if (['dye_delivery_order_weight'].includes(column.field))
          return sumNum(data, 'dye_delivery_order_weight', '')

        return null
      }),
    ]
  },
  datalist: [],
  columnList: [
    {
      title: '基础信息',
      fixed: 'left',
      field: 'A',
      childrenList: [
        {
          sortable: true,
          field: 'product_code',
          title: '成品编号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_name',
          title: '成品名称',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'B',
      title: '成品信息',
      childrenList: [
        {
          sortable: true,
          field: 'quote_order_no',
          title: '染整单号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'quote_order_type_name',
          title: '单据类型',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'customer_name',
          title: '所属客户',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_code',
          title: '色号',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_color_name',
          title: '颜色',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_level_name',
          title: '成品等级',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_remark',
          title: '成品备注',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_craft',
          title: '成品工艺',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_width',
          soltName: 'product_width',
          title: '成品幅宽',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_gram_weight',
          soltName: 'product_gram_weight',
          title: '成品克重',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'product_ingredient',
          title: '成品成分',
          minWidth: 100,
        },
        {
          sortable: true,
          field: 'in_roll', // 100
          title: '进仓匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'customer_account_num',
          title: '款号',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'contract_number',
          title: '合同号',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'voucher_number',
          title: '凭证单号',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'C',
      title: '坯布消耗信息',
      childrenList: [
        {
          sortable: true,
          field: 'use_gf_roll', // '5%'
          title: '用坯匹数',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'use_gf_weight', // 数量
          title: '用坯数量',
          minWidth: '5%',
        },
      ],
    },

    {
      field: 'D',
      title: '成品进仓信息',
      childrenList: [
        {
          sortable: true,
          field: 'in_weight', // 数量
          title: '进仓数量',
          minWidth: '5%',
        },
        // {
        //   field: 'paper_tube_weight', // 数量
        //   title: '纸筒总重（kg）',
        //   minWidth: '5%',
        // },
        {
          sortable: true,
          field: 'weight_error', // 数量
          title: '空差',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'settle_weight', // 数量
          title: '结算数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'unit_name',
          title: '单位',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'dye_delivery_order_weight', // price
          title: '染厂单据数量',
          minWidth: '5%',
        },
        {
          sortable: true,
          field: 'in_length', // price
          title: '进仓辅助数量',
          minWidth: '5%',
        },
      ],
    },
    {
      field: 'E',
      title: '单据备注信息',
      childrenList: [
        {
          sortable: true,
          field: 'remark',
          title: '备注',
          minWidth: 100,
        },
      ],
    },
    {
      field: 'F',
      title: '',
      fixed: 'right',
      childrenList: [
        {
          field: 'xima',
          soltName: 'xima',
          title: '细码',
          width: 100,
        },
      ],
    },
  ],
})
function handlePrintXiMa(row?: any) {
  printList.value = []
  if (!row) {
    finishProductionOptions.datalist.forEach((item: any) => {
      item.item_fc_data.forEach((it: any) => {
        printList.value.push({
          finish_product_craft: it?.finish_product_craft,
          density: it?.density,
          product_kind_name: it?.product_kind_name,
          bleach_name: it?.bleach_name,
          finish_product_width: it?.finish_product_width_and_unit_name,
          finish_product_gram_weight:
            it?.finish_product_gram_weight_and_unit_name,
          weaving_organization_name: it?.weaving_organization_name,
          product_name: item?.product_name,
          product_code: item?.product_code,
          yarn_count: it?.yarn_count,
          finish_product_ingredient: item?.product_ingredient,
          dyelot_number: it?.dye_factory_dyelot_number,
          weight: it.settle_weight || 0,
          measurement_unit_name: it?.measurement_unit_name,
          product_color_code: item?.product_color_code,
          product_color_name: item?.product_color_name,
          qr_code: it?.qr_code, // 二维码
          bar_code: it?.bar_code, // 条形码
          volume_number: it?.volume_number, // 匹号
          print_date: it?.print_date, // 打印时间
        })
      })
    })
  }
  else {
    row.item_fc_data.forEach((it: any) => {
      printList.value.push({
        finish_product_craft: it?.finish_product_craft,
        density: it?.density,
        product_kind_name: it?.product_kind_name,
        bleach_name: it?.bleach_name,
        finish_product_width: it?.finish_product_width_and_unit_name,
        finish_product_gram_weight:
          it?.finish_product_gram_weight_and_unit_name,
        weaving_organization_name: it?.weaving_organization_name,
        product_name: row?.product_name,
        measurement_unit_name: it?.measurement_unit_name,
        product_code: row?.product_code,
        yarn_count: it?.yarn_count,
        finish_product_ingredient: row?.product_ingredient,
        dyelot_number: it?.dye_factory_dyelot_number,
        weight: it.settle_weight,
        product_color_code: row?.product_color_code,
        product_color_name: row?.product_color_name,
        qr_code: it?.qr_code, // 二维码
        bar_code: it?.bar_code, // 条形码
        volume_number: it?.volume_number, // 匹号
        print_date: it?.print_date, // 打印时间
      })
    })
  }
}

const mainOptions = reactive<any>({
  multipleSelection: [],
  tableConfig: {
    fieldApiKey: 'FpProcessingEntryOrder',
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: true,
    showOperate: true,
    operateWidth: '10%',
    height: '100%',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    handAllSelect: (val: any) => handAllSelect(val),
    handleSelectionChange: (val: any) => handleSelectionChange(val),
  },
  mainList: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      soltName: 'order_no',
      title: '单据编号',
      fixed: 'left',
      width: '8%',
    },
    {
      sortable: true,
      field: 'voucher_number',
      title: '凭证号',
      width: 100,
    },
    {
      sortable: true,
      field: 'process_name',
      title: '加工单位',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_name',
      title: '仓库名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'sale_system_name',
      title: '营销体系名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'warehouse_in_time',
      title: '进仓日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'store_keeper_name',
      title: '仓管员',
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_roll',
      title: '匹数总计', // 100
      minWidth: 100,
    },

    {
      sortable: true,
      field: 'total_weight',
      title: '数量总计', // 数量
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'unit_name',
      title: '单位', // no
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_length',
      title: '辅助数量总计', // 100
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'total_price',
      soltName: 'total_price',
      title: '单据金额', // 100
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      width: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      width: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'auditor_name',
      title: '审核人',
      width: 100,
    },
    {
      sortable: true,
      field: 'audit_time',
      title: '审核时间',
      isDate: true,
      width: 150,
    },
    {
      sortable: true,
      field: 'audit_status',
      title: '单据状态',
      showOrder_status: true,
      soltName: 'audit_status',
      fixed: 'right',
      width: '5%',
    },
  ],
  //   导出
  handleExport: async () => {
    // if (mainOptions.multipleSelection.length < 1) return ElMessage.warning('请勾选要导出的数据')
    // const { fetchData: getFetch, success: getSuccess, msg: getMsg } = getSheetOfProductionPlanList()
    // mainOptions.exportOptions.loadingExcel = true
    // exportExcel()
    // await getFetch({
    //   ...getFilterData(mainOptions.mainList),
    //   download: 1,
    // })
    // if (getSuccess.value) {
    //
    // ElMessage({
    //   type: 'success',
    //   message: '成功',
    // })
    // } else {
    //   ElMessage({
    //     type: 'error',
    //     message: getMsg.value,
    //   })
    // }
    // mainOptions.exportOptions.loadingExcel = false
  },
  // exportOptions: {
  //   loadingExport: false,
  //   handleExport: () => {},
  // },
})
// mainOptions.exportOptions.handleExport = mainOptions.handleExport

watch(
  () => mainDataList.value,
  () => {
    mainOptions.mainList
          = mainDataList.value?.list?.map((item: any) => {
        const item_data
            = item.item_data?.map((v: any) => {
              return {
                ...v,
                in_roll: formatTwoDecimalsDiv(item.in_roll), // 进仓匹数
                quote_roll: formatTwoDecimalsDiv(item.quote_roll), // 采购数量
                quote_weight: formatWeightDiv(item.quote_weight), // 采购基本单位数量
                total_weight: formatWeightDiv(item.total_weight), // 进仓数量
                weight_error: formatWeightDiv(item.weight_error), // 空差
                paper_tube_weight: formatWeightDiv(item.paper_tube_weight), // 纸筒总重
                settle_weight: formatWeightDiv(item.settle_weight), // 结算数量
                unit_price: formatUnitPriceDiv(item.unit_price), // 单价
                in_length: formatLengthDiv(item.in_length), // 进仓辅助数量
                quote_length: formatLengthDiv(item.quote_length), // 采购辅助数量
                length_unit_price: formatUnitPriceDiv(item.length_unit_price), // 辅助数量单价
                other_price: formatTwoDecimalsDiv(item.other_price), // 其他金额
                total_price: formatTwoDecimalsDiv(item.total_price), // 进仓金额
              }
            }) || []
        return {
          ...item,
          total_roll: formatTwoDecimalsDiv(item.total_roll), // 匹数总计
          total_weight: formatWeightDiv(item.total_weight), // 数量总计
          total_length: formatLengthDiv(item.total_length), // 辅助数量总计
          total_price: formatTwoDecimalsDiv(item.total_price), // 单据金额
          item_data,
        }
      }) || []
  },
  { deep: true },
)

// 表格选中事件
function handAllSelect({ records }: any) {
  mainOptions.multipleSelection = records
}
function handleSelectionChange({ records }: any) {
  mainOptions.multipleSelection = records
}
function changeDate() {
  // warehousing_date.value = [row.date_min, row.date_max]
  getData()
}
onMounted(() => {
  getData()
})
onActivated(getData)

watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)
// 表格操作列功能
// 审核
async function handAudit(row: any) {
  const res = await deleteToast('确认提交审核嘛？')
  if (res) {
    await auditFetch({ audit_status: 2, id: row.id.toString() })
    if (auditSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(auditMsg.value)
    }
  }
}
// 消审
async function handApproved(row: any) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ audit_status: 1, id: row.id.toString() })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
function handDetail(row: any) {
  router.push({
    name: 'FpProcessingEntryOrderDetail',
    query: {
      id: row.id,
    },
  })
}
function handEdit(row: any) {
  router.push({
    name: 'FpProcessingEntryOrderEdit',
    query: {
      id: row.id,
    },
  })
}
function handAdd() {
  router.push({
    name: 'FpProcessingEntryOrderAdd',
  })
}

// 获取成品信息
function showFinishProductionDetail(row: any) {
  finishProductionOptions.detailShow = true
  getFinishProductionData(row.id)
}

const { fetchData: DetailFetch, data: finishProData } = getFpmProcessInOrder()
async function getFinishProductionData(id: string | number) {
  await DetailFetch({ id })
  finishProductionOptions.datalist = finishProData.value?.item_data?.map(
    (item: any) => {
      return {
        ...item,
        in_roll: formatTwoDecimalsDiv(Number(item.in_roll)),
        quote_roll: formatTwoDecimalsDiv(Number(item.quote_roll)),
        in_weight: formatWeightDiv(Number(item.in_weight)),
        weight_error: formatWeightDiv(Number(item.weight_error)),
        settle_weight: formatWeightDiv(Number(item.settle_weight)),
        quote_length: formatLengthDiv(Number(item.quote_length)),
        in_length: formatLengthDiv(Number(item.in_length)),
        use_gf_roll: formatTwoDecimalsDiv(Number(item.use_gf_roll)),
        use_gf_weight: formatWeightDiv(Number(item.use_gf_weight)),
        dye_delivery_order_weight: formatWeightDiv(
          Number(item.dye_delivery_order_weight),
        ),
      }
    },
  )
}
const FineSizeEnteringDetailRef = ref()
function showDialog(row: any) {
  FineSizeEnteringDetailRef.value.showDialog(row)
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="单据编号:">
          <template #content>
            <vxe-input
              v-model="filterData.order_no"
              style="width: 100%"
              placeholder="单据编号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="凭证号:">
          <template #content>
            <vxe-input
              v-model="filterData.voucher_number"
              style="width: 100%"
              placeholder="凭证号"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="加工单位:">
          <template #content>
            <SelectDialog
              v-model="filterData.process_unit_id"
              :query="{
                unit_type_id: `${BusinessUnitIdEnum.dyeFactory}`,
                name: componentRemoteSearch.name,
              }"
              api="business_unitlist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="仓库名称:">
          <template #content>
            <SelectComponents
              v-model="filterData.warehouse_id"
              style="width: 100%"
              api="GetPhysicalWarehouseDropdownList"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="进仓日期:" width="310">
          <template #content>
            <SelectDate
              v-model="warehousing_date"
              style="width: 100%"
              @change-date="changeDate"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="单据状态:">
          <template #content>
            <SelectComponents
              v-model="filterData.audit_status"
              multiple
              api="GetAuditStatusEnum"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard class="table-card-full" :tool-bar="true">
      <template #right-top>
        <el-button
          v-has="'FpProcessingEntryOrder_add'"
          style="margin-left: 10px"
          type="primary"
          :icon="Plus"
          @click="handAdd"
        >
          新建
        </el-button>
      </template>
      <Table
        ref="mainOptionsTablesRef"
        :config="mainOptions.tableConfig"
        :table-list="mainOptions.mainList"
        :column-list="mainOptions.columnList"
      >
        <template #order_no="{ row }">
          <el-link type="primary" @click="showFinishProductionDetail(row)">
            {{
              row.order_no
            }}
          </el-link>
        </template>
        <template #total_price="{ row }">
          ￥{{ row.total_price }}
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              v-has="'FpProcessingEntryOrder_detail'"
              type="primary"
              :underline="false"
              @click="handDetail(row)"
            >
              查看
            </el-link>
            <el-link
              v-if="row.audit_status === 1 || row.audit_status === 3"
              v-has="'FpProcessingEntryOrder_edit'"
              :underline="false"
              type="primary"
              @click="handEdit(row)"
            >
              编辑
            </el-link>
            <el-link
              v-if="row.audit_status === 1"
              v-has="'FpProcessingEntryOrder_pass'"
              type="primary"
              :underline="false"
              @click="handAudit(row)"
            >
              审核
            </el-link>
            <el-link
              v-if="row.audit_status === 2"
              v-has="'FpProcessingEntryOrder_wait'"
              type="primary"
              :underline="false"
              @click="handApproved(row)"
            >
              消审
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard
      title=""
      class="table-card-bottom"
    >
      <template #right-top>
        <PrintPopoverBtn
          :options="options"
          :list="printList"
          @on-print="handlePrintXiMa(row)"
        />
        <!--   成品布细码标签   -->
        <!--      <el-popover placement="left" title="选择打印" :width="180" trigger="hover"> -->
        <!--        <template #reference> -->
        <!--          <el-button type="primary" plain :icon="Printer">细码标签打印</el-button> -->
        <!--        </template> -->

        <!--        &lt;!&ndash;   成品布细码标签   &ndash;&gt; -->
        <!--        <PrintBtn type="productionXiMa" class="!ml-0" btnText="打印标签1" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签一']" @onPrint="handlePrintXiMa(row)" :list="printList" /> -->
        <!--        &lt;!&ndash;  成品布细码标签（二维码条形码）    &ndash;&gt; -->
        <!--        <PrintBtn @onPrint="handlePrintXiMa(row)" btnText="打印标签2" :list="printList" type="productionXimaLabelCode" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签二']" /> -->
        <!--        <PrintBtn btnText="打印标签3" class="!ml-0" @onPrint="handlePrintXiMa(row)" :list="printList" type="productionXiMa" :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签三']" /> -->
        <!--      </el-popover> -->
        <!--   成品布细码标签   -->
        <!--      <PrintBtn type="productionXiMa" btnType="primary" plain btnText="细码标签打印" :tid="1679373308416256" @onPrint="handlePrintXiMa" :list="printList" /> -->
      </template>
      <Table
        :config="finishProductionOptions.tableConfig"
        :table-list="finishProductionOptions.datalist"
        :column-list="finishProductionOptions.columnList"
      >
        <template #xima="{ row }">
          <el-link @click="showDialog(row)">
            查看
          </el-link>
        </template>
        <template #product_width="{ row }">
          {{ row.product_width }}
          {{ row.finish_product_width_unit_name }}
        </template>
        <template #product_gram_weight="{ row }">
          {{ row.product_gram_weight }}
          {{ row.finish_product_gram_weight_unit_name }}
        </template>
        <template #operate="{ row }">
          <PrintPopoverBtn
            :options="options"
            :list="printList"
            @on-print="handlePrintXiMa(row)"
          />
          <!--   成品布细码标签   -->
          <!--        <el-popover
            placement="left"
            title="选择打印"
            :width="180"
            trigger="hover"
          > -->
          <!--          <template #reference> -->
          <!--            <el-button type="text">打印</el-button> -->
          <!--          </template> -->

          <!--          &lt;!&ndash;   成品布细码标签   &ndash;&gt; -->
          <!--          <PrintBtn
              type="productionXiMa"
              size="small"
              btnText="打印标签1"
              :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签一']"
              @onPrint="handlePrintXiMa(row)"
              :list="printList"
            /> -->
          <!--          &lt;!&ndash;  成品布细码标签（二维码条形码）    &ndash;&gt; -->
          <!--          <PrintBtn
              @onPrint="handlePrintXiMa(row)"
              size="small"
              class="!ml-0"
              btnText="打印标签2"
              :list="printList"
              type="productionXimaLabelCode"
              :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签二']"
            /> -->
          <!--          <PrintBtn
              btnText="打印标签3"
              class="!ml-0"
              size="small"
              @onPrint="handlePrintXiMa(row)"
              :list="printList"
              type="productionXiMa"
              :tid="PrintTemplateIdMap['成品细码库存&#45;&#45;标签三']"
            /> -->
          <!--        </el-popover> -->
          <!--   成品布细码标签   -->
          <!--        <PrintBtn type="productionXiMa" size="small" plain btnText="打印细码" :tid="1679373308416256" @onPrint="handlePrintXiMa(row)" :list="printList" /> -->
        </template>
      </Table>
    </FildCard>
  </div>
  <FineSizeEnteringDetail ref="FineSizeEnteringDetailRef" />
</template>

<style lang="scss" scoped>
.el-link {
  color: #0e7eff;
}
</style>
